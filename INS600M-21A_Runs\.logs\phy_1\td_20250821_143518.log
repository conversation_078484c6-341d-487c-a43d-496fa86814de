============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Aug 21 14:35:18 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-5007 WARNING: data object 'write_en' is already declared in ../../Src/FMC/FMC_Ctrl.v(114)
HDL-1007 : previous declaration of 'write_en' is from here in ../../Src/FMC/FMC_Ctrl.v(113)
HDL-5007 WARNING: second declaration of 'write_en' is ignored in ../../Src/FMC/FMC_Ctrl.v(114)
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.922984s wall, 1.578125s user + 4.328125s system = 5.906250s CPU (99.7%)

RUN-1004 : used memory is 80 MB, reserved memory is 41 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  2.024247s wall, 1.875000s user + 0.140625s system = 2.015625s CPU (99.6%)

RUN-1004 : used memory is 303 MB, reserved memory is 271 MB, peak memory is 306 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 42 trigger nets, 42 data nets.
KIT-1004 : Chipwatcher code = 0000101011000101
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=126) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=126) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=126)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=126)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22823/23 useful/useless nets, 19571/12 useful/useless insts
SYN-1016 : Merged 29 instances.
SYN-1032 : 22491/20 useful/useless nets, 19997/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 398 better
SYN-1014 : Optimize round 2
SYN-1032 : 22171/45 useful/useless nets, 19677/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.642644s wall, 2.578125s user + 0.078125s system = 2.656250s CPU (100.5%)

RUN-1004 : used memory is 330 MB, reserved memory is 296 MB, peak memory is 331 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22219/299 useful/useless nets, 19762/47 useful/useless insts
SYN-1016 : Merged 45 instances.
SYN-2571 : Optimize after map_dsp, round 1, 391 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 40 instances.
SYN-2501 : Optimize round 1, 82 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 22667/5 useful/useless nets, 20210/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82690, tnet num: 22667, tinst num: 20209, tnode num: 115782, tedge num: 129261.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.289884s wall, 1.250000s user + 0.046875s system = 1.296875s CPU (100.5%)

RUN-1004 : used memory is 470 MB, reserved memory is 438 MB, peak memory is 470 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22667 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 234 (3.43), #lev = 7 (1.79)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 234 (3.43), #lev = 7 (1.79)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 534 instances into 234 LUTs, name keeping = 75%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 407 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 128 adder to BLE ...
SYN-4008 : Packed 128 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.954840s wall, 4.828125s user + 0.125000s system = 4.953125s CPU (100.0%)

RUN-1004 : used memory is 353 MB, reserved memory is 319 MB, peak memory is 578 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.957758s wall, 7.718750s user + 0.250000s system = 7.968750s CPU (100.1%)

RUN-1004 : used memory is 354 MB, reserved memory is 319 MB, peak memory is 578 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (271 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4024 : Net "FMC/dsp_out0_n" drives clk pins.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net FMC/dsp_out0_n as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 4 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net FMC/dsp_out0_n to drive 16 clock pins.
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19451 instances
RUN-0007 : 5638 luts, 12206 seqs, 983 mslices, 519 lslices, 59 pads, 40 brams, 0 dsps
RUN-1001 : There are total 21916 nets
RUN-1001 : 16447 nets have 2 pins
RUN-1001 : 4273 nets have [3 - 5] pins
RUN-1001 : 812 nets have [6 - 10] pins
RUN-1001 : 259 nets have [11 - 20] pins
RUN-1001 : 103 nets have [21 - 99] pins
RUN-1001 : 22 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4790     
RUN-1001 :   No   |  No   |  Yes  |     704     
RUN-1001 :   No   |  Yes  |  No   |     127     
RUN-1001 :   Yes  |  No   |  No   |    6067     
RUN-1001 :   Yes  |  No   |  Yes  |     443     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  114  |     14     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19449 instances, 5638 luts, 12206 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81069, tnet num: 21914, tinst num: 19449, tnode num: 113952, tedge num: 127585.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.279877s wall, 1.234375s user + 0.046875s system = 1.281250s CPU (100.1%)

RUN-1004 : used memory is 528 MB, reserved memory is 500 MB, peak memory is 578 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21914 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.250783s wall, 2.187500s user + 0.062500s system = 2.250000s CPU (100.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.52808e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19449.
PHY-3001 : Level 1 #clusters 2164.
PHY-3001 : End clustering;  0.171645s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (182.1%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 935194, overlap = 592.844
PHY-3002 : Step(2): len = 847738, overlap = 640.688
PHY-3002 : Step(3): len = 540400, overlap = 881.531
PHY-3002 : Step(4): len = 471665, overlap = 923.281
PHY-3002 : Step(5): len = 372384, overlap = 1033.69
PHY-3002 : Step(6): len = 340312, overlap = 1086.69
PHY-3002 : Step(7): len = 274104, overlap = 1173.38
PHY-3002 : Step(8): len = 246039, overlap = 1245.44
PHY-3002 : Step(9): len = 212544, overlap = 1313
PHY-3002 : Step(10): len = 196379, overlap = 1328.88
PHY-3002 : Step(11): len = 177384, overlap = 1369.56
PHY-3002 : Step(12): len = 161830, overlap = 1415.72
PHY-3002 : Step(13): len = 145744, overlap = 1442.62
PHY-3002 : Step(14): len = 134494, overlap = 1482.28
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.11707e-06
PHY-3002 : Step(15): len = 138275, overlap = 1458.72
PHY-3002 : Step(16): len = 182045, overlap = 1317.56
PHY-3002 : Step(17): len = 193082, overlap = 1229.41
PHY-3002 : Step(18): len = 192468, overlap = 1159.88
PHY-3002 : Step(19): len = 188124, overlap = 1129
PHY-3002 : Step(20): len = 182101, overlap = 1100.31
PHY-3002 : Step(21): len = 176446, overlap = 1074.56
PHY-3002 : Step(22): len = 171900, overlap = 1061.19
PHY-3002 : Step(23): len = 167572, overlap = 1053.19
PHY-3002 : Step(24): len = 164074, overlap = 1055.31
PHY-3002 : Step(25): len = 162952, overlap = 1042.59
PHY-3002 : Step(26): len = 162372, overlap = 1071.47
PHY-3002 : Step(27): len = 161386, overlap = 1078.19
PHY-3002 : Step(28): len = 161034, overlap = 1101.47
PHY-3002 : Step(29): len = 160875, overlap = 1115.84
PHY-3002 : Step(30): len = 161551, overlap = 1094.56
PHY-3002 : Step(31): len = 161675, overlap = 1078.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.23415e-06
PHY-3002 : Step(32): len = 170423, overlap = 1052.66
PHY-3002 : Step(33): len = 184651, overlap = 969.938
PHY-3002 : Step(34): len = 187753, overlap = 875.781
PHY-3002 : Step(35): len = 191551, overlap = 858.688
PHY-3002 : Step(36): len = 190107, overlap = 867
PHY-3002 : Step(37): len = 189135, overlap = 882.281
PHY-3002 : Step(38): len = 186976, overlap = 899.125
PHY-3002 : Step(39): len = 186797, overlap = 899
PHY-3002 : Step(40): len = 186578, overlap = 895.625
PHY-3002 : Step(41): len = 186445, overlap = 897.688
PHY-3002 : Step(42): len = 185428, overlap = 900.719
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.46829e-06
PHY-3002 : Step(43): len = 195958, overlap = 861.281
PHY-3002 : Step(44): len = 212528, overlap = 792.125
PHY-3002 : Step(45): len = 215393, overlap = 741.156
PHY-3002 : Step(46): len = 216971, overlap = 745.469
PHY-3002 : Step(47): len = 216683, overlap = 752.219
PHY-3002 : Step(48): len = 216203, overlap = 765.062
PHY-3002 : Step(49): len = 214580, overlap = 755
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 8.93659e-06
PHY-3002 : Step(50): len = 228822, overlap = 706.438
PHY-3002 : Step(51): len = 243141, overlap = 631.938
PHY-3002 : Step(52): len = 247165, overlap = 584.812
PHY-3002 : Step(53): len = 248702, overlap = 564.562
PHY-3002 : Step(54): len = 247366, overlap = 539.969
PHY-3002 : Step(55): len = 246219, overlap = 534.812
PHY-3002 : Step(56): len = 244939, overlap = 538.781
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.78732e-05
PHY-3002 : Step(57): len = 258924, overlap = 506.938
PHY-3002 : Step(58): len = 274334, overlap = 450.25
PHY-3002 : Step(59): len = 279075, overlap = 414.312
PHY-3002 : Step(60): len = 278652, overlap = 396.094
PHY-3002 : Step(61): len = 275891, overlap = 394.312
PHY-3002 : Step(62): len = 273153, overlap = 389.906
PHY-3002 : Step(63): len = 270985, overlap = 374.688
PHY-3002 : Step(64): len = 269390, overlap = 400.719
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.57463e-05
PHY-3002 : Step(65): len = 278296, overlap = 354.219
PHY-3002 : Step(66): len = 288581, overlap = 329.344
PHY-3002 : Step(67): len = 291539, overlap = 315.062
PHY-3002 : Step(68): len = 292418, overlap = 302.031
PHY-3002 : Step(69): len = 291443, overlap = 314.688
PHY-3002 : Step(70): len = 290382, overlap = 313.5
PHY-3002 : Step(71): len = 288695, overlap = 317.25
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 7.1269e-05
PHY-3002 : Step(72): len = 296101, overlap = 310.469
PHY-3002 : Step(73): len = 303850, overlap = 297.844
PHY-3002 : Step(74): len = 306825, overlap = 292.375
PHY-3002 : Step(75): len = 307699, overlap = 278.969
PHY-3002 : Step(76): len = 307139, overlap = 264.812
PHY-3002 : Step(77): len = 306386, overlap = 251.906
PHY-3002 : Step(78): len = 304511, overlap = 248.656
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000138034
PHY-3002 : Step(79): len = 309385, overlap = 242.938
PHY-3002 : Step(80): len = 315312, overlap = 233.781
PHY-3002 : Step(81): len = 317344, overlap = 229.281
PHY-3002 : Step(82): len = 318577, overlap = 227.938
PHY-3002 : Step(83): len = 317849, overlap = 227.438
PHY-3002 : Step(84): len = 316925, overlap = 226.031
PHY-3002 : Step(85): len = 315800, overlap = 226.531
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000236801
PHY-3002 : Step(86): len = 318326, overlap = 213.094
PHY-3002 : Step(87): len = 322997, overlap = 210.625
PHY-3002 : Step(88): len = 323744, overlap = 203.719
PHY-3002 : Step(89): len = 325435, overlap = 198.531
PHY-3002 : Step(90): len = 326455, overlap = 205.531
PHY-3002 : Step(91): len = 327842, overlap = 206.094
PHY-3002 : Step(92): len = 327662, overlap = 213.219
PHY-3002 : Step(93): len = 326517, overlap = 222.531
PHY-3002 : Step(94): len = 326928, overlap = 219.906
PHY-3002 : Step(95): len = 326336, overlap = 212.656
PHY-3002 : Step(96): len = 326537, overlap = 215.156
PHY-3002 : Step(97): len = 325883, overlap = 210.906
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000397759
PHY-3002 : Step(98): len = 326702, overlap = 212.938
PHY-3002 : Step(99): len = 328087, overlap = 221.875
PHY-3002 : Step(100): len = 329043, overlap = 216.156
PHY-3001 : :::11::: Try harder cell spreading with beta_ = 0.000643574
PHY-3002 : Step(101): len = 330129, overlap = 213.25
PHY-3002 : Step(102): len = 336235, overlap = 203.125
PHY-3002 : Step(103): len = 337797, overlap = 207.719
PHY-3002 : Step(104): len = 339213, overlap = 192.75
PHY-3002 : Step(105): len = 339790, overlap = 189.5
PHY-3002 : Step(106): len = 340206, overlap = 193.531
PHY-3002 : Step(107): len = 340550, overlap = 196.469
PHY-3002 : Step(108): len = 339645, overlap = 190.375
PHY-3002 : Step(109): len = 339185, overlap = 167.875
PHY-3002 : Step(110): len = 339197, overlap = 172.188
PHY-3002 : Step(111): len = 339294, overlap = 165.594
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.017855s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (175.0%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21916.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 447280, over cnt = 1205(3%), over = 5081, worst = 34
PHY-1001 : End global iterations;  0.869245s wall, 1.218750s user + 0.078125s system = 1.296875s CPU (149.2%)

PHY-1001 : Congestion index: top1 = 70.32, top5 = 50.70, top10 = 41.86, top15 = 36.70.
PHY-3001 : End congestion estimation;  1.098817s wall, 1.437500s user + 0.078125s system = 1.515625s CPU (137.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21914 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.018545s wall, 0.984375s user + 0.031250s system = 1.015625s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000101573
PHY-3002 : Step(112): len = 383473, overlap = 124.5
PHY-3002 : Step(113): len = 397875, overlap = 106.406
PHY-3002 : Step(114): len = 397153, overlap = 104.406
PHY-3002 : Step(115): len = 393467, overlap = 98.6875
PHY-3002 : Step(116): len = 396878, overlap = 86.125
PHY-3002 : Step(117): len = 403906, overlap = 69
PHY-3002 : Step(118): len = 411389, overlap = 73.1875
PHY-3002 : Step(119): len = 413504, overlap = 70.1875
PHY-3002 : Step(120): len = 416158, overlap = 71.5312
PHY-3002 : Step(121): len = 419337, overlap = 69.9375
PHY-3002 : Step(122): len = 420861, overlap = 72.6562
PHY-3002 : Step(123): len = 421737, overlap = 68.5312
PHY-3002 : Step(124): len = 422828, overlap = 70.2812
PHY-3002 : Step(125): len = 423382, overlap = 69.0938
PHY-3002 : Step(126): len = 423867, overlap = 75.375
PHY-3002 : Step(127): len = 426559, overlap = 80.0625
PHY-3002 : Step(128): len = 428433, overlap = 79.7812
PHY-3002 : Step(129): len = 428470, overlap = 84.5
PHY-3002 : Step(130): len = 429680, overlap = 90.125
PHY-3002 : Step(131): len = 429446, overlap = 95.9375
PHY-3002 : Step(132): len = 429727, overlap = 101.375
PHY-3002 : Step(133): len = 430470, overlap = 102.594
PHY-3002 : Step(134): len = 429766, overlap = 105.781
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(135): len = 430484, overlap = 111.219
PHY-3002 : Step(136): len = 431147, overlap = 112.406
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 78/21916.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 500384, over cnt = 2122(6%), over = 9288, worst = 39
PHY-1001 : End global iterations;  1.146493s wall, 2.015625s user + 0.031250s system = 2.046875s CPU (178.5%)

PHY-1001 : Congestion index: top1 = 78.21, top5 = 57.41, top10 = 48.68, top15 = 43.66.
PHY-3001 : End congestion estimation;  1.453848s wall, 2.312500s user + 0.031250s system = 2.343750s CPU (161.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21914 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.230735s wall, 1.218750s user + 0.015625s system = 1.234375s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.95942e-05
PHY-3002 : Step(137): len = 440326, overlap = 403.562
PHY-3002 : Step(138): len = 451709, overlap = 337.625
PHY-3002 : Step(139): len = 447381, overlap = 335.719
PHY-3002 : Step(140): len = 445980, overlap = 325.844
PHY-3002 : Step(141): len = 445814, overlap = 304.531
PHY-3002 : Step(142): len = 445922, overlap = 277.656
PHY-3002 : Step(143): len = 447181, overlap = 247.031
PHY-3002 : Step(144): len = 445923, overlap = 240.938
PHY-3002 : Step(145): len = 445811, overlap = 230.812
PHY-3002 : Step(146): len = 445439, overlap = 228.906
PHY-3002 : Step(147): len = 444282, overlap = 218.688
PHY-3002 : Step(148): len = 444041, overlap = 216
PHY-3002 : Step(149): len = 442496, overlap = 218.688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000179188
PHY-3002 : Step(150): len = 442488, overlap = 204.125
PHY-3002 : Step(151): len = 443499, overlap = 200.281
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000345963
PHY-3002 : Step(152): len = 446641, overlap = 195.75
PHY-3002 : Step(153): len = 454737, overlap = 172.344
PHY-3002 : Step(154): len = 457240, overlap = 165.25
PHY-3002 : Step(155): len = 457928, overlap = 161.531
PHY-3002 : Step(156): len = 459218, overlap = 158.281
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81069, tnet num: 21914, tinst num: 19449, tnode num: 113952, tedge num: 127585.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.640741s wall, 1.593750s user + 0.046875s system = 1.640625s CPU (100.0%)

RUN-1004 : used memory is 568 MB, reserved memory is 542 MB, peak memory is 702 MB
OPT-1001 : Total overflow 531.81 peak overflow 3.56
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 614/21916.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 541608, over cnt = 2520(7%), over = 8616, worst = 28
PHY-1001 : End global iterations;  1.265032s wall, 1.953125s user + 0.015625s system = 1.968750s CPU (155.6%)

PHY-1001 : Congestion index: top1 = 58.49, top5 = 47.87, top10 = 42.83, top15 = 39.69.
PHY-1001 : End incremental global routing;  1.543480s wall, 2.234375s user + 0.015625s system = 2.250000s CPU (145.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21914 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.090246s wall, 1.031250s user + 0.046875s system = 1.078125s CPU (98.9%)

OPT-1001 : 18 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19368 has valid locations, 247 needs to be replaced
PHY-3001 : design contains 19678 instances, 5735 luts, 12338 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 475030
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16897/22145.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 554904, over cnt = 2532(7%), over = 8629, worst = 28
PHY-1001 : End global iterations;  0.199369s wall, 0.312500s user + 0.031250s system = 0.343750s CPU (172.4%)

PHY-1001 : Congestion index: top1 = 59.27, top5 = 48.27, top10 = 43.12, top15 = 39.99.
PHY-3001 : End congestion estimation;  0.587420s wall, 0.703125s user + 0.031250s system = 0.734375s CPU (125.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81817, tnet num: 22143, tinst num: 19678, tnode num: 115015, tedge num: 128623.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.650588s wall, 1.640625s user + 0.015625s system = 1.656250s CPU (100.3%)

RUN-1004 : used memory is 613 MB, reserved memory is 606 MB, peak memory is 703 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22143 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.767032s wall, 2.734375s user + 0.031250s system = 2.765625s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(157): len = 474762, overlap = 6.5
PHY-3002 : Step(158): len = 475614, overlap = 6.6875
PHY-3002 : Step(159): len = 476588, overlap = 6.75
PHY-3002 : Step(160): len = 477481, overlap = 6.8125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 16921/22145.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 553440, over cnt = 2576(7%), over = 8813, worst = 28
PHY-1001 : End global iterations;  0.204911s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (114.4%)

PHY-1001 : Congestion index: top1 = 58.92, top5 = 48.59, top10 = 43.56, top15 = 40.31.
PHY-3001 : End congestion estimation;  0.467715s wall, 0.484375s user + 0.015625s system = 0.500000s CPU (106.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22143 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.081204s wall, 1.062500s user + 0.015625s system = 1.078125s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000669792
PHY-3002 : Step(161): len = 477513, overlap = 161.969
PHY-3002 : Step(162): len = 477632, overlap = 161.906
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00133958
PHY-3002 : Step(163): len = 477857, overlap = 161.062
PHY-3002 : Step(164): len = 478233, overlap = 160.875
PHY-3001 : Final: Len = 478233, Over = 160.875
PHY-3001 : End incremental placement;  5.886813s wall, 6.218750s user + 0.343750s system = 6.562500s CPU (111.5%)

OPT-1001 : Total overflow 536.75 peak overflow 3.56
OPT-1001 : End high-fanout net optimization;  9.108599s wall, 10.156250s user + 0.406250s system = 10.562500s CPU (116.0%)

OPT-1001 : Current memory(MB): used = 706, reserve = 685, peak = 723.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16930/22145.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 556352, over cnt = 2527(7%), over = 8204, worst = 28
PHY-1002 : len = 595728, over cnt = 1734(4%), over = 4364, worst = 20
PHY-1002 : len = 635976, over cnt = 733(2%), over = 1620, worst = 16
PHY-1002 : len = 653176, over cnt = 311(0%), over = 646, worst = 11
PHY-1002 : len = 662760, over cnt = 53(0%), over = 99, worst = 7
PHY-1001 : End global iterations;  1.287424s wall, 1.953125s user + 0.000000s system = 1.953125s CPU (151.7%)

PHY-1001 : Congestion index: top1 = 50.56, top5 = 44.55, top10 = 41.22, top15 = 39.14.
OPT-1001 : End congestion update;  1.564037s wall, 2.234375s user + 0.000000s system = 2.234375s CPU (142.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22143 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.944532s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (99.3%)

OPT-0007 : Start: WNS 4069 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.515339s wall, 3.187500s user + 0.000000s system = 3.187500s CPU (126.7%)

OPT-1001 : Current memory(MB): used = 681, reserve = 660, peak = 723.
OPT-1001 : End physical optimization;  13.603198s wall, 15.375000s user + 0.484375s system = 15.859375s CPU (116.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5735 LUT to BLE ...
SYN-4008 : Packed 5735 LUT and 2732 SEQ to BLE.
SYN-4003 : Packing 9606 remaining SEQ's ...
SYN-4005 : Packed 3384 SEQ with LUT/SLICE
SYN-4006 : 135 single LUT's are left
SYN-4006 : 6222 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11957/13828 primitive instances ...
PHY-3001 : End packing;  3.025927s wall, 3.031250s user + 0.000000s system = 3.031250s CPU (100.2%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8179 instances
RUN-1001 : 4037 mslices, 4037 lslices, 59 pads, 40 brams, 0 dsps
RUN-1001 : There are total 19466 nets
RUN-1001 : 13669 nets have 2 pins
RUN-1001 : 4375 nets have [3 - 5] pins
RUN-1001 : 877 nets have [6 - 10] pins
RUN-1001 : 406 nets have [11 - 20] pins
RUN-1001 : 130 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8177 instances, 8074 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Cell area utilization is 86%
PHY-3001 : After packing: Len = 496970, Over = 413
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7672/19466.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 631256, over cnt = 1680(4%), over = 2738, worst = 8
PHY-1002 : len = 637560, over cnt = 1166(3%), over = 1692, worst = 8
PHY-1002 : len = 650256, over cnt = 483(1%), over = 637, worst = 8
PHY-1002 : len = 657696, over cnt = 142(0%), over = 182, worst = 5
PHY-1002 : len = 660816, over cnt = 2(0%), over = 2, worst = 1
PHY-1001 : End global iterations;  1.306137s wall, 2.125000s user + 0.031250s system = 2.156250s CPU (165.1%)

PHY-1001 : Congestion index: top1 = 51.70, top5 = 44.83, top10 = 41.36, top15 = 39.01.
PHY-3001 : End congestion estimation;  1.655537s wall, 2.468750s user + 0.031250s system = 2.500000s CPU (151.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67928, tnet num: 19464, tinst num: 8177, tnode num: 92140, tedge num: 111949.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.856037s wall, 1.843750s user + 0.015625s system = 1.859375s CPU (100.2%)

RUN-1004 : used memory is 604 MB, reserved memory is 590 MB, peak memory is 723 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19464 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.870946s wall, 2.828125s user + 0.046875s system = 2.875000s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.3226e-05
PHY-3002 : Step(165): len = 501414, overlap = 409
PHY-3002 : Step(166): len = 501082, overlap = 407.75
PHY-3002 : Step(167): len = 501048, overlap = 414.75
PHY-3002 : Step(168): len = 500763, overlap = 422
PHY-3002 : Step(169): len = 500879, overlap = 428.5
PHY-3002 : Step(170): len = 499670, overlap = 429.75
PHY-3002 : Step(171): len = 499622, overlap = 430.75
PHY-3002 : Step(172): len = 496909, overlap = 431.75
PHY-3002 : Step(173): len = 495092, overlap = 435.5
PHY-3002 : Step(174): len = 494004, overlap = 438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.6452e-05
PHY-3002 : Step(175): len = 496613, overlap = 423.75
PHY-3002 : Step(176): len = 501008, overlap = 414.25
PHY-3002 : Step(177): len = 505054, overlap = 404.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000172904
PHY-3002 : Step(178): len = 510938, overlap = 391.75
PHY-3002 : Step(179): len = 522458, overlap = 373
PHY-3002 : Step(180): len = 523267, overlap = 373
PHY-3002 : Step(181): len = 523061, overlap = 369.25
PHY-3002 : Step(182): len = 523245, overlap = 366
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.677065s wall, 0.859375s user + 0.718750s system = 1.578125s CPU (233.1%)

PHY-3001 : Trial Legalized: Len = 643785
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 565/19466.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 736960, over cnt = 2482(7%), over = 4114, worst = 8
PHY-1002 : len = 751144, over cnt = 1612(4%), over = 2349, worst = 8
PHY-1002 : len = 774480, over cnt = 451(1%), over = 606, worst = 4
PHY-1002 : len = 782344, over cnt = 128(0%), over = 176, worst = 4
PHY-1002 : len = 785672, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.142255s wall, 3.515625s user + 0.062500s system = 3.578125s CPU (167.0%)

PHY-1001 : Congestion index: top1 = 50.41, top5 = 45.82, top10 = 43.21, top15 = 41.45.
PHY-3001 : End congestion estimation;  2.538853s wall, 3.906250s user + 0.062500s system = 3.968750s CPU (156.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19464 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.008566s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (99.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000190919
PHY-3002 : Step(183): len = 596930, overlap = 99.75
PHY-3002 : Step(184): len = 576397, overlap = 140.75
PHY-3002 : Step(185): len = 565425, overlap = 188.5
PHY-3002 : Step(186): len = 558061, overlap = 224.5
PHY-3002 : Step(187): len = 554017, overlap = 251.5
PHY-3002 : Step(188): len = 551950, overlap = 271.25
PHY-3002 : Step(189): len = 549749, overlap = 278.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000381837
PHY-3002 : Step(190): len = 555223, overlap = 275.75
PHY-3002 : Step(191): len = 560012, overlap = 271.75
PHY-3002 : Step(192): len = 559565, overlap = 271.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(193): len = 562089, overlap = 266.5
PHY-3002 : Step(194): len = 568002, overlap = 254.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.040274s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (77.6%)

PHY-3001 : Legalized: Len = 610024, Over = 0
PHY-3001 : Spreading special nets. 51 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.101185s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (92.7%)

PHY-3001 : 75 instances has been re-located, deltaX = 27, deltaY = 45, maxDist = 2.
PHY-3001 : Final: Len = 610910, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67928, tnet num: 19464, tinst num: 8177, tnode num: 92140, tedge num: 111949.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.212222s wall, 2.140625s user + 0.078125s system = 2.218750s CPU (100.3%)

RUN-1004 : used memory is 625 MB, reserved memory is 629 MB, peak memory is 723 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3926/19466.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 713936, over cnt = 2256(6%), over = 3532, worst = 7
PHY-1002 : len = 724712, over cnt = 1446(4%), over = 2005, worst = 7
PHY-1002 : len = 738264, over cnt = 683(1%), over = 898, worst = 6
PHY-1002 : len = 744512, over cnt = 427(1%), over = 552, worst = 6
PHY-1002 : len = 753000, over cnt = 28(0%), over = 37, worst = 2
PHY-1001 : End global iterations;  1.791988s wall, 3.000000s user + 0.031250s system = 3.031250s CPU (169.2%)

PHY-1001 : Congestion index: top1 = 48.62, top5 = 43.70, top10 = 41.40, top15 = 39.74.
PHY-1001 : End incremental global routing;  2.138133s wall, 3.343750s user + 0.031250s system = 3.375000s CPU (157.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19464 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.010907s wall, 0.984375s user + 0.031250s system = 1.015625s CPU (100.5%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8113 has valid locations, 9 needs to be replaced
PHY-3001 : design contains 8185 instances, 8082 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 612323
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17497/19473.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 754216, over cnt = 35(0%), over = 44, worst = 2
PHY-1002 : len = 754264, over cnt = 14(0%), over = 14, worst = 1
PHY-1002 : len = 754456, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.426676s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (106.2%)

PHY-1001 : Congestion index: top1 = 48.79, top5 = 43.71, top10 = 41.42, top15 = 39.77.
PHY-3001 : End congestion estimation;  0.756340s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (105.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67975, tnet num: 19471, tinst num: 8185, tnode num: 92195, tedge num: 112003.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.139115s wall, 2.140625s user + 0.000000s system = 2.140625s CPU (100.1%)

RUN-1004 : used memory is 653 MB, reserved memory is 643 MB, peak memory is 723 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19471 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.205553s wall, 3.187500s user + 0.015625s system = 3.203125s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(195): len = 612296, overlap = 0
PHY-3002 : Step(196): len = 612254, overlap = 0.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17494/19473.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 754168, over cnt = 9(0%), over = 10, worst = 2
PHY-1002 : len = 754136, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 754184, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 754216, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.730936s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (100.5%)

PHY-1001 : Congestion index: top1 = 48.58, top5 = 43.72, top10 = 41.42, top15 = 39.77.
PHY-3001 : End congestion estimation;  1.071067s wall, 1.078125s user + 0.000000s system = 1.078125s CPU (100.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19471 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.071712s wall, 1.078125s user + 0.000000s system = 1.078125s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000628856
PHY-3002 : Step(197): len = 612259, overlap = 0.75
PHY-3002 : Step(198): len = 612288, overlap = 0.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007467s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 612298, Over = 0
PHY-3001 : End spreading;  0.077390s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (100.9%)

PHY-3001 : Final: Len = 612298, Over = 0
PHY-3001 : End incremental placement;  6.799073s wall, 6.906250s user + 0.140625s system = 7.046875s CPU (103.6%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.503009s wall, 11.968750s user + 0.218750s system = 12.187500s CPU (116.0%)

OPT-1001 : Current memory(MB): used = 721, reserve = 707, peak = 727.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17494/19473.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 754112, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 753976, over cnt = 10(0%), over = 10, worst = 1
PHY-1002 : len = 754024, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 754056, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.598451s wall, 0.593750s user + 0.015625s system = 0.609375s CPU (101.8%)

PHY-1001 : Congestion index: top1 = 48.58, top5 = 43.69, top10 = 41.38, top15 = 39.74.
OPT-1001 : End congestion update;  0.931859s wall, 0.921875s user + 0.015625s system = 0.937500s CPU (100.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19471 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.877114s wall, 0.875000s user + 0.000000s system = 0.875000s CPU (99.8%)

OPT-0007 : Start: WNS 4053 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.814372s wall, 1.812500s user + 0.015625s system = 1.828125s CPU (100.8%)

OPT-1001 : Current memory(MB): used = 722, reserve = 707, peak = 727.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19471 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.847358s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (99.6%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17505/19473.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 754056, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.127437s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (110.3%)

PHY-1001 : Congestion index: top1 = 48.58, top5 = 43.69, top10 = 41.38, top15 = 39.74.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19471 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.850536s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (99.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4053 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 48.172414
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4053ps with logic level 4 
OPT-1001 : End physical optimization;  16.974572s wall, 18.359375s user + 0.312500s system = 18.671875s CPU (110.0%)

RUN-1003 : finish command "place" in  70.017484s wall, 128.734375s user + 7.578125s system = 136.312500s CPU (194.7%)

RUN-1004 : used memory is 600 MB, reserved memory is 584 MB, peak memory is 727 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.878680s wall, 3.187500s user + 0.046875s system = 3.234375s CPU (172.2%)

RUN-1004 : used memory is 600 MB, reserved memory is 585 MB, peak memory is 727 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8187 instances
RUN-1001 : 4037 mslices, 4045 lslices, 59 pads, 40 brams, 0 dsps
RUN-1001 : There are total 19473 nets
RUN-1001 : 13669 nets have 2 pins
RUN-1001 : 4375 nets have [3 - 5] pins
RUN-1001 : 880 nets have [6 - 10] pins
RUN-1001 : 411 nets have [11 - 20] pins
RUN-1001 : 129 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67975, tnet num: 19471, tinst num: 8185, tnode num: 92195, tedge num: 112003.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.788572s wall, 1.750000s user + 0.031250s system = 1.781250s CPU (99.6%)

RUN-1004 : used memory is 597 MB, reserved memory is 577 MB, peak memory is 727 MB
PHY-1001 : 4037 mslices, 4045 lslices, 59 pads, 40 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19471 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 691144, over cnt = 2366(6%), over = 3963, worst = 8
PHY-1002 : len = 705960, over cnt = 1478(4%), over = 2181, worst = 8
PHY-1002 : len = 722160, over cnt = 730(2%), over = 996, worst = 7
PHY-1002 : len = 739432, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 739608, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.803064s wall, 3.171875s user + 0.015625s system = 3.187500s CPU (176.8%)

PHY-1001 : Congestion index: top1 = 48.51, top5 = 43.61, top10 = 41.06, top15 = 39.35.
PHY-1001 : End global routing;  2.170749s wall, 3.515625s user + 0.046875s system = 3.562500s CPU (164.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 700, reserve = 693, peak = 727.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : clock net FMC/dsp_out0_n_syn_4 will be merged with clock FMC/dsp_out0_n
PHY-1001 : Current memory(MB): used = 975, reserve = 968, peak = 975.
PHY-1001 : End build detailed router design. 4.827838s wall, 4.796875s user + 0.015625s system = 4.812500s CPU (99.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 187056, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.503713s wall, 1.500000s user + 0.000000s system = 1.500000s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 1010, reserve = 1005, peak = 1010.
PHY-1001 : End phase 1; 1.511967s wall, 1.515625s user + 0.000000s system = 1.515625s CPU (100.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.72413e+06, over cnt = 1411(0%), over = 1423, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1026, reserve = 1017, peak = 1026.
PHY-1001 : End initial routed; 18.613765s wall, 49.562500s user + 0.250000s system = 49.812500s CPU (267.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18201(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.552   |   0.000   |   0   
RUN-1001 :   Hold   |   0.195   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.884563s wall, 3.859375s user + 0.015625s system = 3.875000s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 1037, reserve = 1027, peak = 1037.
PHY-1001 : End phase 2; 22.498500s wall, 53.421875s user + 0.265625s system = 53.687500s CPU (238.6%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.72413e+06, over cnt = 1411(0%), over = 1423, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.265747s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (100.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.7099e+06, over cnt = 479(0%), over = 479, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 1.353525s wall, 1.875000s user + 0.000000s system = 1.875000s CPU (138.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.70962e+06, over cnt = 102(0%), over = 102, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.686835s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (125.1%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.71066e+06, over cnt = 25(0%), over = 25, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.280129s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (122.7%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.71095e+06, over cnt = 6(0%), over = 6, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.282954s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (116.0%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.71107e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.202947s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (100.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18201(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.390   |   0.000   |   0   
RUN-1001 :   Hold   |   0.195   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.781585s wall, 3.781250s user + 0.000000s system = 3.781250s CPU (100.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 357 feed throughs used by 301 nets
PHY-1001 : End commit to database; 2.422962s wall, 2.406250s user + 0.015625s system = 2.421875s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1125, reserve = 1118, peak = 1125.
PHY-1001 : End phase 3; 9.887120s wall, 10.671875s user + 0.015625s system = 10.687500s CPU (108.1%)

PHY-1003 : Routed, final wirelength = 1.71107e+06
PHY-1001 : Current memory(MB): used = 1129, reserve = 1122, peak = 1129.
PHY-1001 : End export database. 0.064844s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (96.4%)

PHY-1001 : End detail routing;  39.236660s wall, 70.906250s user + 0.296875s system = 71.203125s CPU (181.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67975, tnet num: 19471, tinst num: 8185, tnode num: 92195, tedge num: 112003.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.884212s wall, 1.875000s user + 0.000000s system = 1.875000s CPU (99.5%)

RUN-1004 : used memory is 1063 MB, reserved memory is 1062 MB, peak memory is 1129 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  47.934379s wall, 80.921875s user + 0.375000s system = 81.296875s CPU (169.6%)

RUN-1004 : used memory is 1062 MB, reserved memory is 1062 MB, peak memory is 1129 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8882   out of  19600   45.32%
#reg                    12432   out of  19600   63.43%
#le                     15056
  #lut only              2624   out of  15056   17.43%
  #reg only              6174   out of  15056   41.01%
  #lut&reg               6258   out of  15056   41.56%
#dsp                        0   out of     29    0.00%
#bram                      40   out of     64   62.50%
  #bram9k                  38
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       59   out of    188   31.38%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                             Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0             6834
#2        config_inst_syn_9                GCLK               config             config_inst.jtck                   158
#3        FMC/dsp_out0_n                   GCLK               lslice             COM2/uart_com2/reg66_syn_228.f1    10
#4        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di                    1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         F2        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         D1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         B3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         C1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         A3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         A2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         B2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         B1        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F15        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDZ        INPUT        K15        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         P2        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         N4        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         M5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         N6        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         N5        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         P5        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M3        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         M4        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         H2        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         M1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         L1        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         N1        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         N3        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         M2        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT         T5        LVCMOS33           8            NONE           OREG       
    Fog_ENY        OUTPUT         T4        LVCMOS33           8            NONE           OREG       
    Fog_ENZ        OUTPUT         T7        LVCMOS33           8            NONE           OREG       
    Fog_INT        OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         T6        LVCMOS33           8            N/A            OREG       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E1        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        G11        LVCMOS25           8            N/A            NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15056  |7380    |1502    |12476   |40      |0       |
|  AnyFog_dataX                      |AnyFog          |208    |90      |22      |165     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |88     |65      |22      |45      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |209    |83      |22      |172     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |60      |22      |49      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |208    |86      |22      |169     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |56      |22      |47      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2931   |749     |39      |2853    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |60     |33      |5       |51      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |219    |101     |5       |209     |0       |0       |
|    STADOP_com2                     |STADOP          |543    |108     |0       |536     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |41      |14      |43      |0       |0       |
|    head_com2                       |uniheading      |273    |63      |5       |260     |0       |0       |
|    rmc_com2                        |Gprmc           |29     |26      |0       |28      |0       |0       |
|    uart_com2                       |Agrica          |1436   |351     |10      |1420    |0       |0       |
|  COM3                              |COM3_Control    |281    |131     |19      |241     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |61     |33      |5       |52      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |61     |41      |14      |38      |0       |0       |
|    rmc_com3                        |Gprmc           |159    |57      |0       |151     |0       |0       |
|  DATA                              |Data_Processing |8869   |4518    |1122    |7054    |0       |0       |
|    DIV_Dtemp                       |Divider         |758    |309     |84      |634     |0       |0       |
|    DIV_Utemp                       |Divider         |591    |297     |84      |461     |0       |0       |
|    DIV_accX                        |Divider         |632    |345     |84      |499     |0       |0       |
|    DIV_accY                        |Divider         |677    |326     |102     |514     |0       |0       |
|    DIV_accZ                        |Divider         |668    |377     |132     |461     |0       |0       |
|    DIV_rateX                       |Divider         |643    |382     |132     |436     |0       |0       |
|    DIV_rateY                       |Divider         |629    |356     |132     |423     |0       |0       |
|    DIV_rateZ                       |Divider         |585    |350     |132     |380     |0       |0       |
|    genclk                          |genclk          |265    |170     |89      |105     |0       |0       |
|  FMC                               |FMC_Ctrl        |431    |380     |43      |342     |0       |0       |
|  IIC                               |I2C_master      |302    |249     |11      |259     |0       |0       |
|  IMU_CTRL                          |SCHA634         |908    |636     |61      |724     |0       |0       |
|    CtrlData                        |CtrlData        |490    |439     |47      |337     |0       |0       |
|      usms                          |Time_1ms        |28     |23      |5       |20      |0       |0       |
|    SPIM                            |SPI_SCHA634     |418    |197     |14      |387     |0       |0       |
|  POWER                             |POWER_EN        |102    |59      |38      |39      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |601    |399     |103     |408     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |601    |399     |103     |408     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |274    |184     |0       |257     |0       |0       |
|        reg_inst                    |register        |272    |182     |0       |255     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |327    |215     |103     |151     |0       |0       |
|        bus_inst                    |bus_top         |130    |84      |46      |48      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |24     |14      |10      |8       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |53     |35      |18      |21      |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |51     |33      |18      |17      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |126    |92      |29      |78      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13609  
    #2          2       3383   
    #3          3        680   
    #4          4        312   
    #5        5-10       962   
    #6        11-50      447   
    #7       51-100      10    
    #8       101-500      3    
    #9        >500        2    
  Average     2.14             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.216994s wall, 3.781250s user + 0.031250s system = 3.812500s CPU (172.0%)

RUN-1004 : used memory is 1062 MB, reserved memory is 1063 MB, peak memory is 1129 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67975, tnet num: 19471, tinst num: 8185, tnode num: 92195, tedge num: 112003.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.847044s wall, 1.843750s user + 0.000000s system = 1.843750s CPU (99.8%)

RUN-1004 : used memory is 1064 MB, reserved memory is 1064 MB, peak memory is 1129 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19471 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 4 (2 unconstrainted).
TMR-5009 WARNING: No clock constraint on 2 clock net(s): 
		FMC/dsp_out0_n_syn_4
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.498145s wall, 1.500000s user + 0.000000s system = 1.500000s CPU (100.1%)

RUN-1004 : used memory is 1069 MB, reserved memory is 1070 MB, peak memory is 1129 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 19285304e968e394d67a8f065444a1f66c49f77adeb419c41e6b7764d8228547 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8185
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19473, pip num: 147508
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 357
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3222 valid insts, and 413632 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011111000000101011000101
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.566687s wall, 125.453125s user + 0.218750s system = 125.671875s CPU (1000.0%)

RUN-1004 : used memory is 1193 MB, reserved memory is 1179 MB, peak memory is 1308 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250821_143518.log"

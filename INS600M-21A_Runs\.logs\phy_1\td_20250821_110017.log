============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Aug 21 11:00:17 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.580610s wall, 1.437500s user + 4.109375s system = 5.546875s CPU (99.4%)

RUN-1004 : used memory is 79 MB, reserved memory is 40 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.864269s wall, 1.812500s user + 0.062500s system = 1.875000s CPU (100.6%)

RUN-1004 : used memory is 302 MB, reserved memory is 270 MB, peak memory is 305 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 28 trigger nets, 28 data nets.
KIT-1004 : Chipwatcher code = 0010010110000101
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=28,BUS_CTRL_NUM=76,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01011,32'sb01100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100010,32'sb0101000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=98) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=98) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=28,BUS_CTRL_NUM=76,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01011,32'sb01100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100010,32'sb0101000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=28,BUS_CTRL_NUM=76,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01011,32'sb01100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100010,32'sb0101000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=28,BUS_CTRL_NUM=76,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01011,32'sb01100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100010,32'sb0101000})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=98)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=98)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=28,BUS_CTRL_NUM=76,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01011,32'sb01100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100010,32'sb0101000})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=28,BUS_CTRL_NUM=76,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01011,32'sb01100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100010,32'sb0101000})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22583/23 useful/useless nets, 19457/12 useful/useless insts
SYN-1016 : Merged 29 instances.
SYN-1032 : 22307/20 useful/useless nets, 19813/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 328 better
SYN-1014 : Optimize round 2
SYN-1032 : 22043/45 useful/useless nets, 19549/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.424250s wall, 2.359375s user + 0.062500s system = 2.421875s CPU (99.9%)

RUN-1004 : used memory is 328 MB, reserved memory is 294 MB, peak memory is 330 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22079/222 useful/useless nets, 19609/33 useful/useless insts
SYN-1016 : Merged 33 instances.
SYN-2571 : Optimize after map_dsp, round 1, 288 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 27 instances.
SYN-2501 : Optimize round 1, 55 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 15 instances.
SYN-1032 : 22464/5 useful/useless nets, 19994/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81580, tnet num: 22464, tinst num: 19993, tnode num: 114401, tedge num: 127556.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.235999s wall, 1.203125s user + 0.015625s system = 1.218750s CPU (98.6%)

RUN-1004 : used memory is 466 MB, reserved memory is 434 MB, peak memory is 466 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22464 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 194 (3.61), #lev = 7 (1.97)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 194 (3.61), #lev = 7 (1.97)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 467 instances into 194 LUTs, name keeping = 72%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 337 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 108 adder to BLE ...
SYN-4008 : Packed 108 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.647036s wall, 4.546875s user + 0.093750s system = 4.640625s CPU (99.9%)

RUN-1004 : used memory is 363 MB, reserved memory is 344 MB, peak memory is 574 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.419893s wall, 7.250000s user + 0.156250s system = 7.406250s CPU (99.8%)

RUN-1004 : used memory is 364 MB, reserved memory is 344 MB, peak memory is 574 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (215 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19283 instances
RUN-0007 : 5569 luts, 12135 seqs, 973 mslices, 515 lslices, 59 pads, 27 brams, 0 dsps
RUN-1001 : There are total 21761 nets
RUN-1001 : 16380 nets have 2 pins
RUN-1001 : 4207 nets have [3 - 5] pins
RUN-1001 : 811 nets have [6 - 10] pins
RUN-1001 : 237 nets have [11 - 20] pins
RUN-1001 : 105 nets have [21 - 99] pins
RUN-1001 : 21 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4789     
RUN-1001 :   No   |  No   |  Yes  |     690     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     387     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19281 instances, 5569 luts, 12135 seqs, 1488 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80073, tnet num: 21759, tinst num: 19281, tnode num: 112615, tedge num: 125925.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.235503s wall, 1.203125s user + 0.031250s system = 1.234375s CPU (99.9%)

RUN-1004 : used memory is 525 MB, reserved memory is 497 MB, peak memory is 574 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21759 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.170442s wall, 2.093750s user + 0.078125s system = 2.171875s CPU (100.1%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.56904e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19281.
PHY-3001 : Level 1 #clusters 2192.
PHY-3001 : End clustering;  0.153065s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (112.3%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 837361, overlap = 631.438
PHY-3002 : Step(2): len = 763564, overlap = 681.562
PHY-3002 : Step(3): len = 494236, overlap = 857.531
PHY-3002 : Step(4): len = 429738, overlap = 921.562
PHY-3002 : Step(5): len = 343006, overlap = 1019.59
PHY-3002 : Step(6): len = 311700, overlap = 1062.53
PHY-3002 : Step(7): len = 267767, overlap = 1100.28
PHY-3002 : Step(8): len = 241677, overlap = 1157.78
PHY-3002 : Step(9): len = 218772, overlap = 1207.28
PHY-3002 : Step(10): len = 201294, overlap = 1253.69
PHY-3002 : Step(11): len = 184215, overlap = 1287.03
PHY-3002 : Step(12): len = 169509, overlap = 1321.97
PHY-3002 : Step(13): len = 157340, overlap = 1343.31
PHY-3002 : Step(14): len = 146102, overlap = 1372.47
PHY-3002 : Step(15): len = 136589, overlap = 1398.22
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.1929e-06
PHY-3002 : Step(16): len = 137764, overlap = 1384.91
PHY-3002 : Step(17): len = 181135, overlap = 1276
PHY-3002 : Step(18): len = 193474, overlap = 1167.81
PHY-3002 : Step(19): len = 195912, overlap = 1104.66
PHY-3002 : Step(20): len = 189002, overlap = 1079.84
PHY-3002 : Step(21): len = 184103, overlap = 1052.03
PHY-3002 : Step(22): len = 178026, overlap = 1082.66
PHY-3002 : Step(23): len = 173144, overlap = 1095.5
PHY-3002 : Step(24): len = 169580, overlap = 1055.41
PHY-3002 : Step(25): len = 166210, overlap = 1050.12
PHY-3002 : Step(26): len = 164700, overlap = 1059.09
PHY-3002 : Step(27): len = 162936, overlap = 1077.94
PHY-3002 : Step(28): len = 162384, overlap = 1070.53
PHY-3002 : Step(29): len = 161258, overlap = 1078.25
PHY-3002 : Step(30): len = 160347, overlap = 1087.69
PHY-3002 : Step(31): len = 158754, overlap = 1088.84
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.38579e-06
PHY-3002 : Step(32): len = 168190, overlap = 1067.72
PHY-3002 : Step(33): len = 185512, overlap = 962.156
PHY-3002 : Step(34): len = 189062, overlap = 921.781
PHY-3002 : Step(35): len = 191295, overlap = 904.406
PHY-3002 : Step(36): len = 191068, overlap = 885.781
PHY-3002 : Step(37): len = 190930, overlap = 871.344
PHY-3002 : Step(38): len = 188635, overlap = 885.969
PHY-3002 : Step(39): len = 188075, overlap = 882.719
PHY-3002 : Step(40): len = 187275, overlap = 903.5
PHY-3002 : Step(41): len = 186518, overlap = 919.688
PHY-3002 : Step(42): len = 185717, overlap = 921.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.77159e-06
PHY-3002 : Step(43): len = 195896, overlap = 872.281
PHY-3002 : Step(44): len = 212830, overlap = 796.969
PHY-3002 : Step(45): len = 217966, overlap = 755.844
PHY-3002 : Step(46): len = 219687, overlap = 746.344
PHY-3002 : Step(47): len = 219358, overlap = 742.688
PHY-3002 : Step(48): len = 217622, overlap = 733.781
PHY-3002 : Step(49): len = 215911, overlap = 731.594
PHY-3002 : Step(50): len = 214576, overlap = 738.125
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 9.54317e-06
PHY-3002 : Step(51): len = 226801, overlap = 713.094
PHY-3002 : Step(52): len = 243316, overlap = 646.875
PHY-3002 : Step(53): len = 247668, overlap = 643.438
PHY-3002 : Step(54): len = 249097, overlap = 631.906
PHY-3002 : Step(55): len = 248157, overlap = 624.281
PHY-3002 : Step(56): len = 247299, overlap = 595.188
PHY-3002 : Step(57): len = 246031, overlap = 586.188
PHY-3002 : Step(58): len = 247096, overlap = 577.031
PHY-3002 : Step(59): len = 246761, overlap = 582.75
PHY-3002 : Step(60): len = 246089, overlap = 599.469
PHY-3002 : Step(61): len = 246624, overlap = 589.125
PHY-3002 : Step(62): len = 245852, overlap = 578.312
PHY-3002 : Step(63): len = 245853, overlap = 555.875
PHY-3002 : Step(64): len = 244374, overlap = 537.344
PHY-3002 : Step(65): len = 243958, overlap = 543.688
PHY-3002 : Step(66): len = 242587, overlap = 537.094
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.90863e-05
PHY-3002 : Step(67): len = 253965, overlap = 524.906
PHY-3002 : Step(68): len = 265001, overlap = 505.188
PHY-3002 : Step(69): len = 266334, overlap = 473.625
PHY-3002 : Step(70): len = 268493, overlap = 459.469
PHY-3002 : Step(71): len = 268355, overlap = 442.344
PHY-3002 : Step(72): len = 267663, overlap = 438.594
PHY-3002 : Step(73): len = 266865, overlap = 445.719
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.81727e-05
PHY-3002 : Step(74): len = 275617, overlap = 412.375
PHY-3002 : Step(75): len = 285673, overlap = 380.594
PHY-3002 : Step(76): len = 288699, overlap = 358.125
PHY-3002 : Step(77): len = 290703, overlap = 348.094
PHY-3002 : Step(78): len = 289452, overlap = 345.5
PHY-3002 : Step(79): len = 287877, overlap = 365.344
PHY-3002 : Step(80): len = 285944, overlap = 361.188
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 7.63454e-05
PHY-3002 : Step(81): len = 292762, overlap = 348.5
PHY-3002 : Step(82): len = 300419, overlap = 301.969
PHY-3002 : Step(83): len = 303626, overlap = 296.594
PHY-3002 : Step(84): len = 304827, overlap = 291.062
PHY-3002 : Step(85): len = 302609, overlap = 290.562
PHY-3002 : Step(86): len = 301100, overlap = 279.406
PHY-3002 : Step(87): len = 298870, overlap = 276.938
PHY-3002 : Step(88): len = 299257, overlap = 268.75
PHY-3002 : Step(89): len = 298496, overlap = 268.625
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000152691
PHY-3002 : Step(90): len = 303062, overlap = 264.031
PHY-3002 : Step(91): len = 309311, overlap = 245.719
PHY-3002 : Step(92): len = 311167, overlap = 223.188
PHY-3002 : Step(93): len = 312857, overlap = 224.531
PHY-3002 : Step(94): len = 312370, overlap = 214.688
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000305382
PHY-3002 : Step(95): len = 314782, overlap = 205.938
PHY-3002 : Step(96): len = 319843, overlap = 186.906
PHY-3002 : Step(97): len = 321260, overlap = 166.75
PHY-3002 : Step(98): len = 321952, overlap = 170.094
PHY-3002 : Step(99): len = 321118, overlap = 165.312
PHY-3002 : Step(100): len = 321253, overlap = 155.156
PHY-3002 : Step(101): len = 321344, overlap = 162.156
PHY-3002 : Step(102): len = 322005, overlap = 167.281
PHY-3002 : Step(103): len = 321274, overlap = 169.062
PHY-3002 : Step(104): len = 321745, overlap = 163.438
PHY-3002 : Step(105): len = 320675, overlap = 161.844
PHY-3002 : Step(106): len = 321240, overlap = 160.844
PHY-3002 : Step(107): len = 320378, overlap = 153.75
PHY-3002 : Step(108): len = 320254, overlap = 159.406
PHY-3002 : Step(109): len = 319606, overlap = 164.219
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000553125
PHY-3002 : Step(110): len = 321499, overlap = 167.406
PHY-3002 : Step(111): len = 323369, overlap = 169.812
PHY-3001 : :::11::: Try harder cell spreading with beta_ = 0.000894956
PHY-3002 : Step(112): len = 323927, overlap = 167.562
PHY-3002 : Step(113): len = 325780, overlap = 166.25
PHY-3002 : Step(114): len = 326977, overlap = 167.438
PHY-3002 : Step(115): len = 328680, overlap = 172.156
PHY-3002 : Step(116): len = 329081, overlap = 179.938
PHY-3002 : Step(117): len = 329399, overlap = 181.062
PHY-3002 : Step(118): len = 329490, overlap = 182.844
PHY-3002 : Step(119): len = 329505, overlap = 179.281
PHY-3002 : Step(120): len = 328980, overlap = 180.156
PHY-3002 : Step(121): len = 329634, overlap = 173.625
PHY-3002 : Step(122): len = 329700, overlap = 167.5
PHY-3001 : :::12::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(123): len = 329877, overlap = 166.562
PHY-3002 : Step(124): len = 330575, overlap = 165.438
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.017076s wall, 0.015625s user + 0.046875s system = 0.062500s CPU (366.0%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21761.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 429080, over cnt = 1159(3%), over = 5460, worst = 40
PHY-1001 : End global iterations;  0.849319s wall, 1.109375s user + 0.031250s system = 1.140625s CPU (134.3%)

PHY-1001 : Congestion index: top1 = 73.71, top5 = 52.12, top10 = 42.21, top15 = 36.64.
PHY-3001 : End congestion estimation;  1.108999s wall, 1.359375s user + 0.046875s system = 1.406250s CPU (126.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21759 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.995389s wall, 0.953125s user + 0.031250s system = 0.984375s CPU (98.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000114733
PHY-3002 : Step(125): len = 376571, overlap = 146.875
PHY-3002 : Step(126): len = 388089, overlap = 136.125
PHY-3002 : Step(127): len = 389486, overlap = 122.625
PHY-3002 : Step(128): len = 387788, overlap = 120.906
PHY-3002 : Step(129): len = 393080, overlap = 121.594
PHY-3002 : Step(130): len = 400680, overlap = 124.156
PHY-3002 : Step(131): len = 403624, overlap = 127.094
PHY-3002 : Step(132): len = 407109, overlap = 126.219
PHY-3002 : Step(133): len = 413090, overlap = 131.625
PHY-3002 : Step(134): len = 419067, overlap = 134.625
PHY-3002 : Step(135): len = 421050, overlap = 137.312
PHY-3002 : Step(136): len = 423153, overlap = 133.156
PHY-3002 : Step(137): len = 426344, overlap = 132.219
PHY-3002 : Step(138): len = 427794, overlap = 131.531
PHY-3002 : Step(139): len = 429611, overlap = 132.875
PHY-3002 : Step(140): len = 433281, overlap = 134.469
PHY-3002 : Step(141): len = 434666, overlap = 136.719
PHY-3002 : Step(142): len = 435181, overlap = 134.219
PHY-3002 : Step(143): len = 437455, overlap = 138.469
PHY-3002 : Step(144): len = 436862, overlap = 139.312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000229465
PHY-3002 : Step(145): len = 437730, overlap = 133.719
PHY-3002 : Step(146): len = 439455, overlap = 129.688
PHY-3002 : Step(147): len = 441634, overlap = 129.969
PHY-3002 : Step(148): len = 443523, overlap = 133.812
PHY-3002 : Step(149): len = 445217, overlap = 135.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000458931
PHY-3002 : Step(150): len = 446647, overlap = 135.094
PHY-3002 : Step(151): len = 450750, overlap = 138.094
PHY-3002 : Step(152): len = 458417, overlap = 140.594
PHY-3002 : Step(153): len = 467029, overlap = 133.938
PHY-3002 : Step(154): len = 466366, overlap = 134.406
PHY-3002 : Step(155): len = 465422, overlap = 137.781
PHY-3002 : Step(156): len = 464537, overlap = 136.188
PHY-3002 : Step(157): len = 463940, overlap = 128.062
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 74/21761.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 523896, over cnt = 2163(6%), over = 9930, worst = 45
PHY-1001 : End global iterations;  1.136396s wall, 1.765625s user + 0.046875s system = 1.812500s CPU (159.5%)

PHY-1001 : Congestion index: top1 = 74.29, top5 = 58.83, top10 = 49.33, top15 = 44.27.
PHY-3001 : End congestion estimation;  1.442820s wall, 2.062500s user + 0.046875s system = 2.109375s CPU (146.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21759 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.052965s wall, 1.046875s user + 0.015625s system = 1.062500s CPU (100.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000103617
PHY-3002 : Step(158): len = 470228, overlap = 375.969
PHY-3002 : Step(159): len = 473760, overlap = 314.438
PHY-3002 : Step(160): len = 470514, overlap = 276.156
PHY-3002 : Step(161): len = 467152, overlap = 268.906
PHY-3002 : Step(162): len = 463739, overlap = 259.938
PHY-3002 : Step(163): len = 461937, overlap = 249.25
PHY-3002 : Step(164): len = 459773, overlap = 252.344
PHY-3002 : Step(165): len = 457944, overlap = 248.031
PHY-3002 : Step(166): len = 457934, overlap = 246.906
PHY-3002 : Step(167): len = 454830, overlap = 248.406
PHY-3002 : Step(168): len = 454197, overlap = 234.406
PHY-3002 : Step(169): len = 452912, overlap = 227.906
PHY-3002 : Step(170): len = 450465, overlap = 230.656
PHY-3002 : Step(171): len = 450177, overlap = 221.312
PHY-3002 : Step(172): len = 448678, overlap = 231.438
PHY-3002 : Step(173): len = 445870, overlap = 228.562
PHY-3002 : Step(174): len = 445068, overlap = 230.312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000207233
PHY-3002 : Step(175): len = 445687, overlap = 231.875
PHY-3002 : Step(176): len = 449083, overlap = 220.906
PHY-3002 : Step(177): len = 452437, overlap = 217.594
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000414467
PHY-3002 : Step(178): len = 456167, overlap = 207.688
PHY-3002 : Step(179): len = 463399, overlap = 181.844
PHY-3002 : Step(180): len = 470998, overlap = 168.188
PHY-3002 : Step(181): len = 472764, overlap = 165.625
PHY-3002 : Step(182): len = 472059, overlap = 162.75
PHY-3002 : Step(183): len = 471103, overlap = 159.312
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000824886
PHY-3002 : Step(184): len = 472795, overlap = 154.375
PHY-3002 : Step(185): len = 477830, overlap = 145.719
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80073, tnet num: 21759, tinst num: 19281, tnode num: 112615, tedge num: 125925.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.616035s wall, 1.562500s user + 0.046875s system = 1.609375s CPU (99.6%)

RUN-1004 : used memory is 565 MB, reserved memory is 540 MB, peak memory is 696 MB
OPT-1001 : Total overflow 517.84 peak overflow 3.72
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 297/21761.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 551856, over cnt = 2410(6%), over = 8755, worst = 27
PHY-1001 : End global iterations;  1.252271s wall, 1.796875s user + 0.046875s system = 1.843750s CPU (147.2%)

PHY-1001 : Congestion index: top1 = 58.25, top5 = 49.21, top10 = 44.18, top15 = 40.97.
PHY-1001 : End incremental global routing;  1.518839s wall, 2.062500s user + 0.046875s system = 2.109375s CPU (138.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21759 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.061679s wall, 1.046875s user + 0.015625s system = 1.062500s CPU (100.1%)

OPT-1001 : 16 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19203 has valid locations, 230 needs to be replaced
PHY-3001 : design contains 19495 instances, 5663 luts, 12255 seqs, 1488 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 492272
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17243/21975.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 563544, over cnt = 2445(6%), over = 8850, worst = 27
PHY-1001 : End global iterations;  0.196174s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (143.4%)

PHY-1001 : Congestion index: top1 = 58.23, top5 = 49.36, top10 = 44.39, top15 = 41.21.
PHY-3001 : End congestion estimation;  0.457948s wall, 0.515625s user + 0.015625s system = 0.531250s CPU (116.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80778, tnet num: 21973, tinst num: 19495, tnode num: 113597, tedge num: 126907.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.580343s wall, 1.562500s user + 0.015625s system = 1.578125s CPU (99.9%)

RUN-1004 : used memory is 610 MB, reserved memory is 603 MB, peak memory is 701 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21973 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.682641s wall, 2.656250s user + 0.031250s system = 2.687500s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(186): len = 491886, overlap = 3.3125
PHY-3002 : Step(187): len = 492556, overlap = 3.25
PHY-3002 : Step(188): len = 493429, overlap = 3.3125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17277/21975.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 561800, over cnt = 2479(7%), over = 8947, worst = 27
PHY-1001 : End global iterations;  0.196452s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (127.3%)

PHY-1001 : Congestion index: top1 = 58.81, top5 = 49.65, top10 = 44.64, top15 = 41.44.
PHY-3001 : End congestion estimation;  0.452539s wall, 0.500000s user + 0.000000s system = 0.500000s CPU (110.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21973 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.047020s wall, 1.031250s user + 0.015625s system = 1.046875s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000757955
PHY-3002 : Step(189): len = 493560, overlap = 148.719
PHY-3002 : Step(190): len = 493934, overlap = 149.438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00151591
PHY-3002 : Step(191): len = 494289, overlap = 149.25
PHY-3002 : Step(192): len = 494835, overlap = 148.719
PHY-3001 : Final: Len = 494835, Over = 148.719
PHY-3001 : End incremental placement;  5.531746s wall, 6.234375s user + 0.281250s system = 6.515625s CPU (117.8%)

OPT-1001 : Total overflow 522.25 peak overflow 3.72
OPT-1001 : End high-fanout net optimization;  8.660165s wall, 10.062500s user + 0.343750s system = 10.406250s CPU (120.2%)

OPT-1001 : Current memory(MB): used = 705, reserve = 685, peak = 721.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17285/21975.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 564656, over cnt = 2431(6%), over = 8444, worst = 27
PHY-1002 : len = 606648, over cnt = 1731(4%), over = 4523, worst = 27
PHY-1002 : len = 637552, over cnt = 814(2%), over = 2177, worst = 19
PHY-1002 : len = 665088, over cnt = 306(0%), over = 593, worst = 9
PHY-1002 : len = 674456, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.325517s wall, 1.968750s user + 0.015625s system = 1.984375s CPU (149.7%)

PHY-1001 : Congestion index: top1 = 50.13, top5 = 44.20, top10 = 40.96, top15 = 38.92.
OPT-1001 : End congestion update;  1.595283s wall, 2.234375s user + 0.015625s system = 2.250000s CPU (141.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 21973 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.912414s wall, 0.906250s user + 0.000000s system = 0.906250s CPU (99.3%)

OPT-0007 : Start: WNS 4287 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.514174s wall, 3.156250s user + 0.015625s system = 3.171875s CPU (126.2%)

OPT-1001 : Current memory(MB): used = 702, reserve = 681, peak = 721.
OPT-1001 : End physical optimization;  13.121981s wall, 15.218750s user + 0.421875s system = 15.640625s CPU (119.2%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5663 LUT to BLE ...
SYN-4008 : Packed 5663 LUT and 2730 SEQ to BLE.
SYN-4003 : Packing 9525 remaining SEQ's ...
SYN-4005 : Packed 3352 SEQ with LUT/SLICE
SYN-4006 : 88 single LUT's are left
SYN-4006 : 6173 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11836/13679 primitive instances ...
PHY-3001 : End packing;  2.900086s wall, 2.875000s user + 0.000000s system = 2.875000s CPU (99.1%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8026 instances
RUN-1001 : 3968 mslices, 3967 lslices, 59 pads, 27 brams, 0 dsps
RUN-1001 : There are total 19299 nets
RUN-1001 : 13574 nets have 2 pins
RUN-1001 : 4331 nets have [3 - 5] pins
RUN-1001 : 887 nets have [6 - 10] pins
RUN-1001 : 370 nets have [11 - 20] pins
RUN-1001 : 128 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8024 instances, 7935 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 510248, Over = 364.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7825/19299.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 640208, over cnt = 1539(4%), over = 2446, worst = 8
PHY-1002 : len = 646448, over cnt = 1020(2%), over = 1403, worst = 5
PHY-1002 : len = 657272, over cnt = 398(1%), over = 533, worst = 5
PHY-1002 : len = 664576, over cnt = 63(0%), over = 88, worst = 5
PHY-1002 : len = 666256, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.231297s wall, 2.046875s user + 0.109375s system = 2.156250s CPU (175.1%)

PHY-1001 : Congestion index: top1 = 51.38, top5 = 44.64, top10 = 41.23, top15 = 38.92.
PHY-3001 : End congestion estimation;  1.568667s wall, 2.406250s user + 0.109375s system = 2.515625s CPU (160.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66907, tnet num: 19297, tinst num: 8024, tnode num: 90725, tedge num: 110341.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.747899s wall, 1.734375s user + 0.015625s system = 1.750000s CPU (100.1%)

RUN-1004 : used memory is 598 MB, reserved memory is 581 MB, peak memory is 721 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19297 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.735327s wall, 2.718750s user + 0.015625s system = 2.734375s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.14998e-05
PHY-3002 : Step(193): len = 514014, overlap = 343.75
PHY-3002 : Step(194): len = 512155, overlap = 350.75
PHY-3002 : Step(195): len = 512288, overlap = 359
PHY-3002 : Step(196): len = 512138, overlap = 358.25
PHY-3002 : Step(197): len = 511719, overlap = 370.5
PHY-3002 : Step(198): len = 510490, overlap = 378.25
PHY-3002 : Step(199): len = 509697, overlap = 387
PHY-3002 : Step(200): len = 507282, overlap = 391.75
PHY-3002 : Step(201): len = 505293, overlap = 400.25
PHY-3002 : Step(202): len = 503034, overlap = 407
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000103
PHY-3002 : Step(203): len = 508164, overlap = 392.5
PHY-3002 : Step(204): len = 512887, overlap = 389.25
PHY-3002 : Step(205): len = 513230, overlap = 378
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000205999
PHY-3002 : Step(206): len = 520000, overlap = 368.75
PHY-3002 : Step(207): len = 529810, overlap = 337.75
PHY-3002 : Step(208): len = 528878, overlap = 334.75
PHY-3002 : Step(209): len = 527510, overlap = 337.25
PHY-3002 : Step(210): len = 527022, overlap = 339.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.836999s wall, 0.890625s user + 1.000000s system = 1.890625s CPU (225.9%)

PHY-3001 : Trial Legalized: Len = 633209
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 632/19299.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 719912, over cnt = 2287(6%), over = 3713, worst = 8
PHY-1002 : len = 734272, over cnt = 1386(3%), over = 1936, worst = 6
PHY-1002 : len = 749112, over cnt = 602(1%), over = 814, worst = 6
PHY-1002 : len = 759080, over cnt = 140(0%), over = 201, worst = 4
PHY-1002 : len = 762504, over cnt = 16(0%), over = 16, worst = 1
PHY-1001 : End global iterations;  1.846077s wall, 3.265625s user + 0.031250s system = 3.296875s CPU (178.6%)

PHY-1001 : Congestion index: top1 = 49.91, top5 = 45.04, top10 = 42.39, top15 = 40.65.
PHY-3001 : End congestion estimation;  2.217685s wall, 3.640625s user + 0.031250s system = 3.671875s CPU (165.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19297 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.940199s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00016842
PHY-3002 : Step(211): len = 591664, overlap = 86.25
PHY-3002 : Step(212): len = 574234, overlap = 120.5
PHY-3002 : Step(213): len = 563778, overlap = 162.5
PHY-3002 : Step(214): len = 557060, overlap = 201
PHY-3002 : Step(215): len = 553368, overlap = 217
PHY-3002 : Step(216): len = 550835, overlap = 232
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000336841
PHY-3002 : Step(217): len = 554998, overlap = 223.75
PHY-3002 : Step(218): len = 558705, overlap = 220
PHY-3002 : Step(219): len = 558855, overlap = 217
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(220): len = 561870, overlap = 214
PHY-3002 : Step(221): len = 568658, overlap = 218.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.035315s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (88.5%)

PHY-3001 : Legalized: Len = 608523, Over = 0
PHY-3001 : Spreading special nets. 35 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.084028s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (93.0%)

PHY-3001 : 52 instances has been re-located, deltaX = 10, deltaY = 36, maxDist = 2.
PHY-3001 : Final: Len = 609615, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66907, tnet num: 19297, tinst num: 8024, tnode num: 90725, tedge num: 110341.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.068219s wall, 2.046875s user + 0.015625s system = 2.062500s CPU (99.7%)

RUN-1004 : used memory is 604 MB, reserved memory is 599 MB, peak memory is 721 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 4005/19299.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 705536, over cnt = 2153(6%), over = 3343, worst = 7
PHY-1002 : len = 715536, over cnt = 1360(3%), over = 1896, worst = 6
PHY-1002 : len = 727112, over cnt = 776(2%), over = 1032, worst = 6
PHY-1002 : len = 737568, over cnt = 249(0%), over = 334, worst = 6
PHY-1002 : len = 742272, over cnt = 8(0%), over = 9, worst = 2
PHY-1001 : End global iterations;  1.829583s wall, 2.984375s user + 0.015625s system = 3.000000s CPU (164.0%)

PHY-1001 : Congestion index: top1 = 48.12, top5 = 43.24, top10 = 40.71, top15 = 39.03.
PHY-1001 : End incremental global routing;  2.167020s wall, 3.312500s user + 0.015625s system = 3.328125s CPU (153.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19297 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.978941s wall, 0.984375s user + 0.000000s system = 0.984375s CPU (100.6%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 7961 has valid locations, 12 needs to be replaced
PHY-3001 : design contains 8035 instances, 7946 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 611193
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17420/19309.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 743976, over cnt = 26(0%), over = 28, worst = 2
PHY-1002 : len = 743928, over cnt = 10(0%), over = 10, worst = 1
PHY-1002 : len = 744032, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 744048, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.551094s wall, 0.562500s user + 0.000000s system = 0.562500s CPU (102.1%)

PHY-1001 : Congestion index: top1 = 48.15, top5 = 43.27, top10 = 40.74, top15 = 39.06.
PHY-3001 : End congestion estimation;  0.870679s wall, 0.875000s user + 0.000000s system = 0.875000s CPU (100.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66950, tnet num: 19307, tinst num: 8035, tnode num: 90779, tedge num: 110394.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.966767s wall, 1.953125s user + 0.000000s system = 1.953125s CPU (99.3%)

RUN-1004 : used memory is 667 MB, reserved memory is 648 MB, peak memory is 721 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19307 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.958557s wall, 2.921875s user + 0.031250s system = 2.953125s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(222): len = 611197, overlap = 0.25
PHY-3002 : Step(223): len = 611186, overlap = 0.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17419/19309.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 743504, over cnt = 15(0%), over = 17, worst = 2
PHY-1002 : len = 743544, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 743568, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 743608, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 743624, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.702201s wall, 0.718750s user + 0.031250s system = 0.750000s CPU (106.8%)

PHY-1001 : Congestion index: top1 = 48.36, top5 = 43.34, top10 = 40.77, top15 = 39.08.
PHY-3001 : End congestion estimation;  1.020054s wall, 1.031250s user + 0.031250s system = 1.062500s CPU (104.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19307 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.948373s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (100.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000565982
PHY-3002 : Step(224): len = 611167, overlap = 1.5
PHY-3002 : Step(225): len = 611175, overlap = 1.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006876s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (227.2%)

PHY-3001 : Legalized: Len = 611187, Over = 0
PHY-3001 : End spreading;  0.070617s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (110.6%)

PHY-3001 : Final: Len = 611187, Over = 0
PHY-3001 : End incremental placement;  6.454623s wall, 6.453125s user + 0.140625s system = 6.593750s CPU (102.2%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.113486s wall, 11.437500s user + 0.156250s system = 11.593750s CPU (114.6%)

OPT-1001 : Current memory(MB): used = 713, reserve = 695, peak = 721.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17419/19309.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 743640, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 743600, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 743616, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 743616, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.517338s wall, 0.531250s user + 0.000000s system = 0.531250s CPU (102.7%)

PHY-1001 : Congestion index: top1 = 48.08, top5 = 43.24, top10 = 40.70, top15 = 39.02.
OPT-1001 : End congestion update;  0.829296s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (101.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19307 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.807458s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (100.6%)

OPT-0007 : Start: WNS 4043 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.642219s wall, 1.656250s user + 0.000000s system = 1.656250s CPU (100.9%)

OPT-1001 : Current memory(MB): used = 713, reserve = 695, peak = 721.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19307 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.805225s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (100.9%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17431/19309.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 743616, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.119084s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (105.0%)

PHY-1001 : Congestion index: top1 = 48.08, top5 = 43.24, top10 = 40.70, top15 = 39.02.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19307 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.800413s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (101.5%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4043 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 47.551724
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4043ps with logic level 8 
RUN-1001 :       #2 path slack 4118ps with logic level 8 
OPT-1001 : End physical optimization;  16.128527s wall, 17.453125s user + 0.171875s system = 17.625000s CPU (109.3%)

RUN-1003 : finish command "place" in  69.080481s wall, 129.531250s user + 8.312500s system = 137.843750s CPU (199.5%)

RUN-1004 : used memory is 630 MB, reserved memory is 607 MB, peak memory is 721 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.673064s wall, 2.828125s user + 0.046875s system = 2.875000s CPU (171.8%)

RUN-1004 : used memory is 630 MB, reserved memory is 608 MB, peak memory is 721 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8037 instances
RUN-1001 : 3979 mslices, 3967 lslices, 59 pads, 27 brams, 0 dsps
RUN-1001 : There are total 19309 nets
RUN-1001 : 13574 nets have 2 pins
RUN-1001 : 4333 nets have [3 - 5] pins
RUN-1001 : 891 nets have [6 - 10] pins
RUN-1001 : 375 nets have [11 - 20] pins
RUN-1001 : 127 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66950, tnet num: 19307, tinst num: 8035, tnode num: 90779, tedge num: 110394.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.763767s wall, 1.765625s user + 0.000000s system = 1.765625s CPU (100.1%)

RUN-1004 : used memory is 639 MB, reserved memory is 627 MB, peak memory is 721 MB
PHY-1001 : 3979 mslices, 3967 lslices, 59 pads, 27 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19307 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 686088, over cnt = 2266(6%), over = 3718, worst = 8
PHY-1002 : len = 702368, over cnt = 1348(3%), over = 1859, worst = 6
PHY-1002 : len = 718624, over cnt = 454(1%), over = 582, worst = 6
PHY-1002 : len = 727344, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 727440, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.867000s wall, 3.203125s user + 0.062500s system = 3.265625s CPU (174.9%)

PHY-1001 : Congestion index: top1 = 47.65, top5 = 42.71, top10 = 40.11, top15 = 38.47.
PHY-1001 : End global routing;  2.220075s wall, 3.531250s user + 0.078125s system = 3.609375s CPU (162.6%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 705, reserve = 690, peak = 721.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 968, reserve = 953, peak = 968.
PHY-1001 : End build detailed router design. 4.665526s wall, 4.609375s user + 0.046875s system = 4.656250s CPU (99.8%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 190904, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.950555s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 1005, reserve = 990, peak = 1005.
PHY-1001 : End phase 1; 0.958274s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (101.1%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.67845e+06, over cnt = 1237(0%), over = 1241, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1019, reserve = 1006, peak = 1019.
PHY-1001 : End initial routed; 16.638700s wall, 44.625000s user + 0.421875s system = 45.046875s CPU (270.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18049(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.788   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.521947s wall, 3.515625s user + 0.000000s system = 3.515625s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 1031, reserve = 1019, peak = 1031.
PHY-1001 : End phase 2; 20.160825s wall, 48.140625s user + 0.421875s system = 48.562500s CPU (240.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.67845e+06, over cnt = 1237(0%), over = 1241, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.253988s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (104.6%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.67001e+06, over cnt = 435(0%), over = 435, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.620168s wall, 1.140625s user + 0.000000s system = 1.140625s CPU (183.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.66969e+06, over cnt = 74(0%), over = 74, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.375571s wall, 0.593750s user + 0.000000s system = 0.593750s CPU (158.1%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.67021e+06, over cnt = 19(0%), over = 19, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.230869s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (94.8%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.67025e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 4; 0.196008s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (111.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18049(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.788   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.562887s wall, 3.562500s user + 0.000000s system = 3.562500s CPU (100.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 281 feed throughs used by 257 nets
PHY-1001 : End commit to database; 2.201162s wall, 2.187500s user + 0.015625s system = 2.203125s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1116, reserve = 1106, peak = 1116.
PHY-1001 : End phase 3; 7.955052s wall, 8.656250s user + 0.015625s system = 8.671875s CPU (109.0%)

PHY-1003 : Routed, final wirelength = 1.67025e+06
PHY-1001 : Current memory(MB): used = 1120, reserve = 1110, peak = 1120.
PHY-1001 : End export database. 0.062023s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.8%)

PHY-1001 : End detail routing;  34.229689s wall, 62.859375s user + 0.500000s system = 63.359375s CPU (185.1%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66950, tnet num: 19307, tinst num: 8035, tnode num: 90779, tedge num: 110394.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.731460s wall, 1.718750s user + 0.000000s system = 1.718750s CPU (99.3%)

RUN-1004 : used memory is 1049 MB, reserved memory is 1044 MB, peak memory is 1120 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  42.641439s wall, 72.578125s user + 0.578125s system = 73.156250s CPU (171.6%)

RUN-1004 : used memory is 1047 MB, reserved memory is 1045 MB, peak memory is 1120 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8784   out of  19600   44.82%
#reg                    12349   out of  19600   63.01%
#le                     14904
  #lut only              2555   out of  14904   17.14%
  #reg only              6120   out of  14904   41.06%
  #lut&reg               6229   out of  14904   41.79%
#dsp                        0   out of     29    0.00%
#bram                      27   out of     64   42.19%
  #bram9k                  25
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       59   out of    188   31.38%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6753
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          127
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         F2        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         D1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         B3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         C1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         A3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         A2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         B2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         B1        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F15        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDZ        INPUT        K15        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         P2        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         N4        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         M5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         N6        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         N5        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         P5        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M3        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         M4        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         H2        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         M1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         L1        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         N1        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         N3        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         M2        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT         T5        LVCMOS33           8            NONE           OREG       
    Fog_ENY        OUTPUT         T4        LVCMOS33           8            NONE           OREG       
    Fog_ENZ        OUTPUT         T7        LVCMOS33           8            NONE           OREG       
    Fog_INT        OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         T6        LVCMOS33           8            N/A            OREG       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E1        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        G11        LVCMOS25           8            N/A            NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |14904  |7296    |1488    |12393   |27      |0       |
|  AnyFog_dataX                      |AnyFog          |231    |122     |22      |171     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |112    |90      |22      |52      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |208    |71      |22      |170     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |54      |22      |48      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |212    |79      |22      |175     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |90     |58      |22      |53      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2926   |733     |39      |2847    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |62     |37      |5       |54      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |211    |70      |5       |201     |0       |0       |
|    STADOP_com2                     |STADOP          |543    |187     |0       |542     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |64     |42      |14      |40      |0       |0       |
|    head_com2                       |uniheading      |274    |76      |5       |258     |0       |0       |
|    rmc_com2                        |Gprmc           |38     |32      |0       |33      |0       |0       |
|    uart_com2                       |Agrica          |1421   |266     |10      |1406    |0       |0       |
|  COM3                              |COM3_Control    |274    |132     |19      |237     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |61     |37      |5       |53      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |62     |40      |14      |40      |0       |0       |
|    rmc_com3                        |Gprmc           |151    |55      |0       |144     |0       |0       |
|  DATA                              |Data_Processing |8846   |4520    |1122    |7051    |0       |0       |
|    DIV_Dtemp                       |Divider         |798    |322     |84      |674     |0       |0       |
|    DIV_Utemp                       |Divider         |627    |310     |84      |506     |0       |0       |
|    DIV_accX                        |Divider         |573    |271     |84      |446     |0       |0       |
|    DIV_accY                        |Divider         |647    |325     |102     |491     |0       |0       |
|    DIV_accZ                        |Divider         |658    |400     |132     |452     |0       |0       |
|    DIV_rateX                       |Divider         |677    |460     |132     |469     |0       |0       |
|    DIV_rateY                       |Divider         |553    |361     |132     |348     |0       |0       |
|    DIV_rateZ                       |Divider         |635    |400     |132     |428     |0       |0       |
|    genclk                          |genclk          |262    |158     |89      |100     |0       |0       |
|  FMC                               |FMC_Ctrl        |412    |365     |43      |327     |0       |0       |
|  IIC                               |I2C_master      |285    |250     |11      |262     |0       |0       |
|  IMU_CTRL                          |SCHA634         |909    |669     |61      |734     |0       |0       |
|    CtrlData                        |CtrlData        |464    |411     |47      |327     |0       |0       |
|      usms                          |Time_1ms        |29     |24      |5       |21      |0       |0       |
|    SPIM                            |SPI_SCHA634     |445    |258     |14      |407     |0       |0       |
|  POWER                             |POWER_EN        |98     |51      |38      |38      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |498    |304     |89      |332     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |498    |304     |89      |332     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |215    |117     |0       |202     |0       |0       |
|        reg_inst                    |register        |213    |115     |0       |200     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |283    |187     |89      |130     |0       |0       |
|        bus_inst                    |bus_top         |79     |51      |28      |30      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |26     |16      |10      |10      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |50     |32      |18      |17      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |124    |92      |29      |75      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13514  
    #2          2       3394   
    #3          3        638   
    #4          4        301   
    #5        5-10       944   
    #6        11-50      440   
    #7       51-100       9    
    #8       101-500      3    
    #9        >500        2    
  Average     2.11             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.080776s wall, 3.500000s user + 0.031250s system = 3.531250s CPU (169.7%)

RUN-1004 : used memory is 1048 MB, reserved memory is 1045 MB, peak memory is 1120 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66950, tnet num: 19307, tinst num: 8035, tnode num: 90779, tedge num: 110394.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.825391s wall, 1.828125s user + 0.000000s system = 1.828125s CPU (100.1%)

RUN-1004 : used memory is 1050 MB, reserved memory is 1047 MB, peak memory is 1120 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19307 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.462225s wall, 1.437500s user + 0.015625s system = 1.453125s CPU (99.4%)

RUN-1004 : used memory is 1054 MB, reserved memory is 1050 MB, peak memory is 1120 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: f93483d4eefe0fc495c71cfd33cdfdfd7fe3ec43aef27c1d437b0386330ceaf7 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8035
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19309, pip num: 144753
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 281
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3234 valid insts, and 406400 bits set as '1'.
BIT-1004 : the usercode register value: 00000000000010010010010110000101
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.385123s wall, 124.781250s user + 0.093750s system = 124.875000s CPU (1008.3%)

RUN-1004 : used memory is 1179 MB, reserved memory is 1163 MB, peak memory is 1293 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250821_110017.log"

============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Aug 21 11:17:23 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  6.095936s wall, 1.796875s user + 4.265625s system = 6.062500s CPU (99.5%)

RUN-1004 : used memory is 79 MB, reserved memory is 41 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  2.068971s wall, 1.937500s user + 0.125000s system = 2.062500s CPU (99.7%)

RUN-1004 : used memory is 302 MB, reserved memory is 270 MB, peak memory is 305 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 8 view nodes, 34 trigger nets, 34 data nets.
KIT-1004 : Chipwatcher code = 0000011010100101
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_DIN_NUM=34,BUS_CTRL_NUM=100,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb011,32'sb011,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01110,32'sb010001,32'sb010010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110000,32'sb0111010,32'sb01000000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=122) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=122) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_DIN_NUM=34,BUS_CTRL_NUM=100,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb011,32'sb011,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01110,32'sb010001,32'sb010010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110000,32'sb0111010,32'sb01000000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_DIN_NUM=34,BUS_CTRL_NUM=100,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb011,32'sb011,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01110,32'sb010001,32'sb010010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110000,32'sb0111010,32'sb01000000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb011) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_DIN_NUM=34,BUS_CTRL_NUM=100,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb011,32'sb011,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01110,32'sb010001,32'sb010010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110000,32'sb0111010,32'sb01000000})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=122)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=122)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_DIN_NUM=34,BUS_CTRL_NUM=100,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb011,32'sb011,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01110,32'sb010001,32'sb010010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110000,32'sb0111010,32'sb01000000})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_DIN_NUM=34,BUS_CTRL_NUM=100,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb011,32'sb011,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01110,32'sb010001,32'sb010010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110000,32'sb0111010,32'sb01000000})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb011)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22816/34 useful/useless nets, 19588/20 useful/useless insts
SYN-1016 : Merged 41 instances.
SYN-1032 : 22480/24 useful/useless nets, 19986/20 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 4 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 4 mux instances.
SYN-1015 : Optimize round 1, 387 better
SYN-1014 : Optimize round 2
SYN-1032 : 22153/60 useful/useless nets, 19659/64 useful/useless insts
SYN-1015 : Optimize round 2, 128 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.590849s wall, 2.515625s user + 0.093750s system = 2.609375s CPU (100.7%)

RUN-1004 : used memory is 329 MB, reserved memory is 295 MB, peak memory is 330 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22189/228 useful/useless nets, 19725/39 useful/useless insts
SYN-1016 : Merged 33 instances.
SYN-2571 : Optimize after map_dsp, round 1, 300 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 30 instances.
SYN-2501 : Optimize round 1, 62 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 18 macro adder
SYN-1019 : Optimized 17 mux instances.
SYN-1016 : Merged 12 instances.
SYN-1032 : 22620/5 useful/useless nets, 20156/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82296, tnet num: 22620, tinst num: 20155, tnode num: 115301, tedge num: 128601.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.284338s wall, 1.281250s user + 0.000000s system = 1.281250s CPU (99.8%)

RUN-1004 : used memory is 468 MB, reserved memory is 436 MB, peak memory is 468 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22620 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 221 (3.54), #lev = 7 (1.93)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 221 (3.54), #lev = 7 (1.93)
SYN-3001 : Logic optimization runtime opt =   0.04 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 551 instances into 221 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 394 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 112 adder to BLE ...
SYN-4008 : Packed 112 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  5.025207s wall, 4.937500s user + 0.078125s system = 5.015625s CPU (99.8%)

RUN-1004 : used memory is 349 MB, reserved memory is 330 MB, peak memory is 576 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.966531s wall, 7.765625s user + 0.218750s system = 7.984375s CPU (100.2%)

RUN-1004 : used memory is 350 MB, reserved memory is 331 MB, peak memory is 576 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (261 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19376 instances
RUN-0007 : 5594 luts, 12189 seqs, 977 mslices, 519 lslices, 59 pads, 33 brams, 0 dsps
RUN-1001 : There are total 21848 nets
RUN-1001 : 16413 nets have 2 pins
RUN-1001 : 4251 nets have [3 - 5] pins
RUN-1001 : 816 nets have [6 - 10] pins
RUN-1001 : 242 nets have [11 - 20] pins
RUN-1001 : 104 nets have [21 - 99] pins
RUN-1001 : 22 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4786     
RUN-1001 :   No   |  No   |  Yes  |     701     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     433     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19374 instances, 5594 luts, 12189 seqs, 1496 slices, 293 macros(1496 instances: 977 mslices 519 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80610, tnet num: 21846, tinst num: 19374, tnode num: 113393, tedge num: 126824.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.276568s wall, 1.218750s user + 0.062500s system = 1.281250s CPU (100.4%)

RUN-1004 : used memory is 526 MB, reserved memory is 498 MB, peak memory is 576 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21846 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.229909s wall, 2.125000s user + 0.109375s system = 2.234375s CPU (100.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.54369e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19374.
PHY-3001 : Level 1 #clusters 2186.
PHY-3001 : End clustering;  0.166523s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (168.9%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 834869, overlap = 666.375
PHY-3002 : Step(2): len = 772716, overlap = 710.156
PHY-3002 : Step(3): len = 502547, overlap = 893.594
PHY-3002 : Step(4): len = 449644, overlap = 946
PHY-3002 : Step(5): len = 345346, overlap = 1053.59
PHY-3002 : Step(6): len = 309645, overlap = 1134.41
PHY-3002 : Step(7): len = 252247, overlap = 1193.75
PHY-3002 : Step(8): len = 230763, overlap = 1212.09
PHY-3002 : Step(9): len = 203550, overlap = 1235.75
PHY-3002 : Step(10): len = 191736, overlap = 1271.97
PHY-3002 : Step(11): len = 173906, overlap = 1322.38
PHY-3002 : Step(12): len = 165285, overlap = 1329.38
PHY-3002 : Step(13): len = 146579, overlap = 1359.59
PHY-3002 : Step(14): len = 140133, overlap = 1392
PHY-3002 : Step(15): len = 128136, overlap = 1428
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.12015e-06
PHY-3002 : Step(16): len = 130750, overlap = 1414.34
PHY-3002 : Step(17): len = 171979, overlap = 1339.97
PHY-3002 : Step(18): len = 183472, overlap = 1244.03
PHY-3002 : Step(19): len = 188854, overlap = 1161
PHY-3002 : Step(20): len = 184789, overlap = 1128.5
PHY-3002 : Step(21): len = 179144, overlap = 1102.03
PHY-3002 : Step(22): len = 173351, overlap = 1100.75
PHY-3002 : Step(23): len = 170955, overlap = 1077.84
PHY-3002 : Step(24): len = 167290, overlap = 1089.12
PHY-3002 : Step(25): len = 165168, overlap = 1106.78
PHY-3002 : Step(26): len = 163700, overlap = 1096.69
PHY-3002 : Step(27): len = 161899, overlap = 1078.78
PHY-3002 : Step(28): len = 161092, overlap = 1066.25
PHY-3002 : Step(29): len = 160853, overlap = 1066.62
PHY-3002 : Step(30): len = 160939, overlap = 1057.88
PHY-3002 : Step(31): len = 161139, overlap = 1059.41
PHY-3002 : Step(32): len = 160874, overlap = 1052.03
PHY-3002 : Step(33): len = 160288, overlap = 1062.19
PHY-3002 : Step(34): len = 159216, overlap = 1065.28
PHY-3002 : Step(35): len = 158775, overlap = 1054.91
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.2403e-06
PHY-3002 : Step(36): len = 165251, overlap = 1051.38
PHY-3002 : Step(37): len = 178737, overlap = 1003
PHY-3002 : Step(38): len = 180514, overlap = 969.344
PHY-3002 : Step(39): len = 181715, overlap = 961.25
PHY-3002 : Step(40): len = 181843, overlap = 952.594
PHY-3002 : Step(41): len = 181719, overlap = 957.656
PHY-3002 : Step(42): len = 180076, overlap = 968.781
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.48061e-06
PHY-3002 : Step(43): len = 190660, overlap = 917.812
PHY-3002 : Step(44): len = 203864, overlap = 864.656
PHY-3002 : Step(45): len = 209519, overlap = 830.594
PHY-3002 : Step(46): len = 211851, overlap = 783.812
PHY-3002 : Step(47): len = 212094, overlap = 774.094
PHY-3002 : Step(48): len = 210576, overlap = 766.688
PHY-3002 : Step(49): len = 209585, overlap = 765.594
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 8.96122e-06
PHY-3002 : Step(50): len = 222229, overlap = 715.062
PHY-3002 : Step(51): len = 235714, overlap = 640.938
PHY-3002 : Step(52): len = 239481, overlap = 623.625
PHY-3002 : Step(53): len = 242557, overlap = 607.125
PHY-3002 : Step(54): len = 242415, overlap = 584.094
PHY-3002 : Step(55): len = 241783, overlap = 582.031
PHY-3002 : Step(56): len = 240066, overlap = 574.062
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.79224e-05
PHY-3002 : Step(57): len = 251594, overlap = 539.406
PHY-3002 : Step(58): len = 265347, overlap = 519.688
PHY-3002 : Step(59): len = 268664, overlap = 483.219
PHY-3002 : Step(60): len = 271248, overlap = 457.625
PHY-3002 : Step(61): len = 269572, overlap = 473.938
PHY-3002 : Step(62): len = 268075, overlap = 462.75
PHY-3002 : Step(63): len = 265878, overlap = 471.812
PHY-3002 : Step(64): len = 264559, overlap = 481.875
PHY-3002 : Step(65): len = 263256, overlap = 476.031
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.58449e-05
PHY-3002 : Step(66): len = 272931, overlap = 436.25
PHY-3002 : Step(67): len = 283321, overlap = 412.188
PHY-3002 : Step(68): len = 285965, overlap = 376.312
PHY-3002 : Step(69): len = 287176, overlap = 367.562
PHY-3002 : Step(70): len = 286200, overlap = 377.219
PHY-3002 : Step(71): len = 283875, overlap = 375.406
PHY-3002 : Step(72): len = 281534, overlap = 371.531
PHY-3002 : Step(73): len = 280507, overlap = 380.25
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 7.16898e-05
PHY-3002 : Step(74): len = 286966, overlap = 364.344
PHY-3002 : Step(75): len = 292489, overlap = 347.969
PHY-3002 : Step(76): len = 294250, overlap = 322.656
PHY-3002 : Step(77): len = 295297, overlap = 320.875
PHY-3002 : Step(78): len = 293838, overlap = 321.469
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.00014338
PHY-3002 : Step(79): len = 297823, overlap = 317.688
PHY-3002 : Step(80): len = 304316, overlap = 295.25
PHY-3002 : Step(81): len = 307895, overlap = 278.406
PHY-3002 : Step(82): len = 309900, overlap = 258.875
PHY-3002 : Step(83): len = 309158, overlap = 258.625
PHY-3002 : Step(84): len = 307433, overlap = 276.062
PHY-3002 : Step(85): len = 306039, overlap = 262.688
PHY-3002 : Step(86): len = 306204, overlap = 261.156
PHY-3002 : Step(87): len = 305044, overlap = 256.156
PHY-3002 : Step(88): len = 304739, overlap = 261.812
PHY-3002 : Step(89): len = 303451, overlap = 262.156
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000264216
PHY-3002 : Step(90): len = 305297, overlap = 261.469
PHY-3002 : Step(91): len = 308770, overlap = 256.312
PHY-3002 : Step(92): len = 310007, overlap = 244.344
PHY-3002 : Step(93): len = 310869, overlap = 244.875
PHY-3002 : Step(94): len = 311140, overlap = 260.375
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000459079
PHY-3002 : Step(95): len = 311644, overlap = 249.656
PHY-3002 : Step(96): len = 313659, overlap = 246.094
PHY-3002 : Step(97): len = 316248, overlap = 242.719
PHY-3002 : Step(98): len = 319131, overlap = 227.719
PHY-3002 : Step(99): len = 319609, overlap = 221.719
PHY-3002 : Step(100): len = 319311, overlap = 217.156
PHY-3002 : Step(101): len = 318814, overlap = 205.5
PHY-3002 : Step(102): len = 318366, overlap = 196.188
PHY-3002 : Step(103): len = 318573, overlap = 182.531
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.016897s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (92.5%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21848.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 421824, over cnt = 1191(3%), over = 5350, worst = 33
PHY-1001 : End global iterations;  0.910028s wall, 1.171875s user + 0.031250s system = 1.203125s CPU (132.2%)

PHY-1001 : Congestion index: top1 = 71.72, top5 = 52.62, top10 = 43.08, top15 = 37.38.
PHY-3001 : End congestion estimation;  1.170572s wall, 1.421875s user + 0.031250s system = 1.453125s CPU (124.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21846 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.007578s wall, 1.000000s user + 0.015625s system = 1.015625s CPU (100.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.88105e-05
PHY-3002 : Step(104): len = 360319, overlap = 149.781
PHY-3002 : Step(105): len = 371448, overlap = 145.75
PHY-3002 : Step(106): len = 373140, overlap = 136.594
PHY-3002 : Step(107): len = 375455, overlap = 127.344
PHY-3002 : Step(108): len = 382128, overlap = 126.594
PHY-3002 : Step(109): len = 385952, overlap = 127.156
PHY-3002 : Step(110): len = 392551, overlap = 127.781
PHY-3002 : Step(111): len = 398567, overlap = 127.688
PHY-3002 : Step(112): len = 400715, overlap = 126.781
PHY-3002 : Step(113): len = 403596, overlap = 123.375
PHY-3002 : Step(114): len = 407780, overlap = 125.656
PHY-3002 : Step(115): len = 412738, overlap = 128.906
PHY-3002 : Step(116): len = 415633, overlap = 128.25
PHY-3002 : Step(117): len = 417999, overlap = 123.625
PHY-3002 : Step(118): len = 421433, overlap = 119.281
PHY-3002 : Step(119): len = 427448, overlap = 110
PHY-3002 : Step(120): len = 430426, overlap = 105.031
PHY-3002 : Step(121): len = 433944, overlap = 96.625
PHY-3002 : Step(122): len = 437811, overlap = 91.8438
PHY-3002 : Step(123): len = 439546, overlap = 87.8438
PHY-3002 : Step(124): len = 441535, overlap = 84.25
PHY-3002 : Step(125): len = 444940, overlap = 83.7188
PHY-3002 : Step(126): len = 445171, overlap = 81
PHY-3002 : Step(127): len = 445292, overlap = 80.9688
PHY-3002 : Step(128): len = 446708, overlap = 78.4375
PHY-3002 : Step(129): len = 447766, overlap = 77.9375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000197621
PHY-3002 : Step(130): len = 448643, overlap = 80.3438
PHY-3002 : Step(131): len = 449854, overlap = 78.7188
PHY-3002 : Step(132): len = 450539, overlap = 79.5625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000368391
PHY-3002 : Step(133): len = 456245, overlap = 81.4688
PHY-3002 : Step(134): len = 464865, overlap = 82.4062
PHY-3002 : Step(135): len = 465893, overlap = 86.5938
PHY-3002 : Step(136): len = 467228, overlap = 87.0625
PHY-3002 : Step(137): len = 469073, overlap = 88.4375
PHY-3002 : Step(138): len = 470120, overlap = 89.8125
PHY-3002 : Step(139): len = 471327, overlap = 94.0312
PHY-3002 : Step(140): len = 473421, overlap = 90.4062
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 64/21848.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 535144, over cnt = 2204(6%), over = 10363, worst = 43
PHY-1001 : End global iterations;  1.043109s wall, 1.953125s user + 0.031250s system = 1.984375s CPU (190.2%)

PHY-1001 : Congestion index: top1 = 76.81, top5 = 58.75, top10 = 50.42, top15 = 45.56.
PHY-3001 : End congestion estimation;  1.370813s wall, 2.281250s user + 0.031250s system = 2.312500s CPU (168.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21846 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.090372s wall, 1.062500s user + 0.031250s system = 1.093750s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000101307
PHY-3002 : Step(141): len = 482165, overlap = 343.719
PHY-3002 : Step(142): len = 490149, overlap = 291.25
PHY-3002 : Step(143): len = 480887, overlap = 268.625
PHY-3002 : Step(144): len = 476110, overlap = 250
PHY-3002 : Step(145): len = 472358, overlap = 239.969
PHY-3002 : Step(146): len = 468359, overlap = 241.094
PHY-3002 : Step(147): len = 466660, overlap = 231.219
PHY-3002 : Step(148): len = 463161, overlap = 214.469
PHY-3002 : Step(149): len = 461778, overlap = 206.188
PHY-3002 : Step(150): len = 460019, overlap = 209.906
PHY-3002 : Step(151): len = 458666, overlap = 221.562
PHY-3002 : Step(152): len = 455926, overlap = 222.375
PHY-3002 : Step(153): len = 455583, overlap = 222.719
PHY-3002 : Step(154): len = 452438, overlap = 226.469
PHY-3002 : Step(155): len = 450041, overlap = 227.5
PHY-3002 : Step(156): len = 448618, overlap = 229.906
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000202613
PHY-3002 : Step(157): len = 449378, overlap = 221.844
PHY-3002 : Step(158): len = 451058, overlap = 213.531
PHY-3002 : Step(159): len = 453529, overlap = 209.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000405227
PHY-3002 : Step(160): len = 455863, overlap = 210.281
PHY-3002 : Step(161): len = 462781, overlap = 198.188
PHY-3002 : Step(162): len = 469651, overlap = 182.938
PHY-3002 : Step(163): len = 471468, overlap = 181.188
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000810453
PHY-3002 : Step(164): len = 471697, overlap = 176.219
PHY-3002 : Step(165): len = 474872, overlap = 169.438
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80610, tnet num: 21846, tinst num: 19374, tnode num: 113393, tedge num: 126824.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.649652s wall, 1.625000s user + 0.046875s system = 1.671875s CPU (101.3%)

RUN-1004 : used memory is 568 MB, reserved memory is 543 MB, peak memory is 700 MB
OPT-1001 : Total overflow 539.59 peak overflow 4.56
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 364/21848.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 548512, over cnt = 2550(7%), over = 8820, worst = 20
PHY-1001 : End global iterations;  1.212513s wall, 2.000000s user + 0.093750s system = 2.093750s CPU (172.7%)

PHY-1001 : Congestion index: top1 = 60.78, top5 = 48.86, top10 = 43.66, top15 = 40.44.
PHY-1001 : End incremental global routing;  1.477115s wall, 2.265625s user + 0.093750s system = 2.359375s CPU (159.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21846 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.128397s wall, 1.125000s user + 0.000000s system = 1.125000s CPU (99.7%)

OPT-1001 : 17 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19295 has valid locations, 229 needs to be replaced
PHY-3001 : design contains 19586 instances, 5682 luts, 12313 seqs, 1496 slices, 293 macros(1496 instances: 977 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 488627
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17433/22060.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 559112, over cnt = 2579(7%), over = 8875, worst = 20
PHY-1001 : End global iterations;  0.195319s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (128.0%)

PHY-1001 : Congestion index: top1 = 60.80, top5 = 49.18, top10 = 43.94, top15 = 40.79.
PHY-3001 : End congestion estimation;  0.457291s wall, 0.500000s user + 0.000000s system = 0.500000s CPU (109.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81318, tnet num: 22058, tinst num: 19586, tnode num: 114393, tedge num: 127816.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.689768s wall, 1.640625s user + 0.046875s system = 1.687500s CPU (99.9%)

RUN-1004 : used memory is 614 MB, reserved memory is 614 MB, peak memory is 704 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22058 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.802851s wall, 2.734375s user + 0.078125s system = 2.812500s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(166): len = 489079, overlap = 2.9375
PHY-3002 : Step(167): len = 490499, overlap = 3.25
PHY-3002 : Step(168): len = 491613, overlap = 3.25
PHY-3002 : Step(169): len = 492356, overlap = 3.125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17457/22060.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 559072, over cnt = 2592(7%), over = 8969, worst = 20
PHY-1001 : End global iterations;  0.201529s wall, 0.281250s user + 0.062500s system = 0.343750s CPU (170.6%)

PHY-1001 : Congestion index: top1 = 61.23, top5 = 49.62, top10 = 44.34, top15 = 41.05.
PHY-3001 : End congestion estimation;  0.468507s wall, 0.546875s user + 0.062500s system = 0.609375s CPU (130.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22058 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.074662s wall, 1.031250s user + 0.046875s system = 1.078125s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000489461
PHY-3002 : Step(170): len = 492102, overlap = 172.062
PHY-3002 : Step(171): len = 492098, overlap = 171.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000978923
PHY-3002 : Step(172): len = 492427, overlap = 171.781
PHY-3002 : Step(173): len = 492861, overlap = 171.344
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0018672
PHY-3002 : Step(174): len = 493005, overlap = 171.312
PHY-3002 : Step(175): len = 493409, overlap = 170.5
PHY-3001 : Final: Len = 493409, Over = 170.5
PHY-3001 : End incremental placement;  5.850330s wall, 5.984375s user + 0.406250s system = 6.390625s CPU (109.2%)

OPT-1001 : Total overflow 543.34 peak overflow 4.56
OPT-1001 : End high-fanout net optimization;  9.031438s wall, 10.125000s user + 0.500000s system = 10.625000s CPU (117.6%)

OPT-1001 : Current memory(MB): used = 707, reserve = 687, peak = 723.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17451/22060.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 561680, over cnt = 2529(7%), over = 8443, worst = 20
PHY-1002 : len = 599064, over cnt = 1735(4%), over = 4778, worst = 20
PHY-1002 : len = 643232, over cnt = 740(2%), over = 1584, worst = 14
PHY-1002 : len = 658536, over cnt = 267(0%), over = 574, worst = 13
PHY-1002 : len = 667624, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.342167s wall, 1.921875s user + 0.000000s system = 1.921875s CPU (143.2%)

PHY-1001 : Congestion index: top1 = 52.00, top5 = 45.03, top10 = 41.40, top15 = 39.13.
OPT-1001 : End congestion update;  1.662907s wall, 2.265625s user + 0.000000s system = 2.265625s CPU (136.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22058 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.928579s wall, 0.906250s user + 0.015625s system = 0.921875s CPU (99.3%)

OPT-0007 : Start: WNS 4145 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.598005s wall, 3.187500s user + 0.015625s system = 3.203125s CPU (123.3%)

OPT-1001 : Current memory(MB): used = 682, reserve = 664, peak = 723.
OPT-1001 : End physical optimization;  13.618781s wall, 15.421875s user + 0.578125s system = 16.000000s CPU (117.5%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5682 LUT to BLE ...
SYN-4008 : Packed 5682 LUT and 2732 SEQ to BLE.
SYN-4003 : Packing 9581 remaining SEQ's ...
SYN-4005 : Packed 3358 SEQ with LUT/SLICE
SYN-4006 : 130 single LUT's are left
SYN-4006 : 6223 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11905/13762 primitive instances ...
PHY-3001 : End packing;  3.094923s wall, 3.093750s user + 0.000000s system = 3.093750s CPU (100.0%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8075 instances
RUN-1001 : 3989 mslices, 3989 lslices, 59 pads, 33 brams, 0 dsps
RUN-1001 : There are total 19382 nets
RUN-1001 : 13595 nets have 2 pins
RUN-1001 : 4399 nets have [3 - 5] pins
RUN-1001 : 871 nets have [6 - 10] pins
RUN-1001 : 374 nets have [11 - 20] pins
RUN-1001 : 134 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8073 instances, 7978 slices, 293 macros(1496 instances: 977 mslices 519 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 507407, Over = 391.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7965/19382.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 633792, over cnt = 1547(4%), over = 2453, worst = 8
PHY-1002 : len = 639944, over cnt = 917(2%), over = 1316, worst = 8
PHY-1002 : len = 651544, over cnt = 305(0%), over = 398, worst = 5
PHY-1002 : len = 657872, over cnt = 27(0%), over = 31, worst = 4
PHY-1002 : len = 658752, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.300470s wall, 2.031250s user + 0.015625s system = 2.046875s CPU (157.4%)

PHY-1001 : Congestion index: top1 = 51.49, top5 = 44.42, top10 = 41.01, top15 = 38.75.
PHY-3001 : End congestion estimation;  1.655623s wall, 2.390625s user + 0.015625s system = 2.406250s CPU (145.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67379, tnet num: 19380, tinst num: 8073, tnode num: 91377, tedge num: 111143.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.809519s wall, 1.781250s user + 0.015625s system = 1.796875s CPU (99.3%)

RUN-1004 : used memory is 605 MB, reserved memory is 596 MB, peak memory is 723 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19380 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.802179s wall, 2.765625s user + 0.031250s system = 2.796875s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.44261e-05
PHY-3002 : Step(176): len = 509849, overlap = 361
PHY-3002 : Step(177): len = 509323, overlap = 368.75
PHY-3002 : Step(178): len = 510140, overlap = 379.5
PHY-3002 : Step(179): len = 510955, overlap = 395
PHY-3002 : Step(180): len = 508895, overlap = 406.75
PHY-3002 : Step(181): len = 508404, overlap = 399.5
PHY-3002 : Step(182): len = 505760, overlap = 402.5
PHY-3002 : Step(183): len = 504061, overlap = 399.25
PHY-3002 : Step(184): len = 502174, overlap = 405.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000108852
PHY-3002 : Step(185): len = 506610, overlap = 392.5
PHY-3002 : Step(186): len = 511463, overlap = 379.25
PHY-3002 : Step(187): len = 512028, overlap = 377.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000217704
PHY-3002 : Step(188): len = 520880, overlap = 351.75
PHY-3002 : Step(189): len = 532891, overlap = 318.5
PHY-3002 : Step(190): len = 530688, overlap = 314.75
PHY-3002 : Step(191): len = 528286, overlap = 314.25
PHY-3002 : Step(192): len = 528223, overlap = 310.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.826272s wall, 0.828125s user + 0.984375s system = 1.812500s CPU (219.4%)

PHY-3001 : Trial Legalized: Len = 636280
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 600/19382.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 717208, over cnt = 2272(6%), over = 3735, worst = 7
PHY-1002 : len = 732512, over cnt = 1369(3%), over = 1882, worst = 7
PHY-1002 : len = 747936, over cnt = 524(1%), over = 689, worst = 7
PHY-1002 : len = 753200, over cnt = 255(0%), over = 329, worst = 7
PHY-1002 : len = 759832, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.984225s wall, 3.359375s user + 0.031250s system = 3.390625s CPU (170.9%)

PHY-1001 : Congestion index: top1 = 49.74, top5 = 45.21, top10 = 42.44, top15 = 40.47.
PHY-3001 : End congestion estimation;  2.369667s wall, 3.750000s user + 0.031250s system = 3.781250s CPU (159.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19380 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.971923s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000188516
PHY-3002 : Step(193): len = 591651, overlap = 84
PHY-3002 : Step(194): len = 574308, overlap = 124
PHY-3002 : Step(195): len = 563185, overlap = 169.25
PHY-3002 : Step(196): len = 555887, overlap = 207.5
PHY-3002 : Step(197): len = 551557, overlap = 231.75
PHY-3002 : Step(198): len = 549428, overlap = 253.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000377031
PHY-3002 : Step(199): len = 553661, overlap = 245.75
PHY-3002 : Step(200): len = 557671, overlap = 238
PHY-3002 : Step(201): len = 559104, overlap = 230.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(202): len = 561514, overlap = 231
PHY-3002 : Step(203): len = 567460, overlap = 232
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.036758s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (85.0%)

PHY-3001 : Legalized: Len = 610699, Over = 0
PHY-3001 : Spreading special nets. 29 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.083906s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (93.1%)

PHY-3001 : 44 instances has been re-located, deltaX = 12, deltaY = 25, maxDist = 2.
PHY-3001 : Final: Len = 611525, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67379, tnet num: 19380, tinst num: 8073, tnode num: 91377, tedge num: 111143.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.072973s wall, 2.046875s user + 0.031250s system = 2.078125s CPU (100.2%)

RUN-1004 : used memory is 618 MB, reserved memory is 615 MB, peak memory is 723 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 4409/19382.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 704712, over cnt = 2102(5%), over = 3197, worst = 7
PHY-1002 : len = 715272, over cnt = 1217(3%), over = 1637, worst = 6
PHY-1002 : len = 725352, over cnt = 601(1%), over = 822, worst = 6
PHY-1002 : len = 732728, over cnt = 291(0%), over = 380, worst = 4
PHY-1002 : len = 738104, over cnt = 29(0%), over = 40, worst = 3
PHY-1001 : End global iterations;  1.673698s wall, 2.750000s user + 0.046875s system = 2.796875s CPU (167.1%)

PHY-1001 : Congestion index: top1 = 49.66, top5 = 44.03, top10 = 41.10, top15 = 39.19.
PHY-1001 : End incremental global routing;  2.005913s wall, 3.078125s user + 0.046875s system = 3.125000s CPU (155.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19380 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.992639s wall, 0.953125s user + 0.046875s system = 1.000000s CPU (100.7%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8010 has valid locations, 7 needs to be replaced
PHY-3001 : design contains 8079 instances, 7984 slices, 293 macros(1496 instances: 977 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 612724
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17569/19387.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 739040, over cnt = 35(0%), over = 46, worst = 3
PHY-1002 : len = 739200, over cnt = 14(0%), over = 15, worst = 2
PHY-1002 : len = 739496, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.404560s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (100.4%)

PHY-1001 : Congestion index: top1 = 49.66, top5 = 44.02, top10 = 41.08, top15 = 39.21.
PHY-3001 : End congestion estimation;  0.737506s wall, 0.718750s user + 0.015625s system = 0.734375s CPU (99.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67414, tnet num: 19385, tinst num: 8079, tnode num: 91418, tedge num: 111183.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.025774s wall, 2.000000s user + 0.031250s system = 2.031250s CPU (100.3%)

RUN-1004 : used memory is 640 MB, reserved memory is 632 MB, peak memory is 723 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19385 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.041076s wall, 3.015625s user + 0.031250s system = 3.046875s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(204): len = 612617, overlap = 0
PHY-3002 : Step(205): len = 612701, overlap = 0
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17565/19387.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 739288, over cnt = 14(0%), over = 14, worst = 1
PHY-1002 : len = 739280, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 739296, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 739344, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.572191s wall, 0.578125s user + 0.000000s system = 0.578125s CPU (101.0%)

PHY-1001 : Congestion index: top1 = 49.76, top5 = 44.11, top10 = 41.19, top15 = 39.30.
PHY-3001 : End congestion estimation;  0.901572s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (102.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19385 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.969058s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000453887
PHY-3002 : Step(206): len = 612613, overlap = 0.5
PHY-3002 : Step(207): len = 612626, overlap = 0.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007205s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 612609, Over = 0
PHY-3001 : End spreading;  0.073622s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (106.1%)

PHY-3001 : Final: Len = 612609, Over = 0
PHY-3001 : End incremental placement;  6.292106s wall, 6.203125s user + 0.078125s system = 6.281250s CPU (99.8%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  9.819874s wall, 10.750000s user + 0.171875s system = 10.921875s CPU (111.2%)

OPT-1001 : Current memory(MB): used = 712, reserve = 697, peak = 723.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17565/19387.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 739168, over cnt = 10(0%), over = 11, worst = 2
PHY-1002 : len = 739152, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 739224, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 739256, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.550029s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (99.4%)

PHY-1001 : Congestion index: top1 = 49.63, top5 = 44.19, top10 = 41.18, top15 = 39.28.
OPT-1001 : End congestion update;  0.869818s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (98.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19385 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.807084s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (100.7%)

OPT-0007 : Start: WNS 4105 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.681921s wall, 1.671875s user + 0.000000s system = 1.671875s CPU (99.4%)

OPT-1001 : Current memory(MB): used = 712, reserve = 697, peak = 723.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19385 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.814271s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (99.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17575/19387.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 739256, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.126470s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (98.8%)

PHY-1001 : Congestion index: top1 = 49.63, top5 = 44.19, top10 = 41.18, top15 = 39.28.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19385 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.806683s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (100.7%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4105 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 49.241379
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4105ps with logic level 5 
OPT-1001 : End physical optimization;  15.923271s wall, 16.812500s user + 0.203125s system = 17.015625s CPU (106.9%)

RUN-1003 : finish command "place" in  71.369614s wall, 128.437500s user + 7.390625s system = 135.828125s CPU (190.3%)

RUN-1004 : used memory is 595 MB, reserved memory is 584 MB, peak memory is 723 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.754938s wall, 3.062500s user + 0.000000s system = 3.062500s CPU (174.5%)

RUN-1004 : used memory is 595 MB, reserved memory is 585 MB, peak memory is 723 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8081 instances
RUN-1001 : 3995 mslices, 3989 lslices, 59 pads, 33 brams, 0 dsps
RUN-1001 : There are total 19387 nets
RUN-1001 : 13595 nets have 2 pins
RUN-1001 : 4397 nets have [3 - 5] pins
RUN-1001 : 873 nets have [6 - 10] pins
RUN-1001 : 380 nets have [11 - 20] pins
RUN-1001 : 133 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67414, tnet num: 19385, tinst num: 8079, tnode num: 91418, tedge num: 111183.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.900781s wall, 1.906250s user + 0.000000s system = 1.906250s CPU (100.3%)

RUN-1004 : used memory is 586 MB, reserved memory is 567 MB, peak memory is 723 MB
PHY-1001 : 3995 mslices, 3989 lslices, 59 pads, 33 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19385 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 682880, over cnt = 2256(6%), over = 3704, worst = 9
PHY-1002 : len = 698608, over cnt = 1346(3%), over = 1845, worst = 7
PHY-1002 : len = 717072, over cnt = 373(1%), over = 474, worst = 7
PHY-1002 : len = 725232, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 725328, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.877944s wall, 3.109375s user + 0.093750s system = 3.203125s CPU (170.6%)

PHY-1001 : Congestion index: top1 = 49.70, top5 = 44.06, top10 = 40.95, top15 = 39.04.
PHY-1001 : End global routing;  2.235578s wall, 3.453125s user + 0.109375s system = 3.562500s CPU (159.4%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 695, reserve = 686, peak = 723.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 964, reserve = 953, peak = 964.
PHY-1001 : End build detailed router design. 4.789153s wall, 4.718750s user + 0.062500s system = 4.781250s CPU (99.8%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 189968, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.943812s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (101.0%)

PHY-1001 : Current memory(MB): used = 1000, reserve = 990, peak = 1000.
PHY-1001 : End phase 1; 0.952066s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (100.1%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 45% nets.
PHY-1001 : Routed 53% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.65449e+06, over cnt = 1258(0%), over = 1263, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1017, reserve = 1007, peak = 1017.
PHY-1001 : End initial routed; 16.677899s wall, 44.281250s user + 0.359375s system = 44.640625s CPU (267.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18123(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.697   |   0.000   |   0   
RUN-1001 :   Hold   |   0.153   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.572644s wall, 3.562500s user + 0.000000s system = 3.562500s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 1031, reserve = 1021, peak = 1031.
PHY-1001 : End phase 2; 20.250718s wall, 47.843750s user + 0.359375s system = 48.203125s CPU (238.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.65449e+06, over cnt = 1258(0%), over = 1263, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.256962s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (97.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.6429e+06, over cnt = 420(0%), over = 420, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.842144s wall, 1.515625s user + 0.000000s system = 1.515625s CPU (180.0%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.64316e+06, over cnt = 44(0%), over = 44, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.463697s wall, 0.609375s user + 0.000000s system = 0.609375s CPU (131.4%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.64362e+06, over cnt = 14(0%), over = 14, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.205606s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (121.6%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.64374e+06, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.187589s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (108.3%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.6439e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.163848s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (104.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18123(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.697   |   0.000   |   0   
RUN-1001 :   Hold   |   0.186   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.583503s wall, 3.593750s user + 0.000000s system = 3.593750s CPU (100.3%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 289 feed throughs used by 246 nets
PHY-1001 : End commit to database; 2.240293s wall, 2.234375s user + 0.000000s system = 2.234375s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 1118, reserve = 1111, peak = 1118.
PHY-1001 : End phase 3; 8.476102s wall, 9.328125s user + 0.015625s system = 9.343750s CPU (110.2%)

PHY-1003 : Routed, final wirelength = 1.6439e+06
PHY-1001 : Current memory(MB): used = 1122, reserve = 1115, peak = 1122.
PHY-1001 : End export database. 0.183164s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (102.4%)

PHY-1001 : End detail routing;  35.096890s wall, 63.484375s user + 0.453125s system = 63.937500s CPU (182.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67414, tnet num: 19385, tinst num: 8079, tnode num: 91418, tedge num: 111183.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.862488s wall, 1.875000s user + 0.000000s system = 1.875000s CPU (100.7%)

RUN-1004 : used memory is 1054 MB, reserved memory is 1050 MB, peak memory is 1122 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  43.892310s wall, 73.437500s user + 0.578125s system = 74.015625s CPU (168.6%)

RUN-1004 : used memory is 1053 MB, reserved memory is 1050 MB, peak memory is 1122 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8823   out of  19600   45.02%
#reg                    12407   out of  19600   63.30%
#le                     14997
  #lut only              2590   out of  14997   17.27%
  #reg only              6174   out of  14997   41.17%
  #lut&reg               6233   out of  14997   41.56%
#dsp                        0   out of     29    0.00%
#bram                      33   out of     64   51.56%
  #bram9k                  31
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       59   out of    188   31.38%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6772
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          149
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         F2        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         D1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         B3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         C1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         A3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         A2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         B2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         B1        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F15        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDZ        INPUT        K15        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         P2        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         N4        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         M5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         N6        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         N5        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         P5        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M3        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         M4        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         H2        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         M1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         L1        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         N1        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         N3        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         M2        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT         T5        LVCMOS33           8            NONE           OREG       
    Fog_ENY        OUTPUT         T4        LVCMOS33           8            NONE           OREG       
    Fog_ENZ        OUTPUT         T7        LVCMOS33           8            NONE           OREG       
    Fog_INT        OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         T6        LVCMOS33           8            N/A            OREG       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E1        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        G11        LVCMOS25           8            N/A            NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |14997  |7327    |1496    |12451   |33      |0       |
|  AnyFog_dataX                      |AnyFog          |222    |110     |22      |172     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |107    |84      |22      |57      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |209    |89      |22      |172     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |63      |22      |49      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |200    |115     |22      |164     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |84     |62      |22      |48      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2920   |711     |39      |2844    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |61     |35      |5       |53      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |213    |83      |5       |203     |0       |0       |
|    STADOP_com2                     |STADOP          |539    |107     |0       |538     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |40      |14      |42      |0       |0       |
|    head_com2                       |uniheading      |261    |46      |5       |250     |0       |0       |
|    rmc_com2                        |Gprmc           |36     |34      |0       |30      |0       |0       |
|    uart_com2                       |Agrica          |1441   |358     |10      |1424    |0       |0       |
|  COM3                              |COM3_Control    |276    |142     |19      |240     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |63     |39      |5       |54      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |61     |41      |14      |39      |0       |0       |
|    rmc_com3                        |Gprmc           |152    |62      |0       |147     |0       |0       |
|  DATA                              |Data_Processing |8829   |4405    |1122    |7040    |0       |0       |
|    DIV_Dtemp                       |Divider         |823    |357     |84      |698     |0       |0       |
|    DIV_Utemp                       |Divider         |628    |259     |84      |503     |0       |0       |
|    DIV_accX                        |Divider         |615    |280     |84      |481     |0       |0       |
|    DIV_accY                        |Divider         |632    |370     |102     |477     |0       |0       |
|    DIV_accZ                        |Divider         |638    |362     |132     |434     |0       |0       |
|    DIV_rateX                       |Divider         |629    |357     |132     |426     |0       |0       |
|    DIV_rateY                       |Divider         |587    |359     |132     |382     |0       |0       |
|    DIV_rateZ                       |Divider         |554    |383     |132     |350     |0       |0       |
|    genclk                          |genclk          |273    |161     |89      |114     |0       |0       |
|  FMC                               |FMC_Ctrl        |434    |382     |43      |335     |0       |0       |
|  IIC                               |I2C_master      |294    |253     |11      |251     |0       |0       |
|  IMU_CTRL                          |SCHA634         |929    |714     |61      |744     |0       |0       |
|    CtrlData                        |CtrlData        |485    |430     |47      |340     |0       |0       |
|      usms                          |Time_1ms        |32     |27      |5       |21      |0       |0       |
|    SPIM                            |SPI_SCHA634     |444    |284     |14      |404     |0       |0       |
|  POWER                             |POWER_EN        |100    |51      |38      |41      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |576    |355     |97      |396     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |576    |355     |97      |396     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |257    |153     |0       |240     |0       |0       |
|        reg_inst                    |register        |254    |150     |0       |237     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |319    |202     |97      |156     |0       |0       |
|        bus_inst                    |bus_top         |112    |70      |40      |45      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |27     |16      |10      |10      |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |1      |0       |0       |1       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |3      |3       |0       |3       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |16     |10      |6       |8       |0       |0       |
|          BUS_DETECTOR[5]$bus_nodes |bus_det         |14     |8       |6       |5       |0       |0       |
|          BUS_DETECTOR[7]$bus_nodes |bus_det         |51     |33      |18      |18      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |126    |90      |29      |78      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13535  
    #2          2       3422   
    #3          3        670   
    #4          4        305   
    #5        5-10       940   
    #6        11-50      437   
    #7       51-100       9    
    #8       101-500      3    
    #9        >500        2    
  Average     2.12             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.201374s wall, 3.796875s user + 0.015625s system = 3.812500s CPU (173.2%)

RUN-1004 : used memory is 1054 MB, reserved memory is 1051 MB, peak memory is 1122 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67414, tnet num: 19385, tinst num: 8079, tnode num: 91418, tedge num: 111183.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.772849s wall, 1.781250s user + 0.000000s system = 1.781250s CPU (100.5%)

RUN-1004 : used memory is 1055 MB, reserved memory is 1053 MB, peak memory is 1122 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19385 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.489912s wall, 1.468750s user + 0.015625s system = 1.484375s CPU (99.6%)

RUN-1004 : used memory is 1060 MB, reserved memory is 1057 MB, peak memory is 1122 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 5914bebc17a1c31f442cd7128357dba2c23fce68140dc4f57a917e3b4f582a0e -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8079
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19387, pip num: 144775
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 289
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3220 valid insts, and 406908 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011100100000011010100101
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.590644s wall, 126.406250s user + 0.203125s system = 126.609375s CPU (1005.6%)

RUN-1004 : used memory is 1182 MB, reserved memory is 1167 MB, peak memory is 1296 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250821_111723.log"

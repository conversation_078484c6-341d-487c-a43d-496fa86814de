============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Fri Aug 22 11:46:12 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(100)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 100 in ../../Src/INS600M-21A.v(103)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(525)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.790493s wall, 1.812500s user + 3.984375s system = 5.796875s CPU (100.1%)

RUN-1004 : used memory is 79 MB, reserved memory is 39 MB, peak memory is 90 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.937304s wall, 1.937500s user + 0.000000s system = 1.937500s CPU (100.0%)

RUN-1004 : used memory is 302 MB, reserved memory is 270 MB, peak memory is 305 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 42 trigger nets, 42 data nets.
KIT-1004 : Chipwatcher code = 1000110011001111
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb011001,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0111110,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=126) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=126) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb011001,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0111110,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb011001,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0111110,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb011001,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0111110,32'sb01000100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=126)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=126)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb011001,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0111110,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb011001,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0111110,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22786/23 useful/useless nets, 19536/12 useful/useless insts
SYN-1016 : Merged 29 instances.
SYN-1032 : 22454/20 useful/useless nets, 19962/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 398 better
SYN-1014 : Optimize round 2
SYN-1032 : 22134/45 useful/useless nets, 19642/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.654412s wall, 2.578125s user + 0.078125s system = 2.656250s CPU (100.1%)

RUN-1004 : used memory is 328 MB, reserved memory is 295 MB, peak memory is 330 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 65 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22182/299 useful/useless nets, 19727/47 useful/useless insts
SYN-1016 : Merged 45 instances.
SYN-2571 : Optimize after map_dsp, round 1, 391 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 40 instances.
SYN-2501 : Optimize round 1, 82 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 22630/5 useful/useless nets, 20175/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 40, tpin num: 82571, tnet num: 22630, tinst num: 20174, tnode num: 115648, tedge num: 129095.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.215239s wall, 1.171875s user + 0.031250s system = 1.203125s CPU (99.0%)

RUN-1004 : used memory is 469 MB, reserved memory is 437 MB, peak memory is 469 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22630 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 237 (3.41), #lev = 7 (1.79)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 237 (3.41), #lev = 7 (1.79)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 534 instances into 237 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 407 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 128 adder to BLE ...
SYN-4008 : Packed 128 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.717563s wall, 4.640625s user + 0.078125s system = 4.718750s CPU (100.0%)

RUN-1004 : used memory is 352 MB, reserved memory is 318 MB, peak memory is 577 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.713548s wall, 7.531250s user + 0.187500s system = 7.718750s CPU (100.1%)

RUN-1004 : used memory is 352 MB, reserved memory is 318 MB, peak memory is 577 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (271 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19418 instances
RUN-0007 : 5613 luts, 12199 seqs, 983 mslices, 519 lslices, 59 pads, 40 brams, 0 dsps
RUN-1001 : There are total 21881 nets
RUN-1001 : 16405 nets have 2 pins
RUN-1001 : 4293 nets have [3 - 5] pins
RUN-1001 : 806 nets have [6 - 10] pins
RUN-1001 : 251 nets have [11 - 20] pins
RUN-1001 : 103 nets have [21 - 99] pins
RUN-1001 : 23 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4783     
RUN-1001 :   No   |  No   |  Yes  |     704     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     443     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19416 instances, 5613 luts, 12199 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 40, tpin num: 80956, tnet num: 21879, tinst num: 19416, tnode num: 113824, tedge num: 127427.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.235414s wall, 1.203125s user + 0.031250s system = 1.234375s CPU (99.9%)

RUN-1004 : used memory is 527 MB, reserved memory is 500 MB, peak memory is 577 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21879 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.160630s wall, 2.109375s user + 0.046875s system = 2.156250s CPU (99.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.64238e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19416.
PHY-3001 : Level 1 #clusters 2143.
PHY-3001 : End clustering;  0.150473s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (124.6%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 896448, overlap = 625.812
PHY-3002 : Step(2): len = 811124, overlap = 694.344
PHY-3002 : Step(3): len = 527590, overlap = 905.25
PHY-3002 : Step(4): len = 461959, overlap = 975.75
PHY-3002 : Step(5): len = 360691, overlap = 1090.12
PHY-3002 : Step(6): len = 319894, overlap = 1144.69
PHY-3002 : Step(7): len = 275395, overlap = 1210.34
PHY-3002 : Step(8): len = 247622, overlap = 1241.91
PHY-3002 : Step(9): len = 214098, overlap = 1292.12
PHY-3002 : Step(10): len = 200148, overlap = 1336.12
PHY-3002 : Step(11): len = 179025, overlap = 1369.66
PHY-3002 : Step(12): len = 166578, overlap = 1380.94
PHY-3002 : Step(13): len = 151519, overlap = 1391.88
PHY-3002 : Step(14): len = 136970, overlap = 1426.69
PHY-3002 : Step(15): len = 125296, overlap = 1466.47
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.02317e-06
PHY-3002 : Step(16): len = 132507, overlap = 1435.66
PHY-3002 : Step(17): len = 175578, overlap = 1344.62
PHY-3002 : Step(18): len = 186983, overlap = 1239.06
PHY-3002 : Step(19): len = 189083, overlap = 1146.59
PHY-3002 : Step(20): len = 186534, overlap = 1110.31
PHY-3002 : Step(21): len = 181895, overlap = 1106.03
PHY-3002 : Step(22): len = 176802, overlap = 1107.31
PHY-3002 : Step(23): len = 173772, overlap = 1098.47
PHY-3002 : Step(24): len = 169840, overlap = 1102.38
PHY-3002 : Step(25): len = 166403, overlap = 1104.53
PHY-3002 : Step(26): len = 163437, overlap = 1100.69
PHY-3002 : Step(27): len = 162475, overlap = 1114.97
PHY-3002 : Step(28): len = 162224, overlap = 1122.56
PHY-3002 : Step(29): len = 161591, overlap = 1116.97
PHY-3002 : Step(30): len = 160616, overlap = 1110.28
PHY-3002 : Step(31): len = 159341, overlap = 1107.19
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.04634e-06
PHY-3002 : Step(32): len = 169441, overlap = 1090.03
PHY-3002 : Step(33): len = 183486, overlap = 990.25
PHY-3002 : Step(34): len = 186964, overlap = 953.906
PHY-3002 : Step(35): len = 189663, overlap = 934.5
PHY-3002 : Step(36): len = 189249, overlap = 945.656
PHY-3002 : Step(37): len = 189303, overlap = 946.625
PHY-3002 : Step(38): len = 187214, overlap = 938.688
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.09269e-06
PHY-3002 : Step(39): len = 199128, overlap = 877.625
PHY-3002 : Step(40): len = 214210, overlap = 765.906
PHY-3002 : Step(41): len = 219744, overlap = 762.812
PHY-3002 : Step(42): len = 222173, overlap = 782.719
PHY-3002 : Step(43): len = 221314, overlap = 785.969
PHY-3002 : Step(44): len = 218901, overlap = 788.969
PHY-3002 : Step(45): len = 218391, overlap = 804.75
PHY-3002 : Step(46): len = 216633, overlap = 803.125
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 8.18538e-06
PHY-3002 : Step(47): len = 229622, overlap = 731.906
PHY-3002 : Step(48): len = 244729, overlap = 682.188
PHY-3002 : Step(49): len = 249741, overlap = 632.812
PHY-3002 : Step(50): len = 251925, overlap = 609.625
PHY-3002 : Step(51): len = 251033, overlap = 602.125
PHY-3002 : Step(52): len = 249876, overlap = 609.625
PHY-3002 : Step(53): len = 247703, overlap = 622.438
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.63708e-05
PHY-3002 : Step(54): len = 259581, overlap = 563.25
PHY-3002 : Step(55): len = 271627, overlap = 505.125
PHY-3002 : Step(56): len = 277096, overlap = 493.531
PHY-3002 : Step(57): len = 280278, overlap = 494.75
PHY-3002 : Step(58): len = 279972, overlap = 500.125
PHY-3002 : Step(59): len = 278187, overlap = 486.062
PHY-3002 : Step(60): len = 276814, overlap = 492.938
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.27415e-05
PHY-3002 : Step(61): len = 287596, overlap = 469.375
PHY-3002 : Step(62): len = 296537, overlap = 397.25
PHY-3002 : Step(63): len = 299271, overlap = 381.094
PHY-3002 : Step(64): len = 301906, overlap = 364.969
PHY-3002 : Step(65): len = 300555, overlap = 364.469
PHY-3002 : Step(66): len = 298766, overlap = 369.031
PHY-3002 : Step(67): len = 296825, overlap = 364.438
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 6.5483e-05
PHY-3002 : Step(68): len = 305570, overlap = 367.562
PHY-3002 : Step(69): len = 314510, overlap = 327.781
PHY-3002 : Step(70): len = 317813, overlap = 300.719
PHY-3002 : Step(71): len = 319719, overlap = 292.938
PHY-3002 : Step(72): len = 319975, overlap = 293.719
PHY-3002 : Step(73): len = 318129, overlap = 297.656
PHY-3002 : Step(74): len = 315932, overlap = 309.812
PHY-3002 : Step(75): len = 317097, overlap = 318.688
PHY-3002 : Step(76): len = 315691, overlap = 307.719
PHY-3002 : Step(77): len = 315991, overlap = 319.281
PHY-3002 : Step(78): len = 313844, overlap = 308.875
PHY-3002 : Step(79): len = 314095, overlap = 314.719
PHY-3002 : Step(80): len = 312862, overlap = 327.156
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000130966
PHY-3002 : Step(81): len = 318102, overlap = 300.281
PHY-3002 : Step(82): len = 323791, overlap = 296.719
PHY-3002 : Step(83): len = 326364, overlap = 289.594
PHY-3002 : Step(84): len = 327636, overlap = 279.594
PHY-3002 : Step(85): len = 327137, overlap = 282.188
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000261932
PHY-3002 : Step(86): len = 329899, overlap = 274.5
PHY-3002 : Step(87): len = 334946, overlap = 262.844
PHY-3002 : Step(88): len = 338801, overlap = 242.469
PHY-3002 : Step(89): len = 343411, overlap = 253.312
PHY-3002 : Step(90): len = 343869, overlap = 261.812
PHY-3002 : Step(91): len = 342353, overlap = 260.188
PHY-3002 : Step(92): len = 339816, overlap = 247.281
PHY-3002 : Step(93): len = 339662, overlap = 234.188
PHY-3002 : Step(94): len = 337359, overlap = 241.281
PHY-3002 : Step(95): len = 336404, overlap = 237.062
PHY-3002 : Step(96): len = 335871, overlap = 238.062
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(97): len = 337477, overlap = 238.625
PHY-3002 : Step(98): len = 340311, overlap = 229.906
PHY-3002 : Step(99): len = 341510, overlap = 226.781
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.012378s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (126.2%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21881.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 451552, over cnt = 1194(3%), over = 5480, worst = 32
PHY-1001 : End global iterations;  0.818690s wall, 1.093750s user + 0.000000s system = 1.093750s CPU (133.6%)

PHY-1001 : Congestion index: top1 = 76.06, top5 = 54.88, top10 = 44.42, top15 = 38.49.
PHY-3001 : End congestion estimation;  1.061919s wall, 1.312500s user + 0.031250s system = 1.343750s CPU (126.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21879 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.987188s wall, 0.953125s user + 0.031250s system = 0.984375s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000108157
PHY-3002 : Step(100): len = 393729, overlap = 160.219
PHY-3002 : Step(101): len = 403134, overlap = 146.906
PHY-3002 : Step(102): len = 404177, overlap = 143.531
PHY-3002 : Step(103): len = 403864, overlap = 136.438
PHY-3002 : Step(104): len = 410844, overlap = 118.125
PHY-3002 : Step(105): len = 415515, overlap = 104.344
PHY-3002 : Step(106): len = 419870, overlap = 92.375
PHY-3002 : Step(107): len = 424450, overlap = 81.9688
PHY-3002 : Step(108): len = 425181, overlap = 77.125
PHY-3002 : Step(109): len = 427311, overlap = 75.6562
PHY-3002 : Step(110): len = 429814, overlap = 77.0938
PHY-3002 : Step(111): len = 433437, overlap = 78.875
PHY-3002 : Step(112): len = 435131, overlap = 85.0625
PHY-3002 : Step(113): len = 437514, overlap = 89.9688
PHY-3002 : Step(114): len = 439909, overlap = 89.7812
PHY-3002 : Step(115): len = 440087, overlap = 91.25
PHY-3002 : Step(116): len = 442185, overlap = 93.4375
PHY-3002 : Step(117): len = 441665, overlap = 99.8438
PHY-3002 : Step(118): len = 441538, overlap = 106.469
PHY-3002 : Step(119): len = 442912, overlap = 112.969
PHY-3002 : Step(120): len = 443147, overlap = 113.156
PHY-3002 : Step(121): len = 442507, overlap = 113.156
PHY-3002 : Step(122): len = 443341, overlap = 115.344
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000216315
PHY-3002 : Step(123): len = 442867, overlap = 113.5
PHY-3002 : Step(124): len = 445155, overlap = 110
PHY-3002 : Step(125): len = 448725, overlap = 108.438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(126): len = 453522, overlap = 99.6562
PHY-3002 : Step(127): len = 462716, overlap = 95.1875
PHY-3002 : Step(128): len = 469594, overlap = 88.3125
PHY-3002 : Step(129): len = 468358, overlap = 89.8438
PHY-3002 : Step(130): len = 469223, overlap = 92.5625
PHY-3002 : Step(131): len = 469337, overlap = 97.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 39/21881.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 533152, over cnt = 2247(6%), over = 9938, worst = 45
PHY-1001 : End global iterations;  1.136400s wall, 1.890625s user + 0.046875s system = 1.937500s CPU (170.5%)

PHY-1001 : Congestion index: top1 = 74.61, top5 = 58.19, top10 = 49.98, top15 = 45.05.
PHY-3001 : End congestion estimation;  1.439243s wall, 2.187500s user + 0.046875s system = 2.234375s CPU (155.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21879 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.086086s wall, 1.078125s user + 0.031250s system = 1.109375s CPU (102.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000102396
PHY-3002 : Step(132): len = 474624, overlap = 365.344
PHY-3002 : Step(133): len = 480798, overlap = 298.906
PHY-3002 : Step(134): len = 474864, overlap = 284.438
PHY-3002 : Step(135): len = 471127, overlap = 267.469
PHY-3002 : Step(136): len = 468697, overlap = 254.062
PHY-3002 : Step(137): len = 465120, overlap = 227.656
PHY-3002 : Step(138): len = 462882, overlap = 221.875
PHY-3002 : Step(139): len = 459186, overlap = 212.062
PHY-3002 : Step(140): len = 457697, overlap = 205.406
PHY-3002 : Step(141): len = 455681, overlap = 214.719
PHY-3002 : Step(142): len = 452196, overlap = 216.531
PHY-3002 : Step(143): len = 450378, overlap = 220.469
PHY-3002 : Step(144): len = 449172, overlap = 218.125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000204792
PHY-3002 : Step(145): len = 447996, overlap = 212.406
PHY-3002 : Step(146): len = 449785, overlap = 209.031
PHY-3002 : Step(147): len = 451305, overlap = 206.312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000409584
PHY-3002 : Step(148): len = 453538, overlap = 194.938
PHY-3002 : Step(149): len = 458320, overlap = 184.25
PHY-3002 : Step(150): len = 462621, overlap = 179.5
PHY-3002 : Step(151): len = 464679, overlap = 169.156
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000819169
PHY-3002 : Step(152): len = 465312, overlap = 165.812
PHY-3002 : Step(153): len = 468100, overlap = 162.219
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 40, tpin num: 80956, tnet num: 21879, tinst num: 19416, tnode num: 113824, tedge num: 127427.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.659314s wall, 1.640625s user + 0.015625s system = 1.656250s CPU (99.8%)

RUN-1004 : used memory is 568 MB, reserved memory is 543 MB, peak memory is 701 MB
OPT-1001 : Total overflow 549.09 peak overflow 4.06
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 542/21881.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 544040, over cnt = 2472(7%), over = 8903, worst = 23
PHY-1001 : End global iterations;  1.357206s wall, 2.125000s user + 0.062500s system = 2.187500s CPU (161.2%)

PHY-1001 : Congestion index: top1 = 60.80, top5 = 48.96, top10 = 43.70, top15 = 40.40.
PHY-1001 : End incremental global routing;  1.630004s wall, 2.406250s user + 0.062500s system = 2.468750s CPU (151.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21879 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.116492s wall, 1.093750s user + 0.015625s system = 1.109375s CPU (99.4%)

OPT-1001 : 18 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19336 has valid locations, 240 needs to be replaced
PHY-3001 : design contains 19638 instances, 5706 luts, 12328 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 482883
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17199/22103.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 556296, over cnt = 2477(7%), over = 8911, worst = 24
PHY-1001 : End global iterations;  0.207813s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (112.8%)

PHY-1001 : Congestion index: top1 = 61.44, top5 = 49.30, top10 = 44.02, top15 = 40.78.
PHY-3001 : End congestion estimation;  0.472177s wall, 0.500000s user + 0.000000s system = 0.500000s CPU (105.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 40, tpin num: 81692, tnet num: 22101, tinst num: 19638, tnode num: 114866, tedge num: 128455.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.636212s wall, 1.625000s user + 0.015625s system = 1.640625s CPU (100.3%)

RUN-1004 : used memory is 611 MB, reserved memory is 602 MB, peak memory is 703 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22101 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.800455s wall, 2.765625s user + 0.031250s system = 2.796875s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(154): len = 483534, overlap = 2.40625
PHY-3002 : Step(155): len = 485125, overlap = 2.53125
PHY-3002 : Step(156): len = 486492, overlap = 2.78125
PHY-3002 : Step(157): len = 487723, overlap = 2.53125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17218/22103.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 557128, over cnt = 2503(7%), over = 8983, worst = 28
PHY-1001 : End global iterations;  0.203439s wall, 0.328125s user + 0.015625s system = 0.343750s CPU (169.0%)

PHY-1001 : Congestion index: top1 = 61.81, top5 = 49.63, top10 = 44.28, top15 = 41.01.
PHY-3001 : End congestion estimation;  0.473464s wall, 0.593750s user + 0.015625s system = 0.609375s CPU (128.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22101 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.102285s wall, 1.093750s user + 0.015625s system = 1.109375s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00062839
PHY-3002 : Step(158): len = 486954, overlap = 164
PHY-3002 : Step(159): len = 486614, overlap = 164.188
PHY-3002 : Step(160): len = 486579, overlap = 164.531
PHY-3001 : Final: Len = 486579, Over = 164.531
PHY-3001 : End incremental placement;  5.712255s wall, 6.062500s user + 0.296875s system = 6.359375s CPU (111.3%)

OPT-1001 : Total overflow 554.50 peak overflow 4.06
OPT-1001 : End high-fanout net optimization;  9.040975s wall, 10.187500s user + 0.375000s system = 10.562500s CPU (116.8%)

OPT-1001 : Current memory(MB): used = 707, reserve = 686, peak = 723.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17229/22103.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 557960, over cnt = 2462(6%), over = 8473, worst = 22
PHY-1002 : len = 604888, over cnt = 1686(4%), over = 4090, worst = 19
PHY-1002 : len = 638608, over cnt = 738(2%), over = 1719, worst = 19
PHY-1002 : len = 664072, over cnt = 84(0%), over = 131, worst = 19
PHY-1002 : len = 666800, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.459957s wall, 2.046875s user + 0.000000s system = 2.046875s CPU (140.2%)

PHY-1001 : Congestion index: top1 = 50.91, top5 = 44.42, top10 = 40.91, top15 = 38.68.
OPT-1001 : End congestion update;  1.745885s wall, 2.328125s user + 0.000000s system = 2.328125s CPU (133.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22101 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.009729s wall, 1.000000s user + 0.015625s system = 1.015625s CPU (100.6%)

OPT-0007 : Start: WNS 4187 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.763357s wall, 3.328125s user + 0.015625s system = 3.343750s CPU (121.0%)

OPT-1001 : Current memory(MB): used = 683, reserve = 664, peak = 723.
OPT-1001 : End physical optimization;  13.815950s wall, 15.609375s user + 0.437500s system = 16.046875s CPU (116.1%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5706 LUT to BLE ...
SYN-4008 : Packed 5706 LUT and 2728 SEQ to BLE.
SYN-4003 : Packing 9600 remaining SEQ's ...
SYN-4005 : Packed 3345 SEQ with LUT/SLICE
SYN-4006 : 153 single LUT's are left
SYN-4006 : 6255 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11961/13831 primitive instances ...
PHY-3001 : End packing;  3.151892s wall, 3.156250s user + 0.015625s system = 3.171875s CPU (100.6%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8093 instances
RUN-1001 : 3995 mslices, 3994 lslices, 59 pads, 40 brams, 0 dsps
RUN-1001 : There are total 19426 nets
RUN-1001 : 13618 nets have 2 pins
RUN-1001 : 4407 nets have [3 - 5] pins
RUN-1001 : 868 nets have [6 - 10] pins
RUN-1001 : 402 nets have [11 - 20] pins
RUN-1001 : 122 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8091 instances, 7989 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 499333, Over = 389.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7860/19426.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 629520, over cnt = 1543(4%), over = 2512, worst = 8
PHY-1002 : len = 636848, over cnt = 1027(2%), over = 1399, worst = 8
PHY-1002 : len = 646032, over cnt = 517(1%), over = 729, worst = 7
PHY-1002 : len = 654168, over cnt = 171(0%), over = 248, worst = 7
PHY-1002 : len = 658808, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.319119s wall, 1.968750s user + 0.031250s system = 2.000000s CPU (151.6%)

PHY-1001 : Congestion index: top1 = 51.66, top5 = 44.25, top10 = 40.68, top15 = 38.47.
PHY-3001 : End congestion estimation;  1.678816s wall, 2.343750s user + 0.031250s system = 2.375000s CPU (141.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 40, tpin num: 67732, tnet num: 19424, tinst num: 8091, tnode num: 91795, tedge num: 111717.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.858746s wall, 1.843750s user + 0.000000s system = 1.843750s CPU (99.2%)

RUN-1004 : used memory is 606 MB, reserved memory is 595 MB, peak memory is 723 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19424 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.925352s wall, 2.890625s user + 0.015625s system = 2.906250s CPU (99.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.99688e-05
PHY-3002 : Step(161): len = 505441, overlap = 382
PHY-3002 : Step(162): len = 504834, overlap = 371.75
PHY-3002 : Step(163): len = 505449, overlap = 371
PHY-3002 : Step(164): len = 504126, overlap = 390.5
PHY-3002 : Step(165): len = 503629, overlap = 395.5
PHY-3002 : Step(166): len = 502829, overlap = 395.5
PHY-3002 : Step(167): len = 501652, overlap = 394.25
PHY-3002 : Step(168): len = 500963, overlap = 403.25
PHY-3002 : Step(169): len = 498992, overlap = 405.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.99376e-05
PHY-3002 : Step(170): len = 502880, overlap = 391.5
PHY-3002 : Step(171): len = 505337, overlap = 391
PHY-3002 : Step(172): len = 506180, overlap = 385.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000199875
PHY-3002 : Step(173): len = 514943, overlap = 372.5
PHY-3002 : Step(174): len = 525349, overlap = 352
PHY-3002 : Step(175): len = 523439, overlap = 338.25
PHY-3002 : Step(176): len = 521539, overlap = 334.75
PHY-3002 : Step(177): len = 521207, overlap = 339
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.783341s wall, 0.984375s user + 1.140625s system = 2.125000s CPU (271.3%)

PHY-3001 : Trial Legalized: Len = 629690
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 737/19426.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 719536, over cnt = 2411(6%), over = 3880, worst = 7
PHY-1002 : len = 733088, over cnt = 1431(4%), over = 2004, worst = 6
PHY-1002 : len = 752448, over cnt = 435(1%), over = 594, worst = 6
PHY-1002 : len = 761008, over cnt = 82(0%), over = 124, worst = 6
PHY-1002 : len = 763312, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.026031s wall, 3.484375s user + 0.125000s system = 3.609375s CPU (178.2%)

PHY-1001 : Congestion index: top1 = 50.97, top5 = 45.52, top10 = 42.59, top15 = 40.80.
PHY-3001 : End congestion estimation;  2.413664s wall, 3.859375s user + 0.140625s system = 4.000000s CPU (165.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19424 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.975295s wall, 0.953125s user + 0.031250s system = 0.984375s CPU (100.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000183532
PHY-3002 : Step(178): len = 587676, overlap = 75
PHY-3002 : Step(179): len = 569965, overlap = 115.25
PHY-3002 : Step(180): len = 558631, overlap = 167
PHY-3002 : Step(181): len = 551013, overlap = 205
PHY-3002 : Step(182): len = 547297, overlap = 246.5
PHY-3002 : Step(183): len = 544915, overlap = 256.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000367064
PHY-3002 : Step(184): len = 549119, overlap = 249.5
PHY-3002 : Step(185): len = 553232, overlap = 245.25
PHY-3002 : Step(186): len = 552322, overlap = 244.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000643723
PHY-3002 : Step(187): len = 555452, overlap = 243.25
PHY-3002 : Step(188): len = 566454, overlap = 238.25
PHY-3002 : Step(189): len = 570588, overlap = 238.75
PHY-3002 : Step(190): len = 569644, overlap = 240.75
PHY-3002 : Step(191): len = 568742, overlap = 246
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.034885s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (89.6%)

PHY-3001 : Legalized: Len = 609940, Over = 0
PHY-3001 : Spreading special nets. 44 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.087667s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (106.9%)

PHY-3001 : 74 instances has been re-located, deltaX = 24, deltaY = 46, maxDist = 2.
PHY-3001 : Final: Len = 610984, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 40, tpin num: 67732, tnet num: 19424, tinst num: 8091, tnode num: 91795, tedge num: 111717.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.147589s wall, 2.140625s user + 0.015625s system = 2.156250s CPU (100.4%)

RUN-1004 : used memory is 616 MB, reserved memory is 615 MB, peak memory is 723 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3670/19426.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 707688, over cnt = 2216(6%), over = 3459, worst = 6
PHY-1002 : len = 718800, over cnt = 1342(3%), over = 1908, worst = 6
PHY-1002 : len = 736192, over cnt = 395(1%), over = 585, worst = 5
PHY-1002 : len = 745984, over cnt = 41(0%), over = 44, worst = 2
PHY-1002 : len = 746680, over cnt = 8(0%), over = 8, worst = 1
PHY-1001 : End global iterations;  1.763607s wall, 2.750000s user + 0.046875s system = 2.796875s CPU (158.6%)

PHY-1001 : Congestion index: top1 = 49.89, top5 = 44.23, top10 = 41.25, top15 = 39.39.
PHY-1001 : End incremental global routing;  2.089910s wall, 3.078125s user + 0.046875s system = 3.125000s CPU (149.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19424 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.010931s wall, 1.000000s user + 0.015625s system = 1.015625s CPU (100.5%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8028 has valid locations, 8 needs to be replaced
PHY-3001 : design contains 8098 instances, 7996 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 612372
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17564/19432.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 747832, over cnt = 20(0%), over = 20, worst = 1
PHY-1002 : len = 747856, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 747888, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 747920, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.528447s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (97.6%)

PHY-1001 : Congestion index: top1 = 49.96, top5 = 44.25, top10 = 41.28, top15 = 39.42.
PHY-3001 : End congestion estimation;  0.847586s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (97.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 40, tpin num: 67773, tnet num: 19430, tinst num: 8098, tnode num: 91843, tedge num: 111764.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.144867s wall, 2.140625s user + 0.015625s system = 2.156250s CPU (100.5%)

RUN-1004 : used memory is 650 MB, reserved memory is 639 MB, peak memory is 723 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19430 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.164027s wall, 3.125000s user + 0.046875s system = 3.171875s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(192): len = 612372, overlap = 0.25
PHY-3002 : Step(193): len = 612372, overlap = 0.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17571/19432.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 747920, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.122188s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (102.3%)

PHY-1001 : Congestion index: top1 = 49.96, top5 = 44.25, top10 = 41.28, top15 = 39.42.
PHY-3001 : End congestion estimation;  0.439436s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (99.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19430 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.959578s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (99.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(194): len = 612336, overlap = 1.25
PHY-3002 : Step(195): len = 612287, overlap = 1
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007040s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 612323, Over = 0
PHY-3001 : Spreading special nets. 1 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.072595s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (86.1%)

PHY-3001 : 1 instances has been re-located, deltaX = 0, deltaY = 1, maxDist = 1.
PHY-3001 : Final: Len = 612315, Over = 0
PHY-3001 : End incremental placement;  6.071907s wall, 6.093750s user + 0.062500s system = 6.156250s CPU (101.4%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  9.718377s wall, 10.718750s user + 0.125000s system = 10.843750s CPU (111.6%)

OPT-1001 : Current memory(MB): used = 721, reserve = 706, peak = 727.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17561/19432.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 748024, over cnt = 10(0%), over = 11, worst = 2
PHY-1002 : len = 748032, over cnt = 4(0%), over = 5, worst = 2
PHY-1002 : len = 748104, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.388669s wall, 0.421875s user + 0.015625s system = 0.437500s CPU (112.6%)

PHY-1001 : Congestion index: top1 = 49.94, top5 = 44.32, top10 = 41.31, top15 = 39.43.
OPT-1001 : End congestion update;  0.707635s wall, 0.750000s user + 0.015625s system = 0.765625s CPU (108.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19430 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.811302s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (100.1%)

OPT-0007 : Start: WNS 4480 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.523842s wall, 1.562500s user + 0.015625s system = 1.578125s CPU (103.6%)

OPT-1001 : Current memory(MB): used = 721, reserve = 706, peak = 727.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19430 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.835860s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (99.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17571/19432.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 748104, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.120282s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (103.9%)

PHY-1001 : Congestion index: top1 = 49.94, top5 = 44.32, top10 = 41.31, top15 = 39.43.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19430 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.084634s wall, 1.078125s user + 0.000000s system = 1.078125s CPU (99.4%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4480 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 49.482759
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4480ps with logic level 8 
RUN-1001 :       #2 path slack 4487ps with logic level 8 
OPT-1001 : End physical optimization;  16.122000s wall, 17.125000s user + 0.171875s system = 17.296875s CPU (107.3%)

RUN-1003 : finish command "place" in  69.340881s wall, 128.968750s user + 7.578125s system = 136.546875s CPU (196.9%)

RUN-1004 : used memory is 569 MB, reserved memory is 544 MB, peak memory is 727 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.736388s wall, 2.984375s user + 0.015625s system = 3.000000s CPU (172.8%)

RUN-1004 : used memory is 569 MB, reserved memory is 546 MB, peak memory is 727 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8100 instances
RUN-1001 : 4002 mslices, 3994 lslices, 59 pads, 40 brams, 0 dsps
RUN-1001 : There are total 19432 nets
RUN-1001 : 13618 nets have 2 pins
RUN-1001 : 4406 nets have [3 - 5] pins
RUN-1001 : 873 nets have [6 - 10] pins
RUN-1001 : 404 nets have [11 - 20] pins
RUN-1001 : 122 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 40, tpin num: 67773, tnet num: 19430, tinst num: 8098, tnode num: 91843, tedge num: 111764.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.816201s wall, 1.796875s user + 0.015625s system = 1.812500s CPU (99.8%)

RUN-1004 : used memory is 598 MB, reserved memory is 586 MB, peak memory is 727 MB
PHY-1001 : 4002 mslices, 3994 lslices, 59 pads, 40 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19430 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 688552, over cnt = 2274(6%), over = 3822, worst = 9
PHY-1002 : len = 704224, over cnt = 1461(4%), over = 2055, worst = 6
PHY-1002 : len = 719112, over cnt = 665(1%), over = 943, worst = 6
PHY-1002 : len = 733488, over cnt = 21(0%), over = 25, worst = 3
PHY-1002 : len = 734144, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.013844s wall, 3.468750s user + 0.062500s system = 3.531250s CPU (175.3%)

PHY-1001 : Congestion index: top1 = 48.88, top5 = 43.44, top10 = 40.60, top15 = 38.86.
PHY-1001 : End global routing;  2.379191s wall, 3.828125s user + 0.062500s system = 3.890625s CPU (163.5%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 704, reserve = 695, peak = 727.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 973, reserve = 962, peak = 973.
PHY-1001 : End build detailed router design. 4.732680s wall, 4.687500s user + 0.046875s system = 4.734375s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 191920, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.965821s wall, 0.937500s user + 0.031250s system = 0.968750s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 1008, reserve = 998, peak = 1008.
PHY-1001 : End phase 1; 0.974678s wall, 0.953125s user + 0.031250s system = 0.984375s CPU (101.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.71094e+06, over cnt = 1351(0%), over = 1355, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1024, reserve = 1014, peak = 1024.
PHY-1001 : End initial routed; 17.993275s wall, 50.609375s user + 0.484375s system = 51.093750s CPU (284.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18160(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.882   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.506806s wall, 3.515625s user + 0.000000s system = 3.515625s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 1040, reserve = 1029, peak = 1040.
PHY-1001 : End phase 2; 21.500255s wall, 54.125000s user + 0.484375s system = 54.609375s CPU (254.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.71094e+06, over cnt = 1351(0%), over = 1355, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.251521s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (99.4%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.69828e+06, over cnt = 443(0%), over = 443, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.747592s wall, 1.500000s user + 0.000000s system = 1.500000s CPU (200.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.6989e+06, over cnt = 82(0%), over = 82, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.444560s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (123.0%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.70017e+06, over cnt = 9(0%), over = 9, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.288736s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (102.8%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.70034e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.227885s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (96.0%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.70035e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.177490s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (96.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18160(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.882   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.512885s wall, 3.500000s user + 0.015625s system = 3.515625s CPU (100.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 287 feed throughs used by 253 nets
PHY-1001 : End commit to database; 2.195464s wall, 2.187500s user + 0.015625s system = 2.203125s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 1124, reserve = 1116, peak = 1124.
PHY-1001 : End phase 3; 8.370864s wall, 9.218750s user + 0.031250s system = 9.250000s CPU (110.5%)

PHY-1003 : Routed, final wirelength = 1.70035e+06
PHY-1001 : Current memory(MB): used = 1128, reserve = 1121, peak = 1128.
PHY-1001 : End export database. 0.062392s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.2%)

PHY-1001 : End detail routing;  36.056494s wall, 69.453125s user + 0.593750s system = 70.046875s CPU (194.3%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 40, tpin num: 67773, tnet num: 19430, tinst num: 8098, tnode num: 91843, tedge num: 111764.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.691071s wall, 1.703125s user + 0.000000s system = 1.703125s CPU (100.7%)

RUN-1004 : used memory is 1057 MB, reserved memory is 1057 MB, peak memory is 1128 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  44.713500s wall, 79.515625s user + 0.703125s system = 80.218750s CPU (179.4%)

RUN-1004 : used memory is 1057 MB, reserved memory is 1057 MB, peak memory is 1128 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        65
  #input                   26
  #output                  37
  #inout                    2

Utilization Statistics
#lut                     8863   out of  19600   45.22%
#reg                    12422   out of  19600   63.38%
#le                     15070
  #lut only              2648   out of  15070   17.57%
  #reg only              6207   out of  15070   41.19%
  #lut&reg               6215   out of  15070   41.24%
#dsp                        0   out of     29    0.00%
#bram                      40   out of     64   62.50%
  #bram9k                  38
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       59   out of    188   31.38%
  #ireg                     9
  #oreg                    34
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6759
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          164
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         F2        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          NONE       
    FMC_NWE         INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         D1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         B3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         C1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         A3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         A2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         B2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         B1        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F15        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDZ        INPUT        K15        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         P2        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         N4        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         M5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         N6        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         N5        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         P5        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M3        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         M4        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         H2        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         M1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         L1        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         N1        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         N3        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         M2        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         T4        LVCMOS33           8            NONE           OREG       
    Fog_ENZ        OUTPUT         T7        LVCMOS33           8            NONE           OREG       
    Fog_INT        OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT         C3        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT        B15        LVCMOS33           8            N/A            OREG       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
     spi_en        OUTPUT         T5        LVCMOS33           8            NONE           OREG       
    I2C_SDA         INOUT         K2        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT         F6        LVCMOS25           8            N/A            NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15070  |7361    |1502    |12466   |40      |0       |
|  AnyFog_dataX                      |AnyFog          |208    |68      |22      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |53      |22      |51      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |208    |68      |22      |172     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |53      |22      |50      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |207    |91      |22      |167     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |60      |22      |46      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2907   |633     |39      |2822    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |62     |40      |5       |52      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |216    |72      |5       |203     |0       |0       |
|    STADOP_com2                     |STADOP          |543    |62      |0       |541     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |41      |14      |42      |0       |0       |
|    head_com2                       |uniheading      |257    |82      |5       |247     |0       |0       |
|    rmc_com2                        |Gprmc           |39     |39      |0       |34      |0       |0       |
|    uart_com2                       |Agrica          |1417   |273     |10      |1395    |0       |0       |
|  COM3                              |COM3_Control    |278    |128     |19      |237     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |65     |41      |5       |55      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |61     |39      |14      |38      |0       |0       |
|    rmc_com3                        |Gprmc           |152    |48      |0       |144     |0       |0       |
|  DATA                              |Data_Processing |8830   |4582    |1122    |7036    |0       |0       |
|    DIV_Dtemp                       |Divider         |752    |325     |84      |626     |0       |0       |
|    DIV_Utemp                       |Divider         |669    |285     |84      |543     |0       |0       |
|    DIV_accX                        |Divider         |600    |312     |84      |474     |0       |0       |
|    DIV_accY                        |Divider         |619    |312     |102     |463     |0       |0       |
|    DIV_accZ                        |Divider         |688    |425     |132     |481     |0       |0       |
|    DIV_rateX                       |Divider         |676    |376     |132     |473     |0       |0       |
|    DIV_rateY                       |Divider         |578    |370     |132     |372     |0       |0       |
|    DIV_rateZ                       |Divider         |560    |370     |132     |355     |0       |0       |
|    genclk                          |genclk          |260    |157     |89      |100     |0       |0       |
|  FMC                               |FMC_Ctrl        |508    |435     |43      |375     |0       |0       |
|  IIC                               |I2C_master      |321    |265     |11      |268     |0       |0       |
|  IMU_CTRL                          |SCHA634         |900    |651     |61      |727     |0       |0       |
|    CtrlData                        |CtrlData        |457    |402     |47      |332     |0       |0       |
|      usms                          |Time_1ms        |33     |28      |5       |17      |0       |0       |
|    SPIM                            |SPI_SCHA634     |443    |249     |14      |395     |0       |0       |
|  POWER                             |POWER_EN        |98     |51      |38      |39      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |600    |389     |103     |401     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |600    |389     |103     |401     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |266    |179     |0       |249     |0       |0       |
|        reg_inst                    |register        |264    |177     |0       |247     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |334    |210     |103     |152     |0       |0       |
|        bus_inst                    |bus_top         |129    |79      |46      |46      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |26     |16      |10      |8       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |3      |3       |0       |3       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |50     |28      |18      |18      |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |49     |31      |18      |16      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |124    |90      |29      |75      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13558  
    #2          2       3391   
    #3          3        697   
    #4          4        318   
    #5        5-10       956   
    #6        11-50      431   
    #7       51-100      12    
    #8       101-500      3    
    #9        >500        2    
  Average     2.14             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.169298s wall, 3.687500s user + 0.031250s system = 3.718750s CPU (171.4%)

RUN-1004 : used memory is 1058 MB, reserved memory is 1057 MB, peak memory is 1128 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 40, tpin num: 67773, tnet num: 19430, tinst num: 8098, tnode num: 91843, tedge num: 111764.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.706427s wall, 1.703125s user + 0.000000s system = 1.703125s CPU (99.8%)

RUN-1004 : used memory is 1061 MB, reserved memory is 1060 MB, peak memory is 1128 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19430 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.496960s wall, 1.500000s user + 0.000000s system = 1.500000s CPU (100.2%)

RUN-1004 : used memory is 1069 MB, reserved memory is 1066 MB, peak memory is 1128 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 19285304e968e394d67a8f065444a1f66c49f77adeb419c41e6b7764d8228547 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8098
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19432, pip num: 146878
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 287
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3238 valid insts, and 411700 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011111001000110011001111
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  11.437523s wall, 113.468750s user + 0.140625s system = 113.609375s CPU (993.3%)

RUN-1004 : used memory is 1189 MB, reserved memory is 1174 MB, peak memory is 1304 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250822_114612.log"

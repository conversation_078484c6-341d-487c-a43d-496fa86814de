============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Wed Aug 20 14:03:46 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.942792s wall, 1.593750s user + 4.328125s system = 5.921875s CPU (99.6%)

RUN-1004 : used memory is 79 MB, reserved memory is 41 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.989118s wall, 1.875000s user + 0.093750s system = 1.968750s CPU (99.0%)

RUN-1004 : used memory is 302 MB, reserved memory is 270 MB, peak memory is 305 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 2 view nodes, 24 trigger nets, 24 data nets.
KIT-1004 : Chipwatcher code = 1010010110001101
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=78) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=78) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=78)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=78)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22394/12 useful/useless nets, 19353/4 useful/useless insts
SYN-1016 : Merged 17 instances.
SYN-1032 : 22170/16 useful/useless nets, 19677/12 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 2 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 2 mux instances.
SYN-1015 : Optimize round 1, 279 better
SYN-1014 : Optimize round 2
SYN-1032 : 21961/30 useful/useless nets, 19468/32 useful/useless insts
SYN-1015 : Optimize round 2, 64 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.843387s wall, 2.703125s user + 0.140625s system = 2.843750s CPU (100.0%)

RUN-1004 : used memory is 329 MB, reserved memory is 294 MB, peak memory is 330 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 21985/155 useful/useless nets, 19513/29 useful/useless insts
SYN-1016 : Merged 21 instances.
SYN-2571 : Optimize after map_dsp, round 1, 205 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 24 instances.
SYN-2501 : Optimize round 1, 50 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 14 macro adder
SYN-1019 : Optimized 9 mux instances.
SYN-1016 : Merged 10 instances.
SYN-1032 : 22356/5 useful/useless nets, 19884/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81088, tnet num: 22356, tinst num: 19883, tnode num: 113760, tedge num: 126843.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.276780s wall, 1.265625s user + 0.015625s system = 1.281250s CPU (100.4%)

RUN-1004 : used memory is 465 MB, reserved memory is 433 MB, peak memory is 465 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22356 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 173 (3.57), #lev = 7 (1.79)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 173 (3.57), #lev = 7 (1.79)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 387 instances into 173 LUTs, name keeping = 77%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 290 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 108 adder to BLE ...
SYN-4008 : Packed 108 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.982790s wall, 4.921875s user + 0.062500s system = 4.984375s CPU (100.0%)

RUN-1004 : used memory is 349 MB, reserved memory is 313 MB, peak memory is 571 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  8.204330s wall, 7.953125s user + 0.250000s system = 8.203125s CPU (100.0%)

RUN-1004 : used memory is 350 MB, reserved memory is 314 MB, peak memory is 571 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (177 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19232 instances
RUN-0007 : 5565 luts, 12091 seqs, 973 mslices, 515 lslices, 59 pads, 24 brams, 0 dsps
RUN-1001 : There are total 21712 nets
RUN-1001 : 16362 nets have 2 pins
RUN-1001 : 4173 nets have [3 - 5] pins
RUN-1001 : 815 nets have [6 - 10] pins
RUN-1001 : 237 nets have [11 - 20] pins
RUN-1001 : 107 nets have [21 - 99] pins
RUN-1001 : 18 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4792     
RUN-1001 :   No   |  No   |  Yes  |     681     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     349     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19230 instances, 5565 luts, 12091 seqs, 1488 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 79731, tnet num: 21710, tinst num: 19230, tnode num: 112077, tedge num: 125347.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.311333s wall, 1.312500s user + 0.000000s system = 1.312500s CPU (100.1%)

RUN-1004 : used memory is 523 MB, reserved memory is 494 MB, peak memory is 571 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21710 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.258267s wall, 2.250000s user + 0.015625s system = 2.265625s CPU (100.3%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.55451e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19230.
PHY-3001 : Level 1 #clusters 2134.
PHY-3001 : End clustering;  0.157974s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (158.3%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 867507, overlap = 588.594
PHY-3002 : Step(2): len = 795084, overlap = 665.406
PHY-3002 : Step(3): len = 514266, overlap = 828.625
PHY-3002 : Step(4): len = 455320, overlap = 912.875
PHY-3002 : Step(5): len = 362277, overlap = 1020.12
PHY-3002 : Step(6): len = 318828, overlap = 1081.12
PHY-3002 : Step(7): len = 262888, overlap = 1151
PHY-3002 : Step(8): len = 233096, overlap = 1197.69
PHY-3002 : Step(9): len = 202401, overlap = 1238.09
PHY-3002 : Step(10): len = 187447, overlap = 1263.97
PHY-3002 : Step(11): len = 167534, overlap = 1318.88
PHY-3002 : Step(12): len = 158175, overlap = 1348.75
PHY-3002 : Step(13): len = 145531, overlap = 1371.25
PHY-3002 : Step(14): len = 134348, overlap = 1399.28
PHY-3002 : Step(15): len = 126819, overlap = 1398.97
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.23306e-06
PHY-3002 : Step(16): len = 133731, overlap = 1392.31
PHY-3002 : Step(17): len = 188806, overlap = 1255.03
PHY-3002 : Step(18): len = 198435, overlap = 1130.59
PHY-3002 : Step(19): len = 199735, overlap = 1082.5
PHY-3002 : Step(20): len = 194229, overlap = 1079.97
PHY-3002 : Step(21): len = 189151, overlap = 1070.47
PHY-3002 : Step(22): len = 183246, overlap = 1081.75
PHY-3002 : Step(23): len = 177722, overlap = 1096.19
PHY-3002 : Step(24): len = 173402, overlap = 1092.69
PHY-3002 : Step(25): len = 171140, overlap = 1087.19
PHY-3002 : Step(26): len = 168486, overlap = 1084.44
PHY-3002 : Step(27): len = 166018, overlap = 1087.97
PHY-3002 : Step(28): len = 164679, overlap = 1065.75
PHY-3002 : Step(29): len = 164121, overlap = 1058.94
PHY-3002 : Step(30): len = 162186, overlap = 1059.5
PHY-3002 : Step(31): len = 162180, overlap = 1046.47
PHY-3002 : Step(32): len = 161904, overlap = 1036.25
PHY-3002 : Step(33): len = 161187, overlap = 1022.34
PHY-3002 : Step(34): len = 160295, overlap = 1023.38
PHY-3002 : Step(35): len = 159955, overlap = 1017.88
PHY-3002 : Step(36): len = 158489, overlap = 1000.56
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.46612e-06
PHY-3002 : Step(37): len = 165474, overlap = 962.344
PHY-3002 : Step(38): len = 181370, overlap = 860.781
PHY-3002 : Step(39): len = 185507, overlap = 812.781
PHY-3002 : Step(40): len = 185733, overlap = 810.25
PHY-3002 : Step(41): len = 185903, overlap = 801.781
PHY-3002 : Step(42): len = 185312, overlap = 806.656
PHY-3002 : Step(43): len = 184547, overlap = 837.125
PHY-3002 : Step(44): len = 182985, overlap = 837.469
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.93224e-06
PHY-3002 : Step(45): len = 193734, overlap = 826.375
PHY-3002 : Step(46): len = 208483, overlap = 779.594
PHY-3002 : Step(47): len = 211755, overlap = 735.875
PHY-3002 : Step(48): len = 212844, overlap = 713.188
PHY-3002 : Step(49): len = 213604, overlap = 725.906
PHY-3002 : Step(50): len = 212316, overlap = 721.656
PHY-3002 : Step(51): len = 210154, overlap = 728.969
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 9.86449e-06
PHY-3002 : Step(52): len = 222659, overlap = 703.312
PHY-3002 : Step(53): len = 237191, overlap = 636.219
PHY-3002 : Step(54): len = 241790, overlap = 615.031
PHY-3002 : Step(55): len = 243868, overlap = 604.156
PHY-3002 : Step(56): len = 243583, overlap = 589.281
PHY-3002 : Step(57): len = 241146, overlap = 578.719
PHY-3002 : Step(58): len = 239163, overlap = 576.656
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.9729e-05
PHY-3002 : Step(59): len = 251770, overlap = 522.656
PHY-3002 : Step(60): len = 262788, overlap = 469.875
PHY-3002 : Step(61): len = 266663, overlap = 428.969
PHY-3002 : Step(62): len = 268358, overlap = 414.406
PHY-3002 : Step(63): len = 267774, overlap = 385.594
PHY-3002 : Step(64): len = 266352, overlap = 357.406
PHY-3002 : Step(65): len = 265240, overlap = 353.156
PHY-3002 : Step(66): len = 264241, overlap = 337.438
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.9458e-05
PHY-3002 : Step(67): len = 274389, overlap = 287.375
PHY-3002 : Step(68): len = 286195, overlap = 265.594
PHY-3002 : Step(69): len = 289665, overlap = 254.469
PHY-3002 : Step(70): len = 291169, overlap = 252.25
PHY-3002 : Step(71): len = 289620, overlap = 245.281
PHY-3002 : Step(72): len = 288695, overlap = 250.875
PHY-3002 : Step(73): len = 284879, overlap = 253.938
PHY-3002 : Step(74): len = 284485, overlap = 245.812
PHY-3002 : Step(75): len = 283030, overlap = 252.156
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 7.89159e-05
PHY-3002 : Step(76): len = 292060, overlap = 239.875
PHY-3002 : Step(77): len = 299747, overlap = 235.844
PHY-3002 : Step(78): len = 302300, overlap = 232.812
PHY-3002 : Step(79): len = 303534, overlap = 219.156
PHY-3002 : Step(80): len = 302786, overlap = 234.281
PHY-3002 : Step(81): len = 301419, overlap = 241.375
PHY-3002 : Step(82): len = 300066, overlap = 247.25
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000157832
PHY-3002 : Step(83): len = 305224, overlap = 234.594
PHY-3002 : Step(84): len = 311162, overlap = 211.188
PHY-3002 : Step(85): len = 313171, overlap = 194.188
PHY-3002 : Step(86): len = 315303, overlap = 191.188
PHY-3002 : Step(87): len = 315487, overlap = 180.312
PHY-3002 : Step(88): len = 314895, overlap = 165.125
PHY-3002 : Step(89): len = 312903, overlap = 144.219
PHY-3002 : Step(90): len = 312780, overlap = 151.219
PHY-3002 : Step(91): len = 312460, overlap = 153.312
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000315664
PHY-3002 : Step(92): len = 314852, overlap = 150.188
PHY-3002 : Step(93): len = 318881, overlap = 142.688
PHY-3002 : Step(94): len = 320592, overlap = 146.688
PHY-3002 : Step(95): len = 321881, overlap = 139.594
PHY-3002 : Step(96): len = 320835, overlap = 165.656
PHY-3002 : Step(97): len = 320676, overlap = 166.781
PHY-3002 : Step(98): len = 319782, overlap = 159.375
PHY-3002 : Step(99): len = 319542, overlap = 163.406
PHY-3002 : Step(100): len = 318474, overlap = 166.875
PHY-3002 : Step(101): len = 318378, overlap = 168.688
PHY-3002 : Step(102): len = 318184, overlap = 167.75
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(103): len = 319348, overlap = 168
PHY-3002 : Step(104): len = 321503, overlap = 167.438
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.013541s wall, 0.000000s user + 0.031250s system = 0.031250s CPU (230.8%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21712.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 412360, over cnt = 1185(3%), over = 5297, worst = 55
PHY-1001 : End global iterations;  0.799691s wall, 1.015625s user + 0.015625s system = 1.031250s CPU (129.0%)

PHY-1001 : Congestion index: top1 = 71.44, top5 = 50.57, top10 = 41.28, top15 = 36.02.
PHY-3001 : End congestion estimation;  1.037283s wall, 1.265625s user + 0.015625s system = 1.281250s CPU (123.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21710 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.054026s wall, 1.000000s user + 0.046875s system = 1.046875s CPU (99.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000109401
PHY-3002 : Step(105): len = 360451, overlap = 152.562
PHY-3002 : Step(106): len = 371656, overlap = 133.719
PHY-3002 : Step(107): len = 370435, overlap = 130.406
PHY-3002 : Step(108): len = 370610, overlap = 125.344
PHY-3002 : Step(109): len = 377765, overlap = 110.5
PHY-3002 : Step(110): len = 382374, overlap = 95.2188
PHY-3002 : Step(111): len = 384627, overlap = 83.875
PHY-3002 : Step(112): len = 388587, overlap = 78.4062
PHY-3002 : Step(113): len = 389533, overlap = 76.1875
PHY-3002 : Step(114): len = 392564, overlap = 80
PHY-3002 : Step(115): len = 392938, overlap = 85.1875
PHY-3002 : Step(116): len = 393079, overlap = 86.1562
PHY-3002 : Step(117): len = 394528, overlap = 87.3125
PHY-3002 : Step(118): len = 395133, overlap = 90.3125
PHY-3002 : Step(119): len = 396768, overlap = 92.625
PHY-3002 : Step(120): len = 396930, overlap = 90.125
PHY-3002 : Step(121): len = 398139, overlap = 88.9375
PHY-3002 : Step(122): len = 399108, overlap = 92.0938
PHY-3002 : Step(123): len = 400035, overlap = 92.9062
PHY-3002 : Step(124): len = 399211, overlap = 92.5625
PHY-3002 : Step(125): len = 399572, overlap = 95.4688
PHY-3002 : Step(126): len = 400673, overlap = 96.3125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000218802
PHY-3002 : Step(127): len = 400343, overlap = 94.2812
PHY-3002 : Step(128): len = 402731, overlap = 93.3125
PHY-3002 : Step(129): len = 404411, overlap = 94.8438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(130): len = 407460, overlap = 92.3125
PHY-3002 : Step(131): len = 414606, overlap = 86.75
PHY-3002 : Step(132): len = 420673, overlap = 82.6875
PHY-3002 : Step(133): len = 422421, overlap = 82.6875
PHY-3002 : Step(134): len = 424519, overlap = 77.9062
PHY-3002 : Step(135): len = 426166, overlap = 74.625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 80/21712.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 490344, over cnt = 2146(6%), over = 9732, worst = 35
PHY-1001 : End global iterations;  1.073263s wall, 1.843750s user + 0.078125s system = 1.921875s CPU (179.1%)

PHY-1001 : Congestion index: top1 = 70.41, top5 = 54.25, top10 = 46.88, top15 = 42.45.
PHY-3001 : End congestion estimation;  1.351990s wall, 2.125000s user + 0.078125s system = 2.203125s CPU (163.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21710 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.020618s wall, 0.984375s user + 0.046875s system = 1.031250s CPU (101.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000113242
PHY-3002 : Step(136): len = 430642, overlap = 352.375
PHY-3002 : Step(137): len = 433906, overlap = 296.469
PHY-3002 : Step(138): len = 428300, overlap = 269.656
PHY-3002 : Step(139): len = 425404, overlap = 253.812
PHY-3002 : Step(140): len = 423773, overlap = 238.906
PHY-3002 : Step(141): len = 420802, overlap = 232.031
PHY-3002 : Step(142): len = 417940, overlap = 227.562
PHY-3002 : Step(143): len = 416367, overlap = 220.562
PHY-3002 : Step(144): len = 416428, overlap = 208.375
PHY-3002 : Step(145): len = 413864, overlap = 213.25
PHY-3002 : Step(146): len = 412140, overlap = 204.625
PHY-3002 : Step(147): len = 411368, overlap = 203.719
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000226485
PHY-3002 : Step(148): len = 412575, overlap = 196
PHY-3002 : Step(149): len = 414354, overlap = 196.438
PHY-3002 : Step(150): len = 414644, overlap = 186.531
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00045297
PHY-3002 : Step(151): len = 419934, overlap = 176.312
PHY-3002 : Step(152): len = 427996, overlap = 159.719
PHY-3002 : Step(153): len = 428943, overlap = 158.469
PHY-3002 : Step(154): len = 428863, overlap = 151.844
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 79731, tnet num: 21710, tinst num: 19230, tnode num: 112077, tedge num: 125347.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.537396s wall, 1.484375s user + 0.046875s system = 1.531250s CPU (99.6%)

RUN-1004 : used memory is 564 MB, reserved memory is 538 MB, peak memory is 695 MB
OPT-1001 : Total overflow 498.03 peak overflow 3.72
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 761/21712.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 504288, over cnt = 2332(6%), over = 8422, worst = 27
PHY-1001 : End global iterations;  1.255002s wall, 1.984375s user + 0.015625s system = 2.000000s CPU (159.4%)

PHY-1001 : Congestion index: top1 = 54.57, top5 = 45.67, top10 = 41.00, top15 = 38.05.
PHY-1001 : End incremental global routing;  1.519828s wall, 2.250000s user + 0.015625s system = 2.265625s CPU (149.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21710 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.321217s wall, 1.312500s user + 0.000000s system = 1.312500s CPU (99.3%)

OPT-1001 : 18 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19150 has valid locations, 226 needs to be replaced
PHY-3001 : design contains 19438 instances, 5656 luts, 12208 seqs, 1488 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 443682
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16888/21920.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 514104, over cnt = 2354(6%), over = 8452, worst = 26
PHY-1001 : End global iterations;  0.214456s wall, 0.328125s user + 0.015625s system = 0.343750s CPU (160.3%)

PHY-1001 : Congestion index: top1 = 54.87, top5 = 45.96, top10 = 41.18, top15 = 38.26.
PHY-3001 : End congestion estimation;  0.525463s wall, 0.640625s user + 0.015625s system = 0.656250s CPU (124.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80405, tnet num: 21918, tinst num: 19438, tnode num: 113019, tedge num: 126279.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.802513s wall, 1.765625s user + 0.031250s system = 1.796875s CPU (99.7%)

RUN-1004 : used memory is 607 MB, reserved memory is 589 MB, peak memory is 698 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21918 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.988649s wall, 2.921875s user + 0.062500s system = 2.984375s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(155): len = 443495, overlap = 3.4375
PHY-3002 : Step(156): len = 444431, overlap = 3.5625
PHY-3002 : Step(157): len = 445134, overlap = 3.4375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 16927/21920.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 512824, over cnt = 2367(6%), over = 8533, worst = 27
PHY-1001 : End global iterations;  0.196808s wall, 0.296875s user + 0.031250s system = 0.328125s CPU (166.7%)

PHY-1001 : Congestion index: top1 = 54.98, top5 = 46.26, top10 = 41.54, top15 = 38.55.
PHY-3001 : End congestion estimation;  0.486521s wall, 0.578125s user + 0.031250s system = 0.609375s CPU (125.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21918 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.189019s wall, 1.171875s user + 0.015625s system = 1.187500s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000660634
PHY-3002 : Step(158): len = 445032, overlap = 153.812
PHY-3002 : Step(159): len = 445211, overlap = 153.375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00132127
PHY-3002 : Step(160): len = 445781, overlap = 153.031
PHY-3002 : Step(161): len = 446418, overlap = 153.156
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00264254
PHY-3002 : Step(162): len = 446339, overlap = 152.844
PHY-3002 : Step(163): len = 446577, overlap = 152.344
PHY-3001 : Final: Len = 446577, Over = 152.344
PHY-3001 : End incremental placement;  6.282093s wall, 6.718750s user + 0.359375s system = 7.078125s CPU (112.7%)

OPT-1001 : Total overflow 501.91 peak overflow 3.72
OPT-1001 : End high-fanout net optimization;  9.784734s wall, 11.093750s user + 0.375000s system = 11.468750s CPU (117.2%)

OPT-1001 : Current memory(MB): used = 700, reserve = 679, peak = 716.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16899/21920.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 514888, over cnt = 2313(6%), over = 7996, worst = 26
PHY-1002 : len = 562720, over cnt = 1555(4%), over = 3616, worst = 25
PHY-1002 : len = 597368, over cnt = 555(1%), over = 1032, worst = 25
PHY-1002 : len = 610568, over cnt = 72(0%), over = 135, worst = 8
PHY-1002 : len = 613640, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.324483s wall, 2.218750s user + 0.000000s system = 2.218750s CPU (167.5%)

PHY-1001 : Congestion index: top1 = 46.55, top5 = 41.29, top10 = 38.31, top15 = 36.41.
OPT-1001 : End congestion update;  1.591148s wall, 2.500000s user + 0.000000s system = 2.500000s CPU (157.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 21918 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.930328s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (99.1%)

OPT-0007 : Start: WNS 4087 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.527596s wall, 3.437500s user + 0.000000s system = 3.437500s CPU (136.0%)

OPT-1001 : Current memory(MB): used = 676, reserve = 655, peak = 716.
OPT-1001 : End physical optimization;  14.179689s wall, 16.328125s user + 0.500000s system = 16.828125s CPU (118.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5656 LUT to BLE ...
SYN-4008 : Packed 5656 LUT and 2730 SEQ to BLE.
SYN-4003 : Packing 9478 remaining SEQ's ...
SYN-4005 : Packed 3243 SEQ with LUT/SLICE
SYN-4006 : 206 single LUT's are left
SYN-4006 : 6235 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11891/13731 primitive instances ...
PHY-3001 : End packing;  2.942156s wall, 2.937500s user + 0.015625s system = 2.953125s CPU (100.4%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8063 instances
RUN-1001 : 3988 mslices, 3987 lslices, 59 pads, 24 brams, 0 dsps
RUN-1001 : There are total 19243 nets
RUN-1001 : 13551 nets have 2 pins
RUN-1001 : 4290 nets have [3 - 5] pins
RUN-1001 : 888 nets have [6 - 10] pins
RUN-1001 : 380 nets have [11 - 20] pins
RUN-1001 : 125 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8061 instances, 7975 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 463884, Over = 362.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7818/19243.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 582360, over cnt = 1481(4%), over = 2458, worst = 9
PHY-1002 : len = 588960, over cnt = 1000(2%), over = 1423, worst = 9
PHY-1002 : len = 601952, over cnt = 339(0%), over = 452, worst = 5
PHY-1002 : len = 608528, over cnt = 63(0%), over = 84, worst = 4
PHY-1002 : len = 610424, over cnt = 2(0%), over = 3, worst = 2
PHY-1001 : End global iterations;  1.293802s wall, 2.171875s user + 0.015625s system = 2.187500s CPU (169.1%)

PHY-1001 : Congestion index: top1 = 48.38, top5 = 41.85, top10 = 38.50, top15 = 36.31.
PHY-3001 : End congestion estimation;  1.653918s wall, 2.515625s user + 0.015625s system = 2.531250s CPU (153.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66673, tnet num: 19241, tinst num: 8061, tnode num: 90386, tedge num: 109924.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.882991s wall, 1.859375s user + 0.031250s system = 1.890625s CPU (100.4%)

RUN-1004 : used memory is 599 MB, reserved memory is 590 MB, peak memory is 716 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19241 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.861365s wall, 2.828125s user + 0.046875s system = 2.875000s CPU (100.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.07959e-05
PHY-3002 : Step(164): len = 467654, overlap = 359.25
PHY-3002 : Step(165): len = 469347, overlap = 366.5
PHY-3002 : Step(166): len = 472424, overlap = 377.25
PHY-3002 : Step(167): len = 471622, overlap = 389
PHY-3002 : Step(168): len = 470245, overlap = 398.75
PHY-3002 : Step(169): len = 469497, overlap = 402.25
PHY-3002 : Step(170): len = 467212, overlap = 409.25
PHY-3002 : Step(171): len = 465863, overlap = 408
PHY-3002 : Step(172): len = 464114, overlap = 404.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000101592
PHY-3002 : Step(173): len = 468555, overlap = 394.5
PHY-3002 : Step(174): len = 471917, overlap = 384
PHY-3002 : Step(175): len = 471979, overlap = 387
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000203184
PHY-3002 : Step(176): len = 479794, overlap = 380
PHY-3002 : Step(177): len = 490717, overlap = 350.25
PHY-3002 : Step(178): len = 490925, overlap = 344.5
PHY-3002 : Step(179): len = 489184, overlap = 346
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.790257s wall, 0.765625s user + 1.046875s system = 1.812500s CPU (229.4%)

PHY-3001 : Trial Legalized: Len = 595603
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 688/19243.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 675272, over cnt = 2268(6%), over = 3604, worst = 8
PHY-1002 : len = 690632, over cnt = 1332(3%), over = 1739, worst = 6
PHY-1002 : len = 701200, over cnt = 695(1%), over = 903, worst = 5
PHY-1002 : len = 709104, over cnt = 334(0%), over = 441, worst = 4
PHY-1002 : len = 716664, over cnt = 10(0%), over = 17, worst = 3
PHY-1001 : End global iterations;  1.933921s wall, 3.250000s user + 0.015625s system = 3.265625s CPU (168.9%)

PHY-1001 : Congestion index: top1 = 48.94, top5 = 44.01, top10 = 41.31, top15 = 39.54.
PHY-3001 : End congestion estimation;  2.330217s wall, 3.625000s user + 0.046875s system = 3.671875s CPU (157.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19241 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.958508s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (99.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000182296
PHY-3002 : Step(180): len = 553380, overlap = 76
PHY-3002 : Step(181): len = 535149, overlap = 117.75
PHY-3002 : Step(182): len = 524337, overlap = 169.25
PHY-3002 : Step(183): len = 518451, overlap = 205
PHY-3002 : Step(184): len = 514811, overlap = 221
PHY-3002 : Step(185): len = 513573, overlap = 232.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000364592
PHY-3002 : Step(186): len = 516163, overlap = 233.5
PHY-3002 : Step(187): len = 520036, overlap = 234
PHY-3002 : Step(188): len = 523234, overlap = 234.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000729184
PHY-3002 : Step(189): len = 525263, overlap = 232.25
PHY-3002 : Step(190): len = 532360, overlap = 227
PHY-3002 : Step(191): len = 536221, overlap = 228.75
PHY-3002 : Step(192): len = 537614, overlap = 230.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.035022s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (89.2%)

PHY-3001 : Legalized: Len = 576790, Over = 0
PHY-3001 : Spreading special nets. 24 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.085637s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (109.5%)

PHY-3001 : 36 instances has been re-located, deltaX = 11, deltaY = 30, maxDist = 2.
PHY-3001 : Final: Len = 577284, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66673, tnet num: 19241, tinst num: 8061, tnode num: 90386, tedge num: 109924.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.992703s wall, 1.984375s user + 0.000000s system = 1.984375s CPU (99.6%)

RUN-1004 : used memory is 601 MB, reserved memory is 590 MB, peak memory is 716 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3839/19243.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 665568, over cnt = 2103(5%), over = 3329, worst = 7
PHY-1002 : len = 678096, over cnt = 1243(3%), over = 1645, worst = 5
PHY-1002 : len = 690224, over cnt = 547(1%), over = 715, worst = 4
PHY-1002 : len = 698472, over cnt = 162(0%), over = 202, worst = 3
PHY-1002 : len = 702008, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.707382s wall, 2.812500s user + 0.062500s system = 2.875000s CPU (168.4%)

PHY-1001 : Congestion index: top1 = 47.31, top5 = 42.78, top10 = 39.94, top15 = 38.13.
PHY-1001 : End incremental global routing;  2.052191s wall, 3.140625s user + 0.062500s system = 3.203125s CPU (156.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19241 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.128605s wall, 1.093750s user + 0.031250s system = 1.125000s CPU (99.7%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 7997 has valid locations, 13 needs to be replaced
PHY-3001 : design contains 8072 instances, 7986 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 578989
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17248/19253.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 703352, over cnt = 24(0%), over = 25, worst = 2
PHY-1002 : len = 703336, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 703384, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 703400, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.539155s wall, 0.593750s user + 0.031250s system = 0.625000s CPU (115.9%)

PHY-1001 : Congestion index: top1 = 47.16, top5 = 42.84, top10 = 39.98, top15 = 38.17.
PHY-3001 : End congestion estimation;  0.860584s wall, 0.906250s user + 0.031250s system = 0.937500s CPU (108.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66748, tnet num: 19251, tinst num: 8072, tnode num: 90476, tedge num: 110017.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.945263s wall, 1.921875s user + 0.015625s system = 1.937500s CPU (99.6%)

RUN-1004 : used memory is 630 MB, reserved memory is 612 MB, peak memory is 716 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19251 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.933217s wall, 2.890625s user + 0.031250s system = 2.921875s CPU (99.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(193): len = 578986, overlap = 0.25
PHY-3002 : Step(194): len = 578822, overlap = 0
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17242/19253.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 703032, over cnt = 24(0%), over = 25, worst = 2
PHY-1002 : len = 703016, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 703112, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 703128, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 703160, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.711075s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (107.7%)

PHY-1001 : Congestion index: top1 = 47.22, top5 = 42.87, top10 = 40.02, top15 = 38.20.
PHY-3001 : End congestion estimation;  1.026080s wall, 1.078125s user + 0.000000s system = 1.078125s CPU (105.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19251 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.953625s wall, 0.937500s user + 0.015625s system = 0.953125s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000740842
PHY-3002 : Step(195): len = 578814, overlap = 2
PHY-3002 : Step(196): len = 578798, overlap = 2.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007403s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (211.1%)

PHY-3001 : Legalized: Len = 578918, Over = 0
PHY-3001 : End spreading;  0.073782s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (105.9%)

PHY-3001 : Final: Len = 578918, Over = 0
PHY-3001 : End incremental placement;  6.431574s wall, 6.718750s user + 0.140625s system = 6.859375s CPU (106.7%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.127972s wall, 11.484375s user + 0.234375s system = 11.718750s CPU (115.7%)

OPT-1001 : Current memory(MB): used = 709, reserve = 697, peak = 716.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17242/19253.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 703272, over cnt = 13(0%), over = 14, worst = 2
PHY-1002 : len = 703256, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 703312, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 703328, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 703344, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.714224s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (105.0%)

PHY-1001 : Congestion index: top1 = 47.22, top5 = 42.80, top10 = 39.98, top15 = 38.16.
OPT-1001 : End congestion update;  1.027356s wall, 1.078125s user + 0.000000s system = 1.078125s CPU (104.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19251 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.816088s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (99.6%)

OPT-0007 : Start: WNS 4512 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.848636s wall, 1.890625s user + 0.000000s system = 1.890625s CPU (102.3%)

OPT-1001 : Current memory(MB): used = 709, reserve = 697, peak = 716.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19251 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.810906s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (100.2%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17258/19253.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 703344, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.126869s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (98.5%)

PHY-1001 : Congestion index: top1 = 47.22, top5 = 42.80, top10 = 39.98, top15 = 38.16.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19251 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.818600s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (99.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4512 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 46.862069
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4512ps with logic level 8 
RUN-1001 :       #2 path slack 4519ps with logic level 8 
RUN-1001 :       #3 path slack 4602ps with logic level 5 
OPT-1001 : End physical optimization;  16.314962s wall, 17.875000s user + 0.250000s system = 18.125000s CPU (111.1%)

RUN-1003 : finish command "place" in  67.878060s wall, 121.578125s user + 7.250000s system = 128.828125s CPU (189.8%)

RUN-1004 : used memory is 628 MB, reserved memory is 616 MB, peak memory is 716 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.638466s wall, 2.843750s user + 0.000000s system = 2.843750s CPU (173.6%)

RUN-1004 : used memory is 629 MB, reserved memory is 617 MB, peak memory is 716 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8074 instances
RUN-1001 : 3988 mslices, 3998 lslices, 59 pads, 24 brams, 0 dsps
RUN-1001 : There are total 19253 nets
RUN-1001 : 13551 nets have 2 pins
RUN-1001 : 4289 nets have [3 - 5] pins
RUN-1001 : 895 nets have [6 - 10] pins
RUN-1001 : 382 nets have [11 - 20] pins
RUN-1001 : 127 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66748, tnet num: 19251, tinst num: 8072, tnode num: 90476, tedge num: 110017.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.742700s wall, 1.734375s user + 0.000000s system = 1.734375s CPU (99.5%)

RUN-1004 : used memory is 611 MB, reserved memory is 599 MB, peak memory is 716 MB
PHY-1001 : 3988 mslices, 3998 lslices, 59 pads, 24 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19251 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 646376, over cnt = 2259(6%), over = 3707, worst = 7
PHY-1002 : len = 663224, over cnt = 1276(3%), over = 1749, worst = 6
PHY-1002 : len = 675824, over cnt = 612(1%), over = 774, worst = 4
PHY-1002 : len = 686512, over cnt = 82(0%), over = 100, worst = 4
PHY-1002 : len = 688568, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.745389s wall, 3.093750s user + 0.031250s system = 3.125000s CPU (179.0%)

PHY-1001 : Congestion index: top1 = 46.70, top5 = 42.53, top10 = 39.76, top15 = 37.96.
PHY-1001 : End global routing;  2.108267s wall, 3.437500s user + 0.046875s system = 3.484375s CPU (165.3%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 694, reserve = 683, peak = 716.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 968, reserve = 955, peak = 968.
PHY-1001 : End build detailed router design. 4.672677s wall, 4.656250s user + 0.015625s system = 4.671875s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 188160, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.933153s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (98.8%)

PHY-1001 : Current memory(MB): used = 1004, reserve = 992, peak = 1004.
PHY-1001 : End phase 1; 0.941190s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (99.6%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.59126e+06, over cnt = 1272(0%), over = 1278, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1018, reserve = 1007, peak = 1018.
PHY-1001 : End initial routed; 14.621426s wall, 43.359375s user + 0.437500s system = 43.796875s CPU (299.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17993(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.200   |   0.000   |   0   
RUN-1001 :   Hold   |   0.130   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.905521s wall, 3.906250s user + 0.000000s system = 3.906250s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1023, reserve = 1012, peak = 1023.
PHY-1001 : End phase 2; 18.527118s wall, 47.265625s user + 0.437500s system = 47.703125s CPU (257.5%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.59126e+06, over cnt = 1272(0%), over = 1278, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.261557s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (101.6%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.58111e+06, over cnt = 503(0%), over = 503, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.712613s wall, 1.265625s user + 0.015625s system = 1.281250s CPU (179.8%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.58128e+06, over cnt = 95(0%), over = 95, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.440074s wall, 0.734375s user + 0.015625s system = 0.750000s CPU (170.4%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.5824e+06, over cnt = 13(0%), over = 13, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.247862s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (113.5%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.5827e+06, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.192184s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (105.7%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.58277e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.163482s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (95.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17993(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.200   |   0.000   |   0   
RUN-1001 :   Hold   |   0.112   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.925840s wall, 3.937500s user + 0.000000s system = 3.937500s CPU (100.3%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 282 feed throughs used by 245 nets
PHY-1001 : End commit to database; 2.268195s wall, 2.250000s user + 0.015625s system = 2.265625s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 1112, reserve = 1104, peak = 1112.
PHY-1001 : End phase 3; 8.738064s wall, 9.609375s user + 0.046875s system = 9.656250s CPU (110.5%)

PHY-1003 : Routed, final wirelength = 1.58277e+06
PHY-1001 : Current memory(MB): used = 1117, reserve = 1109, peak = 1117.
PHY-1001 : End export database. 0.067641s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (92.4%)

PHY-1001 : End detail routing;  33.425618s wall, 63.015625s user + 0.500000s system = 63.515625s CPU (190.0%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66748, tnet num: 19251, tinst num: 8072, tnode num: 90476, tedge num: 110017.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.730746s wall, 1.734375s user + 0.000000s system = 1.734375s CPU (100.2%)

RUN-1004 : used memory is 1053 MB, reserved memory is 1050 MB, peak memory is 1117 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  41.740844s wall, 72.656250s user + 0.562500s system = 73.218750s CPU (175.4%)

RUN-1004 : used memory is 1053 MB, reserved memory is 1050 MB, peak memory is 1117 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8798   out of  19600   44.89%
#reg                    12304   out of  19600   62.78%
#le                     14985
  #lut only              2681   out of  14985   17.89%
  #reg only              6187   out of  14985   41.29%
  #lut&reg               6117   out of  14985   40.82%
#dsp                        0   out of     29    0.00%
#bram                      24   out of     64   37.50%
  #bram9k                  22
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       59   out of    188   31.38%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6780
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          102
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         F2        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         D1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         B3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         C1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         A3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         A2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         B2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         B1        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F15        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDZ        INPUT        K15        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         P2        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         N4        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         M5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         N6        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         N5        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         P5        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M3        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         M4        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         H2        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         M1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         L1        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         N1        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         N3        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         M2        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         T4        LVCMOS33           8            NONE           OREG       
    Fog_ENZ        OUTPUT         T7        LVCMOS33           8            NONE           OREG       
    Fog_INT        OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT         C3        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT        B15        LVCMOS33           8            N/A            OREG       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        R15        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         K2        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT         F6        LVCMOS25           8            N/A            NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |14985  |7310    |1488    |12348   |24      |0       |
|  AnyFog_dataX                      |AnyFog          |216    |83      |22      |176     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |95     |63      |22      |55      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |202    |89      |22      |165     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |85     |59      |22      |48      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |207    |79      |22      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |60      |22      |52      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2936   |599     |39      |2855    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |63     |38      |5       |55      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |221    |74      |5       |208     |0       |0       |
|    STADOP_com2                     |STADOP          |549    |42      |0       |546     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |40      |14      |42      |0       |0       |
|    head_com2                       |uniheading      |265    |127     |5       |253     |0       |0       |
|    rmc_com2                        |Gprmc           |35     |35      |0       |33      |0       |0       |
|    uart_com2                       |Agrica          |1427   |223     |10      |1407    |0       |0       |
|  COM3                              |COM3_Control    |276    |159     |19      |234     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |61     |44      |5       |51      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |61     |40      |14      |38      |0       |0       |
|    rmc_com3                        |Gprmc           |154    |75      |0       |145     |0       |0       |
|  DATA                              |Data_Processing |8820   |4527    |1122    |7021    |0       |0       |
|    DIV_Dtemp                       |Divider         |771    |354     |84      |639     |0       |0       |
|    DIV_Utemp                       |Divider         |655    |307     |84      |531     |0       |0       |
|    DIV_accX                        |Divider         |593    |296     |84      |470     |0       |0       |
|    DIV_accY                        |Divider         |652    |354     |102     |500     |0       |0       |
|    DIV_accZ                        |Divider         |663    |343     |132     |459     |0       |0       |
|    DIV_rateX                       |Divider         |673    |417     |132     |464     |0       |0       |
|    DIV_rateY                       |Divider         |596    |341     |132     |389     |0       |0       |
|    DIV_rateZ                       |Divider         |539    |295     |132     |333     |0       |0       |
|    genclk                          |genclk          |259    |162     |89      |100     |0       |0       |
|  FMC                               |FMC_Ctrl        |498    |444     |43      |360     |0       |0       |
|  IIC                               |I2C_master      |324    |256     |11      |269     |0       |0       |
|  IMU_CTRL                          |SCHA634         |937    |735     |61      |709     |0       |0       |
|    CtrlData                        |CtrlData        |513    |460     |47      |336     |0       |0       |
|      usms                          |Time_1ms        |33     |27      |5       |21      |0       |0       |
|    SPIM                            |SPI_SCHA634     |424    |275     |14      |373     |0       |0       |
|  POWER                             |POWER_EN        |95     |53      |38      |36      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |466    |286     |89      |298     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |466    |286     |89      |298     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |180    |102     |0       |165     |0       |0       |
|        reg_inst                    |register        |177    |99      |0       |162     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |286    |184     |89      |133     |0       |0       |
|        bus_inst                    |bus_top         |79     |51      |28      |30      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |28     |18      |10      |12      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |51     |33      |18      |18      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |124    |86      |29      |75      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13491  
    #2          2       3375   
    #3          3        625   
    #4          4        289   
    #5        5-10       951   
    #6        11-50      446   
    #7       51-100       7    
    #8       101-500      3    
    #9        >500        2    
  Average     2.11             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.111646s wall, 3.671875s user + 0.015625s system = 3.687500s CPU (174.6%)

RUN-1004 : used memory is 1053 MB, reserved memory is 1051 MB, peak memory is 1117 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66748, tnet num: 19251, tinst num: 8072, tnode num: 90476, tedge num: 110017.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.723618s wall, 1.718750s user + 0.000000s system = 1.718750s CPU (99.7%)

RUN-1004 : used memory is 1055 MB, reserved memory is 1053 MB, peak memory is 1117 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19251 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.460600s wall, 1.468750s user + 0.000000s system = 1.468750s CPU (100.6%)

RUN-1004 : used memory is 1060 MB, reserved memory is 1057 MB, peak memory is 1117 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 057bf4ae9dc0d18f7126c20ce7aec8c31cdd0d41110c6b742e6e5620711a0ef6 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8072
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19253, pip num: 141954
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 282
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3229 valid insts, and 401099 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101000001010010110001101
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.605148s wall, 125.531250s user + 0.234375s system = 125.765625s CPU (997.7%)

RUN-1004 : used memory is 1184 MB, reserved memory is 1170 MB, peak memory is 1298 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250820_140346.log"

============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Aug 21 14:42:20 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.846294s wall, 1.562500s user + 4.281250s system = 5.843750s CPU (100.0%)

RUN-1004 : used memory is 79 MB, reserved memory is 40 MB, peak memory is 90 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.934655s wall, 1.859375s user + 0.078125s system = 1.937500s CPU (100.1%)

RUN-1004 : used memory is 302 MB, reserved memory is 270 MB, peak memory is 305 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 42 trigger nets, 42 data nets.
KIT-1004 : Chipwatcher code = 0000101011000101
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=126) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=126) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=126)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=126)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22794/23 useful/useless nets, 19542/12 useful/useless insts
SYN-1016 : Merged 30 instances.
SYN-1032 : 22461/20 useful/useless nets, 19967/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 398 better
SYN-1014 : Optimize round 2
SYN-1032 : 22141/45 useful/useless nets, 19647/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.609891s wall, 2.546875s user + 0.062500s system = 2.609375s CPU (100.0%)

RUN-1004 : used memory is 327 MB, reserved memory is 294 MB, peak memory is 329 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22189/299 useful/useless nets, 19732/47 useful/useless insts
SYN-1016 : Merged 45 instances.
SYN-2571 : Optimize after map_dsp, round 1, 391 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 40 instances.
SYN-2501 : Optimize round 1, 82 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 22637/5 useful/useless nets, 20180/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82610, tnet num: 22637, tinst num: 20179, tnode num: 115699, tedge num: 129160.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.290187s wall, 1.234375s user + 0.046875s system = 1.281250s CPU (99.3%)

RUN-1004 : used memory is 468 MB, reserved memory is 436 MB, peak memory is 468 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22637 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 234 (3.43), #lev = 7 (1.79)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 234 (3.43), #lev = 7 (1.79)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 534 instances into 234 LUTs, name keeping = 75%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 407 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 128 adder to BLE ...
SYN-4008 : Packed 128 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.915451s wall, 4.828125s user + 0.093750s system = 4.921875s CPU (100.1%)

RUN-1004 : used memory is 349 MB, reserved memory is 326 MB, peak memory is 577 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.879639s wall, 7.718750s user + 0.171875s system = 7.890625s CPU (100.1%)

RUN-1004 : used memory is 350 MB, reserved memory is 327 MB, peak memory is 577 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (271 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19420 instances
RUN-0007 : 5609 luts, 12204 seqs, 983 mslices, 519 lslices, 60 pads, 40 brams, 0 dsps
RUN-1001 : There are total 21885 nets
RUN-1001 : 16415 nets have 2 pins
RUN-1001 : 4278 nets have [3 - 5] pins
RUN-1001 : 814 nets have [6 - 10] pins
RUN-1001 : 251 nets have [11 - 20] pins
RUN-1001 : 105 nets have [21 - 99] pins
RUN-1001 : 22 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4788     
RUN-1001 :   No   |  No   |  Yes  |     704     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     443     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19418 instances, 5609 luts, 12204 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80987, tnet num: 21883, tinst num: 19418, tnode num: 113867, tedge num: 127482.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.378777s wall, 1.359375s user + 0.031250s system = 1.390625s CPU (100.9%)

RUN-1004 : used memory is 528 MB, reserved memory is 499 MB, peak memory is 577 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21883 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.353077s wall, 2.281250s user + 0.062500s system = 2.343750s CPU (99.6%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.68055e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19418.
PHY-3001 : Level 1 #clusters 2162.
PHY-3001 : End clustering;  0.172149s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (163.4%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 854126, overlap = 641.844
PHY-3002 : Step(2): len = 774863, overlap = 705.812
PHY-3002 : Step(3): len = 513930, overlap = 886.562
PHY-3002 : Step(4): len = 458054, overlap = 955.062
PHY-3002 : Step(5): len = 362019, overlap = 1048.75
PHY-3002 : Step(6): len = 326455, overlap = 1102.5
PHY-3002 : Step(7): len = 264376, overlap = 1191.56
PHY-3002 : Step(8): len = 239822, overlap = 1256
PHY-3002 : Step(9): len = 214157, overlap = 1289.72
PHY-3002 : Step(10): len = 196828, overlap = 1306.41
PHY-3002 : Step(11): len = 181388, overlap = 1328.31
PHY-3002 : Step(12): len = 168829, overlap = 1343.03
PHY-3002 : Step(13): len = 156194, overlap = 1354.12
PHY-3002 : Step(14): len = 146123, overlap = 1382.66
PHY-3002 : Step(15): len = 133002, overlap = 1405.53
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.4936e-06
PHY-3002 : Step(16): len = 141813, overlap = 1375.25
PHY-3002 : Step(17): len = 205358, overlap = 1218.44
PHY-3002 : Step(18): len = 216766, overlap = 1133.59
PHY-3002 : Step(19): len = 218860, overlap = 1075.97
PHY-3002 : Step(20): len = 215308, overlap = 1023.28
PHY-3002 : Step(21): len = 209096, overlap = 1012.28
PHY-3002 : Step(22): len = 203161, overlap = 1015.69
PHY-3002 : Step(23): len = 198718, overlap = 1004.91
PHY-3002 : Step(24): len = 195151, overlap = 973.656
PHY-3002 : Step(25): len = 190831, overlap = 967.438
PHY-3002 : Step(26): len = 186940, overlap = 965.75
PHY-3002 : Step(27): len = 184509, overlap = 999.125
PHY-3002 : Step(28): len = 183700, overlap = 1017.44
PHY-3002 : Step(29): len = 183138, overlap = 1025.31
PHY-3002 : Step(30): len = 182848, overlap = 1028.91
PHY-3002 : Step(31): len = 181083, overlap = 1047.88
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.9872e-06
PHY-3002 : Step(32): len = 188892, overlap = 1037.06
PHY-3002 : Step(33): len = 202718, overlap = 965
PHY-3002 : Step(34): len = 206887, overlap = 868.656
PHY-3002 : Step(35): len = 208589, overlap = 852.25
PHY-3002 : Step(36): len = 208535, overlap = 852.312
PHY-3002 : Step(37): len = 207547, overlap = 859.094
PHY-3002 : Step(38): len = 206452, overlap = 878.125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 5.97441e-06
PHY-3002 : Step(39): len = 219377, overlap = 836.938
PHY-3002 : Step(40): len = 233759, overlap = 787.562
PHY-3002 : Step(41): len = 237778, overlap = 765.688
PHY-3002 : Step(42): len = 238978, overlap = 729.656
PHY-3002 : Step(43): len = 237696, overlap = 708.344
PHY-3002 : Step(44): len = 236673, overlap = 688.781
PHY-3002 : Step(45): len = 235215, overlap = 677.406
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 1.19488e-05
PHY-3002 : Step(46): len = 249828, overlap = 643.688
PHY-3002 : Step(47): len = 263787, overlap = 586.75
PHY-3002 : Step(48): len = 268653, overlap = 572.469
PHY-3002 : Step(49): len = 270357, overlap = 576.781
PHY-3002 : Step(50): len = 268513, overlap = 564.344
PHY-3002 : Step(51): len = 266845, overlap = 550
PHY-3002 : Step(52): len = 264928, overlap = 552.719
PHY-3002 : Step(53): len = 264495, overlap = 547.531
PHY-3002 : Step(54): len = 263626, overlap = 544.562
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 2.38976e-05
PHY-3002 : Step(55): len = 275597, overlap = 499.781
PHY-3002 : Step(56): len = 291012, overlap = 451.594
PHY-3002 : Step(57): len = 294620, overlap = 424.844
PHY-3002 : Step(58): len = 294720, overlap = 425.969
PHY-3002 : Step(59): len = 292229, overlap = 429.188
PHY-3002 : Step(60): len = 290839, overlap = 420.781
PHY-3002 : Step(61): len = 288683, overlap = 403.625
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 4.77953e-05
PHY-3002 : Step(62): len = 297780, overlap = 402.125
PHY-3002 : Step(63): len = 307593, overlap = 386.969
PHY-3002 : Step(64): len = 311143, overlap = 396.469
PHY-3002 : Step(65): len = 311764, overlap = 390.562
PHY-3002 : Step(66): len = 309874, overlap = 395.406
PHY-3002 : Step(67): len = 307421, overlap = 403.594
PHY-3002 : Step(68): len = 305036, overlap = 391.938
PHY-3002 : Step(69): len = 304665, overlap = 392.438
PHY-3002 : Step(70): len = 304016, overlap = 382.781
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 9.55905e-05
PHY-3002 : Step(71): len = 310218, overlap = 375.688
PHY-3002 : Step(72): len = 315580, overlap = 341.562
PHY-3002 : Step(73): len = 317738, overlap = 333.875
PHY-3002 : Step(74): len = 318339, overlap = 317.5
PHY-3002 : Step(75): len = 316907, overlap = 312.094
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000191181
PHY-3002 : Step(76): len = 319359, overlap = 303.031
PHY-3002 : Step(77): len = 325832, overlap = 289.938
PHY-3002 : Step(78): len = 329150, overlap = 258.312
PHY-3002 : Step(79): len = 330545, overlap = 248.688
PHY-3002 : Step(80): len = 331254, overlap = 257.125
PHY-3002 : Step(81): len = 329733, overlap = 234.25
PHY-3002 : Step(82): len = 329737, overlap = 241.594
PHY-3002 : Step(83): len = 329227, overlap = 242.438
PHY-3002 : Step(84): len = 329808, overlap = 250.281
PHY-3002 : Step(85): len = 329120, overlap = 248.438
PHY-3002 : Step(86): len = 329662, overlap = 256.656
PHY-3002 : Step(87): len = 329003, overlap = 250.188
PHY-3002 : Step(88): len = 329061, overlap = 231.406
PHY-3002 : Step(89): len = 328785, overlap = 215.75
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000338352
PHY-3002 : Step(90): len = 330391, overlap = 212.375
PHY-3002 : Step(91): len = 333823, overlap = 219.969
PHY-3002 : Step(92): len = 335236, overlap = 224.312
PHY-3002 : Step(93): len = 335537, overlap = 232.562
PHY-3002 : Step(94): len = 334932, overlap = 227.125
PHY-3002 : Step(95): len = 334563, overlap = 227.156
PHY-3002 : Step(96): len = 334362, overlap = 233.812
PHY-3002 : Step(97): len = 335833, overlap = 238.062
PHY-3002 : Step(98): len = 335919, overlap = 244.562
PHY-3002 : Step(99): len = 335845, overlap = 238.906
PHY-3002 : Step(100): len = 335426, overlap = 249.969
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(101): len = 336377, overlap = 236.219
PHY-3002 : Step(102): len = 338160, overlap = 229.344
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.015949s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (98.0%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21885.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 443040, over cnt = 1171(3%), over = 5215, worst = 39
PHY-1001 : End global iterations;  0.921805s wall, 1.187500s user + 0.062500s system = 1.250000s CPU (135.6%)

PHY-1001 : Congestion index: top1 = 73.06, top5 = 52.01, top10 = 42.46, top15 = 37.08.
PHY-3001 : End congestion estimation;  1.168014s wall, 1.406250s user + 0.078125s system = 1.484375s CPU (127.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21883 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.008887s wall, 1.000000s user + 0.015625s system = 1.015625s CPU (100.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000110502
PHY-3002 : Step(103): len = 381800, overlap = 172.719
PHY-3002 : Step(104): len = 395750, overlap = 164.188
PHY-3002 : Step(105): len = 394594, overlap = 151.281
PHY-3002 : Step(106): len = 393972, overlap = 148.656
PHY-3002 : Step(107): len = 403146, overlap = 146.188
PHY-3002 : Step(108): len = 411517, overlap = 137.406
PHY-3002 : Step(109): len = 413045, overlap = 132.938
PHY-3002 : Step(110): len = 414546, overlap = 124.281
PHY-3002 : Step(111): len = 417292, overlap = 121.875
PHY-3002 : Step(112): len = 418431, overlap = 110.625
PHY-3002 : Step(113): len = 418724, overlap = 109.875
PHY-3002 : Step(114): len = 419372, overlap = 104.281
PHY-3002 : Step(115): len = 421573, overlap = 108.094
PHY-3002 : Step(116): len = 421591, overlap = 114.438
PHY-3002 : Step(117): len = 421250, overlap = 115.062
PHY-3002 : Step(118): len = 423573, overlap = 115.75
PHY-3002 : Step(119): len = 424726, overlap = 117.25
PHY-3002 : Step(120): len = 424705, overlap = 123.625
PHY-3002 : Step(121): len = 426560, overlap = 119.469
PHY-3002 : Step(122): len = 425945, overlap = 122.125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000221004
PHY-3002 : Step(123): len = 425751, overlap = 118.031
PHY-3002 : Step(124): len = 428391, overlap = 116.562
PHY-3002 : Step(125): len = 431495, overlap = 115.781
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(126): len = 436058, overlap = 117.719
PHY-3002 : Step(127): len = 445125, overlap = 117.469
PHY-3002 : Step(128): len = 452070, overlap = 117.969
PHY-3002 : Step(129): len = 449470, overlap = 112.375
PHY-3002 : Step(130): len = 448995, overlap = 111.094
PHY-3002 : Step(131): len = 447463, overlap = 108.344
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 71/21885.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 519224, over cnt = 2158(6%), over = 10016, worst = 45
PHY-1001 : End global iterations;  1.162886s wall, 1.937500s user + 0.031250s system = 1.968750s CPU (169.3%)

PHY-1001 : Congestion index: top1 = 77.28, top5 = 59.81, top10 = 50.73, top15 = 45.35.
PHY-3001 : End congestion estimation;  1.455252s wall, 2.218750s user + 0.031250s system = 2.250000s CPU (154.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21883 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.060594s wall, 1.046875s user + 0.015625s system = 1.062500s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000100271
PHY-3002 : Step(132): len = 452420, overlap = 372.562
PHY-3002 : Step(133): len = 458120, overlap = 303.281
PHY-3002 : Step(134): len = 453239, overlap = 271.906
PHY-3002 : Step(135): len = 450528, overlap = 256
PHY-3002 : Step(136): len = 448949, overlap = 238.062
PHY-3002 : Step(137): len = 445959, overlap = 237.344
PHY-3002 : Step(138): len = 443544, overlap = 233
PHY-3002 : Step(139): len = 441066, overlap = 227.625
PHY-3002 : Step(140): len = 438760, overlap = 217.875
PHY-3002 : Step(141): len = 436766, overlap = 218.531
PHY-3002 : Step(142): len = 435869, overlap = 222.5
PHY-3002 : Step(143): len = 433045, overlap = 217.781
PHY-3002 : Step(144): len = 430486, overlap = 214.469
PHY-3002 : Step(145): len = 428566, overlap = 217.125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000200541
PHY-3002 : Step(146): len = 429261, overlap = 209.125
PHY-3002 : Step(147): len = 430373, overlap = 204.75
PHY-3002 : Step(148): len = 430578, overlap = 202.312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000388276
PHY-3002 : Step(149): len = 433775, overlap = 192.875
PHY-3002 : Step(150): len = 441932, overlap = 179.219
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000745153
PHY-3002 : Step(151): len = 442905, overlap = 176.906
PHY-3002 : Step(152): len = 450965, overlap = 160.906
PHY-3002 : Step(153): len = 455808, overlap = 155.656
PHY-3002 : Step(154): len = 457485, overlap = 157.375
PHY-3002 : Step(155): len = 459528, overlap = 155.188
PHY-3002 : Step(156): len = 461428, overlap = 148.562
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00142431
PHY-3002 : Step(157): len = 461894, overlap = 151.188
PHY-3002 : Step(158): len = 463205, overlap = 152
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80987, tnet num: 21883, tinst num: 19418, tnode num: 113867, tedge num: 127482.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.707315s wall, 1.640625s user + 0.062500s system = 1.703125s CPU (99.8%)

RUN-1004 : used memory is 570 MB, reserved memory is 544 MB, peak memory is 702 MB
OPT-1001 : Total overflow 518.81 peak overflow 3.78
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 507/21885.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 545368, over cnt = 2532(7%), over = 8786, worst = 29
PHY-1001 : End global iterations;  1.401956s wall, 2.109375s user + 0.078125s system = 2.187500s CPU (156.0%)

PHY-1001 : Congestion index: top1 = 59.22, top5 = 47.33, top10 = 42.67, top15 = 39.85.
PHY-1001 : End incremental global routing;  1.668694s wall, 2.375000s user + 0.078125s system = 2.453125s CPU (147.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21883 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.109029s wall, 1.109375s user + 0.000000s system = 1.109375s CPU (100.0%)

OPT-1001 : 16 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19339 has valid locations, 232 needs to be replaced
PHY-3001 : design contains 19634 instances, 5691 luts, 12338 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 478607
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17069/22101.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 556488, over cnt = 2573(7%), over = 8798, worst = 29
PHY-1001 : End global iterations;  0.201444s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (108.6%)

PHY-1001 : Congestion index: top1 = 59.18, top5 = 47.64, top10 = 42.96, top15 = 40.12.
PHY-3001 : End congestion estimation;  0.589471s wall, 0.609375s user + 0.000000s system = 0.609375s CPU (103.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81693, tnet num: 22099, tinst num: 19634, tnode num: 114895, tedge num: 128462.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.698047s wall, 1.656250s user + 0.031250s system = 1.687500s CPU (99.4%)

RUN-1004 : used memory is 613 MB, reserved memory is 606 MB, peak memory is 704 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22099 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.837225s wall, 2.781250s user + 0.046875s system = 2.828125s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(159): len = 478910, overlap = 0.5625
PHY-3002 : Step(160): len = 479806, overlap = 0.5625
PHY-3002 : Step(161): len = 480750, overlap = 0.5625
PHY-3002 : Step(162): len = 481430, overlap = 0.6875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17095/22101.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 557136, over cnt = 2586(7%), over = 8828, worst = 29
PHY-1001 : End global iterations;  0.205881s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (98.7%)

PHY-1001 : Congestion index: top1 = 59.66, top5 = 47.96, top10 = 43.24, top15 = 40.38.
PHY-3001 : End congestion estimation;  0.578007s wall, 0.578125s user + 0.000000s system = 0.578125s CPU (100.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22099 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.089695s wall, 1.093750s user + 0.000000s system = 1.093750s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000607882
PHY-3002 : Step(163): len = 481339, overlap = 154.625
PHY-3002 : Step(164): len = 481590, overlap = 155.25
PHY-3002 : Step(165): len = 481795, overlap = 155.125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00121576
PHY-3002 : Step(166): len = 481867, overlap = 154.656
PHY-3002 : Step(167): len = 482149, overlap = 154.812
PHY-3001 : Final: Len = 482149, Over = 154.812
PHY-3001 : End incremental placement;  6.119726s wall, 6.562500s user + 0.187500s system = 6.750000s CPU (110.3%)

OPT-1001 : Total overflow 523.62 peak overflow 3.78
OPT-1001 : End high-fanout net optimization;  9.491686s wall, 10.750000s user + 0.265625s system = 11.015625s CPU (116.1%)

OPT-1001 : Current memory(MB): used = 707, reserve = 686, peak = 723.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17104/22101.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 560464, over cnt = 2562(7%), over = 8316, worst = 29
PHY-1002 : len = 603248, over cnt = 1696(4%), over = 4008, worst = 24
PHY-1002 : len = 628296, over cnt = 1008(2%), over = 2256, worst = 14
PHY-1002 : len = 647352, over cnt = 474(1%), over = 1054, worst = 12
PHY-1002 : len = 664952, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.319874s wall, 2.031250s user + 0.046875s system = 2.078125s CPU (157.4%)

PHY-1001 : Congestion index: top1 = 49.57, top5 = 43.46, top10 = 40.51, top15 = 38.51.
OPT-1001 : End congestion update;  1.598205s wall, 2.281250s user + 0.062500s system = 2.343750s CPU (146.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22099 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.935797s wall, 0.937500s user + 0.015625s system = 0.953125s CPU (101.9%)

OPT-0007 : Start: WNS 4287 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.541384s wall, 3.234375s user + 0.078125s system = 3.312500s CPU (130.3%)

OPT-1001 : Current memory(MB): used = 701, reserve = 681, peak = 723.
OPT-1001 : End physical optimization;  14.080811s wall, 16.093750s user + 0.437500s system = 16.531250s CPU (117.4%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5691 LUT to BLE ...
SYN-4008 : Packed 5691 LUT and 2731 SEQ to BLE.
SYN-4003 : Packing 9607 remaining SEQ's ...
SYN-4005 : Packed 3378 SEQ with LUT/SLICE
SYN-4006 : 106 single LUT's are left
SYN-4006 : 6229 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11920/13791 primitive instances ...
PHY-3001 : End packing;  3.088149s wall, 3.093750s user + 0.000000s system = 3.093750s CPU (100.2%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8170 instances
RUN-1001 : 4033 mslices, 4032 lslices, 60 pads, 40 brams, 0 dsps
RUN-1001 : There are total 19424 nets
RUN-1001 : 13600 nets have 2 pins
RUN-1001 : 4424 nets have [3 - 5] pins
RUN-1001 : 869 nets have [6 - 10] pins
RUN-1001 : 391 nets have [11 - 20] pins
RUN-1001 : 131 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8168 instances, 8065 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Cell area utilization is 86%
PHY-3001 : After packing: Len = 499334, Over = 405.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7800/19424.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 630856, over cnt = 1537(4%), over = 2521, worst = 8
PHY-1002 : len = 636888, over cnt = 941(2%), over = 1361, worst = 6
PHY-1002 : len = 647856, over cnt = 387(1%), over = 520, worst = 4
PHY-1002 : len = 653496, over cnt = 139(0%), over = 199, worst = 4
PHY-1002 : len = 656824, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.284163s wall, 1.968750s user + 0.031250s system = 2.000000s CPU (155.7%)

PHY-1001 : Congestion index: top1 = 49.81, top5 = 43.41, top10 = 40.19, top15 = 38.08.
PHY-3001 : End congestion estimation;  1.639496s wall, 2.312500s user + 0.031250s system = 2.343750s CPU (143.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67895, tnet num: 19422, tinst num: 8168, tnode num: 92139, tedge num: 112023.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.834836s wall, 1.828125s user + 0.000000s system = 1.828125s CPU (99.6%)

RUN-1004 : used memory is 602 MB, reserved memory is 583 MB, peak memory is 723 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19422 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.848511s wall, 2.828125s user + 0.015625s system = 2.843750s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.46825e-05
PHY-3002 : Step(168): len = 502962, overlap = 384.25
PHY-3002 : Step(169): len = 502015, overlap = 386.25
PHY-3002 : Step(170): len = 503072, overlap = 389.5
PHY-3002 : Step(171): len = 503170, overlap = 406
PHY-3002 : Step(172): len = 503949, overlap = 413.75
PHY-3002 : Step(173): len = 503200, overlap = 412.5
PHY-3002 : Step(174): len = 501772, overlap = 424.75
PHY-3002 : Step(175): len = 500208, overlap = 425.75
PHY-3002 : Step(176): len = 497903, overlap = 427.25
PHY-3002 : Step(177): len = 495671, overlap = 435.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.93649e-05
PHY-3002 : Step(178): len = 499899, overlap = 425.5
PHY-3002 : Step(179): len = 504353, overlap = 414.5
PHY-3002 : Step(180): len = 504858, overlap = 413.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00017873
PHY-3002 : Step(181): len = 514220, overlap = 384.75
PHY-3002 : Step(182): len = 526427, overlap = 367
PHY-3002 : Step(183): len = 524351, overlap = 361.5
PHY-3002 : Step(184): len = 522369, overlap = 358.75
PHY-3002 : Step(185): len = 522519, overlap = 348.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.663111s wall, 0.703125s user + 0.718750s system = 1.421875s CPU (214.4%)

PHY-3001 : Trial Legalized: Len = 638977
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 465/19424.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 726528, over cnt = 2452(6%), over = 4079, worst = 8
PHY-1002 : len = 742936, over cnt = 1392(3%), over = 1972, worst = 6
PHY-1002 : len = 761224, over cnt = 489(1%), over = 664, worst = 6
PHY-1002 : len = 771440, over cnt = 45(0%), over = 64, worst = 6
PHY-1002 : len = 772720, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.188160s wall, 3.671875s user + 0.031250s system = 3.703125s CPU (169.2%)

PHY-1001 : Congestion index: top1 = 50.47, top5 = 45.85, top10 = 43.11, top15 = 41.30.
PHY-3001 : End congestion estimation;  2.578363s wall, 4.062500s user + 0.031250s system = 4.093750s CPU (158.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19422 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.982729s wall, 0.984375s user + 0.000000s system = 0.984375s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000201462
PHY-3002 : Step(186): len = 592574, overlap = 94.5
PHY-3002 : Step(187): len = 572071, overlap = 140.5
PHY-3002 : Step(188): len = 558541, overlap = 191.25
PHY-3002 : Step(189): len = 551190, overlap = 225.25
PHY-3002 : Step(190): len = 548101, overlap = 253
PHY-3002 : Step(191): len = 546247, overlap = 265.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000402923
PHY-3002 : Step(192): len = 550363, overlap = 255.5
PHY-3002 : Step(193): len = 555348, overlap = 246.75
PHY-3002 : Step(194): len = 555220, overlap = 254.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(195): len = 557882, overlap = 247.5
PHY-3002 : Step(196): len = 563377, overlap = 249.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.036783s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (85.0%)

PHY-3001 : Legalized: Len = 605311, Over = 0
PHY-3001 : Spreading special nets. 45 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.089136s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (105.2%)

PHY-3001 : 70 instances has been re-located, deltaX = 16, deltaY = 45, maxDist = 2.
PHY-3001 : Final: Len = 606349, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67895, tnet num: 19422, tinst num: 8168, tnode num: 92139, tedge num: 112023.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.102217s wall, 2.093750s user + 0.015625s system = 2.109375s CPU (100.3%)

RUN-1004 : used memory is 602 MB, reserved memory is 581 MB, peak memory is 723 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3724/19424.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 706552, over cnt = 2272(6%), over = 3583, worst = 7
PHY-1002 : len = 716480, over cnt = 1519(4%), over = 2173, worst = 6
PHY-1002 : len = 734112, over cnt = 602(1%), over = 844, worst = 6
PHY-1002 : len = 741920, over cnt = 272(0%), over = 358, worst = 5
PHY-1002 : len = 747864, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.857607s wall, 2.890625s user + 0.000000s system = 2.890625s CPU (155.6%)

PHY-1001 : Congestion index: top1 = 48.23, top5 = 43.75, top10 = 41.32, top15 = 39.60.
PHY-1001 : End incremental global routing;  2.195920s wall, 3.234375s user + 0.000000s system = 3.234375s CPU (147.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19422 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.006885s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (99.3%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8104 has valid locations, 12 needs to be replaced
PHY-3001 : design contains 8179 instances, 8076 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 609532
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17501/19445.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 753008, over cnt = 16(0%), over = 23, worst = 3
PHY-1002 : len = 753000, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 753032, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 753072, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 753112, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.744060s wall, 0.734375s user + 0.015625s system = 0.750000s CPU (100.8%)

PHY-1001 : Congestion index: top1 = 48.38, top5 = 43.87, top10 = 41.46, top15 = 39.74.
PHY-3001 : End congestion estimation;  1.072473s wall, 1.078125s user + 0.015625s system = 1.093750s CPU (102.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67993, tnet num: 19443, tinst num: 8179, tnode num: 92259, tedge num: 112175.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.066646s wall, 2.062500s user + 0.000000s system = 2.062500s CPU (99.8%)

RUN-1004 : used memory is 668 MB, reserved memory is 663 MB, peak memory is 723 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19443 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.082848s wall, 3.046875s user + 0.031250s system = 3.078125s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(197): len = 609652, overlap = 1
PHY-3002 : Step(198): len = 609497, overlap = 0.5
PHY-3002 : Step(199): len = 609484, overlap = 0.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17496/19445.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 750632, over cnt = 24(0%), over = 35, worst = 5
PHY-1002 : len = 750704, over cnt = 24(0%), over = 26, worst = 3
PHY-1002 : len = 750936, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 750984, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 751096, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.752748s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (101.7%)

PHY-1001 : Congestion index: top1 = 48.36, top5 = 43.89, top10 = 41.48, top15 = 39.78.
PHY-3001 : End congestion estimation;  1.082808s wall, 1.093750s user + 0.000000s system = 1.093750s CPU (101.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19443 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.977473s wall, 0.968750s user + 0.015625s system = 0.984375s CPU (100.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00110318
PHY-3002 : Step(200): len = 609217, overlap = 2
PHY-3002 : Step(201): len = 609270, overlap = 1.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006829s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (228.8%)

PHY-3001 : Legalized: Len = 609383, Over = 0
PHY-3001 : End spreading;  0.076725s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (101.8%)

PHY-3001 : Final: Len = 609383, Over = 0
PHY-3001 : End incremental placement;  6.912855s wall, 6.921875s user + 0.156250s system = 7.078125s CPU (102.4%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.735598s wall, 11.781250s user + 0.156250s system = 11.937500s CPU (111.2%)

OPT-1001 : Current memory(MB): used = 719, reserve = 703, peak = 724.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17496/19445.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 750880, over cnt = 15(0%), over = 21, worst = 3
PHY-1002 : len = 750920, over cnt = 9(0%), over = 10, worst = 2
PHY-1002 : len = 750976, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 751056, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.572864s wall, 0.609375s user + 0.015625s system = 0.625000s CPU (109.1%)

PHY-1001 : Congestion index: top1 = 48.23, top5 = 43.75, top10 = 41.37, top15 = 39.70.
OPT-1001 : End congestion update;  0.898836s wall, 0.937500s user + 0.015625s system = 0.953125s CPU (106.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19443 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.823115s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (98.7%)

OPT-0007 : Start: WNS 4437 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.726846s wall, 1.765625s user + 0.015625s system = 1.781250s CPU (103.2%)

OPT-1001 : Current memory(MB): used = 719, reserve = 703, peak = 724.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19443 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.833250s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (99.4%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17512/19445.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 751056, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.125566s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (99.5%)

PHY-1001 : Congestion index: top1 = 48.23, top5 = 43.75, top10 = 41.37, top15 = 39.70.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19443 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.843354s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (100.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4437 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 47.758621
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4437ps with logic level 4 
OPT-1001 : End physical optimization;  16.992669s wall, 18.062500s user + 0.187500s system = 18.250000s CPU (107.4%)

RUN-1003 : finish command "place" in  72.493377s wall, 134.171875s user + 7.843750s system = 142.015625s CPU (195.9%)

RUN-1004 : used memory is 635 MB, reserved memory is 613 MB, peak memory is 724 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.885642s wall, 3.359375s user + 0.000000s system = 3.359375s CPU (178.2%)

RUN-1004 : used memory is 635 MB, reserved memory is 615 MB, peak memory is 724 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8181 instances
RUN-1001 : 4033 mslices, 4043 lslices, 60 pads, 40 brams, 0 dsps
RUN-1001 : There are total 19445 nets
RUN-1001 : 13609 nets have 2 pins
RUN-1001 : 4426 nets have [3 - 5] pins
RUN-1001 : 873 nets have [6 - 10] pins
RUN-1001 : 398 nets have [11 - 20] pins
RUN-1001 : 130 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67993, tnet num: 19443, tinst num: 8179, tnode num: 92259, tedge num: 112175.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.976463s wall, 1.968750s user + 0.015625s system = 1.984375s CPU (100.4%)

RUN-1004 : used memory is 642 MB, reserved memory is 639 MB, peak memory is 724 MB
PHY-1001 : 4033 mslices, 4043 lslices, 60 pads, 40 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19443 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 687728, over cnt = 2369(6%), over = 3959, worst = 7
PHY-1002 : len = 704544, over cnt = 1403(3%), over = 1991, worst = 6
PHY-1002 : len = 719808, over cnt = 644(1%), over = 883, worst = 6
PHY-1002 : len = 735424, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 735552, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.987863s wall, 3.328125s user + 0.031250s system = 3.359375s CPU (169.0%)

PHY-1001 : Congestion index: top1 = 48.21, top5 = 43.84, top10 = 41.13, top15 = 39.38.
PHY-1001 : End global routing;  2.370484s wall, 3.718750s user + 0.031250s system = 3.750000s CPU (158.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 706, reserve = 694, peak = 724.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 976, reserve = 961, peak = 976.
PHY-1001 : End build detailed router design. 5.124466s wall, 5.109375s user + 0.000000s system = 5.109375s CPU (99.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 184952, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.002704s wall, 0.984375s user + 0.015625s system = 1.000000s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 1010, reserve = 997, peak = 1010.
PHY-1001 : End phase 1; 1.011844s wall, 1.000000s user + 0.015625s system = 1.015625s CPU (100.4%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.75346e+06, over cnt = 1290(0%), over = 1294, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1024, reserve = 1010, peak = 1024.
PHY-1001 : End initial routed; 20.831496s wall, 49.906250s user + 0.500000s system = 50.406250s CPU (242.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18172(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.061   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.665655s wall, 3.656250s user + 0.015625s system = 3.671875s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 1038, reserve = 1024, peak = 1038.
PHY-1001 : End phase 2; 24.497318s wall, 53.562500s user + 0.515625s system = 54.078125s CPU (220.8%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.75346e+06, over cnt = 1290(0%), over = 1294, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.264071s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (94.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.74102e+06, over cnt = 462(0%), over = 463, worst = 2, crit = 0
PHY-1001 : End DR Iter 1; 1.111527s wall, 1.906250s user + 0.000000s system = 1.906250s CPU (171.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.74094e+06, over cnt = 56(0%), over = 56, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.443648s wall, 0.656250s user + 0.000000s system = 0.656250s CPU (147.9%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.7415e+06, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.226210s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (117.4%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.74162e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.182152s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (111.5%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.74171e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.179898s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (112.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18172(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.061   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.651611s wall, 3.656250s user + 0.000000s system = 3.656250s CPU (100.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 325 feed throughs used by 277 nets
PHY-1001 : End commit to database; 2.311218s wall, 2.265625s user + 0.031250s system = 2.296875s CPU (99.4%)

PHY-1001 : Current memory(MB): used = 1124, reserve = 1112, peak = 1124.
PHY-1001 : End phase 3; 8.918242s wall, 9.937500s user + 0.046875s system = 9.984375s CPU (112.0%)

PHY-1003 : Routed, final wirelength = 1.74171e+06
PHY-1001 : Current memory(MB): used = 1128, reserve = 1117, peak = 1128.
PHY-1001 : End export database. 0.064982s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (96.2%)

PHY-1001 : End detail routing;  40.067703s wall, 70.109375s user + 0.578125s system = 70.687500s CPU (176.4%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67993, tnet num: 19443, tinst num: 8179, tnode num: 92259, tedge num: 112175.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.807259s wall, 1.812500s user + 0.000000s system = 1.812500s CPU (100.3%)

RUN-1004 : used memory is 1060 MB, reserved memory is 1056 MB, peak memory is 1128 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  49.125588s wall, 80.484375s user + 0.625000s system = 81.109375s CPU (165.1%)

RUN-1004 : used memory is 1058 MB, reserved memory is 1055 MB, peak memory is 1128 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8859   out of  19600   45.20%
#reg                    12443   out of  19600   63.48%
#le                     15042
  #lut only              2599   out of  15042   17.28%
  #reg only              6183   out of  15042   41.10%
  #lut&reg               6260   out of  15042   41.62%
#dsp                        0   out of     29    0.00%
#bram                      40   out of     64   62.50%
  #bram9k                  38
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6853
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          163
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         M3        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         P6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15042  |7357    |1502    |12487   |40      |0       |
|  AnyFog_dataX                      |AnyFog          |220    |80      |22      |178     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |98     |65      |22      |56      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |207    |75      |22      |170     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |53      |22      |49      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |206    |89      |22      |172     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |84     |62      |22      |50      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2931   |686     |39      |2845    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |59     |41      |5       |49      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |217    |70      |5       |206     |0       |0       |
|    STADOP_com2                     |STADOP          |540    |102     |0       |536     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |44      |14      |40      |0       |0       |
|    head_com2                       |uniheading      |268    |96      |5       |252     |0       |0       |
|    rmc_com2                        |Gprmc           |33     |32      |0       |29      |0       |0       |
|    uart_com2                       |Agrica          |1420   |265     |10      |1404    |0       |0       |
|  COM3                              |COM3_Control    |278    |152     |19      |239     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |63     |37      |5       |54      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |62     |42      |14      |39      |0       |0       |
|    rmc_com3                        |Gprmc           |153    |73      |0       |146     |0       |0       |
|  DATA                              |Data_Processing |8833   |4519    |1122    |7050    |0       |0       |
|    DIV_Dtemp                       |Divider         |812    |322     |84      |689     |0       |0       |
|    DIV_Utemp                       |Divider         |577    |293     |84      |454     |0       |0       |
|    DIV_accX                        |Divider         |580    |283     |84      |456     |0       |0       |
|    DIV_accY                        |Divider         |674    |326     |102     |519     |0       |0       |
|    DIV_accZ                        |Divider         |715    |397     |132     |510     |0       |0       |
|    DIV_rateX                       |Divider         |639    |376     |132     |436     |0       |0       |
|    DIV_rateY                       |Divider         |614    |387     |132     |409     |0       |0       |
|    DIV_rateZ                       |Divider         |540    |356     |132     |333     |0       |0       |
|    genclk                          |genclk          |285    |178     |89      |111     |0       |0       |
|  FMC                               |FMC_Ctrl        |435    |384     |43      |339     |0       |0       |
|  IIC                               |I2C_master      |288    |228     |11      |259     |0       |0       |
|  IMU_CTRL                          |SCHA634         |926    |680     |61      |726     |0       |0       |
|    CtrlData                        |CtrlData        |481    |426     |47      |331     |0       |0       |
|      usms                          |Time_1ms        |29     |24      |5       |21      |0       |0       |
|    SPIM                            |SPI_SCHA634     |445    |254     |14      |395     |0       |0       |
|  POWER                             |POWER_EN        |97     |52      |38      |37      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |603    |410     |103     |410     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |603    |410     |103     |410     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |261    |189     |0       |244     |0       |0       |
|        reg_inst                    |register        |259    |187     |0       |242     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |342    |221     |103     |166     |0       |0       |
|        bus_inst                    |bus_top         |126    |80      |46      |46      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |26     |16      |10      |10      |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |50     |32      |18      |18      |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |48     |30      |18      |16      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |135    |102     |29      |86      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13548  
    #2          2       3429   
    #3          3        679   
    #4          4        318   
    #5        5-10       949   
    #6        11-50      443   
    #7       51-100       9    
    #8       101-500      3    
    #9        >500        2    
  Average     2.14             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.242138s wall, 3.828125s user + 0.031250s system = 3.859375s CPU (172.1%)

RUN-1004 : used memory is 1059 MB, reserved memory is 1056 MB, peak memory is 1128 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67993, tnet num: 19443, tinst num: 8179, tnode num: 92259, tedge num: 112175.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.885704s wall, 1.859375s user + 0.000000s system = 1.859375s CPU (98.6%)

RUN-1004 : used memory is 1062 MB, reserved memory is 1058 MB, peak memory is 1128 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19443 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.570533s wall, 1.578125s user + 0.000000s system = 1.578125s CPU (100.5%)

RUN-1004 : used memory is 1067 MB, reserved memory is 1062 MB, peak memory is 1128 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 19285304e968e394d67a8f065444a1f66c49f77adeb419c41e6b7764d8228547 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8179
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19445, pip num: 148087
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 325
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3243 valid insts, and 414476 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011111000000101011000101
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  13.249932s wall, 130.281250s user + 0.218750s system = 130.500000s CPU (984.9%)

RUN-1004 : used memory is 1192 MB, reserved memory is 1177 MB, peak memory is 1307 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250821_144220.log"

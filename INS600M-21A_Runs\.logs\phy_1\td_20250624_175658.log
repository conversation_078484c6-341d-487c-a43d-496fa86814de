============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Tue Jun 24 17:56:58 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(516)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
RUN-1001 : Project manager successfully analyzed 22 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.800199s wall, 1.796875s user + 4.015625s system = 5.812500s CPU (100.2%)

RUN-1004 : used memory is 78 MB, reserved memory is 41 MB, peak memory is 90 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.950985s wall, 1.875000s user + 0.062500s system = 1.937500s CPU (99.3%)

RUN-1004 : used memory is 300 MB, reserved memory is 269 MB, peak memory is 303 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 58 trigger nets, 58 data nets.
KIT-1004 : Chipwatcher code = 1110111010000001
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=158) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=158) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=158)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=158)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 23129/23 useful/useless nets, 19833/12 useful/useless insts
SYN-1016 : Merged 28 instances.
SYN-1032 : 22734/20 useful/useless nets, 20340/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 478 better
SYN-1014 : Optimize round 2
SYN-1032 : 22350/45 useful/useless nets, 19956/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.848690s wall, 2.812500s user + 0.031250s system = 2.843750s CPU (99.8%)

RUN-1004 : used memory is 330 MB, reserved memory is 296 MB, peak memory is 331 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22422/441 useful/useless nets, 20079/63 useful/useless insts
SYN-1016 : Merged 69 instances.
SYN-2571 : Optimize after map_dsp, round 1, 573 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 56 instances.
SYN-2501 : Optimize round 1, 114 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 22926/5 useful/useless nets, 20583/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 84624, tnet num: 22926, tinst num: 20582, tnode num: 118617, tedge num: 131862.
TMR-2508 : Levelizing timing graph completed, there are 85 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.284831s wall, 1.218750s user + 0.062500s system = 1.281250s CPU (99.7%)

RUN-1004 : used memory is 473 MB, reserved memory is 441 MB, peak memory is 473 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22926 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 282 (3.29), #lev = 7 (1.66)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 282 (3.29), #lev = 7 (1.66)
SYN-3001 : Logic optimization runtime opt =   0.04 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 614 instances into 282 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 487 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 160 adder to BLE ...
SYN-4008 : Packed 160 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  5.462141s wall, 5.281250s user + 0.156250s system = 5.437500s CPU (99.5%)

RUN-1004 : used memory is 354 MB, reserved memory is 321 MB, peak memory is 583 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  8.705005s wall, 8.453125s user + 0.218750s system = 8.671875s CPU (99.6%)

RUN-1004 : used memory is 355 MB, reserved memory is 322 MB, peak memory is 583 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[7] will be merged to another kept net COM3/rmc_com3/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[6] will be merged to another kept net COM3/rmc_com3/GPRMC_data[6]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[5] will be merged to another kept net COM3/rmc_com3/GPRMC_data[5]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[4] will be merged to another kept net COM3/rmc_com3/GPRMC_data[4]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[3] will be merged to another kept net COM3/rmc_com3/GPRMC_data[3]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[2] will be merged to another kept net COM3/rmc_com3/GPRMC_data[2]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[1] will be merged to another kept net COM3/rmc_com3/GPRMC_data[1]
SYN-5055 WARNING: The kept net FMC/Error_state[0] will be merged to another kept net COM3/rmc_com3/GPRMC_sub_state_cnt0[3]
SYN-5055 WARNING: The kept net COM3/rmc_com3/GPRMC_sub_state_cnt0[3] will be merged to another kept net COM3/rmc_com3/GPRMC_sub_state_cnt0[2]
SYN-5055 WARNING: The kept net COM3/rmc_com3/GPRMC_sub_state_cnt0[2] will be merged to another kept net COM3/rmc_com3/GPRMC_sub_state_cnt0[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (335 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19907 instances
RUN-0007 : 5772 luts, 12577 seqs, 951 mslices, 494 lslices, 56 pads, 52 brams, 0 dsps
RUN-1001 : There are total 22274 nets
RUN-1001 : 16647 nets have 2 pins
RUN-1001 : 4439 nets have [3 - 5] pins
RUN-1001 : 793 nets have [6 - 10] pins
RUN-1001 : 265 nets have [11 - 20] pins
RUN-1001 : 106 nets have [21 - 99] pins
RUN-1001 : 24 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4744     
RUN-1001 :   No   |  No   |  Yes  |     670     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    6556     
RUN-1001 :   Yes  |  No   |  Yes  |     507     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  119  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 127
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19905 instances, 5772 luts, 12577 seqs, 1445 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1131 pins
PHY-3001 : Huge net DATA/done_div with 1644 pins
PHY-0007 : Cell area utilization is 64%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83151, tnet num: 22272, tinst num: 19905, tnode num: 117279, tedge num: 130708.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.312200s wall, 1.281250s user + 0.031250s system = 1.312500s CPU (100.0%)

RUN-1004 : used memory is 534 MB, reserved memory is 506 MB, peak memory is 583 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22272 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.296613s wall, 2.203125s user + 0.093750s system = 2.296875s CPU (100.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.4978e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19905.
PHY-3001 : Level 1 #clusters 2110.
PHY-3001 : End clustering;  0.172702s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (135.7%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 64%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 886215, overlap = 705
PHY-3002 : Step(2): len = 821745, overlap = 755.688
PHY-3002 : Step(3): len = 539449, overlap = 921.031
PHY-3002 : Step(4): len = 481239, overlap = 938.688
PHY-3002 : Step(5): len = 387286, overlap = 1007.44
PHY-3002 : Step(6): len = 339359, overlap = 1075.94
PHY-3002 : Step(7): len = 287891, overlap = 1174.38
PHY-3002 : Step(8): len = 258311, overlap = 1234.88
PHY-3002 : Step(9): len = 226692, overlap = 1278.69
PHY-3002 : Step(10): len = 209479, overlap = 1319.28
PHY-3002 : Step(11): len = 187358, overlap = 1380.06
PHY-3002 : Step(12): len = 178436, overlap = 1424.22
PHY-3002 : Step(13): len = 161670, overlap = 1452.59
PHY-3002 : Step(14): len = 151986, overlap = 1482.75
PHY-3002 : Step(15): len = 138329, overlap = 1527.22
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.88734e-07
PHY-3002 : Step(16): len = 139092, overlap = 1504.84
PHY-3002 : Step(17): len = 179746, overlap = 1401.06
PHY-3002 : Step(18): len = 187711, overlap = 1327.69
PHY-3002 : Step(19): len = 189161, overlap = 1283.5
PHY-3002 : Step(20): len = 184253, overlap = 1253.5
PHY-3002 : Step(21): len = 179462, overlap = 1259.31
PHY-3002 : Step(22): len = 175669, overlap = 1230.56
PHY-3002 : Step(23): len = 172990, overlap = 1218.19
PHY-3002 : Step(24): len = 170370, overlap = 1215.97
PHY-3002 : Step(25): len = 168362, overlap = 1221
PHY-3002 : Step(26): len = 167904, overlap = 1215.59
PHY-3002 : Step(27): len = 167261, overlap = 1210.62
PHY-3002 : Step(28): len = 166249, overlap = 1200.81
PHY-3002 : Step(29): len = 165663, overlap = 1182.69
PHY-3002 : Step(30): len = 165587, overlap = 1177.75
PHY-3002 : Step(31): len = 163980, overlap = 1178.59
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 1.77747e-06
PHY-3002 : Step(32): len = 171539, overlap = 1149.06
PHY-3002 : Step(33): len = 186375, overlap = 1098.06
PHY-3002 : Step(34): len = 191661, overlap = 1050.06
PHY-3002 : Step(35): len = 193960, overlap = 1042.75
PHY-3002 : Step(36): len = 195861, overlap = 1037.5
PHY-3002 : Step(37): len = 195600, overlap = 1019.22
PHY-3002 : Step(38): len = 194633, overlap = 1023.47
PHY-3002 : Step(39): len = 193563, overlap = 1040.81
PHY-3002 : Step(40): len = 192285, overlap = 1050.62
PHY-3002 : Step(41): len = 190745, overlap = 1046.44
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 3.55494e-06
PHY-3002 : Step(42): len = 198976, overlap = 997.312
PHY-3002 : Step(43): len = 210905, overlap = 919.594
PHY-3002 : Step(44): len = 215326, overlap = 839.938
PHY-3002 : Step(45): len = 218168, overlap = 820.062
PHY-3002 : Step(46): len = 219623, overlap = 824.5
PHY-3002 : Step(47): len = 219580, overlap = 831.031
PHY-3002 : Step(48): len = 218637, overlap = 839.812
PHY-3002 : Step(49): len = 216849, overlap = 840.719
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 7.10987e-06
PHY-3002 : Step(50): len = 226908, overlap = 802.906
PHY-3002 : Step(51): len = 240776, overlap = 761
PHY-3002 : Step(52): len = 244742, overlap = 710.062
PHY-3002 : Step(53): len = 246687, overlap = 702.094
PHY-3002 : Step(54): len = 247461, overlap = 702.781
PHY-3002 : Step(55): len = 246572, overlap = 701.812
PHY-3002 : Step(56): len = 244469, overlap = 701.969
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.42197e-05
PHY-3002 : Step(57): len = 256855, overlap = 675.562
PHY-3002 : Step(58): len = 268818, overlap = 630.219
PHY-3002 : Step(59): len = 272980, overlap = 605.406
PHY-3002 : Step(60): len = 275713, overlap = 592.438
PHY-3002 : Step(61): len = 275121, overlap = 587.312
PHY-3002 : Step(62): len = 274867, overlap = 593.656
PHY-3002 : Step(63): len = 272741, overlap = 586.094
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 2.84395e-05
PHY-3002 : Step(64): len = 281814, overlap = 558.969
PHY-3002 : Step(65): len = 293164, overlap = 510.281
PHY-3002 : Step(66): len = 296607, overlap = 466.406
PHY-3002 : Step(67): len = 297656, overlap = 457.156
PHY-3002 : Step(68): len = 296281, overlap = 433.062
PHY-3002 : Step(69): len = 295174, overlap = 410.719
PHY-3002 : Step(70): len = 293046, overlap = 409.281
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 5.6879e-05
PHY-3002 : Step(71): len = 301394, overlap = 381.094
PHY-3002 : Step(72): len = 310110, overlap = 365.938
PHY-3002 : Step(73): len = 312883, overlap = 339.688
PHY-3002 : Step(74): len = 314107, overlap = 327.531
PHY-3002 : Step(75): len = 313638, overlap = 310.5
PHY-3002 : Step(76): len = 313066, overlap = 305.75
PHY-3002 : Step(77): len = 310846, overlap = 308.906
PHY-3002 : Step(78): len = 310175, overlap = 296.312
PHY-3002 : Step(79): len = 309128, overlap = 300.312
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000113758
PHY-3002 : Step(80): len = 313810, overlap = 290.938
PHY-3002 : Step(81): len = 319288, overlap = 280.375
PHY-3002 : Step(82): len = 322519, overlap = 278.188
PHY-3002 : Step(83): len = 323085, overlap = 280.344
PHY-3002 : Step(84): len = 321832, overlap = 284.688
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000220625
PHY-3002 : Step(85): len = 324102, overlap = 291.594
PHY-3002 : Step(86): len = 329641, overlap = 298.844
PHY-3002 : Step(87): len = 331170, overlap = 295.875
PHY-3002 : Step(88): len = 331418, overlap = 284.406
PHY-3002 : Step(89): len = 331175, overlap = 294.406
PHY-3002 : Step(90): len = 331067, overlap = 307.031
PHY-3002 : Step(91): len = 330391, overlap = 314.781
PHY-3002 : Step(92): len = 330294, overlap = 311.094
PHY-3002 : Step(93): len = 330370, overlap = 312.312
PHY-3002 : Step(94): len = 330110, overlap = 306.312
PHY-3002 : Step(95): len = 329977, overlap = 299.844
PHY-3002 : Step(96): len = 330489, overlap = 302.969
PHY-3002 : Step(97): len = 330590, overlap = 303.969
PHY-3002 : Step(98): len = 330379, overlap = 302.188
PHY-3002 : Step(99): len = 330094, overlap = 299.062
PHY-3002 : Step(100): len = 330326, overlap = 295.938
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(101): len = 331668, overlap = 294.25
PHY-3002 : Step(102): len = 333161, overlap = 289.562
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.012406s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/22274.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 436104, over cnt = 1272(3%), over = 6076, worst = 47
PHY-1001 : End global iterations;  0.858105s wall, 1.093750s user + 0.078125s system = 1.171875s CPU (136.6%)

PHY-1001 : Congestion index: top1 = 80.30, top5 = 57.18, top10 = 46.07, top15 = 39.77.
PHY-3001 : End congestion estimation;  1.112656s wall, 1.343750s user + 0.093750s system = 1.437500s CPU (129.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22272 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.028312s wall, 1.000000s user + 0.015625s system = 1.015625s CPU (98.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.92368e-05
PHY-3002 : Step(103): len = 374079, overlap = 191.438
PHY-3002 : Step(104): len = 386192, overlap = 174.281
PHY-3002 : Step(105): len = 386567, overlap = 171.031
PHY-3002 : Step(106): len = 388553, overlap = 156.094
PHY-3002 : Step(107): len = 394807, overlap = 134.938
PHY-3002 : Step(108): len = 397144, overlap = 139.812
PHY-3002 : Step(109): len = 397544, overlap = 134.406
PHY-3002 : Step(110): len = 404135, overlap = 137.125
PHY-3002 : Step(111): len = 404949, overlap = 132.875
PHY-3002 : Step(112): len = 407322, overlap = 134.562
PHY-3002 : Step(113): len = 408507, overlap = 132.562
PHY-3002 : Step(114): len = 409464, overlap = 132.188
PHY-3002 : Step(115): len = 411150, overlap = 136.812
PHY-3002 : Step(116): len = 414188, overlap = 136.562
PHY-3002 : Step(117): len = 414636, overlap = 137.25
PHY-3002 : Step(118): len = 416497, overlap = 137.781
PHY-3002 : Step(119): len = 415091, overlap = 138.25
PHY-3002 : Step(120): len = 414936, overlap = 136.469
PHY-3002 : Step(121): len = 416053, overlap = 140.344
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000158474
PHY-3002 : Step(122): len = 415134, overlap = 139.062
PHY-3002 : Step(123): len = 416700, overlap = 136.312
PHY-3002 : Step(124): len = 419323, overlap = 132.812
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000312662
PHY-3002 : Step(125): len = 424896, overlap = 134.781
PHY-3002 : Step(126): len = 431035, overlap = 137.781
PHY-3002 : Step(127): len = 436088, overlap = 130.156
PHY-3002 : Step(128): len = 440459, overlap = 135.219
PHY-3002 : Step(129): len = 443144, overlap = 134.062
PHY-3002 : Step(130): len = 443561, overlap = 136.406
PHY-3002 : Step(131): len = 444259, overlap = 135.844
PHY-3002 : Step(132): len = 443701, overlap = 139.219
PHY-3002 : Step(133): len = 443852, overlap = 138.531
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000625325
PHY-3002 : Step(134): len = 443350, overlap = 141.938
PHY-3002 : Step(135): len = 447763, overlap = 143.906
PHY-3002 : Step(136): len = 452803, overlap = 144.438
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00104995
PHY-3002 : Step(137): len = 453022, overlap = 140.406
PHY-3002 : Step(138): len = 458703, overlap = 143.219
PHY-3002 : Step(139): len = 476844, overlap = 144.719
PHY-3002 : Step(140): len = 480707, overlap = 144
PHY-3002 : Step(141): len = 480187, overlap = 145.531
PHY-3002 : Step(142): len = 477513, overlap = 138.625
PHY-3002 : Step(143): len = 477107, overlap = 135.625
PHY-3002 : Step(144): len = 476836, overlap = 134.375
PHY-3002 : Step(145): len = 475130, overlap = 135.25
PHY-3002 : Step(146): len = 475223, overlap = 136.219
PHY-3002 : Step(147): len = 476006, overlap = 134.375
PHY-3002 : Step(148): len = 474942, overlap = 133.594
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 33/22274.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 540000, over cnt = 2314(6%), over = 10786, worst = 42
PHY-1001 : End global iterations;  1.210361s wall, 2.062500s user + 0.031250s system = 2.093750s CPU (173.0%)

PHY-1001 : Congestion index: top1 = 82.31, top5 = 62.27, top10 = 52.97, top15 = 47.37.
PHY-3001 : End congestion estimation;  1.596642s wall, 2.421875s user + 0.046875s system = 2.468750s CPU (154.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22272 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.073811s wall, 1.062500s user + 0.000000s system = 1.062500s CPU (98.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.87094e-05
PHY-3002 : Step(149): len = 480073, overlap = 389
PHY-3002 : Step(150): len = 479756, overlap = 335.781
PHY-3002 : Step(151): len = 477135, overlap = 296.375
PHY-3002 : Step(152): len = 468848, overlap = 278.875
PHY-3002 : Step(153): len = 461943, overlap = 276.594
PHY-3002 : Step(154): len = 458827, overlap = 273.125
PHY-3002 : Step(155): len = 456567, overlap = 273.562
PHY-3002 : Step(156): len = 453247, overlap = 263.344
PHY-3002 : Step(157): len = 451689, overlap = 267.469
PHY-3002 : Step(158): len = 449361, overlap = 266.125
PHY-3002 : Step(159): len = 447796, overlap = 258.969
PHY-3002 : Step(160): len = 447122, overlap = 233.062
PHY-3002 : Step(161): len = 446574, overlap = 232.094
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000177419
PHY-3002 : Step(162): len = 445260, overlap = 228.156
PHY-3002 : Step(163): len = 446091, overlap = 217.469
PHY-3002 : Step(164): len = 447247, overlap = 209.062
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000354838
PHY-3002 : Step(165): len = 449075, overlap = 203.969
PHY-3002 : Step(166): len = 454948, overlap = 200.438
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000695399
PHY-3002 : Step(167): len = 457168, overlap = 193.031
PHY-3002 : Step(168): len = 464175, overlap = 179.125
PHY-3002 : Step(169): len = 470889, overlap = 160.812
PHY-3002 : Step(170): len = 472057, overlap = 154.125
PHY-3002 : Step(171): len = 472117, overlap = 153.469
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00137347
PHY-3002 : Step(172): len = 472725, overlap = 153.188
PHY-3002 : Step(173): len = 474690, overlap = 140.906
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83151, tnet num: 22272, tinst num: 19905, tnode num: 117279, tedge num: 130708.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.783895s wall, 1.718750s user + 0.062500s system = 1.781250s CPU (99.9%)

RUN-1004 : used memory is 574 MB, reserved memory is 549 MB, peak memory is 712 MB
OPT-1001 : Total overflow 498.16 peak overflow 4.06
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 371/22274.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 560920, over cnt = 2650(7%), over = 9499, worst = 27
PHY-1001 : End global iterations;  1.394515s wall, 2.140625s user + 0.031250s system = 2.171875s CPU (155.7%)

PHY-1001 : Congestion index: top1 = 60.50, top5 = 48.15, top10 = 43.10, top15 = 40.05.
PHY-1001 : End incremental global routing;  1.653936s wall, 2.406250s user + 0.031250s system = 2.437500s CPU (147.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22272 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.147693s wall, 1.125000s user + 0.015625s system = 1.140625s CPU (99.4%)

OPT-1001 : 18 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 56 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19828 has valid locations, 266 needs to be replaced
PHY-3001 : design contains 20153 instances, 5880 luts, 12717 seqs, 1445 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 492551
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17641/22522.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 578080, over cnt = 2669(7%), over = 9550, worst = 27
PHY-1001 : End global iterations;  0.206420s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (113.5%)

PHY-1001 : Congestion index: top1 = 60.26, top5 = 48.48, top10 = 43.43, top15 = 40.36.
PHY-3001 : End congestion estimation;  0.500412s wall, 0.515625s user + 0.015625s system = 0.531250s CPU (106.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 84007, tnet num: 22520, tinst num: 20153, tnode num: 118477, tedge num: 131924.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.807047s wall, 1.796875s user + 0.000000s system = 1.796875s CPU (99.4%)

RUN-1004 : used memory is 621 MB, reserved memory is 616 MB, peak memory is 713 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22520 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.151025s wall, 3.156250s user + 0.000000s system = 3.156250s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(174): len = 492164, overlap = 2.5
PHY-3002 : Step(175): len = 492413, overlap = 2.4375
PHY-3002 : Step(176): len = 492716, overlap = 2.375
PHY-3002 : Step(177): len = 493187, overlap = 2.375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17698/22522.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 575208, over cnt = 2689(7%), over = 9634, worst = 27
PHY-1001 : End global iterations;  0.221091s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (113.1%)

PHY-1001 : Congestion index: top1 = 60.50, top5 = 48.47, top10 = 43.51, top15 = 40.50.
PHY-3001 : End congestion estimation;  0.505409s wall, 0.531250s user + 0.000000s system = 0.531250s CPU (105.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22520 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.179865s wall, 1.171875s user + 0.015625s system = 1.187500s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000966538
PHY-3002 : Step(178): len = 493258, overlap = 142.125
PHY-3002 : Step(179): len = 493641, overlap = 142.719
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00193308
PHY-3002 : Step(180): len = 493780, overlap = 143.344
PHY-3002 : Step(181): len = 494190, overlap = 142.875
PHY-3001 : Final: Len = 494190, Over = 142.875
PHY-3001 : End incremental placement;  6.384181s wall, 6.906250s user + 0.187500s system = 7.093750s CPU (111.1%)

OPT-1001 : Total overflow 503.50 peak overflow 4.06
OPT-1001 : End high-fanout net optimization;  9.842579s wall, 11.265625s user + 0.234375s system = 11.500000s CPU (116.8%)

OPT-1001 : Current memory(MB): used = 717, reserve = 698, peak = 735.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17685/22522.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 577216, over cnt = 2652(7%), over = 9147, worst = 27
PHY-1002 : len = 627856, over cnt = 1795(5%), over = 4395, worst = 26
PHY-1002 : len = 677224, over cnt = 453(1%), over = 850, worst = 26
PHY-1002 : len = 688480, over cnt = 129(0%), over = 209, worst = 8
PHY-1002 : len = 692312, over cnt = 1(0%), over = 1, worst = 1
PHY-1001 : End global iterations;  1.773650s wall, 2.421875s user + 0.015625s system = 2.437500s CPU (137.4%)

PHY-1001 : Congestion index: top1 = 49.42, top5 = 43.66, top10 = 40.83, top15 = 38.97.
OPT-1001 : End congestion update;  2.083831s wall, 2.734375s user + 0.015625s system = 2.750000s CPU (132.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22520 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.943055s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (101.1%)

OPT-0007 : Start: WNS 4303 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  3.033132s wall, 3.687500s user + 0.015625s system = 3.703125s CPU (122.1%)

OPT-1001 : Current memory(MB): used = 717, reserve = 698, peak = 735.
OPT-1001 : End physical optimization;  15.014941s wall, 17.187500s user + 0.312500s system = 17.500000s CPU (116.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5880 LUT to BLE ...
SYN-4008 : Packed 5880 LUT and 2907 SEQ to BLE.
SYN-4003 : Packing 9810 remaining SEQ's ...
SYN-4005 : Packed 3367 SEQ with LUT/SLICE
SYN-4006 : 120 single LUT's are left
SYN-4006 : 6443 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 12323/14001 primitive instances ...
PHY-3001 : End packing;  3.013991s wall, 3.015625s user + 0.000000s system = 3.015625s CPU (100.1%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8327 instances
RUN-1001 : 4107 mslices, 4107 lslices, 56 pads, 52 brams, 0 dsps
RUN-1001 : There are total 19664 nets
RUN-1001 : 13709 nets have 2 pins
RUN-1001 : 4540 nets have [3 - 5] pins
RUN-1001 : 862 nets have [6 - 10] pins
RUN-1001 : 393 nets have [11 - 20] pins
RUN-1001 : 150 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
PHY-3001 : design contains 8325 instances, 8214 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Cell area utilization is 87%
PHY-3001 : After packing: Len = 513138, Over = 377
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 8105/19664.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 657272, over cnt = 1683(4%), over = 2587, worst = 7
PHY-1002 : len = 663808, over cnt = 1070(3%), over = 1424, worst = 6
PHY-1002 : len = 675040, over cnt = 423(1%), over = 548, worst = 5
PHY-1002 : len = 682200, over cnt = 121(0%), over = 136, worst = 4
PHY-1002 : len = 684984, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.358258s wall, 2.171875s user + 0.015625s system = 2.187500s CPU (161.1%)

PHY-1001 : Congestion index: top1 = 51.27, top5 = 44.19, top10 = 40.96, top15 = 38.81.
PHY-3001 : End congestion estimation;  1.711123s wall, 2.515625s user + 0.015625s system = 2.531250s CPU (147.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69379, tnet num: 19662, tinst num: 8325, tnode num: 94510, tedge num: 114256.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.783875s wall, 1.750000s user + 0.031250s system = 1.781250s CPU (99.9%)

RUN-1004 : used memory is 617 MB, reserved memory is 613 MB, peak memory is 735 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19662 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.800754s wall, 2.765625s user + 0.046875s system = 2.812500s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.21812e-05
PHY-3002 : Step(182): len = 514969, overlap = 361
PHY-3002 : Step(183): len = 513456, overlap = 373.75
PHY-3002 : Step(184): len = 513467, overlap = 384.75
PHY-3002 : Step(185): len = 514668, overlap = 403.25
PHY-3002 : Step(186): len = 513048, overlap = 405
PHY-3002 : Step(187): len = 511405, overlap = 401.25
PHY-3002 : Step(188): len = 509814, overlap = 398.25
PHY-3002 : Step(189): len = 507350, overlap = 397
PHY-3002 : Step(190): len = 506196, overlap = 390
PHY-3002 : Step(191): len = 504388, overlap = 389
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000104362
PHY-3002 : Step(192): len = 508674, overlap = 379
PHY-3002 : Step(193): len = 511408, overlap = 373.75
PHY-3002 : Step(194): len = 511502, overlap = 375.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(195): len = 520297, overlap = 360
PHY-3002 : Step(196): len = 527908, overlap = 342.5
PHY-3002 : Step(197): len = 526019, overlap = 340.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.861105s wall, 1.031250s user + 0.812500s system = 1.843750s CPU (214.1%)

PHY-3001 : Trial Legalized: Len = 635590
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 628/19664.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 731336, over cnt = 2494(7%), over = 4054, worst = 8
PHY-1002 : len = 749840, over cnt = 1304(3%), over = 1702, worst = 6
PHY-1002 : len = 764216, over cnt = 497(1%), over = 625, worst = 6
PHY-1002 : len = 771600, over cnt = 147(0%), over = 183, worst = 4
PHY-1002 : len = 775320, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.107953s wall, 3.593750s user + 0.046875s system = 3.640625s CPU (172.7%)

PHY-1001 : Congestion index: top1 = 50.11, top5 = 44.62, top10 = 42.10, top15 = 40.51.
PHY-3001 : End congestion estimation;  2.511223s wall, 3.984375s user + 0.046875s system = 4.031250s CPU (160.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19662 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.992022s wall, 0.968750s user + 0.031250s system = 1.000000s CPU (100.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000209065
PHY-3002 : Step(198): len = 594360, overlap = 81.25
PHY-3002 : Step(199): len = 577176, overlap = 126
PHY-3002 : Step(200): len = 564883, overlap = 182.25
PHY-3002 : Step(201): len = 556782, overlap = 216.75
PHY-3002 : Step(202): len = 552142, overlap = 250
PHY-3002 : Step(203): len = 550196, overlap = 272.5
PHY-3002 : Step(204): len = 548344, overlap = 282.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000418131
PHY-3002 : Step(205): len = 551998, overlap = 275
PHY-3002 : Step(206): len = 556298, overlap = 263
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000836261
PHY-3002 : Step(207): len = 559789, overlap = 261.75
PHY-3002 : Step(208): len = 567720, overlap = 255.5
PHY-3002 : Step(209): len = 569465, overlap = 254
PHY-3002 : Step(210): len = 570090, overlap = 255.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.039401s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (79.3%)

PHY-3001 : Legalized: Len = 610191, Over = 0
PHY-3001 : Spreading special nets. 61 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.093879s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (99.9%)

PHY-3001 : 88 instances has been re-located, deltaX = 27, deltaY = 61, maxDist = 3.
PHY-3001 : Final: Len = 611869, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69379, tnet num: 19662, tinst num: 8325, tnode num: 94510, tedge num: 114256.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.036115s wall, 1.984375s user + 0.046875s system = 2.031250s CPU (99.8%)

RUN-1004 : used memory is 614 MB, reserved memory is 610 MB, peak memory is 735 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3606/19664.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 715512, over cnt = 2365(6%), over = 3727, worst = 7
PHY-1002 : len = 727504, over cnt = 1513(4%), over = 2095, worst = 7
PHY-1002 : len = 746032, over cnt = 553(1%), over = 706, worst = 4
PHY-1002 : len = 750712, over cnt = 314(0%), over = 393, worst = 4
PHY-1002 : len = 758136, over cnt = 8(0%), over = 8, worst = 1
PHY-1001 : End global iterations;  1.811366s wall, 3.031250s user + 0.031250s system = 3.062500s CPU (169.1%)

PHY-1001 : Congestion index: top1 = 47.41, top5 = 43.40, top10 = 41.08, top15 = 39.42.
PHY-1001 : End incremental global routing;  2.150329s wall, 3.359375s user + 0.031250s system = 3.390625s CPU (157.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19662 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.040043s wall, 1.031250s user + 0.015625s system = 1.046875s CPU (100.7%)

OPT-1001 : 3 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 56 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8263 has valid locations, 26 needs to be replaced
PHY-3001 : design contains 8348 instances, 8237 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 615348
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17791/19689.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 761592, over cnt = 68(0%), over = 83, worst = 4
PHY-1002 : len = 761632, over cnt = 42(0%), over = 46, worst = 4
PHY-1002 : len = 762016, over cnt = 21(0%), over = 21, worst = 1
PHY-1002 : len = 762240, over cnt = 12(0%), over = 12, worst = 1
PHY-1002 : len = 762432, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.732397s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (104.5%)

PHY-1001 : Congestion index: top1 = 47.41, top5 = 43.65, top10 = 41.28, top15 = 39.59.
PHY-3001 : End congestion estimation;  1.063778s wall, 1.093750s user + 0.000000s system = 1.093750s CPU (102.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69532, tnet num: 19687, tinst num: 8348, tnode num: 94702, tedge num: 114468.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.061859s wall, 2.062500s user + 0.000000s system = 2.062500s CPU (100.0%)

RUN-1004 : used memory is 649 MB, reserved memory is 637 MB, peak memory is 735 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19687 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.094706s wall, 3.062500s user + 0.031250s system = 3.093750s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(211): len = 614930, overlap = 0.75
PHY-3002 : Step(212): len = 614772, overlap = 1
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17789/19689.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 761264, over cnt = 55(0%), over = 69, worst = 6
PHY-1002 : len = 761256, over cnt = 36(0%), over = 40, worst = 4
PHY-1002 : len = 761400, over cnt = 21(0%), over = 22, worst = 2
PHY-1002 : len = 761592, over cnt = 12(0%), over = 12, worst = 1
PHY-1002 : len = 761864, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.747703s wall, 0.750000s user + 0.015625s system = 0.765625s CPU (102.4%)

PHY-1001 : Congestion index: top1 = 47.65, top5 = 43.60, top10 = 41.24, top15 = 39.56.
PHY-3001 : End congestion estimation;  1.073599s wall, 1.062500s user + 0.015625s system = 1.078125s CPU (100.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19687 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.984137s wall, 0.968750s user + 0.015625s system = 0.984375s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(213): len = 614698, overlap = 3.25
PHY-3002 : Step(214): len = 614741, overlap = 1.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006479s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 614814, Over = 0
PHY-3001 : Spreading special nets. 1 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.075340s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (103.7%)

PHY-3001 : 2 instances has been re-located, deltaX = 2, deltaY = 0, maxDist = 1.
PHY-3001 : Final: Len = 614886, Over = 0
PHY-3001 : End incremental placement;  6.880428s wall, 6.859375s user + 0.093750s system = 6.953125s CPU (101.1%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.598569s wall, 11.765625s user + 0.140625s system = 11.906250s CPU (112.3%)

OPT-1001 : Current memory(MB): used = 725, reserve = 710, peak = 735.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17772/19689.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 761808, over cnt = 37(0%), over = 49, worst = 5
PHY-1002 : len = 761864, over cnt = 22(0%), over = 24, worst = 2
PHY-1002 : len = 761992, over cnt = 14(0%), over = 15, worst = 2
PHY-1002 : len = 762072, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 762216, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.757495s wall, 0.812500s user + 0.015625s system = 0.828125s CPU (109.3%)

PHY-1001 : Congestion index: top1 = 47.61, top5 = 43.49, top10 = 41.19, top15 = 39.57.
OPT-1001 : End congestion update;  1.081382s wall, 1.125000s user + 0.015625s system = 1.140625s CPU (105.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19687 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.829720s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (99.8%)

OPT-0007 : Start: WNS 4656 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.916496s wall, 1.968750s user + 0.015625s system = 1.984375s CPU (103.5%)

OPT-1001 : Current memory(MB): used = 725, reserve = 710, peak = 735.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19687 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.833668s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (99.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17815/19689.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 762216, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.127968s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (97.7%)

PHY-1001 : Congestion index: top1 = 47.61, top5 = 43.49, top10 = 41.19, top15 = 39.57.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19687 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.828144s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (100.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4656 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 47.206897
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4656ps with logic level 4 
RUN-1001 :       #2 path slack 4700ps with logic level 8 
RUN-1001 :       #3 path slack 4700ps with logic level 8 
RUN-1001 :       #4 path slack 4700ps with logic level 8 
RUN-1001 :       #5 path slack 4726ps with logic level 4 
OPT-1001 : End physical optimization;  16.943263s wall, 18.250000s user + 0.203125s system = 18.453125s CPU (108.9%)

RUN-1003 : finish command "place" in  76.081172s wall, 138.812500s user + 7.671875s system = 146.484375s CPU (192.5%)

RUN-1004 : used memory is 639 MB, reserved memory is 630 MB, peak memory is 735 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.719552s wall, 2.953125s user + 0.015625s system = 2.968750s CPU (172.6%)

RUN-1004 : used memory is 639 MB, reserved memory is 630 MB, peak memory is 735 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8350 instances
RUN-1001 : 4123 mslices, 4114 lslices, 56 pads, 52 brams, 0 dsps
RUN-1001 : There are total 19689 nets
RUN-1001 : 13712 nets have 2 pins
RUN-1001 : 4543 nets have [3 - 5] pins
RUN-1001 : 872 nets have [6 - 10] pins
RUN-1001 : 400 nets have [11 - 20] pins
RUN-1001 : 152 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69532, tnet num: 19687, tinst num: 8348, tnode num: 94702, tedge num: 114468.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.758715s wall, 1.750000s user + 0.000000s system = 1.750000s CPU (99.5%)

RUN-1004 : used memory is 622 MB, reserved memory is 605 MB, peak memory is 735 MB
PHY-1001 : 4123 mslices, 4114 lslices, 56 pads, 52 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19687 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 697736, over cnt = 2463(6%), over = 4098, worst = 7
PHY-1002 : len = 713912, over cnt = 1535(4%), over = 2197, worst = 7
PHY-1002 : len = 731944, over cnt = 678(1%), over = 901, worst = 7
PHY-1002 : len = 746936, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 747160, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.901565s wall, 3.265625s user + 0.031250s system = 3.296875s CPU (173.4%)

PHY-1001 : Congestion index: top1 = 47.20, top5 = 42.92, top10 = 40.55, top15 = 38.95.
PHY-1001 : End global routing;  2.285470s wall, 3.625000s user + 0.046875s system = 3.671875s CPU (160.7%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 708, reserve = 697, peak = 735.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 980, reserve = 968, peak = 980.
PHY-1001 : End build detailed router design. 4.792414s wall, 4.765625s user + 0.031250s system = 4.796875s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 198312, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.950233s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (98.7%)

PHY-1001 : Current memory(MB): used = 1017, reserve = 1006, peak = 1017.
PHY-1001 : End phase 1; 0.957234s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (99.6%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 55% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.81228e+06, over cnt = 1429(0%), over = 1430, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1034, reserve = 1024, peak = 1034.
PHY-1001 : End initial routed; 25.778520s wall, 52.281250s user + 0.437500s system = 52.718750s CPU (204.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18469(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.334   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 4.166439s wall, 4.156250s user + 0.000000s system = 4.156250s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 1045, reserve = 1034, peak = 1045.
PHY-1001 : End phase 2; 29.945135s wall, 56.437500s user + 0.437500s system = 56.875000s CPU (189.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.81228e+06, over cnt = 1429(0%), over = 1430, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.308926s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (101.2%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.8003e+06, over cnt = 511(0%), over = 512, worst = 2, crit = 0
PHY-1001 : End DR Iter 1; 1.115861s wall, 2.015625s user + 0.000000s system = 2.015625s CPU (180.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.80174e+06, over cnt = 103(0%), over = 103, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.505069s wall, 0.781250s user + 0.015625s system = 0.796875s CPU (157.8%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.80268e+06, over cnt = 8(0%), over = 8, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.396654s wall, 0.500000s user + 0.000000s system = 0.500000s CPU (126.1%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.80282e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 4; 0.244968s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (108.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18469(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.334   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 4.449876s wall, 4.437500s user + 0.015625s system = 4.453125s CPU (100.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 348 feed throughs used by 296 nets
PHY-1001 : End commit to database; 2.968599s wall, 2.953125s user + 0.015625s system = 2.968750s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1138, reserve = 1131, peak = 1138.
PHY-1001 : End phase 3; 10.604192s wall, 11.875000s user + 0.078125s system = 11.953125s CPU (112.7%)

PHY-1003 : Routed, final wirelength = 1.80282e+06
PHY-1001 : Current memory(MB): used = 1142, reserve = 1135, peak = 1142.
PHY-1001 : End export database. 0.189805s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (98.8%)

PHY-1001 : End detail routing;  46.958999s wall, 74.703125s user + 0.546875s system = 75.250000s CPU (160.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69532, tnet num: 19687, tinst num: 8348, tnode num: 94702, tedge num: 114468.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.838754s wall, 1.828125s user + 0.000000s system = 1.828125s CPU (99.4%)

RUN-1004 : used memory is 1074 MB, reserved memory is 1077 MB, peak memory is 1142 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  55.917779s wall, 84.984375s user + 0.609375s system = 85.593750s CPU (153.1%)

RUN-1004 : used memory is 1072 MB, reserved memory is 1076 MB, peak memory is 1142 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8928   out of  19600   45.55%
#reg                    12822   out of  19600   65.42%
#le                     15331
  #lut only              2509   out of  15331   16.37%
  #reg only              6403   out of  15331   41.77%
  #lut&reg               6419   out of  15331   41.87%
#dsp                        0   out of     29    0.00%
#bram                      52   out of     64   81.25%
  #bram9k                  52
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       56   out of    188   29.79%
  #ireg                    10
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    7047
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          191
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT        E11        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT        A13        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT        F10        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         E8        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         E7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT        A10        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C6        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         D6        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         C7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT        E12        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         F1        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         A7        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H13        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J11        LVCMOS33          N/A          PULLUP          IREG       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT        E13        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         R7        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         M7        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         P7        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         T8        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         P8        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         N8        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M9        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         T9        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         R9        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         T6        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         P9        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         N9        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT        P11        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         T5        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         B2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        L10        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        E15        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        R14        LVCMOS33           8            N/A            OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT         R2        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT        N12        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        B10        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         J6        LVCMOS33           8            N/A            NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        R15        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E6        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15331  |7483    |1445    |12865   |52      |0       |
|  AnyFog_dataX                      |AnyFog          |226    |94      |22      |186     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |88     |59      |22      |52      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |225    |93      |22      |187     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |88     |65      |22      |54      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |220    |113     |22      |182     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |54      |22      |52      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |3314   |829     |34      |3250    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |733    |82      |5       |721     |0       |0       |
|    STADOP_com2                     |STADOP          |542    |57      |0       |541     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |43      |14      |42      |0       |0       |
|    head_com2                       |uniheading      |263    |72      |5       |254     |0       |0       |
|    uart_com2                       |Agrica          |1421   |285     |10      |1402    |0       |0       |
|  COM3                              |COM3_Control    |227    |123     |14      |188     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |60     |37      |14      |36      |0       |0       |
|    rmc_com3                        |Gprmc           |167    |86      |0       |152     |0       |0       |
|  DATA                              |Data_Processing |8603   |4381    |1059    |6914    |0       |0       |
|    DIV_Dtemp                       |Divider         |809    |304     |84      |684     |0       |0       |
|    DIV_Utemp                       |Divider         |607    |293     |84      |483     |0       |0       |
|    DIV_accX                        |Divider         |629    |362     |84      |503     |0       |0       |
|    DIV_accY                        |Divider         |603    |339     |108     |442     |0       |0       |
|    DIV_accZ                        |Divider         |707    |378     |132     |495     |0       |0       |
|    DIV_rateX                       |Divider         |667    |400     |132     |462     |0       |0       |
|    DIV_rateY                       |Divider         |594    |375     |132     |387     |0       |0       |
|    DIV_rateZ                       |Divider         |547    |356     |132     |342     |0       |0       |
|    genclk                          |genclk          |85     |59      |20      |51      |0       |0       |
|  FMC                               |FMC_Ctrl        |497    |431     |43      |370     |0       |0       |
|  IIC                               |I2C_master      |269    |212     |11      |243     |0       |0       |
|  IMU_CTRL                          |SCHA634         |911    |644     |61      |760     |0       |0       |
|    CtrlData                        |CtrlData        |451    |392     |47      |336     |0       |0       |
|      usms                          |Time_1ms        |28     |22      |5       |20      |0       |0       |
|    SPIM                            |SPI_SCHA634     |460    |252     |14      |424     |0       |0       |
|  POWER                             |POWER_EN        |98     |47      |38      |37      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |735    |514     |119     |499     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |735    |514     |119     |499     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |338    |249     |0       |321     |0       |0       |
|        reg_inst                    |register        |336    |247     |0       |319     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |397    |265     |119     |178     |0       |0       |
|        bus_inst                    |bus_top         |175    |113     |62      |61      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |27     |17      |10      |10      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |50     |32      |18      |18      |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |96     |62      |34      |31      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |141    |108     |29      |86      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13655  
    #2          2       3566   
    #3          3        672   
    #4          4        305   
    #5        5-10       958   
    #6        11-50      446   
    #7       51-100      20    
    #8       101-500      4    
    #9        >500        2    
  Average     2.17             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.280264s wall, 3.890625s user + 0.015625s system = 3.906250s CPU (171.3%)

RUN-1004 : used memory is 1073 MB, reserved memory is 1077 MB, peak memory is 1142 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69532, tnet num: 19687, tinst num: 8348, tnode num: 94702, tedge num: 114468.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  2.078976s wall, 2.078125s user + 0.000000s system = 2.078125s CPU (100.0%)

RUN-1004 : used memory is 1075 MB, reserved memory is 1080 MB, peak memory is 1142 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19687 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.643684s wall, 1.640625s user + 0.000000s system = 1.640625s CPU (99.8%)

RUN-1004 : used memory is 1080 MB, reserved memory is 1083 MB, peak memory is 1142 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 28b752c1c0e7d7ba92ac5beed3c46754b7b56ac64969e4b0149cfdbc1d295011 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8348
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19689, pip num: 151954
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 348
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3238 valid insts, and 423382 bits set as '1'.
BIT-1004 : the usercode register value: 00000000000000001110111010000001
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.288645s wall, 120.890625s user + 0.156250s system = 121.046875s CPU (985.0%)

RUN-1004 : used memory is 1210 MB, reserved memory is 1197 MB, peak memory is 1325 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250624_175658.log"

============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Jul 10 15:58:31 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(520)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
RUN-1001 : Project manager successfully analyzed 22 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.602656s wall, 1.546875s user + 4.062500s system = 5.609375s CPU (100.1%)

RUN-1004 : used memory is 78 MB, reserved memory is 40 MB, peak memory is 89 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.903405s wall, 1.750000s user + 0.125000s system = 1.875000s CPU (98.5%)

RUN-1004 : used memory is 295 MB, reserved memory is 264 MB, peak memory is 298 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 4 view nodes, 26 trigger nets, 26 data nets.
KIT-1004 : Chipwatcher code = 0110100010110101
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=4,BUS_DIN_NUM=26,BUS_CTRL_NUM=68,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=90) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=90) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=4,BUS_DIN_NUM=26,BUS_CTRL_NUM=68,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=4,BUS_DIN_NUM=26,BUS_CTRL_NUM=68,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=4,BUS_DIN_NUM=26,BUS_CTRL_NUM=68,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=90)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=90)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=4,BUS_DIN_NUM=26,BUS_CTRL_NUM=68,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=4,BUS_DIN_NUM=26,BUS_CTRL_NUM=68,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 21781/18 useful/useless nets, 18790/10 useful/useless insts
SYN-1016 : Merged 25 instances.
SYN-1032 : 21525/20 useful/useless nets, 19132/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 2 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 2 mux instances.
SYN-1015 : Optimize round 1, 307 better
SYN-1014 : Optimize round 2
SYN-1032 : 21294/30 useful/useless nets, 18901/32 useful/useless insts
SYN-1015 : Optimize round 2, 64 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.484681s wall, 2.390625s user + 0.093750s system = 2.484375s CPU (100.0%)

RUN-1004 : used memory is 320 MB, reserved memory is 287 MB, peak memory is 322 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 21318/157 useful/useless nets, 18948/31 useful/useless insts
SYN-1016 : Merged 21 instances.
SYN-2571 : Optimize after map_dsp, round 1, 209 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 24 instances.
SYN-2501 : Optimize round 1, 50 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 14 macro adder
SYN-1019 : Optimized 9 mux instances.
SYN-1016 : Merged 10 instances.
SYN-1032 : 21701/5 useful/useless nets, 19331/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 78857, tnet num: 21701, tinst num: 19330, tnode num: 110820, tedge num: 123058.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.267367s wall, 1.234375s user + 0.015625s system = 1.250000s CPU (98.6%)

RUN-1004 : used memory is 454 MB, reserved memory is 422 MB, peak memory is 454 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21701 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 184 (3.58), #lev = 7 (1.95)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 184 (3.58), #lev = 7 (1.95)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 437 instances into 184 LUTs, name keeping = 74%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 318 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 108 adder to BLE ...
SYN-4008 : Packed 108 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.728555s wall, 4.625000s user + 0.078125s system = 4.703125s CPU (99.5%)

RUN-1004 : used memory is 353 MB, reserved memory is 332 MB, peak memory is 558 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.557742s wall, 7.328125s user + 0.218750s system = 7.546875s CPU (99.9%)

RUN-1004 : used memory is 353 MB, reserved memory is 333 MB, peak memory is 558 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/uart_com2/AGRIC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/uart_com2/AGRIC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/uart_com2/AGRIC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/uart_com2/AGRIC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/uart_com2/AGRIC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/uart_com2/AGRIC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/uart_com2/AGRIC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/uart_com2/AGRIC_data[0]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[7] will be merged to another kept net COM3/rmc_com3/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[6] will be merged to another kept net COM3/rmc_com3/GPRMC_data[6]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (199 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 18756 instances
RUN-0007 : 5316 luts, 11936 seqs, 925 mslices, 490 lslices, 60 pads, 24 brams, 0 dsps
RUN-1001 : There are total 21150 nets
RUN-1001 : 15994 nets have 2 pins
RUN-1001 : 4024 nets have [3 - 5] pins
RUN-1001 : 802 nets have [6 - 10] pins
RUN-1001 : 209 nets have [11 - 20] pins
RUN-1001 : 102 nets have [21 - 99] pins
RUN-1001 : 19 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4745     
RUN-1001 :   No   |  No   |  Yes  |     637     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     371     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  111  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 119
PHY-3001 : Initial placement ...
PHY-3001 : design contains 18754 instances, 5316 luts, 11936 seqs, 1415 slices, 281 macros(1415 instances: 925 mslices 490 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 61%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 77624, tnet num: 21148, tinst num: 18754, tnode num: 109521, tedge num: 121964.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.228700s wall, 1.203125s user + 0.031250s system = 1.234375s CPU (100.5%)

RUN-1004 : used memory is 512 MB, reserved memory is 484 MB, peak memory is 558 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21148 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.155131s wall, 2.093750s user + 0.062500s system = 2.156250s CPU (100.1%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.53158e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 18754.
PHY-3001 : Level 1 #clusters 2039.
PHY-3001 : End clustering;  0.151593s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (175.2%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 61%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 834881, overlap = 604.531
PHY-3002 : Step(2): len = 735402, overlap = 679.938
PHY-3002 : Step(3): len = 488786, overlap = 842.875
PHY-3002 : Step(4): len = 417935, overlap = 904.844
PHY-3002 : Step(5): len = 330404, overlap = 994.75
PHY-3002 : Step(6): len = 292118, overlap = 1058.41
PHY-3002 : Step(7): len = 243285, overlap = 1129.72
PHY-3002 : Step(8): len = 218581, overlap = 1194.53
PHY-3002 : Step(9): len = 196404, overlap = 1248.78
PHY-3002 : Step(10): len = 180653, overlap = 1300.28
PHY-3002 : Step(11): len = 165163, overlap = 1341.28
PHY-3002 : Step(12): len = 151898, overlap = 1364.06
PHY-3002 : Step(13): len = 142684, overlap = 1387.44
PHY-3002 : Step(14): len = 131595, overlap = 1409.03
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.42863e-06
PHY-3002 : Step(15): len = 135989, overlap = 1388.28
PHY-3002 : Step(16): len = 190155, overlap = 1225.69
PHY-3002 : Step(17): len = 209790, overlap = 1110.84
PHY-3002 : Step(18): len = 208143, overlap = 1015.5
PHY-3002 : Step(19): len = 201394, overlap = 983.281
PHY-3002 : Step(20): len = 194461, overlap = 945.156
PHY-3002 : Step(21): len = 185871, overlap = 932.156
PHY-3002 : Step(22): len = 179224, overlap = 944.344
PHY-3002 : Step(23): len = 175268, overlap = 939.688
PHY-3002 : Step(24): len = 171218, overlap = 932.469
PHY-3002 : Step(25): len = 168792, overlap = 936.344
PHY-3002 : Step(26): len = 166222, overlap = 949.969
PHY-3002 : Step(27): len = 166067, overlap = 940.75
PHY-3002 : Step(28): len = 164948, overlap = 944.375
PHY-3002 : Step(29): len = 164502, overlap = 939.156
PHY-3002 : Step(30): len = 162625, overlap = 941.938
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.85726e-06
PHY-3002 : Step(31): len = 171066, overlap = 909.031
PHY-3002 : Step(32): len = 185835, overlap = 832.375
PHY-3002 : Step(33): len = 189931, overlap = 801.719
PHY-3002 : Step(34): len = 191820, overlap = 780.281
PHY-3002 : Step(35): len = 191855, overlap = 776.406
PHY-3002 : Step(36): len = 191169, overlap = 771.125
PHY-3002 : Step(37): len = 190047, overlap = 763.406
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 5.71452e-06
PHY-3002 : Step(38): len = 201349, overlap = 735.562
PHY-3002 : Step(39): len = 216556, overlap = 669.594
PHY-3002 : Step(40): len = 222459, overlap = 648.281
PHY-3002 : Step(41): len = 225162, overlap = 639.719
PHY-3002 : Step(42): len = 224352, overlap = 635.219
PHY-3002 : Step(43): len = 222095, overlap = 649.781
PHY-3002 : Step(44): len = 220321, overlap = 657.062
PHY-3002 : Step(45): len = 219930, overlap = 662.781
PHY-3002 : Step(46): len = 219057, overlap = 660.656
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 1.1429e-05
PHY-3002 : Step(47): len = 231145, overlap = 622.406
PHY-3002 : Step(48): len = 245040, overlap = 552.344
PHY-3002 : Step(49): len = 249932, overlap = 510.812
PHY-3002 : Step(50): len = 252045, overlap = 503
PHY-3002 : Step(51): len = 251334, overlap = 506.625
PHY-3002 : Step(52): len = 250412, overlap = 511.469
PHY-3002 : Step(53): len = 248327, overlap = 518
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 2.28581e-05
PHY-3002 : Step(54): len = 261462, overlap = 485.281
PHY-3002 : Step(55): len = 274235, overlap = 425.781
PHY-3002 : Step(56): len = 278759, overlap = 408.094
PHY-3002 : Step(57): len = 280803, overlap = 399.219
PHY-3002 : Step(58): len = 279841, overlap = 385.062
PHY-3002 : Step(59): len = 277230, overlap = 393.938
PHY-3002 : Step(60): len = 274338, overlap = 405.625
PHY-3002 : Step(61): len = 272942, overlap = 430.625
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 4.57162e-05
PHY-3002 : Step(62): len = 283981, overlap = 420.594
PHY-3002 : Step(63): len = 292032, overlap = 406.562
PHY-3002 : Step(64): len = 294595, overlap = 361.875
PHY-3002 : Step(65): len = 296218, overlap = 351.438
PHY-3002 : Step(66): len = 295107, overlap = 345.844
PHY-3002 : Step(67): len = 294084, overlap = 353
PHY-3002 : Step(68): len = 292585, overlap = 346.156
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 9.14324e-05
PHY-3002 : Step(69): len = 299846, overlap = 329.312
PHY-3002 : Step(70): len = 307941, overlap = 294.625
PHY-3002 : Step(71): len = 310822, overlap = 270.062
PHY-3002 : Step(72): len = 312977, overlap = 248.062
PHY-3002 : Step(73): len = 312158, overlap = 259.719
PHY-3002 : Step(74): len = 310748, overlap = 264.469
PHY-3002 : Step(75): len = 308539, overlap = 279.25
PHY-3002 : Step(76): len = 309163, overlap = 291.406
PHY-3002 : Step(77): len = 309206, overlap = 279.531
PHY-3002 : Step(78): len = 310025, overlap = 275.656
PHY-3002 : Step(79): len = 309150, overlap = 274.219
PHY-3002 : Step(80): len = 308839, overlap = 284.719
PHY-3002 : Step(81): len = 307811, overlap = 276.562
PHY-3002 : Step(82): len = 307110, overlap = 271.781
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000182865
PHY-3002 : Step(83): len = 310816, overlap = 269.188
PHY-3002 : Step(84): len = 316620, overlap = 240.938
PHY-3002 : Step(85): len = 318184, overlap = 226.438
PHY-3002 : Step(86): len = 318719, overlap = 222.75
PHY-3002 : Step(87): len = 318396, overlap = 224.188
PHY-3002 : Step(88): len = 318046, overlap = 226.062
PHY-3002 : Step(89): len = 317317, overlap = 222.812
PHY-3002 : Step(90): len = 318806, overlap = 211.406
PHY-3002 : Step(91): len = 318543, overlap = 210.562
PHY-3002 : Step(92): len = 319464, overlap = 190.688
PHY-3002 : Step(93): len = 318717, overlap = 187.5
PHY-3002 : Step(94): len = 319648, overlap = 179.531
PHY-3002 : Step(95): len = 319194, overlap = 180.75
PHY-3002 : Step(96): len = 319364, overlap = 183.281
PHY-3002 : Step(97): len = 318531, overlap = 185.531
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000328446
PHY-3002 : Step(98): len = 320620, overlap = 186.875
PHY-3002 : Step(99): len = 324338, overlap = 186.406
PHY-3002 : Step(100): len = 325458, overlap = 181.969
PHY-3002 : Step(101): len = 326666, overlap = 173.062
PHY-3002 : Step(102): len = 326979, overlap = 184.25
PHY-3002 : Step(103): len = 327127, overlap = 178.938
PHY-3002 : Step(104): len = 326045, overlap = 176.375
PHY-3002 : Step(105): len = 326208, overlap = 188.938
PHY-3002 : Step(106): len = 326694, overlap = 196.406
PHY-3002 : Step(107): len = 327398, overlap = 207.031
PHY-3002 : Step(108): len = 326193, overlap = 209.625
PHY-3002 : Step(109): len = 325737, overlap = 207.75
PHY-3002 : Step(110): len = 326072, overlap = 205.688
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000578929
PHY-3002 : Step(111): len = 327290, overlap = 204.5
PHY-3002 : Step(112): len = 330986, overlap = 196.75
PHY-3002 : Step(113): len = 332513, overlap = 180.906
PHY-3002 : Step(114): len = 334512, overlap = 183.812
PHY-3002 : Step(115): len = 335398, overlap = 191.094
PHY-3002 : Step(116): len = 336500, overlap = 195.719
PHY-3002 : Step(117): len = 336409, overlap = 185.594
PHY-3002 : Step(118): len = 335709, overlap = 188.344
PHY-3002 : Step(119): len = 336138, overlap = 187.969
PHY-3002 : Step(120): len = 336160, overlap = 186.062
PHY-3002 : Step(121): len = 336475, overlap = 184.531
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.013771s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (113.5%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 67%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21150.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 432184, over cnt = 1145(3%), over = 5117, worst = 50
PHY-1001 : End global iterations;  0.875275s wall, 1.203125s user + 0.015625s system = 1.218750s CPU (139.2%)

PHY-1001 : Congestion index: top1 = 76.40, top5 = 53.34, top10 = 43.10, top15 = 37.42.
PHY-3001 : End congestion estimation;  1.115714s wall, 1.453125s user + 0.031250s system = 1.484375s CPU (133.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21148 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.986468s wall, 0.984375s user + 0.000000s system = 0.984375s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000103679
PHY-3002 : Step(122): len = 372261, overlap = 172.344
PHY-3002 : Step(123): len = 385109, overlap = 179.812
PHY-3002 : Step(124): len = 385102, overlap = 168.25
PHY-3002 : Step(125): len = 383919, overlap = 167.281
PHY-3002 : Step(126): len = 388018, overlap = 162.312
PHY-3002 : Step(127): len = 391901, overlap = 149.875
PHY-3002 : Step(128): len = 393791, overlap = 143.531
PHY-3002 : Step(129): len = 396113, overlap = 136.969
PHY-3002 : Step(130): len = 400541, overlap = 134.75
PHY-3002 : Step(131): len = 404209, overlap = 126.938
PHY-3002 : Step(132): len = 403631, overlap = 119.312
PHY-3002 : Step(133): len = 404696, overlap = 120.062
PHY-3002 : Step(134): len = 405996, overlap = 116.75
PHY-3002 : Step(135): len = 404349, overlap = 113.656
PHY-3002 : Step(136): len = 404018, overlap = 114.75
PHY-3002 : Step(137): len = 403470, overlap = 113.125
PHY-3002 : Step(138): len = 403312, overlap = 113.938
PHY-3002 : Step(139): len = 403396, overlap = 116.688
PHY-3002 : Step(140): len = 403393, overlap = 118.656
PHY-3002 : Step(141): len = 403246, overlap = 114.812
PHY-3002 : Step(142): len = 403835, overlap = 113.656
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000207358
PHY-3002 : Step(143): len = 403783, overlap = 111.625
PHY-3002 : Step(144): len = 406394, overlap = 110.969
PHY-3002 : Step(145): len = 410440, overlap = 108.656
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(146): len = 413295, overlap = 106.469
PHY-3002 : Step(147): len = 420514, overlap = 111.469
PHY-3002 : Step(148): len = 429034, overlap = 116.875
PHY-3002 : Step(149): len = 430131, overlap = 117.719
PHY-3002 : Step(150): len = 430432, overlap = 120.188
PHY-3002 : Step(151): len = 430868, overlap = 120.656
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 67%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 64/21150.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 496368, over cnt = 2088(5%), over = 9114, worst = 37
PHY-1001 : End global iterations;  1.090374s wall, 1.750000s user + 0.046875s system = 1.796875s CPU (164.8%)

PHY-1001 : Congestion index: top1 = 70.09, top5 = 53.76, top10 = 46.92, top15 = 42.52.
PHY-3001 : End congestion estimation;  1.367832s wall, 2.031250s user + 0.046875s system = 2.078125s CPU (151.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21148 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.987118s wall, 0.937500s user + 0.046875s system = 0.984375s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000101666
PHY-3002 : Step(152): len = 435128, overlap = 337.906
PHY-3002 : Step(153): len = 441421, overlap = 271.469
PHY-3002 : Step(154): len = 435652, overlap = 243.656
PHY-3002 : Step(155): len = 432040, overlap = 232.188
PHY-3002 : Step(156): len = 431223, overlap = 216.844
PHY-3002 : Step(157): len = 430340, overlap = 209.656
PHY-3002 : Step(158): len = 427978, overlap = 195.5
PHY-3002 : Step(159): len = 426012, overlap = 178.75
PHY-3002 : Step(160): len = 423992, overlap = 169.75
PHY-3002 : Step(161): len = 423419, overlap = 169.844
PHY-3002 : Step(162): len = 420337, overlap = 169.219
PHY-3002 : Step(163): len = 418918, overlap = 172.438
PHY-3002 : Step(164): len = 418890, overlap = 171.312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000203332
PHY-3002 : Step(165): len = 419317, overlap = 171.656
PHY-3002 : Step(166): len = 420987, overlap = 164.031
PHY-3002 : Step(167): len = 423197, overlap = 156.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000406663
PHY-3002 : Step(168): len = 426452, overlap = 147.062
PHY-3002 : Step(169): len = 433453, overlap = 130.812
PHY-3002 : Step(170): len = 438018, overlap = 125.344
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000813327
PHY-3002 : Step(171): len = 438168, overlap = 124.5
PHY-3002 : Step(172): len = 441471, overlap = 119.531
PHY-3002 : Step(173): len = 444997, overlap = 112.594
PHY-3002 : Step(174): len = 447488, overlap = 104.625
PHY-3002 : Step(175): len = 447300, overlap = 102.812
PHY-3002 : Step(176): len = 447379, overlap = 106.906
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 77624, tnet num: 21148, tinst num: 18754, tnode num: 109521, tedge num: 121964.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.658861s wall, 1.609375s user + 0.046875s system = 1.656250s CPU (99.8%)

RUN-1004 : used memory is 552 MB, reserved memory is 527 MB, peak memory is 679 MB
OPT-1001 : Total overflow 464.53 peak overflow 3.53
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 638/21150.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 525720, over cnt = 2306(6%), over = 7895, worst = 24
PHY-1001 : End global iterations;  1.214944s wall, 1.828125s user + 0.031250s system = 1.859375s CPU (153.0%)

PHY-1001 : Congestion index: top1 = 55.04, top5 = 46.08, top10 = 41.30, top15 = 38.24.
PHY-1001 : End incremental global routing;  1.492138s wall, 2.093750s user + 0.031250s system = 2.125000s CPU (142.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21148 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.054999s wall, 1.046875s user + 0.015625s system = 1.062500s CPU (100.7%)

OPT-1001 : 17 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 18674 has valid locations, 226 needs to be replaced
PHY-3001 : design contains 18963 instances, 5409 luts, 12052 seqs, 1415 slices, 281 macros(1415 instances: 925 mslices 490 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 462636
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16483/21359.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 535144, over cnt = 2331(6%), over = 7907, worst = 24
PHY-1001 : End global iterations;  0.211838s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (154.9%)

PHY-1001 : Congestion index: top1 = 54.98, top5 = 46.16, top10 = 41.46, top15 = 38.46.
PHY-3001 : End congestion estimation;  0.473797s wall, 0.593750s user + 0.000000s system = 0.593750s CPU (125.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 78295, tnet num: 21357, tinst num: 18963, tnode num: 110458, tedge num: 122888.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.629489s wall, 1.578125s user + 0.062500s system = 1.640625s CPU (100.7%)

RUN-1004 : used memory is 594 MB, reserved memory is 590 MB, peak memory is 683 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21357 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.730543s wall, 2.640625s user + 0.093750s system = 2.734375s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(177): len = 462384, overlap = 1
PHY-3002 : Step(178): len = 463503, overlap = 1.09375
PHY-3002 : Step(179): len = 464808, overlap = 1
PHY-3002 : Step(180): len = 465712, overlap = 1
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 16506/21359.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 534472, over cnt = 2329(6%), over = 7991, worst = 24
PHY-1001 : End global iterations;  0.199825s wall, 0.234375s user + 0.031250s system = 0.265625s CPU (132.9%)

PHY-1001 : Congestion index: top1 = 55.60, top5 = 46.42, top10 = 41.57, top15 = 38.55.
PHY-3001 : End congestion estimation;  0.529766s wall, 0.578125s user + 0.031250s system = 0.609375s CPU (115.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21357 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.062343s wall, 1.046875s user + 0.015625s system = 1.062500s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000695979
PHY-3002 : Step(181): len = 465723, overlap = 109.562
PHY-3002 : Step(182): len = 465694, overlap = 109.938
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00139196
PHY-3002 : Step(183): len = 465616, overlap = 108.875
PHY-3002 : Step(184): len = 465712, overlap = 109.156
PHY-3001 : Final: Len = 465712, Over = 109.156
PHY-3001 : End incremental placement;  5.718094s wall, 6.250000s user + 0.390625s system = 6.640625s CPU (116.1%)

OPT-1001 : Total overflow 467.78 peak overflow 3.53
OPT-1001 : End high-fanout net optimization;  8.843930s wall, 10.078125s user + 0.437500s system = 10.515625s CPU (118.9%)

OPT-1001 : Current memory(MB): used = 685, reserve = 665, peak = 701.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16528/21359.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 536792, over cnt = 2297(6%), over = 7534, worst = 24
PHY-1002 : len = 571696, over cnt = 1601(4%), over = 4080, worst = 21
PHY-1002 : len = 601144, over cnt = 768(2%), over = 1770, worst = 12
PHY-1002 : len = 611128, over cnt = 463(1%), over = 1088, worst = 12
PHY-1002 : len = 625192, over cnt = 16(0%), over = 46, worst = 7
PHY-1001 : End global iterations;  1.260615s wall, 1.921875s user + 0.015625s system = 1.937500s CPU (153.7%)

PHY-1001 : Congestion index: top1 = 48.08, top5 = 42.05, top10 = 38.96, top15 = 36.96.
OPT-1001 : End congestion update;  1.529333s wall, 2.187500s user + 0.015625s system = 2.203125s CPU (144.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 21357 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.920160s wall, 0.890625s user + 0.031250s system = 0.921875s CPU (100.2%)

OPT-0007 : Start: WNS 4638 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.456751s wall, 3.078125s user + 0.046875s system = 3.125000s CPU (127.2%)

OPT-1001 : Current memory(MB): used = 661, reserve = 646, peak = 701.
OPT-1001 : End physical optimization;  13.290286s wall, 15.234375s user + 0.562500s system = 15.796875s CPU (118.9%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5409 LUT to BLE ...
SYN-4008 : Packed 5409 LUT and 2602 SEQ to BLE.
SYN-4003 : Packing 9450 remaining SEQ's ...
SYN-4005 : Packed 3190 SEQ with LUT/SLICE
SYN-4006 : 126 single LUT's are left
SYN-4006 : 6260 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11669/13309 primitive instances ...
PHY-3001 : End packing;  3.018979s wall, 3.015625s user + 0.000000s system = 3.015625s CPU (99.9%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 7901 instances
RUN-1001 : 3906 mslices, 3906 lslices, 60 pads, 24 brams, 0 dsps
RUN-1001 : There are total 18807 nets
RUN-1001 : 13326 nets have 2 pins
RUN-1001 : 4133 nets have [3 - 5] pins
RUN-1001 : 868 nets have [6 - 10] pins
RUN-1001 : 346 nets have [11 - 20] pins
RUN-1001 : 125 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 7899 instances, 7812 slices, 281 macros(1415 instances: 925 mslices 490 lslices)
PHY-3001 : Cell area utilization is 83%
PHY-3001 : After packing: Len = 483116, Over = 355.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 83%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7570/18807.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 602800, over cnt = 1401(3%), over = 2214, worst = 7
PHY-1002 : len = 608640, over cnt = 836(2%), over = 1163, worst = 6
PHY-1002 : len = 615880, over cnt = 435(1%), over = 564, worst = 6
PHY-1002 : len = 622600, over cnt = 118(0%), over = 157, worst = 4
PHY-1002 : len = 625832, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.185345s wall, 2.031250s user + 0.015625s system = 2.046875s CPU (172.7%)

PHY-1001 : Congestion index: top1 = 50.17, top5 = 43.27, top10 = 39.47, top15 = 37.10.
PHY-3001 : End congestion estimation;  1.529785s wall, 2.375000s user + 0.015625s system = 2.390625s CPU (156.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 64909, tnet num: 18805, tinst num: 7899, tnode num: 88352, tedge num: 106881.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.769111s wall, 1.734375s user + 0.031250s system = 1.765625s CPU (99.8%)

RUN-1004 : used memory is 585 MB, reserved memory is 576 MB, peak memory is 701 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 18805 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.809372s wall, 2.750000s user + 0.062500s system = 2.812500s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.24258e-05
PHY-3002 : Step(185): len = 487265, overlap = 344.5
PHY-3002 : Step(186): len = 484719, overlap = 355.75
PHY-3002 : Step(187): len = 485119, overlap = 357
PHY-3002 : Step(188): len = 486499, overlap = 366.75
PHY-3002 : Step(189): len = 485478, overlap = 369
PHY-3002 : Step(190): len = 485451, overlap = 364.75
PHY-3002 : Step(191): len = 485083, overlap = 361.75
PHY-3002 : Step(192): len = 483750, overlap = 367.75
PHY-3002 : Step(193): len = 481668, overlap = 367
PHY-3002 : Step(194): len = 480042, overlap = 366.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000104852
PHY-3002 : Step(195): len = 484565, overlap = 356.5
PHY-3002 : Step(196): len = 488522, overlap = 342.25
PHY-3002 : Step(197): len = 489120, overlap = 343.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000209703
PHY-3002 : Step(198): len = 494916, overlap = 324.75
PHY-3002 : Step(199): len = 505008, overlap = 314.5
PHY-3002 : Step(200): len = 504248, overlap = 311.25
PHY-3002 : Step(201): len = 502674, overlap = 313.25
PHY-3002 : Step(202): len = 501778, overlap = 322
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.815872s wall, 0.828125s user + 0.843750s system = 1.671875s CPU (204.9%)

PHY-3001 : Trial Legalized: Len = 609528
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 83%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 392/18807.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 689256, over cnt = 2179(6%), over = 3592, worst = 6
PHY-1002 : len = 701344, over cnt = 1380(3%), over = 2024, worst = 6
PHY-1002 : len = 714592, over cnt = 748(2%), over = 1101, worst = 6
PHY-1002 : len = 726896, over cnt = 246(0%), over = 363, worst = 6
PHY-1002 : len = 732496, over cnt = 1(0%), over = 2, worst = 2
PHY-1001 : End global iterations;  2.056142s wall, 3.343750s user + 0.015625s system = 3.359375s CPU (163.4%)

PHY-1001 : Congestion index: top1 = 50.30, top5 = 45.49, top10 = 42.76, top15 = 40.87.
PHY-3001 : End congestion estimation;  2.448867s wall, 3.734375s user + 0.015625s system = 3.750000s CPU (153.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 18805 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.959506s wall, 0.937500s user + 0.031250s system = 0.968750s CPU (101.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000161016
PHY-3002 : Step(203): len = 566584, overlap = 82
PHY-3002 : Step(204): len = 548334, overlap = 121.25
PHY-3002 : Step(205): len = 536694, overlap = 162.5
PHY-3002 : Step(206): len = 528858, overlap = 193.25
PHY-3002 : Step(207): len = 525329, overlap = 203.25
PHY-3002 : Step(208): len = 522654, overlap = 223
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000322033
PHY-3002 : Step(209): len = 526930, overlap = 213.75
PHY-3002 : Step(210): len = 531137, overlap = 211.25
PHY-3002 : Step(211): len = 532324, overlap = 219.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(212): len = 534231, overlap = 213.5
PHY-3002 : Step(213): len = 539500, overlap = 217.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.036255s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (129.3%)

PHY-3001 : Legalized: Len = 577472, Over = 0
PHY-3001 : Spreading special nets. 32 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.084782s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (92.1%)

PHY-3001 : 47 instances has been re-located, deltaX = 15, deltaY = 27, maxDist = 2.
PHY-3001 : Final: Len = 578292, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 64909, tnet num: 18805, tinst num: 7899, tnode num: 88352, tedge num: 106881.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.132127s wall, 2.109375s user + 0.031250s system = 2.140625s CPU (100.4%)

RUN-1004 : used memory is 599 MB, reserved memory is 593 MB, peak memory is 701 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 4161/18807.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 670736, over cnt = 1988(5%), over = 3152, worst = 7
PHY-1002 : len = 680968, over cnt = 1301(3%), over = 1781, worst = 6
PHY-1002 : len = 698712, over cnt = 316(0%), over = 436, worst = 5
PHY-1002 : len = 704576, over cnt = 67(0%), over = 79, worst = 5
PHY-1002 : len = 706288, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.824417s wall, 3.015625s user + 0.015625s system = 3.031250s CPU (166.1%)

PHY-1001 : Congestion index: top1 = 48.06, top5 = 43.39, top10 = 40.76, top15 = 39.07.
PHY-1001 : End incremental global routing;  2.186046s wall, 3.343750s user + 0.015625s system = 3.359375s CPU (153.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 18805 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.967295s wall, 0.953125s user + 0.015625s system = 0.968750s CPU (100.2%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 7835 has valid locations, 16 needs to be replaced
PHY-3001 : design contains 7914 instances, 7827 slices, 281 macros(1415 instances: 925 mslices 490 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 581294
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16913/18836.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 711640, over cnt = 32(0%), over = 41, worst = 4
PHY-1002 : len = 711600, over cnt = 18(0%), over = 20, worst = 2
PHY-1002 : len = 711688, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 711848, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.586427s wall, 0.625000s user + 0.000000s system = 0.625000s CPU (106.6%)

PHY-1001 : Congestion index: top1 = 48.47, top5 = 43.70, top10 = 41.01, top15 = 39.27.
PHY-3001 : End congestion estimation;  0.916496s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (102.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 65043, tnet num: 18834, tinst num: 7914, tnode num: 88516, tedge num: 107089.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.155789s wall, 2.140625s user + 0.015625s system = 2.156250s CPU (100.0%)

RUN-1004 : used memory is 627 MB, reserved memory is 618 MB, peak memory is 701 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 18834 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.143300s wall, 3.125000s user + 0.015625s system = 3.140625s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(214): len = 581361, overlap = 1
PHY-3002 : Step(215): len = 581339, overlap = 0.75
PHY-3002 : Step(216): len = 581401, overlap = 1
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 16907/18836.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 709936, over cnt = 33(0%), over = 46, worst = 5
PHY-1002 : len = 710040, over cnt = 17(0%), over = 17, worst = 1
PHY-1002 : len = 710184, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 710248, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.560710s wall, 0.578125s user + 0.046875s system = 0.625000s CPU (111.5%)

PHY-1001 : Congestion index: top1 = 48.36, top5 = 43.62, top10 = 40.97, top15 = 39.24.
PHY-3001 : End congestion estimation;  0.877672s wall, 0.906250s user + 0.046875s system = 0.953125s CPU (108.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 18834 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.942131s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000634346
PHY-3002 : Step(217): len = 581453, overlap = 1.75
PHY-3002 : Step(218): len = 581428, overlap = 1.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007010s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (222.9%)

PHY-3001 : Legalized: Len = 581560, Over = 0
PHY-3001 : End spreading;  0.073789s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (84.7%)

PHY-3001 : Final: Len = 581560, Over = 0
PHY-3001 : End incremental placement;  6.545758s wall, 6.640625s user + 0.140625s system = 6.781250s CPU (103.6%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.227781s wall, 11.468750s user + 0.171875s system = 11.640625s CPU (113.8%)

OPT-1001 : Current memory(MB): used = 697, reserve = 683, peak = 703.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16911/18836.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 710440, over cnt = 10(0%), over = 16, worst = 2
PHY-1002 : len = 710440, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 710488, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 710552, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.544466s wall, 0.562500s user + 0.015625s system = 0.578125s CPU (106.2%)

PHY-1001 : Congestion index: top1 = 48.34, top5 = 43.49, top10 = 40.85, top15 = 39.14.
OPT-1001 : End congestion update;  0.865962s wall, 0.890625s user + 0.015625s system = 0.906250s CPU (104.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 18834 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.794513s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (98.3%)

OPT-0007 : Start: WNS 4693 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.665203s wall, 1.687500s user + 0.015625s system = 1.703125s CPU (102.3%)

OPT-1001 : Current memory(MB): used = 695, reserve = 681, peak = 703.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 18834 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.800734s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16928/18836.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 710552, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.121495s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (90.0%)

PHY-1001 : Congestion index: top1 = 48.34, top5 = 43.49, top10 = 40.85, top15 = 39.14.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 18834 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.809844s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (100.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4693 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 48.000000
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4693ps with logic level 1 
OPT-1001 : End physical optimization;  16.359815s wall, 17.546875s user + 0.296875s system = 17.843750s CPU (109.1%)

RUN-1003 : finish command "place" in  67.503425s wall, 117.625000s user + 7.531250s system = 125.156250s CPU (185.4%)

RUN-1004 : used memory is 582 MB, reserved memory is 567 MB, peak memory is 703 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.798280s wall, 3.046875s user + 0.000000s system = 3.046875s CPU (169.4%)

RUN-1004 : used memory is 583 MB, reserved memory is 568 MB, peak memory is 703 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 7916 instances
RUN-1001 : 3921 mslices, 3906 lslices, 60 pads, 24 brams, 0 dsps
RUN-1001 : There are total 18836 nets
RUN-1001 : 13340 nets have 2 pins
RUN-1001 : 4138 nets have [3 - 5] pins
RUN-1001 : 872 nets have [6 - 10] pins
RUN-1001 : 353 nets have [11 - 20] pins
RUN-1001 : 124 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 65043, tnet num: 18834, tinst num: 7914, tnode num: 88516, tedge num: 107089.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.852352s wall, 1.843750s user + 0.000000s system = 1.843750s CPU (99.5%)

RUN-1004 : used memory is 570 MB, reserved memory is 551 MB, peak memory is 703 MB
PHY-1001 : 3921 mslices, 3906 lslices, 60 pads, 24 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 18834 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 651712, over cnt = 2164(6%), over = 3647, worst = 8
PHY-1002 : len = 666720, over cnt = 1325(3%), over = 1918, worst = 8
PHY-1002 : len = 683144, over cnt = 551(1%), over = 718, worst = 7
PHY-1002 : len = 691984, over cnt = 139(0%), over = 174, worst = 5
PHY-1002 : len = 694800, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.785115s wall, 3.109375s user + 0.000000s system = 3.109375s CPU (174.2%)

PHY-1001 : Congestion index: top1 = 48.66, top5 = 43.22, top10 = 40.50, top15 = 38.71.
PHY-1001 : End global routing;  2.151604s wall, 3.484375s user + 0.000000s system = 3.484375s CPU (161.9%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 678, reserve = 672, peak = 703.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 948, reserve = 936, peak = 948.
PHY-1001 : End build detailed router design. 4.850371s wall, 4.765625s user + 0.078125s system = 4.843750s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 187216, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.919968s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 985, reserve = 974, peak = 985.
PHY-1001 : End phase 1; 0.928003s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (99.3%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.66686e+06, over cnt = 1175(0%), over = 1179, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1001, reserve = 990, peak = 1001.
PHY-1001 : End initial routed; 15.341621s wall, 42.718750s user + 0.296875s system = 43.015625s CPU (280.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17640(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.335   |   0.000   |   0   
RUN-1001 :   Hold   |   0.195   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.566784s wall, 3.578125s user + 0.000000s system = 3.578125s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 1019, reserve = 1008, peak = 1019.
PHY-1001 : End phase 2; 18.908569s wall, 46.296875s user + 0.296875s system = 46.593750s CPU (246.4%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.66686e+06, over cnt = 1175(0%), over = 1179, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.258652s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (96.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.65563e+06, over cnt = 395(0%), over = 395, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.692570s wall, 1.093750s user + 0.015625s system = 1.109375s CPU (160.2%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.65601e+06, over cnt = 60(0%), over = 60, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.390129s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (140.2%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.65674e+06, over cnt = 18(0%), over = 18, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.234754s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (106.5%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.65689e+06, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.202668s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (115.6%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.65693e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.212228s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (95.7%)

PHY-1001 : ===== DR Iter 6 =====
PHY-1022 : len = 1.65701e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 6; 0.184436s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (101.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17640(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.328   |   0.000   |   0   
RUN-1001 :   Hold   |   0.195   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.632077s wall, 3.625000s user + 0.000000s system = 3.625000s CPU (99.8%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 279 feed throughs used by 228 nets
PHY-1001 : End commit to database; 2.173793s wall, 2.171875s user + 0.000000s system = 2.171875s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 1101, reserve = 1093, peak = 1101.
PHY-1001 : End phase 3; 8.546608s wall, 9.140625s user + 0.031250s system = 9.171875s CPU (107.3%)

PHY-1003 : Routed, final wirelength = 1.65701e+06
PHY-1001 : Current memory(MB): used = 1105, reserve = 1097, peak = 1105.
PHY-1001 : End export database. 0.173090s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (99.3%)

PHY-1001 : End detail routing;  33.836016s wall, 61.734375s user + 0.406250s system = 62.140625s CPU (183.7%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 65043, tnet num: 18834, tinst num: 7914, tnode num: 88516, tedge num: 107089.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.764608s wall, 1.765625s user + 0.000000s system = 1.765625s CPU (100.1%)

RUN-1004 : used memory is 1032 MB, reserved memory is 1026 MB, peak memory is 1105 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  42.362988s wall, 71.546875s user + 0.437500s system = 71.984375s CPU (169.9%)

RUN-1004 : used memory is 1031 MB, reserved memory is 1027 MB, peak memory is 1105 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8391   out of  19600   42.81%
#reg                    12163   out of  19600   62.06%
#le                     14601
  #lut only              2438   out of  14601   16.70%
  #reg only              6210   out of  14601   42.53%
  #lut&reg               5953   out of  14601   40.77%
#dsp                        0   out of     29    0.00%
#bram                      24   out of     64   37.50%
  #bram9k                  24
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                    10
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6699
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          118
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         M3        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         P6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |14601  |6976    |1415    |12206   |24      |0       |
|  AnyFog_dataX                      |AnyFog          |202    |108     |22      |168     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |84     |59      |22      |50      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |208    |72      |22      |174     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |55      |22      |52      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |203    |99      |22      |169     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |85     |61      |22      |51      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2853   |573     |34      |2776    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |203    |54      |5       |195     |0       |0       |
|    STADOP_com2                     |STADOP          |559    |84      |0       |545     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |43      |14      |43      |0       |0       |
|    head_com2                       |uniheading      |281    |91      |5       |262     |0       |0       |
|    uart_com2                       |Agrica          |1418   |259     |10      |1404    |0       |0       |
|  COM3                              |COM3_Control    |214    |98      |14      |185     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |60     |39      |14      |37      |0       |0       |
|    rmc_com3                        |Gprmc           |154    |59      |0       |148     |0       |0       |
|  DATA                              |Data_Processing |8678   |4396    |1059    |6987    |0       |0       |
|    DIV_Dtemp                       |Divider         |747    |313     |84      |620     |0       |0       |
|    DIV_Utemp                       |Divider         |574    |322     |84      |446     |0       |0       |
|    DIV_accX                        |Divider         |649    |321     |84      |521     |0       |0       |
|    DIV_accY                        |Divider         |626    |330     |108     |462     |0       |0       |
|    DIV_accZ                        |Divider         |670    |363     |132     |465     |0       |0       |
|    DIV_rateX                       |Divider         |656    |377     |132     |451     |0       |0       |
|    DIV_rateY                       |Divider         |597    |358     |132     |389     |0       |0       |
|    DIV_rateZ                       |Divider         |646    |387     |132     |439     |0       |0       |
|    genclk                          |genclk          |111    |78      |20      |61      |0       |0       |
|  FMC                               |FMC_Ctrl        |419    |364     |43      |334     |0       |0       |
|  IIC                               |I2C_master      |312    |242     |11      |266     |0       |0       |
|  IMU_CTRL                          |SCHA634         |906    |643     |61      |723     |0       |0       |
|    CtrlData                        |CtrlData        |474    |423     |47      |332     |0       |0       |
|      usms                          |Time_1ms        |28     |22      |5       |20      |0       |0       |
|    SPIM                            |SPI_SCHA634     |432    |220     |14      |391     |0       |0       |
|  POWER                             |POWER_EN        |100    |52      |38      |41      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |489    |329     |89      |323     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |489    |329     |89      |323     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |201    |140     |0       |186     |0       |0       |
|        reg_inst                    |register        |199    |138     |0       |184     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |288    |189     |89      |137     |0       |0       |
|        bus_inst                    |bus_top         |81     |52      |28      |33      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |28     |18      |10      |12      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |1      |0       |0       |1       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |3      |3       |0       |3       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |49     |31      |18      |17      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |124    |94      |29      |75      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13279  
    #2          2       3277   
    #3          3        613   
    #4          4        248   
    #5        5-10       918   
    #6        11-50      426   
    #7       51-100       5    
    #8       101-500      3    
    #9        >500        2    
  Average     2.10             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.177433s wall, 3.750000s user + 0.000000s system = 3.750000s CPU (172.2%)

RUN-1004 : used memory is 1032 MB, reserved memory is 1028 MB, peak memory is 1105 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 65043, tnet num: 18834, tinst num: 7914, tnode num: 88516, tedge num: 107089.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.777710s wall, 1.781250s user + 0.000000s system = 1.781250s CPU (100.2%)

RUN-1004 : used memory is 1034 MB, reserved memory is 1030 MB, peak memory is 1105 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 18834 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.464344s wall, 1.468750s user + 0.000000s system = 1.468750s CPU (100.3%)

RUN-1004 : used memory is 1039 MB, reserved memory is 1034 MB, peak memory is 1105 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 89dcf97a3cd86c8370cef48ec94e5c7c51982d2733d868157b75ae7b794c4fe9 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 7914
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 18836, pip num: 140662
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 279
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3239 valid insts, and 393733 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111110110100010110101
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.521138s wall, 122.187500s user + 0.187500s system = 122.375000s CPU (977.3%)

RUN-1004 : used memory is 1157 MB, reserved memory is 1142 MB, peak memory is 1271 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250710_155831.log"

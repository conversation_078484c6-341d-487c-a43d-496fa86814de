============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.5_SP3/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     51593
   Run Date =   Mon Apr 28 18:44:08 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.498153s wall, 1.484375s user + 0.000000s system = 1.484375s CPU (99.1%)

RUN-1004 : used memory is 285 MB, reserved memory is 257 MB, peak memory is 288 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 -duty_cycle 50.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 7 view nodes, 47 trigger nets, 47 data nets.
KIT-1004 : Chipwatcher code = 0001111110010110
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.5_SP3/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=144) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=144) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.5_SP3/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=144)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=144)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22896/32 useful/useless nets, 19679/17 useful/useless insts
SYN-1016 : Merged 37 instances.
SYN-1032 : 22520/22 useful/useless nets, 20138/18 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 4 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 4 mux instances.
SYN-1015 : Optimize round 1, 442 better
SYN-1014 : Optimize round 2
SYN-1032 : 22149/60 useful/useless nets, 19767/64 useful/useless insts
SYN-1015 : Optimize round 2, 128 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.003115s wall, 1.953125s user + 0.046875s system = 2.000000s CPU (99.8%)

RUN-1004 : used memory is 325 MB, reserved memory is 296 MB, peak memory is 327 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.5_SP3/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 67 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22209/367 useful/useless nets, 19868/52 useful/useless insts
SYN-1016 : Merged 57 instances.
SYN-2571 : Optimize after map_dsp, round 1, 476 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 44 instances.
SYN-2501 : Optimize round 1, 90 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 18 macro adder
SYN-1019 : Optimized 17 mux instances.
SYN-1016 : Merged 12 instances.
SYN-1032 : 22680/5 useful/useless nets, 20339/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1002 : start command "get_pins config_inst.jtck"
RUN-1002 : start command "create_clock -name jtck -period 100 "
RUN-1102 : create_clock: clock name: jtck, type: 0, period: 100000, rise: 0, fall: 50000.
RUN-1002 : start command "get_clocks jtck"
RUN-1002 : start command "set_clock_uncertainty -hold 0.1 "
RUN-1002 : start command "get_clocks jtck"
RUN-1002 : start command "set_clock_groups -exclusive -group "
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 83650, tnet num: 22680, tinst num: 20338, tnode num: 117274, tedge num: 130446.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.001036s wall, 0.953125s user + 0.046875s system = 1.000000s CPU (99.9%)

RUN-1004 : used memory is 466 MB, reserved memory is 439 MB, peak memory is 466 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22680 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 257 (3.41), #lev = 7 (1.78)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 253 (3.43), #lev = 7 (1.76)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 596 instances into 253 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 450 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 140 adder to BLE ...
SYN-4008 : Packed 140 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  3.832015s wall, 3.703125s user + 0.125000s system = 3.828125s CPU (99.9%)

RUN-1004 : used memory is 354 MB, reserved memory is 331 MB, peak memory is 581 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  6.108266s wall, 5.890625s user + 0.218750s system = 6.109375s CPU (100.0%)

RUN-1004 : used memory is 354 MB, reserved memory is 332 MB, peak memory is 581 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.5_SP3/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/rmc_com2/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/rmc_com2/GPRMC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/rmc_com2/GPRMC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/rmc_com2/GPRMC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/rmc_com2/GPRMC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/rmc_com2/GPRMC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/rmc_com2/GPRMC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/rmc_com2/GPRMC_data[0]
SYN-5055 WARNING: The kept net COM2/rx_err will be merged to another kept net COM2/rmc_com2/GPRMC_err
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sub_state_cnt0[3] will be merged to another kept net COM2/rmc_com2/GPRMC_sub_state_cnt0[2]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (306 clock/control pins, 0 other pins).
SYN-4027 : Net DATA/DIV_Dtemp/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net DATA/DIV_Dtemp/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19662 instances
RUN-0007 : 5656 luts, 12474 seqs, 933 mslices, 491 lslices, 61 pads, 42 brams, 0 dsps
RUN-1001 : There are total 22027 nets
RUN-1001 : 16316 nets have 2 pins
RUN-1001 : 4536 nets have [3 - 5] pins
RUN-1001 : 795 nets have [6 - 10] pins
RUN-1001 : 248 nets have [11 - 20] pins
RUN-1001 : 106 nets have [21 - 99] pins
RUN-1001 : 26 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4745     
RUN-1001 :   No   |  No   |  Yes  |     628     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    6531     
RUN-1001 :   Yes  |  No   |  Yes  |     470     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  106  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 114
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19660 instances, 5656 luts, 12474 seqs, 1424 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1131 pins
PHY-3001 : Huge net DATA/done_div with 1650 pins
PHY-0007 : Cell area utilization is 64%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 82170, tnet num: 22025, tinst num: 19660, tnode num: 115892, tedge num: 129243.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.006102s wall, 0.984375s user + 0.015625s system = 1.000000s CPU (99.4%)

RUN-1004 : used memory is 528 MB, reserved memory is 505 MB, peak memory is 581 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22025 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.768102s wall, 1.750000s user + 0.015625s system = 1.765625s CPU (99.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.42701e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19660.
PHY-3001 : Level 1 #clusters 2029.
PHY-3001 : End clustering;  0.134041s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (186.5%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 64%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 896156, overlap = 638.406
PHY-3002 : Step(2): len = 801936, overlap = 683.906
PHY-3002 : Step(3): len = 545707, overlap = 875.969
PHY-3002 : Step(4): len = 487660, overlap = 958.188
PHY-3002 : Step(5): len = 382690, overlap = 1082.78
PHY-3002 : Step(6): len = 333137, overlap = 1148.19
PHY-3002 : Step(7): len = 275876, overlap = 1225.91
PHY-3002 : Step(8): len = 246566, overlap = 1264.72
PHY-3002 : Step(9): len = 219303, overlap = 1322.09
PHY-3002 : Step(10): len = 198053, overlap = 1374.81
PHY-3002 : Step(11): len = 178610, overlap = 1404.81
PHY-3002 : Step(12): len = 165586, overlap = 1435.38
PHY-3002 : Step(13): len = 152903, overlap = 1451.53
PHY-3002 : Step(14): len = 141162, overlap = 1478.88
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.35715e-06
PHY-3002 : Step(15): len = 153992, overlap = 1456.94
PHY-3002 : Step(16): len = 219579, overlap = 1284.62
PHY-3002 : Step(17): len = 221871, overlap = 1194.19
PHY-3002 : Step(18): len = 216815, overlap = 1155.97
PHY-3002 : Step(19): len = 209195, overlap = 1147.94
PHY-3002 : Step(20): len = 202785, overlap = 1115.41
PHY-3002 : Step(21): len = 196116, overlap = 1104.34
PHY-3002 : Step(22): len = 190504, overlap = 1119.19
PHY-3002 : Step(23): len = 187187, overlap = 1107.03
PHY-3002 : Step(24): len = 184641, overlap = 1097.91
PHY-3002 : Step(25): len = 182509, overlap = 1089.31
PHY-3002 : Step(26): len = 180311, overlap = 1084.19
PHY-3002 : Step(27): len = 179180, overlap = 1074.59
PHY-3002 : Step(28): len = 178475, overlap = 1066.06
PHY-3002 : Step(29): len = 178270, overlap = 1072.25
PHY-3002 : Step(30): len = 177810, overlap = 1077.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.7143e-06
PHY-3002 : Step(31): len = 186191, overlap = 1035.72
PHY-3002 : Step(32): len = 200902, overlap = 997.781
PHY-3002 : Step(33): len = 205084, overlap = 957
PHY-3002 : Step(34): len = 207701, overlap = 935.844
PHY-3002 : Step(35): len = 207579, overlap = 924.781
PHY-3002 : Step(36): len = 207232, overlap = 909.281
PHY-3002 : Step(37): len = 205183, overlap = 910.875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 5.4286e-06
PHY-3002 : Step(38): len = 220478, overlap = 844.219
PHY-3002 : Step(39): len = 239269, overlap = 748.406
PHY-3002 : Step(40): len = 246018, overlap = 694.156
PHY-3002 : Step(41): len = 247083, overlap = 678.312
PHY-3002 : Step(42): len = 244821, overlap = 664.781
PHY-3002 : Step(43): len = 242511, overlap = 675.656
PHY-3002 : Step(44): len = 241139, overlap = 689.688
PHY-3002 : Step(45): len = 239726, overlap = 688.625
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 1.08572e-05
PHY-3002 : Step(46): len = 252693, overlap = 630.656
PHY-3002 : Step(47): len = 268441, overlap = 542.281
PHY-3002 : Step(48): len = 273961, overlap = 523.312
PHY-3002 : Step(49): len = 276997, overlap = 529.844
PHY-3002 : Step(50): len = 276103, overlap = 507.469
PHY-3002 : Step(51): len = 273394, overlap = 503.969
PHY-3002 : Step(52): len = 271392, overlap = 507.5
PHY-3002 : Step(53): len = 271513, overlap = 508.344
PHY-3002 : Step(54): len = 270900, overlap = 523.594
PHY-3002 : Step(55): len = 269403, overlap = 524.031
PHY-3002 : Step(56): len = 267972, overlap = 533.531
PHY-3002 : Step(57): len = 266839, overlap = 547.188
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 2.17144e-05
PHY-3002 : Step(58): len = 279666, overlap = 502.531
PHY-3002 : Step(59): len = 293640, overlap = 451.188
PHY-3002 : Step(60): len = 297790, overlap = 436.938
PHY-3002 : Step(61): len = 298765, overlap = 428.781
PHY-3002 : Step(62): len = 295705, overlap = 433.656
PHY-3002 : Step(63): len = 294024, overlap = 425.188
PHY-3002 : Step(64): len = 291763, overlap = 432.188
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 4.34288e-05
PHY-3002 : Step(65): len = 301668, overlap = 386.531
PHY-3002 : Step(66): len = 312604, overlap = 390.062
PHY-3002 : Step(67): len = 316175, overlap = 389.031
PHY-3002 : Step(68): len = 316562, overlap = 378.219
PHY-3002 : Step(69): len = 314272, overlap = 379.781
PHY-3002 : Step(70): len = 312248, overlap = 367.562
PHY-3002 : Step(71): len = 310729, overlap = 362.844
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 8.68576e-05
PHY-3002 : Step(72): len = 317858, overlap = 341.281
PHY-3002 : Step(73): len = 327205, overlap = 335.281
PHY-3002 : Step(74): len = 330823, overlap = 342.75
PHY-3002 : Step(75): len = 331145, overlap = 327
PHY-3002 : Step(76): len = 329920, overlap = 319.875
PHY-3002 : Step(77): len = 328566, overlap = 318.031
PHY-3002 : Step(78): len = 327946, overlap = 297.344
PHY-3002 : Step(79): len = 328314, overlap = 274.469
PHY-3002 : Step(80): len = 327633, overlap = 275.625
PHY-3002 : Step(81): len = 326499, overlap = 285.438
PHY-3002 : Step(82): len = 325914, overlap = 279.312
PHY-3002 : Step(83): len = 324816, overlap = 281.656
PHY-3002 : Step(84): len = 323419, overlap = 272.781
PHY-3002 : Step(85): len = 324086, overlap = 263.344
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000172527
PHY-3002 : Step(86): len = 328280, overlap = 264.125
PHY-3002 : Step(87): len = 334136, overlap = 264.75
PHY-3002 : Step(88): len = 336126, overlap = 258.688
PHY-3002 : Step(89): len = 337100, overlap = 255.438
PHY-3002 : Step(90): len = 336883, overlap = 254.5
PHY-3002 : Step(91): len = 336913, overlap = 254.406
PHY-3002 : Step(92): len = 335821, overlap = 254.562
PHY-3002 : Step(93): len = 335879, overlap = 242.281
PHY-3002 : Step(94): len = 335288, overlap = 230.25
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000319826
PHY-3002 : Step(95): len = 338812, overlap = 230.219
PHY-3002 : Step(96): len = 342995, overlap = 230.312
PHY-3002 : Step(97): len = 344027, overlap = 227.156
PHY-3002 : Step(98): len = 345182, overlap = 221.5
PHY-3002 : Step(99): len = 345401, overlap = 210.781
PHY-3002 : Step(100): len = 346202, overlap = 193.312
PHY-3002 : Step(101): len = 345756, overlap = 202.125
PHY-3002 : Step(102): len = 346171, overlap = 188.656
PHY-3002 : Step(103): len = 345842, overlap = 191.312
PHY-3002 : Step(104): len = 346127, overlap = 180.656
PHY-3002 : Step(105): len = 345588, overlap = 180.719
PHY-3002 : Step(106): len = 345096, overlap = 182.969
PHY-3002 : Step(107): len = 345249, overlap = 188.344
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(108): len = 346332, overlap = 184.438
PHY-3002 : Step(109): len = 348568, overlap = 173
PHY-3001 : Before Legalized: Len = 370329
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.011324s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (276.0%)

PHY-3001 : After Legalized: Len = 376770, Over = 76.8125
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/22027.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 460664, over cnt = 1281(3%), over = 5483, worst = 42
PHY-1001 : End global iterations;  0.695807s wall, 0.968750s user + 0.093750s system = 1.062500s CPU (152.7%)

PHY-1001 : Congestion index: top1 = 71.72, top5 = 51.48, top10 = 43.15, top15 = 38.23.
PHY-3001 : End congestion estimation;  0.870184s wall, 1.109375s user + 0.109375s system = 1.218750s CPU (140.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22025 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.805389s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (100.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.39171e-05
PHY-3002 : Step(110): len = 390531, overlap = 109.531
PHY-3002 : Step(111): len = 411662, overlap = 113.844
PHY-3002 : Step(112): len = 408650, overlap = 113.906
PHY-3002 : Step(113): len = 405016, overlap = 111.125
PHY-3002 : Step(114): len = 410822, overlap = 104.719
PHY-3002 : Step(115): len = 411793, overlap = 95.0312
PHY-3002 : Step(116): len = 417010, overlap = 88.9375
PHY-3002 : Step(117): len = 422918, overlap = 87.3125
PHY-3002 : Step(118): len = 422147, overlap = 85.1562
PHY-3002 : Step(119): len = 422559, overlap = 77.375
PHY-3002 : Step(120): len = 423361, overlap = 78.5312
PHY-3002 : Step(121): len = 421094, overlap = 77.8438
PHY-3002 : Step(122): len = 421572, overlap = 76.375
PHY-3002 : Step(123): len = 420460, overlap = 78.5312
PHY-3002 : Step(124): len = 420916, overlap = 77.8438
PHY-3002 : Step(125): len = 417929, overlap = 77.1562
PHY-3002 : Step(126): len = 416275, overlap = 75.6562
PHY-3002 : Step(127): len = 416503, overlap = 83
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000187834
PHY-3002 : Step(128): len = 417110, overlap = 75.7188
PHY-3002 : Step(129): len = 419407, overlap = 72.5312
PHY-3002 : Step(130): len = 423510, overlap = 70.2812
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000375668
PHY-3002 : Step(131): len = 428491, overlap = 66.4688
PHY-3002 : Step(132): len = 436059, overlap = 65.5625
PHY-3002 : Step(133): len = 443167, overlap = 69
PHY-3002 : Step(134): len = 446409, overlap = 63.4062
PHY-3002 : Step(135): len = 449013, overlap = 60.6875
PHY-3002 : Step(136): len = 452169, overlap = 58.2812
PHY-3002 : Step(137): len = 454520, overlap = 59.0938
PHY-3002 : Step(138): len = 453606, overlap = 55.3125
PHY-3002 : Step(139): len = 453592, overlap = 57.4375
PHY-3002 : Step(140): len = 454541, overlap = 69.0312
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(141): len = 453578, overlap = 68.1875
PHY-3002 : Step(142): len = 456975, overlap = 72.0938
PHY-3002 : Step(143): len = 463924, overlap = 69.9375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 99/22027.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 545040, over cnt = 2381(6%), over = 10226, worst = 30
PHY-1001 : End global iterations;  0.987351s wall, 1.703125s user + 0.171875s system = 1.875000s CPU (189.9%)

PHY-1001 : Congestion index: top1 = 71.31, top5 = 56.38, top10 = 49.21, top15 = 44.73.
PHY-3001 : End congestion estimation;  1.193067s wall, 1.890625s user + 0.171875s system = 2.062500s CPU (172.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22025 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.835533s wall, 0.812500s user + 0.015625s system = 0.828125s CPU (99.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000130205
PHY-3002 : Step(144): len = 463348, overlap = 322.094
PHY-3002 : Step(145): len = 464634, overlap = 260.406
PHY-3002 : Step(146): len = 462912, overlap = 234.156
PHY-3002 : Step(147): len = 460060, overlap = 216.094
PHY-3002 : Step(148): len = 457181, overlap = 208.75
PHY-3002 : Step(149): len = 454208, overlap = 211.375
PHY-3002 : Step(150): len = 450923, overlap = 205.906
PHY-3002 : Step(151): len = 448199, overlap = 207.969
PHY-3002 : Step(152): len = 446043, overlap = 198.875
PHY-3002 : Step(153): len = 444645, overlap = 201.531
PHY-3002 : Step(154): len = 443576, overlap = 198.312
PHY-3002 : Step(155): len = 442778, overlap = 197.844
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00026041
PHY-3002 : Step(156): len = 442845, overlap = 196.094
PHY-3002 : Step(157): len = 445024, overlap = 181
PHY-3002 : Step(158): len = 447762, overlap = 178.156
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00052082
PHY-3002 : Step(159): len = 448216, overlap = 171.812
PHY-3002 : Step(160): len = 451920, overlap = 164.25
PHY-3002 : Step(161): len = 456316, overlap = 159.094
PHY-3002 : Step(162): len = 459450, overlap = 156.531
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00104164
PHY-3002 : Step(163): len = 459979, overlap = 159.031
PHY-3002 : Step(164): len = 463053, overlap = 155.906
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 82170, tnet num: 22025, tinst num: 19660, tnode num: 115892, tedge num: 129243.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.257596s wall, 1.218750s user + 0.046875s system = 1.265625s CPU (100.6%)

RUN-1004 : used memory is 571 MB, reserved memory is 550 MB, peak memory is 712 MB
OPT-1001 : Total overflow 535.09 peak overflow 3.56
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 608/22027.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 554880, over cnt = 2561(7%), over = 8902, worst = 26
PHY-1001 : End global iterations;  1.225015s wall, 1.765625s user + 0.078125s system = 1.843750s CPU (150.5%)

PHY-1001 : Congestion index: top1 = 59.31, top5 = 49.59, top10 = 44.46, top15 = 41.30.
PHY-1001 : End incremental global routing;  1.422217s wall, 1.968750s user + 0.078125s system = 2.046875s CPU (143.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22025 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.866056s wall, 0.812500s user + 0.046875s system = 0.859375s CPU (99.2%)

OPT-1001 : 21 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 61 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19575 has valid locations, 295 needs to be replaced
PHY-3001 : design contains 19934 instances, 5776 luts, 12628 seqs, 1424 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 483329
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17199/22301.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 571040, over cnt = 2574(7%), over = 8879, worst = 26
PHY-1001 : End global iterations;  0.186226s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (142.6%)

PHY-1001 : Congestion index: top1 = 59.09, top5 = 49.72, top10 = 44.64, top15 = 41.45.
PHY-3001 : End congestion estimation;  0.392744s wall, 0.484375s user + 0.015625s system = 0.500000s CPU (127.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 83082, tnet num: 22299, tinst num: 19934, tnode num: 117166, tedge num: 130519.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.260433s wall, 1.218750s user + 0.031250s system = 1.250000s CPU (99.2%)

RUN-1004 : used memory is 619 MB, reserved memory is 616 MB, peak memory is 714 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22299 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.151935s wall, 2.109375s user + 0.046875s system = 2.156250s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(165): len = 482618, overlap = 4.1875
PHY-3002 : Step(166): len = 483317, overlap = 4
PHY-3002 : Step(167): len = 484275, overlap = 4.0625
PHY-3002 : Step(168): len = 484352, overlap = 4
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(169): len = 484354, overlap = 4
PHY-3002 : Step(170): len = 485047, overlap = 4.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(171): len = 485014, overlap = 4.1875
PHY-3002 : Step(172): len = 486544, overlap = 3.8125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17212/22301.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 570784, over cnt = 2590(7%), over = 8950, worst = 26
PHY-1001 : End global iterations;  0.194536s wall, 0.296875s user + 0.062500s system = 0.359375s CPU (184.7%)

PHY-1001 : Congestion index: top1 = 59.29, top5 = 49.67, top10 = 44.71, top15 = 41.57.
PHY-3001 : End congestion estimation;  0.407343s wall, 0.500000s user + 0.062500s system = 0.562500s CPU (138.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22299 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.863073s wall, 0.875000s user + 0.000000s system = 0.875000s CPU (101.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00136927
PHY-3002 : Step(173): len = 486438, overlap = 158.844
PHY-3002 : Step(174): len = 486683, overlap = 157.469
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00262727
PHY-3002 : Step(175): len = 487032, overlap = 157.281
PHY-3002 : Step(176): len = 487472, overlap = 157.031
PHY-3001 : Final: Len = 487472, Over = 157.031
PHY-3001 : End incremental placement;  4.754202s wall, 5.390625s user + 0.375000s system = 5.765625s CPU (121.3%)

OPT-1001 : Total overflow 539.81 peak overflow 3.56
OPT-1001 : End high-fanout net optimization;  7.510367s wall, 8.703125s user + 0.500000s system = 9.203125s CPU (122.5%)

OPT-1001 : Current memory(MB): used = 717, reserve = 701, peak = 735.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17247/22301.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 571864, over cnt = 2526(7%), over = 8256, worst = 26
PHY-1002 : len = 604528, over cnt = 1853(5%), over = 5079, worst = 26
PHY-1002 : len = 654272, over cnt = 790(2%), over = 1748, worst = 18
PHY-1002 : len = 665824, over cnt = 509(1%), over = 1066, worst = 18
PHY-1002 : len = 681488, over cnt = 60(0%), over = 114, worst = 8
PHY-1001 : End global iterations;  1.073203s wall, 1.593750s user + 0.062500s system = 1.656250s CPU (154.3%)

PHY-1001 : Congestion index: top1 = 50.82, top5 = 44.76, top10 = 41.84, top15 = 39.81.
OPT-1001 : End congestion update;  1.287790s wall, 1.828125s user + 0.062500s system = 1.890625s CPU (146.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22299 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.767087s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (99.8%)

OPT-0007 : Start: WNS 3769 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.060052s wall, 2.593750s user + 0.062500s system = 2.656250s CPU (128.9%)

OPT-1001 : Current memory(MB): used = 696, reserve = 682, peak = 735.
OPT-1001 : End physical optimization;  11.107538s wall, 12.765625s user + 0.625000s system = 13.390625s CPU (120.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5776 LUT to BLE ...
SYN-4008 : Packed 5776 LUT and 2861 SEQ to BLE.
SYN-4003 : Packing 9767 remaining SEQ's ...
SYN-4005 : Packed 3295 SEQ with LUT/SLICE
SYN-4006 : 123 single LUT's are left
SYN-4006 : 6472 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 12248/13900 primitive instances ...
PHY-3001 : End packing;  2.503508s wall, 2.500000s user + 0.000000s system = 2.500000s CPU (99.9%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8247 instances
RUN-1001 : 4069 mslices, 4070 lslices, 61 pads, 42 brams, 0 dsps
RUN-1001 : There are total 19484 nets
RUN-1001 : 13402 nets have 2 pins
RUN-1001 : 4660 nets have [3 - 5] pins
RUN-1001 : 858 nets have [6 - 10] pins
RUN-1001 : 413 nets have [11 - 20] pins
RUN-1001 : 143 nets have [21 - 99] pins
RUN-1001 : 8 nets have 100+ pins
PHY-3001 : design contains 8245 instances, 8139 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Cell area utilization is 86%
PHY-3001 : After packing: Len = 504622, Over = 382.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7783/19484.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 644640, over cnt = 1667(4%), over = 2601, worst = 9
PHY-1002 : len = 652296, over cnt = 990(2%), over = 1324, worst = 7
PHY-1002 : len = 661856, over cnt = 419(1%), over = 536, worst = 7
PHY-1002 : len = 665584, over cnt = 253(0%), over = 317, worst = 7
PHY-1002 : len = 670320, over cnt = 41(0%), over = 60, worst = 4
PHY-1001 : End global iterations;  1.058422s wall, 1.640625s user + 0.031250s system = 1.671875s CPU (158.0%)

PHY-1001 : Congestion index: top1 = 51.25, top5 = 45.20, top10 = 41.74, top15 = 39.47.
PHY-3001 : End congestion estimation;  1.327657s wall, 1.937500s user + 0.031250s system = 1.968750s CPU (148.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68472, tnet num: 19482, tinst num: 8245, tnode num: 93288, tedge num: 112786.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.554972s wall, 1.546875s user + 0.000000s system = 1.546875s CPU (99.5%)

RUN-1004 : used memory is 593 MB, reserved memory is 586 MB, peak memory is 735 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19482 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.342105s wall, 2.343750s user + 0.000000s system = 2.343750s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.43292e-05
PHY-3002 : Step(177): len = 502730, overlap = 362
PHY-3002 : Step(178): len = 499239, overlap = 368.5
PHY-3002 : Step(179): len = 496771, overlap = 386.75
PHY-3002 : Step(180): len = 496989, overlap = 393.25
PHY-3002 : Step(181): len = 496103, overlap = 404.25
PHY-3002 : Step(182): len = 497645, overlap = 416.5
PHY-3002 : Step(183): len = 494540, overlap = 419.25
PHY-3002 : Step(184): len = 492780, overlap = 413.75
PHY-3002 : Step(185): len = 491348, overlap = 415.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.86583e-05
PHY-3002 : Step(186): len = 496315, overlap = 397.5
PHY-3002 : Step(187): len = 499339, overlap = 390.75
PHY-3002 : Step(188): len = 499610, overlap = 390.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000177317
PHY-3002 : Step(189): len = 509683, overlap = 368.5
PHY-3002 : Step(190): len = 519240, overlap = 354.75
PHY-3002 : Step(191): len = 517085, overlap = 352.75
PHY-3001 : Before Legalized: Len = 517085
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.545815s wall, 0.562500s user + 0.484375s system = 1.046875s CPU (191.8%)

PHY-3001 : After Legalized: Len = 637966, Over = 0
PHY-3001 : Trial Legalized: Len = 637966
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 672/19484.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 736112, over cnt = 2456(6%), over = 3986, worst = 9
PHY-1002 : len = 750600, over cnt = 1550(4%), over = 2177, worst = 5
PHY-1002 : len = 772504, over cnt = 419(1%), over = 551, worst = 5
PHY-1002 : len = 778568, over cnt = 143(0%), over = 189, worst = 5
PHY-1002 : len = 782488, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.675847s wall, 2.656250s user + 0.000000s system = 2.656250s CPU (158.5%)

PHY-1001 : Congestion index: top1 = 53.32, top5 = 47.33, top10 = 44.39, top15 = 42.42.
PHY-3001 : End congestion estimation;  1.965526s wall, 2.953125s user + 0.000000s system = 2.953125s CPU (150.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19482 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.776837s wall, 0.765625s user + 0.015625s system = 0.781250s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000205132
PHY-3002 : Step(192): len = 588667, overlap = 92
PHY-3002 : Step(193): len = 569012, overlap = 129
PHY-3002 : Step(194): len = 555831, overlap = 174.5
PHY-3002 : Step(195): len = 547544, overlap = 214.75
PHY-3002 : Step(196): len = 544114, overlap = 238.75
PHY-3002 : Step(197): len = 541700, overlap = 244.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000410265
PHY-3002 : Step(198): len = 547470, overlap = 240.75
PHY-3002 : Step(199): len = 553974, overlap = 239.25
PHY-3002 : Step(200): len = 553951, overlap = 239.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000787039
PHY-3002 : Step(201): len = 557631, overlap = 231.25
PHY-3002 : Step(202): len = 565005, overlap = 230.75
PHY-3002 : Step(203): len = 568570, overlap = 241
PHY-3002 : Step(204): len = 570027, overlap = 239.75
PHY-3002 : Step(205): len = 571860, overlap = 243
PHY-3001 : Before Legalized: Len = 571860
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.031241s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (100.0%)

PHY-3001 : After Legalized: Len = 612702, Over = 0
PHY-3001 : Legalized: Len = 612702, Over = 0
PHY-3001 : Spreading special nets. 55 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.067029s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (93.2%)

PHY-3001 : 80 instances has been re-located, deltaX = 19, deltaY = 51, maxDist = 2.
PHY-3001 : Final: Len = 613872, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68472, tnet num: 19482, tinst num: 8245, tnode num: 93288, tedge num: 112786.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.735366s wall, 1.734375s user + 0.015625s system = 1.750000s CPU (100.8%)

RUN-1004 : used memory is 611 MB, reserved memory is 623 MB, peak memory is 735 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3551/19484.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 718824, over cnt = 2326(6%), over = 3648, worst = 8
PHY-1002 : len = 729360, over cnt = 1549(4%), over = 2195, worst = 6
PHY-1002 : len = 744304, over cnt = 801(2%), over = 1088, worst = 5
PHY-1002 : len = 752928, over cnt = 434(1%), over = 580, worst = 5
PHY-1002 : len = 762888, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.485351s wall, 2.296875s user + 0.000000s system = 2.296875s CPU (154.6%)

PHY-1001 : Congestion index: top1 = 50.19, top5 = 45.11, top10 = 42.33, top15 = 40.47.
PHY-1001 : End incremental global routing;  1.734610s wall, 2.562500s user + 0.000000s system = 2.562500s CPU (147.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19482 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.032496s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (99.9%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 61 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8179 has valid locations, 11 needs to be replaced
PHY-3001 : design contains 8254 instances, 8148 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 617067
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17650/19492.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 766960, over cnt = 27(0%), over = 28, worst = 2
PHY-1002 : len = 766984, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 767048, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 767064, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 767080, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.606365s wall, 0.593750s user + 0.015625s system = 0.609375s CPU (100.5%)

PHY-1001 : Congestion index: top1 = 50.19, top5 = 45.12, top10 = 42.39, top15 = 40.54.
PHY-3001 : End congestion estimation;  0.856177s wall, 0.843750s user + 0.015625s system = 0.859375s CPU (100.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68546, tnet num: 19490, tinst num: 8254, tnode num: 93377, tedge num: 112880.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.714114s wall, 1.687500s user + 0.031250s system = 1.718750s CPU (100.3%)

RUN-1004 : used memory is 640 MB, reserved memory is 641 MB, peak memory is 735 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19490 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.754441s wall, 2.734375s user + 0.031250s system = 2.765625s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(206): len = 616527, overlap = 0.5
PHY-3002 : Step(207): len = 616321, overlap = 0
PHY-3002 : Step(208): len = 615993, overlap = 0
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17648/19492.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 764640, over cnt = 22(0%), over = 24, worst = 3
PHY-1002 : len = 764640, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 764656, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 764744, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.470587s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (109.6%)

PHY-1001 : Congestion index: top1 = 50.34, top5 = 45.23, top10 = 42.43, top15 = 40.55.
PHY-3001 : End congestion estimation;  0.730799s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (104.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19490 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.800590s wall, 0.796875s user + 0.015625s system = 0.812500s CPU (101.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00122908
PHY-3002 : Step(209): len = 616004, overlap = 1.25
PHY-3002 : Step(210): len = 616006, overlap = 1.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005404s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (289.1%)

PHY-3001 : Legalized: Len = 616126, Over = 0
PHY-3001 : End spreading;  0.061187s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.1%)

PHY-3001 : Final: Len = 616126, Over = 0
PHY-3001 : End incremental placement;  5.856597s wall, 5.953125s user + 0.156250s system = 6.109375s CPU (104.3%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  9.024979s wall, 9.937500s user + 0.156250s system = 10.093750s CPU (111.8%)

OPT-1001 : Current memory(MB): used = 715, reserve = 710, peak = 735.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17646/19492.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 764816, over cnt = 18(0%), over = 26, worst = 3
PHY-1002 : len = 764920, over cnt = 3(0%), over = 4, worst = 2
PHY-1002 : len = 764952, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.341410s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (100.7%)

PHY-1001 : Congestion index: top1 = 50.15, top5 = 45.14, top10 = 42.36, top15 = 40.51.
OPT-1001 : End congestion update;  0.603710s wall, 0.593750s user + 0.000000s system = 0.593750s CPU (98.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19490 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.679877s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (101.1%)

OPT-0007 : Start: WNS 4238 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.287992s wall, 1.296875s user + 0.000000s system = 1.296875s CPU (100.7%)

OPT-1001 : Current memory(MB): used = 715, reserve = 710, peak = 735.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19490 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.685120s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (100.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17659/19492.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 764952, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.104956s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (104.2%)

PHY-1001 : Congestion index: top1 = 50.15, top5 = 45.14, top10 = 42.36, top15 = 40.51.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19490 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.672226s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (102.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4238 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 49.724138
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4238ps with logic level 4 
OPT-1001 : End physical optimization;  13.999864s wall, 15.078125s user + 0.187500s system = 15.265625s CPU (109.0%)

RUN-1003 : finish command "place" in  56.599909s wall, 86.171875s user + 5.968750s system = 92.140625s CPU (162.8%)

RUN-1004 : used memory is 566 MB, reserved memory is 551 MB, peak memory is 735 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.376006s wall, 2.375000s user + 0.031250s system = 2.406250s CPU (174.9%)

RUN-1004 : used memory is 566 MB, reserved memory is 552 MB, peak memory is 735 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.5_SP3/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8256 instances
RUN-1001 : 4078 mslices, 4070 lslices, 61 pads, 42 brams, 0 dsps
RUN-1001 : There are total 19492 nets
RUN-1001 : 13401 nets have 2 pins
RUN-1001 : 4655 nets have [3 - 5] pins
RUN-1001 : 864 nets have [6 - 10] pins
RUN-1001 : 420 nets have [11 - 20] pins
RUN-1001 : 144 nets have [21 - 99] pins
RUN-1001 : 8 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68546, tnet num: 19490, tinst num: 8254, tnode num: 93377, tedge num: 112880.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.537037s wall, 1.531250s user + 0.000000s system = 1.531250s CPU (99.6%)

RUN-1004 : used memory is 589 MB, reserved memory is 595 MB, peak memory is 735 MB
PHY-1001 : 4078 mslices, 4070 lslices, 61 pads, 42 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19490 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 701312, over cnt = 2375(6%), over = 3928, worst = 8
PHY-1002 : len = 715552, over cnt = 1534(4%), over = 2267, worst = 7
PHY-1002 : len = 733176, over cnt = 691(1%), over = 1001, worst = 7
PHY-1002 : len = 749616, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 749920, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.544116s wall, 2.593750s user + 0.031250s system = 2.625000s CPU (170.0%)

PHY-1001 : Congestion index: top1 = 49.89, top5 = 44.82, top10 = 41.85, top15 = 39.99.
PHY-1001 : End global routing;  1.825861s wall, 2.875000s user + 0.031250s system = 2.906250s CPU (159.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 698, reserve = 700, peak = 735.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net DATA/DIV_Dtemp/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 967, reserve = 966, peak = 967.
PHY-1001 : End build detailed router design. 3.995639s wall, 4.000000s user + 0.015625s system = 4.015625s CPU (100.5%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 195552, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.712447s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (98.7%)

PHY-1001 : Current memory(MB): used = 1003, reserve = 1003, peak = 1003.
PHY-1001 : End phase 1; 0.719111s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (99.9%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.80088e+06, over cnt = 1498(0%), over = 1501, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1021, reserve = 1020, peak = 1021.
PHY-1001 : End initial routed; 16.270146s wall, 46.125000s user + 0.546875s system = 46.671875s CPU (286.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18287(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.198   |   0.000   |   0   
RUN-1001 :   Hold   |  -1.926   |  -9.634   |   6   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.018384s wall, 3.015625s user + 0.000000s system = 3.015625s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 1035, reserve = 1034, peak = 1035.
PHY-1001 : End phase 2; 19.288686s wall, 49.140625s user + 0.546875s system = 49.687500s CPU (257.6%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.80088e+06, over cnt = 1498(0%), over = 1501, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.211768s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (95.9%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.78283e+06, over cnt = 463(0%), over = 463, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.821374s wall, 1.296875s user + 0.031250s system = 1.328125s CPU (161.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.78415e+06, over cnt = 95(0%), over = 95, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.320924s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (131.5%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.78502e+06, over cnt = 18(0%), over = 18, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.199995s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (132.8%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.78518e+06, over cnt = 5(0%), over = 5, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.148065s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (105.5%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.78536e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.138504s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (101.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18287(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.198   |   0.000   |   0   
RUN-1001 :   Hold   |  -1.926   |  -9.634   |   6   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 2.985690s wall, 2.984375s user + 0.000000s system = 2.984375s CPU (100.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 405 feed throughs used by 334 nets
PHY-1001 : End commit to database; 1.976360s wall, 1.968750s user + 0.000000s system = 1.968750s CPU (99.6%)

PHY-1001 : Current memory(MB): used = 1122, reserve = 1124, peak = 1122.
PHY-1001 : End phase 3; 7.246471s wall, 7.859375s user + 0.046875s system = 7.906250s CPU (109.1%)

PHY-1003 : Routed, final wirelength = 1.78536e+06
PHY-1001 : Current memory(MB): used = 1126, reserve = 1129, peak = 1126.
PHY-1001 : End export database. 0.055545s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (112.5%)

PHY-1001 : End detail routing;  31.660625s wall, 62.140625s user + 0.609375s system = 62.750000s CPU (198.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68546, tnet num: 19490, tinst num: 8254, tnode num: 93377, tedge num: 112880.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.518885s wall, 1.500000s user + 0.000000s system = 1.500000s CPU (98.8%)

RUN-1004 : used memory is 1058 MB, reserved memory is 1069 MB, peak memory is 1126 MB
RUN-1001 : fix hold violation on net config_inst_syn_2 endpin IMU_CTRL/CtrlData/SPI_FPGAtoSCH634_b[27]_syn_21.sr slack -1926ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net config_inst_syn_2 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_24.sr slack -1717ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net config_inst_syn_2 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_28.sr slack -1514ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net config_inst_syn_2 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_30.sr slack -1514ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net config_inst_syn_2 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_33.sr slack -1279ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net config_inst_syn_2 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_35.sr slack -1684ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68582, tnet num: 19508, tinst num: 8272, tnode num: 93413, tedge num: 112916.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.535038s wall, 1.531250s user + 0.000000s system = 1.531250s CPU (99.8%)

RUN-1004 : used memory is 1129 MB, reserved memory is 1131 MB, peak memory is 1132 MB
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  26.902241s wall, 26.687500s user + 0.171875s system = 26.859375s CPU (99.8%)

RUN-1003 : finish command "route" in  62.714832s wall, 93.984375s user + 0.859375s system = 94.843750s CPU (151.2%)

RUN-1004 : used memory is 1127 MB, reserved memory is 1129 MB, peak memory is 1132 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        67
  #input                   26
  #output                  39
  #inout                    2

Utilization Statistics
#lut                     8762   out of  19600   44.70%
#reg                    12728   out of  19600   64.94%
#le                     15215
  #lut only              2487   out of  15215   16.35%
  #reg only              6453   out of  15215   42.41%
  #lut&reg               6275   out of  15215   41.24%
#dsp                        0   out of     29    0.00%
#bram                      42   out of     64   65.62%
  #bram9k                  42
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       61   out of    188   32.45%
  #ireg                     9
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet              Type               DriverType         Driver                    Fanout
#1        DATA/DIV_Dtemp/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6956
#2        config_inst_syn_9     GCLK               config             config_inst.jtck          187
#3        clk_in_dup_1          GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         L4        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          NONE       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D14        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    GNSS_RMC       OUTPUT         L3        LVCMOS33           8            NONE           NONE       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         T6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT        D16        LVCMOS33           8            NONE           NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        R15        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15215  |7338    |1424    |12770   |42      |0       |
|  AnyFog_dataX                      |AnyFog          |207    |68      |22      |172     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |83     |51      |22      |48      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |199    |68      |22      |165     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |83     |50      |22      |49      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |198    |80      |22      |164     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |78     |53      |22      |44      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |3495   |906     |34      |3418    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |731    |83      |5       |720     |0       |0       |
|    STADOP_com2                     |STADOP          |557    |47      |0       |553     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |46      |14      |42      |0       |0       |
|    head_com2                       |uniheading      |271    |71      |5       |256     |0       |0       |
|    rmc_com2                        |Gprmc           |155    |42      |0       |150     |0       |0       |
|    uart_com2                       |Agrica          |1435   |336     |10      |1416    |0       |0       |
|  DATA                              |Data_Processing |8639   |4394    |1062    |6954    |0       |0       |
|    DIV_Dtemp                       |Divider         |812    |353     |84      |673     |0       |0       |
|    DIV_Utemp                       |Divider         |603    |293     |84      |481     |0       |0       |
|    DIV_accX                        |Divider         |638    |291     |84      |511     |0       |0       |
|    DIV_accY                        |Divider         |694    |328     |111     |524     |0       |0       |
|    DIV_accZ                        |Divider         |702    |382     |132     |498     |0       |0       |
|    DIV_rateX                       |Divider         |596    |355     |132     |393     |0       |0       |
|    DIV_rateY                       |Divider         |584    |366     |132     |381     |0       |0       |
|    DIV_rateZ                       |Divider         |567    |389     |132     |350     |0       |0       |
|    genclk                          |genclk          |77     |50      |20      |44      |0       |0       |
|  FMC                               |FMC_Ctrl        |426    |365     |43      |340     |0       |0       |
|  IIC                               |I2C_master      |297    |237     |11      |265     |0       |0       |
|  IMU_CTRL                          |SCHA634         |922    |725     |61      |720     |0       |0       |
|    CtrlData                        |CtrlData        |512    |462     |47      |331     |0       |0       |
|      usms                          |Time_1ms        |29     |24      |5       |19      |0       |0       |
|    SPIM                            |SPI_SCHA634     |410    |263     |14      |389     |0       |0       |
|  POWER                             |POWER_EN        |97     |49      |38      |36      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |682    |406     |109     |477     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |682    |406     |109     |477     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |313    |162     |0       |297     |0       |0       |
|        reg_inst                    |register        |303    |155     |0       |287     |0       |0       |
|        tap_inst                    |tap             |10     |7       |0       |10      |0       |0       |
|      trigger_inst                  |trigger         |369    |244     |109     |180     |0       |0       |
|        bus_inst                    |bus_top         |149    |97      |52      |59      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |26     |16      |10      |9       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |16     |10      |6       |7       |0       |0       |
|          BUS_DETECTOR[5]$bus_nodes |bus_det         |52     |34      |18      |20      |0       |0       |
|          BUS_DETECTOR[6]$bus_nodes |bus_det         |53     |35      |18      |21      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |139    |104     |29      |89      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13357  
    #2          2       3774   
    #3          3        611   
    #4          4        270   
    #5        5-10       906   
    #6        11-50      507   
    #7       51-100      15    
    #8       101-500      4    
  Average     2.15             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  1.745953s wall, 2.953125s user + 0.046875s system = 3.000000s CPU (171.8%)

RUN-1004 : used memory is 1128 MB, reserved memory is 1129 MB, peak memory is 1182 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68582, tnet num: 19508, tinst num: 8272, tnode num: 93413, tedge num: 112916.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.548606s wall, 1.546875s user + 0.000000s system = 1.546875s CPU (99.9%)

RUN-1004 : used memory is 1127 MB, reserved memory is 1129 MB, peak memory is 1182 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19508 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 3. Number of clock nets = 3 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.260428s wall, 1.250000s user + 0.015625s system = 1.265625s CPU (100.4%)

RUN-1004 : used memory is 1126 MB, reserved memory is 1126 MB, peak memory is 1182 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8272
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19510, pip num: 151781
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 406
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3252 valid insts, and 421396 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011101100001111110010110
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  10.894370s wall, 112.828125s user + 0.140625s system = 112.968750s CPU (1036.9%)

RUN-1004 : used memory is 1222 MB, reserved memory is 1220 MB, peak memory is 1337 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250428_184408.log"

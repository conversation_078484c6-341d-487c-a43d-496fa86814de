============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Tue Jun 17 15:47:28 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(516)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
RUN-1001 : Project manager successfully analyzed 22 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.337780s wall, 1.375000s user + 3.953125s system = 5.328125s CPU (99.8%)

RUN-1004 : used memory is 78 MB, reserved memory is 40 MB, peak memory is 89 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.751429s wall, 1.656250s user + 0.093750s system = 1.750000s CPU (99.9%)

RUN-1004 : used memory is 300 MB, reserved memory is 269 MB, peak memory is 303 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 58 trigger nets, 58 data nets.
KIT-1004 : Chipwatcher code = 1110111010000001
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=158) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=158) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=158)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=158)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 23280/23 useful/useless nets, 19988/12 useful/useless insts
SYN-1016 : Merged 30 instances.
SYN-1032 : 22883/20 useful/useless nets, 20493/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 478 better
SYN-1014 : Optimize round 2
SYN-1032 : 22499/45 useful/useless nets, 20109/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.346514s wall, 2.312500s user + 0.031250s system = 2.343750s CPU (99.9%)

RUN-1004 : used memory is 328 MB, reserved memory is 297 MB, peak memory is 331 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22571/441 useful/useless nets, 20232/63 useful/useless insts
SYN-1016 : Merged 69 instances.
SYN-2571 : Optimize after map_dsp, round 1, 573 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 56 instances.
SYN-2501 : Optimize round 1, 114 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 23075/5 useful/useless nets, 20736/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 85016, tnet num: 23075, tinst num: 20735, tnode num: 119229, tedge num: 132484.
TMR-2508 : Levelizing timing graph completed, there are 85 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.141823s wall, 1.125000s user + 0.015625s system = 1.140625s CPU (99.9%)

RUN-1004 : used memory is 474 MB, reserved memory is 444 MB, peak memory is 474 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 23075 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 282 (3.29), #lev = 7 (1.66)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 282 (3.29), #lev = 7 (1.66)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 614 instances into 282 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 487 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 160 adder to BLE ...
SYN-4008 : Packed 160 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.402160s wall, 4.312500s user + 0.109375s system = 4.421875s CPU (100.4%)

RUN-1004 : used memory is 356 MB, reserved memory is 328 MB, peak memory is 586 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.064610s wall, 6.875000s user + 0.187500s system = 7.062500s CPU (100.0%)

RUN-1004 : used memory is 356 MB, reserved memory is 328 MB, peak memory is 586 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/rmc_com2/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/rmc_com2/GPRMC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/rmc_com2/GPRMC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/rmc_com2/GPRMC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/rmc_com2/GPRMC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/rmc_com2/GPRMC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/rmc_com2/GPRMC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/rmc_com2/GPRMC_data[0]
SYN-5055 WARNING: The kept net COM2/rx_err will be merged to another kept net COM2/rmc_com2/GPRMC_err
SYN-5055 WARNING: The kept net FMC/Error_state[0] will be merged to another kept net COM2/rmc_com2/GPRMC_sub_state_cnt0[3]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (335 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19980 instances
RUN-0007 : 5811 luts, 12607 seqs, 951 mslices, 494 lslices, 60 pads, 52 brams, 0 dsps
RUN-1001 : There are total 22347 nets
RUN-1001 : 16627 nets have 2 pins
RUN-1001 : 4530 nets have [3 - 5] pins
RUN-1001 : 802 nets have [6 - 10] pins
RUN-1001 : 262 nets have [11 - 20] pins
RUN-1001 : 102 nets have [21 - 99] pins
RUN-1001 : 24 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4774     
RUN-1001 :   No   |  No   |  Yes  |     670     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    6556     
RUN-1001 :   Yes  |  No   |  Yes  |     507     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  119  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 127
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19978 instances, 5811 luts, 12607 seqs, 1445 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1131 pins
PHY-3001 : Huge net DATA/done_div with 1644 pins
PHY-0007 : Cell area utilization is 64%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83383, tnet num: 22345, tinst num: 19978, tnode num: 117571, tedge num: 131022.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.193700s wall, 1.156250s user + 0.031250s system = 1.187500s CPU (99.5%)

RUN-1004 : used memory is 535 MB, reserved memory is 508 MB, peak memory is 586 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22345 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.026511s wall, 2.000000s user + 0.031250s system = 2.031250s CPU (100.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.37098e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19978.
PHY-3001 : Level 1 #clusters 2025.
PHY-3001 : End clustering;  0.134868s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (139.0%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 64%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 885773, overlap = 667.125
PHY-3002 : Step(2): len = 813549, overlap = 723.781
PHY-3002 : Step(3): len = 528082, overlap = 890.219
PHY-3002 : Step(4): len = 447274, overlap = 995.656
PHY-3002 : Step(5): len = 367810, overlap = 1065.72
PHY-3002 : Step(6): len = 325423, overlap = 1128.75
PHY-3002 : Step(7): len = 266531, overlap = 1198.19
PHY-3002 : Step(8): len = 238032, overlap = 1268.97
PHY-3002 : Step(9): len = 213888, overlap = 1319.84
PHY-3002 : Step(10): len = 199933, overlap = 1367.81
PHY-3002 : Step(11): len = 182418, overlap = 1423.5
PHY-3002 : Step(12): len = 167588, overlap = 1481.62
PHY-3002 : Step(13): len = 151027, overlap = 1515.81
PHY-3002 : Step(14): len = 137629, overlap = 1541.56
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.01608e-06
PHY-3002 : Step(15): len = 143448, overlap = 1529.22
PHY-3002 : Step(16): len = 179564, overlap = 1430.12
PHY-3002 : Step(17): len = 190905, overlap = 1313.78
PHY-3002 : Step(18): len = 197855, overlap = 1242.44
PHY-3002 : Step(19): len = 195984, overlap = 1214.34
PHY-3002 : Step(20): len = 191837, overlap = 1218
PHY-3002 : Step(21): len = 188242, overlap = 1219.38
PHY-3002 : Step(22): len = 185376, overlap = 1201.97
PHY-3002 : Step(23): len = 180635, overlap = 1200.78
PHY-3002 : Step(24): len = 176722, overlap = 1195
PHY-3002 : Step(25): len = 174586, overlap = 1182.44
PHY-3002 : Step(26): len = 172857, overlap = 1182.38
PHY-3002 : Step(27): len = 172723, overlap = 1191.97
PHY-3002 : Step(28): len = 173287, overlap = 1180.5
PHY-3002 : Step(29): len = 173300, overlap = 1158.75
PHY-3002 : Step(30): len = 171897, overlap = 1166.12
PHY-3002 : Step(31): len = 171651, overlap = 1160
PHY-3002 : Step(32): len = 170247, overlap = 1161.91
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.03215e-06
PHY-3002 : Step(33): len = 180135, overlap = 1087.44
PHY-3002 : Step(34): len = 195271, overlap = 998.062
PHY-3002 : Step(35): len = 199186, overlap = 986.094
PHY-3002 : Step(36): len = 200694, overlap = 969.938
PHY-3002 : Step(37): len = 201554, overlap = 977.438
PHY-3002 : Step(38): len = 200932, overlap = 986.719
PHY-3002 : Step(39): len = 199031, overlap = 982.906
PHY-3002 : Step(40): len = 198163, overlap = 992.906
PHY-3002 : Step(41): len = 196697, overlap = 990.844
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.06431e-06
PHY-3002 : Step(42): len = 207363, overlap = 958.188
PHY-3002 : Step(43): len = 222246, overlap = 870.156
PHY-3002 : Step(44): len = 227197, overlap = 825.688
PHY-3002 : Step(45): len = 229125, overlap = 818.031
PHY-3002 : Step(46): len = 229098, overlap = 818.031
PHY-3002 : Step(47): len = 227610, overlap = 823.281
PHY-3002 : Step(48): len = 224759, overlap = 828.375
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 8.12862e-06
PHY-3002 : Step(49): len = 236440, overlap = 776.562
PHY-3002 : Step(50): len = 248615, overlap = 741.625
PHY-3002 : Step(51): len = 253348, overlap = 681.875
PHY-3002 : Step(52): len = 255293, overlap = 669.531
PHY-3002 : Step(53): len = 254331, overlap = 681.562
PHY-3002 : Step(54): len = 253152, overlap = 702.719
PHY-3002 : Step(55): len = 251104, overlap = 718.344
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.62572e-05
PHY-3002 : Step(56): len = 262202, overlap = 673.656
PHY-3002 : Step(57): len = 276402, overlap = 606.312
PHY-3002 : Step(58): len = 280283, overlap = 550.969
PHY-3002 : Step(59): len = 282412, overlap = 544.094
PHY-3002 : Step(60): len = 281124, overlap = 532.531
PHY-3002 : Step(61): len = 279839, overlap = 543.562
PHY-3002 : Step(62): len = 278563, overlap = 539.125
PHY-3002 : Step(63): len = 278474, overlap = 523.438
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.25145e-05
PHY-3002 : Step(64): len = 286797, overlap = 494.688
PHY-3002 : Step(65): len = 297935, overlap = 456.125
PHY-3002 : Step(66): len = 302813, overlap = 434.094
PHY-3002 : Step(67): len = 305001, overlap = 413.125
PHY-3002 : Step(68): len = 304107, overlap = 400.562
PHY-3002 : Step(69): len = 303272, overlap = 392.406
PHY-3002 : Step(70): len = 302101, overlap = 400.688
PHY-3002 : Step(71): len = 301427, overlap = 408.906
PHY-3002 : Step(72): len = 302247, overlap = 400.656
PHY-3002 : Step(73): len = 302309, overlap = 407.094
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 6.50289e-05
PHY-3002 : Step(74): len = 310093, overlap = 429.625
PHY-3002 : Step(75): len = 319629, overlap = 387.062
PHY-3002 : Step(76): len = 321510, overlap = 367.875
PHY-3002 : Step(77): len = 321910, overlap = 361
PHY-3002 : Step(78): len = 320428, overlap = 339.031
PHY-3002 : Step(79): len = 318943, overlap = 335.625
PHY-3002 : Step(80): len = 316567, overlap = 343.344
PHY-3002 : Step(81): len = 316429, overlap = 305.156
PHY-3002 : Step(82): len = 316470, overlap = 293.969
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000130058
PHY-3002 : Step(83): len = 320738, overlap = 307.75
PHY-3002 : Step(84): len = 327643, overlap = 296.594
PHY-3002 : Step(85): len = 332899, overlap = 283.406
PHY-3002 : Step(86): len = 336164, overlap = 296.875
PHY-3002 : Step(87): len = 333844, overlap = 301.25
PHY-3002 : Step(88): len = 331811, overlap = 300.719
PHY-3002 : Step(89): len = 328619, overlap = 300.031
PHY-3002 : Step(90): len = 327730, overlap = 296.312
PHY-3002 : Step(91): len = 326683, overlap = 315.094
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000218392
PHY-3002 : Step(92): len = 328988, overlap = 313.188
PHY-3002 : Step(93): len = 333174, overlap = 307.906
PHY-3002 : Step(94): len = 335092, overlap = 293.5
PHY-3002 : Step(95): len = 335906, overlap = 296.438
PHY-3002 : Step(96): len = 335765, overlap = 310.25
PHY-3002 : Step(97): len = 336245, overlap = 314.656
PHY-3002 : Step(98): len = 336066, overlap = 314.875
PHY-3002 : Step(99): len = 336590, overlap = 295.031
PHY-3002 : Step(100): len = 338131, overlap = 289.562
PHY-3002 : Step(101): len = 338196, overlap = 290.25
PHY-3002 : Step(102): len = 338210, overlap = 294.406
PHY-3002 : Step(103): len = 337682, overlap = 293.094
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(104): len = 338416, overlap = 291.656
PHY-3002 : Step(105): len = 340183, overlap = 279.906
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007363s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/22347.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 452392, over cnt = 1252(3%), over = 5598, worst = 56
PHY-1001 : End global iterations;  0.802181s wall, 1.015625s user + 0.046875s system = 1.062500s CPU (132.5%)

PHY-1001 : Congestion index: top1 = 75.41, top5 = 53.07, top10 = 43.83, top15 = 38.44.
PHY-3001 : End congestion estimation;  1.043519s wall, 1.265625s user + 0.046875s system = 1.312500s CPU (125.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22345 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.880046s wall, 0.875000s user + 0.000000s system = 0.875000s CPU (99.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.78192e-05
PHY-3002 : Step(106): len = 382794, overlap = 180.812
PHY-3002 : Step(107): len = 401594, overlap = 157.062
PHY-3002 : Step(108): len = 400427, overlap = 139.094
PHY-3002 : Step(109): len = 398986, overlap = 127.188
PHY-3002 : Step(110): len = 404678, overlap = 121.031
PHY-3002 : Step(111): len = 405648, overlap = 116.094
PHY-3002 : Step(112): len = 408256, overlap = 118.375
PHY-3002 : Step(113): len = 414320, overlap = 126.531
PHY-3002 : Step(114): len = 412355, overlap = 132.062
PHY-3002 : Step(115): len = 411894, overlap = 129.281
PHY-3002 : Step(116): len = 414380, overlap = 128.5
PHY-3002 : Step(117): len = 413995, overlap = 130.25
PHY-3002 : Step(118): len = 414682, overlap = 128.406
PHY-3002 : Step(119): len = 417075, overlap = 128.062
PHY-3002 : Step(120): len = 415415, overlap = 126.25
PHY-3002 : Step(121): len = 415390, overlap = 121.812
PHY-3002 : Step(122): len = 416412, overlap = 122.188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000175638
PHY-3002 : Step(123): len = 415746, overlap = 125.031
PHY-3002 : Step(124): len = 416703, overlap = 124.938
PHY-3002 : Step(125): len = 417554, overlap = 123.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000304088
PHY-3002 : Step(126): len = 423846, overlap = 115.656
PHY-3002 : Step(127): len = 430405, overlap = 113.219
PHY-3002 : Step(128): len = 431552, overlap = 113.156
PHY-3002 : Step(129): len = 434216, overlap = 112.531
PHY-3002 : Step(130): len = 433748, overlap = 115.125
PHY-3002 : Step(131): len = 435246, overlap = 118.281
PHY-3002 : Step(132): len = 439131, overlap = 106.188
PHY-3002 : Step(133): len = 441010, overlap = 104.688
PHY-3002 : Step(134): len = 441406, overlap = 101.531
PHY-3002 : Step(135): len = 440894, overlap = 93.7188
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000608175
PHY-3002 : Step(136): len = 440964, overlap = 95.0625
PHY-3002 : Step(137): len = 445116, overlap = 93
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00108123
PHY-3002 : Step(138): len = 446422, overlap = 87.8125
PHY-3002 : Step(139): len = 457098, overlap = 92.5938
PHY-3002 : Step(140): len = 471205, overlap = 90.375
PHY-3002 : Step(141): len = 475320, overlap = 91.2812
PHY-3002 : Step(142): len = 474928, overlap = 95.0938
PHY-3002 : Step(143): len = 474758, overlap = 90.9688
PHY-3002 : Step(144): len = 478543, overlap = 99.5938
PHY-3002 : Step(145): len = 479597, overlap = 108.406
PHY-3002 : Step(146): len = 479426, overlap = 96.7812
PHY-3002 : Step(147): len = 479746, overlap = 105.688
PHY-3002 : Step(148): len = 478979, overlap = 106.438
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(149): len = 478630, overlap = 103.25
PHY-3002 : Step(150): len = 480541, overlap = 104.562
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 88/22347.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 552744, over cnt = 2227(6%), over = 10516, worst = 76
PHY-1001 : End global iterations;  1.037894s wall, 1.671875s user + 0.046875s system = 1.718750s CPU (165.6%)

PHY-1001 : Congestion index: top1 = 84.98, top5 = 62.34, top10 = 52.97, top15 = 47.46.
PHY-3001 : End congestion estimation;  1.305565s wall, 1.906250s user + 0.078125s system = 1.984375s CPU (152.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22345 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.942553s wall, 0.921875s user + 0.015625s system = 0.937500s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000104519
PHY-3002 : Step(151): len = 486859, overlap = 409.094
PHY-3002 : Step(152): len = 488898, overlap = 343.562
PHY-3002 : Step(153): len = 479427, overlap = 311.5
PHY-3002 : Step(154): len = 475047, overlap = 290.75
PHY-3002 : Step(155): len = 470001, overlap = 267.25
PHY-3002 : Step(156): len = 466969, overlap = 247.594
PHY-3002 : Step(157): len = 463108, overlap = 239.781
PHY-3002 : Step(158): len = 459939, overlap = 234.188
PHY-3002 : Step(159): len = 460743, overlap = 233.625
PHY-3002 : Step(160): len = 456881, overlap = 232.375
PHY-3002 : Step(161): len = 454474, overlap = 225.188
PHY-3002 : Step(162): len = 452627, overlap = 221.812
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000209037
PHY-3002 : Step(163): len = 451760, overlap = 213.312
PHY-3002 : Step(164): len = 453402, overlap = 212.188
PHY-3002 : Step(165): len = 454931, overlap = 205.594
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000418075
PHY-3002 : Step(166): len = 458193, overlap = 191.688
PHY-3002 : Step(167): len = 464179, overlap = 188.375
PHY-3002 : Step(168): len = 467254, overlap = 175.094
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000836149
PHY-3002 : Step(169): len = 467673, overlap = 171.062
PHY-3002 : Step(170): len = 470988, overlap = 157.25
PHY-3002 : Step(171): len = 474037, overlap = 158.688
PHY-3002 : Step(172): len = 475427, overlap = 158.312
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00161219
PHY-3002 : Step(173): len = 476009, overlap = 156.25
PHY-3002 : Step(174): len = 478742, overlap = 149.062
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83383, tnet num: 22345, tinst num: 19978, tnode num: 117571, tedge num: 131022.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.446362s wall, 1.437500s user + 0.015625s system = 1.453125s CPU (100.5%)

RUN-1004 : used memory is 575 MB, reserved memory is 551 MB, peak memory is 713 MB
OPT-1001 : Total overflow 531.53 peak overflow 4.53
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 650/22347.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 568000, over cnt = 2638(7%), over = 9308, worst = 24
PHY-1001 : End global iterations;  1.105064s wall, 1.703125s user + 0.046875s system = 1.750000s CPU (158.4%)

PHY-1001 : Congestion index: top1 = 57.41, top5 = 48.48, top10 = 44.05, top15 = 41.06.
PHY-1001 : End incremental global routing;  1.326797s wall, 1.921875s user + 0.046875s system = 1.968750s CPU (148.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22345 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.952100s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (100.1%)

OPT-1001 : 15 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19900 has valid locations, 256 needs to be replaced
PHY-3001 : design contains 20219 instances, 5914 luts, 12745 seqs, 1445 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 495533
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17500/22588.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 582928, over cnt = 2644(7%), over = 9335, worst = 24
PHY-1001 : End global iterations;  0.182239s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (137.2%)

PHY-1001 : Congestion index: top1 = 57.69, top5 = 48.72, top10 = 44.33, top15 = 41.40.
PHY-3001 : End congestion estimation;  0.404303s wall, 0.468750s user + 0.015625s system = 0.484375s CPU (119.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 84195, tnet num: 22586, tinst num: 20219, tnode num: 118722, tedge num: 132164.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.451618s wall, 1.453125s user + 0.000000s system = 1.453125s CPU (100.1%)

RUN-1004 : used memory is 621 MB, reserved memory is 619 MB, peak memory is 713 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22586 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.400631s wall, 2.406250s user + 0.000000s system = 2.406250s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(175): len = 495511, overlap = 0.5
PHY-3002 : Step(176): len = 496101, overlap = 0.5625
PHY-3002 : Step(177): len = 496642, overlap = 0.5625
PHY-3002 : Step(178): len = 497157, overlap = 0.5625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17541/22588.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 580424, over cnt = 2657(7%), over = 9454, worst = 24
PHY-1001 : End global iterations;  0.186061s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (109.2%)

PHY-1001 : Congestion index: top1 = 58.04, top5 = 48.90, top10 = 44.42, top15 = 41.48.
PHY-3001 : End congestion estimation;  0.415526s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (101.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22586 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.958760s wall, 0.953125s user + 0.015625s system = 0.968750s CPU (101.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000654636
PHY-3002 : Step(179): len = 497447, overlap = 151.125
PHY-3002 : Step(180): len = 498295, overlap = 151.031
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00130927
PHY-3002 : Step(181): len = 498505, overlap = 151.031
PHY-3002 : Step(182): len = 498782, overlap = 151.188
PHY-3001 : Final: Len = 498782, Over = 151.188
PHY-3001 : End incremental placement;  4.978729s wall, 5.281250s user + 0.203125s system = 5.484375s CPU (110.2%)

OPT-1001 : Total overflow 537.78 peak overflow 4.53
OPT-1001 : End high-fanout net optimization;  7.751520s wall, 8.734375s user + 0.265625s system = 9.000000s CPU (116.1%)

OPT-1001 : Current memory(MB): used = 716, reserve = 697, peak = 733.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17548/22588.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 582528, over cnt = 2616(7%), over = 8914, worst = 24
PHY-1002 : len = 630520, over cnt = 1777(5%), over = 4271, worst = 20
PHY-1002 : len = 664864, over cnt = 860(2%), over = 1830, worst = 13
PHY-1002 : len = 684032, over cnt = 297(0%), over = 623, worst = 13
PHY-1002 : len = 692944, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.224319s wall, 1.859375s user + 0.000000s system = 1.859375s CPU (151.9%)

PHY-1001 : Congestion index: top1 = 48.94, top5 = 44.17, top10 = 41.41, top15 = 39.54.
OPT-1001 : End congestion update;  1.454142s wall, 2.093750s user + 0.000000s system = 2.093750s CPU (144.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22586 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.804505s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (101.0%)

OPT-0007 : Start: WNS 3853 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.265057s wall, 2.906250s user + 0.000000s system = 2.906250s CPU (128.3%)

OPT-1001 : Current memory(MB): used = 711, reserve = 692, peak = 733.
OPT-1001 : End physical optimization;  11.760841s wall, 13.515625s user + 0.296875s system = 13.812500s CPU (117.4%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5914 LUT to BLE ...
SYN-4008 : Packed 5914 LUT and 2931 SEQ to BLE.
SYN-4003 : Packing 9814 remaining SEQ's ...
SYN-4005 : Packed 3355 SEQ with LUT/SLICE
SYN-4006 : 130 single LUT's are left
SYN-4006 : 6459 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 12373/14127 primitive instances ...
PHY-3001 : End packing;  2.760576s wall, 2.765625s user + 0.000000s system = 2.765625s CPU (100.2%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8337 instances
RUN-1001 : 4110 mslices, 4110 lslices, 60 pads, 52 brams, 0 dsps
RUN-1001 : There are total 19706 nets
RUN-1001 : 13629 nets have 2 pins
RUN-1001 : 4651 nets have [3 - 5] pins
RUN-1001 : 870 nets have [6 - 10] pins
RUN-1001 : 404 nets have [11 - 20] pins
RUN-1001 : 142 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
PHY-3001 : design contains 8335 instances, 8220 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Cell area utilization is 87%
PHY-3001 : After packing: Len = 516417, Over = 376.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 8128/19706.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 657032, over cnt = 1704(4%), over = 2773, worst = 7
PHY-1002 : len = 665208, over cnt = 1129(3%), over = 1567, worst = 7
PHY-1002 : len = 677008, over cnt = 501(1%), over = 635, worst = 7
PHY-1002 : len = 682544, over cnt = 235(0%), over = 321, worst = 7
PHY-1002 : len = 688992, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.253325s wall, 1.968750s user + 0.109375s system = 2.078125s CPU (165.8%)

PHY-1001 : Congestion index: top1 = 51.16, top5 = 44.60, top10 = 41.38, top15 = 39.21.
PHY-3001 : End congestion estimation;  1.543367s wall, 2.265625s user + 0.109375s system = 2.375000s CPU (153.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69556, tnet num: 19704, tinst num: 8335, tnode num: 94669, tedge num: 114588.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.642939s wall, 1.625000s user + 0.015625s system = 1.640625s CPU (99.9%)

RUN-1004 : used memory is 605 MB, reserved memory is 595 MB, peak memory is 733 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19704 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.472364s wall, 2.437500s user + 0.031250s system = 2.468750s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.14168e-05
PHY-3002 : Step(183): len = 518605, overlap = 353.75
PHY-3002 : Step(184): len = 516448, overlap = 379.5
PHY-3002 : Step(185): len = 517559, overlap = 399
PHY-3002 : Step(186): len = 518857, overlap = 405.5
PHY-3002 : Step(187): len = 517842, overlap = 409.25
PHY-3002 : Step(188): len = 518265, overlap = 400.25
PHY-3002 : Step(189): len = 515177, overlap = 397
PHY-3002 : Step(190): len = 512974, overlap = 403
PHY-3002 : Step(191): len = 510891, overlap = 396.25
PHY-3002 : Step(192): len = 509306, overlap = 403.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000102834
PHY-3002 : Step(193): len = 513259, overlap = 386.25
PHY-3002 : Step(194): len = 517520, overlap = 373
PHY-3002 : Step(195): len = 519835, overlap = 368.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(196): len = 524444, overlap = 363
PHY-3002 : Step(197): len = 529279, overlap = 360.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.618187s wall, 0.640625s user + 0.765625s system = 1.406250s CPU (227.5%)

PHY-3001 : Trial Legalized: Len = 649985
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 479/19706.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 744960, over cnt = 2506(7%), over = 4192, worst = 7
PHY-1002 : len = 761248, over cnt = 1497(4%), over = 2187, worst = 6
PHY-1002 : len = 782984, over cnt = 516(1%), over = 689, worst = 5
PHY-1002 : len = 791264, over cnt = 160(0%), over = 191, worst = 3
PHY-1002 : len = 795088, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.850585s wall, 3.171875s user + 0.093750s system = 3.265625s CPU (176.5%)

PHY-1001 : Congestion index: top1 = 51.29, top5 = 46.47, top10 = 43.68, top15 = 41.89.
PHY-3001 : End congestion estimation;  2.181170s wall, 3.500000s user + 0.093750s system = 3.593750s CPU (164.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19704 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.849275s wall, 0.828125s user + 0.015625s system = 0.843750s CPU (99.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000196792
PHY-3002 : Step(198): len = 603272, overlap = 93.5
PHY-3002 : Step(199): len = 583475, overlap = 131.25
PHY-3002 : Step(200): len = 572053, overlap = 160.75
PHY-3002 : Step(201): len = 561871, overlap = 206.25
PHY-3002 : Step(202): len = 556948, overlap = 234.5
PHY-3002 : Step(203): len = 553845, overlap = 251
PHY-3002 : Step(204): len = 552327, overlap = 266
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000393585
PHY-3002 : Step(205): len = 556780, overlap = 267
PHY-3002 : Step(206): len = 561608, overlap = 265.5
PHY-3002 : Step(207): len = 562950, overlap = 259
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000767623
PHY-3002 : Step(208): len = 565605, overlap = 257.5
PHY-3002 : Step(209): len = 572291, overlap = 247.5
PHY-3002 : Step(210): len = 579303, overlap = 251.5
PHY-3002 : Step(211): len = 578207, overlap = 252
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.029109s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (53.7%)

PHY-3001 : Legalized: Len = 620541, Over = 0
PHY-3001 : Spreading special nets. 50 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.078298s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (99.8%)

PHY-3001 : 76 instances has been re-located, deltaX = 29, deltaY = 52, maxDist = 2.
PHY-3001 : Final: Len = 621541, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69556, tnet num: 19704, tinst num: 8335, tnode num: 94669, tedge num: 114588.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.900850s wall, 1.890625s user + 0.015625s system = 1.906250s CPU (100.3%)

RUN-1004 : used memory is 606 MB, reserved memory is 586 MB, peak memory is 733 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3054/19706.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 724496, over cnt = 2355(6%), over = 3739, worst = 8
PHY-1002 : len = 737192, over cnt = 1446(4%), over = 1989, worst = 8
PHY-1002 : len = 752504, over cnt = 637(1%), over = 859, worst = 8
PHY-1002 : len = 761952, over cnt = 170(0%), over = 241, worst = 4
PHY-1002 : len = 765584, over cnt = 34(0%), over = 46, worst = 3
PHY-1001 : End global iterations;  1.607537s wall, 2.734375s user + 0.062500s system = 2.796875s CPU (174.0%)

PHY-1001 : Congestion index: top1 = 47.74, top5 = 43.56, top10 = 41.23, top15 = 39.60.
PHY-1001 : End incremental global routing;  1.891431s wall, 3.015625s user + 0.062500s system = 3.078125s CPU (162.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19704 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.958114s wall, 0.937500s user + 0.015625s system = 0.953125s CPU (99.5%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8270 has valid locations, 16 needs to be replaced
PHY-3001 : design contains 8349 instances, 8234 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 624996
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17754/19718.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 770784, over cnt = 68(0%), over = 82, worst = 3
PHY-1002 : len = 770776, over cnt = 42(0%), over = 42, worst = 1
PHY-1002 : len = 771184, over cnt = 12(0%), over = 12, worst = 1
PHY-1002 : len = 771320, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 771544, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.677370s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (106.1%)

PHY-1001 : Congestion index: top1 = 47.67, top5 = 43.73, top10 = 41.39, top15 = 39.77.
PHY-3001 : End congestion estimation;  0.951213s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (105.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69655, tnet num: 19716, tinst num: 8349, tnode num: 94784, tedge num: 114703.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.875016s wall, 1.875000s user + 0.000000s system = 1.875000s CPU (100.0%)

RUN-1004 : used memory is 655 MB, reserved memory is 647 MB, peak memory is 733 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19716 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.732813s wall, 2.718750s user + 0.015625s system = 2.734375s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(212): len = 624550, overlap = 1
PHY-3002 : Step(213): len = 624220, overlap = 1.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17753/19718.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 769536, over cnt = 28(0%), over = 45, worst = 5
PHY-1002 : len = 769512, over cnt = 27(0%), over = 29, worst = 2
PHY-1002 : len = 769768, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 769880, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 769944, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.671857s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (102.3%)

PHY-1001 : Congestion index: top1 = 48.08, top5 = 43.86, top10 = 41.44, top15 = 39.79.
PHY-3001 : End congestion estimation;  0.960353s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (100.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19716 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.814149s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (96.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00109563
PHY-3002 : Step(214): len = 624207, overlap = 2.25
PHY-3002 : Step(215): len = 624241, overlap = 2.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005889s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 624499, Over = 0
PHY-3001 : End spreading;  0.063898s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.8%)

PHY-3001 : Final: Len = 624499, Over = 0
PHY-3001 : End incremental placement;  6.065887s wall, 6.000000s user + 0.062500s system = 6.062500s CPU (99.9%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  9.999835s wall, 11.187500s user + 0.140625s system = 11.328125s CPU (113.3%)

OPT-1001 : Current memory(MB): used = 729, reserve = 715, peak = 733.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17750/19718.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 770104, over cnt = 29(0%), over = 34, worst = 3
PHY-1002 : len = 769984, over cnt = 18(0%), over = 18, worst = 1
PHY-1002 : len = 770104, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 770232, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 770344, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.655816s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (109.6%)

PHY-1001 : Congestion index: top1 = 48.25, top5 = 43.91, top10 = 41.47, top15 = 39.79.
OPT-1001 : End congestion update;  0.928122s wall, 0.984375s user + 0.000000s system = 0.984375s CPU (106.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19716 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.703094s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (100.0%)

OPT-0007 : Start: WNS 3863 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.635694s wall, 1.687500s user + 0.000000s system = 1.687500s CPU (103.2%)

OPT-1001 : Current memory(MB): used = 728, reserve = 714, peak = 733.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19716 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.701186s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (100.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17770/19718.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 770344, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.111965s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (97.7%)

PHY-1001 : Congestion index: top1 = 48.25, top5 = 43.91, top10 = 41.47, top15 = 39.79.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19716 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.726216s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (99.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3863 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 47.827586
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3863ps with logic level 4 
RUN-1001 :       #2 path slack 3870ps with logic level 4 
OPT-1001 : End physical optimization;  15.597732s wall, 16.906250s user + 0.171875s system = 17.078125s CPU (109.5%)

RUN-1003 : finish command "place" in  72.516327s wall, 130.062500s user + 7.718750s system = 137.781250s CPU (190.0%)

RUN-1004 : used memory is 607 MB, reserved memory is 602 MB, peak memory is 733 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.559358s wall, 2.750000s user + 0.000000s system = 2.750000s CPU (176.4%)

RUN-1004 : used memory is 607 MB, reserved memory is 603 MB, peak memory is 733 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8351 instances
RUN-1001 : 4124 mslices, 4110 lslices, 60 pads, 52 brams, 0 dsps
RUN-1001 : There are total 19718 nets
RUN-1001 : 13628 nets have 2 pins
RUN-1001 : 4650 nets have [3 - 5] pins
RUN-1001 : 876 nets have [6 - 10] pins
RUN-1001 : 412 nets have [11 - 20] pins
RUN-1001 : 142 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69655, tnet num: 19716, tinst num: 8349, tnode num: 94784, tedge num: 114703.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.655030s wall, 1.656250s user + 0.000000s system = 1.656250s CPU (100.1%)

RUN-1004 : used memory is 599 MB, reserved memory is 595 MB, peak memory is 733 MB
PHY-1001 : 4124 mslices, 4110 lslices, 60 pads, 52 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19716 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 710056, over cnt = 2442(6%), over = 4151, worst = 9
PHY-1002 : len = 727040, over cnt = 1491(4%), over = 2188, worst = 8
PHY-1002 : len = 742536, over cnt = 690(1%), over = 987, worst = 6
PHY-1002 : len = 759456, over cnt = 26(0%), over = 30, worst = 3
PHY-1002 : len = 759984, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.644933s wall, 2.718750s user + 0.046875s system = 2.765625s CPU (168.1%)

PHY-1001 : Congestion index: top1 = 48.34, top5 = 43.36, top10 = 40.94, top15 = 39.34.
PHY-1001 : End global routing;  1.956072s wall, 3.031250s user + 0.046875s system = 3.078125s CPU (157.4%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 708, reserve = 700, peak = 733.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 979, reserve = 969, peak = 979.
PHY-1001 : End build detailed router design. 4.298424s wall, 4.203125s user + 0.078125s system = 4.281250s CPU (99.6%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 196832, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.788064s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (99.1%)

PHY-1001 : Current memory(MB): used = 1016, reserve = 1007, peak = 1016.
PHY-1001 : End phase 1; 0.794813s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (100.3%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 56% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.85089e+06, over cnt = 1383(0%), over = 1388, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1032, reserve = 1022, peak = 1032.
PHY-1001 : End initial routed; 25.671620s wall, 55.062500s user + 0.296875s system = 55.359375s CPU (215.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18494(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.495   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.215790s wall, 3.218750s user + 0.000000s system = 3.218750s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1047, reserve = 1038, peak = 1047.
PHY-1001 : End phase 2; 28.887548s wall, 58.281250s user + 0.296875s system = 58.578125s CPU (202.8%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.85089e+06, over cnt = 1383(0%), over = 1388, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.226224s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (96.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.84095e+06, over cnt = 503(0%), over = 504, worst = 2, crit = 0
PHY-1001 : End DR Iter 1; 0.854285s wall, 1.421875s user + 0.000000s system = 1.421875s CPU (166.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.8405e+06, over cnt = 108(0%), over = 108, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.439642s wall, 0.671875s user + 0.031250s system = 0.703125s CPU (159.9%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.84053e+06, over cnt = 17(0%), over = 17, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.311447s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (115.4%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.8408e+06, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.158922s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (108.2%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.84082e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.259933s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (102.2%)

PHY-1001 : ===== DR Iter 6 =====
PHY-1022 : len = 1.84082e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 6; 0.390649s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (100.0%)

PHY-1001 : ===== DR Iter 7 =====
PHY-1022 : len = 1.84082e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 7; 0.524233s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (98.4%)

PHY-1001 : ===== DR Iter 8 =====
PHY-1022 : len = 1.84084e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 8; 0.155307s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (100.6%)

PHY-1001 : ==== DR Iter 9 ====
PHY-1022 : len = 1.84082e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 9; 0.157163s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (99.4%)

PHY-1001 : ==== DR Iter 10 ====
PHY-1022 : len = 1.84081e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 10; 0.201220s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (100.9%)

PHY-1001 : ==== DR Iter 11 ====
PHY-1022 : len = 1.84081e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 11; 0.164322s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (104.6%)

PHY-1001 : ==== DR Iter 12 ====
PHY-1022 : len = 1.84082e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 12; 0.143544s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (98.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18494(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.208   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.344627s wall, 3.343750s user + 0.000000s system = 3.343750s CPU (100.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 326 feed throughs used by 285 nets
PHY-1001 : End commit to database; 2.192223s wall, 2.125000s user + 0.062500s system = 2.187500s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 1136, reserve = 1129, peak = 1136.
PHY-1001 : End phase 3; 10.028338s wall, 10.812500s user + 0.093750s system = 10.906250s CPU (108.8%)

PHY-1003 : Routed, final wirelength = 1.84082e+06
PHY-1001 : Current memory(MB): used = 1140, reserve = 1133, peak = 1140.
PHY-1001 : End export database. 0.150955s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (93.2%)

PHY-1001 : End detail routing;  44.545314s wall, 74.640625s user + 0.468750s system = 75.109375s CPU (168.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69655, tnet num: 19716, tinst num: 8349, tnode num: 94784, tedge num: 114703.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.618937s wall, 1.609375s user + 0.000000s system = 1.609375s CPU (99.4%)

RUN-1004 : used memory is 1070 MB, reserved memory is 1068 MB, peak memory is 1140 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  52.199269s wall, 83.343750s user + 0.546875s system = 83.890625s CPU (160.7%)

RUN-1004 : used memory is 1069 MB, reserved memory is 1069 MB, peak memory is 1140 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8979   out of  19600   45.81%
#reg                    12843   out of  19600   65.53%
#le                     15405
  #lut only              2562   out of  15405   16.63%
  #reg only              6426   out of  15405   41.71%
  #lut&reg               6417   out of  15405   41.66%
#dsp                        0   out of     29    0.00%
#bram                      52   out of     64   81.25%
  #bram9k                  52
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                    10
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    7040
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          191
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         M3        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         P6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15405  |7534    |1445    |12886   |52      |0       |
|  AnyFog_dataX                      |AnyFog          |230    |86      |22      |187     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |91     |60      |22      |52      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |222    |122     |22      |183     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |87     |65      |22      |52      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |231    |120     |22      |188     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |58      |22      |52      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |3388   |851     |34      |3295    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |740    |90      |5       |725     |0       |0       |
|    STADOP_com2                     |STADOP          |566    |56      |0       |554     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |69     |54      |14      |44      |0       |0       |
|    head_com2                       |uniheading      |261    |61      |5       |247     |0       |0       |
|    rmc_com2                        |Gprmc           |32     |32      |0       |30      |0       |0       |
|    uart_com2                       |Agrica          |1432   |270     |10      |1407    |0       |0       |
|  COM3                              |COM3_Control    |219    |125     |14      |183     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |60     |42      |14      |36      |0       |0       |
|    rmc_com3                        |Gprmc           |159    |83      |0       |147     |0       |0       |
|  DATA                              |Data_Processing |8634   |4406    |1059    |6926    |0       |0       |
|    DIV_Dtemp                       |Divider         |838    |344     |84      |705     |0       |0       |
|    DIV_Utemp                       |Divider         |640    |336     |84      |515     |0       |0       |
|    DIV_accX                        |Divider         |554    |290     |84      |420     |0       |0       |
|    DIV_accY                        |Divider         |684    |345     |108     |499     |0       |0       |
|    DIV_accZ                        |Divider         |716    |358     |132     |509     |0       |0       |
|    DIV_rateX                       |Divider         |670    |380     |132     |462     |0       |0       |
|    DIV_rateY                       |Divider         |562    |340     |132     |351     |0       |0       |
|    DIV_rateZ                       |Divider         |568    |358     |132     |363     |0       |0       |
|    genclk                          |genclk          |83     |52      |20      |50      |0       |0       |
|  FMC                               |FMC_Ctrl        |471    |406     |43      |353     |0       |0       |
|  IIC                               |I2C_master      |277    |203     |11      |251     |0       |0       |
|  IMU_CTRL                          |SCHA634         |886    |666     |61      |715     |0       |0       |
|    CtrlData                        |CtrlData        |469    |413     |47      |331     |0       |0       |
|      usms                          |Time_1ms        |29     |24      |5       |19      |0       |0       |
|    SPIM                            |SPI_SCHA634     |417    |253     |14      |384     |0       |0       |
|  POWER                             |POWER_EN        |96     |51      |38      |36      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |732    |492     |119     |507     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |732    |492     |119     |507     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |326    |218     |0       |310     |0       |0       |
|        reg_inst                    |register        |323    |215     |0       |307     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |406    |274     |119     |197     |0       |0       |
|        bus_inst                    |bus_top         |181    |119     |62      |68      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |29     |19      |10      |12      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |50     |32      |18      |18      |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |98     |64      |34      |34      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |147    |107     |29      |98      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13567  
    #2          2       3634   
    #3          3        707   
    #4          4        309   
    #5        5-10       964   
    #6        11-50      441   
    #7       51-100      25    
    #8       101-500      4    
    #9        >500        2    
  Average     2.17             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  1.921806s wall, 3.312500s user + 0.062500s system = 3.375000s CPU (175.6%)

RUN-1004 : used memory is 1070 MB, reserved memory is 1070 MB, peak memory is 1140 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69655, tnet num: 19716, tinst num: 8349, tnode num: 94784, tedge num: 114703.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.648755s wall, 1.640625s user + 0.000000s system = 1.640625s CPU (99.5%)

RUN-1004 : used memory is 1072 MB, reserved memory is 1071 MB, peak memory is 1140 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19716 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.300862s wall, 1.281250s user + 0.000000s system = 1.281250s CPU (98.5%)

RUN-1004 : used memory is 1077 MB, reserved memory is 1076 MB, peak memory is 1140 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 28b752c1c0e7d7ba92ac5beed3c46754b7b56ac64969e4b0149cfdbc1d295011 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8349
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19718, pip num: 153585
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 326
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3266 valid insts, and 426922 bits set as '1'.
BIT-1004 : the usercode register value: 00000000000000001110111010000001
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  10.271002s wall, 102.406250s user + 0.156250s system = 102.562500s CPU (998.6%)

RUN-1004 : used memory is 1202 MB, reserved memory is 1190 MB, peak memory is 1317 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250617_154728.log"

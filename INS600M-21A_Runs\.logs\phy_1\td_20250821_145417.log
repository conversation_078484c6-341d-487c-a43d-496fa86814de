============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Aug 21 14:54:17 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.810027s wall, 1.640625s user + 4.171875s system = 5.812500s CPU (100.0%)

RUN-1004 : used memory is 86 MB, reserved memory is 41 MB, peak memory is 98 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.948096s wall, 1.812500s user + 0.140625s system = 1.953125s CPU (100.3%)

RUN-1004 : used memory is 309 MB, reserved memory is 270 MB, peak memory is 312 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 6 view nodes, 43 trigger nets, 43 data nets.
KIT-1004 : Chipwatcher code = 0011101000100101
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100,32'sb01001010}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=132) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=132) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100,32'sb01001010}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100,32'sb01001010}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100,32'sb01001010})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=132)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=132)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100,32'sb01001010})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100,32'sb01001010})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22852/26 useful/useless nets, 19575/15 useful/useless insts
SYN-1016 : Merged 33 instances.
SYN-1032 : 22504/22 useful/useless nets, 20010/18 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 412 better
SYN-1014 : Optimize round 2
SYN-1032 : 22173/45 useful/useless nets, 19679/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.631842s wall, 2.609375s user + 0.015625s system = 2.625000s CPU (99.7%)

RUN-1004 : used memory is 331 MB, reserved memory is 295 MB, peak memory is 333 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22221/300 useful/useless nets, 19765/48 useful/useless insts
SYN-1016 : Merged 45 instances.
SYN-2571 : Optimize after map_dsp, round 1, 393 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 40 instances.
SYN-2501 : Optimize round 1, 82 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 22675/5 useful/useless nets, 20219/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82769, tnet num: 22675, tinst num: 20218, tnode num: 115891, tedge num: 129386.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.313647s wall, 1.281250s user + 0.046875s system = 1.328125s CPU (101.1%)

RUN-1004 : used memory is 472 MB, reserved memory is 438 MB, peak memory is 472 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22675 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 240 (3.43), #lev = 7 (1.80)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 240 (3.43), #lev = 7 (1.80)
SYN-3001 : Logic optimization runtime opt =   0.04 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 559 instances into 240 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 421 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 128 adder to BLE ...
SYN-4008 : Packed 128 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.967132s wall, 4.859375s user + 0.093750s system = 4.953125s CPU (99.7%)

RUN-1004 : used memory is 354 MB, reserved memory is 321 MB, peak memory is 579 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.957796s wall, 7.843750s user + 0.109375s system = 7.953125s CPU (99.9%)

RUN-1004 : used memory is 354 MB, reserved memory is 322 MB, peak memory is 579 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (282 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19440 instances
RUN-0007 : 5620 luts, 12213 seqs, 983 mslices, 519 lslices, 59 pads, 41 brams, 0 dsps
RUN-1001 : There are total 21904 nets
RUN-1001 : 16434 nets have 2 pins
RUN-1001 : 4272 nets have [3 - 5] pins
RUN-1001 : 821 nets have [6 - 10] pins
RUN-1001 : 251 nets have [11 - 20] pins
RUN-1001 : 104 nets have [21 - 99] pins
RUN-1001 : 22 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4783     
RUN-1001 :   No   |  No   |  Yes  |     707     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     454     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19438 instances, 5620 luts, 12213 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81093, tnet num: 21902, tinst num: 19438, tnode num: 114020, tedge num: 127654.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.293782s wall, 1.281250s user + 0.015625s system = 1.296875s CPU (100.2%)

RUN-1004 : used memory is 530 MB, reserved memory is 500 MB, peak memory is 579 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21902 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.251696s wall, 2.203125s user + 0.046875s system = 2.250000s CPU (99.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.66403e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19438.
PHY-3001 : Level 1 #clusters 2199.
PHY-3001 : End clustering;  0.164953s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (189.4%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 894482, overlap = 647
PHY-3002 : Step(2): len = 808466, overlap = 701.125
PHY-3002 : Step(3): len = 519574, overlap = 922.188
PHY-3002 : Step(4): len = 466792, overlap = 1003.53
PHY-3002 : Step(5): len = 366289, overlap = 1081.5
PHY-3002 : Step(6): len = 329937, overlap = 1127.09
PHY-3002 : Step(7): len = 276019, overlap = 1194
PHY-3002 : Step(8): len = 250737, overlap = 1236.78
PHY-3002 : Step(9): len = 212705, overlap = 1282.91
PHY-3002 : Step(10): len = 195838, overlap = 1299.19
PHY-3002 : Step(11): len = 173648, overlap = 1338.31
PHY-3002 : Step(12): len = 161796, overlap = 1375.5
PHY-3002 : Step(13): len = 150434, overlap = 1390.69
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.48666e-06
PHY-3002 : Step(14): len = 160601, overlap = 1376.69
PHY-3002 : Step(15): len = 208211, overlap = 1223.34
PHY-3002 : Step(16): len = 216473, overlap = 1114.5
PHY-3002 : Step(17): len = 221077, overlap = 1040.78
PHY-3002 : Step(18): len = 211982, overlap = 1002.88
PHY-3002 : Step(19): len = 207636, overlap = 1000.97
PHY-3002 : Step(20): len = 200470, overlap = 1005.84
PHY-3002 : Step(21): len = 195565, overlap = 1000.88
PHY-3002 : Step(22): len = 188716, overlap = 1000.09
PHY-3002 : Step(23): len = 186154, overlap = 991.062
PHY-3002 : Step(24): len = 183843, overlap = 984.781
PHY-3002 : Step(25): len = 182806, overlap = 986.062
PHY-3002 : Step(26): len = 182141, overlap = 975.812
PHY-3002 : Step(27): len = 181617, overlap = 975.531
PHY-3002 : Step(28): len = 179630, overlap = 982.875
PHY-3002 : Step(29): len = 178872, overlap = 980
PHY-3002 : Step(30): len = 178287, overlap = 987.531
PHY-3002 : Step(31): len = 178490, overlap = 966.719
PHY-3002 : Step(32): len = 176962, overlap = 967.531
PHY-3002 : Step(33): len = 176114, overlap = 978.188
PHY-3002 : Step(34): len = 174267, overlap = 987.156
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.97333e-06
PHY-3002 : Step(35): len = 183174, overlap = 969.812
PHY-3002 : Step(36): len = 198611, overlap = 909.75
PHY-3002 : Step(37): len = 201423, overlap = 880.062
PHY-3002 : Step(38): len = 203439, overlap = 877.875
PHY-3002 : Step(39): len = 203620, overlap = 877.844
PHY-3002 : Step(40): len = 203103, overlap = 866.938
PHY-3002 : Step(41): len = 201146, overlap = 866.188
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 5.94665e-06
PHY-3002 : Step(42): len = 212811, overlap = 837.781
PHY-3002 : Step(43): len = 227037, overlap = 775.906
PHY-3002 : Step(44): len = 232041, overlap = 731.312
PHY-3002 : Step(45): len = 233022, overlap = 724.406
PHY-3002 : Step(46): len = 233062, overlap = 711
PHY-3002 : Step(47): len = 231266, overlap = 713.531
PHY-3002 : Step(48): len = 229729, overlap = 706.656
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 1.18933e-05
PHY-3002 : Step(49): len = 243064, overlap = 649.125
PHY-3002 : Step(50): len = 257220, overlap = 570.219
PHY-3002 : Step(51): len = 262396, overlap = 550.344
PHY-3002 : Step(52): len = 264540, overlap = 534.844
PHY-3002 : Step(53): len = 263576, overlap = 525.625
PHY-3002 : Step(54): len = 262777, overlap = 530.406
PHY-3002 : Step(55): len = 260284, overlap = 539.281
PHY-3002 : Step(56): len = 259349, overlap = 529.312
PHY-3002 : Step(57): len = 258817, overlap = 529.062
PHY-3002 : Step(58): len = 259015, overlap = 529.625
PHY-3002 : Step(59): len = 257213, overlap = 536.281
PHY-3002 : Step(60): len = 256992, overlap = 541.469
PHY-3002 : Step(61): len = 255701, overlap = 535.25
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 2.37866e-05
PHY-3002 : Step(62): len = 269294, overlap = 495.469
PHY-3002 : Step(63): len = 283138, overlap = 454.781
PHY-3002 : Step(64): len = 285851, overlap = 437.781
PHY-3002 : Step(65): len = 285748, overlap = 429.688
PHY-3002 : Step(66): len = 284765, overlap = 437.406
PHY-3002 : Step(67): len = 282602, overlap = 436.688
PHY-3002 : Step(68): len = 280619, overlap = 452.031
PHY-3002 : Step(69): len = 278559, overlap = 449.031
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 4.75732e-05
PHY-3002 : Step(70): len = 287755, overlap = 438.75
PHY-3002 : Step(71): len = 297240, overlap = 425.969
PHY-3002 : Step(72): len = 299899, overlap = 419.312
PHY-3002 : Step(73): len = 301075, overlap = 433.594
PHY-3002 : Step(74): len = 301069, overlap = 423.781
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 9.51465e-05
PHY-3002 : Step(75): len = 307445, overlap = 371.219
PHY-3002 : Step(76): len = 316122, overlap = 321.969
PHY-3002 : Step(77): len = 320362, overlap = 303.531
PHY-3002 : Step(78): len = 322305, overlap = 296.281
PHY-3002 : Step(79): len = 320778, overlap = 283.438
PHY-3002 : Step(80): len = 319188, overlap = 280.562
PHY-3002 : Step(81): len = 316822, overlap = 273.438
PHY-3002 : Step(82): len = 315258, overlap = 274.344
PHY-3002 : Step(83): len = 314782, overlap = 267.375
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000190293
PHY-3002 : Step(84): len = 319391, overlap = 245.844
PHY-3002 : Step(85): len = 324453, overlap = 247.844
PHY-3002 : Step(86): len = 326198, overlap = 250.812
PHY-3002 : Step(87): len = 327908, overlap = 253.344
PHY-3002 : Step(88): len = 327418, overlap = 244.812
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000365304
PHY-3002 : Step(89): len = 329290, overlap = 242.781
PHY-3002 : Step(90): len = 332838, overlap = 238.906
PHY-3002 : Step(91): len = 334210, overlap = 231.125
PHY-3002 : Step(92): len = 334772, overlap = 220.344
PHY-3002 : Step(93): len = 334726, overlap = 220.906
PHY-3002 : Step(94): len = 334705, overlap = 228.875
PHY-3002 : Step(95): len = 334998, overlap = 235.094
PHY-3002 : Step(96): len = 334738, overlap = 221.281
PHY-3002 : Step(97): len = 334103, overlap = 240.625
PHY-3002 : Step(98): len = 334108, overlap = 254.562
PHY-3002 : Step(99): len = 333926, overlap = 252.312
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.011091s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21904.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 440336, over cnt = 1208(3%), over = 5473, worst = 55
PHY-1001 : End global iterations;  0.945176s wall, 1.312500s user + 0.062500s system = 1.375000s CPU (145.5%)

PHY-1001 : Congestion index: top1 = 75.56, top5 = 53.63, top10 = 43.34, top15 = 37.59.
PHY-3001 : End congestion estimation;  1.264730s wall, 1.609375s user + 0.078125s system = 1.687500s CPU (133.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21902 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.023659s wall, 0.984375s user + 0.031250s system = 1.015625s CPU (99.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.70163e-05
PHY-3002 : Step(100): len = 376827, overlap = 189.938
PHY-3002 : Step(101): len = 388433, overlap = 175.188
PHY-3002 : Step(102): len = 390591, overlap = 165.156
PHY-3002 : Step(103): len = 390412, overlap = 173.25
PHY-3002 : Step(104): len = 393921, overlap = 166.812
PHY-3002 : Step(105): len = 400769, overlap = 163.062
PHY-3002 : Step(106): len = 406447, overlap = 167.719
PHY-3002 : Step(107): len = 412913, overlap = 168.062
PHY-3002 : Step(108): len = 415522, overlap = 166.844
PHY-3002 : Step(109): len = 416798, overlap = 163.531
PHY-3002 : Step(110): len = 418793, overlap = 160.969
PHY-3002 : Step(111): len = 421614, overlap = 151.062
PHY-3002 : Step(112): len = 423552, overlap = 151.062
PHY-3002 : Step(113): len = 425115, overlap = 142.75
PHY-3002 : Step(114): len = 425705, overlap = 139.812
PHY-3002 : Step(115): len = 427523, overlap = 134.219
PHY-3002 : Step(116): len = 430647, overlap = 129.781
PHY-3002 : Step(117): len = 430758, overlap = 126.469
PHY-3002 : Step(118): len = 432071, overlap = 123.031
PHY-3002 : Step(119): len = 432233, overlap = 121.219
PHY-3002 : Step(120): len = 431657, overlap = 116.75
PHY-3002 : Step(121): len = 432504, overlap = 115
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000194033
PHY-3002 : Step(122): len = 432372, overlap = 116.156
PHY-3002 : Step(123): len = 433510, overlap = 118.938
PHY-3002 : Step(124): len = 436109, overlap = 116.594
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(125): len = 442610, overlap = 105.062
PHY-3002 : Step(126): len = 448269, overlap = 105.312
PHY-3002 : Step(127): len = 451784, overlap = 98.7188
PHY-3002 : Step(128): len = 453232, overlap = 96.5
PHY-3002 : Step(129): len = 454553, overlap = 98.375
PHY-3002 : Step(130): len = 455781, overlap = 91.5312
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 72/21904.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 522696, over cnt = 2194(6%), over = 10244, worst = 50
PHY-1001 : End global iterations;  1.236473s wall, 1.984375s user + 0.015625s system = 2.000000s CPU (161.8%)

PHY-1001 : Congestion index: top1 = 77.39, top5 = 58.37, top10 = 50.31, top15 = 45.30.
PHY-3001 : End congestion estimation;  1.554090s wall, 2.296875s user + 0.015625s system = 2.312500s CPU (148.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21902 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.042189s wall, 1.046875s user + 0.000000s system = 1.046875s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000102028
PHY-3002 : Step(131): len = 463991, overlap = 400.188
PHY-3002 : Step(132): len = 471157, overlap = 315.938
PHY-3002 : Step(133): len = 466543, overlap = 285.344
PHY-3002 : Step(134): len = 461914, overlap = 270.5
PHY-3002 : Step(135): len = 460075, overlap = 247.469
PHY-3002 : Step(136): len = 458599, overlap = 235.344
PHY-3002 : Step(137): len = 455618, overlap = 223.469
PHY-3002 : Step(138): len = 454861, overlap = 210.062
PHY-3002 : Step(139): len = 453416, overlap = 203.25
PHY-3002 : Step(140): len = 453287, overlap = 196.656
PHY-3002 : Step(141): len = 451921, overlap = 196.062
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000204057
PHY-3002 : Step(142): len = 453154, overlap = 187.906
PHY-3002 : Step(143): len = 454430, overlap = 186.344
PHY-3002 : Step(144): len = 454851, overlap = 178.625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000408114
PHY-3002 : Step(145): len = 458596, overlap = 169.156
PHY-3002 : Step(146): len = 466315, overlap = 158.594
PHY-3002 : Step(147): len = 467707, overlap = 152.688
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00076804
PHY-3002 : Step(148): len = 469155, overlap = 151.5
PHY-3002 : Step(149): len = 477249, overlap = 143.094
PHY-3002 : Step(150): len = 480860, overlap = 133.594
PHY-3002 : Step(151): len = 482418, overlap = 130.812
PHY-3002 : Step(152): len = 483126, overlap = 134
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00145549
PHY-3002 : Step(153): len = 483931, overlap = 129.219
PHY-3002 : Step(154): len = 486403, overlap = 128.531
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81093, tnet num: 21902, tinst num: 19438, tnode num: 114020, tedge num: 127654.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.661639s wall, 1.640625s user + 0.015625s system = 1.656250s CPU (99.7%)

RUN-1004 : used memory is 570 MB, reserved memory is 544 MB, peak memory is 704 MB
OPT-1001 : Total overflow 491.09 peak overflow 4.31
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 687/21904.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 569848, over cnt = 2573(7%), over = 8947, worst = 24
PHY-1001 : End global iterations;  1.344791s wall, 2.078125s user + 0.000000s system = 2.078125s CPU (154.5%)

PHY-1001 : Congestion index: top1 = 60.78, top5 = 49.04, top10 = 43.83, top15 = 40.76.
PHY-1001 : End incremental global routing;  1.612914s wall, 2.343750s user + 0.000000s system = 2.343750s CPU (145.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21902 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.117944s wall, 1.125000s user + 0.000000s system = 1.125000s CPU (100.6%)

OPT-1001 : 17 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19359 has valid locations, 245 needs to be replaced
PHY-3001 : design contains 19666 instances, 5712 luts, 12349 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 501699
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17416/22132.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 582008, over cnt = 2620(7%), over = 9043, worst = 24
PHY-1001 : End global iterations;  0.206302s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (159.1%)

PHY-1001 : Congestion index: top1 = 60.86, top5 = 49.31, top10 = 44.16, top15 = 41.06.
PHY-3001 : End congestion estimation;  0.488766s wall, 0.609375s user + 0.000000s system = 0.609375s CPU (124.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81843, tnet num: 22130, tinst num: 19666, tnode num: 115096, tedge num: 128698.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.648759s wall, 1.609375s user + 0.031250s system = 1.640625s CPU (99.5%)

RUN-1004 : used memory is 613 MB, reserved memory is 605 MB, peak memory is 706 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22130 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.787288s wall, 2.718750s user + 0.062500s system = 2.781250s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(155): len = 502593, overlap = 0.4375
PHY-3002 : Step(156): len = 504389, overlap = 0.4375
PHY-3002 : Step(157): len = 505562, overlap = 0.4375
PHY-3002 : Step(158): len = 506741, overlap = 0.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17436/22132.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 582592, over cnt = 2645(7%), over = 9152, worst = 24
PHY-1001 : End global iterations;  0.202068s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (108.3%)

PHY-1001 : Congestion index: top1 = 61.36, top5 = 49.42, top10 = 44.27, top15 = 41.20.
PHY-3001 : End congestion estimation;  0.529667s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (103.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22130 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.137765s wall, 1.125000s user + 0.015625s system = 1.140625s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000596963
PHY-3002 : Step(159): len = 506393, overlap = 131.031
PHY-3002 : Step(160): len = 506387, overlap = 131.031
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00119393
PHY-3002 : Step(161): len = 506558, overlap = 131.312
PHY-3002 : Step(162): len = 506652, overlap = 131.781
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00238417
PHY-3002 : Step(163): len = 506676, overlap = 131.656
PHY-3002 : Step(164): len = 506720, overlap = 131.406
PHY-3001 : Final: Len = 506720, Over = 131.406
PHY-3001 : End incremental placement;  6.097802s wall, 6.843750s user + 0.234375s system = 7.078125s CPU (116.1%)

OPT-1001 : Total overflow 497.56 peak overflow 4.31
OPT-1001 : End high-fanout net optimization;  9.417745s wall, 10.984375s user + 0.250000s system = 11.234375s CPU (119.3%)

OPT-1001 : Current memory(MB): used = 708, reserve = 687, peak = 724.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17444/22132.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 584912, over cnt = 2618(7%), over = 8644, worst = 23
PHY-1002 : len = 633392, over cnt = 1818(5%), over = 4157, worst = 20
PHY-1002 : len = 671424, over cnt = 727(2%), over = 1401, worst = 15
PHY-1002 : len = 688960, over cnt = 202(0%), over = 376, worst = 11
PHY-1002 : len = 694144, over cnt = 25(0%), over = 47, worst = 6
PHY-1001 : End global iterations;  1.456478s wall, 2.203125s user + 0.046875s system = 2.250000s CPU (154.5%)

PHY-1001 : Congestion index: top1 = 52.89, top5 = 45.46, top10 = 41.83, top15 = 39.65.
OPT-1001 : End congestion update;  1.746917s wall, 2.500000s user + 0.046875s system = 2.546875s CPU (145.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22130 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.959561s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (99.3%)

OPT-0007 : Start: WNS 4137 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.712619s wall, 3.453125s user + 0.046875s system = 3.500000s CPU (129.0%)

OPT-1001 : Current memory(MB): used = 684, reserve = 666, peak = 724.
OPT-1001 : End physical optimization;  14.130714s wall, 16.546875s user + 0.359375s system = 16.906250s CPU (119.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5712 LUT to BLE ...
SYN-4008 : Packed 5712 LUT and 2729 SEQ to BLE.
SYN-4003 : Packing 9620 remaining SEQ's ...
SYN-4005 : Packed 3395 SEQ with LUT/SLICE
SYN-4006 : 119 single LUT's are left
SYN-4006 : 6225 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11937/13808 primitive instances ...
PHY-3001 : End packing;  3.095254s wall, 3.093750s user + 0.015625s system = 3.109375s CPU (100.5%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8151 instances
RUN-1001 : 4023 mslices, 4023 lslices, 59 pads, 41 brams, 0 dsps
RUN-1001 : There are total 19455 nets
RUN-1001 : 13615 nets have 2 pins
RUN-1001 : 4420 nets have [3 - 5] pins
RUN-1001 : 886 nets have [6 - 10] pins
RUN-1001 : 394 nets have [11 - 20] pins
RUN-1001 : 131 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8149 instances, 8046 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 522156, Over = 364.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7876/19455.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 655768, over cnt = 1713(4%), over = 2697, worst = 7
PHY-1002 : len = 662240, over cnt = 1010(2%), over = 1410, worst = 6
PHY-1002 : len = 676680, over cnt = 263(0%), over = 310, worst = 6
PHY-1002 : len = 680768, over cnt = 96(0%), over = 101, worst = 3
PHY-1002 : len = 683144, over cnt = 4(0%), over = 4, worst = 1
PHY-1001 : End global iterations;  1.398173s wall, 2.218750s user + 0.015625s system = 2.234375s CPU (159.8%)

PHY-1001 : Congestion index: top1 = 52.91, top5 = 44.92, top10 = 41.28, top15 = 39.00.
PHY-3001 : End congestion estimation;  1.755428s wall, 2.593750s user + 0.015625s system = 2.609375s CPU (148.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67981, tnet num: 19453, tinst num: 8149, tnode num: 92194, tedge num: 112151.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.858701s wall, 1.859375s user + 0.000000s system = 1.859375s CPU (100.0%)

RUN-1004 : used memory is 604 MB, reserved memory is 596 MB, peak memory is 724 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19453 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.854065s wall, 2.843750s user + 0.000000s system = 2.843750s CPU (99.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.22743e-05
PHY-3002 : Step(165): len = 524723, overlap = 345.5
PHY-3002 : Step(166): len = 522356, overlap = 352
PHY-3002 : Step(167): len = 521784, overlap = 356.25
PHY-3002 : Step(168): len = 519870, overlap = 370
PHY-3002 : Step(169): len = 517764, overlap = 370
PHY-3002 : Step(170): len = 517087, overlap = 383.75
PHY-3002 : Step(171): len = 514918, overlap = 381.25
PHY-3002 : Step(172): len = 513754, overlap = 378
PHY-3002 : Step(173): len = 512101, overlap = 376.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000104549
PHY-3002 : Step(174): len = 515101, overlap = 368.5
PHY-3002 : Step(175): len = 519965, overlap = 359
PHY-3002 : Step(176): len = 521074, overlap = 359
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000209097
PHY-3002 : Step(177): len = 527403, overlap = 349.25
PHY-3002 : Step(178): len = 538857, overlap = 335.25
PHY-3002 : Step(179): len = 539055, overlap = 330
PHY-3002 : Step(180): len = 537265, overlap = 324.5
PHY-3002 : Step(181): len = 536967, overlap = 318
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.768129s wall, 0.906250s user + 0.875000s system = 1.781250s CPU (231.9%)

PHY-3001 : Trial Legalized: Len = 641040
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 617/19455.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 730560, over cnt = 2420(6%), over = 3981, worst = 8
PHY-1002 : len = 745272, over cnt = 1523(4%), over = 2194, worst = 8
PHY-1002 : len = 763840, over cnt = 517(1%), over = 722, worst = 8
PHY-1002 : len = 771712, over cnt = 221(0%), over = 300, worst = 4
PHY-1002 : len = 776272, over cnt = 14(0%), over = 15, worst = 2
PHY-1001 : End global iterations;  2.040106s wall, 3.406250s user + 0.000000s system = 3.406250s CPU (167.0%)

PHY-1001 : Congestion index: top1 = 51.12, top5 = 45.89, top10 = 43.08, top15 = 41.19.
PHY-3001 : End congestion estimation;  2.440357s wall, 3.812500s user + 0.000000s system = 3.812500s CPU (156.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19453 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.018432s wall, 0.984375s user + 0.031250s system = 1.015625s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000178199
PHY-3002 : Step(182): len = 601468, overlap = 72.75
PHY-3002 : Step(183): len = 583280, overlap = 121
PHY-3002 : Step(184): len = 571945, overlap = 169.5
PHY-3002 : Step(185): len = 565500, overlap = 200
PHY-3002 : Step(186): len = 561604, overlap = 223.5
PHY-3002 : Step(187): len = 559506, overlap = 245.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000356397
PHY-3002 : Step(188): len = 563956, overlap = 237.25
PHY-3002 : Step(189): len = 569171, overlap = 228.75
PHY-3002 : Step(190): len = 570475, overlap = 229.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(191): len = 573077, overlap = 233.25
PHY-3002 : Step(192): len = 579662, overlap = 229
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.043167s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (108.6%)

PHY-3001 : Legalized: Len = 619644, Over = 0
PHY-3001 : Spreading special nets. 41 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.091501s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (85.4%)

PHY-3001 : 66 instances has been re-located, deltaX = 12, deltaY = 48, maxDist = 3.
PHY-3001 : Final: Len = 621250, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67981, tnet num: 19453, tinst num: 8149, tnode num: 92194, tedge num: 112151.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.228135s wall, 2.218750s user + 0.000000s system = 2.218750s CPU (99.6%)

RUN-1004 : used memory is 617 MB, reserved memory is 618 MB, peak memory is 724 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 4309/19455.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 722080, over cnt = 2196(6%), over = 3407, worst = 7
PHY-1002 : len = 732832, over cnt = 1376(3%), over = 1863, worst = 6
PHY-1002 : len = 749256, over cnt = 492(1%), over = 649, worst = 6
PHY-1002 : len = 754512, over cnt = 245(0%), over = 332, worst = 6
PHY-1002 : len = 760224, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.932912s wall, 2.984375s user + 0.015625s system = 3.000000s CPU (155.2%)

PHY-1001 : Congestion index: top1 = 48.53, top5 = 44.04, top10 = 41.45, top15 = 39.70.
PHY-1001 : End incremental global routing;  2.278571s wall, 3.328125s user + 0.015625s system = 3.343750s CPU (146.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19453 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.019199s wall, 0.984375s user + 0.015625s system = 1.000000s CPU (98.1%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8086 has valid locations, 9 needs to be replaced
PHY-3001 : design contains 8157 instances, 8054 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 622423
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17542/19462.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 761216, over cnt = 10(0%), over = 10, worst = 1
PHY-1002 : len = 761208, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 761232, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 761304, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.575486s wall, 0.593750s user + 0.000000s system = 0.593750s CPU (103.2%)

PHY-1001 : Congestion index: top1 = 48.53, top5 = 44.01, top10 = 41.44, top15 = 39.70.
PHY-3001 : End congestion estimation;  0.910400s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (101.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68028, tnet num: 19460, tinst num: 8157, tnode num: 92249, tedge num: 112205.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.082185s wall, 2.062500s user + 0.015625s system = 2.078125s CPU (99.8%)

RUN-1004 : used memory is 649 MB, reserved memory is 636 MB, peak memory is 724 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19460 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.110583s wall, 3.062500s user + 0.046875s system = 3.109375s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(193): len = 622595, overlap = 0
PHY-3002 : Step(194): len = 622603, overlap = 0
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17539/19462.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 761072, over cnt = 15(0%), over = 15, worst = 1
PHY-1002 : len = 761120, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 761088, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 761088, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.573833s wall, 0.562500s user + 0.000000s system = 0.562500s CPU (98.0%)

PHY-1001 : Congestion index: top1 = 48.56, top5 = 44.08, top10 = 41.48, top15 = 39.74.
PHY-3001 : End congestion estimation;  0.899839s wall, 0.890625s user + 0.000000s system = 0.890625s CPU (99.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19460 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.982195s wall, 0.984375s user + 0.000000s system = 0.984375s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000639092
PHY-3002 : Step(195): len = 622663, overlap = 1.75
PHY-3002 : Step(196): len = 622653, overlap = 0.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007765s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (201.2%)

PHY-3001 : Legalized: Len = 622609, Over = 0
PHY-3001 : End spreading;  0.073859s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (105.8%)

PHY-3001 : Final: Len = 622609, Over = 0
PHY-3001 : End incremental placement;  6.634489s wall, 6.968750s user + 0.140625s system = 7.109375s CPU (107.2%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.479662s wall, 11.906250s user + 0.171875s system = 12.078125s CPU (115.3%)

OPT-1001 : Current memory(MB): used = 721, reserve = 705, peak = 726.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17539/19462.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 761152, over cnt = 6(0%), over = 7, worst = 2
PHY-1002 : len = 761120, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 761136, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 761200, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 761216, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.736550s wall, 0.750000s user + 0.015625s system = 0.765625s CPU (103.9%)

PHY-1001 : Congestion index: top1 = 48.53, top5 = 44.07, top10 = 41.48, top15 = 39.74.
OPT-1001 : End congestion update;  1.068506s wall, 1.078125s user + 0.015625s system = 1.093750s CPU (102.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19460 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.825822s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (100.3%)

OPT-0007 : Start: WNS 4157 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.899227s wall, 1.906250s user + 0.015625s system = 1.921875s CPU (101.2%)

OPT-1001 : Current memory(MB): used = 721, reserve = 705, peak = 726.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19460 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.822588s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (100.7%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17550/19462.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 761216, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.128892s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (97.0%)

PHY-1001 : Congestion index: top1 = 48.53, top5 = 44.07, top10 = 41.48, top15 = 39.74.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19460 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.835652s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (99.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4157 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 48.000000
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4157ps with logic level 8 
RUN-1001 :       #2 path slack 4182ps with logic level 8 
OPT-1001 : End physical optimization;  17.012197s wall, 18.593750s user + 0.218750s system = 18.812500s CPU (110.6%)

RUN-1003 : finish command "place" in  72.061305s wall, 129.593750s user + 7.031250s system = 136.625000s CPU (189.6%)

RUN-1004 : used memory is 568 MB, reserved memory is 543 MB, peak memory is 726 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.839332s wall, 3.171875s user + 0.015625s system = 3.187500s CPU (173.3%)

RUN-1004 : used memory is 569 MB, reserved memory is 545 MB, peak memory is 726 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8159 instances
RUN-1001 : 4023 mslices, 4031 lslices, 59 pads, 41 brams, 0 dsps
RUN-1001 : There are total 19462 nets
RUN-1001 : 13615 nets have 2 pins
RUN-1001 : 4421 nets have [3 - 5] pins
RUN-1001 : 888 nets have [6 - 10] pins
RUN-1001 : 398 nets have [11 - 20] pins
RUN-1001 : 131 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68028, tnet num: 19460, tinst num: 8157, tnode num: 92249, tedge num: 112205.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.949636s wall, 1.953125s user + 0.000000s system = 1.953125s CPU (100.2%)

RUN-1004 : used memory is 583 MB, reserved memory is 559 MB, peak memory is 726 MB
PHY-1001 : 4023 mslices, 4031 lslices, 59 pads, 41 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19460 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 700264, over cnt = 2327(6%), over = 3839, worst = 7
PHY-1002 : len = 717072, over cnt = 1389(3%), over = 1930, worst = 6
PHY-1002 : len = 735240, over cnt = 441(1%), over = 581, worst = 4
PHY-1002 : len = 744648, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 744840, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.996306s wall, 3.281250s user + 0.078125s system = 3.359375s CPU (168.3%)

PHY-1001 : Congestion index: top1 = 48.15, top5 = 43.39, top10 = 41.02, top15 = 39.30.
PHY-1001 : End global routing;  2.369782s wall, 3.640625s user + 0.078125s system = 3.718750s CPU (156.9%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 699, reserve = 690, peak = 726.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 971, reserve = 959, peak = 971.
PHY-1001 : End build detailed router design. 4.950662s wall, 4.875000s user + 0.062500s system = 4.937500s CPU (99.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 194064, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.984554s wall, 0.968750s user + 0.015625s system = 0.984375s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1006, reserve = 995, peak = 1006.
PHY-1001 : End phase 1; 0.991916s wall, 0.968750s user + 0.015625s system = 0.984375s CPU (99.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.7457e+06, over cnt = 1391(0%), over = 1395, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1020, reserve = 1009, peak = 1020.
PHY-1001 : End initial routed; 20.758511s wall, 53.031250s user + 0.609375s system = 53.640625s CPU (258.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18190(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.654   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.740242s wall, 3.718750s user + 0.015625s system = 3.734375s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 1028, reserve = 1018, peak = 1028.
PHY-1001 : End phase 2; 24.498927s wall, 56.750000s user + 0.625000s system = 57.375000s CPU (234.2%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.7457e+06, over cnt = 1391(0%), over = 1395, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.263575s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (100.8%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.73155e+06, over cnt = 424(0%), over = 424, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.953479s wall, 1.500000s user + 0.000000s system = 1.500000s CPU (157.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.73185e+06, over cnt = 63(0%), over = 63, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.494926s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (145.2%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.73269e+06, over cnt = 13(0%), over = 13, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.277686s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (123.8%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.73302e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 4; 0.182034s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (94.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18190(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.654   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 4.060568s wall, 4.046875s user + 0.015625s system = 4.062500s CPU (100.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 318 feed throughs used by 278 nets
PHY-1001 : End commit to database; 2.508888s wall, 2.484375s user + 0.031250s system = 2.515625s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 1116, reserve = 1110, peak = 1116.
PHY-1001 : End phase 3; 9.303823s wall, 10.062500s user + 0.046875s system = 10.109375s CPU (108.7%)

PHY-1003 : Routed, final wirelength = 1.73302e+06
PHY-1001 : Current memory(MB): used = 1121, reserve = 1114, peak = 1121.
PHY-1001 : End export database. 0.190768s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (98.3%)

PHY-1001 : End detail routing;  40.419129s wall, 73.343750s user + 0.750000s system = 74.093750s CPU (183.3%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68028, tnet num: 19460, tinst num: 8157, tnode num: 92249, tedge num: 112205.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.918943s wall, 1.906250s user + 0.015625s system = 1.921875s CPU (100.2%)

RUN-1004 : used memory is 1001 MB, reserved memory is 1006 MB, peak memory is 1121 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  49.740041s wall, 83.875000s user + 0.906250s system = 84.781250s CPU (170.4%)

RUN-1004 : used memory is 1032 MB, reserved memory is 1036 MB, peak memory is 1121 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8878   out of  19600   45.30%
#reg                    12443   out of  19600   63.48%
#le                     15047
  #lut only              2604   out of  15047   17.31%
  #reg only              6169   out of  15047   41.00%
  #lut&reg               6274   out of  15047   41.70%
#dsp                        0   out of     29    0.00%
#bram                      41   out of     64   64.06%
  #bram9k                  39
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       59   out of    188   31.38%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6820
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          163
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         F2        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         D1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         B3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         C1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         A3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         A2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         B2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         B1        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F15        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDZ        INPUT        K15        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         P2        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         N4        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         M5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         N6        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         N5        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         P5        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M3        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         M4        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         H2        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         M1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         L1        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         N1        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         N3        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         M2        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT         T5        LVCMOS33           8            NONE           OREG       
    Fog_ENY        OUTPUT         T4        LVCMOS33           8            NONE           OREG       
    Fog_ENZ        OUTPUT         T7        LVCMOS33           8            NONE           OREG       
    Fog_INT        OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         T6        LVCMOS33           8            N/A            OREG       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E1        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        G11        LVCMOS25           8            N/A            NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15047  |7376    |1502    |12487   |41      |0       |
|  AnyFog_dataX                      |AnyFog          |209    |72      |22      |175     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |87     |54      |22      |53      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |207    |71      |22      |172     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |53      |22      |51      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |209    |100     |22      |170     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |89     |59      |22      |50      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2922   |678     |39      |2837    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |62     |37      |5       |54      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |207    |56      |5       |199     |0       |0       |
|    STADOP_com2                     |STADOP          |552    |70      |0       |548     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |64     |48      |14      |40      |0       |0       |
|    head_com2                       |uniheading      |261    |59      |5       |250     |0       |0       |
|    rmc_com2                        |Gprmc           |45     |44      |0       |33      |0       |0       |
|    uart_com2                       |Agrica          |1406   |330     |10      |1388    |0       |0       |
|  COM3                              |COM3_Control    |272    |129     |19      |238     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |63     |39      |5       |54      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |62     |40      |14      |40      |0       |0       |
|    rmc_com3                        |Gprmc           |147    |50      |0       |144     |0       |0       |
|  DATA                              |Data_Processing |8817   |4550    |1122    |7030    |0       |0       |
|    DIV_Dtemp                       |Divider         |773    |304     |84      |653     |0       |0       |
|    DIV_Utemp                       |Divider         |673    |286     |84      |547     |0       |0       |
|    DIV_accX                        |Divider         |622    |293     |84      |500     |0       |0       |
|    DIV_accY                        |Divider         |535    |342     |102     |377     |0       |0       |
|    DIV_accZ                        |Divider         |677    |342     |132     |472     |0       |0       |
|    DIV_rateX                       |Divider         |697    |399     |132     |496     |0       |0       |
|    DIV_rateY                       |Divider         |621    |414     |132     |416     |0       |0       |
|    DIV_rateZ                       |Divider         |531    |334     |132     |327     |0       |0       |
|    genclk                          |genclk          |269    |169     |89      |110     |0       |0       |
|  FMC                               |FMC_Ctrl        |455    |402     |43      |348     |0       |0       |
|  IIC                               |I2C_master      |284    |238     |11      |259     |0       |0       |
|  IMU_CTRL                          |SCHA634         |941    |689     |61      |740     |0       |0       |
|    CtrlData                        |CtrlData        |504    |442     |47      |336     |0       |0       |
|      usms                          |Time_1ms        |33     |28      |5       |18      |0       |0       |
|    SPIM                            |SPI_SCHA634     |437    |247     |14      |404     |0       |0       |
|  POWER                             |POWER_EN        |94     |53      |38      |35      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |627    |394     |103     |429     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |627    |394     |103     |429     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |280    |173     |0       |263     |0       |0       |
|        reg_inst                    |register        |277    |170     |0       |260     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |347    |221     |103     |166     |0       |0       |
|        bus_inst                    |bus_top         |131    |85      |46      |49      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |26     |16      |10      |10      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |48     |30      |18      |15      |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |3      |3       |0       |3       |0       |0       |
|          BUS_DETECTOR[5]$bus_nodes |bus_det         |52     |34      |18      |19      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |134    |95      |29      |84      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13555  
    #2          2       3425   
    #3          3        688   
    #4          4        308   
    #5        5-10       961   
    #6        11-50      448   
    #7       51-100       8    
    #8       101-500      3    
    #9        >500        2    
  Average     2.14             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.238113s wall, 3.843750s user + 0.015625s system = 3.859375s CPU (172.4%)

RUN-1004 : used memory is 1033 MB, reserved memory is 1037 MB, peak memory is 1121 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68028, tnet num: 19460, tinst num: 8157, tnode num: 92249, tedge num: 112205.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.909710s wall, 1.921875s user + 0.000000s system = 1.921875s CPU (100.6%)

RUN-1004 : used memory is 1034 MB, reserved memory is 1038 MB, peak memory is 1121 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19460 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.513410s wall, 1.500000s user + 0.000000s system = 1.500000s CPU (99.1%)

RUN-1004 : used memory is 1044 MB, reserved memory is 1046 MB, peak memory is 1121 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: e7acc41f3f3c0493db2e7c96172f62b19e1071d28814331b86e973fbb54fbf81 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8157
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19462, pip num: 148252
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 318
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3239 valid insts, and 414801 bits set as '1'.
BIT-1004 : the usercode register value: 00000000000000110011101000100101
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.026014s wall, 117.515625s user + 0.234375s system = 117.750000s CPU (979.1%)

RUN-1004 : used memory is 1191 MB, reserved memory is 1176 MB, peak memory is 1306 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250821_145417.log"

============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Jul 10 20:09:23 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param rtl default_reg_initial 0"
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |     0      |       auto       |   *    
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(520)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
HDL-5007 WARNING: 'GNRMC_STARTdy' is not declared in ../../Src/GNSS/GNRMC_Tx.v(83)
HDL-5007 WARNING: 'GNRMC_STARTdy' is not declared in ../../Src/GNSS/GNRMC_Tx.v(108)
HDL-5007 WARNING: 'fifo_valid' is not declared in ../../Src/GNSS/GNRMC_Tx.v(189)
HDL-5007 WARNING: 'GNRMC_STARTdy' is not declared in ../../Src/GNSS/GNRMC_Tx.v(83)
HDL-5007 WARNING: 'GNRMC_STARTdy' is not declared in ../../Src/GNSS/GNRMC_Tx.v(108)
HDL-5007 WARNING: 'fifo_valid' is not declared in ../../Src/GNSS/GNRMC_Tx.v(189)
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.087514s wall, 1.359375s user + 3.734375s system = 5.093750s CPU (100.1%)

RUN-1004 : used memory is 80 MB, reserved memory is 41 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "elaborate -top INS600M_21A"
RUN-1002 : start command "set_param rtl default_reg_initial 0"
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |     0      |       auto       |   *    
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param rtl default_reg_initial 0"
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |     0      |       auto       |   *    
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : port 'GPRMC_latch' remains unconnected for this instance in ../../Src/INS600M-21A.v(421)
HDL-1007 : port 'AGRIC_test' remains unconnected for this instance in ../../Src/INS600M-21A.v(435)
HDL-1007 : port 'GPRMC_test' remains unconnected for this instance in ../../Src/INS600M-21A.v(435)
HDL-1007 : port 'PPPdat_test' remains unconnected for this instance in ../../Src/INS600M-21A.v(435)
HDL-1007 : port 'STADOP_test' remains unconnected for this instance in ../../Src/INS600M-21A.v(435)
HDL-1007 : port 'HEADING_test' remains unconnected for this instance in ../../Src/INS600M-21A.v(435)
HDL-1007 : port 'GPRMC_POS' remains unconnected for this instance in ../../Src/INS600M-21A.v(435)
HDL-1007 : port 'GPRMC_LON' remains unconnected for this instance in ../../Src/INS600M-21A.v(435)
HDL-1007 : port 'GPRMC_LAT' remains unconnected for this instance in ../../Src/INS600M-21A.v(435)
HDL-1007 : port 'GPRMC_TRA' remains unconnected for this instance in ../../Src/INS600M-21A.v(435)
HDL-1007 : port 'FOG_INT' remains unconnected for this instance in ../../Src/INS600M-21A.v(492)
HDL-5007 WARNING: port 'Modle_state' is not connected on this instance in ../../Src/INS600M-21A.v(687)
HDL-1007 : port 'FMC_DACC_MdataX' remains unconnected for this instance in ../../Src/INS600M-21A.v(687)
HDL-1007 : port 'FMC_DACC_MdataY' remains unconnected for this instance in ../../Src/INS600M-21A.v(687)
HDL-1007 : port 'FMC_DACC_MdataZ' remains unconnected for this instance in ../../Src/INS600M-21A.v(687)
HDL-1007 : elaborate module INS600M_21A in ../../Src/INS600M-21A.v(16)
HDL-1007 : elaborate module global_clock in ../../al_ip/global_clock.v(22)
HDL-1007 : elaborate module EG_PHY_PLL(FIN="25.000",FBCLK_DIV=40,CLKC0_DIV=10,CLKC0_ENABLE="ENABLE",FEEDBK_MODE="NOCOMP",STDBY_ENABLE="DISABLE",CLKC0_CPHASE=9,GMC_GAIN=2,ICP_CURRENT=9,KVCO=2,LPF_CAPACITOR=1,LPF_RESISTOR=8,SYNC_ENABLE="DISABLE") in D:/softwawe/Anlogic/TD_5.6.2/arch/eagle_macro.v(930)
HDL-1007 : elaborate module POWER_EN in ../../Src/POWER/POWER_EN.v(16)
HDL-5007 WARNING: port 'tx_data' is not connected on this instance in ../../Src/IFOG/AnyFog.v(46)
HDL-1007 : port 'tx_rdy' remains unconnected for this instance in ../../Src/IFOG/AnyFog.v(46)
HDL-1007 : port 'txd' remains unconnected for this instance in ../../Src/IFOG/AnyFog.v(46)
HDL-1007 : elaborate module AnyFog in ../../Src/IFOG/AnyFog.v(17)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/UART_RX115200E_gate.v(272)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/UART_RX115200E_gate.v(298)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/UART_RX115200E_gate.v(441)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/UART_RX115200E_gate.v(672)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/UART_RX115200E_gate.v(705)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/UART_RX115200E_gate.v(1027)
HDL-1007 : elaborate module UART_RX115200E in ../../al_ip/UART_RX115200E_gate.v(5)
HDL-1007 : elaborate module AL_MAP_ADDER(ALUTYPE="ADD") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(72)
HDL-1007 : elaborate module AL_MAP_ADDER(ALUTYPE="ADD_CARRY") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(72)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b0100,EQN="(~C*B*~A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT2(INIT=4'b010,EQN="(~B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(48)
HDL-1007 : elaborate module AL_MAP_ADDER(ALUTYPE="A_LE_B_CARRY") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(72)
HDL-1007 : elaborate module AL_MAP_ADDER(ALUTYPE="A_LE_B") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(72)
HDL-1007 : elaborate module AL_DFF_X in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(236)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b0100111,EQN="~(C*~(B)*~(A)+C*B*~(A)+~(C)*B*A+C*B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b011111110,EQN="(~D*~(~C*~B*~A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b0111010100100000,EQN="(~E*(D*~((C*~B))*~(A)+D*(C*~B)*~(A)+~(D)*(C*~B)*A+D*(C*~B)*A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b10000010,EQN="(A*~(C@B))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b01,EQN="(~C*~B*~A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b0100000,EQN="(C*~B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b01000,EQN="(~D*~C*B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b011110010,EQN="(~D*~(~C*~(~B*A)))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b0100111011100100,EQN="(~E*(B*~((D@C))*~(A)+B*(D@C)*~(A)+~(B)*(D@C)*A+B*(D@C)*A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b10110000,EQN="(C*~(B*~A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b0100000000,EQN="(D*~C*~B*~A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b010110000,EQN="(~D*C*~(B*~A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b010000,EQN="(C*~B*~A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b01,EQN="(~D*~C*~B*~A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b01001100,EQN="(B*~(C*A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b0100000000000,EQN="(D*~C*B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT2(INIT=4'b1000,EQN="(B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(48)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b10011101110111010100000000000000,EQN="(~(A)*B*(D*C)*~(E)+~(A)*~(B)*~((D*C))*E+~(A)*B*~((D*C))*E+A*B*~((D*C))*E+~(A)*~(B)*(D*C)*E+A*B*(D*C)*E)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b01000000000000000000000000000,EQN="(E*D*~C*B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b010111010,EQN="(~D*~(~A*~(C*~B)))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT2(INIT=4'b01,EQN="(~B*~A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(48)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b010000000000000,EQN="(D*C*~B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b01000,EQN="(~C*B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b1000000000000000,EQN="(D*C*B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b010001011101010,EQN="(A*~(B)*~(C)*~(D)+A*B*~(C)*~(D)+A*~(B)*C*~(D)+~(A)*B*C*~(D)+A*B*C*~(D)+A*~(B)*~(C)*D+A*~(B)*C*D)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b010,EQN="(~E*~D*~C*~B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b0100000111101110000000000000000,EQN="(E*(~(A)*~(B)*~(C)*~(D)+A*~(B)*~(C)*~(D)+~(A)*B*~(C)*~(D)+~(A)*~(B)*C*~(D)+A*~(B)*C*~(D)+~(A)*B*C*~(D)+A*B*C*~(D)+A*~(B)*C*D))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b0110001,EQN="(~B*~(~C*A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b01000000000001000100010000,EQN="(~B*~A*~(~D*~(E)*~(C)+~D*E*~(C)+~(~D)*E*C+~D*E*C))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11111111011111111000000000000000,EQN="(E*~(D)*~((C*B*A))+E*D*~((C*B*A))+~(E)*D*(C*B*A)+E*D*(C*B*A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b11111110,EQN="~(~C*~B*~A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b010,EQN="(~D*~C*~B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b010000000,EQN="(~D*C*B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT2(INIT=4'b0100,EQN="(B*~A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(48)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b01000000,EQN="(C*B*~A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11111111111000000000000000000000,EQN="(E*~(~D*~(C*~(~B*~A))))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b1011000110111011,EQN="~(B*~((D*~C))*~(A)+B*(D*~C)*~(A)+~(B)*(D*~C)*A+B*(D*~C)*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b0111100000100,EQN="(~C*~(~D*~(B*~A)))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b011100000000,EQN="(D*~C*~(B*A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b01000001010000,EQN="(C*~A*~(D*B))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b01111000,EQN="(~D*(C@(B*A)))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b0110000000,EQN="(A*B*C*~(D)+~(A)*~(B)*~(C)*D)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b01100100011000100110011001100,EQN="(B*~(C*(E*~(D)*~(A)+E*D*~(A)+~(E)*D*A+E*D*A)))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b10111011101110111011000010111011,EQN="(~(~E*D*~C)*~(B*~A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b01101,EQN="(~C*~(~B*A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b0101100111011,EQN="(~(A)*~(B)*~(C)*~(D)+A*~(B)*~(C)*~(D)+A*B*~(C)*~(D)+~(A)*~(B)*C*~(D)+A*~(B)*C*~(D)+~(A)*~(B)*~(C)*D+A*~(B)*~(C)*D+A*B*~(C)*D)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b010001000000010001000000000000,EQN="(~B*~A*(E*~(D)*~(C)+E*D*~(C)+~(E)*D*C+E*D*C))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11110111000000001100010000000000,EQN="(D*~(~E*~((~C*A))*~(B)+~E*(~C*A)*~(B)+~(~E)*(~C*A)*B+~E*(~C*A)*B))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11010101110100001000101010000000,EQN="(A*B*C*~(D)*~(E)+A*~(B)*~(C)*D*~(E)+A*B*~(C)*D*~(E)+A*B*C*D*~(E)+~(A)*~(B)*C*~(D)*E+~(A)*B*C*~(D)*E+A*B*C*~(D)*E+~(A)*~(B)*~(C)*D*E+~(A)*B*~(C)*D*E+~(A)*~(B)*C*D*E+~(A)*B*C*D*E+A*B*C*D*E)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11110101110011001010000011001100,EQN="(B*~((E*~(C)*~(A)+E*C*~(A)+~(E)*C*A+E*C*A))*~(D)+B*(E*~(C)*~(A)+E*C*~(A)+~(E)*C*A+E*C*A)*~(D)+~(B)*(E*~(C)*~(A)+E*C*~(A)+~(E)*C*A+E*C*A)*D+B*(E*~(C)*~(A)+E*C*~(A)+~(E)*C*A+E*C*A)*D)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b10101011100010111101111111111111,EQN="(~(A)*~(B)*~(C)*~(D)*~(E)+A*~(B)*~(C)*~(D)*~(E)+~(A)*B*~(C)*~(D)*~(E)+A*B*~(C)*~(D)*~(E)+~(A)*~(B)*C*~(D)*~(E)+A*~(B)*C*~(D)*~(E)+~(A)*B*C*~(D)*~(E)+A*B*C*~(D)*~(E)+~(A)*~(B)*~(C)*D*~(E)+A*~(B)*~(C)*D*~(E)+~(A)*B*~(C)*D*~(E)+A*B*~(C)*D*~(E)+~(A)*~(B)*C*D*~(E)+~(A)*B*C*D*~(E)+A*B*C*D*~(E)+~(A)*~(B)*~(C)*~(D)*E+A*~(B)*~(C)*~(D)*E+A*B*~(C)*~(D)*E+A*B*C*~(D)*E+~(A)*~(B)*~(C)*D*E+A*~(B)*~(C)*D*E+A*B*~(C)*D*E+A*~(B)*C*D*E+A*B*C*D*E)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b0101001100000011,EQN="(~B*~((D*~A))*~(C)+~B*(D*~A)*~(C)+~(~B)*(D*~A)*C+~B*(D*~A)*C)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b10101000101011111111011111111111,EQN="(~(A)*~(B)*~(C)*~(D)*~(E)+A*~(B)*~(C)*~(D)*~(E)+~(A)*B*~(C)*~(D)*~(E)+A*B*~(C)*~(D)*~(E)+~(A)*~(B)*C*~(D)*~(E)+A*~(B)*C*~(D)*~(E)+~(A)*B*C*~(D)*~(E)+A*B*C*~(D)*~(E)+~(A)*~(B)*~(C)*D*~(E)+A*~(B)*~(C)*D*~(E)+~(A)*B*~(C)*D*~(E)+~(A)*~(B)*C*D*~(E)+A*~(B)*C*D*~(E)+~(A)*B*C*D*~(E)+A*B*C*D*~(E)+~(A)*~(B)*~(C)*~(D)*E+A*~(B)*~(C)*~(D)*E+~(A)*B*~(C)*~(D)*E+A*B*~(C)*~(D)*E+A*~(B)*C*~(D)*E+A*B*C*~(D)*E+A*B*~(C)*D*E+A*~(B)*C*D*E+A*B*C*D*E)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b011010100000101,EQN="(~A*~((D*~B))*~(C)+~A*(D*~B)*~(C)+~(~A)*(D*~B)*C+~A*(D*~B)*C)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b1001011001101001,EQN="~(D@C@B@A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b10010110011010010110100110010110,EQN="(E@D@C@B@A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11111111011111110000000001000000,EQN="~(~E*~(A)*~((~D*C*B))+~E*A*~((~D*C*B))+~(~E)*A*(~D*C*B)+~E*A*(~D*C*B))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b10001111000011111000100000000000,EQN="~(~(E*~C)*~(D*B*A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b010000000000,EQN="(D*~C*B*~A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b10000000,EQN="(C*B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b0100000000000000000000000000000,EQN="(E*D*C*~B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b010010001100,EQN="(B*~(C*~(D)*~(A)+C*D*~(A)+~(C)*D*A+C*D*A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b011110001111,EQN="~(C*~(D)*~((B*A))+C*D*~((B*A))+~(C)*D*(B*A)+C*D*(B*A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b0111101110000000011010101,EQN="(~D*~(A*~(E*~(C)*~(B)+E*C*~(B)+~(E)*C*B+E*C*B)))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11111111000100001111111101010000,EQN="~(~D*~(C*~A*~(E*B)))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b10101010000011001111111100111111,EQN="(~(A)*~(B)*~(C)*~(D)*~(E)+A*~(B)*~(C)*~(D)*~(E)+~(A)*B*~(C)*~(D)*~(E)+A*B*~(C)*~(D)*~(E)+~(A)*~(B)*C*~(D)*~(E)+A*~(B)*C*~(D)*~(E)+~(A)*~(B)*~(C)*D*~(E)+A*~(B)*~(C)*D*~(E)+~(A)*B*~(C)*D*~(E)+A*B*~(C)*D*~(E)+~(A)*~(B)*C*D*~(E)+A*~(B)*C*D*~(E)+~(A)*B*C*D*~(E)+A*B*C*D*~(E)+~(A)*B*~(C)*~(D)*E+A*B*~(C)*~(D)*E+A*~(B)*~(C)*D*E+A*B*~(C)*D*E+A*~(B)*C*D*E+A*B*C*D*E)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11111111010100110000000000000011,EQN="(~(A)*~(B)*~(C)*~(D)*~(E)+A*~(B)*~(C)*~(D)*~(E)+~(A)*~(B)*~(C)*~(D)*E+A*~(B)*~(C)*~(D)*E+~(A)*~(B)*C*~(D)*E+~(A)*B*C*~(D)*E+~(A)*~(B)*~(C)*D*E+A*~(B)*~(C)*D*E+~(A)*B*~(C)*D*E+A*B*~(C)*D*E+~(A)*~(B)*C*D*E+A*~(B)*C*D*E+~(A)*B*C*D*E+A*B*C*D*E)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/UART_RX115200E_gate.v(274)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/UART_RX115200E_gate.v(300)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/UART_RX115200E_gate.v(443)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/UART_RX115200E_gate.v(674)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/UART_RX115200E_gate.v(707)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/UART_RX115200E_gate.v(1029)
HDL-5007 WARNING: net 'Fog_TXO[6]' does not have a driver in ../../Src/IFOG/AnyFog.v(40)
HDL-5007 WARNING: net 'Fog_TXO_C[6]' does not have a driver in ../../Src/IFOG/AnyFog.v(42)
HDL-5007 WARNING: input port 'tx_data[7]' remains unconnected for this instance in ../../Src/IFOG/AnyFog.v(46)
HDL-1007 : elaborate module SCHA634(SCLK_FREQ=1000000) in ../../Src/SPI/SCHA634.v(18)
HDL-1007 : elaborate module SPI_SCHA634(SCLK_FREQ=1000000) in ../../Src/SPI/SPI_MASTER.v(17)
HDL-1007 : elaborate module CtrlData in ../../Src/SPI/CtrlData.v(17)
HDL-1007 : elaborate module Time_1ms in ../../Src/SPI/Time_1ms.v(17)
HDL-7007 CRITICAL-WARNING: 'top_state' should be on the sensitivity list in ../../Src/SPI/CtrlData.v(1952)
HDL-7007 CRITICAL-WARNING: 'cnt_time0' should be on the sensitivity list in ../../Src/SPI/CtrlData.v(1953)
HDL-7007 CRITICAL-WARNING: 'cnt_time1' should be on the sensitivity list in ../../Src/SPI/CtrlData.v(1954)
HDL-7007 CRITICAL-WARNING: 'cnt_time2' should be on the sensitivity list in ../../Src/SPI/CtrlData.v(1955)
HDL-7007 CRITICAL-WARNING: 'cnt_time3' should be on the sensitivity list in ../../Src/SPI/CtrlData.v(1956)
HDL-7007 CRITICAL-WARNING: 'cnt_time4' should be on the sensitivity list in ../../Src/SPI/CtrlData.v(1957)
HDL-7007 CRITICAL-WARNING: 'cnt_time5' should be on the sensitivity list in ../../Src/SPI/CtrlData.v(1958)
HDL-7007 CRITICAL-WARNING: 'cnt_time6' should be on the sensitivity list in ../../Src/SPI/CtrlData.v(1959)
HDL-7007 CRITICAL-WARNING: 'cnt_time7' should be on the sensitivity list in ../../Src/SPI/CtrlData.v(1960)
HDL-7007 CRITICAL-WARNING: 'cnt_time8' should be on the sensitivity list in ../../Src/SPI/CtrlData.v(1961)
HDL-7007 Similar messages will be suppressed.
HDL-5007 WARNING: port 'tx_data' is not connected on this instance in ../../Src/UART/COM3_Control.v(37)
HDL-1007 : port 'tx_rdy' remains unconnected for this instance in ../../Src/UART/COM3_Control.v(37)
HDL-1007 : port 'txd' remains unconnected for this instance in ../../Src/UART/COM3_Control.v(37)
HDL-1007 : elaborate module COM3_Control in ../../Src/UART/COM3_Control.v(16)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/UART_RX460800_gate.v(206)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/UART_RX460800_gate.v(461)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/UART_RX460800_gate.v(494)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/UART_RX460800_gate.v(838)
HDL-1007 : elaborate module UART_RX460800 in ../../al_ip/UART_RX460800_gate.v(5)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b010101,EQN="(~A*~(C*B))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b011001100110,EQN="(~(D*C)*(B@A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b0100001111000,EQN="((B*A)*~(C)*~(D)+~((B*A))*C*~(D)+(B*A)*~(C)*D)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b0111110000000,EQN="((B*A)*C*~(D)+~((B*A))*~(C)*D+(B*A)*~(C)*D)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b11011000,EQN="(C*~(B)*~(A)+C*B*~(A)+~(C)*B*A+C*B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b1111011100000000,EQN="(D*~(~C*B*A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b01010100010101010101000001010101,EQN="(~A*~(D*~C*~(E*B)))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b01010101000,EQN="(A*~(D@(~C*~B)))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b01000000000000000000000000000000,EQN="(E*D*C*B*~A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b010101000,EQN="(~D*A*~(~C*~B))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b011111000,EQN="(~D*~(~C*~(B*A)))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT2(INIT=4'b1101,EQN="~(~B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(48)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b1010101011101010,EQN="~(~A*~(~D*C*B))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b01000000000000000000000,EQN="(E*~D*C*~B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b0100000000000000000,EQN="(E*~D*~C*~B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b0100010001000000,EQN="(B*~A*~(~D*~C))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b01111111110000000,EQN="(~E*~(~D*~(C*B*A)))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b10111101,EQN="(~(A)*~(B)*~(C)+~(A)*B*~(C)+A*B*~(C)+~(A)*~(B)*C+A*~(B)*C+A*B*C)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b01000000000000,EQN="(D*C*~B*~A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b0111111000000001010101000000000,EQN="(D*~(~A*~((C*B))*~(E)+~A*(C*B)*~(E)+~(~A)*(C*B)*E+~A*(C*B)*E))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b0101010001111110010101001111111,EQN="(~(A)*~((C*B))*~(D)*~(E)+A*~((C*B))*~(D)*~(E)+~(A)*(C*B)*~(D)*~(E)+A*~((C*B))*D*~(E)+~(A)*~((C*B))*~(D)*E+A*~((C*B))*~(D)*E+A*~((C*B))*D*E)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b10000000000000000000000000000000,EQN="(E*D*C*B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b1000100000001000,EQN="(B*A*~(~D*C))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11111111111110000000000000000000,EQN="(E*~(~D*~C*~(B*A)))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b1100110011011100,EQN="~(~B*~(~D*C*~A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b01010110101010,EQN="(~E*(A*~((C*B))*~(D)+A*(C*B)*~(D)+~(A)*~((C*B))*D))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11111110111011101010101010101010,EQN="~(~A*~(E*~(~B*~(D*C))))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b01100101011000101110011111100,EQN="~(~B*~((E*~(D)*~(A)+E*D*~(A)+~(E)*D*A+E*D*A))*~(C)+~B*(E*~(D)*~(A)+E*D*~(A)+~(E)*D*A+E*D*A)*~(C)+~(~B)*(E*~(D)*~(A)+E*D*~(A)+~(E)*D*A+E*D*A)*C+~B*(E*~(D)*~(A)+E*D*~(A)+~(E)*D*A+E*D*A)*C)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11001100110011001111110011011101,EQN="~(~B*~(~E*(~A*~(C)*~(D)+~A*C*~(D)+~(~A)*C*D+~A*C*D)))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b1111000000100000,EQN="(C*~(~D*~(~B*A)))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b010011000011001101111111,EQN="(~E*~((~D*~(C*A)))*~(B)+~E*(~D*~(C*A))*~(B)+~(~E)*(~D*~(C*A))*B+~E*(~D*~(C*A))*B)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b01,EQN="(~E*~D*~C*~B*~A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b01011111011101110000101000100010,EQN="~(~E*~((B*~(C)*~(D)+B*C*~(D)+~(B)*C*D+B*C*D))*~(A)+~E*(B*~(C)*~(D)+B*C*~(D)+~(B)*C*D+B*C*D)*~(A)+~(~E)*(B*~(C)*~(D)+B*C*~(D)+~(B)*C*D+B*C*D)*A+~E*(B*~(C)*~(D)+B*C*~(D)+~(B)*C*D+B*C*D)*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b1111110101010100,EQN="(~(A)*~((~C*~B))*~(D)+~(A)*~((~C*~B))*D+A*~((~C*~B))*D+~(A)*(~C*~B)*D)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11111111111100110011101000111111,EQN="(~(A)*~(B)*~(C)*~(D)*~(E)+A*~(B)*~(C)*~(D)*~(E)+~(A)*B*~(C)*~(D)*~(E)+A*B*~(C)*~(D)*~(E)+~(A)*~(B)*C*~(D)*~(E)+A*~(B)*C*~(D)*~(E)+A*~(B)*~(C)*D*~(E)+A*B*~(C)*D*~(E)+~(A)*~(B)*C*D*~(E)+A*~(B)*C*D*~(E)+~(A)*~(B)*~(C)*~(D)*E+A*~(B)*~(C)*~(D)*E+~(A)*~(B)*C*~(D)*E+A*~(B)*C*~(D)*E+~(A)*B*C*~(D)*E+A*B*C*~(D)*E+~(A)*~(B)*~(C)*D*E+A*~(B)*~(C)*D*E+~(A)*B*~(C)*D*E+A*B*~(C)*D*E+~(A)*~(B)*C*D*E+A*~(B)*C*D*E+~(A)*B*C*D*E+A*B*C*D*E)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b1010101000110000,EQN="((C*~B)*~(A)*~(D)+(C*~B)*A*~(D)+~((C*~B))*A*D+(C*~B)*A*D)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b10101010111010101100000000000000,EQN="(~(A)*(C*B)*D*~(E)+A*(C*B)*D*~(E)+A*~((C*B))*~(D)*E+~(A)*(C*B)*~(D)*E+A*(C*B)*~(D)*E+A*~((C*B))*D*E+A*(C*B)*D*E)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b1010101100000011,EQN="~(~(~C*~B)*~(D*A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b11010000,EQN="(C*~(~B*A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b01110100,EQN="(~D*~(~C*~(A)*~(B)+~C*A*~(B)+~(~C)*A*B+~C*A*B))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b1011101011000000,EQN="(~(A)*B*C*~(D)+A*B*C*~(D)+A*~(B)*~(C)*D+A*B*~(C)*D+~(A)*~(B)*C*D+A*~(B)*C*D+A*B*C*D)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b0111110100101000,EQN="(D*~((C@B))*~(A)+D*(C@B)*~(A)+~(D)*(C@B)*A+D*(C@B)*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b10111010,EQN="~(~A*~(C*~B))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b01000000000,EQN="(D*~C*~B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11111111001000001101111100000000,EQN="(D*~(E)*~((C*~B*A))+D*E*~((C*~B*A))+~(D)*E*(C*~B*A)+D*E*(C*~B*A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b011010100111010001111110011,EQN="~(B*~((D*~(E)*~(A)+D*E*~(A)+~(D)*E*A+D*E*A))*~(C)+B*(D*~(E)*~(A)+D*E*~(A)+~(D)*E*A+D*E*A)*~(C)+~(B)*(D*~(E)*~(A)+D*E*~(A)+~(D)*E*A+D*E*A)*C+B*(D*~(E)*~(A)+D*E*~(A)+~(D)*E*A+D*E*A)*C)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11111111101000110000000010100011,EQN="((~B*~(A)*~(C)+~B*A*~(C)+~(~B)*A*C+~B*A*C)*~(E)*~(D)+(~B*~(A)*~(C)+~B*A*~(C)+~(~B)*A*C+~B*A*C)*E*~(D)+~((~B*~(A)*~(C)+~B*A*~(C)+~(~B)*A*C+~B*A*C))*E*D+(~B*~(A)*~(C)+~B*A*~(C)+~(~B)*A*C+~B*A*C)*E*D)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11111100101011000101110000001100,EQN="(B*~((D*~(E)*~(A)+D*E*~(A)+~(D)*E*A+D*E*A))*~(C)+B*(D*~(E)*~(A)+D*E*~(A)+~(D)*E*A+D*E*A)*~(C)+~(B)*(D*~(E)*~(A)+D*E*~(A)+~(D)*E*A+D*E*A)*C+B*(D*~(E)*~(A)+D*E*~(A)+~(D)*E*A+D*E*A)*C)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11111111110010100000000011001010,EQN="((A*~(B)*~(C)+A*B*~(C)+~(A)*B*C+A*B*C)*~(E)*~(D)+(A*~(B)*~(C)+A*B*~(C)+~(A)*B*C+A*B*C)*E*~(D)+~((A*~(B)*~(C)+A*B*~(C)+~(A)*B*C+A*B*C))*E*D+(A*~(B)*~(C)+A*B*~(C)+~(A)*B*C+A*B*C)*E*D)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11111111110001010000000011000101,EQN="((~A*~(B)*~(C)+~A*B*~(C)+~(~A)*B*C+~A*B*C)*~(E)*~(D)+(~A*~(B)*~(C)+~A*B*~(C)+~(~A)*B*C+~A*B*C)*E*~(D)+~((~A*~(B)*~(C)+~A*B*~(C)+~(~A)*B*C+~A*B*C))*E*D+(~A*~(B)*~(C)+~A*B*~(C)+~(~A)*B*C+~A*B*C)*E*D)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b01000000000000000,EQN="(~E*D*C*B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/UART_RX460800_gate.v(208)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/UART_RX460800_gate.v(463)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/UART_RX460800_gate.v(496)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/UART_RX460800_gate.v(840)
HDL-1007 : elaborate module Gprmc in ../../Src/GNSS/Gprmc.v(17)
HDL-5007 WARNING: 'GNRMC_STARTdy' is not declared in ../../Src/GNSS/GNRMC_Tx.v(83)
HDL-5007 WARNING: 'GNRMC_STARTdy' is not declared in ../../Src/GNSS/GNRMC_Tx.v(108)
HDL-5007 WARNING: 'fifo_valid' is not declared in ../../Src/GNSS/GNRMC_Tx.v(189)
HDL-1007 : elaborate module GNRMC_Tx in ../../Src/GNSS/GNRMC_Tx.v(17)
HDL-8007 ERROR: external reference 'GNRMC_STARTdy' remains unresolved in ../../Src/GNSS/GNRMC_Tx.v(83)
HDL-1007 : module 'GNRMC_Tx' remains a black box due to errors in its contents in ../../Src/GNSS/GNRMC_Tx.v(17)
HDL-5007 WARNING: input port 'tx_data[7]' remains unconnected for this instance in ../../Src/UART/COM3_Control.v(37)
HDL-1007 : port 'tx_data' remains unconnected for this instance in ../../Src/UART/COM2_Control.v(88)
HDL-1007 : port 'tx_en' remains unconnected for this instance in ../../Src/UART/COM2_Control.v(88)
HDL-1007 : port 'tx_rdy' remains unconnected for this instance in ../../Src/UART/COM2_Control.v(88)
HDL-1007 : port 'txd' remains unconnected for this instance in ../../Src/UART/COM2_Control.v(88)
HDL-1007 : port 'AGRIC_BaseAlt' remains unconnected for this instance in ../../Src/UART/COM2_Control.v(97)
HDL-1007 : port 'HEADING_latch' remains unconnected for this instance in ../../Src/UART/COM2_Control.v(129)
HDL-1007 : port 'STADOP_latch' remains unconnected for this instance in ../../Src/UART/COM2_Control.v(156)
HDL-1007 : port 'PPPdat_valid' remains unconnected for this instance in ../../Src/UART/COM2_Control.v(174)
HDL-1007 : port 'PPPNAV_satanum' remains unconnected for this instance in ../../Src/UART/COM2_Control.v(174)
HDL-1007 : port 'PPPNAV_SVs' remains unconnected for this instance in ../../Src/UART/COM2_Control.v(174)
HDL-1007 : port 'PPPNAV_solnSVs' remains unconnected for this instance in ../../Src/UART/COM2_Control.v(174)
HDL-1007 : elaborate module COM2_Control in ../../Src/UART/COM2_Control.v(16)
HDL-1007 : elaborate module Agrica in ../../Src/GNSS/Agrica.v(18)
HDL-5007 WARNING: actual bit length 16 differs from formal bit length 8 for port 'hGPSData_satanum' in ../../Src/UART/COM2_Control.v(125)
HDL-1007 : elaborate module uniheading in ../../Src/GNSS/uniheading.v(17)
HDL-1007 : elaborate module STADOP in ../../Src/GNSS/STADOP.v(18)
HDL-1007 : elaborate module PPPNAV in ../../Src/GNSS/PPPNAV.v(17)
HDL-5007 WARNING: input port 'tx_data[7]' remains unconnected for this instance in ../../Src/UART/COM2_Control.v(88)
HDL-5007 WARNING: actual bit length 32 differs from formal bit length 8 for port 'AGRIC_SpeedType' in ../../Src/INS600M-21A.v(455)
HDL-1007 : elaborate module I2C_master in ../../Src/IIC/I2C_master.v(17)
HDL-1007 : elaborate module Data_Processing in ../../Src/FMC/Data_Processing.v(16)
HDL-1007 : elaborate module genclk in ../../Src/FMC/genclk.v(16)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(475)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(550)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(1131)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(1648)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(1731)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(2304)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(2821)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(3338)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(3421)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(3994)
HDL-5007 Similar messages will be suppressed.
HDL-1007 : elaborate module Divider in ../../al_ip/Divider_gate.v(5)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b10100010,EQN="(A*~(~C*B))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_ADDER(ALUTYPE="SUB_CARRY") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(72)
HDL-1007 : elaborate module AL_MAP_ADDER(ALUTYPE="SUB") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(72)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b10111000,EQN="(C*~(A)*~(B)+C*A*~(B)+~(C)*A*B+C*A*B)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b11001010,EQN="(A*~(B)*~(C)+A*B*~(C)+~(A)*B*C+A*B*C)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b1011111100000000,EQN="(D*~(C*B*~A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b0100011111011110000000000000000,EQN="(E*~((C*~A)*~(D)*~(B)+(C*~A)*D*~(B)+~((C*~A))*D*B+(C*~A)*D*B))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b010,EQN="(~C*~B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b11100100,EQN="(B*~(C)*~(A)+B*C*~(A)+~(B)*C*A+B*C*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b11100010,EQN="(A*~(C)*~(B)+A*C*~(B)+~(A)*C*B+A*C*B)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b10101100,EQN="(B*~(A)*~(C)+B*A*~(C)+~(B)*A*C+B*A*C)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT1(INIT=2'b01,EQN="(~A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(60)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b10001011,EQN="(~C*~(A)*~(B)+~C*A*~(B)+~(~C)*A*B+~C*A*B)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(477)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(552)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(1133)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(1650)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(1733)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(2306)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(2823)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(3340)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(3423)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(3996)
HDL-5007 Similar messages will be suppressed.
HDL-1007 : elaborate module FMC_Ctrl(DATA_LEN=16'b01101100,Vers_Num=16'b0100000010) in ../../Src/FMC/FMC_Ctrl.v(17)
HDL-5007 WARNING: net 'DACC_Temp[15]' does not have a driver in ../../Src/INS600M-21A.v(139)
HDL-5007 WARNING: net 'gps_valid_rmc' does not have a driver in ../../Src/INS600M-21A.v(189)
HDL-5007 WARNING: net 'gps_valid_head' does not have a driver in ../../Src/INS600M-21A.v(190)
HDL-5007 WARNING: net 'FMC_DACC_MdataX[31]' does not have a driver in ../../Src/INS600M-21A.v(227)
HDL-5007 WARNING: net 'FMC_DACC_MdataY[31]' does not have a driver in ../../Src/INS600M-21A.v(228)
HDL-5007 WARNING: net 'FMC_DACC_MdataZ[31]' does not have a driver in ../../Src/INS600M-21A.v(229)
HDL-5007 WARNING: net 'DACC_valid' does not have a driver in ../../Src/INS600M-21A.v(520)
HDL-5007 WARNING: input port 'DACC_dataX[31]' remains unconnected for this instance in ../../Src/INS600M-21A.v(498)
HDL-5007 WARNING: input port 'Modle_state[7]' is not connected on this instance in ../../Src/INS600M-21A.v(693)
HDL-5007 WARNING: input port 'FMC_DACC_MdataX[31]' remains unconnected for this instance in ../../Src/INS600M-21A.v(693)
RUN-1002 : start command "backup_run_log run.log ../.logs/syn_1/td_20250710_200923.log"

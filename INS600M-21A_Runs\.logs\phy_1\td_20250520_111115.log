============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Tue May 20 11:11:15 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(247)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(252)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(273)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(278)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(421)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(428)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(435)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(442)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(449)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(456)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(463)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(470)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(647)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(652)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(680)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(685)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(919)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(926)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(933)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(940)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(947)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(952)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(959)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(966)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(973)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(980)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(514)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
RUN-1001 : Project manager successfully analyzed 21 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  6.273433s wall, 1.593750s user + 4.687500s system = 6.281250s CPU (100.1%)

RUN-1004 : used memory is 78 MB, reserved memory is 40 MB, peak memory is 89 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  2.023512s wall, 1.890625s user + 0.125000s system = 2.015625s CPU (99.6%)

RUN-1004 : used memory is 298 MB, reserved memory is 267 MB, peak memory is 301 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 7 view nodes, 47 trigger nets, 47 data nets.
KIT-1004 : Chipwatcher code = 0001111110010110
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=144) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=144) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=144)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=144)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22879/31 useful/useless nets, 19663/17 useful/useless insts
SYN-1016 : Merged 36 instances.
SYN-1032 : 22504/22 useful/useless nets, 20123/18 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 4 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 4 mux instances.
SYN-1015 : Optimize round 1, 443 better
SYN-1014 : Optimize round 2
SYN-1032 : 22132/60 useful/useless nets, 19751/64 useful/useless insts
SYN-1015 : Optimize round 2, 128 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.908219s wall, 2.843750s user + 0.078125s system = 2.921875s CPU (100.5%)

RUN-1004 : used memory is 326 MB, reserved memory is 293 MB, peak memory is 328 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22192/367 useful/useless nets, 19852/52 useful/useless insts
SYN-1016 : Merged 57 instances.
SYN-2571 : Optimize after map_dsp, round 1, 476 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 44 instances.
SYN-2501 : Optimize round 1, 90 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 18 macro adder
SYN-1019 : Optimized 17 mux instances.
SYN-1016 : Merged 12 instances.
SYN-1032 : 22663/5 useful/useless nets, 20323/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83239, tnet num: 22663, tinst num: 20322, tnode num: 116861, tedge num: 129639.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.452073s wall, 1.421875s user + 0.031250s system = 1.453125s CPU (100.1%)

RUN-1004 : used memory is 468 MB, reserved memory is 437 MB, peak memory is 468 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22663 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 257 (3.41), #lev = 7 (1.78)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 257 (3.41), #lev = 7 (1.78)
SYN-3001 : Logic optimization runtime opt =   0.04 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 596 instances into 257 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 450 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 140 adder to BLE ...
SYN-4008 : Packed 140 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  5.450602s wall, 5.265625s user + 0.187500s system = 5.453125s CPU (100.0%)

RUN-1004 : used memory is 352 MB, reserved memory is 320 MB, peak memory is 578 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  8.729366s wall, 8.453125s user + 0.296875s system = 8.750000s CPU (100.2%)

RUN-1004 : used memory is 352 MB, reserved memory is 321 MB, peak memory is 578 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/rmc_com2/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/rmc_com2/GPRMC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/rmc_com2/GPRMC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/rmc_com2/GPRMC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/rmc_com2/GPRMC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/rmc_com2/GPRMC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/rmc_com2/GPRMC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/rmc_com2/GPRMC_data[0]
SYN-5055 WARNING: The kept net COM2/rx_err will be merged to another kept net COM2/rmc_com2/GPRMC_err
SYN-5055 WARNING: The kept net FMC/Error_state[0] will be merged to another kept net COM2/rmc_com2/GPRMC_sub_state_cnt0[3]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (306 clock/control pins, 0 other pins).
SYN-4027 : Net DATA/DIV_Dtemp/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net DATA/DIV_Dtemp/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19650 instances
RUN-0007 : 5646 luts, 12473 seqs, 933 mslices, 491 lslices, 60 pads, 42 brams, 0 dsps
RUN-1001 : There are total 22014 nets
RUN-1001 : 16520 nets have 2 pins
RUN-1001 : 4355 nets have [3 - 5] pins
RUN-1001 : 785 nets have [6 - 10] pins
RUN-1001 : 224 nets have [11 - 20] pins
RUN-1001 : 106 nets have [21 - 99] pins
RUN-1001 : 24 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4744     
RUN-1001 :   No   |  No   |  Yes  |     628     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    6531     
RUN-1001 :   Yes  |  No   |  Yes  |     470     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19648 instances, 5646 luts, 12473 seqs, 1424 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1131 pins
PHY-3001 : Huge net DATA/done_div with 1650 pins
PHY-0007 : Cell area utilization is 64%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81770, tnet num: 22012, tinst num: 19648, tnode num: 115490, tedge num: 128450.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.275084s wall, 1.250000s user + 0.031250s system = 1.281250s CPU (100.5%)

RUN-1004 : used memory is 528 MB, reserved memory is 499 MB, peak memory is 578 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22012 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.255467s wall, 2.234375s user + 0.031250s system = 2.265625s CPU (100.5%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.40091e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19648.
PHY-3001 : Level 1 #clusters 2112.
PHY-3001 : End clustering;  0.168915s wall, 0.296875s user + 0.031250s system = 0.328125s CPU (194.3%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 64%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 885824, overlap = 613.156
PHY-3002 : Step(2): len = 822353, overlap = 682.5
PHY-3002 : Step(3): len = 534159, overlap = 859.031
PHY-3002 : Step(4): len = 469761, overlap = 935.75
PHY-3002 : Step(5): len = 369807, overlap = 1036.84
PHY-3002 : Step(6): len = 332286, overlap = 1074.22
PHY-3002 : Step(7): len = 275218, overlap = 1151.5
PHY-3002 : Step(8): len = 241520, overlap = 1214.62
PHY-3002 : Step(9): len = 212334, overlap = 1259.91
PHY-3002 : Step(10): len = 194865, overlap = 1278.5
PHY-3002 : Step(11): len = 176115, overlap = 1323.31
PHY-3002 : Step(12): len = 162099, overlap = 1371.75
PHY-3002 : Step(13): len = 148636, overlap = 1424.47
PHY-3002 : Step(14): len = 137427, overlap = 1463.88
PHY-3002 : Step(15): len = 128884, overlap = 1478.72
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.2852e-06
PHY-3002 : Step(16): len = 139196, overlap = 1446.69
PHY-3002 : Step(17): len = 198393, overlap = 1297.88
PHY-3002 : Step(18): len = 205588, overlap = 1190.62
PHY-3002 : Step(19): len = 204405, overlap = 1137.84
PHY-3002 : Step(20): len = 199231, overlap = 1126.53
PHY-3002 : Step(21): len = 195953, overlap = 1067.72
PHY-3002 : Step(22): len = 191104, overlap = 1063.91
PHY-3002 : Step(23): len = 186645, overlap = 1050.47
PHY-3002 : Step(24): len = 181136, overlap = 1043.31
PHY-3002 : Step(25): len = 178300, overlap = 1048.09
PHY-3002 : Step(26): len = 172782, overlap = 1064.47
PHY-3002 : Step(27): len = 170813, overlap = 1064.34
PHY-3002 : Step(28): len = 168836, overlap = 1064.56
PHY-3002 : Step(29): len = 168306, overlap = 1072.81
PHY-3002 : Step(30): len = 167209, overlap = 1075.75
PHY-3002 : Step(31): len = 166069, overlap = 1074.59
PHY-3002 : Step(32): len = 165514, overlap = 1089.38
PHY-3002 : Step(33): len = 165619, overlap = 1103.47
PHY-3002 : Step(34): len = 163797, overlap = 1099.06
PHY-3002 : Step(35): len = 163309, overlap = 1089.19
PHY-3002 : Step(36): len = 161869, overlap = 1072.34
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.5704e-06
PHY-3002 : Step(37): len = 167196, overlap = 1039.47
PHY-3002 : Step(38): len = 177752, overlap = 990.188
PHY-3002 : Step(39): len = 179964, overlap = 967.031
PHY-3002 : Step(40): len = 182303, overlap = 953.281
PHY-3002 : Step(41): len = 182223, overlap = 952.5
PHY-3002 : Step(42): len = 182535, overlap = 943.438
PHY-3002 : Step(43): len = 181566, overlap = 932.969
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 5.1408e-06
PHY-3002 : Step(44): len = 191788, overlap = 884.312
PHY-3002 : Step(45): len = 203981, overlap = 812.781
PHY-3002 : Step(46): len = 209574, overlap = 733.688
PHY-3002 : Step(47): len = 212248, overlap = 677.125
PHY-3002 : Step(48): len = 212329, overlap = 650.344
PHY-3002 : Step(49): len = 212353, overlap = 658.656
PHY-3002 : Step(50): len = 211125, overlap = 669.312
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 1.02816e-05
PHY-3002 : Step(51): len = 223736, overlap = 636.031
PHY-3002 : Step(52): len = 240036, overlap = 572.375
PHY-3002 : Step(53): len = 244725, overlap = 553.656
PHY-3002 : Step(54): len = 245451, overlap = 541.5
PHY-3002 : Step(55): len = 245633, overlap = 581.469
PHY-3002 : Step(56): len = 244672, overlap = 583.062
PHY-3002 : Step(57): len = 243165, overlap = 587.969
PHY-3002 : Step(58): len = 241982, overlap = 580.812
PHY-3002 : Step(59): len = 241461, overlap = 575.062
PHY-3002 : Step(60): len = 239839, overlap = 577.594
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 2.05632e-05
PHY-3002 : Step(61): len = 248939, overlap = 544.312
PHY-3002 : Step(62): len = 259599, overlap = 515.969
PHY-3002 : Step(63): len = 263045, overlap = 465.625
PHY-3002 : Step(64): len = 265032, overlap = 450.188
PHY-3002 : Step(65): len = 264485, overlap = 449.688
PHY-3002 : Step(66): len = 263052, overlap = 446.938
PHY-3002 : Step(67): len = 260713, overlap = 445.5
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 4.11264e-05
PHY-3002 : Step(68): len = 269818, overlap = 394.406
PHY-3002 : Step(69): len = 278463, overlap = 369.281
PHY-3002 : Step(70): len = 282366, overlap = 333.094
PHY-3002 : Step(71): len = 284777, overlap = 294.969
PHY-3002 : Step(72): len = 284111, overlap = 307.938
PHY-3002 : Step(73): len = 282797, overlap = 319.594
PHY-3002 : Step(74): len = 280315, overlap = 309.344
PHY-3002 : Step(75): len = 278753, overlap = 305.469
PHY-3002 : Step(76): len = 277731, overlap = 314.625
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 8.22528e-05
PHY-3002 : Step(77): len = 285710, overlap = 277.562
PHY-3002 : Step(78): len = 292382, overlap = 275.344
PHY-3002 : Step(79): len = 295289, overlap = 281.719
PHY-3002 : Step(80): len = 296406, overlap = 271.5
PHY-3002 : Step(81): len = 296928, overlap = 273.969
PHY-3002 : Step(82): len = 296339, overlap = 274.062
PHY-3002 : Step(83): len = 294631, overlap = 291.281
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000156438
PHY-3002 : Step(84): len = 298616, overlap = 281.469
PHY-3002 : Step(85): len = 304708, overlap = 277.438
PHY-3002 : Step(86): len = 306618, overlap = 293.594
PHY-3002 : Step(87): len = 307788, overlap = 290.031
PHY-3002 : Step(88): len = 307463, overlap = 286.906
PHY-3002 : Step(89): len = 307629, overlap = 265.812
PHY-3002 : Step(90): len = 306329, overlap = 263.438
PHY-3002 : Step(91): len = 306440, overlap = 261.312
PHY-3002 : Step(92): len = 306797, overlap = 259.969
PHY-3002 : Step(93): len = 306404, overlap = 262.219
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000286048
PHY-3002 : Step(94): len = 308655, overlap = 258.344
PHY-3002 : Step(95): len = 311969, overlap = 252.844
PHY-3002 : Step(96): len = 312481, overlap = 244.875
PHY-3002 : Step(97): len = 314122, overlap = 237.594
PHY-3002 : Step(98): len = 314191, overlap = 228.531
PHY-3002 : Step(99): len = 313624, overlap = 234.25
PHY-3002 : Step(100): len = 313081, overlap = 241.531
PHY-3002 : Step(101): len = 312950, overlap = 239.688
PHY-3002 : Step(102): len = 312604, overlap = 243.406
PHY-3002 : Step(103): len = 313050, overlap = 261.094
PHY-3002 : Step(104): len = 312662, overlap = 272.125
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.013931s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (224.3%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/22014.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 425752, over cnt = 1230(3%), over = 5529, worst = 34
PHY-1001 : End global iterations;  0.901095s wall, 1.234375s user + 0.093750s system = 1.328125s CPU (147.4%)

PHY-1001 : Congestion index: top1 = 74.31, top5 = 52.92, top10 = 43.36, top15 = 37.71.
PHY-3001 : End congestion estimation;  1.139678s wall, 1.468750s user + 0.093750s system = 1.562500s CPU (137.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22012 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.031181s wall, 0.984375s user + 0.046875s system = 1.031250s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.60414e-05
PHY-3002 : Step(105): len = 356988, overlap = 211.688
PHY-3002 : Step(106): len = 368906, overlap = 196.875
PHY-3002 : Step(107): len = 371266, overlap = 195.812
PHY-3002 : Step(108): len = 371856, overlap = 186.125
PHY-3002 : Step(109): len = 377317, overlap = 184.438
PHY-3002 : Step(110): len = 383825, overlap = 170.438
PHY-3002 : Step(111): len = 387420, overlap = 169.094
PHY-3002 : Step(112): len = 390824, overlap = 159.938
PHY-3002 : Step(113): len = 393157, overlap = 159.969
PHY-3002 : Step(114): len = 395114, overlap = 152.25
PHY-3002 : Step(115): len = 397311, overlap = 144.281
PHY-3002 : Step(116): len = 400496, overlap = 139.062
PHY-3002 : Step(117): len = 403216, overlap = 135.031
PHY-3002 : Step(118): len = 405636, overlap = 134.156
PHY-3002 : Step(119): len = 407098, overlap = 131.344
PHY-3002 : Step(120): len = 409324, overlap = 130.844
PHY-3002 : Step(121): len = 412267, overlap = 121.844
PHY-3002 : Step(122): len = 412764, overlap = 119.281
PHY-3002 : Step(123): len = 413537, overlap = 122.188
PHY-3002 : Step(124): len = 415291, overlap = 125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000192083
PHY-3002 : Step(125): len = 415653, overlap = 113.781
PHY-3002 : Step(126): len = 418000, overlap = 107.875
PHY-3002 : Step(127): len = 419900, overlap = 105.438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000384166
PHY-3002 : Step(128): len = 426236, overlap = 95.0625
PHY-3002 : Step(129): len = 433210, overlap = 96.1875
PHY-3002 : Step(130): len = 437019, overlap = 98.2812
PHY-3002 : Step(131): len = 439398, overlap = 95.6562
PHY-3002 : Step(132): len = 441974, overlap = 91.3438
PHY-3002 : Step(133): len = 441972, overlap = 91.875
PHY-3002 : Step(134): len = 441756, overlap = 92
PHY-3002 : Step(135): len = 442240, overlap = 91.4688
PHY-3002 : Step(136): len = 442206, overlap = 98.5
PHY-3002 : Step(137): len = 441697, overlap = 98.0625
PHY-3002 : Step(138): len = 441867, overlap = 100.906
PHY-3002 : Step(139): len = 442963, overlap = 105.094
PHY-3002 : Step(140): len = 443811, overlap = 107.531
PHY-3002 : Step(141): len = 444075, overlap = 110.219
PHY-3002 : Step(142): len = 445428, overlap = 114.594
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000768332
PHY-3002 : Step(143): len = 444860, overlap = 105.094
PHY-3002 : Step(144): len = 447446, overlap = 104.156
PHY-3002 : Step(145): len = 448957, overlap = 105.344
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.0014775
PHY-3002 : Step(146): len = 449272, overlap = 96.5
PHY-3002 : Step(147): len = 455080, overlap = 101.844
PHY-3002 : Step(148): len = 468625, overlap = 101.594
PHY-3002 : Step(149): len = 471630, overlap = 98.9062
PHY-3002 : Step(150): len = 471004, overlap = 103.219
PHY-3002 : Step(151): len = 470771, overlap = 104.5
PHY-3002 : Step(152): len = 468356, overlap = 106.688
PHY-3002 : Step(153): len = 466075, overlap = 104.625
PHY-3002 : Step(154): len = 465941, overlap = 102.344
PHY-3002 : Step(155): len = 463751, overlap = 98.4062
PHY-3002 : Step(156): len = 463416, overlap = 95.5312
PHY-3002 : Step(157): len = 462624, overlap = 97.5938
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 50/22014.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 536080, over cnt = 2193(6%), over = 11073, worst = 37
PHY-1001 : End global iterations;  1.077769s wall, 1.843750s user + 0.031250s system = 1.875000s CPU (174.0%)

PHY-1001 : Congestion index: top1 = 84.94, top5 = 64.55, top10 = 54.50, top15 = 48.30.
PHY-3001 : End congestion estimation;  1.424228s wall, 2.187500s user + 0.031250s system = 2.218750s CPU (155.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22012 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.249196s wall, 1.250000s user + 0.000000s system = 1.250000s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.90512e-05
PHY-3002 : Step(158): len = 471195, overlap = 370.531
PHY-3002 : Step(159): len = 472563, overlap = 334.062
PHY-3002 : Step(160): len = 468335, overlap = 305.156
PHY-3002 : Step(161): len = 461259, overlap = 298.406
PHY-3002 : Step(162): len = 456199, overlap = 277.688
PHY-3002 : Step(163): len = 454036, overlap = 263.781
PHY-3002 : Step(164): len = 450545, overlap = 253.281
PHY-3002 : Step(165): len = 449240, overlap = 246.875
PHY-3002 : Step(166): len = 448395, overlap = 252.188
PHY-3002 : Step(167): len = 446930, overlap = 250.594
PHY-3002 : Step(168): len = 446471, overlap = 241.219
PHY-3002 : Step(169): len = 443937, overlap = 241.375
PHY-3002 : Step(170): len = 442266, overlap = 233.938
PHY-3002 : Step(171): len = 441666, overlap = 230.844
PHY-3002 : Step(172): len = 439017, overlap = 226.312
PHY-3002 : Step(173): len = 437173, overlap = 230.594
PHY-3002 : Step(174): len = 435920, overlap = 235.781
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000198102
PHY-3002 : Step(175): len = 435426, overlap = 229.344
PHY-3002 : Step(176): len = 436927, overlap = 224.625
PHY-3002 : Step(177): len = 437900, overlap = 218.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000396205
PHY-3002 : Step(178): len = 440202, overlap = 215.688
PHY-3002 : Step(179): len = 445495, overlap = 196.906
PHY-3002 : Step(180): len = 449758, overlap = 185.906
PHY-3002 : Step(181): len = 452629, overlap = 180.375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00079241
PHY-3002 : Step(182): len = 452820, overlap = 179.906
PHY-3002 : Step(183): len = 455453, overlap = 173.406
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81770, tnet num: 22012, tinst num: 19648, tnode num: 115490, tedge num: 128450.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.612547s wall, 1.546875s user + 0.062500s system = 1.609375s CPU (99.8%)

RUN-1004 : used memory is 568 MB, reserved memory is 543 MB, peak memory is 704 MB
OPT-1001 : Total overflow 540.72 peak overflow 3.88
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 364/22014.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 541488, over cnt = 2521(7%), over = 9186, worst = 23
PHY-1001 : End global iterations;  1.286749s wall, 2.140625s user + 0.046875s system = 2.187500s CPU (170.0%)

PHY-1001 : Congestion index: top1 = 60.97, top5 = 49.62, top10 = 44.21, top15 = 40.82.
PHY-1001 : End incremental global routing;  1.551409s wall, 2.406250s user + 0.046875s system = 2.453125s CPU (158.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22012 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.244752s wall, 1.218750s user + 0.031250s system = 1.250000s CPU (100.4%)

OPT-1001 : 22 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19563 has valid locations, 300 needs to be replaced
PHY-3001 : design contains 19926 instances, 5789 luts, 12608 seqs, 1424 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 475595
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17446/22292.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 562192, over cnt = 2573(7%), over = 9288, worst = 23
PHY-1001 : End global iterations;  0.240365s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (136.5%)

PHY-1001 : Congestion index: top1 = 61.23, top5 = 50.16, top10 = 44.68, top15 = 41.32.
PHY-3001 : End congestion estimation;  0.507217s wall, 0.593750s user + 0.000000s system = 0.593750s CPU (117.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82742, tnet num: 22290, tinst num: 19926, tnode num: 116784, tedge num: 129838.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.789572s wall, 1.750000s user + 0.046875s system = 1.796875s CPU (100.4%)

RUN-1004 : used memory is 614 MB, reserved memory is 610 MB, peak memory is 705 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22290 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.078161s wall, 3.031250s user + 0.062500s system = 3.093750s CPU (100.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(184): len = 475798, overlap = 6.5
PHY-3002 : Step(185): len = 476757, overlap = 6.5625
PHY-3002 : Step(186): len = 477442, overlap = 6.4375
PHY-3002 : Step(187): len = 477976, overlap = 6.5
PHY-3002 : Step(188): len = 478652, overlap = 6.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17483/22292.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 558600, over cnt = 2612(7%), over = 9413, worst = 23
PHY-1001 : End global iterations;  0.214363s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (109.3%)

PHY-1001 : Congestion index: top1 = 62.63, top5 = 51.02, top10 = 45.29, top15 = 41.78.
PHY-3001 : End congestion estimation;  0.489167s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (105.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22290 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.106873s wall, 1.078125s user + 0.031250s system = 1.109375s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000644604
PHY-3002 : Step(189): len = 478753, overlap = 176.062
PHY-3002 : Step(190): len = 479519, overlap = 175.969
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00128921
PHY-3002 : Step(191): len = 479710, overlap = 175.906
PHY-3002 : Step(192): len = 480339, overlap = 175.875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00257842
PHY-3002 : Step(193): len = 480544, overlap = 176
PHY-3002 : Step(194): len = 480949, overlap = 175.688
PHY-3001 : Final: Len = 480949, Over = 175.688
PHY-3001 : End incremental placement;  6.357192s wall, 6.734375s user + 0.328125s system = 7.062500s CPU (111.1%)

OPT-1001 : Total overflow 546.53 peak overflow 3.88
OPT-1001 : End high-fanout net optimization;  9.748520s wall, 11.125000s user + 0.406250s system = 11.531250s CPU (118.3%)

OPT-1001 : Current memory(MB): used = 711, reserve = 691, peak = 728.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17471/22292.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 562208, over cnt = 2548(7%), over = 8690, worst = 23
PHY-1002 : len = 614184, over cnt = 1832(5%), over = 4177, worst = 21
PHY-1002 : len = 641512, over cnt = 949(2%), over = 2169, worst = 15
PHY-1002 : len = 661960, over cnt = 451(1%), over = 957, worst = 15
PHY-1002 : len = 679280, over cnt = 2(0%), over = 2, worst = 1
PHY-1001 : End global iterations;  1.532908s wall, 2.250000s user + 0.015625s system = 2.265625s CPU (147.8%)

PHY-1001 : Congestion index: top1 = 51.44, top5 = 45.46, top10 = 42.21, top15 = 40.03.
OPT-1001 : End congestion update;  1.821697s wall, 2.515625s user + 0.015625s system = 2.531250s CPU (139.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22290 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.965998s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (100.3%)

OPT-0007 : Start: WNS 3869 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.794620s wall, 3.500000s user + 0.015625s system = 3.515625s CPU (125.8%)

OPT-1001 : Current memory(MB): used = 687, reserve = 670, peak = 728.
OPT-1001 : End physical optimization;  14.521839s wall, 16.687500s user + 0.484375s system = 17.171875s CPU (118.2%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5789 LUT to BLE ...
SYN-4008 : Packed 5789 LUT and 2862 SEQ to BLE.
SYN-4003 : Packing 9746 remaining SEQ's ...
SYN-4005 : Packed 3322 SEQ with LUT/SLICE
SYN-4006 : 107 single LUT's are left
SYN-4006 : 6424 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 12213/13864 primitive instances ...
PHY-3001 : End packing;  3.064175s wall, 3.062500s user + 0.000000s system = 3.062500s CPU (99.9%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8202 instances
RUN-1001 : 4047 mslices, 4048 lslices, 60 pads, 42 brams, 0 dsps
RUN-1001 : There are total 19471 nets
RUN-1001 : 13613 nets have 2 pins
RUN-1001 : 4459 nets have [3 - 5] pins
RUN-1001 : 873 nets have [6 - 10] pins
RUN-1001 : 380 nets have [11 - 20] pins
RUN-1001 : 136 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
PHY-3001 : design contains 8200 instances, 8095 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Cell area utilization is 86%
PHY-3001 : After packing: Len = 497495, Over = 402.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7825/19471.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 635176, over cnt = 1652(4%), over = 2679, worst = 8
PHY-1002 : len = 642384, over cnt = 1109(3%), over = 1506, worst = 7
PHY-1002 : len = 653576, over cnt = 531(1%), over = 701, worst = 6
PHY-1002 : len = 662848, over cnt = 128(0%), over = 174, worst = 5
PHY-1002 : len = 666032, over cnt = 1(0%), over = 1, worst = 1
PHY-1001 : End global iterations;  1.409840s wall, 2.265625s user + 0.078125s system = 2.343750s CPU (166.2%)

PHY-1001 : Congestion index: top1 = 51.79, top5 = 45.19, top10 = 41.75, top15 = 39.40.
PHY-3001 : End congestion estimation;  1.766446s wall, 2.625000s user + 0.078125s system = 2.703125s CPU (153.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68192, tnet num: 19469, tinst num: 8200, tnode num: 92883, tedge num: 112244.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.937242s wall, 1.906250s user + 0.031250s system = 1.937500s CPU (100.0%)

RUN-1004 : used memory is 608 MB, reserved memory is 604 MB, peak memory is 728 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19469 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.975494s wall, 2.921875s user + 0.062500s system = 2.984375s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.41064e-05
PHY-3002 : Step(195): len = 499370, overlap = 391.75
PHY-3002 : Step(196): len = 496798, overlap = 403.75
PHY-3002 : Step(197): len = 497089, overlap = 420.5
PHY-3002 : Step(198): len = 499777, overlap = 424.75
PHY-3002 : Step(199): len = 499000, overlap = 437.5
PHY-3002 : Step(200): len = 497756, overlap = 445
PHY-3002 : Step(201): len = 495671, overlap = 448
PHY-3002 : Step(202): len = 493815, overlap = 439.75
PHY-3002 : Step(203): len = 492535, overlap = 442.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.82127e-05
PHY-3002 : Step(204): len = 496240, overlap = 426.75
PHY-3002 : Step(205): len = 500226, overlap = 413.5
PHY-3002 : Step(206): len = 499397, overlap = 411.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00017509
PHY-3002 : Step(207): len = 507978, overlap = 384.5
PHY-3002 : Step(208): len = 519777, overlap = 375
PHY-3002 : Step(209): len = 517291, overlap = 368.25
PHY-3002 : Step(210): len = 515178, overlap = 367.5
PHY-3002 : Step(211): len = 515264, overlap = 366
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000296592
PHY-3002 : Step(212): len = 521713, overlap = 362
PHY-3002 : Step(213): len = 528206, overlap = 352.75
PHY-3002 : Step(214): len = 530064, overlap = 351.5
PHY-3002 : Step(215): len = 530879, overlap = 344.25
PHY-3002 : Step(216): len = 532353, overlap = 342.75
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(217): len = 536318, overlap = 337.25
PHY-3002 : Step(218): len = 539018, overlap = 335.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.756639s wall, 0.828125s user + 0.828125s system = 1.656250s CPU (218.9%)

PHY-3001 : Trial Legalized: Len = 640549
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 433/19471.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 731104, over cnt = 2407(6%), over = 3985, worst = 8
PHY-1002 : len = 747672, over cnt = 1428(4%), over = 1963, worst = 7
PHY-1002 : len = 763480, over cnt = 654(1%), over = 863, worst = 6
PHY-1002 : len = 770056, over cnt = 338(0%), over = 431, worst = 4
PHY-1002 : len = 777320, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.350599s wall, 3.750000s user + 0.093750s system = 3.843750s CPU (163.5%)

PHY-1001 : Congestion index: top1 = 50.28, top5 = 45.19, top10 = 42.43, top15 = 40.70.
PHY-3001 : End congestion estimation;  2.756969s wall, 4.156250s user + 0.093750s system = 4.250000s CPU (154.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19469 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.021561s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (100.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000187535
PHY-3002 : Step(219): len = 596600, overlap = 78.25
PHY-3002 : Step(220): len = 577320, overlap = 122
PHY-3002 : Step(221): len = 563247, overlap = 175.75
PHY-3002 : Step(222): len = 555249, overlap = 214.5
PHY-3002 : Step(223): len = 550351, overlap = 234.75
PHY-3002 : Step(224): len = 547105, overlap = 252
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00037507
PHY-3002 : Step(225): len = 550608, overlap = 251.25
PHY-3002 : Step(226): len = 554072, overlap = 247
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00075014
PHY-3002 : Step(227): len = 556054, overlap = 245.25
PHY-3002 : Step(228): len = 561408, overlap = 237
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.036692s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (127.8%)

PHY-3001 : Legalized: Len = 607109, Over = 0
PHY-3001 : Spreading special nets. 54 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.091335s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (85.5%)

PHY-3001 : 85 instances has been re-located, deltaX = 21, deltaY = 44, maxDist = 2.
PHY-3001 : Final: Len = 607971, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68192, tnet num: 19469, tinst num: 8200, tnode num: 92883, tedge num: 112244.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.158822s wall, 2.140625s user + 0.015625s system = 2.156250s CPU (99.9%)

RUN-1004 : used memory is 621 MB, reserved memory is 624 MB, peak memory is 728 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 4708/19471.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 712568, over cnt = 2238(6%), over = 3498, worst = 6
PHY-1002 : len = 723544, over cnt = 1419(4%), over = 1922, worst = 5
PHY-1002 : len = 738792, over cnt = 581(1%), over = 764, worst = 5
PHY-1002 : len = 747992, over cnt = 105(0%), over = 139, worst = 4
PHY-1002 : len = 751032, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.079755s wall, 3.312500s user + 0.000000s system = 3.312500s CPU (159.3%)

PHY-1001 : Congestion index: top1 = 48.25, top5 = 43.80, top10 = 41.28, top15 = 39.56.
PHY-1001 : End incremental global routing;  2.434324s wall, 3.656250s user + 0.000000s system = 3.656250s CPU (150.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19469 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.282354s wall, 1.281250s user + 0.000000s system = 1.281250s CPU (99.9%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8136 has valid locations, 11 needs to be replaced
PHY-3001 : design contains 8210 instances, 8105 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 611226
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17563/19480.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 755880, over cnt = 16(0%), over = 22, worst = 4
PHY-1002 : len = 755864, over cnt = 12(0%), over = 12, worst = 1
PHY-1002 : len = 755864, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 755944, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 756064, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.768934s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (101.6%)

PHY-1001 : Congestion index: top1 = 48.28, top5 = 43.91, top10 = 41.44, top15 = 39.71.
PHY-3001 : End congestion estimation;  1.108684s wall, 1.125000s user + 0.000000s system = 1.125000s CPU (101.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68261, tnet num: 19478, tinst num: 8210, tnode num: 92962, tedge num: 112322.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.168503s wall, 2.171875s user + 0.000000s system = 2.171875s CPU (100.2%)

RUN-1004 : used memory is 652 MB, reserved memory is 646 MB, peak memory is 728 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19478 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.224112s wall, 3.234375s user + 0.000000s system = 3.234375s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(229): len = 611138, overlap = 0.25
PHY-3002 : Step(230): len = 610982, overlap = 0
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17560/19480.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 754712, over cnt = 8(0%), over = 12, worst = 3
PHY-1002 : len = 754704, over cnt = 12(0%), over = 13, worst = 2
PHY-1002 : len = 754704, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 754768, over cnt = 1(0%), over = 1, worst = 1
PHY-1001 : End global iterations;  0.615988s wall, 0.671875s user + 0.046875s system = 0.718750s CPU (116.7%)

PHY-1001 : Congestion index: top1 = 48.45, top5 = 43.95, top10 = 41.46, top15 = 39.73.
PHY-3001 : End congestion estimation;  0.966046s wall, 1.000000s user + 0.046875s system = 1.046875s CPU (108.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19478 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.988452s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (101.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00108306
PHY-3002 : Step(231): len = 610909, overlap = 1
PHY-3002 : Step(232): len = 610860, overlap = 0.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007390s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 610916, Over = 0
PHY-3001 : End spreading;  0.089150s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (105.2%)

PHY-3001 : Final: Len = 610916, Over = 0
PHY-3001 : End incremental placement;  6.984422s wall, 7.218750s user + 0.109375s system = 7.328125s CPU (104.9%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  11.271107s wall, 12.906250s user + 0.109375s system = 13.015625s CPU (115.5%)

OPT-1001 : Current memory(MB): used = 722, reserve = 707, peak = 728.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17560/19480.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 754904, over cnt = 12(0%), over = 16, worst = 3
PHY-1002 : len = 754848, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 754968, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 754968, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 755000, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.802527s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (101.2%)

PHY-1001 : Congestion index: top1 = 48.43, top5 = 43.90, top10 = 41.39, top15 = 39.65.
OPT-1001 : End congestion update;  1.143951s wall, 1.156250s user + 0.000000s system = 1.156250s CPU (101.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19478 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.849935s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (99.3%)

OPT-0007 : Start: WNS 3975 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.999428s wall, 2.015625s user + 0.000000s system = 2.015625s CPU (100.8%)

OPT-1001 : Current memory(MB): used = 722, reserve = 707, peak = 728.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19478 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.844695s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (99.9%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17573/19480.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 755000, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.130161s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (96.0%)

PHY-1001 : Congestion index: top1 = 48.43, top5 = 43.90, top10 = 41.39, top15 = 39.65.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19478 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.850643s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (101.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3975 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 47.896552
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3975ps with logic level 4 
OPT-1001 : End physical optimization;  17.875939s wall, 19.484375s user + 0.140625s system = 19.625000s CPU (109.8%)

RUN-1003 : finish command "place" in  79.416090s wall, 145.828125s user + 7.781250s system = 153.609375s CPU (193.4%)

RUN-1004 : used memory is 636 MB, reserved memory is 633 MB, peak memory is 728 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.886297s wall, 3.187500s user + 0.031250s system = 3.218750s CPU (170.6%)

RUN-1004 : used memory is 637 MB, reserved memory is 633 MB, peak memory is 728 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8212 instances
RUN-1001 : 4047 mslices, 4058 lslices, 60 pads, 42 brams, 0 dsps
RUN-1001 : There are total 19480 nets
RUN-1001 : 13612 nets have 2 pins
RUN-1001 : 4460 nets have [3 - 5] pins
RUN-1001 : 877 nets have [6 - 10] pins
RUN-1001 : 386 nets have [11 - 20] pins
RUN-1001 : 135 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68261, tnet num: 19478, tinst num: 8210, tnode num: 92962, tedge num: 112322.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.894683s wall, 1.906250s user + 0.000000s system = 1.906250s CPU (100.6%)

RUN-1004 : used memory is 617 MB, reserved memory is 612 MB, peak memory is 728 MB
PHY-1001 : 4047 mslices, 4058 lslices, 60 pads, 42 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19478 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 693440, over cnt = 2400(6%), over = 3982, worst = 7
PHY-1002 : len = 709720, over cnt = 1478(4%), over = 2103, worst = 6
PHY-1002 : len = 731224, over cnt = 391(1%), over = 527, worst = 5
PHY-1002 : len = 740320, over cnt = 27(0%), over = 28, worst = 2
PHY-1002 : len = 741216, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.946646s wall, 3.468750s user + 0.125000s system = 3.593750s CPU (184.6%)

PHY-1001 : Congestion index: top1 = 48.41, top5 = 43.57, top10 = 40.85, top15 = 39.18.
PHY-1001 : End global routing;  2.311372s wall, 3.812500s user + 0.140625s system = 3.953125s CPU (171.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 703, reserve = 693, peak = 728.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net DATA/DIV_Dtemp/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 973, reserve = 961, peak = 973.
PHY-1001 : End build detailed router design. 4.902993s wall, 4.875000s user + 0.031250s system = 4.906250s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 190032, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.918466s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (100.4%)

PHY-1001 : Current memory(MB): used = 1009, reserve = 998, peak = 1009.
PHY-1001 : End phase 1; 0.925923s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (99.6%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 55% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.81822e+06, over cnt = 1378(0%), over = 1383, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1025, reserve = 1013, peak = 1025.
PHY-1001 : End initial routed; 20.085483s wall, 47.125000s user + 0.765625s system = 47.890625s CPU (238.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18276(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.304   |   0.000   |   0   
RUN-1001 :   Hold   |   0.112   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.678660s wall, 3.671875s user + 0.000000s system = 3.671875s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 1033, reserve = 1021, peak = 1033.
PHY-1001 : End phase 2; 23.764307s wall, 50.796875s user + 0.765625s system = 51.562500s CPU (217.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.81822e+06, over cnt = 1378(0%), over = 1383, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.260528s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (96.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.80087e+06, over cnt = 496(0%), over = 496, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 1.207358s wall, 1.968750s user + 0.015625s system = 1.984375s CPU (164.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.8015e+06, over cnt = 104(0%), over = 104, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.476062s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (141.1%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.80246e+06, over cnt = 26(0%), over = 26, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.322713s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (111.4%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.80297e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.190618s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (98.4%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.80306e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.168996s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (101.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18276(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.220   |   0.000   |   0   
RUN-1001 :   Hold   |   0.112   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.697023s wall, 3.671875s user + 0.000000s system = 3.671875s CPU (99.3%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 344 feed throughs used by 286 nets
PHY-1001 : End commit to database; 2.493558s wall, 2.453125s user + 0.031250s system = 2.484375s CPU (99.6%)

PHY-1001 : Current memory(MB): used = 1125, reserve = 1116, peak = 1125.
PHY-1001 : End phase 3; 9.341952s wall, 10.281250s user + 0.046875s system = 10.328125s CPU (110.6%)

PHY-1003 : Routed, final wirelength = 1.80306e+06
PHY-1001 : Current memory(MB): used = 1130, reserve = 1121, peak = 1130.
PHY-1001 : End export database. 0.064803s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (120.6%)

PHY-1001 : End detail routing;  39.460724s wall, 67.390625s user + 0.843750s system = 68.234375s CPU (172.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68261, tnet num: 19478, tinst num: 8210, tnode num: 92962, tedge num: 112322.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.938047s wall, 1.937500s user + 0.000000s system = 1.937500s CPU (100.0%)

RUN-1004 : used memory is 1062 MB, reserved memory is 1062 MB, peak memory is 1130 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  48.501795s wall, 77.906250s user + 1.031250s system = 78.937500s CPU (162.8%)

RUN-1004 : used memory is 1062 MB, reserved memory is 1063 MB, peak memory is 1130 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8778   out of  19600   44.79%
#reg                    12705   out of  19600   64.82%
#le                     15161
  #lut only              2456   out of  15161   16.20%
  #reg only              6383   out of  15161   42.10%
  #lut&reg               6322   out of  15161   41.70%
#dsp                        0   out of     29    0.00%
#bram                      42   out of     64   65.62%
  #bram9k                  42
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                     9
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet              Type               DriverType         Driver                    Fanout
#1        DATA/DIV_Dtemp/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6921
#2        config_inst_syn_9     GCLK               config             config_inst.jtck          173
#3        clk_in_dup_1          GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         M3        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          NONE       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D14        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         P6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT        D16        LVCMOS33           8            NONE           NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        R15        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15161  |7354    |1424    |12747   |42      |0       |
|  AnyFog_dataX                      |AnyFog          |220    |96      |22      |169     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |100    |70      |22      |49      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |205    |87      |22      |171     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |83     |58      |22      |49      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |201    |111     |22      |167     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |83     |60      |22      |49      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |3504   |931     |34      |3422    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |716    |93      |5       |706     |0       |0       |
|    STADOP_com2                     |STADOP          |563    |49      |0       |558     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |40      |14      |42      |0       |0       |
|    head_com2                       |uniheading      |276    |76      |5       |264     |0       |0       |
|    rmc_com2                        |Gprmc           |158    |62      |0       |147     |0       |0       |
|    uart_com2                       |Agrica          |1431   |316     |10      |1410    |0       |0       |
|  DATA                              |Data_Processing |8663   |4454    |1062    |6949    |0       |0       |
|    DIV_Dtemp                       |Divider         |809    |329     |84      |677     |0       |0       |
|    DIV_Utemp                       |Divider         |622    |312     |84      |498     |0       |0       |
|    DIV_accX                        |Divider         |574    |325     |84      |419     |0       |0       |
|    DIV_accY                        |Divider         |679    |337     |111     |509     |0       |0       |
|    DIV_accZ                        |Divider         |700    |358     |132     |487     |0       |0       |
|    DIV_rateX                       |Divider         |658    |386     |132     |456     |0       |0       |
|    DIV_rateY                       |Divider         |583    |392     |132     |367     |0       |0       |
|    DIV_rateZ                       |Divider         |586    |401     |132     |382     |0       |0       |
|    genclk                          |genclk          |80     |52      |20      |46      |0       |0       |
|  FMC                               |FMC_Ctrl        |417    |361     |43      |333     |0       |0       |
|  IIC                               |I2C_master      |260    |210     |11      |239     |0       |0       |
|  IMU_CTRL                          |SCHA634         |899    |620     |61      |725     |0       |0       |
|    CtrlData                        |CtrlData        |471    |419     |47      |331     |0       |0       |
|      usms                          |Time_1ms        |27     |22      |5       |19      |0       |0       |
|    SPIM                            |SPI_SCHA634     |428    |201     |14      |394     |0       |0       |
|  POWER                             |POWER_EN        |98     |57      |38      |38      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |675    |427     |109     |473     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |675    |427     |109     |473     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |306    |183     |0       |289     |0       |0       |
|        reg_inst                    |register        |304    |181     |0       |287     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |369    |244     |109     |184     |0       |0       |
|        bus_inst                    |bus_top         |153    |99      |52      |64      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |28     |18      |10      |12      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |1      |0       |0       |1       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |16     |9       |6       |7       |0       |0       |
|          BUS_DETECTOR[5]$bus_nodes |bus_det         |51     |33      |18      |19      |0       |0       |
|          BUS_DETECTOR[6]$bus_nodes |bus_det         |53     |35      |18      |21      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |137    |103     |29      |89      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13551  
    #2          2       3518   
    #3          3        682   
    #4          4        260   
    #5        5-10       923   
    #6        11-50      461   
    #7       51-100      14    
    #8       101-500      4    
    #9        >500        2    
  Average     2.14             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.230786s wall, 3.781250s user + 0.015625s system = 3.796875s CPU (170.2%)

RUN-1004 : used memory is 1062 MB, reserved memory is 1064 MB, peak memory is 1130 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68261, tnet num: 19478, tinst num: 8210, tnode num: 92962, tedge num: 112322.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.995272s wall, 1.984375s user + 0.000000s system = 1.984375s CPU (99.5%)

RUN-1004 : used memory is 1064 MB, reserved memory is 1065 MB, peak memory is 1130 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19478 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.512217s wall, 1.484375s user + 0.000000s system = 1.484375s CPU (98.2%)

RUN-1004 : used memory is 1070 MB, reserved memory is 1070 MB, peak memory is 1130 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 8f169df8acfcb835781910b1ac29267cd80e16d8a5e1b8cf9d032ec93c0a2865 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8210
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19480, pip num: 150031
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 344
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3259 valid insts, and 418134 bits set as '1'.
BIT-1004 : the usercode register value: 00000000110101000001111110010110
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.591102s wall, 126.171875s user + 0.187500s system = 126.359375s CPU (1003.6%)

RUN-1004 : used memory is 1197 MB, reserved memory is 1183 MB, peak memory is 1312 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250520_111115.log"

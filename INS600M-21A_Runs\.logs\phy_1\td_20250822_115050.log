============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Fri Aug 22 11:50:50 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(100)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 100 in ../../Src/INS600M-21A.v(103)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(525)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.661047s wall, 1.640625s user + 4.000000s system = 5.640625s CPU (99.6%)

RUN-1004 : used memory is 79 MB, reserved memory is 41 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.809387s wall, 1.703125s user + 0.093750s system = 1.796875s CPU (99.3%)

RUN-1004 : used memory is 303 MB, reserved memory is 271 MB, peak memory is 306 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 42 trigger nets, 42 data nets.
KIT-1004 : Chipwatcher code = 1000110011001111
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb011001,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0111110,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=126) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=126) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb011001,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0111110,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb011001,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0111110,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb011001,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0111110,32'sb01000100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=126)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=126)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb011001,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0111110,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb011001,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0111110,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22792/23 useful/useless nets, 19541/12 useful/useless insts
SYN-1016 : Merged 29 instances.
SYN-1032 : 22460/20 useful/useless nets, 19967/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 398 better
SYN-1014 : Optimize round 2
SYN-1032 : 22140/45 useful/useless nets, 19647/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.536388s wall, 2.500000s user + 0.046875s system = 2.546875s CPU (100.4%)

RUN-1004 : used memory is 328 MB, reserved memory is 295 MB, peak memory is 330 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 65 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22188/299 useful/useless nets, 19732/47 useful/useless insts
SYN-1016 : Merged 45 instances.
SYN-2571 : Optimize after map_dsp, round 1, 391 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 40 instances.
SYN-2501 : Optimize round 1, 82 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 22636/5 useful/useless nets, 20180/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 40, tpin num: 82606, tnet num: 22636, tinst num: 20179, tnode num: 115686, tedge num: 129153.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.226878s wall, 1.203125s user + 0.015625s system = 1.218750s CPU (99.3%)

RUN-1004 : used memory is 469 MB, reserved memory is 437 MB, peak memory is 469 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22636 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 237 (3.41), #lev = 7 (1.79)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 237 (3.41), #lev = 7 (1.79)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 534 instances into 237 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 407 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 128 adder to BLE ...
SYN-4008 : Packed 128 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.765271s wall, 4.687500s user + 0.078125s system = 4.765625s CPU (100.0%)

RUN-1004 : used memory is 350 MB, reserved memory is 326 MB, peak memory is 578 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.653913s wall, 7.515625s user + 0.140625s system = 7.656250s CPU (100.0%)

RUN-1004 : used memory is 350 MB, reserved memory is 326 MB, peak memory is 578 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (271 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19423 instances
RUN-0007 : 5617 luts, 12199 seqs, 983 mslices, 519 lslices, 60 pads, 40 brams, 0 dsps
RUN-1001 : There are total 21887 nets
RUN-1001 : 16427 nets have 2 pins
RUN-1001 : 4263 nets have [3 - 5] pins
RUN-1001 : 820 nets have [6 - 10] pins
RUN-1001 : 251 nets have [11 - 20] pins
RUN-1001 : 104 nets have [21 - 99] pins
RUN-1001 : 22 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4783     
RUN-1001 :   No   |  No   |  Yes  |     704     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     443     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19421 instances, 5617 luts, 12199 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 40, tpin num: 80991, tnet num: 21885, tinst num: 19421, tnode num: 113862, tedge num: 127485.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.233311s wall, 1.187500s user + 0.046875s system = 1.234375s CPU (100.1%)

RUN-1004 : used memory is 529 MB, reserved memory is 500 MB, peak memory is 578 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21885 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.171092s wall, 2.093750s user + 0.078125s system = 2.171875s CPU (100.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.68092e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19421.
PHY-3001 : Level 1 #clusters 2187.
PHY-3001 : End clustering;  0.151252s wall, 0.250000s user + 0.031250s system = 0.281250s CPU (185.9%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 888102, overlap = 637.469
PHY-3002 : Step(2): len = 815340, overlap = 677.625
PHY-3002 : Step(3): len = 525713, overlap = 844.594
PHY-3002 : Step(4): len = 455299, overlap = 923.844
PHY-3002 : Step(5): len = 368905, overlap = 1025.03
PHY-3002 : Step(6): len = 331239, overlap = 1088.09
PHY-3002 : Step(7): len = 281583, overlap = 1161.06
PHY-3002 : Step(8): len = 243594, overlap = 1237.38
PHY-3002 : Step(9): len = 220194, overlap = 1310.41
PHY-3002 : Step(10): len = 197387, overlap = 1356.41
PHY-3002 : Step(11): len = 177361, overlap = 1392.72
PHY-3002 : Step(12): len = 163213, overlap = 1409.66
PHY-3002 : Step(13): len = 147481, overlap = 1412.84
PHY-3002 : Step(14): len = 135271, overlap = 1434.66
PHY-3002 : Step(15): len = 128661, overlap = 1453.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.2098e-06
PHY-3002 : Step(16): len = 138288, overlap = 1417.47
PHY-3002 : Step(17): len = 186971, overlap = 1289.69
PHY-3002 : Step(18): len = 193354, overlap = 1173.84
PHY-3002 : Step(19): len = 193197, overlap = 1128.41
PHY-3002 : Step(20): len = 191682, overlap = 1095.16
PHY-3002 : Step(21): len = 188638, overlap = 1054.34
PHY-3002 : Step(22): len = 182849, overlap = 1029.69
PHY-3002 : Step(23): len = 178905, overlap = 1040.91
PHY-3002 : Step(24): len = 173223, overlap = 1048.62
PHY-3002 : Step(25): len = 170248, overlap = 1041.69
PHY-3002 : Step(26): len = 165982, overlap = 1066.97
PHY-3002 : Step(27): len = 164625, overlap = 1053.06
PHY-3002 : Step(28): len = 162887, overlap = 1042.47
PHY-3002 : Step(29): len = 162046, overlap = 1034.91
PHY-3002 : Step(30): len = 161302, overlap = 1046.88
PHY-3002 : Step(31): len = 161168, overlap = 1068.91
PHY-3002 : Step(32): len = 160676, overlap = 1060.81
PHY-3002 : Step(33): len = 159601, overlap = 1073.81
PHY-3002 : Step(34): len = 159176, overlap = 1062.88
PHY-3002 : Step(35): len = 158297, overlap = 1055.72
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.4196e-06
PHY-3002 : Step(36): len = 165180, overlap = 1029.66
PHY-3002 : Step(37): len = 179626, overlap = 987.281
PHY-3002 : Step(38): len = 183078, overlap = 956.906
PHY-3002 : Step(39): len = 184126, overlap = 938.5
PHY-3002 : Step(40): len = 183926, overlap = 933.562
PHY-3002 : Step(41): len = 182923, overlap = 913.438
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.8392e-06
PHY-3002 : Step(42): len = 192507, overlap = 886.031
PHY-3002 : Step(43): len = 208697, overlap = 799.469
PHY-3002 : Step(44): len = 213266, overlap = 766.781
PHY-3002 : Step(45): len = 215033, overlap = 723.188
PHY-3002 : Step(46): len = 214796, overlap = 692.281
PHY-3002 : Step(47): len = 213440, overlap = 682.656
PHY-3002 : Step(48): len = 212238, overlap = 665.719
PHY-3002 : Step(49): len = 211185, overlap = 664.75
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 9.6784e-06
PHY-3002 : Step(50): len = 223395, overlap = 649.906
PHY-3002 : Step(51): len = 236010, overlap = 592.594
PHY-3002 : Step(52): len = 239976, overlap = 567.156
PHY-3002 : Step(53): len = 243583, overlap = 550.531
PHY-3002 : Step(54): len = 242805, overlap = 552.906
PHY-3002 : Step(55): len = 241658, overlap = 551.406
PHY-3002 : Step(56): len = 240211, overlap = 538.281
PHY-3002 : Step(57): len = 239448, overlap = 536.562
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.93568e-05
PHY-3002 : Step(58): len = 250595, overlap = 521.031
PHY-3002 : Step(59): len = 261899, overlap = 507.938
PHY-3002 : Step(60): len = 264576, overlap = 486
PHY-3002 : Step(61): len = 266961, overlap = 467.281
PHY-3002 : Step(62): len = 266239, overlap = 473.906
PHY-3002 : Step(63): len = 265060, overlap = 458
PHY-3002 : Step(64): len = 263452, overlap = 455.312
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.87136e-05
PHY-3002 : Step(65): len = 274774, overlap = 433.344
PHY-3002 : Step(66): len = 286543, overlap = 419.031
PHY-3002 : Step(67): len = 290799, overlap = 408.5
PHY-3002 : Step(68): len = 291633, overlap = 410.5
PHY-3002 : Step(69): len = 289898, overlap = 407.188
PHY-3002 : Step(70): len = 287246, overlap = 400.125
PHY-3002 : Step(71): len = 286398, overlap = 418.688
PHY-3002 : Step(72): len = 285589, overlap = 423.219
PHY-3002 : Step(73): len = 284792, overlap = 418.688
PHY-3002 : Step(74): len = 283820, overlap = 413.594
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 7.74272e-05
PHY-3002 : Step(75): len = 290596, overlap = 419.625
PHY-3002 : Step(76): len = 296951, overlap = 409.969
PHY-3002 : Step(77): len = 300282, overlap = 400.562
PHY-3002 : Step(78): len = 301467, overlap = 402.75
PHY-3002 : Step(79): len = 300029, overlap = 400.125
PHY-3002 : Step(80): len = 299380, overlap = 394.844
PHY-3002 : Step(81): len = 298307, overlap = 392.219
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000148209
PHY-3002 : Step(82): len = 302352, overlap = 382.281
PHY-3002 : Step(83): len = 308280, overlap = 368.469
PHY-3002 : Step(84): len = 310933, overlap = 343.406
PHY-3002 : Step(85): len = 312858, overlap = 338.562
PHY-3002 : Step(86): len = 312523, overlap = 325.438
PHY-3002 : Step(87): len = 312198, overlap = 314.75
PHY-3002 : Step(88): len = 311883, overlap = 301.438
PHY-3002 : Step(89): len = 313326, overlap = 275.344
PHY-3002 : Step(90): len = 313862, overlap = 259.469
PHY-3002 : Step(91): len = 313443, overlap = 260.906
PHY-3002 : Step(92): len = 311927, overlap = 263.125
PHY-3002 : Step(93): len = 311454, overlap = 265.75
PHY-3002 : Step(94): len = 310994, overlap = 278.844
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000274257
PHY-3002 : Step(95): len = 313313, overlap = 276.906
PHY-3002 : Step(96): len = 317154, overlap = 271.312
PHY-3002 : Step(97): len = 318814, overlap = 261.438
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000536736
PHY-3002 : Step(98): len = 319600, overlap = 261.062
PHY-3002 : Step(99): len = 323642, overlap = 274
PHY-3002 : Step(100): len = 325276, overlap = 267.594
PHY-3002 : Step(101): len = 324872, overlap = 266.188
PHY-3002 : Step(102): len = 324900, overlap = 280.594
PHY-3002 : Step(103): len = 324757, overlap = 286.531
PHY-3002 : Step(104): len = 325154, overlap = 285
PHY-3002 : Step(105): len = 324403, overlap = 280.094
PHY-3002 : Step(106): len = 324148, overlap = 274.781
PHY-3002 : Step(107): len = 324333, overlap = 269.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.012601s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21887.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 428056, over cnt = 1197(3%), over = 5503, worst = 53
PHY-1001 : End global iterations;  0.812497s wall, 1.078125s user + 0.031250s system = 1.109375s CPU (136.5%)

PHY-1001 : Congestion index: top1 = 77.11, top5 = 52.01, top10 = 42.72, top15 = 37.28.
PHY-3001 : End congestion estimation;  1.054120s wall, 1.312500s user + 0.031250s system = 1.343750s CPU (127.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21885 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.003196s wall, 0.968750s user + 0.031250s system = 1.000000s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.0001141
PHY-3002 : Step(108): len = 367432, overlap = 203.844
PHY-3002 : Step(109): len = 383000, overlap = 185.719
PHY-3002 : Step(110): len = 385619, overlap = 168.094
PHY-3002 : Step(111): len = 384771, overlap = 156.562
PHY-3002 : Step(112): len = 391411, overlap = 145.25
PHY-3002 : Step(113): len = 399903, overlap = 128.688
PHY-3002 : Step(114): len = 406682, overlap = 126.219
PHY-3002 : Step(115): len = 408771, overlap = 119.594
PHY-3002 : Step(116): len = 409729, overlap = 115.812
PHY-3002 : Step(117): len = 411186, overlap = 109.281
PHY-3002 : Step(118): len = 410333, overlap = 104.938
PHY-3002 : Step(119): len = 412004, overlap = 107.719
PHY-3002 : Step(120): len = 414541, overlap = 108.188
PHY-3002 : Step(121): len = 415165, overlap = 114.125
PHY-3002 : Step(122): len = 417690, overlap = 114.156
PHY-3002 : Step(123): len = 418150, overlap = 113.406
PHY-3002 : Step(124): len = 419625, overlap = 113.094
PHY-3002 : Step(125): len = 422026, overlap = 115.625
PHY-3002 : Step(126): len = 424367, overlap = 116.344
PHY-3002 : Step(127): len = 423147, overlap = 119.625
PHY-3002 : Step(128): len = 423439, overlap = 119.312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000228199
PHY-3002 : Step(129): len = 423951, overlap = 120.688
PHY-3002 : Step(130): len = 427005, overlap = 123
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(131): len = 430445, overlap = 118.094
PHY-3002 : Step(132): len = 438180, overlap = 114.125
PHY-3002 : Step(133): len = 443352, overlap = 106.031
PHY-3002 : Step(134): len = 446311, overlap = 105.531
PHY-3002 : Step(135): len = 447319, overlap = 105.188
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 57/21887.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 509736, over cnt = 2149(6%), over = 9492, worst = 42
PHY-1001 : End global iterations;  1.092480s wall, 1.859375s user + 0.031250s system = 1.890625s CPU (173.1%)

PHY-1001 : Congestion index: top1 = 76.53, top5 = 56.25, top10 = 48.35, top15 = 43.54.
PHY-3001 : End congestion estimation;  1.368500s wall, 2.125000s user + 0.031250s system = 2.156250s CPU (157.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21885 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.008929s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (100.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000101919
PHY-3002 : Step(136): len = 452158, overlap = 373.188
PHY-3002 : Step(137): len = 456497, overlap = 317.156
PHY-3002 : Step(138): len = 453152, overlap = 293.375
PHY-3002 : Step(139): len = 447745, overlap = 273.688
PHY-3002 : Step(140): len = 443314, overlap = 265.844
PHY-3002 : Step(141): len = 439913, overlap = 259.281
PHY-3002 : Step(142): len = 437424, overlap = 247.531
PHY-3002 : Step(143): len = 436691, overlap = 245.219
PHY-3002 : Step(144): len = 435187, overlap = 253.594
PHY-3002 : Step(145): len = 433672, overlap = 248.469
PHY-3002 : Step(146): len = 432706, overlap = 246.125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000203838
PHY-3002 : Step(147): len = 433397, overlap = 240.625
PHY-3002 : Step(148): len = 435379, overlap = 234.312
PHY-3002 : Step(149): len = 436019, overlap = 220.344
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000407677
PHY-3002 : Step(150): len = 439141, overlap = 209.719
PHY-3002 : Step(151): len = 446151, overlap = 184.406
PHY-3002 : Step(152): len = 450413, overlap = 175.188
PHY-3002 : Step(153): len = 450885, overlap = 175.812
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000815354
PHY-3002 : Step(154): len = 451397, overlap = 171.5
PHY-3002 : Step(155): len = 453453, overlap = 159.5
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 40, tpin num: 80991, tnet num: 21885, tinst num: 19421, tnode num: 113862, tedge num: 127485.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.646025s wall, 1.625000s user + 0.031250s system = 1.656250s CPU (100.6%)

RUN-1004 : used memory is 569 MB, reserved memory is 543 MB, peak memory is 702 MB
OPT-1001 : Total overflow 541.97 peak overflow 3.84
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 624/21887.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 533568, over cnt = 2468(7%), over = 8821, worst = 29
PHY-1001 : End global iterations;  1.260547s wall, 1.843750s user + 0.000000s system = 1.843750s CPU (146.3%)

PHY-1001 : Congestion index: top1 = 59.68, top5 = 48.69, top10 = 43.48, top15 = 40.33.
PHY-1001 : End incremental global routing;  1.530760s wall, 2.109375s user + 0.000000s system = 2.109375s CPU (137.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21885 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.059152s wall, 1.031250s user + 0.031250s system = 1.062500s CPU (100.3%)

OPT-1001 : 17 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19341 has valid locations, 240 needs to be replaced
PHY-3001 : design contains 19644 instances, 5710 luts, 12329 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 467915
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17087/22110.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 543600, over cnt = 2496(7%), over = 8886, worst = 29
PHY-1001 : End global iterations;  0.194096s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (153.0%)

PHY-1001 : Congestion index: top1 = 59.72, top5 = 49.01, top10 = 43.82, top15 = 40.62.
PHY-3001 : End congestion estimation;  0.453428s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (120.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 40, tpin num: 81730, tnet num: 22108, tinst num: 19644, tnode num: 114911, tedge num: 128517.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.570890s wall, 1.515625s user + 0.046875s system = 1.562500s CPU (99.5%)

RUN-1004 : used memory is 614 MB, reserved memory is 593 MB, peak memory is 705 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22108 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.678665s wall, 2.593750s user + 0.078125s system = 2.671875s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(156): len = 467889, overlap = 2.0625
PHY-3002 : Step(157): len = 469547, overlap = 2.1875
PHY-3002 : Step(158): len = 470118, overlap = 2.25
PHY-3002 : Step(159): len = 470617, overlap = 2.3125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17109/22110.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 543696, over cnt = 2515(7%), over = 9001, worst = 29
PHY-1001 : End global iterations;  0.197714s wall, 0.250000s user + 0.031250s system = 0.281250s CPU (142.3%)

PHY-1001 : Congestion index: top1 = 59.94, top5 = 49.32, top10 = 44.09, top15 = 40.82.
PHY-3001 : End congestion estimation;  0.458409s wall, 0.500000s user + 0.031250s system = 0.531250s CPU (115.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22108 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.055954s wall, 1.046875s user + 0.015625s system = 1.062500s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000569594
PHY-3002 : Step(160): len = 470766, overlap = 162.156
PHY-3002 : Step(161): len = 471026, overlap = 161.406
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00113919
PHY-3002 : Step(162): len = 471286, overlap = 161
PHY-3002 : Step(163): len = 471737, overlap = 161.125
PHY-3001 : Final: Len = 471737, Over = 161.125
PHY-3001 : End incremental placement;  5.544444s wall, 6.046875s user + 0.265625s system = 6.312500s CPU (113.9%)

OPT-1001 : Total overflow 547.12 peak overflow 3.84
OPT-1001 : End high-fanout net optimization;  8.700124s wall, 9.890625s user + 0.296875s system = 10.187500s CPU (117.1%)

OPT-1001 : Current memory(MB): used = 710, reserve = 690, peak = 725.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17117/22110.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 546632, over cnt = 2480(7%), over = 8479, worst = 29
PHY-1002 : len = 585104, over cnt = 1797(5%), over = 4922, worst = 29
PHY-1002 : len = 613888, over cnt = 1078(3%), over = 2797, worst = 20
PHY-1002 : len = 638232, over cnt = 526(1%), over = 1341, worst = 14
PHY-1002 : len = 658288, over cnt = 83(0%), over = 197, worst = 8
PHY-1001 : End global iterations;  1.266813s wall, 1.890625s user + 0.000000s system = 1.890625s CPU (149.2%)

PHY-1001 : Congestion index: top1 = 51.64, top5 = 44.80, top10 = 41.41, top15 = 39.23.
OPT-1001 : End congestion update;  1.639534s wall, 2.265625s user + 0.000000s system = 2.265625s CPU (138.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22108 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.917769s wall, 0.906250s user + 0.000000s system = 0.906250s CPU (98.7%)

OPT-0007 : Start: WNS 3819 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.564042s wall, 3.187500s user + 0.000000s system = 3.187500s CPU (124.3%)

OPT-1001 : Current memory(MB): used = 709, reserve = 690, peak = 725.
OPT-1001 : End physical optimization;  13.240442s wall, 15.125000s user + 0.343750s system = 15.468750s CPU (116.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5710 LUT to BLE ...
SYN-4008 : Packed 5710 LUT and 2728 SEQ to BLE.
SYN-4003 : Packing 9601 remaining SEQ's ...
SYN-4005 : Packed 3367 SEQ with LUT/SLICE
SYN-4006 : 159 single LUT's are left
SYN-4006 : 6234 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11944/13815 primitive instances ...
PHY-3001 : End packing;  2.714067s wall, 2.687500s user + 0.000000s system = 2.687500s CPU (99.0%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8129 instances
RUN-1001 : 4012 mslices, 4012 lslices, 60 pads, 40 brams, 0 dsps
RUN-1001 : There are total 19433 nets
RUN-1001 : 13603 nets have 2 pins
RUN-1001 : 4413 nets have [3 - 5] pins
RUN-1001 : 892 nets have [6 - 10] pins
RUN-1001 : 387 nets have [11 - 20] pins
RUN-1001 : 129 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8127 instances, 8024 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 489077, Over = 392.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7951/19433.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 621248, over cnt = 1594(4%), over = 2528, worst = 11
PHY-1002 : len = 627136, over cnt = 1066(3%), over = 1490, worst = 8
PHY-1002 : len = 637880, over cnt = 454(1%), over = 623, worst = 8
PHY-1002 : len = 644592, over cnt = 140(0%), over = 184, worst = 8
PHY-1002 : len = 648448, over cnt = 2(0%), over = 2, worst = 1
PHY-1001 : End global iterations;  1.298356s wall, 1.968750s user + 0.046875s system = 2.015625s CPU (155.2%)

PHY-1001 : Congestion index: top1 = 52.52, top5 = 44.85, top10 = 41.06, top15 = 38.62.
PHY-3001 : End congestion estimation;  1.632888s wall, 2.296875s user + 0.046875s system = 2.343750s CPU (143.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 40, tpin num: 67823, tnet num: 19431, tinst num: 8127, tnode num: 91938, tedge num: 111885.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.723926s wall, 1.703125s user + 0.031250s system = 1.734375s CPU (100.6%)

RUN-1004 : used memory is 608 MB, reserved memory is 594 MB, peak memory is 725 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19431 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.699319s wall, 2.640625s user + 0.062500s system = 2.703125s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.42923e-05
PHY-3002 : Step(164): len = 489541, overlap = 387.75
PHY-3002 : Step(165): len = 488634, overlap = 397.25
PHY-3002 : Step(166): len = 487842, overlap = 402.75
PHY-3002 : Step(167): len = 485053, overlap = 416
PHY-3002 : Step(168): len = 482983, overlap = 422.5
PHY-3002 : Step(169): len = 480577, overlap = 423.5
PHY-3002 : Step(170): len = 478718, overlap = 429.75
PHY-3002 : Step(171): len = 477510, overlap = 423.25
PHY-3002 : Step(172): len = 475852, overlap = 421
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.85846e-05
PHY-3002 : Step(173): len = 479368, overlap = 415.5
PHY-3002 : Step(174): len = 483140, overlap = 406.25
PHY-3002 : Step(175): len = 483800, overlap = 402.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(176): len = 493188, overlap = 375.25
PHY-3002 : Step(177): len = 503111, overlap = 361.75
PHY-3002 : Step(178): len = 500753, overlap = 357.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.752816s wall, 0.875000s user + 0.734375s system = 1.609375s CPU (213.8%)

PHY-3001 : Trial Legalized: Len = 621073
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 613/19433.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 709752, over cnt = 2409(6%), over = 3864, worst = 7
PHY-1002 : len = 722512, over cnt = 1486(4%), over = 2123, worst = 6
PHY-1002 : len = 738600, over cnt = 643(1%), over = 922, worst = 5
PHY-1002 : len = 749728, over cnt = 173(0%), over = 238, worst = 4
PHY-1002 : len = 753688, over cnt = 2(0%), over = 2, worst = 1
PHY-1001 : End global iterations;  2.045866s wall, 3.453125s user + 0.046875s system = 3.500000s CPU (171.1%)

PHY-1001 : Congestion index: top1 = 50.69, top5 = 45.57, top10 = 42.61, top15 = 40.74.
PHY-3001 : End congestion estimation;  2.428349s wall, 3.828125s user + 0.046875s system = 3.875000s CPU (159.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19431 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.985765s wall, 0.984375s user + 0.000000s system = 0.984375s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000195452
PHY-3002 : Step(179): len = 574503, overlap = 83.5
PHY-3002 : Step(180): len = 553985, overlap = 144
PHY-3002 : Step(181): len = 541637, overlap = 189
PHY-3002 : Step(182): len = 534174, overlap = 227
PHY-3002 : Step(183): len = 530042, overlap = 265.75
PHY-3002 : Step(184): len = 528892, overlap = 283.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000390903
PHY-3002 : Step(185): len = 532785, overlap = 275.5
PHY-3002 : Step(186): len = 537513, overlap = 266.5
PHY-3002 : Step(187): len = 540246, overlap = 263.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000781806
PHY-3002 : Step(188): len = 542654, overlap = 264.5
PHY-3002 : Step(189): len = 548470, overlap = 263.25
PHY-3002 : Step(190): len = 552989, overlap = 263.5
PHY-3002 : Step(191): len = 555385, overlap = 257.5
PHY-3002 : Step(192): len = 558396, overlap = 252.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.033936s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (92.1%)

PHY-3001 : Legalized: Len = 599343, Over = 0
PHY-3001 : Spreading special nets. 39 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.087329s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (107.4%)

PHY-3001 : 66 instances has been re-located, deltaX = 25, deltaY = 40, maxDist = 2.
PHY-3001 : Final: Len = 600453, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 40, tpin num: 67823, tnet num: 19431, tinst num: 8127, tnode num: 91938, tedge num: 111885.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.002731s wall, 1.984375s user + 0.015625s system = 2.000000s CPU (99.9%)

RUN-1004 : used memory is 609 MB, reserved memory is 600 MB, peak memory is 725 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2965/19433.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 691528, over cnt = 2219(6%), over = 3579, worst = 8
PHY-1002 : len = 703840, over cnt = 1400(3%), over = 1950, worst = 8
PHY-1002 : len = 717976, over cnt = 645(1%), over = 834, worst = 8
PHY-1002 : len = 729088, over cnt = 156(0%), over = 200, worst = 4
PHY-1002 : len = 732264, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.905947s wall, 3.031250s user + 0.078125s system = 3.109375s CPU (163.1%)

PHY-1001 : Congestion index: top1 = 48.17, top5 = 43.36, top10 = 40.73, top15 = 38.95.
PHY-1001 : End incremental global routing;  2.234500s wall, 3.359375s user + 0.078125s system = 3.437500s CPU (153.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19431 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.983145s wall, 0.984375s user + 0.000000s system = 0.984375s CPU (100.1%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8062 has valid locations, 14 needs to be replaced
PHY-3001 : design contains 8139 instances, 8036 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 603779
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17513/19444.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 737088, over cnt = 26(0%), over = 30, worst = 3
PHY-1002 : len = 737128, over cnt = 15(0%), over = 15, worst = 1
PHY-1002 : len = 737256, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 737304, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 737336, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.717536s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (106.7%)

PHY-1001 : Congestion index: top1 = 48.32, top5 = 43.49, top10 = 40.86, top15 = 39.08.
PHY-3001 : End congestion estimation;  1.031451s wall, 1.078125s user + 0.000000s system = 1.078125s CPU (104.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 40, tpin num: 67918, tnet num: 19442, tinst num: 8139, tnode num: 92051, tedge num: 112003.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.952969s wall, 1.937500s user + 0.015625s system = 1.953125s CPU (100.0%)

RUN-1004 : used memory is 675 MB, reserved memory is 655 MB, peak memory is 725 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19442 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.932847s wall, 2.906250s user + 0.031250s system = 2.937500s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(193): len = 603454, overlap = 0
PHY-3002 : Step(194): len = 603239, overlap = 0.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17507/19444.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 735624, over cnt = 29(0%), over = 35, worst = 3
PHY-1002 : len = 735552, over cnt = 14(0%), over = 17, worst = 2
PHY-1002 : len = 735696, over cnt = 6(0%), over = 7, worst = 2
PHY-1002 : len = 735824, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 735840, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.702870s wall, 0.750000s user + 0.046875s system = 0.796875s CPU (113.4%)

PHY-1001 : Congestion index: top1 = 48.10, top5 = 43.45, top10 = 40.88, top15 = 39.08.
PHY-3001 : End congestion estimation;  1.016387s wall, 1.062500s user + 0.046875s system = 1.109375s CPU (109.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19442 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.955499s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000943535
PHY-3002 : Step(195): len = 603221, overlap = 1.25
PHY-3002 : Step(196): len = 603209, overlap = 1.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007280s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (214.6%)

PHY-3001 : Legalized: Len = 603279, Over = 0
PHY-3001 : End spreading;  0.071069s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (109.9%)

PHY-3001 : Final: Len = 603279, Over = 0
PHY-3001 : End incremental placement;  6.585050s wall, 6.734375s user + 0.156250s system = 6.890625s CPU (104.6%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.323696s wall, 11.578125s user + 0.234375s system = 11.812500s CPU (114.4%)

OPT-1001 : Current memory(MB): used = 722, reserve = 705, peak = 727.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17507/19444.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 735600, over cnt = 21(0%), over = 23, worst = 2
PHY-1002 : len = 735624, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 735624, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 735712, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.546773s wall, 0.578125s user + 0.000000s system = 0.578125s CPU (105.7%)

PHY-1001 : Congestion index: top1 = 48.12, top5 = 43.45, top10 = 40.86, top15 = 39.08.
OPT-1001 : End congestion update;  0.858881s wall, 0.890625s user + 0.000000s system = 0.890625s CPU (103.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19442 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.819017s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (101.1%)

OPT-0007 : Start: WNS 3826 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.683266s wall, 1.718750s user + 0.000000s system = 1.718750s CPU (102.1%)

OPT-1001 : Current memory(MB): used = 723, reserve = 706, peak = 727.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19442 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.805470s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (100.9%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17525/19444.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 735712, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.120190s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (91.0%)

PHY-1001 : Congestion index: top1 = 48.12, top5 = 43.45, top10 = 40.86, top15 = 39.08.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19442 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.816366s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (99.5%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3826 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 47.655172
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3826ps with logic level 4 
OPT-1001 : End physical optimization;  16.335351s wall, 17.781250s user + 0.250000s system = 18.031250s CPU (110.4%)

RUN-1003 : finish command "place" in  65.338358s wall, 117.328125s user + 6.828125s system = 124.156250s CPU (190.0%)

RUN-1004 : used memory is 641 MB, reserved memory is 618 MB, peak memory is 727 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.647531s wall, 2.843750s user + 0.000000s system = 2.843750s CPU (172.6%)

RUN-1004 : used memory is 641 MB, reserved memory is 619 MB, peak memory is 727 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8141 instances
RUN-1001 : 4021 mslices, 4015 lslices, 60 pads, 40 brams, 0 dsps
RUN-1001 : There are total 19444 nets
RUN-1001 : 13601 nets have 2 pins
RUN-1001 : 4414 nets have [3 - 5] pins
RUN-1001 : 897 nets have [6 - 10] pins
RUN-1001 : 394 nets have [11 - 20] pins
RUN-1001 : 129 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 40, tpin num: 67918, tnet num: 19442, tinst num: 8139, tnode num: 92051, tedge num: 112003.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.699840s wall, 1.703125s user + 0.000000s system = 1.703125s CPU (100.2%)

RUN-1004 : used memory is 622 MB, reserved memory is 599 MB, peak memory is 727 MB
PHY-1001 : 4021 mslices, 4015 lslices, 60 pads, 40 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19442 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 675984, over cnt = 2265(6%), over = 3782, worst = 8
PHY-1002 : len = 693360, over cnt = 1323(3%), over = 1807, worst = 7
PHY-1002 : len = 705904, over cnt = 642(1%), over = 852, worst = 6
PHY-1002 : len = 720000, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 720176, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.952075s wall, 3.140625s user + 0.031250s system = 3.171875s CPU (162.5%)

PHY-1001 : Congestion index: top1 = 48.36, top5 = 43.10, top10 = 40.44, top15 = 38.68.
PHY-1001 : End global routing;  2.302456s wall, 3.484375s user + 0.031250s system = 3.515625s CPU (152.7%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 699, reserve = 686, peak = 727.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 971, reserve = 957, peak = 971.
PHY-1001 : End build detailed router design. 4.508490s wall, 4.468750s user + 0.046875s system = 4.515625s CPU (100.2%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 192944, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.928827s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (99.3%)

PHY-1001 : Current memory(MB): used = 1008, reserve = 994, peak = 1008.
PHY-1001 : End phase 1; 0.936522s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (100.1%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 55% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.76067e+06, over cnt = 1346(0%), over = 1351, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1025, reserve = 1013, peak = 1025.
PHY-1001 : End initial routed; 17.758943s wall, 43.734375s user + 0.406250s system = 44.140625s CPU (248.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18171(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.181   |   0.000   |   0   
RUN-1001 :   Hold   |   0.130   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.465167s wall, 3.468750s user + 0.000000s system = 3.468750s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1036, reserve = 1025, peak = 1036.
PHY-1001 : End phase 2; 21.224271s wall, 47.203125s user + 0.406250s system = 47.609375s CPU (224.3%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.76067e+06, over cnt = 1346(0%), over = 1351, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.259309s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (102.4%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.74643e+06, over cnt = 388(0%), over = 389, worst = 2, crit = 0
PHY-1001 : End DR Iter 1; 0.865273s wall, 1.546875s user + 0.015625s system = 1.562500s CPU (180.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.74731e+06, over cnt = 61(0%), over = 61, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.424530s wall, 0.562500s user + 0.000000s system = 0.562500s CPU (132.5%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.7479e+06, over cnt = 12(0%), over = 12, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.242335s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (103.2%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.7481e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.175149s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (98.1%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.74814e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.158031s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (98.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18171(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.181   |   0.000   |   0   
RUN-1001 :   Hold   |   0.130   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.500601s wall, 3.500000s user + 0.000000s system = 3.500000s CPU (100.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 323 feed throughs used by 275 nets
PHY-1001 : End commit to database; 2.200832s wall, 2.171875s user + 0.015625s system = 2.187500s CPU (99.4%)

PHY-1001 : Current memory(MB): used = 1124, reserve = 1115, peak = 1124.
PHY-1001 : End phase 3; 8.312861s wall, 9.125000s user + 0.031250s system = 9.156250s CPU (110.1%)

PHY-1003 : Routed, final wirelength = 1.74814e+06
PHY-1001 : Current memory(MB): used = 1128, reserve = 1120, peak = 1128.
PHY-1001 : End export database. 0.173522s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (99.1%)

PHY-1001 : End detail routing;  35.566888s wall, 62.312500s user + 0.484375s system = 62.796875s CPU (176.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 40, tpin num: 67918, tnet num: 19442, tinst num: 8139, tnode num: 92051, tedge num: 112003.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.702511s wall, 1.703125s user + 0.000000s system = 1.703125s CPU (100.0%)

RUN-1004 : used memory is 1062 MB, reserved memory is 1061 MB, peak memory is 1128 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  44.020770s wall, 71.921875s user + 0.546875s system = 72.468750s CPU (164.6%)

RUN-1004 : used memory is 1061 MB, reserved memory is 1061 MB, peak memory is 1128 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        65
  #input                   26
  #output                  37
  #inout                    2

Utilization Statistics
#lut                     8879   out of  19600   45.30%
#reg                    12426   out of  19600   63.40%
#le                     15069
  #lut only              2643   out of  15069   17.54%
  #reg only              6190   out of  15069   41.08%
  #lut&reg               6236   out of  15069   41.38%
#dsp                        0   out of     29    0.00%
#bram                      40   out of     64   62.50%
  #bram9k                  38
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                    10
  #oreg                    34
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6810
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          152
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         M3        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         P6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
     spi_en        OUTPUT        E12        LVCMOS33           8            N/A            OREG       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15069  |7377    |1502    |12471   |40      |0       |
|  AnyFog_dataX                      |AnyFog          |204    |95      |22      |169     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |84     |62      |22      |49      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |211    |84      |22      |175     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |87     |59      |22      |51      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |208    |79      |22      |174     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |61      |22      |52      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2932   |635     |39      |2844    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |60     |36      |5       |52      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |213    |96      |5       |204     |0       |0       |
|    STADOP_com2                     |STADOP          |542    |78      |0       |535     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |47      |14      |42      |0       |0       |
|    head_com2                       |uniheading      |278    |97      |5       |262     |0       |0       |
|    rmc_com2                        |Gprmc           |42     |42      |0       |34      |0       |0       |
|    uart_com2                       |Agrica          |1414   |213     |10      |1397    |0       |0       |
|  COM3                              |COM3_Control    |274    |146     |19      |236     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |60     |36      |5       |52      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |60     |40      |14      |37      |0       |0       |
|    rmc_com3                        |Gprmc           |154    |70      |0       |147     |0       |0       |
|  DATA                              |Data_Processing |8824   |4604    |1122    |7020    |0       |0       |
|    DIV_Dtemp                       |Divider         |793    |308     |84      |665     |0       |0       |
|    DIV_Utemp                       |Divider         |633    |325     |84      |489     |0       |0       |
|    DIV_accX                        |Divider         |619    |321     |84      |490     |0       |0       |
|    DIV_accY                        |Divider         |604    |323     |102     |445     |0       |0       |
|    DIV_accZ                        |Divider         |655    |400     |132     |452     |0       |0       |
|    DIV_rateX                       |Divider         |681    |405     |132     |474     |0       |0       |
|    DIV_rateY                       |Divider         |598    |389     |132     |396     |0       |0       |
|    DIV_rateZ                       |Divider         |593    |356     |132     |385     |0       |0       |
|    genclk                          |genclk          |256    |161     |89      |96      |0       |0       |
|  FMC                               |FMC_Ctrl        |453    |399     |43      |348     |0       |0       |
|  IIC                               |I2C_master      |307    |271     |11      |245     |0       |0       |
|  IMU_CTRL                          |SCHA634         |931    |662     |61      |735     |0       |0       |
|    CtrlData                        |CtrlData        |482    |432     |47      |329     |0       |0       |
|      usms                          |Time_1ms        |27     |22      |5       |19      |0       |0       |
|    SPIM                            |SPI_SCHA634     |449    |230     |14      |406     |0       |0       |
|  POWER                             |POWER_EN        |100    |45      |38      |41      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |610    |357     |103     |424     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |610    |357     |103     |424     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |264    |123     |0       |253     |0       |0       |
|        reg_inst                    |register        |261    |121     |0       |250     |0       |0       |
|        tap_inst                    |tap             |3      |2       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |346    |234     |103     |171     |0       |0       |
|        bus_inst                    |bus_top         |130    |83      |46      |49      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |28     |18      |10      |12      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |49     |30      |18      |16      |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |50     |32      |18      |18      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |140    |104     |29      |92      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13540  
    #2          2       3430   
    #3          3        680   
    #4          4        304   
    #5        5-10       970   
    #6        11-50      441   
    #7       51-100       9    
    #8       101-500      3    
    #9        >500        2    
  Average     2.14             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.084540s wall, 3.562500s user + 0.031250s system = 3.593750s CPU (172.4%)

RUN-1004 : used memory is 1061 MB, reserved memory is 1062 MB, peak memory is 1128 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 40, tpin num: 67918, tnet num: 19442, tinst num: 8139, tnode num: 92051, tedge num: 112003.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.759665s wall, 1.750000s user + 0.000000s system = 1.750000s CPU (99.5%)

RUN-1004 : used memory is 1064 MB, reserved memory is 1065 MB, peak memory is 1128 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19442 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.473951s wall, 1.484375s user + 0.000000s system = 1.484375s CPU (100.7%)

RUN-1004 : used memory is 1069 MB, reserved memory is 1069 MB, peak memory is 1128 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 19285304e968e394d67a8f065444a1f66c49f77adeb419c41e6b7764d8228547 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8139
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19444, pip num: 147867
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 323
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3260 valid insts, and 414222 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011111001000110011001111
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.417468s wall, 124.437500s user + 0.250000s system = 124.687500s CPU (1004.1%)

RUN-1004 : used memory is 1191 MB, reserved memory is 1175 MB, peak memory is 1305 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250822_115050.log"

`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: tianhailu
// Engineer: Andrew
// 
// Create Date:    	15:33:33 8/27/2025 
// Design Name: 	INS3XX
// Module Name:    	dronecan_protocol_parser  
// Project Name: 	INS3XX
// Target Devices: 
// Tool versions: 	TD5.0.1-64bit
// DroneCAN协议完整解析器
// 支持CAN 2.0B帧格式，多帧传输，标准数据类型解析
// Additional Comments: 
// /* synthesis keep */
//////////////////////////////////////////////////////////////////////////////////
module dronecan_protocol_parser (
    input wire clk,                    // 系统时钟 50MHz
    input wire rst_n,                  // 复位信号 (低电平有效)
    
    // CAN总线物理接口
    input wire can_rx,                 // CAN接收线
    output wire can_tx,                // CAN发送线
    
    // 解析后的数据输出
    // 节点状态 (uavcan.protocol.NodeStatus - 必需消息)
    output reg [7:0] node_status_health,      // 节点健康状态
    output reg [7:0] node_status_mode,        // 节点模式
    output reg [31:0] node_status_uptime,     // 运行时间 (秒)
    output reg [31:0] node_status_vendor_code, // 供应商特定状态码
    output reg node_status_valid,
    
    // GNSS数据 (uavcan.equipment.gnss.Fix) - 完整数据集
    output reg [63:0] gnss_timestamp_usec,    // GPS时间戳 (微秒)
    output reg [63:0] gnss_latitude_deg_1e8,  // 纬度 (*1e8度)
    output reg [63:0] gnss_longitude_deg_1e8, // 经度 (*1e8度)
    output reg [31:0] gnss_height_ellipsoid_mm, // 椭球高度 (毫米)
    output reg [31:0] gnss_height_msl_mm,     // 海平面高度 (毫米)
    
    // NED速度分量 (北东下坐标系)
    output reg [31:0] gnss_velocity_north_m_s,  // 北向速度 (m/s, IEEE754)
    output reg [31:0] gnss_velocity_east_m_s,   // 东向速度 (m/s, IEEE754)
    output reg [31:0] gnss_velocity_down_m_s,   // 下向速度 (m/s, IEEE754)
    
    // 速度准确度和状态
    output reg [15:0] gnss_velocity_accuracy_m_s, // 速度精度 (m/s * 100)
    output reg [31:0] gnss_ground_speed_m_s,    // 地面速度 (m/s, IEEE754)
    output reg [31:0] gnss_course_over_ground,  // 地面航向 (弧度, IEEE754)
    
    // 位置精度信息
    output reg [15:0] gnss_position_accuracy_mm, // 位置精度 (毫米)
    output reg [15:0] gnss_hdop,                // 水平精度因子 (*100)
    output reg [15:0] gnss_vdop,                // 垂直精度因子 (*100)
    
    // 卫星和状态信息
    output reg [7:0] gnss_sats_used,           // 使用的卫星数
    output reg [7:0] gnss_sats_visible,        // 可见卫星数
    output reg [7:0] gnss_fix_type,            // 定位类型 (0=无效, 2=2D, 3=3D, 4=RTK等)
    output reg [7:0] gnss_status,              // GNSS系统状态
    output reg gnss_valid,
    
    // 罗盘数据 (uavcan.equipment.ahrs.MagneticFieldStrength)
    output reg [31:0] mag_field_ga_x,         // X轴磁场强度 (高斯)
    output reg [31:0] mag_field_ga_y,         // Y轴磁场强度 (高斯)
    output reg [31:0] mag_field_ga_z,         // Z轴磁场强度 (高斯)
    output reg mag_field_valid,
    
    // IMU数据 (uavcan.equipment.ahrs.RawIMU) 
    output reg [31:0] imu_timestamp_usec,
    output reg [31:0] imu_rate_gyro_x,        // X轴角速度 rad/s
    output reg [31:0] imu_rate_gyro_y,        // Y轴角速度 rad/s
    output reg [31:0] imu_rate_gyro_z,        // Z轴角速度 rad/s
    output reg [31:0] imu_accelerometer_x,    // X轴加速度 m/s^2
    output reg [31:0] imu_accelerometer_y,    // Y轴加速度 m/s^2
    output reg [31:0] imu_accelerometer_z,    // Z轴加速度 m/s^2
    output reg imu_valid,
    
    // 电池状态 (uavcan.equipment.power.BatteryInfo)
    output reg [15:0] battery_voltage,         // 电压 mV
    output reg [15:0] battery_current,         // 电流 mA
    output reg [7:0] battery_remaining_pct,    // 剩余百分比
    output reg battery_valid,
    
    // 系统状态
    output reg [15:0] active_node_mask,       // 活跃节点掩码
    output reg [7:0] protocol_errors,         // 协议错误计数
    output reg [7:0] current_node_id,         // 当前处理的节点ID
    output reg data_ready                     // 数据就绪标志
);

// DroneCAN标准数据类型ID定义
parameter DTID_NODE_STATUS      = 16'd341;   // uavcan.protocol.NodeStatus
parameter DTID_GNSS_FIX         = 16'd1060;  // uavcan.equipment.gnss.Fix
parameter DTID_MAGNETIC_FIELD   = 16'd1001;  // uavcan.equipment.ahrs.MagneticFieldStrength
parameter DTID_RAW_IMU          = 16'd1000;  // uavcan.equipment.ahrs.RawIMU
parameter DTID_BATTERY_INFO     = 16'd1092;  // uavcan.equipment.power.BatteryInfo

// CAN位定时参数 (125kbps @ 50MHz)
parameter BIT_TIME = 9'd400;
parameter SAMPLE_POINT = 9'd320;  // 80%采样点

// CAN接收状态机状态定义
localparam CAN_IDLE = 4'b0000;
localparam CAN_SOF = 4'b0001;
localparam CAN_ID_HIGH = 4'b0010;
localparam CAN_ID_MID = 4'b0011;
localparam CAN_ID_LOW = 4'b0100;
localparam CAN_CONTROL = 4'b0101;
localparam CAN_DATA = 4'b0110;
localparam CAN_CRC = 4'b0111;
localparam CAN_ACK = 4'b1000;
localparam CAN_EOF = 4'b1001;
localparam CAN_PROCESS = 4'b1010;

reg [3:0] can_rx_state;

// DroneCAN传输重组状态定义
localparam TRANSFER_IDLE = 3'b000;
localparam TRANSFER_START = 3'b001;
localparam TRANSFER_MULTI = 3'b010;
localparam TRANSFER_COMPLETE = 3'b011;
localparam TRANSFER_ERROR = 3'b100;

reg [2:0] transfer_state;

// CAN帧缓冲区 - 使用标准寄存器
reg [28:0] rx_can_id;              // 29位CAN ID
reg [7:0] rx_dlc;                  // 数据长度码
reg [7:0] rx_data_0, rx_data_1, rx_data_2, rx_data_3;  // 数据缓冲区
reg [7:0] rx_data_4, rx_data_5, rx_data_6, rx_data_7;  // 数据缓冲区
reg [15:0] rx_crc_received;        // 接收到的CRC
reg [15:0] rx_crc_calculated;      // 计算的CRC

// DroneCAN帧解析字段
reg [15:0] dtid;                   // 数据类型ID
reg [6:0] source_node_id;          // 源节点ID
reg [7:0] transfer_id;             // 传输ID
reg start_of_transfer;             // 传输开始标志
reg end_of_transfer;               // 传输结束标志
reg toggle;                        // 切换位

// 多帧传输重组缓冲区 - 展开为独立寄存器
reg [7:0] transfer_buffer_0, transfer_buffer_1, transfer_buffer_2, transfer_buffer_3;
reg [7:0] transfer_buffer_4, transfer_buffer_5, transfer_buffer_6, transfer_buffer_7;
reg [7:0] transfer_buffer_8, transfer_buffer_9, transfer_buffer_10, transfer_buffer_11;
reg [7:0] transfer_buffer_12, transfer_buffer_13, transfer_buffer_14, transfer_buffer_15;
reg [7:0] transfer_buffer_16, transfer_buffer_17, transfer_buffer_18, transfer_buffer_19;
reg [7:0] transfer_buffer_20, transfer_buffer_21, transfer_buffer_22, transfer_buffer_23;
reg [7:0] transfer_buffer_24, transfer_buffer_25, transfer_buffer_26, transfer_buffer_27;
reg [7:0] transfer_buffer_28, transfer_buffer_29, transfer_buffer_30, transfer_buffer_31;
// 对于大多数DroneCAN消息，32字节缓冲区就足够了

reg [7:0] transfer_length;         // 传输总长度
reg [7:0] expected_toggle;         // 期望的切换位
reg [7:0] current_transfer_id;     // 当前传输ID

// 数据缓冲区读写函数
function [7:0] get_rx_data;
    input [2:0] index;
    begin
        case (index)
            3'd0: get_rx_data = rx_data_0;
            3'd1: get_rx_data = rx_data_1;
            3'd2: get_rx_data = rx_data_2;
            3'd3: get_rx_data = rx_data_3;
            3'd4: get_rx_data = rx_data_4;
            3'd5: get_rx_data = rx_data_5;
            3'd6: get_rx_data = rx_data_6;
            3'd7: get_rx_data = rx_data_7;
        endcase
    end
endfunction

task set_rx_data;
    input [2:0] index;
    input [7:0] data;
    begin
        case (index)
            3'd0: rx_data_0 <= data;
            3'd1: rx_data_1 <= data;
            3'd2: rx_data_2 <= data;
            3'd3: rx_data_3 <= data;
            3'd4: rx_data_4 <= data;
            3'd5: rx_data_5 <= data;
            3'd6: rx_data_6 <= data;
            3'd7: rx_data_7 <= data;
// 位定时和采样
reg [8:0] bit_counter;
reg bit_sample;
reg [4:0] bit_index;
reg [2:0] byte_index;

function [7:0] get_transfer_buffer;
    input [4:0] index;  // 最大32个字节
    begin
        case (index)
            5'd0:  get_transfer_buffer = transfer_buffer_0;
            5'd1:  get_transfer_buffer = transfer_buffer_1;
            5'd2:  get_transfer_buffer = transfer_buffer_2;
            5'd3:  get_transfer_buffer = transfer_buffer_3;
            5'd4:  get_transfer_buffer = transfer_buffer_4;
            5'd5:  get_transfer_buffer = transfer_buffer_5;
            5'd6:  get_transfer_buffer = transfer_buffer_6;
            5'd7:  get_transfer_buffer = transfer_buffer_7;
            5'd8:  get_transfer_buffer = transfer_buffer_8;
            5'd9:  get_transfer_buffer = transfer_buffer_9;
            5'd10: get_transfer_buffer = transfer_buffer_10;
            5'd11: get_transfer_buffer = transfer_buffer_11;
            5'd12: get_transfer_buffer = transfer_buffer_12;
            5'd13: get_transfer_buffer = transfer_buffer_13;
            5'd14: get_transfer_buffer = transfer_buffer_14;
            5'd15: get_transfer_buffer = transfer_buffer_15;
            5'd16: get_transfer_buffer = transfer_buffer_16;
            5'd17: get_transfer_buffer = transfer_buffer_17;
            5'd18: get_transfer_buffer = transfer_buffer_18;
            5'd19: get_transfer_buffer = transfer_buffer_19;
            5'd20: get_transfer_buffer = transfer_buffer_20;
            5'd21: get_transfer_buffer = transfer_buffer_21;
            5'd22: get_transfer_buffer = transfer_buffer_22;
            5'd23: get_transfer_buffer = transfer_buffer_23;
            5'd24: get_transfer_buffer = transfer_buffer_24;
            5'd25: get_transfer_buffer = transfer_buffer_25;
            5'd26: get_transfer_buffer = transfer_buffer_26;
            5'd27: get_transfer_buffer = transfer_buffer_27;
            5'd28: get_transfer_buffer = transfer_buffer_28;
            5'd29: get_transfer_buffer = transfer_buffer_29;
            5'd30: get_transfer_buffer = transfer_buffer_30;
            5'd31: get_transfer_buffer = transfer_buffer_31;
        endcase
    end
endfunction

task set_transfer_buffer;
    input [4:0] index;
    input [7:0] data;
    begin
        case (index)
            5'd0:  transfer_buffer_0 <= data;
            5'd1:  transfer_buffer_1 <= data;
            5'd2:  transfer_buffer_2 <= data;
            5'd3:  transfer_buffer_3 <= data;
            5'd4:  transfer_buffer_4 <= data;
            5'd5:  transfer_buffer_5 <= data;
            5'd6:  transfer_buffer_6 <= data;
            5'd7:  transfer_buffer_7 <= data;
            5'd8:  transfer_buffer_8 <= data;
            5'd9:  transfer_buffer_9 <= data;
            5'd10: transfer_buffer_10 <= data;
            5'd11: transfer_buffer_11 <= data;
            5'd12: transfer_buffer_12 <= data;
            5'd13: transfer_buffer_13 <= data;
            5'd14: transfer_buffer_14 <= data;
            5'd15: transfer_buffer_15 <= data;
            5'd16: transfer_buffer_16 <= data;
            5'd17: transfer_buffer_17 <= data;
            5'd18: transfer_buffer_18 <= data;
            5'd19: transfer_buffer_19 <= data;
            5'd20: transfer_buffer_20 <= data;
            5'd21: transfer_buffer_21 <= data;
            5'd22: transfer_buffer_22 <= data;
            5'd23: transfer_buffer_23 <= data;
            5'd24: transfer_buffer_24 <= data;
            5'd25: transfer_buffer_25 <= data;
            5'd26: transfer_buffer_26 <= data;
            5'd27: transfer_buffer_27 <= data;
            5'd28: transfer_buffer_28 <= data;
            5'd29: transfer_buffer_29 <= data;
            5'd30: transfer_buffer_30 <= data;
            5'd31: transfer_buffer_31 <= data;
        endcase
    end
endtask

// 位定时生成器
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        bit_counter <= 9'd0;
        bit_sample <= 1'b0;
    end else begin
        if (can_rx_state != CAN_IDLE) begin
            if (bit_counter >= BIT_TIME - 1) begin
                bit_counter <= 9'd0;
                bit_sample <= 1'b0;
            end else begin
                bit_counter <= bit_counter + 1;
                bit_sample <= (bit_counter == SAMPLE_POINT);
            end
        end else begin
            bit_counter <= 9'd0;
            bit_sample <= 1'b0;
        end
    end
end

// CAN帧接收状态机
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        can_rx_state <= CAN_IDLE;
        rx_can_id <= 29'd0;
        rx_dlc <= 8'd0;
        bit_index <= 5'd0;
        byte_index <= 3'd0;
        protocol_errors <= 8'd0;
    end else begin
        case (can_rx_state)
            CAN_IDLE: begin
                if (!can_rx) begin // 检测SOF
                    can_rx_state <= CAN_SOF;
                    bit_index <= 5'd0;
                end
            end
            
            CAN_SOF: begin
                if (bit_sample) begin
                    can_rx_state <= CAN_ID_HIGH;
                    bit_index <= 5'd0;
                end
            end
            
            CAN_ID_HIGH: begin
                if (bit_sample) begin
                    if (bit_index < 11) begin // ID[28:18]
                        rx_can_id[28 - bit_index] <= can_rx;
                        bit_index <= bit_index + 1;
                    end else begin
                        can_rx_state <= CAN_ID_MID;
                        bit_index <= 5'd0;
                    end
                end
            end
            
            CAN_ID_MID: begin
                if (bit_sample) begin
                    if (bit_index < 6) begin // SRR, IDE, ID[17:15]
                        if (bit_index >= 2)
                            rx_can_id[17 - (bit_index - 2)] <= can_rx;
                        bit_index <= bit_index + 1;
                    end else begin
                        can_rx_state <= CAN_ID_LOW;
                        bit_index <= 5'd0;
                    end
                end
            end
            
            CAN_ID_LOW: begin
                if (bit_sample) begin
                    if (bit_index < 18) begin // ID[14:0], RTR, r1, r0
                        if (bit_index < 15)
                            rx_can_id[14 - bit_index] <= can_rx;
                        bit_index <= bit_index + 1;
                    end else begin
                        can_rx_state <= CAN_CONTROL;
                        bit_index <= 5'd0;
                    end
                end
            end
            
            CAN_CONTROL: begin
                if (bit_sample) begin
                    if (bit_index < 4) begin // DLC
                        rx_dlc[3 - bit_index] <= can_rx;
                        bit_index <= bit_index + 1;
                    end else begin
                        if (rx_dlc > 0) begin
                            can_rx_state <= CAN_DATA;
                            bit_index <= 5'd0;
                            byte_index <= 3'd0;
                        end else begin
                            can_rx_state <= CAN_CRC;
                            bit_index <= 5'd0;
                        end
                    end
                end
            end
            
            CAN_DATA: begin
                if (bit_sample) begin
                    // 使用task来设置接收数据
                    if ((bit_index % 8) == 0 && bit_index > 0) begin
                        byte_index <= byte_index + 1;
                    end
                    
                    // 根据位位置设置数据
                    case (byte_index)
                        3'd0: rx_data_0[7 - (bit_index % 8)] <= can_rx;
                        3'd1: rx_data_1[7 - (bit_index % 8)] <= can_rx;
                        3'd2: rx_data_2[7 - (bit_index % 8)] <= can_rx;
                        3'd3: rx_data_3[7 - (bit_index % 8)] <= can_rx;
                        3'd4: rx_data_4[7 - (bit_index % 8)] <= can_rx;
                        3'd5: rx_data_5[7 - (bit_index % 8)] <= can_rx;
                        3'd6: rx_data_6[7 - (bit_index % 8)] <= can_rx;
                        3'd7: rx_data_7[7 - (bit_index % 8)] <= can_rx;
                    endcase
                    
                    bit_index <= bit_index + 1;
                    
                    if ((bit_index % 8) == 7 && byte_index >= rx_dlc - 1) begin
                        can_rx_state <= CAN_CRC;
                        bit_index <= 5'd0;
                    end
                end
            end
            
            CAN_CRC: begin
                if (bit_sample) begin
                    if (bit_index < 16) begin
                        rx_crc_received[15 - bit_index] <= can_rx;
                        bit_index <= bit_index + 1;
                    end else begin
                        can_rx_state <= CAN_ACK;
                        bit_index <= 5'd0;
                    end
                end
            end
            
            CAN_ACK: begin
                if (bit_sample) begin
                    if (bit_index < 2) begin
                        bit_index <= bit_index + 1;
                    end else begin
                        can_rx_state <= CAN_EOF;
                        bit_index <= 5'd0;
                    end
                end
            end
            
            CAN_EOF: begin
                if (bit_sample) begin
                    if (bit_index < 7) begin
                        bit_index <= bit_index + 1;
                    end else begin
                        can_rx_state <= CAN_PROCESS;
                    end
                end
            end
            
            CAN_PROCESS: begin
                // 处理接收到的CAN帧
                parse_dronecan_frame();
                can_rx_state <= CAN_IDLE;
            end
            
            default: begin
                can_rx_state <= CAN_IDLE;
                protocol_errors <= protocol_errors + 1;
            end
        endcase
    end
end

// DroneCAN帧解析任务
task parse_dronecan_frame;
    reg [7:0] tail_byte;
    begin
        // 解析CAN ID字段
        if (rx_can_id[28:24] == 5'b00000) begin // 消息传输
            dtid = {rx_can_id[20:16], rx_can_id[9:0], 1'b0}; // 重构DTID
            source_node_id = rx_can_id[15:9];
            
            // 解析尾字节
            if (rx_dlc > 0) begin
                tail_byte = get_rx_data(rx_dlc - 1);
                transfer_id = tail_byte[7:3];
                start_of_transfer = tail_byte[2];
                end_of_transfer = tail_byte[1];
                toggle = tail_byte[0];
                
                current_node_id = source_node_id;
                
                // 处理传输
                process_dronecan_transfer();
            end
        end
    end
endtask

// DroneCAN传输处理任务
task process_dronecan_transfer;
    integer i;
    begin
        case (transfer_state)
            TRANSFER_IDLE: begin
                if (start_of_transfer) begin
                    // 开始新传输
                    current_transfer_id = transfer_id;
                    expected_toggle = toggle;
                    transfer_length = 8'd0;
                    
                    if (end_of_transfer) begin
                        // 单帧传输 - 直接复制数据
                        for (i = 0; i < rx_dlc - 1; i = i + 1) begin
                            if (i < 32) begin
                                set_transfer_buffer(i, get_rx_data(i));
                            end
                        end
                        transfer_length = rx_dlc - 1;
                        transfer_state = TRANSFER_COMPLETE;
                    end else begin
                        // 多帧传输开始
                        for (i = 0; i < rx_dlc - 1; i = i + 1) begin
                            if (transfer_length + i < 32) begin
                                set_transfer_buffer(transfer_length + i, get_rx_data(i));
                            end
                        end
                        transfer_length = transfer_length + rx_dlc - 1;
                        transfer_state = TRANSFER_MULTI;
                        expected_toggle = ~toggle;
                    end
                end
            end
            
            TRANSFER_MULTI: begin
                if (transfer_id == current_transfer_id && toggle == expected_toggle) begin
                    // 继续多帧传输
                    for (i = 0; i < rx_dlc - 1; i = i + 1) begin
                        if (transfer_length + i < 32) begin
                            set_transfer_buffer(transfer_length + i, get_rx_data(i));
                        end
                    end
                    transfer_length = transfer_length + rx_dlc - 1;
                    
                    if (end_of_transfer) begin
                        transfer_state = TRANSFER_COMPLETE;
                    end else begin
                        expected_toggle = ~toggle;
                    end
                end else begin
                    // 传输错误
                    transfer_state = TRANSFER_ERROR;
                    protocol_errors = protocol_errors + 1;
                end
            end
            
            TRANSFER_COMPLETE: begin
                // 解析完整的数据类型
                decode_dronecan_message();
                transfer_state = TRANSFER_IDLE;
                data_ready = 1'b1;
                
                // 更新活跃节点掩码
                if (source_node_id < 16) begin
                    active_node_mask[source_node_id] = 1'b1;
                end
            end
            
            TRANSFER_ERROR: begin
                transfer_state = TRANSFER_IDLE;
            end
        endcase
    end
endtask

// DroneCAN消息解码任务
task decode_dronecan_message;
    begin
        case (dtid)
            DTID_NODE_STATUS: decode_node_status();
            DTID_GNSS_FIX: decode_gnss_fix();
            DTID_MAGNETIC_FIELD: decode_magnetic_field();
            DTID_RAW_IMU: decode_raw_imu();
            DTID_BATTERY_INFO: decode_battery_info();
            default: begin
                // 未知数据类型
            end
        endcase
    end
endtask

// 节点状态解码 (uavcan.protocol.NodeStatus)
task decode_node_status;
    begin
        if (transfer_length >= 7) begin
            node_status_uptime = {transfer_buffer[3], transfer_buffer[2], 
                                transfer_buffer[1], transfer_buffer[0]};
            node_status_health = transfer_buffer[4];
            node_status_mode = transfer_buffer[5];
            node_status_vendor_code = {transfer_buffer[6], 16'h0000};
            node_status_valid = 1'b1;
        end
    end
endtask

// 节点状态解码 (uavcan.protocol.NodeStatus)
task decode_node_status;
    begin
        if (transfer_length >= 7) begin
            node_status_uptime = {get_transfer_buffer(3), get_transfer_buffer(2), 
                                get_transfer_buffer(1), get_transfer_buffer(0)};
            node_status_health = get_transfer_buffer(4);
            node_status_mode = get_transfer_buffer(5);
            node_status_vendor_code = {get_transfer_buffer(6), 16'h0000};
            node_status_valid = 1'b1;
        end
    end
endtask

// GNSS定位解码 (uavcan.equipment.gnss.Fix) - 完整版本
task decode_gnss_fix;
    integer byte_offset;
    begin
        if (transfer_length >= 32) begin // 限制为32字节缓冲区
            byte_offset = 0;
            
            // GPS时间戳 (8字节, uint64)
            gnss_timestamp_usec = {get_transfer_buffer(byte_offset+7), get_transfer_buffer(byte_offset+6),
                                 get_transfer_buffer(byte_offset+5), get_transfer_buffer(byte_offset+4),
                                 get_transfer_buffer(byte_offset+3), get_transfer_buffer(byte_offset+2),
                                 get_transfer_buffer(byte_offset+1), get_transfer_buffer(byte_offset+0)};
            byte_offset = byte_offset + 8;
            
            // 纬度 (8字节, int64, *1e8度)
            gnss_latitude_deg_1e8 = {get_transfer_buffer(byte_offset+7), get_transfer_buffer(byte_offset+6),
                                   get_transfer_buffer(byte_offset+5), get_transfer_buffer(byte_offset+4),
                                   get_transfer_buffer(byte_offset+3), get_transfer_buffer(byte_offset+2),
                                   get_transfer_buffer(byte_offset+1), get_transfer_buffer(byte_offset+0)};
            byte_offset = byte_offset + 8;
            
            // 经度 (8字节, int64, *1e8度)
            gnss_longitude_deg_1e8 = {get_transfer_buffer(byte_offset+7), get_transfer_buffer(byte_offset+6),
                                    get_transfer_buffer(byte_offset+5), get_transfer_buffer(byte_offset+4),
                                    get_transfer_buffer(byte_offset+3), get_transfer_buffer(byte_offset+2),
                                    get_transfer_buffer(byte_offset+1), get_transfer_buffer(byte_offset+0)};
            
            // 对于32字节限制，这里只解析基本的位置信息
// 数据有效标志清除 (定时清除)
reg [15:0] validity_timer;
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        validity_timer <= 16'd0;
        node_status_valid <= 1'b0;
        gnss_valid <= 1'b0;
        mag_field_valid <= 1'b0;
        imu_valid <= 1'b0;
        battery_valid <= 1'b0;
        data_ready <= 1'b0;
    end else begin
        validity_timer <= validity_timer + 1;
        
        // 清除数据就绪标志
        if (data_ready) begin
            data_ready <= 1'b0;
        end
        
        // 超时清除有效标志 (约1秒 @ 50MHz)
        if (validity_timer >= 16'd50000) begin
            validity_timer <= 16'd0;
            // 注意：实际应用中应该基于每个数据类型的具体超时要求
        end
    end
end

// CAN发送 (简化版本 - 用于ACK)
assign can_tx = 1'b1; // 默认隐性状态

endmodule

// 磁场强度解码 (uavcan.equipment.ahrs.MagneticFieldStrength)
task decode_magnetic_field;
    begin
        if (transfer_length >= 12) begin
            mag_field_ga_x = {get_transfer_buffer(3), get_transfer_buffer(2),
                             get_transfer_buffer(1), get_transfer_buffer(0)};
            mag_field_ga_y = {get_transfer_buffer(7), get_transfer_buffer(6),
                             get_transfer_buffer(5), get_transfer_buffer(4)};
            mag_field_ga_z = {get_transfer_buffer(11), get_transfer_buffer(10),
                             get_transfer_buffer(9), get_transfer_buffer(8)};
            mag_field_valid = 1'b1;
        end
    end
endtask

// 原始IMU数据解码 (uavcan.equipment.ahrs.RawIMU)
task decode_raw_imu;
    begin
        if (transfer_length >= 28) begin
            imu_timestamp_usec = {get_transfer_buffer(3), get_transfer_buffer(2),
                                get_transfer_buffer(1), get_transfer_buffer(0)};
            
            // 角速度 (rad/s)
            imu_rate_gyro_x = {get_transfer_buffer(7), get_transfer_buffer(6),
                              get_transfer_buffer(5), get_transfer_buffer(4)};
            imu_rate_gyro_y = {get_transfer_buffer(11), get_transfer_buffer(10),
                              get_transfer_buffer(9), get_transfer_buffer(8)};
            imu_rate_gyro_z = {get_transfer_buffer(15), get_transfer_buffer(14),
                              get_transfer_buffer(13), get_transfer_buffer(12)};
            
            // 加速度 (m/s^2)
            imu_accelerometer_x = {get_transfer_buffer(19), get_transfer_buffer(18),
                                  get_transfer_buffer(17), get_transfer_buffer(16)};
            imu_accelerometer_y = {get_transfer_buffer(23), get_transfer_buffer(22),
                                  get_transfer_buffer(21), get_transfer_buffer(20)};
            imu_accelerometer_z = {get_transfer_buffer(27), get_transfer_buffer(26),
                                  get_transfer_buffer(25), get_transfer_buffer(24)};
            
            imu_valid = 1'b1;
        end
    end
endtask

// 电池信息解码 (uavcan.equipment.power.BatteryInfo)
task decode_battery_info;
    begin
        if (transfer_length >= 11) begin
            battery_voltage = {get_transfer_buffer(1), get_transfer_buffer(0)};     // mV
            battery_current = {get_transfer_buffer(3), get_transfer_buffer(2)};     // mA
            battery_remaining_pct = get_transfer_buffer(10);                        // %
            battery_valid = 1'b1;
        end
    end
endtask_buffer[byte_offset+2],
                                      transfer_buffer[byte_offset+1], transfer_buffer[byte_offset+0]};
            byte_offset = byte_offset + 4;
            
            // 海平面高度 (4字节, int32, 毫米)
            gnss_height_msl_mm = {transfer_buffer[byte_offset+3], transfer_buffer[byte_offset+2],
                                transfer_buffer[byte_offset+1], transfer_buffer[byte_offset+0]};
            byte_offset = byte_offset + 4;
            
            // NED速度 - 北向速度 (4字节, float32, m/s)
            gnss_velocity_north_m_s = {transfer_buffer[byte_offset+3], transfer_buffer[byte_offset+2],
                                     transfer_buffer[byte_offset+1], transfer_buffer[byte_offset+0]};
            byte_offset = byte_offset + 4;
            
            // NED速度 - 东向速度 (4字节, float32, m/s)
            gnss_velocity_east_m_s = {transfer_buffer[byte_offset+3], transfer_buffer[byte_offset+2],
                                    transfer_buffer[byte_offset+1], transfer_buffer[byte_offset+0]};
            byte_offset = byte_offset + 4;
            
            // NED速度 - 下向速度 (4字节, float32, m/s)
            gnss_velocity_down_m_s = {transfer_buffer[byte_offset+3], transfer_buffer[byte_offset+2],
                                    transfer_buffer[byte_offset+1], transfer_buffer[byte_offset+0]};
            byte_offset = byte_offset + 4;
            
            // 如果消息长度足够，解析更多字段
            if (transfer_length >= byte_offset + 16) begin
                // 位置协方差矩阵 (6个float32值，表示6x6对称矩阵的上三角)
                // 这里我们跳过协方差矩阵，直接到其他有用字段
                byte_offset = byte_offset + 24; // 跳过6个float32 (24字节)
                
                // 速度协方差矩阵 (6个float32值)
                byte_offset = byte_offset + 24; // 跳过6个float32 (24字节)
                
                // 使用的卫星数 (1字节, uint8)
                if (transfer_length > byte_offset) begin
                    gnss_sats_used = transfer_buffer[byte_offset];
                    byte_offset = byte_offset + 1;
                end
                
                // 可见卫星数 (1字节, uint8) 
                if (transfer_length > byte_offset) begin
                    gnss_sats_visible = transfer_buffer[byte_offset];
                    byte_offset = byte_offset + 1;
                end
                
                // 定位类型 (1字节, uint8)
                // 0 = 无定位, 1 = 推算定位, 2 = 2D定位, 3 = 3D定位
                // 4 = RTK固定解, 5 = RTK浮点解
                if (transfer_length > byte_offset) begin
                    gnss_fix_type = transfer_buffer[byte_offset];
                    byte_offset = byte_offset + 1;
                end
                
                // GNSS系统状态位掩码
                if (transfer_length > byte_offset) begin
                    gnss_status = transfer_buffer[byte_offset];
                    byte_offset = byte_offset + 1;
                end
                
                // 位置精度 (2字节, uint16, 毫米)
                if (transfer_length >= byte_offset + 2) begin
                    gnss_position_accuracy_mm = {transfer_buffer[byte_offset+1], transfer_buffer[byte_offset]};
                    byte_offset = byte_offset + 2;
                end
                
                // 速度精度 (2字节, uint16, cm/s)
                if (transfer_length >= byte_offset + 2) begin
                    gnss_velocity_accuracy_m_s = {transfer_buffer[byte_offset+1], transfer_buffer[byte_offset]};
                    byte_offset = byte_offset + 2;
                end
                
                // HDOP - 水平精度稀释 (2字节, uint16, *100)
                if (transfer_length >= byte_offset + 2) begin
                    gnss_hdop = {transfer_buffer[byte_offset+1], transfer_buffer[byte_offset]};
                    byte_offset = byte_offset + 2;
                end
                
                // VDOP - 垂直精度稀释 (2字节, uint16, *100)
                if (transfer_length >= byte_offset + 2) begin
                    gnss_vdop = {transfer_buffer[byte_offset+1], transfer_buffer[byte_offset]};
                    byte_offset = byte_offset + 2;
                end
                
                // 地面速度 (4字节, float32, m/s)
                if (transfer_length >= byte_offset + 4) begin
                    gnss_ground_speed_m_s = {transfer_buffer[byte_offset+3], transfer_buffer[byte_offset+2],
                                           transfer_buffer[byte_offset+1], transfer_buffer[byte_offset+0]};
                    byte_offset = byte_offset + 4;
                end
                
                // 地面航向 (4字节, float32, 弧度)
                if (transfer_length >= byte_offset + 4) begin
                    gnss_course_over_ground = {transfer_buffer[byte_offset+3], transfer_buffer[byte_offset+2],
                                             transfer_buffer[byte_offset+1], transfer_buffer[byte_offset+0]};
                    byte_offset = byte_offset + 4;
                end
            end
            
            gnss_valid = 1'b1;
        end
    end
endtask

// 磁场强度解码 (uavcan.equipment.ahrs.MagneticFieldStrength)
task decode_magnetic_field;
    begin
        if (transfer_length >= 12) begin
            mag_field_ga_x = {transfer_buffer[3], transfer_buffer[2],
                             transfer_buffer[1], transfer_buffer[0]};
            mag_field_ga_y = {transfer_buffer[7], transfer_buffer[6],
                             transfer_buffer[5], transfer_buffer[4]};
            mag_field_ga_z = {transfer_buffer[11], transfer_buffer[10],
                             transfer_buffer[9], transfer_buffer[8]};
            mag_field_valid = 1'b1;
        end
    end
endtask

// 原始IMU数据解码 (uavcan.equipment.ahrs.RawIMU)
task decode_raw_imu;
    begin
        if (transfer_length >= 28) begin
            imu_timestamp_usec = {transfer_buffer[3], transfer_buffer[2],
                                transfer_buffer[1], transfer_buffer[0]};
            
            // 角速度 (rad/s)
            imu_rate_gyro_x = {transfer_buffer[7], transfer_buffer[6],
                              transfer_buffer[5], transfer_buffer[4]};
            imu_rate_gyro_y = {transfer_buffer[11], transfer_buffer[10],
                              transfer_buffer[9], transfer_buffer[8]};
            imu_rate_gyro_z = {transfer_buffer[15], transfer_buffer[14],
                              transfer_buffer[13], transfer_buffer[12]};
            
            // 加速度 (m/s^2)
            imu_accelerometer_x = {transfer_buffer[19], transfer_buffer[18],
                                  transfer_buffer[17], transfer_buffer[16]};
            imu_accelerometer_y = {transfer_buffer[23], transfer_buffer[22],
                                  transfer_buffer[21], transfer_buffer[20]};
            imu_accelerometer_z = {transfer_buffer[27], transfer_buffer[26],
                                  transfer_buffer[25], transfer_buffer[24]};
            
            imu_valid = 1'b1;
        end
    end
endtask

// 电池信息解码 (uavcan.equipment.power.BatteryInfo)
task decode_battery_info;
    begin
        if (transfer_length >= 11) begin
            battery_voltage = {transfer_buffer[1], transfer_buffer[0]};     // mV
            battery_current = {transfer_buffer[3], transfer_buffer[2]};     // mA
            battery_remaining_pct = transfer_buffer[10];                    // %
            battery_valid = 1'b1;
        end
    end
endtask

// 数据有效标志清除 (定时清除)
reg [15:0] validity_timer;
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        validity_timer <= 16'd0;
        node_status_valid <= 1'b0;
        gnss_valid <= 1'b0;
        mag_field_valid <= 1'b0;
        imu_valid <= 1'b0;
        battery_valid <= 1'b0;
        data_ready <= 1'b0;
    end else begin
        validity_timer <= validity_timer + 1;
        
        // 清除数据就绪标志
        if (data_ready) begin
            data_ready <= 1'b0;
        end
        
        // 超时清除有效标志 (约1秒 @ 50MHz)
        if (validity_timer >= 16'd50000) begin
            validity_timer <= 16'd0;
            // 注意：实际应用中应该基于每个数据类型的具体超时要求
        end
    end
end

// CAN发送 (简化版本 - 用于ACK)
assign can_tx = 1'b1; // 默认隐性状态

endmodule

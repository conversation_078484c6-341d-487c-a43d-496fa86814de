============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.5_SP3/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     51593
   Run Date =   Mon Apr 28 18:05:23 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.733507s wall, 1.609375s user + 0.125000s system = 1.734375s CPU (100.1%)

RUN-1004 : used memory is 284 MB, reserved memory is 257 MB, peak memory is 287 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 -duty_cycle 50.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 10 view nodes, 53 trigger nets, 53 data nets.
KIT-1004 : Chipwatcher code = 1101110111001010
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.5_SP3/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=10,BUS_DIN_NUM=53,BUS_CTRL_NUM=146,BUS_WIDTH='{32'sb01,32'sb01,32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01,32'sb010,32'sb0110,32'sb01110,32'sb01111,32'sb010000,32'sb010001,32'sb010101,32'sb0100101},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01100,32'sb011000,32'sb0101100,32'sb0110010,32'sb0111000,32'sb0111110,32'sb01001010,32'sb01101110}) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=168) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=168) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=10,BUS_DIN_NUM=53,BUS_CTRL_NUM=146,BUS_WIDTH='{32'sb01,32'sb01,32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01,32'sb010,32'sb0110,32'sb01110,32'sb01111,32'sb010000,32'sb010001,32'sb010101,32'sb0100101},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01100,32'sb011000,32'sb0101100,32'sb0110010,32'sb0111000,32'sb0111110,32'sb01001010,32'sb01101110}) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=10,BUS_DIN_NUM=53,BUS_CTRL_NUM=146,BUS_WIDTH='{32'sb01,32'sb01,32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01,32'sb010,32'sb0110,32'sb01110,32'sb01111,32'sb010000,32'sb010001,32'sb010101,32'sb0100101},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01100,32'sb011000,32'sb0101100,32'sb0110010,32'sb0111000,32'sb0111110,32'sb01001010,32'sb01101110}) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.5_SP3/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=10,BUS_DIN_NUM=53,BUS_CTRL_NUM=146,BUS_WIDTH='{32'sb01,32'sb01,32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01,32'sb010,32'sb0110,32'sb01110,32'sb01111,32'sb010000,32'sb010001,32'sb010101,32'sb0100101},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01100,32'sb011000,32'sb0101100,32'sb0110010,32'sb0111000,32'sb0111110,32'sb01001010,32'sb01101110})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=168)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=168)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=10,BUS_DIN_NUM=53,BUS_CTRL_NUM=146,BUS_WIDTH='{32'sb01,32'sb01,32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01,32'sb010,32'sb0110,32'sb01110,32'sb01111,32'sb010000,32'sb010001,32'sb010101,32'sb0100101},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01100,32'sb011000,32'sb0101100,32'sb0110010,32'sb0111000,32'sb0111110,32'sb01001010,32'sb01101110})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=10,BUS_DIN_NUM=53,BUS_CTRL_NUM=146,BUS_WIDTH='{32'sb01,32'sb01,32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01,32'sb010,32'sb0110,32'sb01110,32'sb01111,32'sb010000,32'sb010001,32'sb010101,32'sb0100101},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01100,32'sb011000,32'sb0101100,32'sb0110010,32'sb0111000,32'sb0111110,32'sb01001010,32'sb01101110})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 23125/43 useful/useless nets, 19799/25 useful/useless insts
SYN-1016 : Merged 48 instances.
SYN-1032 : 22690/26 useful/useless nets, 20301/22 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 5 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 5 mux instances.
SYN-1015 : Optimize round 1, 501 better
SYN-1014 : Optimize round 2
SYN-1032 : 22256/75 useful/useless nets, 19867/80 useful/useless insts
SYN-1015 : Optimize round 2, 160 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.810810s wall, 2.765625s user + 0.031250s system = 2.796875s CPU (99.5%)

RUN-1004 : used memory is 324 MB, reserved memory is 296 MB, peak memory is 326 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.5_SP3/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |    high    |      medium      |   *    
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 67 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22316/373 useful/useless nets, 19974/58 useful/useless insts
SYN-1016 : Merged 57 instances.
SYN-2571 : Optimize after map_dsp, round 1, 488 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 48 instances.
SYN-2501 : Optimize round 1, 98 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 20 macro adder
SYN-1019 : Optimized 21 mux instances.
SYN-1016 : Merged 199 instances.
SYN-1032 : 22836/5 useful/useless nets, 20494/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1002 : start command "get_pins config_inst.jtck"
RUN-1002 : start command "create_clock -name jtck -period 100 "
RUN-1102 : create_clock: clock name: jtck, type: 0, period: 100000, rise: 0, fall: 50000.
RUN-1002 : start command "get_clocks jtck"
RUN-1002 : start command "set_clock_uncertainty -hold 0.1 "
RUN-1002 : start command "get_clocks jtck"
RUN-1002 : start command "set_clock_groups -exclusive -group "
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 84465, tnet num: 22836, tinst num: 20493, tnode num: 118283, tedge num: 131610.
TMR-2508 : Levelizing timing graph completed, there are 51 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.237524s wall, 1.187500s user + 0.046875s system = 1.234375s CPU (99.7%)

RUN-1004 : used memory is 467 MB, reserved memory is 442 MB, peak memory is 467 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22836 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 338 (3.98), #lev = 8 (2.81)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 301 (4.05), #lev = 8 (3.01)
SYN-3001 : Logic optimization runtime opt =   0.06 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 843 instances into 301 LUTs, name keeping = 42%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 509 DFF/LATCH to SEQ ...
SYN-4009 : Pack 4 carry chain into lslice
SYN-4007 : Packing 67 adder to BLE ...
SYN-4008 : Packed 67 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  6.461771s wall, 6.265625s user + 0.203125s system = 6.468750s CPU (100.1%)

RUN-1004 : used memory is 356 MB, reserved memory is 334 MB, peak memory is 585 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  9.620077s wall, 9.343750s user + 0.265625s system = 9.609375s CPU (99.9%)

RUN-1004 : used memory is 357 MB, reserved memory is 334 MB, peak memory is 585 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.5_SP3/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/rmc_com2/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/rmc_com2/GPRMC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/rmc_com2/GPRMC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/rmc_com2/GPRMC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/rmc_com2/GPRMC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/rmc_com2/GPRMC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/rmc_com2/GPRMC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/rmc_com2/GPRMC_data[0]
SYN-5055 WARNING: The kept net COM2/rx_err will be merged to another kept net COM2/rmc_com2/GPRMC_err
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sub_state_cnt0[3] will be merged to another kept net COM2/rmc_com2/GPRMC_sub_state_cnt0[2]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (352 clock/control pins, 0 other pins).
SYN-4027 : Net DATA/DIV_Dtemp/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net DATA/DIV_Dtemp/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19704 instances
RUN-0007 : 5744 luts, 12531 seqs, 864 mslices, 451 lslices, 61 pads, 48 brams, 0 dsps
RUN-1001 : There are total 22070 nets
RUN-1001 : 16227 nets have 2 pins
RUN-1001 : 4670 nets have [3 - 5] pins
RUN-1001 : 784 nets have [6 - 10] pins
RUN-1001 : 255 nets have [11 - 20] pins
RUN-1001 : 109 nets have [21 - 99] pins
RUN-1001 : 25 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4743     
RUN-1001 :   No   |  No   |  Yes  |     641     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    6531     
RUN-1001 :   Yes  |  No   |  Yes  |     516     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  106  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 114
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19702 instances, 5744 luts, 12531 seqs, 1315 slices, 262 macros(1315 instances: 864 mslices 451 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1131 pins
PHY-3001 : Huge net DATA/done_div with 1650 pins
PHY-0007 : Cell area utilization is 64%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 82677, tnet num: 22068, tinst num: 19702, tnode num: 116652, tedge num: 130002.
TMR-2508 : Levelizing timing graph completed, there are 29 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.221261s wall, 1.218750s user + 0.000000s system = 1.218750s CPU (99.8%)

RUN-1004 : used memory is 530 MB, reserved memory is 508 MB, peak memory is 585 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22068 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.229974s wall, 2.187500s user + 0.031250s system = 2.218750s CPU (99.5%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.54673e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19702.
PHY-3001 : Level 1 #clusters 1993.
PHY-3001 : End clustering;  0.174490s wall, 0.343750s user + 0.031250s system = 0.375000s CPU (214.9%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 64%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 880116, overlap = 692.562
PHY-3002 : Step(2): len = 783931, overlap = 765.281
PHY-3002 : Step(3): len = 526410, overlap = 916.375
PHY-3002 : Step(4): len = 455820, overlap = 988.531
PHY-3002 : Step(5): len = 363717, overlap = 1067.59
PHY-3002 : Step(6): len = 314419, overlap = 1116.72
PHY-3002 : Step(7): len = 272915, overlap = 1206.16
PHY-3002 : Step(8): len = 242039, overlap = 1270.56
PHY-3002 : Step(9): len = 214068, overlap = 1329.59
PHY-3002 : Step(10): len = 194722, overlap = 1374.59
PHY-3002 : Step(11): len = 177601, overlap = 1403.72
PHY-3002 : Step(12): len = 163754, overlap = 1416.09
PHY-3002 : Step(13): len = 151044, overlap = 1448.75
PHY-3002 : Step(14): len = 139934, overlap = 1464.19
PHY-3002 : Step(15): len = 126745, overlap = 1489.38
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.02693e-06
PHY-3002 : Step(16): len = 132782, overlap = 1470.16
PHY-3002 : Step(17): len = 181610, overlap = 1345.94
PHY-3002 : Step(18): len = 192840, overlap = 1284.09
PHY-3002 : Step(19): len = 194864, overlap = 1198.44
PHY-3002 : Step(20): len = 191028, overlap = 1162.69
PHY-3002 : Step(21): len = 185164, overlap = 1155.88
PHY-3002 : Step(22): len = 182307, overlap = 1154.53
PHY-3002 : Step(23): len = 177267, overlap = 1143.62
PHY-3002 : Step(24): len = 174465, overlap = 1150.88
PHY-3002 : Step(25): len = 170496, overlap = 1158.28
PHY-3002 : Step(26): len = 168046, overlap = 1168.69
PHY-3002 : Step(27): len = 166669, overlap = 1191.66
PHY-3002 : Step(28): len = 165414, overlap = 1185.72
PHY-3002 : Step(29): len = 164867, overlap = 1180.88
PHY-3002 : Step(30): len = 164153, overlap = 1172.38
PHY-3002 : Step(31): len = 164503, overlap = 1158.09
PHY-3002 : Step(32): len = 163252, overlap = 1160.69
PHY-3002 : Step(33): len = 162382, overlap = 1153.41
PHY-3002 : Step(34): len = 160996, overlap = 1141.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.05387e-06
PHY-3002 : Step(35): len = 168548, overlap = 1140.31
PHY-3002 : Step(36): len = 182797, overlap = 1094.84
PHY-3002 : Step(37): len = 186490, overlap = 1037.19
PHY-3002 : Step(38): len = 188062, overlap = 1033.97
PHY-3002 : Step(39): len = 189395, overlap = 1032.03
PHY-3002 : Step(40): len = 188786, overlap = 1017.78
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.10773e-06
PHY-3002 : Step(41): len = 198778, overlap = 989.875
PHY-3002 : Step(42): len = 216273, overlap = 915.562
PHY-3002 : Step(43): len = 222614, overlap = 831.75
PHY-3002 : Step(44): len = 224390, overlap = 787.375
PHY-3002 : Step(45): len = 223815, overlap = 762.906
PHY-3002 : Step(46): len = 221854, overlap = 771.781
PHY-3002 : Step(47): len = 220450, overlap = 772.656
PHY-3002 : Step(48): len = 219596, overlap = 779.312
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 8.21547e-06
PHY-3002 : Step(49): len = 231272, overlap = 740.875
PHY-3002 : Step(50): len = 248850, overlap = 654.594
PHY-3002 : Step(51): len = 254738, overlap = 604.312
PHY-3002 : Step(52): len = 257618, overlap = 564.969
PHY-3002 : Step(53): len = 257288, overlap = 553.5
PHY-3002 : Step(54): len = 255982, overlap = 557.156
PHY-3002 : Step(55): len = 254017, overlap = 557.406
PHY-3002 : Step(56): len = 252149, overlap = 559.281
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.64309e-05
PHY-3002 : Step(57): len = 264522, overlap = 541.125
PHY-3002 : Step(58): len = 278964, overlap = 483.312
PHY-3002 : Step(59): len = 283315, overlap = 487.656
PHY-3002 : Step(60): len = 284945, overlap = 493.594
PHY-3002 : Step(61): len = 283648, overlap = 483.844
PHY-3002 : Step(62): len = 281601, overlap = 485.844
PHY-3002 : Step(63): len = 279857, overlap = 474.656
PHY-3002 : Step(64): len = 278286, overlap = 480.406
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.28619e-05
PHY-3002 : Step(65): len = 288395, overlap = 464.562
PHY-3002 : Step(66): len = 301868, overlap = 424.031
PHY-3002 : Step(67): len = 306603, overlap = 385.094
PHY-3002 : Step(68): len = 307382, overlap = 376.219
PHY-3002 : Step(69): len = 306598, overlap = 407.125
PHY-3002 : Step(70): len = 305005, overlap = 387.5
PHY-3002 : Step(71): len = 303036, overlap = 389.688
PHY-3002 : Step(72): len = 302009, overlap = 400.281
PHY-3002 : Step(73): len = 300842, overlap = 397.188
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 6.57238e-05
PHY-3002 : Step(74): len = 308999, overlap = 380.719
PHY-3002 : Step(75): len = 316681, overlap = 364.188
PHY-3002 : Step(76): len = 318738, overlap = 344.656
PHY-3002 : Step(77): len = 319652, overlap = 333.656
PHY-3002 : Step(78): len = 318237, overlap = 325.906
PHY-3002 : Step(79): len = 317278, overlap = 325.219
PHY-3002 : Step(80): len = 315415, overlap = 330.531
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000126311
PHY-3002 : Step(81): len = 320540, overlap = 307.125
PHY-3002 : Step(82): len = 328062, overlap = 303.656
PHY-3002 : Step(83): len = 332429, overlap = 295.062
PHY-3002 : Step(84): len = 334849, overlap = 280.562
PHY-3002 : Step(85): len = 333877, overlap = 276.656
PHY-3002 : Step(86): len = 332248, overlap = 283.125
PHY-3002 : Step(87): len = 329757, overlap = 288.812
PHY-3002 : Step(88): len = 329902, overlap = 296.25
PHY-3002 : Step(89): len = 328472, overlap = 280.719
PHY-3002 : Step(90): len = 328709, overlap = 285
PHY-3002 : Step(91): len = 327802, overlap = 289.031
PHY-3002 : Step(92): len = 328365, overlap = 283.281
PHY-3002 : Step(93): len = 328415, overlap = 279.531
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000235351
PHY-3002 : Step(94): len = 330988, overlap = 283.844
PHY-3002 : Step(95): len = 335972, overlap = 280.562
PHY-3002 : Step(96): len = 338905, overlap = 250.031
PHY-3002 : Step(97): len = 340447, overlap = 246.406
PHY-3002 : Step(98): len = 340429, overlap = 253.656
PHY-3002 : Step(99): len = 340009, overlap = 260.625
PHY-3002 : Step(100): len = 339648, overlap = 260.969
PHY-3002 : Step(101): len = 340126, overlap = 266.375
PHY-3002 : Step(102): len = 341451, overlap = 269.656
PHY-3002 : Step(103): len = 341459, overlap = 287.281
PHY-3002 : Step(104): len = 339372, overlap = 289.594
PHY-3002 : Step(105): len = 338516, overlap = 285.812
PHY-3002 : Step(106): len = 338310, overlap = 274
PHY-3002 : Step(107): len = 338898, overlap = 267.406
PHY-3002 : Step(108): len = 338152, overlap = 266
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.00040988
PHY-3002 : Step(109): len = 339440, overlap = 267.969
PHY-3002 : Step(110): len = 343076, overlap = 262.156
PHY-3001 : :::11::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(111): len = 343739, overlap = 262.031
PHY-3002 : Step(112): len = 345790, overlap = 266.812
PHY-3001 : Before Legalized: Len = 368093
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.015632s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (100.0%)

PHY-3001 : After Legalized: Len = 373564, Over = 153.562
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/22070.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 456432, over cnt = 1251(3%), over = 5416, worst = 36
PHY-1001 : End global iterations;  0.870243s wall, 1.046875s user + 0.000000s system = 1.046875s CPU (120.3%)

PHY-1001 : Congestion index: top1 = 73.77, top5 = 51.95, top10 = 43.04, top15 = 37.84.
PHY-3001 : End congestion estimation;  1.120827s wall, 1.296875s user + 0.000000s system = 1.296875s CPU (115.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22068 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.040696s wall, 1.000000s user + 0.046875s system = 1.046875s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.53061e-05
PHY-3002 : Step(113): len = 384689, overlap = 180.938
PHY-3002 : Step(114): len = 399768, overlap = 166.969
PHY-3002 : Step(115): len = 403912, overlap = 141.906
PHY-3002 : Step(116): len = 405721, overlap = 142.156
PHY-3002 : Step(117): len = 411701, overlap = 143.781
PHY-3002 : Step(118): len = 416898, overlap = 135.469
PHY-3002 : Step(119): len = 420509, overlap = 132.875
PHY-3002 : Step(120): len = 425437, overlap = 129.844
PHY-3002 : Step(121): len = 428724, overlap = 125.906
PHY-3002 : Step(122): len = 429919, overlap = 125.812
PHY-3002 : Step(123): len = 431909, overlap = 125.125
PHY-3002 : Step(124): len = 432754, overlap = 123.031
PHY-3002 : Step(125): len = 434011, overlap = 125.562
PHY-3002 : Step(126): len = 435595, overlap = 127.594
PHY-3002 : Step(127): len = 435000, overlap = 127.844
PHY-3002 : Step(128): len = 435980, overlap = 126.688
PHY-3002 : Step(129): len = 434124, overlap = 122.719
PHY-3002 : Step(130): len = 433187, overlap = 124.344
PHY-3002 : Step(131): len = 432717, overlap = 123.438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000170612
PHY-3002 : Step(132): len = 433331, overlap = 117.938
PHY-3002 : Step(133): len = 434486, overlap = 116.375
PHY-3002 : Step(134): len = 435776, overlap = 109.656
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000300862
PHY-3002 : Step(135): len = 440924, overlap = 101.469
PHY-3002 : Step(136): len = 448558, overlap = 96.0625
PHY-3002 : Step(137): len = 450876, overlap = 96.6875
PHY-3002 : Step(138): len = 454881, overlap = 95.5312
PHY-3002 : Step(139): len = 457140, overlap = 96.3438
PHY-3002 : Step(140): len = 455627, overlap = 103.844
PHY-3002 : Step(141): len = 455142, overlap = 104.531
PHY-3002 : Step(142): len = 456074, overlap = 109.188
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 58/22070.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 532136, over cnt = 2269(6%), over = 10254, worst = 35
PHY-1001 : End global iterations;  1.177451s wall, 1.875000s user + 0.015625s system = 1.890625s CPU (160.6%)

PHY-1001 : Congestion index: top1 = 75.19, top5 = 58.20, top10 = 50.14, top15 = 45.26.
PHY-3001 : End congestion estimation;  1.500903s wall, 2.218750s user + 0.015625s system = 2.234375s CPU (148.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22068 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.070476s wall, 1.046875s user + 0.015625s system = 1.062500s CPU (99.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000106059
PHY-3002 : Step(143): len = 461891, overlap = 349.469
PHY-3002 : Step(144): len = 468254, overlap = 287.656
PHY-3002 : Step(145): len = 466448, overlap = 250.875
PHY-3002 : Step(146): len = 463569, overlap = 252.312
PHY-3002 : Step(147): len = 461400, overlap = 226.781
PHY-3002 : Step(148): len = 459243, overlap = 221.656
PHY-3002 : Step(149): len = 457610, overlap = 204.469
PHY-3002 : Step(150): len = 455924, overlap = 207.562
PHY-3002 : Step(151): len = 453240, overlap = 198.906
PHY-3002 : Step(152): len = 452028, overlap = 200.094
PHY-3002 : Step(153): len = 449140, overlap = 204.969
PHY-3002 : Step(154): len = 447934, overlap = 204.875
PHY-3002 : Step(155): len = 445782, overlap = 195.469
PHY-3002 : Step(156): len = 443765, overlap = 205.219
PHY-3002 : Step(157): len = 442069, overlap = 208.719
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000212117
PHY-3002 : Step(158): len = 441231, overlap = 204.625
PHY-3002 : Step(159): len = 443816, overlap = 194.531
PHY-3002 : Step(160): len = 445654, overlap = 193.969
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000424234
PHY-3002 : Step(161): len = 446506, overlap = 184.5
PHY-3002 : Step(162): len = 451190, overlap = 180.812
PHY-3002 : Step(163): len = 458359, overlap = 172.219
PHY-3002 : Step(164): len = 460599, overlap = 165.188
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000848469
PHY-3002 : Step(165): len = 460641, overlap = 165.625
PHY-3002 : Step(166): len = 462757, overlap = 145.594
PHY-3002 : Step(167): len = 466112, overlap = 139.188
PHY-3002 : Step(168): len = 468227, overlap = 136.906
PHY-3002 : Step(169): len = 469012, overlap = 134.781
PHY-3002 : Step(170): len = 470085, overlap = 131.469
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00151669
PHY-3002 : Step(171): len = 470513, overlap = 134.156
PHY-3002 : Step(172): len = 472123, overlap = 135.125
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 82677, tnet num: 22068, tinst num: 19702, tnode num: 116652, tedge num: 130002.
TMR-2508 : Levelizing timing graph completed, there are 29 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.538680s wall, 1.500000s user + 0.031250s system = 1.531250s CPU (99.5%)

RUN-1004 : used memory is 572 MB, reserved memory is 552 MB, peak memory is 713 MB
OPT-1001 : Total overflow 500.38 peak overflow 3.66
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 373/22070.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 562744, over cnt = 2682(7%), over = 9301, worst = 22
PHY-1001 : End global iterations;  1.354200s wall, 2.171875s user + 0.062500s system = 2.234375s CPU (165.0%)

PHY-1001 : Congestion index: top1 = 55.86, top5 = 46.96, top10 = 42.98, top15 = 40.41.
PHY-1001 : End incremental global routing;  1.615139s wall, 2.437500s user + 0.062500s system = 2.500000s CPU (154.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22068 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.128353s wall, 1.109375s user + 0.015625s system = 1.125000s CPU (99.7%)

OPT-1001 : 22 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 61 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19616 has valid locations, 294 needs to be replaced
PHY-3001 : design contains 19974 instances, 5861 luts, 12686 seqs, 1315 slices, 262 macros(1315 instances: 864 mslices 451 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 493538
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17251/22342.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 580712, over cnt = 2750(7%), over = 9455, worst = 22
PHY-1001 : End global iterations;  0.225914s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (152.2%)

PHY-1001 : Congestion index: top1 = 56.49, top5 = 47.50, top10 = 43.44, top15 = 40.88.
PHY-3001 : End congestion estimation;  0.545138s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (123.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 83584, tnet num: 22340, tinst num: 19974, tnode num: 117928, tedge num: 131272.
TMR-2508 : Levelizing timing graph completed, there are 29 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.529390s wall, 1.500000s user + 0.031250s system = 1.531250s CPU (100.1%)

RUN-1004 : used memory is 620 MB, reserved memory is 620 MB, peak memory is 716 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22340 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.694716s wall, 2.671875s user + 0.031250s system = 2.703125s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(173): len = 493071, overlap = 2.125
PHY-3002 : Step(174): len = 493182, overlap = 2.125
PHY-3002 : Step(175): len = 493945, overlap = 2.3125
PHY-3002 : Step(176): len = 494426, overlap = 2.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17296/22342.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 579064, over cnt = 2737(7%), over = 9536, worst = 22
PHY-1001 : End global iterations;  0.224434s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (118.4%)

PHY-1001 : Congestion index: top1 = 56.94, top5 = 47.67, top10 = 43.65, top15 = 41.12.
PHY-3001 : End congestion estimation;  0.490796s wall, 0.531250s user + 0.000000s system = 0.531250s CPU (108.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22340 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.115773s wall, 1.093750s user + 0.031250s system = 1.125000s CPU (100.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000695928
PHY-3002 : Step(177): len = 494415, overlap = 138.5
PHY-3002 : Step(178): len = 494652, overlap = 137.969
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00139186
PHY-3002 : Step(179): len = 495159, overlap = 137.719
PHY-3002 : Step(180): len = 495548, overlap = 137.344
PHY-3001 : Final: Len = 495548, Over = 137.344
PHY-3001 : End incremental placement;  5.788346s wall, 6.140625s user + 0.218750s system = 6.359375s CPU (109.9%)

OPT-1001 : Total overflow 506.62 peak overflow 3.66
OPT-1001 : End high-fanout net optimization;  9.139741s wall, 10.437500s user + 0.296875s system = 10.734375s CPU (117.4%)

OPT-1001 : Current memory(MB): used = 721, reserve = 706, peak = 738.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17292/22342.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 582984, over cnt = 2717(7%), over = 8861, worst = 22
PHY-1002 : len = 628712, over cnt = 1823(5%), over = 4407, worst = 22
PHY-1002 : len = 668584, over cnt = 765(2%), over = 1552, worst = 16
PHY-1002 : len = 677424, over cnt = 558(1%), over = 1069, worst = 14
PHY-1002 : len = 692752, over cnt = 78(0%), over = 117, worst = 7
PHY-1001 : End global iterations;  1.475310s wall, 2.093750s user + 0.015625s system = 2.109375s CPU (143.0%)

PHY-1001 : Congestion index: top1 = 49.55, top5 = 44.11, top10 = 41.30, top15 = 39.42.
OPT-1001 : End congestion update;  1.746712s wall, 2.375000s user + 0.015625s system = 2.390625s CPU (136.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22340 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.967821s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (100.1%)

OPT-0007 : Start: WNS 4580 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.722442s wall, 3.359375s user + 0.015625s system = 3.375000s CPU (124.0%)

OPT-1001 : Current memory(MB): used = 719, reserve = 704, peak = 738.
OPT-1001 : End physical optimization;  13.745030s wall, 15.781250s user + 0.359375s system = 16.140625s CPU (117.4%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5861 LUT to BLE ...
SYN-4008 : Packed 5861 LUT and 2869 SEQ to BLE.
SYN-4003 : Packing 9817 remaining SEQ's ...
SYN-4005 : Packed 3367 SEQ with LUT/SLICE
SYN-4006 : 88 single LUT's are left
SYN-4006 : 6450 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 12311/13860 primitive instances ...
PHY-3001 : End packing;  2.989464s wall, 3.000000s user + 0.000000s system = 3.000000s CPU (100.4%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8173 instances
RUN-1001 : 4030 mslices, 4029 lslices, 61 pads, 48 brams, 0 dsps
RUN-1001 : There are total 19519 nets
RUN-1001 : 13347 nets have 2 pins
RUN-1001 : 4740 nets have [3 - 5] pins
RUN-1001 : 849 nets have [6 - 10] pins
RUN-1001 : 421 nets have [11 - 20] pins
RUN-1001 : 153 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8171 instances, 8059 slices, 262 macros(1315 instances: 864 mslices 451 lslices)
PHY-3001 : Cell area utilization is 86%
PHY-3001 : After packing: Len = 510101, Over = 389.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7814/19519.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 651256, over cnt = 1766(5%), over = 2849, worst = 9
PHY-1002 : len = 658752, over cnt = 1188(3%), over = 1617, worst = 6
PHY-1002 : len = 668416, over cnt = 693(1%), over = 907, worst = 6
PHY-1002 : len = 681048, over cnt = 86(0%), over = 99, worst = 4
PHY-1002 : len = 683552, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.343347s wall, 2.218750s user + 0.078125s system = 2.296875s CPU (171.0%)

PHY-1001 : Congestion index: top1 = 50.93, top5 = 44.90, top10 = 41.72, top15 = 39.64.
PHY-3001 : End congestion estimation;  1.689249s wall, 2.562500s user + 0.078125s system = 2.640625s CPU (156.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68945, tnet num: 19517, tinst num: 8171, tnode num: 93922, tedge num: 113525.
TMR-2508 : Levelizing timing graph completed, there are 29 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.843153s wall, 1.812500s user + 0.015625s system = 1.828125s CPU (99.2%)

RUN-1004 : used memory is 606 MB, reserved memory is 605 MB, peak memory is 738 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19517 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.049956s wall, 3.015625s user + 0.015625s system = 3.031250s CPU (99.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.25341e-05
PHY-3002 : Step(181): len = 508449, overlap = 361.25
PHY-3002 : Step(182): len = 505096, overlap = 356.5
PHY-3002 : Step(183): len = 501794, overlap = 365.25
PHY-3002 : Step(184): len = 500834, overlap = 371.5
PHY-3002 : Step(185): len = 500244, overlap = 381.75
PHY-3002 : Step(186): len = 501430, overlap = 397.75
PHY-3002 : Step(187): len = 498479, overlap = 401.5
PHY-3002 : Step(188): len = 497332, overlap = 402.5
PHY-3002 : Step(189): len = 495502, overlap = 400.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.50682e-05
PHY-3002 : Step(190): len = 498858, overlap = 389.25
PHY-3002 : Step(191): len = 503145, overlap = 377
PHY-3002 : Step(192): len = 504211, overlap = 370.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000170136
PHY-3002 : Step(193): len = 512574, overlap = 356.25
PHY-3002 : Step(194): len = 520836, overlap = 337.25
PHY-3002 : Step(195): len = 519135, overlap = 334
PHY-3002 : Step(196): len = 518528, overlap = 336.25
PHY-3002 : Step(197): len = 518720, overlap = 339.25
PHY-3001 : Before Legalized: Len = 518720
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.754428s wall, 0.796875s user + 0.984375s system = 1.781250s CPU (236.1%)

PHY-3001 : After Legalized: Len = 652382, Over = 0
PHY-3001 : Trial Legalized: Len = 652382
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 671/19519.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 752112, over cnt = 2605(7%), over = 4405, worst = 7
PHY-1002 : len = 769304, over cnt = 1570(4%), over = 2272, worst = 6
PHY-1002 : len = 788672, over cnt = 673(1%), over = 897, worst = 5
PHY-1002 : len = 801272, over cnt = 164(0%), over = 209, worst = 5
PHY-1002 : len = 805000, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.027024s wall, 3.484375s user + 0.015625s system = 3.500000s CPU (172.7%)

PHY-1001 : Congestion index: top1 = 50.39, top5 = 46.20, top10 = 43.78, top15 = 42.15.
PHY-3001 : End congestion estimation;  2.401988s wall, 3.859375s user + 0.015625s system = 3.875000s CPU (161.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19517 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.000003s wall, 0.984375s user + 0.015625s system = 1.000000s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000195278
PHY-3002 : Step(198): len = 603151, overlap = 86
PHY-3002 : Step(199): len = 582910, overlap = 135.75
PHY-3002 : Step(200): len = 568939, overlap = 183
PHY-3002 : Step(201): len = 559679, overlap = 218.25
PHY-3002 : Step(202): len = 554223, overlap = 240.75
PHY-3002 : Step(203): len = 551486, overlap = 257
PHY-3002 : Step(204): len = 549422, overlap = 268.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000390556
PHY-3002 : Step(205): len = 553790, overlap = 263.5
PHY-3002 : Step(206): len = 558701, overlap = 260
PHY-3002 : Step(207): len = 558715, overlap = 261.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000703063
PHY-3002 : Step(208): len = 562191, overlap = 261.75
PHY-3002 : Step(209): len = 569746, overlap = 251
PHY-3002 : Step(210): len = 578017, overlap = 254.5
PHY-3002 : Step(211): len = 579676, overlap = 255.25
PHY-3002 : Step(212): len = 580669, overlap = 251.25
PHY-3001 : Before Legalized: Len = 580669
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.039419s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (79.3%)

PHY-3001 : After Legalized: Len = 622138, Over = 0
PHY-3001 : Legalized: Len = 622138, Over = 0
PHY-3001 : Spreading special nets. 62 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.089898s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (86.9%)

PHY-3001 : 87 instances has been re-located, deltaX = 35, deltaY = 47, maxDist = 2.
PHY-3001 : Final: Len = 623902, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68945, tnet num: 19517, tinst num: 8171, tnode num: 93922, tedge num: 113525.
TMR-2508 : Levelizing timing graph completed, there are 29 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.102592s wall, 2.093750s user + 0.000000s system = 2.093750s CPU (99.6%)

RUN-1004 : used memory is 602 MB, reserved memory is 604 MB, peak memory is 738 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3035/19519.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 729928, over cnt = 2462(6%), over = 3856, worst = 7
PHY-1002 : len = 741584, over cnt = 1499(4%), over = 2107, worst = 7
PHY-1002 : len = 758000, over cnt = 678(1%), over = 905, worst = 5
PHY-1002 : len = 765112, over cnt = 387(1%), over = 516, worst = 5
PHY-1002 : len = 774168, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.846060s wall, 2.984375s user + 0.031250s system = 3.015625s CPU (163.4%)

PHY-1001 : Congestion index: top1 = 48.28, top5 = 44.08, top10 = 41.97, top15 = 40.56.
PHY-1001 : End incremental global routing;  2.173843s wall, 3.312500s user + 0.031250s system = 3.343750s CPU (153.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19517 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.017458s wall, 0.984375s user + 0.031250s system = 1.015625s CPU (99.8%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 61 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8105 has valid locations, 17 needs to be replaced
PHY-3001 : design contains 8186 instances, 8074 slices, 262 macros(1315 instances: 864 mslices 451 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 627007
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17785/19532.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 777608, over cnt = 33(0%), over = 40, worst = 5
PHY-1002 : len = 777592, over cnt = 23(0%), over = 25, worst = 2
PHY-1002 : len = 777592, over cnt = 10(0%), over = 10, worst = 1
PHY-1002 : len = 777840, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.579784s wall, 0.593750s user + 0.015625s system = 0.609375s CPU (105.1%)

PHY-1001 : Congestion index: top1 = 48.30, top5 = 44.09, top10 = 42.02, top15 = 40.65.
PHY-3001 : End congestion estimation;  0.903849s wall, 0.921875s user + 0.015625s system = 0.937500s CPU (103.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 69045, tnet num: 19530, tinst num: 8186, tnode num: 94043, tedge num: 113644.
TMR-2508 : Levelizing timing graph completed, there are 29 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.094690s wall, 2.093750s user + 0.000000s system = 2.093750s CPU (100.0%)

RUN-1004 : used memory is 635 MB, reserved memory is 625 MB, peak memory is 738 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19530 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.142763s wall, 3.093750s user + 0.046875s system = 3.140625s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(213): len = 627007, overlap = 0
PHY-3002 : Step(214): len = 627007, overlap = 0
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17800/19532.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 777840, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.127533s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (98.0%)

PHY-1001 : Congestion index: top1 = 48.30, top5 = 44.09, top10 = 42.02, top15 = 40.65.
PHY-3001 : End congestion estimation;  0.447841s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (101.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19530 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.054555s wall, 1.046875s user + 0.015625s system = 1.062500s CPU (100.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00129064
PHY-3002 : Step(215): len = 626727, overlap = 2
PHY-3002 : Step(216): len = 626705, overlap = 2.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007071s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 626839, Over = 0
PHY-3001 : End spreading;  0.072421s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (107.9%)

PHY-3001 : Final: Len = 626839, Over = 0
PHY-3001 : End incremental placement;  6.203832s wall, 6.500000s user + 0.109375s system = 6.609375s CPU (106.5%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  9.923776s wall, 11.500000s user + 0.171875s system = 11.671875s CPU (117.6%)

OPT-1001 : Current memory(MB): used = 722, reserve = 714, peak = 738.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17781/19532.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 777744, over cnt = 36(0%), over = 42, worst = 3
PHY-1002 : len = 777600, over cnt = 28(0%), over = 28, worst = 1
PHY-1002 : len = 777864, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 778080, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 778128, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.736956s wall, 0.796875s user + 0.015625s system = 0.812500s CPU (110.3%)

PHY-1001 : Congestion index: top1 = 48.43, top5 = 44.15, top10 = 42.07, top15 = 40.65.
OPT-1001 : End congestion update;  1.058617s wall, 1.125000s user + 0.015625s system = 1.140625s CPU (107.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19530 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.849001s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (99.4%)

OPT-0007 : Start: WNS 4590 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.913246s wall, 1.968750s user + 0.015625s system = 1.984375s CPU (103.7%)

OPT-1001 : Current memory(MB): used = 723, reserve = 714, peak = 738.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19530 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.845318s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (101.7%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17800/19532.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 778128, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.123095s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (88.9%)

PHY-1001 : Congestion index: top1 = 48.43, top5 = 44.15, top10 = 42.07, top15 = 40.65.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19530 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.842866s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (100.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4590 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 47.931034
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4590ps with logic level 8 
OPT-1001 : End physical optimization;  16.358611s wall, 18.171875s user + 0.187500s system = 18.359375s CPU (112.2%)

RUN-1003 : finish command "place" in  66.962569s wall, 109.562500s user + 7.718750s system = 117.281250s CPU (175.1%)

RUN-1004 : used memory is 575 MB, reserved memory is 555 MB, peak memory is 738 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.731727s wall, 2.968750s user + 0.031250s system = 3.000000s CPU (173.2%)

RUN-1004 : used memory is 575 MB, reserved memory is 556 MB, peak memory is 738 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.5_SP3/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8188 instances
RUN-1001 : 4030 mslices, 4044 lslices, 61 pads, 48 brams, 0 dsps
RUN-1001 : There are total 19532 nets
RUN-1001 : 13345 nets have 2 pins
RUN-1001 : 4740 nets have [3 - 5] pins
RUN-1001 : 859 nets have [6 - 10] pins
RUN-1001 : 425 nets have [11 - 20] pins
RUN-1001 : 154 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 69045, tnet num: 19530, tinst num: 8186, tnode num: 94043, tedge num: 113644.
TMR-2508 : Levelizing timing graph completed, there are 29 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.808902s wall, 1.812500s user + 0.000000s system = 1.812500s CPU (100.2%)

RUN-1004 : used memory is 600 MB, reserved memory is 606 MB, peak memory is 738 MB
PHY-1001 : 4030 mslices, 4044 lslices, 61 pads, 48 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19530 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 713648, over cnt = 2520(7%), over = 4186, worst = 7
PHY-1002 : len = 731424, over cnt = 1476(4%), over = 2086, worst = 6
PHY-1002 : len = 747176, over cnt = 686(1%), over = 924, worst = 5
PHY-1002 : len = 762096, over cnt = 42(0%), over = 53, worst = 3
PHY-1002 : len = 763136, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.946747s wall, 3.359375s user + 0.031250s system = 3.390625s CPU (174.2%)

PHY-1001 : Congestion index: top1 = 47.63, top5 = 43.90, top10 = 41.65, top15 = 40.16.
PHY-1001 : End global routing;  2.340070s wall, 3.765625s user + 0.031250s system = 3.796875s CPU (162.3%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 707, reserve = 702, peak = 738.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net DATA/DIV_Dtemp/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 973, reserve = 966, peak = 973.
PHY-1001 : End build detailed router design. 4.609559s wall, 4.609375s user + 0.000000s system = 4.609375s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 191864, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.928882s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (100.9%)

PHY-1001 : Current memory(MB): used = 1011, reserve = 1005, peak = 1011.
PHY-1001 : End phase 1; 0.937623s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (100.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 94% nets.
PHY-1022 : len = 1.83391e+06, over cnt = 1583(0%), over = 1588, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1027, reserve = 1020, peak = 1027.
PHY-1001 : End initial routed; 23.361739s wall, 58.906250s user + 0.484375s system = 59.390625s CPU (254.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18416(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.138   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.275   |  -19.055  |  10   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.995026s wall, 3.953125s user + 0.031250s system = 3.984375s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 1031, reserve = 1025, peak = 1031.
PHY-1001 : End phase 2; 27.356937s wall, 62.859375s user + 0.515625s system = 63.375000s CPU (231.7%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.83391e+06, over cnt = 1583(0%), over = 1588, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.271299s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (97.9%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.81839e+06, over cnt = 572(0%), over = 573, worst = 2, crit = 0
PHY-1001 : End DR Iter 1; 0.888672s wall, 1.671875s user + 0.000000s system = 1.671875s CPU (188.1%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.81818e+06, over cnt = 105(0%), over = 105, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.545726s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (137.4%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.81958e+06, over cnt = 15(0%), over = 15, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.333995s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (131.0%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.81981e+06, over cnt = 6(0%), over = 6, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.232926s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (114.0%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.8199e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.291498s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (101.8%)

PHY-1001 : ===== DR Iter 6 =====
PHY-1022 : len = 1.8199e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 6; 0.359291s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (100.0%)

PHY-1001 : ===== DR Iter 7 =====
PHY-1022 : len = 1.8199e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 7; 0.581960s wall, 0.578125s user + 0.000000s system = 0.578125s CPU (99.3%)

PHY-1001 : ===== DR Iter 8 =====
PHY-1022 : len = 1.81993e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 8; 0.175666s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (106.7%)

PHY-1001 : ==== DR Iter 9 ====
PHY-1022 : len = 1.81993e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 9; 0.181880s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (103.1%)

PHY-1001 : ==== DR Iter 10 ====
PHY-1022 : len = 1.81994e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 10; 0.219023s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (99.9%)

PHY-1001 : ==== DR Iter 11 ====
PHY-1022 : len = 1.81994e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 11; 0.250539s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (99.8%)

PHY-1001 : ==== DR Iter 12 ====
PHY-1022 : len = 1.81994e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 12; 0.380928s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (98.4%)

PHY-1001 : ===== DR Iter 13 =====
PHY-1022 : len = 1.81994e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 13; 0.183154s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (119.4%)

PHY-1001 : ==== DR Iter 14 ====
PHY-1022 : len = 1.81994e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 14; 0.172224s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (99.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18416(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.138   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.275   |  -19.055  |  10   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 4.263048s wall, 4.218750s user + 0.046875s system = 4.265625s CPU (100.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 472 feed throughs used by 381 nets
PHY-1001 : End commit to database; 2.830901s wall, 2.843750s user + 0.000000s system = 2.843750s CPU (100.5%)

PHY-1001 : Current memory(MB): used = 1124, reserve = 1122, peak = 1124.
PHY-1001 : End phase 3; 12.764845s wall, 13.828125s user + 0.078125s system = 13.906250s CPU (108.9%)

PHY-1003 : Routed, final wirelength = 1.81994e+06
PHY-1001 : Current memory(MB): used = 1128, reserve = 1126, peak = 1128.
PHY-1001 : End export database. 0.066391s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (94.1%)

PHY-1001 : End detail routing;  46.215196s wall, 82.765625s user + 0.609375s system = 83.375000s CPU (180.4%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 69045, tnet num: 19530, tinst num: 8186, tnode num: 94043, tedge num: 113644.
TMR-2508 : Levelizing timing graph completed, there are 29 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.982377s wall, 1.984375s user + 0.000000s system = 1.984375s CPU (100.1%)

RUN-1004 : used memory is 1007 MB, reserved memory is 1033 MB, peak memory is 1128 MB
RUN-1001 : fix hold violation on net config_inst_syn_2 endpin DATA/DIV_accZ/al_9533fc37_syn_3.sr slack -1394ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net config_inst_syn_2 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_30.sr slack -1717ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net config_inst_syn_2 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_32.sr slack -2136ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net config_inst_syn_2 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_34.sr slack -1669ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net config_inst_syn_2 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_36.sr slack -1855ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net config_inst_syn_2 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_38.sr slack -2208ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net config_inst_syn_2 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_40.sr slack -2274ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net config_inst_syn_2 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_42.sr slack -2120ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net config_inst_syn_2 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_44.sr slack -1407ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net config_inst_syn_2 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_46.sr slack -2275ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 69105, tnet num: 19560, tinst num: 8216, tnode num: 94103, tedge num: 113704.
TMR-2508 : Levelizing timing graph completed, there are 29 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.901844s wall, 1.890625s user + 0.000000s system = 1.890625s CPU (99.4%)

RUN-1004 : used memory is 1050 MB, reserved memory is 1053 MB, peak memory is 1140 MB
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net config_inst_syn_2_holdbuf38 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_46_sr slack -299ps
RUN-1001 : End hold fix;  34.249246s wall, 34.765625s user + 0.234375s system = 35.000000s CPU (102.2%)

RUN-1003 : finish command "route" in  85.654218s wall, 124.125000s user + 0.890625s system = 125.015625s CPU (146.0%)

RUN-1004 : used memory is 1071 MB, reserved memory is 1079 MB, peak memory is 1140 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        67
  #input                   26
  #output                  39
  #inout                    2

Utilization Statistics
#lut                     8686   out of  19600   44.32%
#reg                    12785   out of  19600   65.23%
#le                     15104
  #lut only              2319   out of  15104   15.35%
  #reg only              6418   out of  15104   42.49%
  #lut&reg               6367   out of  15104   42.15%
#dsp                        0   out of     29    0.00%
#bram                      48   out of     64   75.00%
  #bram9k                  48
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       61   out of    188   32.45%
  #ireg                     9
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet              Type               DriverType         Driver                    Fanout
#1        DATA/DIV_Dtemp/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6956
#2        config_inst_syn_9     GCLK               config             config_inst.jtck          221
#3        clk_in_dup_1          GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         L4        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          NONE       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D14        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    GNSS_RMC       OUTPUT         L3        LVCMOS33           8            NONE           NONE       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         T6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT        D16        LVCMOS33           8            NONE           NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        R15        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15104  |7371    |1315    |12827   |48      |0       |
|  AnyFog_dataX                      |AnyFog          |201    |85      |22      |165     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |84     |51      |22      |48      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |202    |102     |22      |167     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |83     |60      |22      |48      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |204    |89      |22      |168     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |83     |52      |22      |47      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |3537   |882     |34      |3432    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |741    |84      |5       |724     |0       |0       |
|    STADOP_com2                     |STADOP          |558    |87      |0       |551     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |77     |53      |14      |45      |0       |0       |
|    head_com2                       |uniheading      |273    |81      |5       |257     |0       |0       |
|    rmc_com2                        |Gprmc           |145    |47      |0       |140     |0       |0       |
|    uart_com2                       |Agrica          |1449   |236     |10      |1421    |0       |0       |
|  DATA                              |Data_Processing |8646   |4457    |1057    |6976    |0       |0       |
|    DIV_Dtemp                       |Divider         |803    |301     |84      |681     |0       |0       |
|    DIV_Utemp                       |Divider         |606    |292     |84      |484     |0       |0       |
|    DIV_accX                        |Divider         |618    |369     |84      |496     |0       |0       |
|    DIV_accY                        |Divider         |750    |351     |132     |543     |0       |0       |
|    DIV_accZ                        |Divider         |657    |366     |132     |452     |0       |0       |
|    DIV_rateX                       |Divider         |633    |343     |132     |426     |0       |0       |
|    DIV_rateY                       |Divider         |560    |362     |132     |356     |0       |0       |
|    DIV_rateZ                       |Divider         |580    |366     |132     |372     |0       |0       |
|    genclk                          |genclk          |70     |50      |11      |52      |0       |0       |
|  FMC                               |FMC_Ctrl        |363    |336     |20      |331     |0       |0       |
|  IIC                               |I2C_master      |262    |190     |11      |243     |0       |0       |
|  IMU_CTRL                          |SCHA634         |905    |677     |61      |729     |0       |0       |
|    CtrlData                        |CtrlData        |486    |428     |47      |332     |0       |0       |
|      usms                          |Time_1ms        |30     |25      |5       |22      |0       |0       |
|    SPIM                            |SPI_SCHA634     |419    |249     |14      |397     |0       |0       |
|  POWER                             |POWER_EN        |46     |30      |11      |30      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |658    |462     |55      |524     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |658    |462     |55      |524     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |364    |233     |0       |346     |0       |0       |
|        reg_inst                    |register        |354    |223     |0       |336     |0       |0       |
|        tap_inst                    |tap             |10     |10      |0       |10      |0       |0       |
|      trigger_inst                  |trigger         |294    |229     |55      |178     |0       |0       |
|        bus_inst                    |bus_top         |56     |56      |0       |56      |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |10     |10      |0       |10      |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |3      |3       |0       |3       |0       |0       |
|          BUS_DETECTOR[6]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[7]$bus_nodes |bus_det         |5      |5       |0       |5       |0       |0       |
|          BUS_DETECTOR[8]$bus_nodes |bus_det         |16     |16      |0       |16      |0       |0       |
|          BUS_DETECTOR[9]$bus_nodes |bus_det         |19     |19      |0       |19      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |146    |104     |32      |87      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13313  
    #2          2       3886   
    #3          3        554   
    #4          4        300   
    #5        5-10       915   
    #6        11-50      496   
    #7       51-100      27    
    #8       101-500      5    
  Average     2.17             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.177876s wall, 3.750000s user + 0.015625s system = 3.765625s CPU (172.9%)

RUN-1004 : used memory is 1072 MB, reserved memory is 1079 MB, peak memory is 1140 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 69105, tnet num: 19560, tinst num: 8216, tnode num: 94103, tedge num: 113704.
TMR-2508 : Levelizing timing graph completed, there are 29 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.899227s wall, 1.875000s user + 0.015625s system = 1.890625s CPU (99.5%)

RUN-1004 : used memory is 1075 MB, reserved memory is 1082 MB, peak memory is 1140 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19560 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 3. Number of clock nets = 3 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.658634s wall, 1.609375s user + 0.031250s system = 1.640625s CPU (98.9%)

RUN-1004 : used memory is 1090 MB, reserved memory is 1092 MB, peak memory is 1140 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8216
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19562, pip num: 154231
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 473
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3245 valid insts, and 425104 bits set as '1'.
BIT-1004 : the usercode register value: 00000000001001011101110111001010
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.975717s wall, 131.281250s user + 0.109375s system = 131.390625s CPU (1012.6%)

RUN-1004 : used memory is 1234 MB, reserved memory is 1226 MB, peak memory is 1349 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250428_180523.log"

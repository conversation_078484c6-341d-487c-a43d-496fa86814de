============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Wed Aug 20 15:09:43 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.677509s wall, 1.656250s user + 4.015625s system = 5.671875s CPU (99.9%)

RUN-1004 : used memory is 79 MB, reserved memory is 40 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.862335s wall, 1.765625s user + 0.093750s system = 1.859375s CPU (99.8%)

RUN-1004 : used memory is 302 MB, reserved memory is 270 MB, peak memory is 305 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 6 view nodes, 43 trigger nets, 43 data nets.
KIT-1004 : Chipwatcher code = 1011100100001111
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb01001010}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=132) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=132) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb01001010}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb01001010}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb01001010})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=132)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=132)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb01001010})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb01001010})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22854/26 useful/useless nets, 19576/15 useful/useless insts
SYN-1016 : Merged 34 instances.
SYN-1032 : 22505/22 useful/useless nets, 20010/18 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 412 better
SYN-1014 : Optimize round 2
SYN-1032 : 22174/45 useful/useless nets, 19679/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.559476s wall, 2.468750s user + 0.078125s system = 2.546875s CPU (99.5%)

RUN-1004 : used memory is 328 MB, reserved memory is 295 MB, peak memory is 330 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22222/300 useful/useless nets, 19765/48 useful/useless insts
SYN-1016 : Merged 45 instances.
SYN-2571 : Optimize after map_dsp, round 1, 393 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 40 instances.
SYN-2501 : Optimize round 1, 82 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 22676/5 useful/useless nets, 20219/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82775, tnet num: 22676, tinst num: 20218, tnode num: 115907, tedge num: 129397.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.232164s wall, 1.234375s user + 0.000000s system = 1.234375s CPU (100.2%)

RUN-1004 : used memory is 469 MB, reserved memory is 437 MB, peak memory is 469 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22676 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 242 (3.42), #lev = 7 (1.80)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 242 (3.42), #lev = 7 (1.80)
SYN-3001 : Logic optimization runtime opt =   0.04 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 559 instances into 242 LUTs, name keeping = 72%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 421 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 128 adder to BLE ...
SYN-4008 : Packed 128 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.781825s wall, 4.734375s user + 0.062500s system = 4.796875s CPU (100.3%)

RUN-1004 : used memory is 352 MB, reserved memory is 318 MB, peak memory is 578 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.701650s wall, 7.531250s user + 0.171875s system = 7.703125s CPU (100.0%)

RUN-1004 : used memory is 353 MB, reserved memory is 318 MB, peak memory is 578 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (282 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19442 instances
RUN-0007 : 5617 luts, 12218 seqs, 983 mslices, 519 lslices, 59 pads, 41 brams, 0 dsps
RUN-1001 : There are total 21907 nets
RUN-1001 : 16426 nets have 2 pins
RUN-1001 : 4289 nets have [3 - 5] pins
RUN-1001 : 815 nets have [6 - 10] pins
RUN-1001 : 250 nets have [11 - 20] pins
RUN-1001 : 105 nets have [21 - 99] pins
RUN-1001 : 22 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4788     
RUN-1001 :   No   |  No   |  Yes  |     707     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     454     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19440 instances, 5617 luts, 12218 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81105, tnet num: 21905, tinst num: 19440, tnode num: 114042, tedge num: 127673.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.237177s wall, 1.203125s user + 0.031250s system = 1.234375s CPU (99.8%)

RUN-1004 : used memory is 528 MB, reserved memory is 500 MB, peak memory is 578 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.181969s wall, 2.140625s user + 0.046875s system = 2.187500s CPU (100.3%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.66049e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19440.
PHY-3001 : Level 1 #clusters 2178.
PHY-3001 : End clustering;  0.162227s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (144.5%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 879089, overlap = 617.844
PHY-3002 : Step(2): len = 817978, overlap = 683.75
PHY-3002 : Step(3): len = 515601, overlap = 839.969
PHY-3002 : Step(4): len = 465644, overlap = 932.031
PHY-3002 : Step(5): len = 370225, overlap = 1013.38
PHY-3002 : Step(6): len = 335515, overlap = 1084.84
PHY-3002 : Step(7): len = 286053, overlap = 1167.16
PHY-3002 : Step(8): len = 254015, overlap = 1236.44
PHY-3002 : Step(9): len = 225679, overlap = 1284.03
PHY-3002 : Step(10): len = 206239, overlap = 1318.47
PHY-3002 : Step(11): len = 188569, overlap = 1347.72
PHY-3002 : Step(12): len = 171372, overlap = 1365.81
PHY-3002 : Step(13): len = 159312, overlap = 1415.41
PHY-3002 : Step(14): len = 149113, overlap = 1429.06
PHY-3002 : Step(15): len = 140237, overlap = 1448.47
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.7775e-07
PHY-3002 : Step(16): len = 143371, overlap = 1445.09
PHY-3002 : Step(17): len = 180518, overlap = 1338.72
PHY-3002 : Step(18): len = 188175, overlap = 1283.72
PHY-3002 : Step(19): len = 190747, overlap = 1201.19
PHY-3002 : Step(20): len = 188611, overlap = 1184.69
PHY-3002 : Step(21): len = 184357, overlap = 1178.31
PHY-3002 : Step(22): len = 181417, overlap = 1169.94
PHY-3002 : Step(23): len = 178448, overlap = 1152.03
PHY-3002 : Step(24): len = 175784, overlap = 1147.47
PHY-3002 : Step(25): len = 173302, overlap = 1150
PHY-3002 : Step(26): len = 170138, overlap = 1141.03
PHY-3002 : Step(27): len = 169285, overlap = 1118.06
PHY-3002 : Step(28): len = 168551, overlap = 1118.47
PHY-3002 : Step(29): len = 168520, overlap = 1143.09
PHY-3002 : Step(30): len = 168519, overlap = 1149.53
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 1.9555e-06
PHY-3002 : Step(31): len = 173646, overlap = 1139.59
PHY-3002 : Step(32): len = 186404, overlap = 1077.31
PHY-3002 : Step(33): len = 191579, overlap = 1025
PHY-3002 : Step(34): len = 195710, overlap = 986.656
PHY-3002 : Step(35): len = 195142, overlap = 957.031
PHY-3002 : Step(36): len = 195196, overlap = 935.156
PHY-3002 : Step(37): len = 193041, overlap = 926.594
PHY-3002 : Step(38): len = 193938, overlap = 937.469
PHY-3002 : Step(39): len = 192214, overlap = 931.438
PHY-3002 : Step(40): len = 191540, overlap = 939.312
PHY-3002 : Step(41): len = 189938, overlap = 946.469
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 3.911e-06
PHY-3002 : Step(42): len = 198489, overlap = 933.375
PHY-3002 : Step(43): len = 216913, overlap = 861.125
PHY-3002 : Step(44): len = 223087, overlap = 828.125
PHY-3002 : Step(45): len = 224374, overlap = 814.688
PHY-3002 : Step(46): len = 223815, overlap = 782.062
PHY-3002 : Step(47): len = 222465, overlap = 751.875
PHY-3002 : Step(48): len = 220642, overlap = 753.75
PHY-3002 : Step(49): len = 218635, overlap = 758.062
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 7.822e-06
PHY-3002 : Step(50): len = 228648, overlap = 727.375
PHY-3002 : Step(51): len = 245851, overlap = 664.844
PHY-3002 : Step(52): len = 251571, overlap = 628.844
PHY-3002 : Step(53): len = 252882, overlap = 626.562
PHY-3002 : Step(54): len = 251190, overlap = 631.75
PHY-3002 : Step(55): len = 249895, overlap = 630.562
PHY-3002 : Step(56): len = 247430, overlap = 619.438
PHY-3002 : Step(57): len = 246736, overlap = 585.469
PHY-3002 : Step(58): len = 244853, overlap = 574.688
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.5644e-05
PHY-3002 : Step(59): len = 256823, overlap = 554.031
PHY-3002 : Step(60): len = 269754, overlap = 470.031
PHY-3002 : Step(61): len = 273253, overlap = 476.594
PHY-3002 : Step(62): len = 275414, overlap = 450.844
PHY-3002 : Step(63): len = 274954, overlap = 455.719
PHY-3002 : Step(64): len = 272724, overlap = 440.094
PHY-3002 : Step(65): len = 271884, overlap = 423.594
PHY-3002 : Step(66): len = 271260, overlap = 423
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.1288e-05
PHY-3002 : Step(67): len = 280521, overlap = 416.219
PHY-3002 : Step(68): len = 291916, overlap = 396.906
PHY-3002 : Step(69): len = 293405, overlap = 382.531
PHY-3002 : Step(70): len = 293988, overlap = 364.25
PHY-3002 : Step(71): len = 292833, overlap = 378.156
PHY-3002 : Step(72): len = 291116, overlap = 357.781
PHY-3002 : Step(73): len = 289504, overlap = 357.781
PHY-3002 : Step(74): len = 288848, overlap = 364.406
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 6.2576e-05
PHY-3002 : Step(75): len = 295342, overlap = 334.656
PHY-3002 : Step(76): len = 303285, overlap = 324.969
PHY-3002 : Step(77): len = 303911, overlap = 321.281
PHY-3002 : Step(78): len = 303741, overlap = 325.031
PHY-3002 : Step(79): len = 302583, overlap = 308.562
PHY-3002 : Step(80): len = 301190, overlap = 305.469
PHY-3002 : Step(81): len = 301117, overlap = 298.062
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000123711
PHY-3002 : Step(82): len = 306000, overlap = 285.781
PHY-3002 : Step(83): len = 312482, overlap = 267.031
PHY-3002 : Step(84): len = 314431, overlap = 251.156
PHY-3002 : Step(85): len = 315200, overlap = 239.062
PHY-3002 : Step(86): len = 314516, overlap = 226.406
PHY-3002 : Step(87): len = 313555, overlap = 211.781
PHY-3002 : Step(88): len = 312045, overlap = 214.469
PHY-3002 : Step(89): len = 313320, overlap = 197.156
PHY-3002 : Step(90): len = 313209, overlap = 206.031
PHY-3002 : Step(91): len = 314149, overlap = 208.5
PHY-3002 : Step(92): len = 312554, overlap = 188.094
PHY-3002 : Step(93): len = 312649, overlap = 191.75
PHY-3002 : Step(94): len = 311733, overlap = 196.219
PHY-3002 : Step(95): len = 312156, overlap = 200.719
PHY-3002 : Step(96): len = 311260, overlap = 208.094
PHY-3002 : Step(97): len = 311806, overlap = 213.531
PHY-3002 : Step(98): len = 311459, overlap = 211.938
PHY-3002 : Step(99): len = 311772, overlap = 221.719
PHY-3002 : Step(100): len = 310600, overlap = 235.344
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000220885
PHY-3002 : Step(101): len = 312115, overlap = 226.219
PHY-3002 : Step(102): len = 315308, overlap = 226.938
PHY-3002 : Step(103): len = 317919, overlap = 232.656
PHY-3002 : Step(104): len = 319982, overlap = 235.25
PHY-3002 : Step(105): len = 319813, overlap = 233.312
PHY-3002 : Step(106): len = 319724, overlap = 229.719
PHY-3002 : Step(107): len = 318428, overlap = 236.438
PHY-3002 : Step(108): len = 318328, overlap = 243.062
PHY-3002 : Step(109): len = 318156, overlap = 252.594
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(110): len = 319285, overlap = 248.594
PHY-3002 : Step(111): len = 323089, overlap = 242.219
PHY-3002 : Step(112): len = 324511, overlap = 244.75
PHY-3002 : Step(113): len = 325487, overlap = 245.031
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.015268s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (102.3%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21907.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 434096, over cnt = 1236(3%), over = 5464, worst = 37
PHY-1001 : End global iterations;  0.898254s wall, 1.125000s user + 0.031250s system = 1.156250s CPU (128.7%)

PHY-1001 : Congestion index: top1 = 69.74, top5 = 51.76, top10 = 42.72, top15 = 37.29.
PHY-3001 : End congestion estimation;  1.159518s wall, 1.406250s user + 0.031250s system = 1.437500s CPU (124.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.989063s wall, 0.984375s user + 0.000000s system = 0.984375s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000110568
PHY-3002 : Step(114): len = 368741, overlap = 185.375
PHY-3002 : Step(115): len = 380751, overlap = 173.438
PHY-3002 : Step(116): len = 385202, overlap = 155.406
PHY-3002 : Step(117): len = 386178, overlap = 152.844
PHY-3002 : Step(118): len = 389660, overlap = 145.062
PHY-3002 : Step(119): len = 394481, overlap = 135.625
PHY-3002 : Step(120): len = 397685, overlap = 123.625
PHY-3002 : Step(121): len = 400119, overlap = 118.031
PHY-3002 : Step(122): len = 401485, overlap = 108.188
PHY-3002 : Step(123): len = 404097, overlap = 98.375
PHY-3002 : Step(124): len = 403893, overlap = 95.1562
PHY-3002 : Step(125): len = 404013, overlap = 95.4062
PHY-3002 : Step(126): len = 405175, overlap = 93.25
PHY-3002 : Step(127): len = 404729, overlap = 97.0312
PHY-3002 : Step(128): len = 405243, overlap = 98.5
PHY-3002 : Step(129): len = 404976, overlap = 101.844
PHY-3002 : Step(130): len = 403960, overlap = 103.844
PHY-3002 : Step(131): len = 404735, overlap = 102.094
PHY-3002 : Step(132): len = 405406, overlap = 102.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000221137
PHY-3002 : Step(133): len = 404661, overlap = 101.25
PHY-3002 : Step(134): len = 406658, overlap = 99
PHY-3002 : Step(135): len = 409644, overlap = 99
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000442273
PHY-3002 : Step(136): len = 416794, overlap = 89
PHY-3002 : Step(137): len = 425836, overlap = 83.4062
PHY-3002 : Step(138): len = 430285, overlap = 77.5312
PHY-3002 : Step(139): len = 430582, overlap = 76.75
PHY-3002 : Step(140): len = 432905, overlap = 75.5
PHY-3002 : Step(141): len = 434640, overlap = 76.6562
PHY-3002 : Step(142): len = 435217, overlap = 76.6562
PHY-3002 : Step(143): len = 435401, overlap = 81.6562
PHY-3002 : Step(144): len = 435550, overlap = 96.5
PHY-3002 : Step(145): len = 433666, overlap = 105.344
PHY-3002 : Step(146): len = 430971, overlap = 110.875
PHY-3002 : Step(147): len = 429683, overlap = 114.438
PHY-3002 : Step(148): len = 429695, overlap = 122.406
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(149): len = 428846, overlap = 118.812
PHY-3002 : Step(150): len = 429760, overlap = 122.219
PHY-3002 : Step(151): len = 433836, overlap = 127.312
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 52/21907.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 501320, over cnt = 2064(5%), over = 9537, worst = 35
PHY-1001 : End global iterations;  1.048790s wall, 1.734375s user + 0.015625s system = 1.750000s CPU (166.9%)

PHY-1001 : Congestion index: top1 = 77.46, top5 = 58.64, top10 = 49.38, top15 = 44.05.
PHY-3001 : End congestion estimation;  1.410460s wall, 2.109375s user + 0.015625s system = 2.125000s CPU (150.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.025107s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (99.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.35138e-05
PHY-3002 : Step(152): len = 441836, overlap = 402.562
PHY-3002 : Step(153): len = 451591, overlap = 324.625
PHY-3002 : Step(154): len = 444059, overlap = 312.375
PHY-3002 : Step(155): len = 438997, overlap = 281.25
PHY-3002 : Step(156): len = 434926, overlap = 257.438
PHY-3002 : Step(157): len = 432254, overlap = 240.688
PHY-3002 : Step(158): len = 430231, overlap = 237.25
PHY-3002 : Step(159): len = 426883, overlap = 217.219
PHY-3002 : Step(160): len = 426967, overlap = 216.781
PHY-3002 : Step(161): len = 425790, overlap = 224.375
PHY-3002 : Step(162): len = 422738, overlap = 225.219
PHY-3002 : Step(163): len = 421342, overlap = 228.094
PHY-3002 : Step(164): len = 421060, overlap = 212.688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000187028
PHY-3002 : Step(165): len = 420347, overlap = 206.875
PHY-3002 : Step(166): len = 422315, overlap = 206.469
PHY-3002 : Step(167): len = 424295, overlap = 198.531
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000374055
PHY-3002 : Step(168): len = 427962, overlap = 193
PHY-3002 : Step(169): len = 434262, overlap = 183.25
PHY-3002 : Step(170): len = 438548, overlap = 174.188
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00074811
PHY-3002 : Step(171): len = 438871, overlap = 171.844
PHY-3002 : Step(172): len = 442001, overlap = 157.312
PHY-3002 : Step(173): len = 445564, overlap = 157.656
PHY-3002 : Step(174): len = 446891, overlap = 154.031
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00137664
PHY-3002 : Step(175): len = 447698, overlap = 149.469
PHY-3002 : Step(176): len = 451893, overlap = 138.469
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81105, tnet num: 21905, tinst num: 19440, tnode num: 114042, tedge num: 127673.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.558127s wall, 1.453125s user + 0.109375s system = 1.562500s CPU (100.3%)

RUN-1004 : used memory is 568 MB, reserved memory is 541 MB, peak memory is 701 MB
OPT-1001 : Total overflow 482.81 peak overflow 4.38
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 517/21907.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 532592, over cnt = 2546(7%), over = 8834, worst = 23
PHY-1001 : End global iterations;  1.245750s wall, 2.000000s user + 0.031250s system = 2.031250s CPU (163.1%)

PHY-1001 : Congestion index: top1 = 56.10, top5 = 46.34, top10 = 41.81, top15 = 38.95.
PHY-1001 : End incremental global routing;  1.517308s wall, 2.265625s user + 0.031250s system = 2.296875s CPU (151.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.068387s wall, 1.015625s user + 0.046875s system = 1.062500s CPU (99.4%)

OPT-1001 : 17 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19361 has valid locations, 237 needs to be replaced
PHY-3001 : design contains 19660 instances, 5699 luts, 12356 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 467474
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17259/22127.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 543848, over cnt = 2559(7%), over = 8890, worst = 22
PHY-1001 : End global iterations;  0.209213s wall, 0.265625s user + 0.031250s system = 0.296875s CPU (141.9%)

PHY-1001 : Congestion index: top1 = 56.29, top5 = 46.61, top10 = 42.11, top15 = 39.32.
PHY-3001 : End congestion estimation;  0.474763s wall, 0.531250s user + 0.031250s system = 0.562500s CPU (118.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81828, tnet num: 22125, tinst num: 19660, tnode num: 115094, tedge num: 128679.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.588411s wall, 1.578125s user + 0.000000s system = 1.578125s CPU (99.4%)

RUN-1004 : used memory is 611 MB, reserved memory is 601 MB, peak memory is 704 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22125 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.697255s wall, 2.687500s user + 0.000000s system = 2.687500s CPU (99.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(177): len = 467386, overlap = 1.0625
PHY-3002 : Step(178): len = 468077, overlap = 1
PHY-3002 : Step(179): len = 468326, overlap = 0.9375
PHY-3002 : Step(180): len = 468373, overlap = 1
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17301/22127.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 542728, over cnt = 2569(7%), over = 8942, worst = 22
PHY-1001 : End global iterations;  0.193591s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (153.4%)

PHY-1001 : Congestion index: top1 = 56.64, top5 = 46.71, top10 = 42.25, top15 = 39.42.
PHY-3001 : End congestion estimation;  0.451147s wall, 0.562500s user + 0.000000s system = 0.562500s CPU (124.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22125 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.074000s wall, 1.062500s user + 0.015625s system = 1.078125s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000511025
PHY-3002 : Step(181): len = 468601, overlap = 141.562
PHY-3002 : Step(182): len = 469274, overlap = 141.281
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00102205
PHY-3002 : Step(183): len = 469456, overlap = 140.844
PHY-3002 : Step(184): len = 469856, overlap = 140.656
PHY-3001 : Final: Len = 469856, Over = 140.656
PHY-3001 : End incremental placement;  5.657750s wall, 5.859375s user + 0.265625s system = 6.125000s CPU (108.3%)

OPT-1001 : Total overflow 487.38 peak overflow 4.38
OPT-1001 : End high-fanout net optimization;  8.803024s wall, 9.875000s user + 0.343750s system = 10.218750s CPU (116.1%)

OPT-1001 : Current memory(MB): used = 705, reserve = 685, peak = 722.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17304/22127.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 544704, over cnt = 2542(7%), over = 8472, worst = 22
PHY-1002 : len = 586200, over cnt = 1706(4%), over = 4326, worst = 20
PHY-1002 : len = 630752, over cnt = 556(1%), over = 1164, worst = 16
PHY-1002 : len = 643504, over cnt = 185(0%), over = 373, worst = 14
PHY-1002 : len = 650248, over cnt = 10(0%), over = 12, worst = 2
PHY-1001 : End global iterations;  1.343672s wall, 2.015625s user + 0.015625s system = 2.031250s CPU (151.2%)

PHY-1001 : Congestion index: top1 = 48.64, top5 = 42.72, top10 = 39.66, top15 = 37.71.
OPT-1001 : End congestion update;  1.620019s wall, 2.296875s user + 0.015625s system = 2.312500s CPU (142.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22125 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.945818s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (99.1%)

OPT-0007 : Start: WNS 3887 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.572527s wall, 3.250000s user + 0.015625s system = 3.265625s CPU (126.9%)

OPT-1001 : Current memory(MB): used = 680, reserve = 660, peak = 722.
OPT-1001 : End physical optimization;  13.298212s wall, 15.093750s user + 0.484375s system = 15.578125s CPU (117.1%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5699 LUT to BLE ...
SYN-4008 : Packed 5699 LUT and 2732 SEQ to BLE.
SYN-4003 : Packing 9624 remaining SEQ's ...
SYN-4005 : Packed 3334 SEQ with LUT/SLICE
SYN-4006 : 160 single LUT's are left
SYN-4006 : 6290 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11989/13860 primitive instances ...
PHY-3001 : End packing;  3.028775s wall, 3.015625s user + 0.000000s system = 3.015625s CPU (99.6%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8171 instances
RUN-1001 : 4033 mslices, 4033 lslices, 59 pads, 41 brams, 0 dsps
RUN-1001 : There are total 19449 nets
RUN-1001 : 13604 nets have 2 pins
RUN-1001 : 4442 nets have [3 - 5] pins
RUN-1001 : 870 nets have [6 - 10] pins
RUN-1001 : 393 nets have [11 - 20] pins
RUN-1001 : 131 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8169 instances, 8066 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Cell area utilization is 86%
PHY-3001 : After packing: Len = 488478, Over = 387.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7847/19449.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 617280, over cnt = 1654(4%), over = 2601, worst = 8
PHY-1002 : len = 623440, over cnt = 1064(3%), over = 1484, worst = 7
PHY-1002 : len = 636856, over cnt = 335(0%), over = 447, worst = 6
PHY-1002 : len = 643328, over cnt = 67(0%), over = 79, worst = 3
PHY-1002 : len = 644928, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.326959s wall, 2.109375s user + 0.046875s system = 2.156250s CPU (162.5%)

PHY-1001 : Congestion index: top1 = 50.15, top5 = 43.26, top10 = 39.83, top15 = 37.46.
PHY-3001 : End congestion estimation;  1.672588s wall, 2.468750s user + 0.046875s system = 2.515625s CPU (150.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67966, tnet num: 19447, tinst num: 8169, tnode num: 92184, tedge num: 112150.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.818057s wall, 1.796875s user + 0.015625s system = 1.812500s CPU (99.7%)

RUN-1004 : used memory is 602 MB, reserved memory is 598 MB, peak memory is 722 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19447 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.808912s wall, 2.781250s user + 0.015625s system = 2.796875s CPU (99.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.09893e-05
PHY-3002 : Step(185): len = 492621, overlap = 361.75
PHY-3002 : Step(186): len = 492065, overlap = 378.25
PHY-3002 : Step(187): len = 492561, overlap = 390.5
PHY-3002 : Step(188): len = 494910, overlap = 396.25
PHY-3002 : Step(189): len = 494103, overlap = 391.75
PHY-3002 : Step(190): len = 494898, overlap = 393.25
PHY-3002 : Step(191): len = 493030, overlap = 400.75
PHY-3002 : Step(192): len = 490366, overlap = 406.25
PHY-3002 : Step(193): len = 489389, overlap = 401.25
PHY-3002 : Step(194): len = 488096, overlap = 409
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000101979
PHY-3002 : Step(195): len = 492747, overlap = 394.25
PHY-3002 : Step(196): len = 497921, overlap = 384.25
PHY-3002 : Step(197): len = 498850, overlap = 385.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000203957
PHY-3002 : Step(198): len = 505994, overlap = 375
PHY-3002 : Step(199): len = 514409, overlap = 357.75
PHY-3002 : Step(200): len = 514180, overlap = 353
PHY-3002 : Step(201): len = 513662, overlap = 351
PHY-3002 : Step(202): len = 513454, overlap = 348.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.792872s wall, 0.921875s user + 0.843750s system = 1.765625s CPU (222.7%)

PHY-3001 : Trial Legalized: Len = 625109
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 567/19449.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 712232, over cnt = 2443(6%), over = 4073, worst = 9
PHY-1002 : len = 728248, over cnt = 1503(4%), over = 2117, worst = 6
PHY-1002 : len = 748864, over cnt = 496(1%), over = 680, worst = 5
PHY-1002 : len = 757400, over cnt = 104(0%), over = 148, worst = 5
PHY-1002 : len = 760360, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.029398s wall, 3.500000s user + 0.046875s system = 3.546875s CPU (174.8%)

PHY-1001 : Congestion index: top1 = 51.25, top5 = 46.18, top10 = 43.42, top15 = 41.55.
PHY-3001 : End congestion estimation;  2.421855s wall, 3.859375s user + 0.078125s system = 3.937500s CPU (162.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19447 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.964797s wall, 0.921875s user + 0.031250s system = 0.953125s CPU (98.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000177743
PHY-3002 : Step(203): len = 583000, overlap = 87.25
PHY-3002 : Step(204): len = 562733, overlap = 137.75
PHY-3002 : Step(205): len = 550220, overlap = 174
PHY-3002 : Step(206): len = 542084, overlap = 211.5
PHY-3002 : Step(207): len = 537309, overlap = 232
PHY-3002 : Step(208): len = 534553, overlap = 252
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000355485
PHY-3002 : Step(209): len = 538014, overlap = 245.25
PHY-3002 : Step(210): len = 542080, overlap = 242.25
PHY-3002 : Step(211): len = 543349, overlap = 246
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(212): len = 545701, overlap = 241
PHY-3002 : Step(213): len = 552431, overlap = 238
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.058996s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.9%)

PHY-3001 : Legalized: Len = 594674, Over = 0
PHY-3001 : Spreading special nets. 55 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.096283s wall, 0.078125s user + 0.015625s system = 0.093750s CPU (97.4%)

PHY-3001 : 84 instances has been re-located, deltaX = 29, deltaY = 44, maxDist = 2.
PHY-3001 : Final: Len = 595936, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67966, tnet num: 19447, tinst num: 8169, tnode num: 92184, tedge num: 112150.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.987472s wall, 1.968750s user + 0.031250s system = 2.000000s CPU (100.6%)

RUN-1004 : used memory is 608 MB, reserved memory is 602 MB, peak memory is 722 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 4302/19449.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 694664, over cnt = 2243(6%), over = 3523, worst = 8
PHY-1002 : len = 706864, over cnt = 1434(4%), over = 1943, worst = 7
PHY-1002 : len = 727808, over cnt = 321(0%), over = 409, worst = 7
PHY-1002 : len = 732888, over cnt = 89(0%), over = 103, worst = 3
PHY-1002 : len = 734656, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.906312s wall, 2.750000s user + 0.078125s system = 2.828125s CPU (148.4%)

PHY-1001 : Congestion index: top1 = 49.91, top5 = 44.77, top10 = 41.89, top15 = 40.00.
PHY-1001 : End incremental global routing;  2.242516s wall, 3.078125s user + 0.078125s system = 3.156250s CPU (140.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19447 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.985105s wall, 0.984375s user + 0.015625s system = 1.000000s CPU (101.5%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8106 has valid locations, 9 needs to be replaced
PHY-3001 : design contains 8177 instances, 8074 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 597242
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17449/19456.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 735728, over cnt = 13(0%), over = 13, worst = 1
PHY-1002 : len = 735712, over cnt = 10(0%), over = 10, worst = 1
PHY-1002 : len = 735760, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 735808, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 735936, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.719160s wall, 0.750000s user + 0.015625s system = 0.765625s CPU (106.5%)

PHY-1001 : Congestion index: top1 = 49.91, top5 = 44.80, top10 = 41.92, top15 = 40.03.
PHY-3001 : End congestion estimation;  1.041011s wall, 1.078125s user + 0.015625s system = 1.093750s CPU (105.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68013, tnet num: 19454, tinst num: 8177, tnode num: 92239, tedge num: 112204.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.986290s wall, 1.984375s user + 0.000000s system = 1.984375s CPU (99.9%)

RUN-1004 : used memory is 641 MB, reserved memory is 631 MB, peak memory is 722 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19454 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.988220s wall, 2.984375s user + 0.000000s system = 2.984375s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(214): len = 597147, overlap = 0
PHY-3002 : Step(215): len = 597191, overlap = 0
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17447/19456.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 735376, over cnt = 15(0%), over = 15, worst = 1
PHY-1002 : len = 735376, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 735376, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 735440, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 735536, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.714387s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (100.6%)

PHY-1001 : Congestion index: top1 = 49.91, top5 = 44.80, top10 = 41.91, top15 = 40.02.
PHY-3001 : End congestion estimation;  1.027826s wall, 1.046875s user + 0.000000s system = 1.046875s CPU (101.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19454 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.974492s wall, 0.953125s user + 0.015625s system = 0.968750s CPU (99.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00124639
PHY-3002 : Step(216): len = 597216, overlap = 1.25
PHY-3002 : Step(217): len = 597238, overlap = 0.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006914s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (226.0%)

PHY-3001 : Legalized: Len = 597277, Over = 0
PHY-3001 : End spreading;  0.073665s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (106.1%)

PHY-3001 : Final: Len = 597277, Over = 0
PHY-3001 : End incremental placement;  6.696113s wall, 6.875000s user + 0.140625s system = 7.015625s CPU (104.8%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.446341s wall, 11.437500s user + 0.265625s system = 11.703125s CPU (112.0%)

OPT-1001 : Current memory(MB): used = 713, reserve = 698, peak = 722.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17447/19456.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 735496, over cnt = 8(0%), over = 10, worst = 2
PHY-1002 : len = 735488, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 735560, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 735560, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.535403s wall, 0.546875s user + 0.015625s system = 0.562500s CPU (105.1%)

PHY-1001 : Congestion index: top1 = 49.91, top5 = 44.79, top10 = 41.89, top15 = 39.99.
OPT-1001 : End congestion update;  0.850006s wall, 0.859375s user + 0.015625s system = 0.875000s CPU (102.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19454 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.805076s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.0%)

OPT-0007 : Start: WNS 3891 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.660262s wall, 1.671875s user + 0.015625s system = 1.687500s CPU (101.6%)

OPT-1001 : Current memory(MB): used = 713, reserve = 698, peak = 722.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19454 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.810346s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (100.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17459/19456.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 735560, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.120927s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (90.4%)

PHY-1001 : Congestion index: top1 = 49.91, top5 = 44.79, top10 = 41.89, top15 = 39.99.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19454 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.789559s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (100.9%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3891 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 49.517241
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3891ps with logic level 8 
RUN-1001 :       #2 path slack 3984ps with logic level 8 
OPT-1001 : End physical optimization;  16.426987s wall, 17.359375s user + 0.343750s system = 17.703125s CPU (107.8%)

RUN-1003 : finish command "place" in  72.796940s wall, 132.859375s user + 8.125000s system = 140.984375s CPU (193.7%)

RUN-1004 : used memory is 594 MB, reserved memory is 580 MB, peak memory is 722 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.726013s wall, 2.984375s user + 0.000000s system = 2.984375s CPU (172.9%)

RUN-1004 : used memory is 594 MB, reserved memory is 582 MB, peak memory is 722 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8179 instances
RUN-1001 : 4041 mslices, 4033 lslices, 59 pads, 41 brams, 0 dsps
RUN-1001 : There are total 19456 nets
RUN-1001 : 13604 nets have 2 pins
RUN-1001 : 4443 nets have [3 - 5] pins
RUN-1001 : 871 nets have [6 - 10] pins
RUN-1001 : 399 nets have [11 - 20] pins
RUN-1001 : 130 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68013, tnet num: 19454, tinst num: 8177, tnode num: 92239, tedge num: 112204.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.878639s wall, 1.875000s user + 0.000000s system = 1.875000s CPU (99.8%)

RUN-1004 : used memory is 592 MB, reserved memory is 582 MB, peak memory is 722 MB
PHY-1001 : 4041 mslices, 4033 lslices, 59 pads, 41 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19454 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 670984, over cnt = 2384(6%), over = 4052, worst = 9
PHY-1002 : len = 688840, over cnt = 1532(4%), over = 2151, worst = 9
PHY-1002 : len = 712928, over cnt = 344(0%), over = 404, worst = 5
PHY-1002 : len = 720240, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 720560, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.868556s wall, 3.234375s user + 0.031250s system = 3.265625s CPU (174.8%)

PHY-1001 : Congestion index: top1 = 49.78, top5 = 44.60, top10 = 41.71, top15 = 39.74.
PHY-1001 : End global routing;  2.235555s wall, 3.593750s user + 0.031250s system = 3.625000s CPU (162.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 699, reserve = 690, peak = 722.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 973, reserve = 963, peak = 973.
PHY-1001 : End build detailed router design. 4.735830s wall, 4.703125s user + 0.031250s system = 4.734375s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 188312, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.948508s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (100.5%)

PHY-1001 : Current memory(MB): used = 1008, reserve = 1000, peak = 1008.
PHY-1001 : End phase 1; 0.956939s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (101.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 55% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.69291e+06, over cnt = 1505(0%), over = 1515, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1025, reserve = 1016, peak = 1025.
PHY-1001 : End initial routed; 16.713761s wall, 45.421875s user + 0.406250s system = 45.828125s CPU (274.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18184(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.890   |   0.000   |   0   
RUN-1001 :   Hold   |   0.140   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.625529s wall, 3.625000s user + 0.000000s system = 3.625000s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1036, reserve = 1028, peak = 1036.
PHY-1001 : End phase 2; 20.339468s wall, 49.046875s user + 0.406250s system = 49.453125s CPU (243.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.69291e+06, over cnt = 1505(0%), over = 1515, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.258213s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (96.8%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.68173e+06, over cnt = 603(0%), over = 603, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.838469s wall, 1.312500s user + 0.000000s system = 1.312500s CPU (156.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.68322e+06, over cnt = 126(0%), over = 126, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.548601s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (136.7%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.68499e+06, over cnt = 11(0%), over = 11, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.359670s wall, 0.421875s user + 0.015625s system = 0.437500s CPU (121.6%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.68513e+06, over cnt = 6(0%), over = 6, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.259448s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (102.4%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.68534e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.288467s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (113.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18184(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.890   |   0.000   |   0   
RUN-1001 :   Hold   |   0.140   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.650098s wall, 3.656250s user + 0.000000s system = 3.656250s CPU (100.2%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 385 feed throughs used by 323 nets
PHY-1001 : End commit to database; 2.249233s wall, 2.203125s user + 0.046875s system = 2.250000s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1122, reserve = 1116, peak = 1122.
PHY-1001 : End phase 3; 9.004027s wall, 9.687500s user + 0.093750s system = 9.781250s CPU (108.6%)

PHY-1003 : Routed, final wirelength = 1.68534e+06
PHY-1001 : Current memory(MB): used = 1127, reserve = 1121, peak = 1127.
PHY-1001 : End export database. 0.062110s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.6%)

PHY-1001 : End detail routing;  35.541849s wall, 64.906250s user + 0.531250s system = 65.437500s CPU (184.1%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68013, tnet num: 19454, tinst num: 8177, tnode num: 92239, tedge num: 112204.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.772631s wall, 1.765625s user + 0.000000s system = 1.765625s CPU (99.6%)

RUN-1004 : used memory is 1059 MB, reserved memory is 1054 MB, peak memory is 1127 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  44.199287s wall, 74.890625s user + 0.593750s system = 75.484375s CPU (170.8%)

RUN-1004 : used memory is 1059 MB, reserved memory is 1054 MB, peak memory is 1127 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8868   out of  19600   45.24%
#reg                    12450   out of  19600   63.52%
#le                     15113
  #lut only              2663   out of  15113   17.62%
  #reg only              6245   out of  15113   41.32%
  #lut&reg               6205   out of  15113   41.06%
#dsp                        0   out of     29    0.00%
#bram                      41   out of     64   64.06%
  #bram9k                  39
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       59   out of    188   31.38%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6824
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          168
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         F2        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         D1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         B3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         C1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         A3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         A2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         B2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         B1        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F15        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDZ        INPUT        K15        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         P2        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         N4        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         M5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         N6        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         N5        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         P5        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M3        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         M4        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         H2        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         M1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         L1        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         N1        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         N3        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         M2        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT         T5        LVCMOS33           8            NONE           OREG       
    Fog_ENY        OUTPUT         T4        LVCMOS33           8            NONE           OREG       
    Fog_ENZ        OUTPUT         T7        LVCMOS33           8            NONE           OREG       
    Fog_INT        OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         T6        LVCMOS33           8            N/A            OREG       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        R15        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E1        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        G11        LVCMOS25           8            N/A            NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15113  |7366    |1502    |12494   |41      |0       |
|  AnyFog_dataX                      |AnyFog          |209    |83      |22      |174     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |53      |22      |51      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |196    |79      |22      |158     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |85     |54      |22      |47      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |209    |78      |22      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |58      |22      |50      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2952   |638     |39      |2855    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |63     |35      |5       |53      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |220    |85      |5       |205     |0       |0       |
|    STADOP_com2                     |STADOP          |552    |96      |0       |548     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |51      |14      |43      |0       |0       |
|    head_com2                       |uniheading      |267    |61      |5       |250     |0       |0       |
|    rmc_com2                        |Gprmc           |36     |36      |0       |30      |0       |0       |
|    uart_com2                       |Agrica          |1436   |244     |10      |1413    |0       |0       |
|  COM3                              |COM3_Control    |275    |143     |19      |234     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |59     |39      |5       |51      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |60     |46      |14      |35      |0       |0       |
|    rmc_com3                        |Gprmc           |156    |58      |0       |148     |0       |0       |
|  DATA                              |Data_Processing |8819   |4520    |1122    |7020    |0       |0       |
|    DIV_Dtemp                       |Divider         |797    |325     |84      |674     |0       |0       |
|    DIV_Utemp                       |Divider         |624    |293     |84      |501     |0       |0       |
|    DIV_accX                        |Divider         |540    |309     |84      |416     |0       |0       |
|    DIV_accY                        |Divider         |657    |310     |102     |504     |0       |0       |
|    DIV_accZ                        |Divider         |713    |383     |132     |510     |0       |0       |
|    DIV_rateX                       |Divider         |673    |396     |132     |459     |0       |0       |
|    DIV_rateY                       |Divider         |538    |359     |132     |327     |0       |0       |
|    DIV_rateZ                       |Divider         |566    |371     |132     |362     |0       |0       |
|    genclk                          |genclk          |268    |165     |89      |107     |0       |0       |
|  FMC                               |FMC_Ctrl        |530    |473     |43      |367     |0       |0       |
|  IIC                               |I2C_master      |281    |247     |11      |247     |0       |0       |
|  IMU_CTRL                          |SCHA634         |905    |657     |61      |738     |0       |0       |
|    CtrlData                        |CtrlData        |471    |418     |47      |338     |0       |0       |
|      usms                          |Time_1ms        |27     |22      |5       |19      |0       |0       |
|    SPIM                            |SPI_SCHA634     |434    |239     |14      |400     |0       |0       |
|  POWER                             |POWER_EN        |97     |55      |38      |38      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |629    |393     |103     |435     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |629    |393     |103     |435     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |281    |168     |0       |264     |0       |0       |
|        reg_inst                    |register        |278    |165     |0       |261     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |348    |225     |103     |171     |0       |0       |
|        bus_inst                    |bus_top         |134    |88      |46      |52      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |26     |16      |10      |9       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |3      |3       |0       |3       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |3      |3       |0       |3       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |52     |34      |18      |19      |0       |0       |
|          BUS_DETECTOR[5]$bus_nodes |bus_det         |50     |32      |18      |18      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |135    |99      |29      |87      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13544  
    #2          2       3438   
    #3          3        683   
    #4          4        322   
    #5        5-10       951   
    #6        11-50      439   
    #7       51-100      10    
    #8       101-500      3    
    #9        >500        2    
  Average     2.14             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.172371s wall, 3.687500s user + 0.046875s system = 3.734375s CPU (171.9%)

RUN-1004 : used memory is 1059 MB, reserved memory is 1055 MB, peak memory is 1127 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68013, tnet num: 19454, tinst num: 8177, tnode num: 92239, tedge num: 112204.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.807924s wall, 1.796875s user + 0.000000s system = 1.796875s CPU (99.4%)

RUN-1004 : used memory is 1061 MB, reserved memory is 1056 MB, peak memory is 1127 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19454 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.477143s wall, 1.484375s user + 0.000000s system = 1.484375s CPU (100.5%)

RUN-1004 : used memory is 1066 MB, reserved memory is 1060 MB, peak memory is 1127 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: e7acc41f3f3c0493db2e7c96172f62b19e1071d28814331b86e973fbb54fbf81 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8177
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19456, pip num: 146888
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 385
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3233 valid insts, and 412508 bits set as '1'.
BIT-1004 : the usercode register value: 00000000000000111011100100001111
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.663825s wall, 126.390625s user + 0.250000s system = 126.640625s CPU (1000.0%)

RUN-1004 : used memory is 1190 MB, reserved memory is 1175 MB, peak memory is 1305 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250820_150943.log"

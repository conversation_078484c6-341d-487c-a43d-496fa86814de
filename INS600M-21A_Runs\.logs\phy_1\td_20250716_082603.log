============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Wed Jul 16 08:26:03 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  4.981020s wall, 1.343750s user + 3.640625s system = 4.984375s CPU (100.1%)

RUN-1004 : used memory is 78 MB, reserved memory is 40 MB, peak memory is 89 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.654548s wall, 1.625000s user + 0.031250s system = 1.656250s CPU (100.1%)

RUN-1004 : used memory is 299 MB, reserved memory is 268 MB, peak memory is 302 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 2 view nodes, 24 trigger nets, 24 data nets.
KIT-1004 : Chipwatcher code = 1010010110001101
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=78) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=78) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=78)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=78)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22200/12 useful/useless nets, 19215/4 useful/useless insts
SYN-1016 : Merged 17 instances.
SYN-1032 : 21976/16 useful/useless nets, 19539/12 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 2 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 2 mux instances.
SYN-1015 : Optimize round 1, 279 better
SYN-1014 : Optimize round 2
SYN-1032 : 21767/30 useful/useless nets, 19330/32 useful/useless insts
SYN-1015 : Optimize round 2, 64 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  1.889478s wall, 1.828125s user + 0.062500s system = 1.890625s CPU (100.1%)

RUN-1004 : used memory is 323 MB, reserved memory is 291 MB, peak memory is 325 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 21791/155 useful/useless nets, 19375/29 useful/useless insts
SYN-1016 : Merged 21 instances.
SYN-2571 : Optimize after map_dsp, round 1, 205 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 24 instances.
SYN-2501 : Optimize round 1, 50 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 14 macro adder
SYN-1019 : Optimized 9 mux instances.
SYN-1016 : Merged 10 instances.
SYN-1032 : 22162/5 useful/useless nets, 19746/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80394, tnet num: 22162, tinst num: 19745, tnode num: 112994, tedge num: 125642.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.129094s wall, 1.109375s user + 0.015625s system = 1.125000s CPU (99.6%)

RUN-1004 : used memory is 460 MB, reserved memory is 429 MB, peak memory is 460 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22162 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 173 (3.57), #lev = 7 (1.79)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 173 (3.57), #lev = 7 (1.79)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 387 instances into 173 LUTs, name keeping = 77%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 290 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 108 adder to BLE ...
SYN-4008 : Packed 108 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  3.829402s wall, 3.750000s user + 0.062500s system = 3.812500s CPU (99.6%)

RUN-1004 : used memory is 345 MB, reserved memory is 312 MB, peak memory is 567 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  6.001668s wall, 5.859375s user + 0.125000s system = 5.984375s CPU (99.7%)

RUN-1004 : used memory is 346 MB, reserved memory is 313 MB, peak memory is 567 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (177 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19054 instances
RUN-0007 : 5502 luts, 12036 seqs, 937 mslices, 494 lslices, 56 pads, 24 brams, 0 dsps
RUN-1001 : There are total 21498 nets
RUN-1001 : 16190 nets have 2 pins
RUN-1001 : 4140 nets have [3 - 5] pins
RUN-1001 : 816 nets have [6 - 10] pins
RUN-1001 : 224 nets have [11 - 20] pins
RUN-1001 : 110 nets have [21 - 99] pins
RUN-1001 : 18 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4787     
RUN-1001 :   No   |  No   |  Yes  |     631     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     349     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19052 instances, 5502 luts, 12036 seqs, 1431 slices, 283 macros(1431 instances: 937 mslices 494 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 61%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 78957, tnet num: 21496, tinst num: 19052, tnode num: 111151, tedge num: 124046.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.067476s wall, 1.046875s user + 0.015625s system = 1.062500s CPU (99.5%)

RUN-1004 : used memory is 518 MB, reserved memory is 491 MB, peak memory is 567 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21496 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.848878s wall, 1.812500s user + 0.031250s system = 1.843750s CPU (99.7%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.46335e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19052.
PHY-3001 : Level 1 #clusters 2159.
PHY-3001 : End clustering;  0.123692s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (101.1%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 61%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 819711, overlap = 598.938
PHY-3002 : Step(2): len = 757351, overlap = 645.938
PHY-3002 : Step(3): len = 492831, overlap = 829.094
PHY-3002 : Step(4): len = 421714, overlap = 894.969
PHY-3002 : Step(5): len = 341263, overlap = 968.438
PHY-3002 : Step(6): len = 307457, overlap = 1025.88
PHY-3002 : Step(7): len = 256998, overlap = 1072.84
PHY-3002 : Step(8): len = 230187, overlap = 1134.84
PHY-3002 : Step(9): len = 204173, overlap = 1196.53
PHY-3002 : Step(10): len = 189934, overlap = 1249.34
PHY-3002 : Step(11): len = 167809, overlap = 1299.72
PHY-3002 : Step(12): len = 156766, overlap = 1346.97
PHY-3002 : Step(13): len = 144048, overlap = 1365.97
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.41611e-06
PHY-3002 : Step(14): len = 149853, overlap = 1331.5
PHY-3002 : Step(15): len = 201870, overlap = 1184.19
PHY-3002 : Step(16): len = 213688, overlap = 1086.78
PHY-3002 : Step(17): len = 212893, overlap = 1006.16
PHY-3002 : Step(18): len = 206043, overlap = 979.688
PHY-3002 : Step(19): len = 200930, overlap = 971.688
PHY-3002 : Step(20): len = 193371, overlap = 978.219
PHY-3002 : Step(21): len = 189718, overlap = 970.75
PHY-3002 : Step(22): len = 185356, overlap = 977.969
PHY-3002 : Step(23): len = 181193, overlap = 974.281
PHY-3002 : Step(24): len = 177450, overlap = 974.188
PHY-3002 : Step(25): len = 175215, overlap = 981.062
PHY-3002 : Step(26): len = 173105, overlap = 992.125
PHY-3002 : Step(27): len = 172325, overlap = 990.875
PHY-3002 : Step(28): len = 172091, overlap = 994.125
PHY-3002 : Step(29): len = 171081, overlap = 975.75
PHY-3002 : Step(30): len = 170347, overlap = 941.594
PHY-3002 : Step(31): len = 169342, overlap = 956.438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.83223e-06
PHY-3002 : Step(32): len = 177321, overlap = 939.938
PHY-3002 : Step(33): len = 193938, overlap = 890.125
PHY-3002 : Step(34): len = 198540, overlap = 848.938
PHY-3002 : Step(35): len = 200389, overlap = 800.562
PHY-3002 : Step(36): len = 200125, overlap = 780.469
PHY-3002 : Step(37): len = 199602, overlap = 777.219
PHY-3002 : Step(38): len = 197332, overlap = 789.125
PHY-3002 : Step(39): len = 196743, overlap = 784.438
PHY-3002 : Step(40): len = 195419, overlap = 783.562
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 5.66446e-06
PHY-3002 : Step(41): len = 206827, overlap = 741.875
PHY-3002 : Step(42): len = 221959, overlap = 662.094
PHY-3002 : Step(43): len = 226625, overlap = 631.156
PHY-3002 : Step(44): len = 228127, overlap = 621.875
PHY-3002 : Step(45): len = 227611, overlap = 629.531
PHY-3002 : Step(46): len = 227025, overlap = 633.594
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 1.13289e-05
PHY-3002 : Step(47): len = 237444, overlap = 610.219
PHY-3002 : Step(48): len = 254382, overlap = 541.906
PHY-3002 : Step(49): len = 258351, overlap = 509.312
PHY-3002 : Step(50): len = 258090, overlap = 495.562
PHY-3002 : Step(51): len = 256350, overlap = 477.844
PHY-3002 : Step(52): len = 255119, overlap = 467
PHY-3002 : Step(53): len = 253491, overlap = 486.5
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 2.26578e-05
PHY-3002 : Step(54): len = 265327, overlap = 437.281
PHY-3002 : Step(55): len = 281101, overlap = 360.969
PHY-3002 : Step(56): len = 285067, overlap = 338.562
PHY-3002 : Step(57): len = 285566, overlap = 331.625
PHY-3002 : Step(58): len = 284490, overlap = 324.812
PHY-3002 : Step(59): len = 282488, overlap = 329.844
PHY-3002 : Step(60): len = 281043, overlap = 335.906
PHY-3002 : Step(61): len = 280516, overlap = 345.094
PHY-3002 : Step(62): len = 280411, overlap = 362.094
PHY-3002 : Step(63): len = 280380, overlap = 383.219
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 4.53157e-05
PHY-3002 : Step(64): len = 288384, overlap = 343.781
PHY-3002 : Step(65): len = 298303, overlap = 315.844
PHY-3002 : Step(66): len = 300743, overlap = 304.531
PHY-3002 : Step(67): len = 302286, overlap = 288.156
PHY-3002 : Step(68): len = 300977, overlap = 291.5
PHY-3002 : Step(69): len = 301257, overlap = 288.375
PHY-3002 : Step(70): len = 299225, overlap = 301.75
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 9.06052e-05
PHY-3002 : Step(71): len = 306104, overlap = 292.062
PHY-3002 : Step(72): len = 314637, overlap = 253.938
PHY-3002 : Step(73): len = 318169, overlap = 236.719
PHY-3002 : Step(74): len = 319405, overlap = 215.562
PHY-3002 : Step(75): len = 318498, overlap = 211.906
PHY-3002 : Step(76): len = 318069, overlap = 214.906
PHY-3002 : Step(77): len = 316083, overlap = 208.969
PHY-3002 : Step(78): len = 316989, overlap = 213.688
PHY-3002 : Step(79): len = 316572, overlap = 209.625
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.00018121
PHY-3002 : Step(80): len = 319749, overlap = 204.688
PHY-3002 : Step(81): len = 327122, overlap = 208.688
PHY-3002 : Step(82): len = 330372, overlap = 211.406
PHY-3002 : Step(83): len = 332578, overlap = 213.969
PHY-3002 : Step(84): len = 332612, overlap = 205
PHY-3002 : Step(85): len = 331711, overlap = 184.062
PHY-3002 : Step(86): len = 331314, overlap = 181.75
PHY-3002 : Step(87): len = 331171, overlap = 185.656
PHY-3002 : Step(88): len = 331800, overlap = 179.156
PHY-3002 : Step(89): len = 329372, overlap = 175.844
PHY-3002 : Step(90): len = 328925, overlap = 176.625
PHY-3002 : Step(91): len = 328442, overlap = 176
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000338193
PHY-3002 : Step(92): len = 330328, overlap = 168.938
PHY-3002 : Step(93): len = 334524, overlap = 171.844
PHY-3002 : Step(94): len = 336557, overlap = 172.5
PHY-3002 : Step(95): len = 337716, overlap = 167.438
PHY-3002 : Step(96): len = 337184, overlap = 181.094
PHY-3002 : Step(97): len = 337152, overlap = 175.531
PHY-3002 : Step(98): len = 336306, overlap = 180.969
PHY-3002 : Step(99): len = 336284, overlap = 177.812
PHY-3002 : Step(100): len = 336194, overlap = 185.25
PHY-3002 : Step(101): len = 336399, overlap = 176.625
PHY-3002 : Step(102): len = 335677, overlap = 171.031
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(103): len = 336810, overlap = 172.906
PHY-3002 : Step(104): len = 338948, overlap = 172.812
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.014821s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (210.8%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21498.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 428768, over cnt = 1136(3%), over = 5295, worst = 36
PHY-1001 : End global iterations;  0.729425s wall, 1.000000s user + 0.046875s system = 1.046875s CPU (143.5%)

PHY-1001 : Congestion index: top1 = 73.23, top5 = 51.41, top10 = 42.04, top15 = 36.52.
PHY-3001 : End congestion estimation;  0.968013s wall, 1.234375s user + 0.062500s system = 1.296875s CPU (134.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21496 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.784313s wall, 0.765625s user + 0.015625s system = 0.781250s CPU (99.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000109953
PHY-3002 : Step(105): len = 379743, overlap = 154.094
PHY-3002 : Step(106): len = 395366, overlap = 138.375
PHY-3002 : Step(107): len = 390387, overlap = 133.406
PHY-3002 : Step(108): len = 388423, overlap = 125.281
PHY-3002 : Step(109): len = 394002, overlap = 110.5
PHY-3002 : Step(110): len = 403595, overlap = 110.562
PHY-3002 : Step(111): len = 409712, overlap = 105.938
PHY-3002 : Step(112): len = 411738, overlap = 108.625
PHY-3002 : Step(113): len = 413728, overlap = 106.469
PHY-3002 : Step(114): len = 417472, overlap = 108.688
PHY-3002 : Step(115): len = 419559, overlap = 108.25
PHY-3002 : Step(116): len = 419334, overlap = 108.75
PHY-3002 : Step(117): len = 420433, overlap = 108.031
PHY-3002 : Step(118): len = 421317, overlap = 115.75
PHY-3002 : Step(119): len = 422982, overlap = 113.625
PHY-3002 : Step(120): len = 422118, overlap = 112.969
PHY-3002 : Step(121): len = 422250, overlap = 111.094
PHY-3002 : Step(122): len = 423053, overlap = 107.344
PHY-3002 : Step(123): len = 422591, overlap = 108.969
PHY-3002 : Step(124): len = 422450, overlap = 106.094
PHY-3002 : Step(125): len = 422768, overlap = 109.062
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000219905
PHY-3002 : Step(126): len = 422703, overlap = 105.375
PHY-3002 : Step(127): len = 423723, overlap = 101.906
PHY-3002 : Step(128): len = 426188, overlap = 99.5312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000414029
PHY-3002 : Step(129): len = 431423, overlap = 94.3438
PHY-3002 : Step(130): len = 439322, overlap = 89.7188
PHY-3002 : Step(131): len = 441626, overlap = 86.4375
PHY-3002 : Step(132): len = 445341, overlap = 83.3438
PHY-3002 : Step(133): len = 448028, overlap = 81.4375
PHY-3002 : Step(134): len = 448077, overlap = 81.125
PHY-3002 : Step(135): len = 450437, overlap = 83.3125
PHY-3002 : Step(136): len = 452759, overlap = 91.4688
PHY-3002 : Step(137): len = 451409, overlap = 100.781
PHY-3002 : Step(138): len = 449730, overlap = 103.281
PHY-3002 : Step(139): len = 449639, overlap = 102.594
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000828059
PHY-3002 : Step(140): len = 449291, overlap = 101.312
PHY-3002 : Step(141): len = 453399, overlap = 101.781
PHY-3002 : Step(142): len = 459877, overlap = 96.8438
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00165612
PHY-3002 : Step(143): len = 458525, overlap = 102.125
PHY-3002 : Step(144): len = 464302, overlap = 99.25
PHY-3002 : Step(145): len = 474249, overlap = 111.844
PHY-3002 : Step(146): len = 477469, overlap = 106.812
PHY-3002 : Step(147): len = 478356, overlap = 112.469
PHY-3002 : Step(148): len = 479271, overlap = 111.125
PHY-3002 : Step(149): len = 481259, overlap = 111.781
PHY-3002 : Step(150): len = 482436, overlap = 111.938
PHY-3002 : Step(151): len = 482419, overlap = 110.281
PHY-3002 : Step(152): len = 482586, overlap = 114.156
PHY-3002 : Step(153): len = 481842, overlap = 114.781
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 73/21498.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 538008, over cnt = 2095(5%), over = 10295, worst = 53
PHY-1001 : End global iterations;  0.919311s wall, 1.453125s user + 0.046875s system = 1.500000s CPU (163.2%)

PHY-1001 : Congestion index: top1 = 86.75, top5 = 63.64, top10 = 52.97, top15 = 46.89.
PHY-3001 : End congestion estimation;  1.194626s wall, 1.718750s user + 0.046875s system = 1.765625s CPU (147.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21496 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.824268s wall, 0.796875s user + 0.031250s system = 0.828125s CPU (100.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000102889
PHY-3002 : Step(154): len = 488059, overlap = 381.656
PHY-3002 : Step(155): len = 490565, overlap = 308.938
PHY-3002 : Step(156): len = 486293, overlap = 278.469
PHY-3002 : Step(157): len = 480841, overlap = 260.219
PHY-3002 : Step(158): len = 477009, overlap = 238.812
PHY-3002 : Step(159): len = 473003, overlap = 240.906
PHY-3002 : Step(160): len = 468963, overlap = 237.844
PHY-3002 : Step(161): len = 466441, overlap = 227.031
PHY-3002 : Step(162): len = 464282, overlap = 225.562
PHY-3002 : Step(163): len = 463481, overlap = 224.812
PHY-3002 : Step(164): len = 460601, overlap = 223.812
PHY-3002 : Step(165): len = 458842, overlap = 229.812
PHY-3002 : Step(166): len = 456647, overlap = 229.312
PHY-3002 : Step(167): len = 454027, overlap = 231.562
PHY-3002 : Step(168): len = 452985, overlap = 240.812
PHY-3002 : Step(169): len = 450343, overlap = 242.781
PHY-3002 : Step(170): len = 447970, overlap = 241.938
PHY-3002 : Step(171): len = 445839, overlap = 240
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000205778
PHY-3002 : Step(172): len = 445759, overlap = 230.156
PHY-3002 : Step(173): len = 447642, overlap = 229.5
PHY-3002 : Step(174): len = 448803, overlap = 222.188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000411555
PHY-3002 : Step(175): len = 451229, overlap = 218.5
PHY-3002 : Step(176): len = 457049, overlap = 212.188
PHY-3002 : Step(177): len = 464769, overlap = 190
PHY-3002 : Step(178): len = 466239, overlap = 173.969
PHY-3002 : Step(179): len = 464995, overlap = 174.219
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000823111
PHY-3002 : Step(180): len = 465490, overlap = 174.219
PHY-3002 : Step(181): len = 471387, overlap = 168.688
PHY-3002 : Step(182): len = 477054, overlap = 156.438
PHY-3002 : Step(183): len = 478245, overlap = 152.75
PHY-3002 : Step(184): len = 477584, overlap = 156.812
PHY-3002 : Step(185): len = 477152, overlap = 152.562
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.0013562
PHY-3002 : Step(186): len = 478082, overlap = 153.188
PHY-3002 : Step(187): len = 480654, overlap = 143.219
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 78957, tnet num: 21496, tinst num: 19052, tnode num: 111151, tedge num: 124046.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.536339s wall, 1.484375s user + 0.046875s system = 1.531250s CPU (99.7%)

RUN-1004 : used memory is 559 MB, reserved memory is 534 MB, peak memory is 689 MB
OPT-1001 : Total overflow 490.84 peak overflow 3.94
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 234/21498.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 553976, over cnt = 2475(7%), over = 8694, worst = 24
PHY-1001 : End global iterations;  1.312777s wall, 2.140625s user + 0.015625s system = 2.156250s CPU (164.3%)

PHY-1001 : Congestion index: top1 = 59.42, top5 = 48.46, top10 = 43.50, top15 = 40.37.
PHY-1001 : End incremental global routing;  1.535868s wall, 2.359375s user + 0.015625s system = 2.375000s CPU (154.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21496 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.887422s wall, 0.859375s user + 0.015625s system = 0.875000s CPU (98.6%)

OPT-1001 : 15 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 56 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 18978 has valid locations, 224 needs to be replaced
PHY-3001 : design contains 19261 instances, 5597 luts, 12150 seqs, 1431 slices, 283 macros(1431 instances: 937 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 496098
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17255/21707.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 567320, over cnt = 2482(7%), over = 8767, worst = 24
PHY-1001 : End global iterations;  0.178676s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (131.2%)

PHY-1001 : Congestion index: top1 = 59.74, top5 = 48.94, top10 = 43.87, top15 = 40.73.
PHY-3001 : End congestion estimation;  0.384595s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (113.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 79640, tnet num: 21705, tinst num: 19261, tnode num: 112094, tedge num: 124994.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.314635s wall, 1.296875s user + 0.015625s system = 1.312500s CPU (99.8%)

RUN-1004 : used memory is 601 MB, reserved memory is 597 MB, peak memory is 691 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21705 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.187645s wall, 2.156250s user + 0.031250s system = 2.187500s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(188): len = 496646, overlap = 0.0625
PHY-3002 : Step(189): len = 498217, overlap = 0.125
PHY-3002 : Step(190): len = 498905, overlap = 0.1875
PHY-3002 : Step(191): len = 499889, overlap = 0.125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17272/21707.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 565872, over cnt = 2515(7%), over = 8865, worst = 24
PHY-1001 : End global iterations;  0.169356s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (147.6%)

PHY-1001 : Congestion index: top1 = 60.02, top5 = 49.07, top10 = 44.17, top15 = 40.96.
PHY-3001 : End congestion estimation;  0.377825s wall, 0.437500s user + 0.015625s system = 0.453125s CPU (119.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21705 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.848290s wall, 0.828125s user + 0.015625s system = 0.843750s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000771463
PHY-3002 : Step(192): len = 499632, overlap = 145.5
PHY-3002 : Step(193): len = 499490, overlap = 145.062
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00154293
PHY-3002 : Step(194): len = 499647, overlap = 144.969
PHY-3002 : Step(195): len = 500051, overlap = 145.312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00308585
PHY-3002 : Step(196): len = 499977, overlap = 145.062
PHY-3002 : Step(197): len = 500056, overlap = 144.875
PHY-3001 : Final: Len = 500056, Over = 144.875
PHY-3001 : End incremental placement;  4.674912s wall, 4.843750s user + 0.312500s system = 5.156250s CPU (110.3%)

OPT-1001 : Total overflow 494.22 peak overflow 3.94
OPT-1001 : End high-fanout net optimization;  7.524863s wall, 8.656250s user + 0.343750s system = 9.000000s CPU (119.6%)

OPT-1001 : Current memory(MB): used = 694, reserve = 675, peak = 710.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17276/21707.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 568528, over cnt = 2473(7%), over = 8312, worst = 24
PHY-1002 : len = 610816, over cnt = 1727(4%), over = 4236, worst = 20
PHY-1002 : len = 645896, over cnt = 728(2%), over = 1627, worst = 16
PHY-1002 : len = 666512, over cnt = 172(0%), over = 336, worst = 11
PHY-1002 : len = 673080, over cnt = 2(0%), over = 13, worst = 8
PHY-1001 : End global iterations;  1.069863s wall, 1.625000s user + 0.015625s system = 1.640625s CPU (153.3%)

PHY-1001 : Congestion index: top1 = 50.45, top5 = 44.29, top10 = 41.29, top15 = 39.23.
OPT-1001 : End congestion update;  1.291197s wall, 1.843750s user + 0.015625s system = 1.859375s CPU (144.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 21705 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.749080s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (100.1%)

OPT-0007 : Start: WNS 4367 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.045331s wall, 2.593750s user + 0.015625s system = 2.609375s CPU (127.6%)

OPT-1001 : Current memory(MB): used = 669, reserve = 651, peak = 710.
OPT-1001 : End physical optimization;  11.393887s wall, 13.156250s user + 0.421875s system = 13.578125s CPU (119.2%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5597 LUT to BLE ...
SYN-4008 : Packed 5597 LUT and 2676 SEQ to BLE.
SYN-4003 : Packing 9474 remaining SEQ's ...
SYN-4005 : Packed 3312 SEQ with LUT/SLICE
SYN-4006 : 124 single LUT's are left
SYN-4006 : 6162 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11759/13539 primitive instances ...
PHY-3001 : End packing;  2.489599s wall, 2.484375s user + 0.000000s system = 2.484375s CPU (99.8%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 7987 instances
RUN-1001 : 3951 mslices, 3951 lslices, 56 pads, 24 brams, 0 dsps
RUN-1001 : There are total 19082 nets
RUN-1001 : 13411 nets have 2 pins
RUN-1001 : 4284 nets have [3 - 5] pins
RUN-1001 : 883 nets have [6 - 10] pins
RUN-1001 : 358 nets have [11 - 20] pins
RUN-1001 : 137 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 7985 instances, 7902 slices, 283 macros(1431 instances: 937 mslices 494 lslices)
PHY-3001 : Cell area utilization is 84%
PHY-3001 : After packing: Len = 515707, Over = 357.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7688/19082.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 639488, over cnt = 1482(4%), over = 2249, worst = 9
PHY-1002 : len = 643672, over cnt = 976(2%), over = 1344, worst = 9
PHY-1002 : len = 654824, over cnt = 336(0%), over = 422, worst = 5
PHY-1002 : len = 660000, over cnt = 90(0%), over = 119, worst = 4
PHY-1002 : len = 662160, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.060950s wall, 1.656250s user + 0.015625s system = 1.671875s CPU (157.6%)

PHY-1001 : Congestion index: top1 = 50.56, top5 = 43.78, top10 = 40.57, top15 = 38.44.
PHY-3001 : End congestion estimation;  1.332448s wall, 1.921875s user + 0.015625s system = 1.937500s CPU (145.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66155, tnet num: 19080, tinst num: 7985, tnode num: 89857, tedge num: 108985.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.509254s wall, 1.484375s user + 0.015625s system = 1.500000s CPU (99.4%)

RUN-1004 : used memory is 594 MB, reserved memory is 581 MB, peak memory is 710 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19080 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.283805s wall, 2.234375s user + 0.046875s system = 2.281250s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.63733e-05
PHY-3002 : Step(198): len = 519819, overlap = 350.25
PHY-3002 : Step(199): len = 519857, overlap = 360
PHY-3002 : Step(200): len = 520102, overlap = 370.25
PHY-3002 : Step(201): len = 521087, overlap = 373
PHY-3002 : Step(202): len = 520456, overlap = 374.5
PHY-3002 : Step(203): len = 520157, overlap = 379
PHY-3002 : Step(204): len = 517515, overlap = 381.5
PHY-3002 : Step(205): len = 515855, overlap = 380.5
PHY-3002 : Step(206): len = 513751, overlap = 382.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000112747
PHY-3002 : Step(207): len = 518359, overlap = 375.75
PHY-3002 : Step(208): len = 523198, overlap = 369.25
PHY-3002 : Step(209): len = 524496, overlap = 363.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000225493
PHY-3002 : Step(210): len = 530604, overlap = 353.25
PHY-3002 : Step(211): len = 539933, overlap = 327.5
PHY-3002 : Step(212): len = 539623, overlap = 321.75
PHY-3002 : Step(213): len = 538207, overlap = 314
PHY-3002 : Step(214): len = 537702, overlap = 311.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.608538s wall, 0.640625s user + 0.671875s system = 1.312500s CPU (215.7%)

PHY-3001 : Trial Legalized: Len = 633459
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 736/19082.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 714192, over cnt = 2294(6%), over = 3671, worst = 7
PHY-1002 : len = 729104, over cnt = 1273(3%), over = 1711, worst = 7
PHY-1002 : len = 743400, over cnt = 482(1%), over = 624, worst = 7
PHY-1002 : len = 749472, over cnt = 213(0%), over = 272, worst = 5
PHY-1002 : len = 754064, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.678802s wall, 3.000000s user + 0.015625s system = 3.015625s CPU (179.6%)

PHY-1001 : Congestion index: top1 = 49.40, top5 = 44.68, top10 = 41.99, top15 = 40.19.
PHY-3001 : End congestion estimation;  1.985000s wall, 3.281250s user + 0.031250s system = 3.312500s CPU (166.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19080 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.772166s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (101.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000185072
PHY-3002 : Step(215): len = 592913, overlap = 78.5
PHY-3002 : Step(216): len = 577631, overlap = 113.5
PHY-3002 : Step(217): len = 566824, overlap = 153
PHY-3002 : Step(218): len = 559459, overlap = 199.25
PHY-3002 : Step(219): len = 556041, overlap = 225.5
PHY-3002 : Step(220): len = 553753, overlap = 237.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000370143
PHY-3002 : Step(221): len = 558344, overlap = 231
PHY-3002 : Step(222): len = 563139, overlap = 222
PHY-3002 : Step(223): len = 562428, overlap = 224
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(224): len = 565317, overlap = 218.5
PHY-3002 : Step(225): len = 570951, overlap = 217.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.028282s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (110.5%)

PHY-3001 : Legalized: Len = 609015, Over = 0
PHY-3001 : Spreading special nets. 34 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.069858s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (89.5%)

PHY-3001 : 53 instances has been re-located, deltaX = 23, deltaY = 24, maxDist = 2.
PHY-3001 : Final: Len = 609663, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66155, tnet num: 19080, tinst num: 7985, tnode num: 89857, tedge num: 108985.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.718706s wall, 1.718750s user + 0.000000s system = 1.718750s CPU (100.0%)

RUN-1004 : used memory is 598 MB, reserved memory is 596 MB, peak memory is 710 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 4006/19082.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 699088, over cnt = 2081(5%), over = 3289, worst = 6
PHY-1002 : len = 710248, over cnt = 1235(3%), over = 1692, worst = 6
PHY-1002 : len = 726648, over cnt = 339(0%), over = 412, worst = 5
PHY-1002 : len = 731408, over cnt = 123(0%), over = 141, worst = 4
PHY-1002 : len = 733944, over cnt = 1(0%), over = 1, worst = 1
PHY-1001 : End global iterations;  1.370793s wall, 2.343750s user + 0.015625s system = 2.359375s CPU (172.1%)

PHY-1001 : Congestion index: top1 = 46.81, top5 = 42.56, top10 = 40.17, top15 = 38.70.
PHY-1001 : End incremental global routing;  1.633790s wall, 2.593750s user + 0.015625s system = 2.609375s CPU (159.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19080 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.779067s wall, 0.750000s user + 0.031250s system = 0.781250s CPU (100.3%)

OPT-1001 : 3 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 56 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 7923 has valid locations, 17 needs to be replaced
PHY-3001 : design contains 7999 instances, 7916 slices, 283 macros(1431 instances: 937 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 611450
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17275/19095.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 735768, over cnt = 33(0%), over = 43, worst = 5
PHY-1002 : len = 735808, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 735960, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.362911s wall, 0.406250s user + 0.015625s system = 0.421875s CPU (116.2%)

PHY-1001 : Congestion index: top1 = 46.72, top5 = 42.61, top10 = 40.24, top15 = 38.79.
PHY-3001 : End congestion estimation;  0.622975s wall, 0.671875s user + 0.015625s system = 0.687500s CPU (110.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66263, tnet num: 19093, tinst num: 7999, tnode num: 89989, tedge num: 109126.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.733231s wall, 1.734375s user + 0.000000s system = 1.734375s CPU (100.1%)

RUN-1004 : used memory is 634 MB, reserved memory is 624 MB, peak memory is 710 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19093 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.517639s wall, 2.515625s user + 0.000000s system = 2.515625s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(226): len = 611137, overlap = 0
PHY-3002 : Step(227): len = 611144, overlap = 0
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17273/19095.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 735464, over cnt = 20(0%), over = 21, worst = 2
PHY-1002 : len = 735512, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 735544, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 735544, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.500715s wall, 0.484375s user + 0.000000s system = 0.484375s CPU (96.7%)

PHY-1001 : Congestion index: top1 = 46.85, top5 = 42.63, top10 = 40.24, top15 = 38.78.
PHY-3001 : End congestion estimation;  0.756670s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (99.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19093 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.779281s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00120086
PHY-3002 : Step(228): len = 611132, overlap = 2
PHY-3002 : Step(229): len = 611132, overlap = 2.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006135s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (254.7%)

PHY-3001 : Legalized: Len = 611265, Over = 0
PHY-3001 : End spreading;  0.065267s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (95.8%)

PHY-3001 : Final: Len = 611265, Over = 0
PHY-3001 : End incremental placement;  5.238447s wall, 5.218750s user + 0.062500s system = 5.281250s CPU (100.8%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  8.054110s wall, 8.953125s user + 0.109375s system = 9.062500s CPU (112.5%)

OPT-1001 : Current memory(MB): used = 704, reserve = 690, peak = 710.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17274/19095.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 735400, over cnt = 25(0%), over = 28, worst = 3
PHY-1002 : len = 735480, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 735480, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 735488, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.486842s wall, 0.484375s user + 0.031250s system = 0.515625s CPU (105.9%)

PHY-1001 : Congestion index: top1 = 47.00, top5 = 42.71, top10 = 40.27, top15 = 38.80.
OPT-1001 : End congestion update;  0.742817s wall, 0.734375s user + 0.046875s system = 0.781250s CPU (105.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19093 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.659599s wall, 0.656250s user + 0.000000s system = 0.656250s CPU (99.5%)

OPT-0007 : Start: WNS 4302 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.406503s wall, 1.390625s user + 0.046875s system = 1.437500s CPU (102.2%)

OPT-1001 : Current memory(MB): used = 704, reserve = 690, peak = 710.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19093 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.668839s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (100.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17290/19095.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 735488, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.104018s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (90.1%)

PHY-1001 : Congestion index: top1 = 47.00, top5 = 42.71, top10 = 40.27, top15 = 38.80.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19093 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.661243s wall, 0.656250s user + 0.000000s system = 0.656250s CPU (99.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4302 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 46.655172
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4302ps with logic level 5 
RUN-1001 :       #2 path slack 4318ps with logic level 5 
RUN-1001 :       #3 path slack 4319ps with logic level 5 
RUN-1001 :       #4 path slack 4321ps with logic level 5 
RUN-1001 :       #5 path slack 4342ps with logic level 5 
RUN-1001 :       #6 path slack 4376ps with logic level 5 
RUN-1001 :       #7 path slack 4402ps with logic level 5 
RUN-1001 :       #8 path slack 4402ps with logic level 5 
OPT-1001 : End physical optimization;  13.101214s wall, 14.109375s user + 0.156250s system = 14.265625s CPU (108.9%)

RUN-1003 : finish command "place" in  64.143291s wall, 135.843750s user + 8.296875s system = 144.140625s CPU (224.7%)

RUN-1004 : used memory is 585 MB, reserved memory is 571 MB, peak memory is 710 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.487962s wall, 2.500000s user + 0.031250s system = 2.531250s CPU (170.1%)

RUN-1004 : used memory is 586 MB, reserved memory is 571 MB, peak memory is 710 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8001 instances
RUN-1001 : 3953 mslices, 3963 lslices, 56 pads, 24 brams, 0 dsps
RUN-1001 : There are total 19095 nets
RUN-1001 : 13409 nets have 2 pins
RUN-1001 : 4286 nets have [3 - 5] pins
RUN-1001 : 889 nets have [6 - 10] pins
RUN-1001 : 361 nets have [11 - 20] pins
RUN-1001 : 141 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66263, tnet num: 19093, tinst num: 7999, tnode num: 89989, tedge num: 109126.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.504907s wall, 1.500000s user + 0.015625s system = 1.515625s CPU (100.7%)

RUN-1004 : used memory is 579 MB, reserved memory is 562 MB, peak memory is 710 MB
PHY-1001 : 3953 mslices, 3963 lslices, 56 pads, 24 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19093 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 680248, over cnt = 2208(6%), over = 3655, worst = 7
PHY-1002 : len = 693880, over cnt = 1402(3%), over = 2067, worst = 7
PHY-1002 : len = 712856, over cnt = 505(1%), over = 687, worst = 5
PHY-1002 : len = 724376, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 724784, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.415559s wall, 2.515625s user + 0.015625s system = 2.531250s CPU (178.8%)

PHY-1001 : Congestion index: top1 = 47.24, top5 = 42.58, top10 = 40.08, top15 = 38.48.
PHY-1001 : End global routing;  1.703381s wall, 2.796875s user + 0.015625s system = 2.812500s CPU (165.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 689, reserve = 682, peak = 710.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 958, reserve = 949, peak = 958.
PHY-1001 : End build detailed router design. 4.106822s wall, 4.062500s user + 0.046875s system = 4.109375s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 192832, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.743867s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (100.8%)

PHY-1001 : Current memory(MB): used = 993, reserve = 984, peak = 993.
PHY-1001 : End phase 1; 0.750534s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (99.9%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.65591e+06, over cnt = 1302(0%), over = 1306, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1008, reserve = 999, peak = 1008.
PHY-1001 : End initial routed; 12.941831s wall, 42.234375s user + 0.359375s system = 42.593750s CPU (329.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17889(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.385   |   0.000   |   0   
RUN-1001 :   Hold   |   0.130   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 2.928265s wall, 2.937500s user + 0.000000s system = 2.937500s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 1022, reserve = 1015, peak = 1022.
PHY-1001 : End phase 2; 15.870236s wall, 45.171875s user + 0.359375s system = 45.531250s CPU (286.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.65591e+06, over cnt = 1302(0%), over = 1306, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.203774s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (99.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.64306e+06, over cnt = 394(0%), over = 394, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.622769s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (150.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.64355e+06, over cnt = 73(0%), over = 73, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.343074s wall, 0.375000s user + 0.031250s system = 0.406250s CPU (118.4%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.64433e+06, over cnt = 27(0%), over = 27, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.185423s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (109.5%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.64465e+06, over cnt = 8(0%), over = 8, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.146376s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (96.1%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.64481e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.132052s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (106.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17889(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.385   |   0.000   |   0   
RUN-1001 :   Hold   |   0.130   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 2.939084s wall, 2.937500s user + 0.000000s system = 2.937500s CPU (99.9%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 292 feed throughs used by 257 nets
PHY-1001 : End commit to database; 1.957810s wall, 1.953125s user + 0.000000s system = 1.953125s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 1105, reserve = 1099, peak = 1105.
PHY-1001 : End phase 3; 6.985882s wall, 7.359375s user + 0.031250s system = 7.390625s CPU (105.8%)

PHY-1003 : Routed, final wirelength = 1.64481e+06
PHY-1001 : Current memory(MB): used = 1109, reserve = 1103, peak = 1109.
PHY-1001 : End export database. 0.052462s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.3%)

PHY-1001 : End detail routing;  28.126067s wall, 57.750000s user + 0.437500s system = 58.187500s CPU (206.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66263, tnet num: 19093, tinst num: 7999, tnode num: 89989, tedge num: 109126.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.467573s wall, 1.468750s user + 0.000000s system = 1.468750s CPU (100.1%)

RUN-1004 : used memory is 1041 MB, reserved memory is 1038 MB, peak memory is 1109 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  35.024263s wall, 65.718750s user + 0.484375s system = 66.203125s CPU (189.0%)

RUN-1004 : used memory is 1040 MB, reserved memory is 1037 MB, peak memory is 1109 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8629   out of  19600   44.03%
#reg                    12253   out of  19600   62.52%
#le                     14741
  #lut only              2488   out of  14741   16.88%
  #reg only              6112   out of  14741   41.46%
  #lut&reg               6141   out of  14741   41.66%
#dsp                        0   out of     29    0.00%
#bram                      24   out of     64   37.50%
  #bram9k                  22
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       56   out of    188   29.79%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6793
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          105
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT        E11        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT        A13        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT        F10        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         E8        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         E7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT        A10        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C6        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         D6        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         C7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT        E12        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         F1        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         A7        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H13        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J11        LVCMOS33          N/A          PULLUP          IREG       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT        E13        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         R7        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         M7        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         P7        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         T8        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         P8        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         N8        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M9        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         T9        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         R9        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         T6        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         P9        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         N9        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT        P11        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         T5        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         B2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        L10        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        E15        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        R14        LVCMOS33           8            N/A            OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT         R2        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT        N12        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        B10        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         J6        LVCMOS33           8            N/A            OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        R15        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E6        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |14741  |7198    |1431    |12297   |24      |0       |
|  AnyFog_dataX                      |AnyFog          |212    |86      |22      |176     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |90     |61      |22      |54      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |198    |72      |22      |162     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |53      |22      |50      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |206    |111     |22      |171     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |84     |55      |22      |49      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2903   |725     |39      |2819    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |61     |37      |5       |52      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |208    |54      |5       |196     |0       |0       |
|    STADOP_com2                     |STADOP          |541    |83      |0       |537     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |40      |14      |42      |0       |0       |
|    head_com2                       |uniheading      |282    |89      |5       |266     |0       |0       |
|    rmc_com2                        |Gprmc           |34     |34      |0       |29      |0       |0       |
|    uart_com2                       |Agrica          |1396   |368     |10      |1381    |0       |0       |
|  COM3                              |COM3_Control    |275    |142     |19      |237     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |62     |34      |5       |53      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |60     |36      |14      |37      |0       |0       |
|    rmc_com3                        |Gprmc           |153    |72      |0       |147     |0       |0       |
|  DATA                              |Data_Processing |8695   |4348    |1065    |7010    |0       |0       |
|    DIV_Dtemp                       |Divider         |800    |287     |84      |674     |0       |0       |
|    DIV_Utemp                       |Divider         |635    |326     |84      |507     |0       |0       |
|    DIV_accX                        |Divider         |624    |301     |84      |498     |0       |0       |
|    DIV_accY                        |Divider         |624    |374     |114     |450     |0       |0       |
|    DIV_accZ                        |Divider         |650    |381     |132     |445     |0       |0       |
|    DIV_rateX                       |Divider         |687    |346     |132     |482     |0       |0       |
|    DIV_rateY                       |Divider         |540    |331     |132     |336     |0       |0       |
|    DIV_rateZ                       |Divider         |601    |411     |132     |397     |0       |0       |
|    genclk                          |genclk          |90     |61      |20      |57      |0       |0       |
|  FMC                               |FMC_Ctrl        |502    |439     |43      |362     |0       |0       |
|  IIC                               |I2C_master      |310    |240     |11      |263     |0       |0       |
|  IMU_CTRL                          |SCHA634         |891    |690     |61      |723     |0       |0       |
|    CtrlData                        |CtrlData        |468    |412     |47      |337     |0       |0       |
|      usms                          |Time_1ms        |29     |24      |5       |20      |0       |0       |
|    SPIM                            |SPI_SCHA634     |423    |278     |14      |386     |0       |0       |
|  POWER                             |POWER_EN        |98     |57      |38      |39      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |447    |288     |89      |287     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |447    |288     |89      |287     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |172    |118     |0       |162     |0       |0       |
|        reg_inst                    |register        |170    |117     |0       |160     |0       |0       |
|        tap_inst                    |tap             |2      |1       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |275    |170     |89      |125     |0       |0       |
|        bus_inst                    |bus_top         |76     |45      |28      |28      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |26     |13      |10      |10      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |50     |32      |18      |18      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |120    |85      |29      |72      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13352  
    #2          2       3386   
    #3          3        633   
    #4          4        267   
    #5        5-10       946   
    #6        11-50      437   
    #7       51-100       8    
    #8       101-500      3    
    #9        >500        2    
  Average     2.11             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  1.796690s wall, 3.046875s user + 0.031250s system = 3.078125s CPU (171.3%)

RUN-1004 : used memory is 1040 MB, reserved memory is 1038 MB, peak memory is 1109 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66263, tnet num: 19093, tinst num: 7999, tnode num: 89989, tedge num: 109126.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.483047s wall, 1.484375s user + 0.000000s system = 1.484375s CPU (100.1%)

RUN-1004 : used memory is 1042 MB, reserved memory is 1040 MB, peak memory is 1109 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19093 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.162052s wall, 1.156250s user + 0.000000s system = 1.156250s CPU (99.5%)

RUN-1004 : used memory is 1048 MB, reserved memory is 1045 MB, peak memory is 1109 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 057bf4ae9dc0d18f7126c20ce7aec8c31cdd0d41110c6b742e6e5620711a0ef6 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 7999
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19095, pip num: 143337
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 292
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3236 valid insts, and 401572 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101000001010010110001101
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  9.631180s wall, 98.562500s user + 0.171875s system = 98.734375s CPU (1025.2%)

RUN-1004 : used memory is 1170 MB, reserved memory is 1155 MB, peak memory is 1284 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250716_082603.log"

============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Fri Aug  1 13:51:35 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.153736s wall, 1.546875s user + 3.593750s system = 5.140625s CPU (99.7%)

RUN-1004 : used memory is 78 MB, reserved memory is 40 MB, peak memory is 89 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.691968s wall, 1.625000s user + 0.062500s system = 1.687500s CPU (99.7%)

RUN-1004 : used memory is 299 MB, reserved memory is 268 MB, peak memory is 302 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 2 view nodes, 24 trigger nets, 24 data nets.
KIT-1004 : Chipwatcher code = 1010010110001101
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=78) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=78) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=78)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=78)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22225/12 useful/useless nets, 19240/4 useful/useless insts
SYN-1016 : Merged 17 instances.
SYN-1032 : 22001/16 useful/useless nets, 19564/12 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 2 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 2 mux instances.
SYN-1015 : Optimize round 1, 279 better
SYN-1014 : Optimize round 2
SYN-1032 : 21792/30 useful/useless nets, 19355/32 useful/useless insts
SYN-1015 : Optimize round 2, 64 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  1.940495s wall, 1.937500s user + 0.015625s system = 1.953125s CPU (100.7%)

RUN-1004 : used memory is 323 MB, reserved memory is 291 MB, peak memory is 325 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 21816/155 useful/useless nets, 19400/29 useful/useless insts
SYN-1016 : Merged 21 instances.
SYN-2571 : Optimize after map_dsp, round 1, 205 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 24 instances.
SYN-2501 : Optimize round 1, 50 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 14 macro adder
SYN-1019 : Optimized 9 mux instances.
SYN-1016 : Merged 10 instances.
SYN-1032 : 22187/5 useful/useless nets, 19771/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80441, tnet num: 22187, tinst num: 19770, tnode num: 113051, tedge num: 125682.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.095337s wall, 1.078125s user + 0.031250s system = 1.109375s CPU (101.3%)

RUN-1004 : used memory is 459 MB, reserved memory is 428 MB, peak memory is 459 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22187 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 173 (3.57), #lev = 7 (1.79)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 173 (3.57), #lev = 7 (1.79)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 387 instances into 173 LUTs, name keeping = 77%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 290 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 108 adder to BLE ...
SYN-4008 : Packed 108 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  3.824740s wall, 3.750000s user + 0.078125s system = 3.828125s CPU (100.1%)

RUN-1004 : used memory is 344 MB, reserved memory is 323 MB, peak memory is 567 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  6.045897s wall, 5.953125s user + 0.109375s system = 6.062500s CPU (100.3%)

RUN-1004 : used memory is 344 MB, reserved memory is 324 MB, peak memory is 567 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (177 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19079 instances
RUN-0007 : 5518 luts, 12041 seqs, 937 mslices, 494 lslices, 60 pads, 24 brams, 0 dsps
RUN-1001 : There are total 21523 nets
RUN-1001 : 16218 nets have 2 pins
RUN-1001 : 4138 nets have [3 - 5] pins
RUN-1001 : 818 nets have [6 - 10] pins
RUN-1001 : 225 nets have [11 - 20] pins
RUN-1001 : 106 nets have [21 - 99] pins
RUN-1001 : 18 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4792     
RUN-1001 :   No   |  No   |  Yes  |     631     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     349     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19077 instances, 5518 luts, 12041 seqs, 1431 slices, 283 macros(1431 instances: 937 mslices 494 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 61%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 79004, tnet num: 21521, tinst num: 19077, tnode num: 111208, tedge num: 124086.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.162019s wall, 1.093750s user + 0.046875s system = 1.140625s CPU (98.2%)

RUN-1004 : used memory is 519 MB, reserved memory is 492 MB, peak memory is 567 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21521 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.933329s wall, 1.843750s user + 0.078125s system = 1.921875s CPU (99.4%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.56783e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19077.
PHY-3001 : Level 1 #clusters 2159.
PHY-3001 : End clustering;  0.126703s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (148.0%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 61%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 858321, overlap = 563.469
PHY-3002 : Step(2): len = 774436, overlap = 612.938
PHY-3002 : Step(3): len = 489729, overlap = 797.344
PHY-3002 : Step(4): len = 433019, overlap = 872.875
PHY-3002 : Step(5): len = 347726, overlap = 996.031
PHY-3002 : Step(6): len = 309919, overlap = 1045.25
PHY-3002 : Step(7): len = 260679, overlap = 1111.94
PHY-3002 : Step(8): len = 228860, overlap = 1165.66
PHY-3002 : Step(9): len = 204319, overlap = 1201.06
PHY-3002 : Step(10): len = 185691, overlap = 1238.72
PHY-3002 : Step(11): len = 168787, overlap = 1271.47
PHY-3002 : Step(12): len = 155718, overlap = 1327.47
PHY-3002 : Step(13): len = 145895, overlap = 1367.62
PHY-3002 : Step(14): len = 133616, overlap = 1390.97
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.39997e-06
PHY-3002 : Step(15): len = 138789, overlap = 1360.72
PHY-3002 : Step(16): len = 189686, overlap = 1226.12
PHY-3002 : Step(17): len = 201739, overlap = 1136.78
PHY-3002 : Step(18): len = 206615, overlap = 1037.75
PHY-3002 : Step(19): len = 202843, overlap = 993.75
PHY-3002 : Step(20): len = 196034, overlap = 975.812
PHY-3002 : Step(21): len = 190262, overlap = 962.969
PHY-3002 : Step(22): len = 184758, overlap = 955.656
PHY-3002 : Step(23): len = 180455, overlap = 953.219
PHY-3002 : Step(24): len = 176310, overlap = 951.469
PHY-3002 : Step(25): len = 173459, overlap = 955.062
PHY-3002 : Step(26): len = 172401, overlap = 941.594
PHY-3002 : Step(27): len = 171272, overlap = 932.406
PHY-3002 : Step(28): len = 171673, overlap = 921.125
PHY-3002 : Step(29): len = 170634, overlap = 905.469
PHY-3002 : Step(30): len = 170487, overlap = 891.625
PHY-3002 : Step(31): len = 170133, overlap = 866.562
PHY-3002 : Step(32): len = 170050, overlap = 867.031
PHY-3002 : Step(33): len = 166971, overlap = 866.969
PHY-3002 : Step(34): len = 166244, overlap = 858.312
PHY-3002 : Step(35): len = 164660, overlap = 857.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.79994e-06
PHY-3002 : Step(36): len = 172984, overlap = 854.969
PHY-3002 : Step(37): len = 186389, overlap = 811.969
PHY-3002 : Step(38): len = 189192, overlap = 779.938
PHY-3002 : Step(39): len = 190254, overlap = 767.281
PHY-3002 : Step(40): len = 189328, overlap = 770.656
PHY-3002 : Step(41): len = 188472, overlap = 765
PHY-3002 : Step(42): len = 187077, overlap = 778.375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 5.59988e-06
PHY-3002 : Step(43): len = 198432, overlap = 734.844
PHY-3002 : Step(44): len = 212025, overlap = 710.031
PHY-3002 : Step(45): len = 216608, overlap = 723.906
PHY-3002 : Step(46): len = 218221, overlap = 722.062
PHY-3002 : Step(47): len = 217810, overlap = 716.812
PHY-3002 : Step(48): len = 215988, overlap = 717.688
PHY-3002 : Step(49): len = 214701, overlap = 708.625
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 1.11998e-05
PHY-3002 : Step(50): len = 226079, overlap = 669.094
PHY-3002 : Step(51): len = 240566, overlap = 608.031
PHY-3002 : Step(52): len = 245988, overlap = 550.312
PHY-3002 : Step(53): len = 247848, overlap = 525.469
PHY-3002 : Step(54): len = 246520, overlap = 528.562
PHY-3002 : Step(55): len = 245147, overlap = 528.281
PHY-3002 : Step(56): len = 243899, overlap = 549.25
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 2.23995e-05
PHY-3002 : Step(57): len = 254497, overlap = 513.406
PHY-3002 : Step(58): len = 267347, overlap = 460.156
PHY-3002 : Step(59): len = 271296, overlap = 456.375
PHY-3002 : Step(60): len = 272661, overlap = 440.188
PHY-3002 : Step(61): len = 270272, overlap = 426.344
PHY-3002 : Step(62): len = 268163, overlap = 427.5
PHY-3002 : Step(63): len = 265630, overlap = 419.938
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 4.47991e-05
PHY-3002 : Step(64): len = 275066, overlap = 370.281
PHY-3002 : Step(65): len = 284690, overlap = 335.062
PHY-3002 : Step(66): len = 288094, overlap = 304.25
PHY-3002 : Step(67): len = 288518, overlap = 296.469
PHY-3002 : Step(68): len = 286057, overlap = 293.469
PHY-3002 : Step(69): len = 284486, overlap = 290.25
PHY-3002 : Step(70): len = 282574, overlap = 284.719
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 8.95981e-05
PHY-3002 : Step(71): len = 289491, overlap = 273.375
PHY-3002 : Step(72): len = 296485, overlap = 269.812
PHY-3002 : Step(73): len = 298724, overlap = 257.406
PHY-3002 : Step(74): len = 298992, overlap = 249.5
PHY-3002 : Step(75): len = 297760, overlap = 244.719
PHY-3002 : Step(76): len = 296250, overlap = 249.062
PHY-3002 : Step(77): len = 295442, overlap = 244.156
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000171442
PHY-3002 : Step(78): len = 299810, overlap = 235.312
PHY-3002 : Step(79): len = 305128, overlap = 210.125
PHY-3002 : Step(80): len = 307970, overlap = 196.281
PHY-3002 : Step(81): len = 310828, overlap = 183.75
PHY-3002 : Step(82): len = 310903, overlap = 186
PHY-3002 : Step(83): len = 309989, overlap = 180.719
PHY-3002 : Step(84): len = 308615, overlap = 191.75
PHY-3002 : Step(85): len = 309229, overlap = 185.281
PHY-3002 : Step(86): len = 308804, overlap = 199.156
PHY-3002 : Step(87): len = 307206, overlap = 208.312
PHY-3002 : Step(88): len = 307271, overlap = 214.594
PHY-3002 : Step(89): len = 306858, overlap = 233.625
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000342884
PHY-3002 : Step(90): len = 308803, overlap = 229.156
PHY-3002 : Step(91): len = 312337, overlap = 222.656
PHY-3002 : Step(92): len = 314242, overlap = 205.719
PHY-3002 : Step(93): len = 316245, overlap = 202.719
PHY-3002 : Step(94): len = 316258, overlap = 202.75
PHY-3002 : Step(95): len = 316142, overlap = 190.531
PHY-3002 : Step(96): len = 314812, overlap = 179.188
PHY-3002 : Step(97): len = 314320, overlap = 171.75
PHY-3002 : Step(98): len = 314755, overlap = 179.688
PHY-3002 : Step(99): len = 314410, overlap = 175.906
PHY-3002 : Step(100): len = 314334, overlap = 176.5
PHY-3002 : Step(101): len = 313400, overlap = 174.094
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(102): len = 314457, overlap = 172.75
PHY-3002 : Step(103): len = 316381, overlap = 162.969
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.013260s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (117.8%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21523.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 406976, over cnt = 1108(3%), over = 5137, worst = 35
PHY-1001 : End global iterations;  0.735058s wall, 0.953125s user + 0.015625s system = 0.968750s CPU (131.8%)

PHY-1001 : Congestion index: top1 = 74.22, top5 = 51.28, top10 = 41.87, top15 = 36.33.
PHY-3001 : End congestion estimation;  0.942198s wall, 1.156250s user + 0.015625s system = 1.171875s CPU (124.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21521 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.800911s wall, 0.781250s user + 0.015625s system = 0.796875s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.94564e-05
PHY-3002 : Step(104): len = 355252, overlap = 164.812
PHY-3002 : Step(105): len = 372423, overlap = 146.969
PHY-3002 : Step(106): len = 373360, overlap = 138.781
PHY-3002 : Step(107): len = 370683, overlap = 133.719
PHY-3002 : Step(108): len = 378049, overlap = 131.906
PHY-3002 : Step(109): len = 381026, overlap = 120
PHY-3002 : Step(110): len = 383224, overlap = 118
PHY-3002 : Step(111): len = 388985, overlap = 111.781
PHY-3002 : Step(112): len = 389602, overlap = 111.594
PHY-3002 : Step(113): len = 391124, overlap = 108.375
PHY-3002 : Step(114): len = 393779, overlap = 115.688
PHY-3002 : Step(115): len = 392796, overlap = 117.156
PHY-3002 : Step(116): len = 394787, overlap = 115.562
PHY-3002 : Step(117): len = 396711, overlap = 114.438
PHY-3002 : Step(118): len = 396320, overlap = 110.688
PHY-3002 : Step(119): len = 397476, overlap = 112.031
PHY-3002 : Step(120): len = 398166, overlap = 111.938
PHY-3002 : Step(121): len = 399777, overlap = 119.219
PHY-3002 : Step(122): len = 400487, overlap = 122.438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000198913
PHY-3002 : Step(123): len = 400040, overlap = 120.156
PHY-3002 : Step(124): len = 402272, overlap = 116.312
PHY-3002 : Step(125): len = 404559, overlap = 113.938
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000397826
PHY-3002 : Step(126): len = 410137, overlap = 103.312
PHY-3002 : Step(127): len = 418097, overlap = 104.344
PHY-3002 : Step(128): len = 424381, overlap = 101.281
PHY-3002 : Step(129): len = 427761, overlap = 97.0625
PHY-3002 : Step(130): len = 429752, overlap = 104.969
PHY-3002 : Step(131): len = 431788, overlap = 109.375
PHY-3002 : Step(132): len = 433668, overlap = 106.25
PHY-3002 : Step(133): len = 434992, overlap = 111.531
PHY-3002 : Step(134): len = 434392, overlap = 105.938
PHY-3002 : Step(135): len = 434192, overlap = 106.438
PHY-3002 : Step(136): len = 434503, overlap = 107.062
PHY-3002 : Step(137): len = 435130, overlap = 101.281
PHY-3002 : Step(138): len = 433973, overlap = 92.6875
PHY-3002 : Step(139): len = 434435, overlap = 81.0625
PHY-3002 : Step(140): len = 436633, overlap = 76.4062
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000795651
PHY-3002 : Step(141): len = 435533, overlap = 80.7812
PHY-3002 : Step(142): len = 437969, overlap = 83.5625
PHY-3002 : Step(143): len = 440659, overlap = 84.0625
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(144): len = 442051, overlap = 80.0938
PHY-3002 : Step(145): len = 448929, overlap = 81.75
PHY-3002 : Step(146): len = 454419, overlap = 76.8438
PHY-3002 : Step(147): len = 453436, overlap = 77.8125
PHY-3002 : Step(148): len = 453188, overlap = 76.375
PHY-3002 : Step(149): len = 452988, overlap = 74.0625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 50/21523.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 508544, over cnt = 2073(5%), over = 10336, worst = 37
PHY-1001 : End global iterations;  0.895051s wall, 1.546875s user + 0.046875s system = 1.593750s CPU (178.1%)

PHY-1001 : Congestion index: top1 = 79.27, top5 = 59.70, top10 = 50.47, top15 = 45.01.
PHY-3001 : End congestion estimation;  1.122733s wall, 1.781250s user + 0.046875s system = 1.828125s CPU (162.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21521 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.876308s wall, 0.859375s user + 0.015625s system = 0.875000s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000112396
PHY-3002 : Step(150): len = 461836, overlap = 374.969
PHY-3002 : Step(151): len = 466017, overlap = 314.031
PHY-3002 : Step(152): len = 459785, overlap = 290.531
PHY-3002 : Step(153): len = 454047, overlap = 275.219
PHY-3002 : Step(154): len = 449168, overlap = 256.062
PHY-3002 : Step(155): len = 445184, overlap = 246.406
PHY-3002 : Step(156): len = 440498, overlap = 233
PHY-3002 : Step(157): len = 436826, overlap = 221.625
PHY-3002 : Step(158): len = 433837, overlap = 212.906
PHY-3002 : Step(159): len = 431530, overlap = 210.219
PHY-3002 : Step(160): len = 428297, overlap = 218.219
PHY-3002 : Step(161): len = 425573, overlap = 220.875
PHY-3002 : Step(162): len = 425094, overlap = 223.219
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000224792
PHY-3002 : Step(163): len = 424401, overlap = 216.094
PHY-3002 : Step(164): len = 426156, overlap = 203.656
PHY-3002 : Step(165): len = 428218, overlap = 186.875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000449583
PHY-3002 : Step(166): len = 430208, overlap = 179.781
PHY-3002 : Step(167): len = 435921, overlap = 168.281
PHY-3002 : Step(168): len = 441274, overlap = 154.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000899166
PHY-3002 : Step(169): len = 441953, overlap = 155.469
PHY-3002 : Step(170): len = 446003, overlap = 144.75
PHY-3002 : Step(171): len = 449900, overlap = 139.969
PHY-3002 : Step(172): len = 451463, overlap = 133.031
PHY-3002 : Step(173): len = 451532, overlap = 132.5
PHY-3002 : Step(174): len = 450861, overlap = 125.562
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 79004, tnet num: 21521, tinst num: 19077, tnode num: 111208, tedge num: 124086.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.368467s wall, 1.343750s user + 0.015625s system = 1.359375s CPU (99.3%)

RUN-1004 : used memory is 558 MB, reserved memory is 532 MB, peak memory is 689 MB
OPT-1001 : Total overflow 484.38 peak overflow 3.25
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 473/21523.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 521216, over cnt = 2367(6%), over = 8133, worst = 23
PHY-1001 : End global iterations;  0.998928s wall, 1.703125s user + 0.031250s system = 1.734375s CPU (173.6%)

PHY-1001 : Congestion index: top1 = 54.05, top5 = 45.80, top10 = 41.32, top15 = 38.42.
PHY-1001 : End incremental global routing;  1.201639s wall, 1.906250s user + 0.031250s system = 1.937500s CPU (161.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21521 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.852742s wall, 0.828125s user + 0.046875s system = 0.875000s CPU (102.6%)

OPT-1001 : 14 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19000 has valid locations, 209 needs to be replaced
PHY-3001 : design contains 19272 instances, 5597 luts, 12157 seqs, 1431 slices, 283 macros(1431 instances: 937 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 464852
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16853/21718.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 532648, over cnt = 2373(6%), over = 8170, worst = 23
PHY-1001 : End global iterations;  0.164590s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (142.4%)

PHY-1001 : Congestion index: top1 = 54.18, top5 = 46.09, top10 = 41.59, top15 = 38.76.
PHY-3001 : End congestion estimation;  0.380387s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (119.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 79630, tnet num: 21716, tinst num: 19272, tnode num: 112101, tedge num: 124948.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.364683s wall, 1.312500s user + 0.031250s system = 1.343750s CPU (98.5%)

RUN-1004 : used memory is 600 MB, reserved memory is 595 MB, peak memory is 690 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21716 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.240244s wall, 2.140625s user + 0.078125s system = 2.218750s CPU (99.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(175): len = 465152, overlap = 0.125
PHY-3002 : Step(176): len = 466469, overlap = 0.125
PHY-3002 : Step(177): len = 466877, overlap = 0.25
PHY-3002 : Step(178): len = 467819, overlap = 0.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 16872/21718.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 531816, over cnt = 2391(6%), over = 8192, worst = 23
PHY-1001 : End global iterations;  0.158016s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (128.5%)

PHY-1001 : Congestion index: top1 = 54.76, top5 = 46.34, top10 = 41.72, top15 = 38.82.
PHY-3001 : End congestion estimation;  0.366418s wall, 0.390625s user + 0.015625s system = 0.406250s CPU (110.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21716 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.848716s wall, 0.812500s user + 0.031250s system = 0.843750s CPU (99.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00109898
PHY-3002 : Step(179): len = 467655, overlap = 127.25
PHY-3002 : Step(180): len = 468032, overlap = 127.25
PHY-3002 : Step(181): len = 468457, overlap = 127.5
PHY-3001 : Final: Len = 468457, Over = 127.5
PHY-3001 : End incremental placement;  4.563829s wall, 4.765625s user + 0.234375s system = 5.000000s CPU (109.6%)

OPT-1001 : Total overflow 488.72 peak overflow 3.25
OPT-1001 : End high-fanout net optimization;  7.052379s wall, 7.968750s user + 0.312500s system = 8.281250s CPU (117.4%)

OPT-1001 : Current memory(MB): used = 694, reserve = 674, peak = 709.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16882/21718.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 534760, over cnt = 2351(6%), over = 7774, worst = 23
PHY-1002 : len = 575336, over cnt = 1534(4%), over = 3726, worst = 22
PHY-1002 : len = 604312, over cnt = 750(2%), over = 1536, worst = 18
PHY-1002 : len = 627128, over cnt = 78(0%), over = 117, worst = 7
PHY-1002 : len = 629000, over cnt = 14(0%), over = 16, worst = 2
PHY-1001 : End global iterations;  0.986251s wall, 1.609375s user + 0.000000s system = 1.609375s CPU (163.2%)

PHY-1001 : Congestion index: top1 = 46.90, top5 = 41.84, top10 = 38.95, top15 = 37.00.
OPT-1001 : End congestion update;  1.195919s wall, 1.812500s user + 0.000000s system = 1.812500s CPU (151.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 21716 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.743959s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (100.8%)

OPT-0007 : Start: WNS 3669 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  1.945322s wall, 2.562500s user + 0.000000s system = 2.562500s CPU (131.7%)

OPT-1001 : Current memory(MB): used = 689, reserve = 668, peak = 709.
OPT-1001 : End physical optimization;  10.645866s wall, 12.312500s user + 0.343750s system = 12.656250s CPU (118.9%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5597 LUT to BLE ...
SYN-4008 : Packed 5597 LUT and 2681 SEQ to BLE.
SYN-4003 : Packing 9476 remaining SEQ's ...
SYN-4005 : Packed 3326 SEQ with LUT/SLICE
SYN-4006 : 108 single LUT's are left
SYN-4006 : 6150 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11747/13531 primitive instances ...
PHY-3001 : End packing;  2.473250s wall, 2.468750s user + 0.000000s system = 2.468750s CPU (99.8%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 7920 instances
RUN-1001 : 3915 mslices, 3916 lslices, 60 pads, 24 brams, 0 dsps
RUN-1001 : There are total 19091 nets
RUN-1001 : 13477 nets have 2 pins
RUN-1001 : 4240 nets have [3 - 5] pins
RUN-1001 : 876 nets have [6 - 10] pins
RUN-1001 : 371 nets have [11 - 20] pins
RUN-1001 : 118 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 7918 instances, 7831 slices, 283 macros(1431 instances: 937 mslices 494 lslices)
PHY-3001 : Cell area utilization is 84%
PHY-3001 : After packing: Len = 484206, Over = 343.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7818/19091.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 598784, over cnt = 1454(4%), over = 2323, worst = 8
PHY-1002 : len = 604528, over cnt = 906(2%), over = 1239, worst = 7
PHY-1002 : len = 613392, over cnt = 419(1%), over = 548, worst = 5
PHY-1002 : len = 619536, over cnt = 139(0%), over = 183, worst = 4
PHY-1002 : len = 622944, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.057019s wall, 1.718750s user + 0.046875s system = 1.765625s CPU (167.0%)

PHY-1001 : Congestion index: top1 = 49.59, top5 = 42.44, top10 = 38.81, top15 = 36.65.
PHY-3001 : End congestion estimation;  1.325514s wall, 1.968750s user + 0.062500s system = 2.031250s CPU (153.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 65923, tnet num: 19089, tinst num: 7918, tnode num: 89479, tedge num: 108551.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.552735s wall, 1.515625s user + 0.031250s system = 1.546875s CPU (99.6%)

RUN-1004 : used memory is 591 MB, reserved memory is 573 MB, peak memory is 709 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19089 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.345670s wall, 2.296875s user + 0.046875s system = 2.343750s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.96848e-05
PHY-3002 : Step(182): len = 486990, overlap = 327.75
PHY-3002 : Step(183): len = 485263, overlap = 333.75
PHY-3002 : Step(184): len = 485603, overlap = 337.25
PHY-3002 : Step(185): len = 486827, overlap = 352
PHY-3002 : Step(186): len = 487102, overlap = 372.75
PHY-3002 : Step(187): len = 487918, overlap = 384.25
PHY-3002 : Step(188): len = 486377, overlap = 386.75
PHY-3002 : Step(189): len = 484886, overlap = 390.25
PHY-3002 : Step(190): len = 483087, overlap = 386.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.93695e-05
PHY-3002 : Step(191): len = 487249, overlap = 375.25
PHY-3002 : Step(192): len = 490295, overlap = 368.5
PHY-3002 : Step(193): len = 489509, overlap = 367
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000198739
PHY-3002 : Step(194): len = 496803, overlap = 352
PHY-3002 : Step(195): len = 503476, overlap = 337.5
PHY-3002 : Step(196): len = 501729, overlap = 338.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00038167
PHY-3002 : Step(197): len = 505692, overlap = 330
PHY-3002 : Step(198): len = 514798, overlap = 313.5
PHY-3002 : Step(199): len = 515506, overlap = 307.5
PHY-3002 : Step(200): len = 514734, overlap = 305.75
PHY-3002 : Step(201): len = 514299, overlap = 308.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.722301s wall, 0.843750s user + 0.734375s system = 1.578125s CPU (218.5%)

PHY-3001 : Trial Legalized: Len = 614487
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 83%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 666/19091.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 692808, over cnt = 2254(6%), over = 3548, worst = 7
PHY-1002 : len = 705544, over cnt = 1329(3%), over = 1859, worst = 6
PHY-1002 : len = 718496, over cnt = 657(1%), over = 882, worst = 5
PHY-1002 : len = 727864, over cnt = 245(0%), over = 315, worst = 4
PHY-1002 : len = 732632, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.534513s wall, 2.671875s user + 0.031250s system = 2.703125s CPU (176.2%)

PHY-1001 : Congestion index: top1 = 47.09, top5 = 43.17, top10 = 40.82, top15 = 39.17.
PHY-3001 : End congestion estimation;  1.838435s wall, 2.968750s user + 0.031250s system = 3.000000s CPU (163.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19089 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.764118s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000194277
PHY-3002 : Step(202): len = 572656, overlap = 71.25
PHY-3002 : Step(203): len = 554874, overlap = 110.75
PHY-3002 : Step(204): len = 543038, overlap = 149.25
PHY-3002 : Step(205): len = 535896, overlap = 175.75
PHY-3002 : Step(206): len = 530778, overlap = 209.5
PHY-3002 : Step(207): len = 527702, overlap = 231.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000388554
PHY-3002 : Step(208): len = 530651, overlap = 224
PHY-3002 : Step(209): len = 533855, overlap = 214
PHY-3002 : Step(210): len = 533737, overlap = 214.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(211): len = 536158, overlap = 214.75
PHY-3002 : Step(212): len = 542406, overlap = 216
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.026039s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (120.0%)

PHY-3001 : Legalized: Len = 581411, Over = 0
PHY-3001 : Spreading special nets. 25 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.066699s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (93.7%)

PHY-3001 : 36 instances has been re-located, deltaX = 10, deltaY = 27, maxDist = 2.
PHY-3001 : Final: Len = 581937, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 65923, tnet num: 19089, tinst num: 7918, tnode num: 89479, tedge num: 108551.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.756696s wall, 1.734375s user + 0.015625s system = 1.750000s CPU (99.6%)

RUN-1004 : used memory is 600 MB, reserved memory is 598 MB, peak memory is 709 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 4684/19091.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 669752, over cnt = 2025(5%), over = 3185, worst = 6
PHY-1002 : len = 681984, over cnt = 1106(3%), over = 1465, worst = 6
PHY-1002 : len = 695192, over cnt = 348(0%), over = 478, worst = 4
PHY-1002 : len = 698512, over cnt = 208(0%), over = 283, worst = 4
PHY-1002 : len = 702600, over cnt = 18(0%), over = 27, worst = 3
PHY-1001 : End global iterations;  1.455820s wall, 2.265625s user + 0.031250s system = 2.296875s CPU (157.8%)

PHY-1001 : Congestion index: top1 = 47.72, top5 = 42.30, top10 = 39.65, top15 = 37.94.
PHY-1001 : End incremental global routing;  1.714051s wall, 2.515625s user + 0.031250s system = 2.546875s CPU (148.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19089 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.776737s wall, 0.750000s user + 0.031250s system = 0.781250s CPU (100.6%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 7854 has valid locations, 12 needs to be replaced
PHY-3001 : design contains 7929 instances, 7842 slices, 283 macros(1431 instances: 937 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 583365
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17157/19101.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 704184, over cnt = 25(0%), over = 34, worst = 3
PHY-1002 : len = 704352, over cnt = 10(0%), over = 11, worst = 2
PHY-1002 : len = 704424, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 704472, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.459904s wall, 0.453125s user + 0.015625s system = 0.468750s CPU (101.9%)

PHY-1001 : Congestion index: top1 = 47.82, top5 = 42.39, top10 = 39.70, top15 = 37.98.
PHY-3001 : End congestion estimation;  0.719607s wall, 0.718750s user + 0.015625s system = 0.734375s CPU (102.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 65988, tnet num: 19099, tinst num: 7929, tnode num: 89555, tedge num: 108626.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.767119s wall, 1.750000s user + 0.015625s system = 1.765625s CPU (99.9%)

RUN-1004 : used memory is 631 MB, reserved memory is 620 MB, peak memory is 709 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19099 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.566630s wall, 2.546875s user + 0.015625s system = 2.562500s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(213): len = 583365, overlap = 0
PHY-3002 : Step(214): len = 583365, overlap = 0
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17168/19101.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 704472, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.102821s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (91.2%)

PHY-1001 : Congestion index: top1 = 47.82, top5 = 42.39, top10 = 39.70, top15 = 37.98.
PHY-3001 : End congestion estimation;  0.364497s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (102.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19099 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.766585s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000869432
PHY-3002 : Step(215): len = 583332, overlap = 0.75
PHY-3002 : Step(216): len = 583357, overlap = 0.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005682s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (275.0%)

PHY-3001 : Legalized: Len = 583345, Over = 0
PHY-3001 : End spreading;  0.058458s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.9%)

PHY-3001 : Final: Len = 583345, Over = 0
PHY-3001 : End incremental placement;  4.943624s wall, 5.046875s user + 0.125000s system = 5.171875s CPU (104.6%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  7.858829s wall, 8.734375s user + 0.203125s system = 8.937500s CPU (113.7%)

OPT-1001 : Current memory(MB): used = 705, reserve = 691, peak = 710.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17154/19101.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 704184, over cnt = 10(0%), over = 13, worst = 2
PHY-1002 : len = 704176, over cnt = 8(0%), over = 9, worst = 2
PHY-1002 : len = 704272, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.361771s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (108.0%)

PHY-1001 : Congestion index: top1 = 47.80, top5 = 42.41, top10 = 39.74, top15 = 38.02.
OPT-1001 : End congestion update;  0.624818s wall, 0.656250s user + 0.000000s system = 0.656250s CPU (105.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19099 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.665924s wall, 0.656250s user + 0.000000s system = 0.656250s CPU (98.5%)

OPT-0007 : Start: WNS 3813 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.294796s wall, 1.312500s user + 0.000000s system = 1.312500s CPU (101.4%)

OPT-1001 : Current memory(MB): used = 705, reserve = 691, peak = 710.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19099 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.674438s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (101.9%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17168/19101.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 704272, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.094780s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (98.9%)

PHY-1001 : Congestion index: top1 = 47.80, top5 = 42.41, top10 = 39.74, top15 = 38.02.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19099 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.664227s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (101.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3813 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 47.344828
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3813ps with logic level 4 
RUN-1001 :       #2 path slack 3843ps with logic level 8 
OPT-1001 : End physical optimization;  12.838668s wall, 13.687500s user + 0.265625s system = 13.953125s CPU (108.7%)

RUN-1003 : finish command "place" in  65.743023s wall, 117.000000s user + 7.687500s system = 124.687500s CPU (189.7%)

RUN-1004 : used memory is 586 MB, reserved memory is 574 MB, peak memory is 710 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.476477s wall, 2.515625s user + 0.015625s system = 2.531250s CPU (171.4%)

RUN-1004 : used memory is 586 MB, reserved memory is 574 MB, peak memory is 710 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 7931 instances
RUN-1001 : 3926 mslices, 3916 lslices, 60 pads, 24 brams, 0 dsps
RUN-1001 : There are total 19101 nets
RUN-1001 : 13479 nets have 2 pins
RUN-1001 : 4238 nets have [3 - 5] pins
RUN-1001 : 884 nets have [6 - 10] pins
RUN-1001 : 372 nets have [11 - 20] pins
RUN-1001 : 119 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 65988, tnet num: 19099, tinst num: 7929, tnode num: 89555, tedge num: 108626.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.545304s wall, 1.546875s user + 0.000000s system = 1.546875s CPU (100.1%)

RUN-1004 : used memory is 582 MB, reserved memory is 568 MB, peak memory is 710 MB
PHY-1001 : 3926 mslices, 3916 lslices, 60 pads, 24 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19099 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 649424, over cnt = 2184(6%), over = 3639, worst = 7
PHY-1002 : len = 665976, over cnt = 1234(3%), over = 1698, worst = 6
PHY-1002 : len = 677344, over cnt = 589(1%), over = 811, worst = 6
PHY-1002 : len = 690424, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 690472, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.462628s wall, 2.406250s user + 0.015625s system = 2.421875s CPU (165.6%)

PHY-1001 : Congestion index: top1 = 47.37, top5 = 41.98, top10 = 39.44, top15 = 37.71.
PHY-1001 : End global routing;  1.760639s wall, 2.718750s user + 0.015625s system = 2.734375s CPU (155.3%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 688, reserve = 678, peak = 710.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 957, reserve = 946, peak = 957.
PHY-1001 : End build detailed router design. 4.250698s wall, 4.203125s user + 0.046875s system = 4.250000s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 188112, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.737126s wall, 0.718750s user + 0.015625s system = 0.734375s CPU (99.6%)

PHY-1001 : Current memory(MB): used = 993, reserve = 983, peak = 993.
PHY-1001 : End phase 1; 0.743770s wall, 0.718750s user + 0.015625s system = 0.734375s CPU (98.7%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 53% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.60642e+06, over cnt = 1228(0%), over = 1236, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1010, reserve = 999, peak = 1010.
PHY-1001 : End initial routed; 12.119871s wall, 39.281250s user + 0.312500s system = 39.593750s CPU (326.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17891(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.096   |   0.000   |   0   
RUN-1001 :   Hold   |   0.236   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.056773s wall, 3.046875s user + 0.000000s system = 3.046875s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 1022, reserve = 1011, peak = 1022.
PHY-1001 : End phase 2; 15.176797s wall, 42.328125s user + 0.312500s system = 42.640625s CPU (281.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.60642e+06, over cnt = 1228(0%), over = 1236, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.208198s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (97.6%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.59431e+06, over cnt = 388(0%), over = 389, worst = 2, crit = 0
PHY-1001 : End DR Iter 1; 0.616046s wall, 1.203125s user + 0.031250s system = 1.234375s CPU (200.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.59523e+06, over cnt = 53(0%), over = 53, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.313180s wall, 0.500000s user + 0.000000s system = 0.500000s CPU (159.7%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.5961e+06, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.218288s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (100.2%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.59626e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.138314s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (101.7%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.59626e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.131745s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (94.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17891(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.096   |   0.000   |   0   
RUN-1001 :   Hold   |   0.228   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.014193s wall, 3.015625s user + 0.000000s system = 3.015625s CPU (100.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 288 feed throughs used by 256 nets
PHY-1001 : End commit to database; 1.958236s wall, 1.937500s user + 0.015625s system = 1.953125s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 1106, reserve = 1098, peak = 1106.
PHY-1001 : End phase 3; 7.080970s wall, 7.828125s user + 0.046875s system = 7.875000s CPU (111.2%)

PHY-1003 : Routed, final wirelength = 1.59626e+06
PHY-1001 : Current memory(MB): used = 1110, reserve = 1102, peak = 1110.
PHY-1001 : End export database. 0.054464s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (114.8%)

PHY-1001 : End detail routing;  27.678367s wall, 55.500000s user + 0.421875s system = 55.921875s CPU (202.0%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 65988, tnet num: 19099, tinst num: 7929, tnode num: 89555, tedge num: 108626.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.536420s wall, 1.531250s user + 0.000000s system = 1.531250s CPU (99.7%)

RUN-1004 : used memory is 1044 MB, reserved memory is 1043 MB, peak memory is 1110 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  34.740662s wall, 63.515625s user + 0.437500s system = 63.953125s CPU (184.1%)

RUN-1004 : used memory is 1046 MB, reserved memory is 1046 MB, peak memory is 1110 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8610   out of  19600   43.93%
#reg                    12255   out of  19600   62.53%
#le                     14725
  #lut only              2470   out of  14725   16.77%
  #reg only              6115   out of  14725   41.53%
  #lut&reg               6140   out of  14725   41.70%
#dsp                        0   out of     29    0.00%
#bram                      24   out of     64   37.50%
  #bram9k                  22
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6733
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          103
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         M3        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         P6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |14725  |7179    |1431    |12299   |24      |0       |
|  AnyFog_dataX                      |AnyFog          |213    |85      |22      |172     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |91     |58      |22      |50      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |209    |72      |22      |175     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |53      |22      |52      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |203    |125     |22      |168     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |63      |22      |51      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2943   |689     |39      |2833    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |60     |36      |5       |50      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |216    |77      |5       |202     |0       |0       |
|    STADOP_com2                     |STADOP          |565    |133     |0       |548     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |63     |45      |14      |40      |0       |0       |
|    head_com2                       |uniheading      |282    |106     |5       |262     |0       |0       |
|    rmc_com2                        |Gprmc           |47     |45      |0       |37      |0       |0       |
|    uart_com2                       |Agrica          |1409   |235     |10      |1393    |0       |0       |
|  COM3                              |COM3_Control    |269    |137     |19      |233     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |63     |38      |5       |54      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |55     |33      |14      |32      |0       |0       |
|    rmc_com3                        |Gprmc           |151    |66      |0       |147     |0       |0       |
|  DATA                              |Data_Processing |8696   |4459    |1065    |6994    |0       |0       |
|    DIV_Dtemp                       |Divider         |820    |357     |84      |689     |0       |0       |
|    DIV_Utemp                       |Divider         |631    |315     |84      |499     |0       |0       |
|    DIV_accX                        |Divider         |616    |296     |84      |490     |0       |0       |
|    DIV_accY                        |Divider         |646    |342     |114     |472     |0       |0       |
|    DIV_accZ                        |Divider         |653    |385     |132     |451     |0       |0       |
|    DIV_rateX                       |Divider         |713    |374     |132     |509     |0       |0       |
|    DIV_rateY                       |Divider         |557    |336     |132     |350     |0       |0       |
|    DIV_rateZ                       |Divider         |576    |342     |132     |372     |0       |0       |
|    genclk                          |genclk          |86     |57      |20      |53      |0       |0       |
|  FMC                               |FMC_Ctrl        |425    |375     |43      |337     |0       |0       |
|  IIC                               |I2C_master      |316    |262     |11      |274     |0       |0       |
|  IMU_CTRL                          |SCHA634         |884    |640     |61      |724     |0       |0       |
|    CtrlData                        |CtrlData        |463    |410     |47      |340     |0       |0       |
|      usms                          |Time_1ms        |29     |24      |5       |19      |0       |0       |
|    SPIM                            |SPI_SCHA634     |421    |230     |14      |384     |0       |0       |
|  POWER                             |POWER_EN        |96     |49      |38      |36      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |457    |284     |89      |295     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |457    |284     |89      |295     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |172    |105     |0       |162     |0       |0       |
|        reg_inst                    |register        |170    |103     |0       |160     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |285    |179     |89      |133     |0       |0       |
|        bus_inst                    |bus_top         |75     |46      |28      |26      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |25     |15      |10      |8       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |50     |31      |18      |18      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |123    |92      |29      |74      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13418  
    #2          2       3345   
    #3          3        626   
    #4          4        267   
    #5        5-10       939   
    #6        11-50      426   
    #7       51-100      10    
    #8       101-500      3    
    #9        >500        2    
  Average     2.10             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  1.818891s wall, 3.125000s user + 0.015625s system = 3.140625s CPU (172.7%)

RUN-1004 : used memory is 1046 MB, reserved memory is 1046 MB, peak memory is 1110 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 65988, tnet num: 19099, tinst num: 7929, tnode num: 89555, tedge num: 108626.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.539352s wall, 1.546875s user + 0.000000s system = 1.546875s CPU (100.5%)

RUN-1004 : used memory is 1048 MB, reserved memory is 1048 MB, peak memory is 1110 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19099 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.205373s wall, 1.187500s user + 0.015625s system = 1.203125s CPU (99.8%)

RUN-1004 : used memory is 1055 MB, reserved memory is 1054 MB, peak memory is 1110 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 057bf4ae9dc0d18f7126c20ce7aec8c31cdd0d41110c6b742e6e5620711a0ef6 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 7929
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19101, pip num: 141033
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 288
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3253 valid insts, and 397068 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101000001010010110001101
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  10.236800s wall, 102.765625s user + 0.156250s system = 102.921875s CPU (1005.4%)

RUN-1004 : used memory is 1170 MB, reserved memory is 1155 MB, peak memory is 1284 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250801_135135.log"

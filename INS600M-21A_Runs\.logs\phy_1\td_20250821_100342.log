============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Aug 21 10:03:42 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.669437s wall, 1.656250s user + 3.984375s system = 5.640625s CPU (99.5%)

RUN-1004 : used memory is 79 MB, reserved memory is 40 MB, peak memory is 90 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.832463s wall, 1.734375s user + 0.078125s system = 1.812500s CPU (98.9%)

RUN-1004 : used memory is 302 MB, reserved memory is 271 MB, peak memory is 305 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 27 trigger nets, 27 data nets.
KIT-1004 : Chipwatcher code = 1011100110110011
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=27,BUS_CTRL_NUM=74,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=96) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=96) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=27,BUS_CTRL_NUM=74,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=27,BUS_CTRL_NUM=74,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=27,BUS_CTRL_NUM=74,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=96)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=96)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=27,BUS_CTRL_NUM=74,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=27,BUS_CTRL_NUM=74,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22588/21 useful/useless nets, 19470/13 useful/useless insts
SYN-1016 : Merged 28 instances.
SYN-1032 : 22317/22 useful/useless nets, 19822/18 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 2 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 2 mux instances.
SYN-1015 : Optimize round 1, 321 better
SYN-1014 : Optimize round 2
SYN-1032 : 22075/30 useful/useless nets, 19580/32 useful/useless insts
SYN-1015 : Optimize round 2, 64 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.536491s wall, 2.484375s user + 0.062500s system = 2.546875s CPU (100.4%)

RUN-1004 : used memory is 328 MB, reserved memory is 294 MB, peak memory is 330 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22111/221 useful/useless nets, 19639/32 useful/useless insts
SYN-1016 : Merged 33 instances.
SYN-2571 : Optimize after map_dsp, round 1, 286 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 24 instances.
SYN-2501 : Optimize round 1, 50 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 14 macro adder
SYN-1019 : Optimized 9 mux instances.
SYN-1016 : Merged 10 instances.
SYN-1032 : 22488/5 useful/useless nets, 20016/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81739, tnet num: 22488, tinst num: 20015, tnode num: 114464, tedge num: 127811.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_39.b to FMC/top_state[0]_syn_39.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_39.a to FMC/top_state[0]_syn_39.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_39.c to FMC/top_state[0]_syn_39.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_47.e to FMC/top_state[0]_syn_47.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_47.d to FMC/top_state[0]_syn_47.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_47.c to FMC/top_state[0]_syn_47.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_47.a to FMC/top_state[0]_syn_47.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_5.a to FMC/FMC_data_b[14]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_5.b to FMC/FMC_data_b[14]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_5.c to FMC/FMC_data_b[14]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_13.b to FMC/FMC_data_b[14]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_13.c to FMC/FMC_data_b[14]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_13.e to FMC/FMC_data_b[14]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_13.d to FMC/FMC_data_b[14]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_5.a to FMC/FMC_data_b[11]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_5.b to FMC/FMC_data_b[11]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_5.c to FMC/FMC_data_b[11]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_13.b to FMC/FMC_data_b[11]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_13.c to FMC/FMC_data_b[11]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_13.e to FMC/FMC_data_b[11]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_13.d to FMC/FMC_data_b[11]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[9]_syn_5.a to FMC/FMC_data_b[9]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[9]_syn_5.b to FMC/FMC_data_b[9]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[9]_syn_5.c to FMC/FMC_data_b[9]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[9]_syn_13.b to FMC/FMC_data_b[9]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[9]_syn_13.c to FMC/FMC_data_b[9]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[9]_syn_13.e to FMC/FMC_data_b[9]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[9]_syn_13.d to FMC/FMC_data_b[9]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[1]_syn_5.a to FMC/FMC_data_b[1]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[1]_syn_5.b to FMC/FMC_data_b[1]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[1]_syn_5.c to FMC/FMC_data_b[1]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[1]_syn_13.b to FMC/FMC_data_b[1]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[1]_syn_13.c to FMC/FMC_data_b[1]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[1]_syn_13.e to FMC/FMC_data_b[1]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[1]_syn_13.d to FMC/FMC_data_b[1]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[0]_syn_5.a to FMC/FMC_data_b[0]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[0]_syn_5.b to FMC/FMC_data_b[0]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[0]_syn_5.c to FMC/FMC_data_b[0]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[0]_syn_13.b to FMC/FMC_data_b[0]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[0]_syn_13.c to FMC/FMC_data_b[0]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[0]_syn_13.e to FMC/FMC_data_b[0]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[0]_syn_13.d to FMC/FMC_data_b[0]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_5.a to FMC/FMC_data_b[13]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_5.b to FMC/FMC_data_b[13]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_5.c to FMC/FMC_data_b[13]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_13.b to FMC/FMC_data_b[13]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_13.c to FMC/FMC_data_b[13]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_13.e to FMC/FMC_data_b[13]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_13.d to FMC/FMC_data_b[13]_syn_13.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_37.a to FMC/top_state[0]_syn_37.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_43.a to FMC/top_state[0]_syn_43.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_45.d to FMC/top_state[0]_syn_45.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_45.b to FMC/top_state[0]_syn_45.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_45.c to FMC/top_state[0]_syn_45.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_31.b to FMC/top_state[0]_syn_31.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_31.a to FMC/top_state[0]_syn_31.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[6]_syn_5.a to FMC/FMC_data_b[6]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[6]_syn_5.b to FMC/FMC_data_b[6]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[6]_syn_5.c to FMC/FMC_data_b[6]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[6]_syn_13.b to FMC/FMC_data_b[6]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[6]_syn_13.c to FMC/FMC_data_b[6]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[6]_syn_13.e to FMC/FMC_data_b[6]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[6]_syn_13.d to FMC/FMC_data_b[6]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_1.d to FMC/FMC_data_b[13]_syn_1.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_3.d to FMC/FMC_data_b[13]_syn_3.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_10.d to FMC/FMC_data_b[14]_syn_10.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_12.d to FMC/FMC_data_b[14]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_7.d to FMC/FMC_data_b[14]_syn_7.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_9.d to FMC/FMC_data_b[14]_syn_9.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_1.d to FMC/FMC_data_b[14]_syn_1.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_3.d to FMC/FMC_data_b[14]_syn_3.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_9.d to FMC/FMC_data_b[15]_syn_9.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_11.d to FMC/FMC_data_b[15]_syn_11.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_12.a to FMC/FMC_data_b[15]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_12.d to FMC/FMC_data_b[15]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_12.c to FMC/FMC_data_b[15]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_14.c to FMC/FMC_data_b[15]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_14.b to FMC/FMC_data_b[15]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_14.d to FMC/FMC_data_b[15]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_6.d to FMC/FMC_data_b[15]_syn_6.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_8.d to FMC/FMC_data_b[15]_syn_8.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_4.a to FMC/FMC_data_b[15]_syn_4.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_1.d to FMC/FMC_data_b[15]_syn_1.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_3.d to FMC/FMC_data_b[15]_syn_3.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_10.d to FMC/FMC_data_b[11]_syn_10.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_12.d to FMC/FMC_data_b[11]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_7.d to FMC/FMC_data_b[11]_syn_7.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_9.d to FMC/FMC_data_b[11]_syn_9.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_1.d to FMC/FMC_data_b[11]_syn_1.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_3.d to FMC/FMC_data_b[11]_syn_3.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[12]_syn_9.d to FMC/FMC_data_b[12]_syn_9.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[12]_syn_11.d to FMC/FMC_data_b[12]_syn_11.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[12]_syn_14.b to FMC/FMC_data_b[12]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[12]_syn_14.a to FMC/FMC_data_b[12]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[12]_syn_14.d to FMC/FMC_data_b[12]_syn_14.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_3.a to FMC/top_state[0]_syn_3.o.
TMR-2509 : Cut connection from FMC/top_state[1]_syn_8.c to FMC/top_state[1]_syn_8.o.
TMR-2509 : Cut connection from FMC/top_state[1]_syn_8.d to FMC/top_state[1]_syn_8.o.
TMR-2509 : Cut connection from FMC/top_state[1]_syn_8.b to FMC/top_state[1]_syn_8.o.
TMR-2509 : Cut connection from FMC/top_state[1]_syn_9.c to FMC/top_state[1]_syn_9.o.
TMR-2509 : Cut connection from FMC/top_state[1]_syn_9.b to FMC/top_state[1]_syn_9.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[12]_syn_12.d to FMC/FMC_data_b[12]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[12]_syn_12.c to FMC/FMC_data_b[12]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[10]_syn_18.d to FMC/FMC_data_b[10]_syn_18.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[10]_syn_18.b to FMC/FMC_data_b[10]_syn_18.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[10]_syn_18.a to FMC/FMC_data_b[10]_syn_18.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[10]_syn_16.d to FMC/FMC_data_b[10]_syn_16.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[10]_syn_16.c to FMC/FMC_data_b[10]_syn_16.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[8]_syn_14.d to FMC/FMC_data_b[8]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[8]_syn_14.b to FMC/FMC_data_b[8]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[8]_syn_14.a to FMC/FMC_data_b[8]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[8]_syn_12.d to FMC/FMC_data_b[8]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[8]_syn_12.c to FMC/FMC_data_b[8]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[7]_syn_14.d to FMC/FMC_data_b[7]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[7]_syn_14.b to FMC/FMC_data_b[7]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[7]_syn_14.a to FMC/FMC_data_b[7]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[7]_syn_12.d to FMC/FMC_data_b[7]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[7]_syn_12.c to FMC/FMC_data_b[7]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[5]_syn_12.d to FMC/FMC_data_b[5]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[5]_syn_12.c to FMC/FMC_data_b[5]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[5]_syn_14.b to FMC/FMC_data_b[5]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[5]_syn_14.d to FMC/FMC_data_b[5]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[4]_syn_14.d to FMC/FMC_data_b[4]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[4]_syn_14.b to FMC/FMC_data_b[4]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[4]_syn_14.a to FMC/FMC_data_b[4]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[4]_syn_12.d to FMC/FMC_data_b[4]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[4]_syn_12.c to FMC/FMC_data_b[4]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[3]_syn_14.d to FMC/FMC_data_b[3]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[3]_syn_14.b to FMC/FMC_data_b[3]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[3]_syn_14.a to FMC/FMC_data_b[3]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[3]_syn_12.d to FMC/FMC_data_b[3]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[3]_syn_12.c to FMC/FMC_data_b[3]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[2]_syn_14.d to FMC/FMC_data_b[2]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[2]_syn_14.b to FMC/FMC_data_b[2]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[2]_syn_14.a to FMC/FMC_data_b[2]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[2]_syn_12.d to FMC/FMC_data_b[2]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[2]_syn_12.c to FMC/FMC_data_b[2]_syn_12.o.
TMR-2509 : Cut connection from FMC/top_state[1]_syn_5.c to FMC/top_state[1]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[10]_syn_1.c to FMC/FMC_data_b[10]_syn_1.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[10]_syn_1.b to FMC/FMC_data_b[10]_syn_1.o.
TMR-2509 : Cut connection from FMC/FMC_data_n_syn_1.c to FMC/FMC_data_n_syn_1.o.
TMR-2509 : Cut connection from FMC/FMC_data_n_syn_1.b to FMC/FMC_data_n_syn_1.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_15.b to FMC/top_state[0]_syn_15.o.
TMR-2509 : Cut connection from FMC/top_state[2]_syn_6.c to FMC/top_state[2]_syn_6.o.
TMR-2509 : Cut connection from FMC/top_state[2]_syn_8.b to FMC/top_state[2]_syn_8.o.
TMR-2509 : Cut connection from FMC/top_state[2]_syn_8.c to FMC/top_state[2]_syn_8.o.
TMR-2509 : Cut connection from FMC/top_state[3]_syn_3.d to FMC/top_state[3]_syn_3.o.
TMR-2507 : Eliminate loop in the timing graph, delete 147 tedges.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.304535s wall, 1.281250s user + 0.015625s system = 1.296875s CPU (99.4%)

RUN-1004 : used memory is 466 MB, reserved memory is 434 MB, peak memory is 466 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22488 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 188 (3.61), #lev = 7 (1.95)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 188 (3.61), #lev = 7 (1.95)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 462 instances into 188 LUTs, name keeping = 75%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 332 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 108 adder to BLE ...
SYN-4008 : Packed 108 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.850341s wall, 4.765625s user + 0.078125s system = 4.843750s CPU (99.9%)

RUN-1004 : used memory is 349 MB, reserved memory is 326 MB, peak memory is 573 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.739780s wall, 7.593750s user + 0.140625s system = 7.734375s CPU (99.9%)

RUN-1004 : used memory is 349 MB, reserved memory is 326 MB, peak memory is 573 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (210 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4024 : Net "FMC/FMC_data_n" drives clk pins.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net FMC/FMC_data_n as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 4 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net FMC/FMC_data_n to drive 32 clock pins.
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19301 instances
RUN-0007 : 5618 luts, 12104 seqs, 973 mslices, 515 lslices, 59 pads, 26 brams, 0 dsps
RUN-1001 : There are total 21781 nets
RUN-1001 : 16405 nets have 2 pins
RUN-1001 : 4182 nets have [3 - 5] pins
RUN-1001 : 827 nets have [6 - 10] pins
RUN-1001 : 234 nets have [11 - 20] pins
RUN-1001 : 114 nets have [21 - 99] pins
RUN-1001 : 19 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4763     
RUN-1001 :   No   |  No   |  Yes  |     690     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     382     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 124
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19299 instances, 5618 luts, 12104 seqs, 1488 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80211, tnet num: 21779, tinst num: 19299, tnode num: 112644, tedge num: 126133.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_39.b to FMC/top_state[0]_syn_39.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_39.a to FMC/top_state[0]_syn_39.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_39.c to FMC/top_state[0]_syn_39.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_47.e to FMC/top_state[0]_syn_47.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_47.d to FMC/top_state[0]_syn_47.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_47.c to FMC/top_state[0]_syn_47.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_47.a to FMC/top_state[0]_syn_47.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_5.a to FMC/FMC_data_b[14]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_5.b to FMC/FMC_data_b[14]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_5.c to FMC/FMC_data_b[14]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_13.b to FMC/FMC_data_b[14]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_13.c to FMC/FMC_data_b[14]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_13.e to FMC/FMC_data_b[14]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_13.d to FMC/FMC_data_b[14]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_5.a to FMC/FMC_data_b[11]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_5.b to FMC/FMC_data_b[11]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_5.c to FMC/FMC_data_b[11]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_13.b to FMC/FMC_data_b[11]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_13.c to FMC/FMC_data_b[11]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_13.e to FMC/FMC_data_b[11]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_13.d to FMC/FMC_data_b[11]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[9]_syn_5.a to FMC/FMC_data_b[9]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[9]_syn_5.b to FMC/FMC_data_b[9]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[9]_syn_5.c to FMC/FMC_data_b[9]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[9]_syn_13.b to FMC/FMC_data_b[9]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[9]_syn_13.c to FMC/FMC_data_b[9]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[9]_syn_13.e to FMC/FMC_data_b[9]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[9]_syn_13.d to FMC/FMC_data_b[9]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[1]_syn_5.a to FMC/FMC_data_b[1]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[1]_syn_5.b to FMC/FMC_data_b[1]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[1]_syn_5.c to FMC/FMC_data_b[1]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[1]_syn_13.b to FMC/FMC_data_b[1]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[1]_syn_13.c to FMC/FMC_data_b[1]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[1]_syn_13.e to FMC/FMC_data_b[1]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[1]_syn_13.d to FMC/FMC_data_b[1]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[0]_syn_5.a to FMC/FMC_data_b[0]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[0]_syn_5.b to FMC/FMC_data_b[0]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[0]_syn_5.c to FMC/FMC_data_b[0]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[0]_syn_13.b to FMC/FMC_data_b[0]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[0]_syn_13.c to FMC/FMC_data_b[0]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[0]_syn_13.e to FMC/FMC_data_b[0]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[0]_syn_13.d to FMC/FMC_data_b[0]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_5.a to FMC/FMC_data_b[13]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_5.b to FMC/FMC_data_b[13]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_5.c to FMC/FMC_data_b[13]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_13.b to FMC/FMC_data_b[13]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_13.c to FMC/FMC_data_b[13]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_13.e to FMC/FMC_data_b[13]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_13.d to FMC/FMC_data_b[13]_syn_13.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_37.a to FMC/top_state[0]_syn_37.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_43.a to FMC/top_state[0]_syn_43.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_45.d to FMC/top_state[0]_syn_45.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_45.b to FMC/top_state[0]_syn_45.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_45.c to FMC/top_state[0]_syn_45.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_31.b to FMC/top_state[0]_syn_31.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_31.a to FMC/top_state[0]_syn_31.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[6]_syn_5.a to FMC/FMC_data_b[6]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[6]_syn_5.b to FMC/FMC_data_b[6]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[6]_syn_5.c to FMC/FMC_data_b[6]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[6]_syn_13.b to FMC/FMC_data_b[6]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[6]_syn_13.c to FMC/FMC_data_b[6]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[6]_syn_13.e to FMC/FMC_data_b[6]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[6]_syn_13.d to FMC/FMC_data_b[6]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_1.d to FMC/FMC_data_b[13]_syn_1.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_3.d to FMC/FMC_data_b[13]_syn_3.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_10.d to FMC/FMC_data_b[14]_syn_10.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_12.d to FMC/FMC_data_b[14]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_7.d to FMC/FMC_data_b[14]_syn_7.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_9.d to FMC/FMC_data_b[14]_syn_9.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_1.d to FMC/FMC_data_b[14]_syn_1.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_3.d to FMC/FMC_data_b[14]_syn_3.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_9.d to FMC/FMC_data_b[15]_syn_9.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_11.d to FMC/FMC_data_b[15]_syn_11.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_12.a to FMC/FMC_data_b[15]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_12.d to FMC/FMC_data_b[15]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_12.c to FMC/FMC_data_b[15]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_14.c to FMC/FMC_data_b[15]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_14.b to FMC/FMC_data_b[15]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_14.d to FMC/FMC_data_b[15]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_6.d to FMC/FMC_data_b[15]_syn_6.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_8.d to FMC/FMC_data_b[15]_syn_8.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_4.a to FMC/FMC_data_b[15]_syn_4.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_1.d to FMC/FMC_data_b[15]_syn_1.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_3.d to FMC/FMC_data_b[15]_syn_3.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_10.d to FMC/FMC_data_b[11]_syn_10.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_12.d to FMC/FMC_data_b[11]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_7.d to FMC/FMC_data_b[11]_syn_7.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_9.d to FMC/FMC_data_b[11]_syn_9.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_1.d to FMC/FMC_data_b[11]_syn_1.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_3.d to FMC/FMC_data_b[11]_syn_3.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[12]_syn_9.d to FMC/FMC_data_b[12]_syn_9.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[12]_syn_11.d to FMC/FMC_data_b[12]_syn_11.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[12]_syn_14.b to FMC/FMC_data_b[12]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[12]_syn_14.a to FMC/FMC_data_b[12]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[12]_syn_14.d to FMC/FMC_data_b[12]_syn_14.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_3.a to FMC/top_state[0]_syn_3.o.
TMR-2509 : Cut connection from FMC/top_state[1]_syn_8.c to FMC/top_state[1]_syn_8.o.
TMR-2509 : Cut connection from FMC/top_state[1]_syn_8.d to FMC/top_state[1]_syn_8.o.
TMR-2509 : Cut connection from FMC/top_state[1]_syn_8.b to FMC/top_state[1]_syn_8.o.
TMR-2509 : Cut connection from FMC/top_state[1]_syn_9.c to FMC/top_state[1]_syn_9.o.
TMR-2509 : Cut connection from FMC/top_state[1]_syn_9.b to FMC/top_state[1]_syn_9.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[12]_syn_12.d to FMC/FMC_data_b[12]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[12]_syn_12.c to FMC/FMC_data_b[12]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[10]_syn_18.d to FMC/FMC_data_b[10]_syn_18.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[10]_syn_18.b to FMC/FMC_data_b[10]_syn_18.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[10]_syn_18.a to FMC/FMC_data_b[10]_syn_18.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[10]_syn_16.d to FMC/FMC_data_b[10]_syn_16.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[10]_syn_16.c to FMC/FMC_data_b[10]_syn_16.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[8]_syn_14.d to FMC/FMC_data_b[8]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[8]_syn_14.b to FMC/FMC_data_b[8]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[8]_syn_14.a to FMC/FMC_data_b[8]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[8]_syn_12.d to FMC/FMC_data_b[8]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[8]_syn_12.c to FMC/FMC_data_b[8]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[7]_syn_14.d to FMC/FMC_data_b[7]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[7]_syn_14.b to FMC/FMC_data_b[7]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[7]_syn_14.a to FMC/FMC_data_b[7]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[7]_syn_12.d to FMC/FMC_data_b[7]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[7]_syn_12.c to FMC/FMC_data_b[7]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[5]_syn_12.d to FMC/FMC_data_b[5]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[5]_syn_12.c to FMC/FMC_data_b[5]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[5]_syn_14.b to FMC/FMC_data_b[5]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[5]_syn_14.d to FMC/FMC_data_b[5]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[4]_syn_14.d to FMC/FMC_data_b[4]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[4]_syn_14.b to FMC/FMC_data_b[4]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[4]_syn_14.a to FMC/FMC_data_b[4]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[4]_syn_12.d to FMC/FMC_data_b[4]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[4]_syn_12.c to FMC/FMC_data_b[4]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[3]_syn_14.d to FMC/FMC_data_b[3]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[3]_syn_14.b to FMC/FMC_data_b[3]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[3]_syn_14.a to FMC/FMC_data_b[3]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[3]_syn_12.d to FMC/FMC_data_b[3]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[3]_syn_12.c to FMC/FMC_data_b[3]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[2]_syn_14.d to FMC/FMC_data_b[2]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[2]_syn_14.b to FMC/FMC_data_b[2]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[2]_syn_14.a to FMC/FMC_data_b[2]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[2]_syn_12.d to FMC/FMC_data_b[2]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[2]_syn_12.c to FMC/FMC_data_b[2]_syn_12.o.
TMR-2509 : Cut connection from FMC/top_state[1]_syn_5.c to FMC/top_state[1]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[10]_syn_1.c to FMC/FMC_data_b[10]_syn_1.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[10]_syn_1.b to FMC/FMC_data_b[10]_syn_1.o.
TMR-2509 : Cut connection from FMC/FMC_data_n_syn_1.c to FMC/FMC_data_n_syn_1.o.
TMR-2509 : Cut connection from FMC/FMC_data_n_syn_1.b to FMC/FMC_data_n_syn_1.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_15.b to FMC/top_state[0]_syn_15.o.
TMR-2509 : Cut connection from FMC/top_state[2]_syn_6.c to FMC/top_state[2]_syn_6.o.
TMR-2509 : Cut connection from FMC/top_state[2]_syn_8.b to FMC/top_state[2]_syn_8.o.
TMR-2509 : Cut connection from FMC/top_state[2]_syn_8.c to FMC/top_state[2]_syn_8.o.
TMR-2509 : Cut connection from FMC/top_state[3]_syn_3.d to FMC/top_state[3]_syn_3.o.
TMR-2507 : Eliminate loop in the timing graph, delete 147 tedges.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.313827s wall, 1.312500s user + 0.000000s system = 1.312500s CPU (99.9%)

RUN-1004 : used memory is 525 MB, reserved memory is 498 MB, peak memory is 573 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21779 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.255584s wall, 2.234375s user + 0.015625s system = 2.250000s CPU (99.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.5095e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19299.
PHY-3001 : Level 1 #clusters 2152.
PHY-3001 : End clustering;  0.164550s wall, 0.187500s user + 0.046875s system = 0.234375s CPU (142.4%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 853100, overlap = 604.656
PHY-3002 : Step(2): len = 786733, overlap = 637.688
PHY-3002 : Step(3): len = 507497, overlap = 827.875
PHY-3002 : Step(4): len = 448029, overlap = 899.031
PHY-3002 : Step(5): len = 357636, overlap = 977.656
PHY-3002 : Step(6): len = 314396, overlap = 1016.19
PHY-3002 : Step(7): len = 267847, overlap = 1082.69
PHY-3002 : Step(8): len = 240335, overlap = 1149.78
PHY-3002 : Step(9): len = 215437, overlap = 1199.69
PHY-3002 : Step(10): len = 202064, overlap = 1251.12
PHY-3002 : Step(11): len = 183515, overlap = 1299.84
PHY-3002 : Step(12): len = 173009, overlap = 1324.28
PHY-3002 : Step(13): len = 158153, overlap = 1358.72
PHY-3002 : Step(14): len = 147943, overlap = 1387.16
PHY-3002 : Step(15): len = 137064, overlap = 1384.34
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.05862e-06
PHY-3002 : Step(16): len = 139709, overlap = 1367.88
PHY-3002 : Step(17): len = 167376, overlap = 1290
PHY-3002 : Step(18): len = 174166, overlap = 1223.47
PHY-3002 : Step(19): len = 183436, overlap = 1182.5
PHY-3002 : Step(20): len = 181305, overlap = 1147.97
PHY-3002 : Step(21): len = 179167, overlap = 1113.59
PHY-3002 : Step(22): len = 175584, overlap = 1092.03
PHY-3002 : Step(23): len = 173646, overlap = 1082.06
PHY-3002 : Step(24): len = 170776, overlap = 1070.22
PHY-3002 : Step(25): len = 169882, overlap = 1063.94
PHY-3002 : Step(26): len = 167509, overlap = 1058.25
PHY-3002 : Step(27): len = 166675, overlap = 1042.03
PHY-3002 : Step(28): len = 165016, overlap = 1038.97
PHY-3002 : Step(29): len = 164000, overlap = 1043.69
PHY-3002 : Step(30): len = 162310, overlap = 1049.19
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.11724e-06
PHY-3002 : Step(31): len = 171053, overlap = 1033.97
PHY-3002 : Step(32): len = 186511, overlap = 976.875
PHY-3002 : Step(33): len = 189896, overlap = 919.75
PHY-3002 : Step(34): len = 191422, overlap = 908.562
PHY-3002 : Step(35): len = 191153, overlap = 910.438
PHY-3002 : Step(36): len = 192159, overlap = 904.781
PHY-3002 : Step(37): len = 192516, overlap = 894.75
PHY-3002 : Step(38): len = 192724, overlap = 892
PHY-3002 : Step(39): len = 192182, overlap = 891.438
PHY-3002 : Step(40): len = 192626, overlap = 899.094
PHY-3002 : Step(41): len = 191640, overlap = 899.031
PHY-3002 : Step(42): len = 190751, overlap = 895.875
PHY-3002 : Step(43): len = 189583, overlap = 894.562
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.23449e-06
PHY-3002 : Step(44): len = 197572, overlap = 872.719
PHY-3002 : Step(45): len = 213381, overlap = 836.875
PHY-3002 : Step(46): len = 215868, overlap = 802.844
PHY-3002 : Step(47): len = 216726, overlap = 787.906
PHY-3002 : Step(48): len = 215901, overlap = 770.594
PHY-3002 : Step(49): len = 215418, overlap = 780.844
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 8.46898e-06
PHY-3002 : Step(50): len = 224942, overlap = 722.125
PHY-3002 : Step(51): len = 238251, overlap = 668.062
PHY-3002 : Step(52): len = 243502, overlap = 623.812
PHY-3002 : Step(53): len = 244949, overlap = 619.938
PHY-3002 : Step(54): len = 244177, overlap = 618.938
PHY-3002 : Step(55): len = 243573, overlap = 595.375
PHY-3002 : Step(56): len = 241956, overlap = 591.531
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.6938e-05
PHY-3002 : Step(57): len = 252368, overlap = 567.062
PHY-3002 : Step(58): len = 267831, overlap = 511.094
PHY-3002 : Step(59): len = 272492, overlap = 475.25
PHY-3002 : Step(60): len = 274405, overlap = 476.781
PHY-3002 : Step(61): len = 274585, overlap = 450.312
PHY-3002 : Step(62): len = 273046, overlap = 439.312
PHY-3002 : Step(63): len = 270527, overlap = 427.625
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.38759e-05
PHY-3002 : Step(64): len = 278959, overlap = 408.844
PHY-3002 : Step(65): len = 288654, overlap = 393.312
PHY-3002 : Step(66): len = 291877, overlap = 364.562
PHY-3002 : Step(67): len = 293859, overlap = 356.062
PHY-3002 : Step(68): len = 293415, overlap = 378.375
PHY-3002 : Step(69): len = 292443, overlap = 367.656
PHY-3002 : Step(70): len = 291190, overlap = 359.188
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 6.77518e-05
PHY-3002 : Step(71): len = 296802, overlap = 349.344
PHY-3002 : Step(72): len = 305922, overlap = 337.719
PHY-3002 : Step(73): len = 310005, overlap = 322.812
PHY-3002 : Step(74): len = 311786, overlap = 306.844
PHY-3002 : Step(75): len = 310519, overlap = 317.656
PHY-3002 : Step(76): len = 309361, overlap = 316.719
PHY-3002 : Step(77): len = 307404, overlap = 311.219
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000135504
PHY-3002 : Step(78): len = 312245, overlap = 297.938
PHY-3002 : Step(79): len = 320693, overlap = 277.906
PHY-3002 : Step(80): len = 323464, overlap = 269.562
PHY-3002 : Step(81): len = 325009, overlap = 248.344
PHY-3002 : Step(82): len = 323925, overlap = 234.969
PHY-3002 : Step(83): len = 323321, overlap = 275.844
PHY-3002 : Step(84): len = 321620, overlap = 282.375
PHY-3002 : Step(85): len = 322019, overlap = 289.531
PHY-3002 : Step(86): len = 321965, overlap = 268.219
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000271007
PHY-3002 : Step(87): len = 324358, overlap = 269.906
PHY-3002 : Step(88): len = 329160, overlap = 262.469
PHY-3002 : Step(89): len = 330766, overlap = 256.562
PHY-3002 : Step(90): len = 333628, overlap = 247.906
PHY-3002 : Step(91): len = 333820, overlap = 232.75
PHY-3002 : Step(92): len = 333712, overlap = 240.188
PHY-3002 : Step(93): len = 333818, overlap = 230.469
PHY-3002 : Step(94): len = 333448, overlap = 224.469
PHY-3002 : Step(95): len = 333622, overlap = 222.25
PHY-3002 : Step(96): len = 333910, overlap = 215.156
PHY-3002 : Step(97): len = 332845, overlap = 214.375
PHY-3002 : Step(98): len = 332855, overlap = 201.906
PHY-3002 : Step(99): len = 332328, overlap = 199.469
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(100): len = 333412, overlap = 193.906
PHY-3002 : Step(101): len = 336851, overlap = 184.531
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.014137s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (331.6%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21781.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 437416, over cnt = 1165(3%), over = 5460, worst = 51
PHY-1001 : End global iterations;  0.840832s wall, 1.093750s user + 0.031250s system = 1.125000s CPU (133.8%)

PHY-1001 : Congestion index: top1 = 80.95, top5 = 53.53, top10 = 43.18, top15 = 37.56.
PHY-3001 : End congestion estimation;  1.110248s wall, 1.328125s user + 0.046875s system = 1.375000s CPU (123.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21779 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.014523s wall, 0.984375s user + 0.031250s system = 1.015625s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.53031e-05
PHY-3002 : Step(102): len = 379130, overlap = 180.625
PHY-3002 : Step(103): len = 393742, overlap = 154.031
PHY-3002 : Step(104): len = 399446, overlap = 126.656
PHY-3002 : Step(105): len = 400926, overlap = 128.75
PHY-3002 : Step(106): len = 403393, overlap = 125.688
PHY-3002 : Step(107): len = 411708, overlap = 115.938
PHY-3002 : Step(108): len = 423439, overlap = 113.688
PHY-3002 : Step(109): len = 423338, overlap = 107.938
PHY-3002 : Step(110): len = 424908, overlap = 99.9375
PHY-3002 : Step(111): len = 429108, overlap = 90.5625
PHY-3002 : Step(112): len = 429177, overlap = 85.7188
PHY-3002 : Step(113): len = 430428, overlap = 84.125
PHY-3002 : Step(114): len = 429855, overlap = 77.0625
PHY-3002 : Step(115): len = 430623, overlap = 74.9375
PHY-3002 : Step(116): len = 433422, overlap = 80.7812
PHY-3002 : Step(117): len = 431920, overlap = 83.2812
PHY-3002 : Step(118): len = 431962, overlap = 85.3125
PHY-3002 : Step(119): len = 433275, overlap = 86.1562
PHY-3002 : Step(120): len = 434598, overlap = 90.125
PHY-3002 : Step(121): len = 435094, overlap = 91.1562
PHY-3002 : Step(122): len = 435516, overlap = 87.9062
PHY-3002 : Step(123): len = 437484, overlap = 85.6875
PHY-3002 : Step(124): len = 437072, overlap = 85.5938
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000190606
PHY-3002 : Step(125): len = 438557, overlap = 80.0938
PHY-3002 : Step(126): len = 440036, overlap = 79.0938
PHY-3002 : Step(127): len = 441723, overlap = 76.2188
PHY-3002 : Step(128): len = 444605, overlap = 73.4688
PHY-3002 : Step(129): len = 449908, overlap = 72.625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000381212
PHY-3002 : Step(130): len = 450436, overlap = 72.2812
PHY-3002 : Step(131): len = 452995, overlap = 72.3125
PHY-3002 : Step(132): len = 456065, overlap = 66.9062
PHY-3002 : Step(133): len = 464619, overlap = 72.5938
PHY-3002 : Step(134): len = 470896, overlap = 72.375
PHY-3002 : Step(135): len = 470340, overlap = 82.0312
PHY-3002 : Step(136): len = 472131, overlap = 91
PHY-3002 : Step(137): len = 474132, overlap = 93.9062
PHY-3002 : Step(138): len = 474799, overlap = 99.9062
PHY-3002 : Step(139): len = 472905, overlap = 107.219
PHY-3002 : Step(140): len = 471901, overlap = 108.812
PHY-3002 : Step(141): len = 471708, overlap = 111.531
PHY-3002 : Step(142): len = 473399, overlap = 114.062
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000762425
PHY-3002 : Step(143): len = 472469, overlap = 112.312
PHY-3002 : Step(144): len = 476539, overlap = 109.656
PHY-3002 : Step(145): len = 479177, overlap = 111.312
PHY-3002 : Step(146): len = 482155, overlap = 104.219
PHY-3002 : Step(147): len = 486704, overlap = 98.2188
PHY-3002 : Step(148): len = 489670, overlap = 99.2188
PHY-3002 : Step(149): len = 488868, overlap = 95.8438
PHY-3002 : Step(150): len = 488715, overlap = 96.4375
PHY-3002 : Step(151): len = 490002, overlap = 102.438
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(152): len = 488979, overlap = 97.4688
PHY-3002 : Step(153): len = 489891, overlap = 97.5
PHY-3002 : Step(154): len = 493412, overlap = 95.375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 36/21781.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 552592, over cnt = 2223(6%), over = 10856, worst = 40
PHY-1001 : End global iterations;  1.054700s wall, 1.765625s user + 0.015625s system = 1.781250s CPU (168.9%)

PHY-1001 : Congestion index: top1 = 80.39, top5 = 60.81, top10 = 51.76, top15 = 46.42.
PHY-3001 : End congestion estimation;  1.363624s wall, 2.078125s user + 0.015625s system = 2.093750s CPU (153.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21779 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.038217s wall, 1.015625s user + 0.031250s system = 1.046875s CPU (100.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00010505
PHY-3002 : Step(155): len = 498545, overlap = 387.969
PHY-3002 : Step(156): len = 501965, overlap = 324.375
PHY-3002 : Step(157): len = 501622, overlap = 275.688
PHY-3002 : Step(158): len = 496211, overlap = 257.375
PHY-3002 : Step(159): len = 491865, overlap = 238.781
PHY-3002 : Step(160): len = 488793, overlap = 245.094
PHY-3002 : Step(161): len = 483130, overlap = 247
PHY-3002 : Step(162): len = 480994, overlap = 234.281
PHY-3002 : Step(163): len = 477077, overlap = 240.094
PHY-3002 : Step(164): len = 473788, overlap = 241.469
PHY-3002 : Step(165): len = 471640, overlap = 238.812
PHY-3002 : Step(166): len = 469970, overlap = 245.094
PHY-3002 : Step(167): len = 467065, overlap = 244.781
PHY-3002 : Step(168): len = 464033, overlap = 240.969
PHY-3002 : Step(169): len = 461451, overlap = 244.438
PHY-3002 : Step(170): len = 459735, overlap = 239.562
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0002101
PHY-3002 : Step(171): len = 459863, overlap = 232.844
PHY-3002 : Step(172): len = 461121, overlap = 228.812
PHY-3002 : Step(173): len = 461747, overlap = 228.031
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000420199
PHY-3002 : Step(174): len = 464703, overlap = 218.094
PHY-3002 : Step(175): len = 474311, overlap = 189.312
PHY-3002 : Step(176): len = 477698, overlap = 181.406
PHY-3002 : Step(177): len = 477234, overlap = 181.656
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000840398
PHY-3002 : Step(178): len = 478295, overlap = 176.969
PHY-3002 : Step(179): len = 481651, overlap = 164.594
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80211, tnet num: 21779, tinst num: 19299, tnode num: 112644, tedge num: 126133.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_39.b to FMC/top_state[0]_syn_39.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_39.a to FMC/top_state[0]_syn_39.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_39.c to FMC/top_state[0]_syn_39.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_47.e to FMC/top_state[0]_syn_47.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_47.d to FMC/top_state[0]_syn_47.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_47.c to FMC/top_state[0]_syn_47.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_47.a to FMC/top_state[0]_syn_47.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_5.a to FMC/FMC_data_b[14]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_5.b to FMC/FMC_data_b[14]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_5.c to FMC/FMC_data_b[14]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_13.b to FMC/FMC_data_b[14]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_13.c to FMC/FMC_data_b[14]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_13.e to FMC/FMC_data_b[14]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_13.d to FMC/FMC_data_b[14]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_5.a to FMC/FMC_data_b[11]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_5.b to FMC/FMC_data_b[11]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_5.c to FMC/FMC_data_b[11]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_13.b to FMC/FMC_data_b[11]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_13.c to FMC/FMC_data_b[11]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_13.e to FMC/FMC_data_b[11]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_13.d to FMC/FMC_data_b[11]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[9]_syn_5.a to FMC/FMC_data_b[9]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[9]_syn_5.b to FMC/FMC_data_b[9]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[9]_syn_5.c to FMC/FMC_data_b[9]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[9]_syn_13.b to FMC/FMC_data_b[9]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[9]_syn_13.c to FMC/FMC_data_b[9]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[9]_syn_13.e to FMC/FMC_data_b[9]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[9]_syn_13.d to FMC/FMC_data_b[9]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[1]_syn_5.a to FMC/FMC_data_b[1]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[1]_syn_5.b to FMC/FMC_data_b[1]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[1]_syn_5.c to FMC/FMC_data_b[1]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[1]_syn_13.b to FMC/FMC_data_b[1]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[1]_syn_13.c to FMC/FMC_data_b[1]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[1]_syn_13.e to FMC/FMC_data_b[1]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[1]_syn_13.d to FMC/FMC_data_b[1]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[0]_syn_5.a to FMC/FMC_data_b[0]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[0]_syn_5.b to FMC/FMC_data_b[0]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[0]_syn_5.c to FMC/FMC_data_b[0]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[0]_syn_13.b to FMC/FMC_data_b[0]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[0]_syn_13.c to FMC/FMC_data_b[0]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[0]_syn_13.e to FMC/FMC_data_b[0]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[0]_syn_13.d to FMC/FMC_data_b[0]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_5.a to FMC/FMC_data_b[13]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_5.b to FMC/FMC_data_b[13]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_5.c to FMC/FMC_data_b[13]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_13.b to FMC/FMC_data_b[13]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_13.c to FMC/FMC_data_b[13]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_13.e to FMC/FMC_data_b[13]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_13.d to FMC/FMC_data_b[13]_syn_13.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_37.a to FMC/top_state[0]_syn_37.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_43.a to FMC/top_state[0]_syn_43.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_45.d to FMC/top_state[0]_syn_45.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_45.b to FMC/top_state[0]_syn_45.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_45.c to FMC/top_state[0]_syn_45.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_31.b to FMC/top_state[0]_syn_31.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_31.a to FMC/top_state[0]_syn_31.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[6]_syn_5.a to FMC/FMC_data_b[6]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[6]_syn_5.b to FMC/FMC_data_b[6]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[6]_syn_5.c to FMC/FMC_data_b[6]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[6]_syn_13.b to FMC/FMC_data_b[6]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[6]_syn_13.c to FMC/FMC_data_b[6]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[6]_syn_13.e to FMC/FMC_data_b[6]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[6]_syn_13.d to FMC/FMC_data_b[6]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_1.d to FMC/FMC_data_b[13]_syn_1.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_3.d to FMC/FMC_data_b[13]_syn_3.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_10.d to FMC/FMC_data_b[14]_syn_10.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_12.d to FMC/FMC_data_b[14]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_7.d to FMC/FMC_data_b[14]_syn_7.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_9.d to FMC/FMC_data_b[14]_syn_9.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_1.d to FMC/FMC_data_b[14]_syn_1.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_3.d to FMC/FMC_data_b[14]_syn_3.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_9.d to FMC/FMC_data_b[15]_syn_9.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_11.d to FMC/FMC_data_b[15]_syn_11.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_12.a to FMC/FMC_data_b[15]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_12.d to FMC/FMC_data_b[15]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_12.c to FMC/FMC_data_b[15]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_14.c to FMC/FMC_data_b[15]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_14.b to FMC/FMC_data_b[15]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_14.d to FMC/FMC_data_b[15]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_6.d to FMC/FMC_data_b[15]_syn_6.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_8.d to FMC/FMC_data_b[15]_syn_8.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_4.a to FMC/FMC_data_b[15]_syn_4.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_1.d to FMC/FMC_data_b[15]_syn_1.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_3.d to FMC/FMC_data_b[15]_syn_3.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_10.d to FMC/FMC_data_b[11]_syn_10.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_12.d to FMC/FMC_data_b[11]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_7.d to FMC/FMC_data_b[11]_syn_7.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_9.d to FMC/FMC_data_b[11]_syn_9.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_1.d to FMC/FMC_data_b[11]_syn_1.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_3.d to FMC/FMC_data_b[11]_syn_3.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[12]_syn_9.d to FMC/FMC_data_b[12]_syn_9.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[12]_syn_11.d to FMC/FMC_data_b[12]_syn_11.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[12]_syn_14.b to FMC/FMC_data_b[12]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[12]_syn_14.a to FMC/FMC_data_b[12]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[12]_syn_14.d to FMC/FMC_data_b[12]_syn_14.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_3.a to FMC/top_state[0]_syn_3.o.
TMR-2509 : Cut connection from FMC/top_state[1]_syn_8.c to FMC/top_state[1]_syn_8.o.
TMR-2509 : Cut connection from FMC/top_state[1]_syn_8.d to FMC/top_state[1]_syn_8.o.
TMR-2509 : Cut connection from FMC/top_state[1]_syn_8.b to FMC/top_state[1]_syn_8.o.
TMR-2509 : Cut connection from FMC/top_state[1]_syn_9.c to FMC/top_state[1]_syn_9.o.
TMR-2509 : Cut connection from FMC/top_state[1]_syn_9.b to FMC/top_state[1]_syn_9.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[12]_syn_12.d to FMC/FMC_data_b[12]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[12]_syn_12.c to FMC/FMC_data_b[12]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[10]_syn_18.d to FMC/FMC_data_b[10]_syn_18.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[10]_syn_18.b to FMC/FMC_data_b[10]_syn_18.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[10]_syn_18.a to FMC/FMC_data_b[10]_syn_18.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[10]_syn_16.d to FMC/FMC_data_b[10]_syn_16.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[10]_syn_16.c to FMC/FMC_data_b[10]_syn_16.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[8]_syn_14.d to FMC/FMC_data_b[8]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[8]_syn_14.b to FMC/FMC_data_b[8]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[8]_syn_14.a to FMC/FMC_data_b[8]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[8]_syn_12.d to FMC/FMC_data_b[8]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[8]_syn_12.c to FMC/FMC_data_b[8]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[7]_syn_14.d to FMC/FMC_data_b[7]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[7]_syn_14.b to FMC/FMC_data_b[7]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[7]_syn_14.a to FMC/FMC_data_b[7]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[7]_syn_12.d to FMC/FMC_data_b[7]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[7]_syn_12.c to FMC/FMC_data_b[7]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[5]_syn_12.d to FMC/FMC_data_b[5]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[5]_syn_12.c to FMC/FMC_data_b[5]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[5]_syn_14.b to FMC/FMC_data_b[5]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[5]_syn_14.d to FMC/FMC_data_b[5]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[4]_syn_14.d to FMC/FMC_data_b[4]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[4]_syn_14.b to FMC/FMC_data_b[4]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[4]_syn_14.a to FMC/FMC_data_b[4]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[4]_syn_12.d to FMC/FMC_data_b[4]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[4]_syn_12.c to FMC/FMC_data_b[4]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[3]_syn_14.d to FMC/FMC_data_b[3]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[3]_syn_14.b to FMC/FMC_data_b[3]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[3]_syn_14.a to FMC/FMC_data_b[3]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[3]_syn_12.d to FMC/FMC_data_b[3]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[3]_syn_12.c to FMC/FMC_data_b[3]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[2]_syn_14.d to FMC/FMC_data_b[2]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[2]_syn_14.b to FMC/FMC_data_b[2]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[2]_syn_14.a to FMC/FMC_data_b[2]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[2]_syn_12.d to FMC/FMC_data_b[2]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[2]_syn_12.c to FMC/FMC_data_b[2]_syn_12.o.
TMR-2509 : Cut connection from FMC/top_state[1]_syn_5.c to FMC/top_state[1]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[10]_syn_1.c to FMC/FMC_data_b[10]_syn_1.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[10]_syn_1.b to FMC/FMC_data_b[10]_syn_1.o.
TMR-2509 : Cut connection from FMC/FMC_data_n_syn_1.c to FMC/FMC_data_n_syn_1.o.
TMR-2509 : Cut connection from FMC/FMC_data_n_syn_1.b to FMC/FMC_data_n_syn_1.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_15.b to FMC/top_state[0]_syn_15.o.
TMR-2509 : Cut connection from FMC/top_state[2]_syn_6.c to FMC/top_state[2]_syn_6.o.
TMR-2509 : Cut connection from FMC/top_state[2]_syn_8.b to FMC/top_state[2]_syn_8.o.
TMR-2509 : Cut connection from FMC/top_state[2]_syn_8.c to FMC/top_state[2]_syn_8.o.
TMR-2509 : Cut connection from FMC/top_state[3]_syn_3.d to FMC/top_state[3]_syn_3.o.
TMR-2507 : Eliminate loop in the timing graph, delete 147 tedges.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.643962s wall, 1.609375s user + 0.031250s system = 1.640625s CPU (99.8%)

RUN-1004 : used memory is 565 MB, reserved memory is 538 MB, peak memory is 697 MB
OPT-1001 : Total overflow 504.88 peak overflow 4.53
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 348/21781.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 557104, over cnt = 2513(7%), over = 9193, worst = 24
PHY-1001 : End global iterations;  1.242123s wall, 2.046875s user + 0.046875s system = 2.093750s CPU (168.6%)

PHY-1001 : Congestion index: top1 = 57.78, top5 = 49.49, top10 = 44.57, top15 = 41.31.
PHY-1001 : End incremental global routing;  1.518442s wall, 2.343750s user + 0.046875s system = 2.390625s CPU (157.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21779 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.082208s wall, 1.062500s user + 0.015625s system = 1.078125s CPU (99.6%)

OPT-1001 : 15 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19221 has valid locations, 219 needs to be replaced
PHY-3001 : design contains 19503 instances, 5711 luts, 12215 seqs, 1488 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 496183
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17288/21985.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 569144, over cnt = 2545(7%), over = 9253, worst = 23
PHY-1001 : End global iterations;  0.189264s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (132.1%)

PHY-1001 : Congestion index: top1 = 57.78, top5 = 49.56, top10 = 44.79, top15 = 41.56.
PHY-3001 : End congestion estimation;  0.451352s wall, 0.500000s user + 0.015625s system = 0.515625s CPU (114.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80872, tnet num: 21983, tinst num: 19503, tnode num: 113562, tedge num: 127047.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_39.b to FMC/top_state[0]_syn_39.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_39.a to FMC/top_state[0]_syn_39.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_39.c to FMC/top_state[0]_syn_39.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_47.e to FMC/top_state[0]_syn_47.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_47.d to FMC/top_state[0]_syn_47.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_47.c to FMC/top_state[0]_syn_47.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_47.a to FMC/top_state[0]_syn_47.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_5.a to FMC/FMC_data_b[14]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_5.b to FMC/FMC_data_b[14]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_5.c to FMC/FMC_data_b[14]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_13.b to FMC/FMC_data_b[14]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_13.c to FMC/FMC_data_b[14]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_13.e to FMC/FMC_data_b[14]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_13.d to FMC/FMC_data_b[14]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_5.a to FMC/FMC_data_b[11]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_5.b to FMC/FMC_data_b[11]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_5.c to FMC/FMC_data_b[11]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_13.b to FMC/FMC_data_b[11]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_13.c to FMC/FMC_data_b[11]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_13.e to FMC/FMC_data_b[11]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_13.d to FMC/FMC_data_b[11]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[9]_syn_5.a to FMC/FMC_data_b[9]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[9]_syn_5.b to FMC/FMC_data_b[9]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[9]_syn_5.c to FMC/FMC_data_b[9]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[9]_syn_13.b to FMC/FMC_data_b[9]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[9]_syn_13.c to FMC/FMC_data_b[9]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[9]_syn_13.e to FMC/FMC_data_b[9]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[9]_syn_13.d to FMC/FMC_data_b[9]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[1]_syn_5.a to FMC/FMC_data_b[1]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[1]_syn_5.b to FMC/FMC_data_b[1]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[1]_syn_5.c to FMC/FMC_data_b[1]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[1]_syn_13.b to FMC/FMC_data_b[1]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[1]_syn_13.c to FMC/FMC_data_b[1]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[1]_syn_13.e to FMC/FMC_data_b[1]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[1]_syn_13.d to FMC/FMC_data_b[1]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[0]_syn_5.a to FMC/FMC_data_b[0]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[0]_syn_5.b to FMC/FMC_data_b[0]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[0]_syn_5.c to FMC/FMC_data_b[0]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[0]_syn_13.b to FMC/FMC_data_b[0]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[0]_syn_13.c to FMC/FMC_data_b[0]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[0]_syn_13.e to FMC/FMC_data_b[0]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[0]_syn_13.d to FMC/FMC_data_b[0]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_5.a to FMC/FMC_data_b[13]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_5.b to FMC/FMC_data_b[13]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_5.c to FMC/FMC_data_b[13]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_13.b to FMC/FMC_data_b[13]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_13.c to FMC/FMC_data_b[13]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_13.e to FMC/FMC_data_b[13]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_13.d to FMC/FMC_data_b[13]_syn_13.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_37.a to FMC/top_state[0]_syn_37.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_43.a to FMC/top_state[0]_syn_43.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_45.d to FMC/top_state[0]_syn_45.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_45.b to FMC/top_state[0]_syn_45.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_45.c to FMC/top_state[0]_syn_45.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_31.b to FMC/top_state[0]_syn_31.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_31.a to FMC/top_state[0]_syn_31.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[6]_syn_5.a to FMC/FMC_data_b[6]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[6]_syn_5.b to FMC/FMC_data_b[6]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[6]_syn_5.c to FMC/FMC_data_b[6]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[6]_syn_13.b to FMC/FMC_data_b[6]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[6]_syn_13.c to FMC/FMC_data_b[6]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[6]_syn_13.e to FMC/FMC_data_b[6]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[6]_syn_13.d to FMC/FMC_data_b[6]_syn_13.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_1.d to FMC/FMC_data_b[13]_syn_1.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[13]_syn_3.d to FMC/FMC_data_b[13]_syn_3.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_10.d to FMC/FMC_data_b[14]_syn_10.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_12.d to FMC/FMC_data_b[14]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_7.d to FMC/FMC_data_b[14]_syn_7.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_9.d to FMC/FMC_data_b[14]_syn_9.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_1.d to FMC/FMC_data_b[14]_syn_1.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[14]_syn_3.d to FMC/FMC_data_b[14]_syn_3.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_9.d to FMC/FMC_data_b[15]_syn_9.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_11.d to FMC/FMC_data_b[15]_syn_11.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_12.a to FMC/FMC_data_b[15]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_12.d to FMC/FMC_data_b[15]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_12.c to FMC/FMC_data_b[15]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_14.c to FMC/FMC_data_b[15]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_14.b to FMC/FMC_data_b[15]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_14.d to FMC/FMC_data_b[15]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_6.d to FMC/FMC_data_b[15]_syn_6.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_8.d to FMC/FMC_data_b[15]_syn_8.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_4.a to FMC/FMC_data_b[15]_syn_4.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_1.d to FMC/FMC_data_b[15]_syn_1.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[15]_syn_3.d to FMC/FMC_data_b[15]_syn_3.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_10.d to FMC/FMC_data_b[11]_syn_10.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_12.d to FMC/FMC_data_b[11]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_7.d to FMC/FMC_data_b[11]_syn_7.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_9.d to FMC/FMC_data_b[11]_syn_9.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_1.d to FMC/FMC_data_b[11]_syn_1.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[11]_syn_3.d to FMC/FMC_data_b[11]_syn_3.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[12]_syn_9.d to FMC/FMC_data_b[12]_syn_9.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[12]_syn_11.d to FMC/FMC_data_b[12]_syn_11.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[12]_syn_14.b to FMC/FMC_data_b[12]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[12]_syn_14.a to FMC/FMC_data_b[12]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[12]_syn_14.d to FMC/FMC_data_b[12]_syn_14.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_3.a to FMC/top_state[0]_syn_3.o.
TMR-2509 : Cut connection from FMC/top_state[1]_syn_8.c to FMC/top_state[1]_syn_8.o.
TMR-2509 : Cut connection from FMC/top_state[1]_syn_8.d to FMC/top_state[1]_syn_8.o.
TMR-2509 : Cut connection from FMC/top_state[1]_syn_8.b to FMC/top_state[1]_syn_8.o.
TMR-2509 : Cut connection from FMC/top_state[1]_syn_9.c to FMC/top_state[1]_syn_9.o.
TMR-2509 : Cut connection from FMC/top_state[1]_syn_9.b to FMC/top_state[1]_syn_9.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[12]_syn_12.d to FMC/FMC_data_b[12]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[12]_syn_12.c to FMC/FMC_data_b[12]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[10]_syn_18.d to FMC/FMC_data_b[10]_syn_18.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[10]_syn_18.b to FMC/FMC_data_b[10]_syn_18.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[10]_syn_18.a to FMC/FMC_data_b[10]_syn_18.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[10]_syn_16.d to FMC/FMC_data_b[10]_syn_16.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[10]_syn_16.c to FMC/FMC_data_b[10]_syn_16.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[8]_syn_14.d to FMC/FMC_data_b[8]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[8]_syn_14.b to FMC/FMC_data_b[8]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[8]_syn_14.a to FMC/FMC_data_b[8]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[8]_syn_12.d to FMC/FMC_data_b[8]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[8]_syn_12.c to FMC/FMC_data_b[8]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[7]_syn_14.d to FMC/FMC_data_b[7]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[7]_syn_14.b to FMC/FMC_data_b[7]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[7]_syn_14.a to FMC/FMC_data_b[7]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[7]_syn_12.d to FMC/FMC_data_b[7]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[7]_syn_12.c to FMC/FMC_data_b[7]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[5]_syn_12.d to FMC/FMC_data_b[5]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[5]_syn_12.c to FMC/FMC_data_b[5]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[5]_syn_14.b to FMC/FMC_data_b[5]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[5]_syn_14.d to FMC/FMC_data_b[5]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[4]_syn_14.d to FMC/FMC_data_b[4]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[4]_syn_14.b to FMC/FMC_data_b[4]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[4]_syn_14.a to FMC/FMC_data_b[4]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[4]_syn_12.d to FMC/FMC_data_b[4]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[4]_syn_12.c to FMC/FMC_data_b[4]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[3]_syn_14.d to FMC/FMC_data_b[3]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[3]_syn_14.b to FMC/FMC_data_b[3]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[3]_syn_14.a to FMC/FMC_data_b[3]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[3]_syn_12.d to FMC/FMC_data_b[3]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[3]_syn_12.c to FMC/FMC_data_b[3]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[2]_syn_14.d to FMC/FMC_data_b[2]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[2]_syn_14.b to FMC/FMC_data_b[2]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[2]_syn_14.a to FMC/FMC_data_b[2]_syn_14.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[2]_syn_12.d to FMC/FMC_data_b[2]_syn_12.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[2]_syn_12.c to FMC/FMC_data_b[2]_syn_12.o.
TMR-2509 : Cut connection from FMC/top_state[1]_syn_5.c to FMC/top_state[1]_syn_5.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[10]_syn_1.c to FMC/FMC_data_b[10]_syn_1.o.
TMR-2509 : Cut connection from FMC/FMC_data_b[10]_syn_1.b to FMC/FMC_data_b[10]_syn_1.o.
TMR-2509 : Cut connection from FMC/FMC_data_n_syn_1.c to FMC/FMC_data_n_syn_1.o.
TMR-2509 : Cut connection from FMC/FMC_data_n_syn_1.b to FMC/FMC_data_n_syn_1.o.
TMR-2509 : Cut connection from FMC/top_state[0]_syn_15.b to FMC/top_state[0]_syn_15.o.
TMR-2509 : Cut connection from FMC/top_state[2]_syn_6.c to FMC/top_state[2]_syn_6.o.
TMR-2509 : Cut connection from FMC/top_state[2]_syn_8.b to FMC/top_state[2]_syn_8.o.
TMR-2509 : Cut connection from FMC/top_state[2]_syn_8.c to FMC/top_state[2]_syn_8.o.
TMR-2509 : Cut connection from FMC/top_state[3]_syn_3.d to FMC/top_state[3]_syn_3.o.
TMR-2507 : Eliminate loop in the timing graph, delete 147 tedges.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.638034s wall, 1.578125s user + 0.062500s system = 1.640625s CPU (100.2%)

RUN-1004 : used memory is 609 MB, reserved memory is 603 MB, peak memory is 699 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21983 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.732146s wall, 2.640625s user + 0.093750s system = 2.734375s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(180): len = 496292, overlap = 4.0625
PHY-3002 : Step(181): len = 497228, overlap = 4.0625
PHY-3002 : Step(182): len = 498305, overlap = 4
PHY-3002 : Step(183): len = 498762, overlap = 4
PHY-3002 : Step(184): len = 499291, overlap = 4.125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(185): len = 499185, overlap = 4.125
PHY-3002 : Step(186): len = 499171, overlap = 4.3125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(187): len = 499189, overlap = 4.1875
PHY-3002 : Step(188): len = 501185, overlap = 4.0625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17297/21985.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 571456, over cnt = 2543(7%), over = 9291, worst = 23
PHY-1001 : End global iterations;  0.196107s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (111.5%)

PHY-1001 : Congestion index: top1 = 58.19, top5 = 49.80, top10 = 44.97, top15 = 41.70.
PHY-3001 : End congestion estimation;  0.459413s wall, 0.484375s user + 0.000000s system = 0.484375s CPU (105.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21983 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.073698s wall, 1.062500s user + 0.015625s system = 1.078125s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000894448
PHY-3002 : Step(189): len = 501024, overlap = 166.344
PHY-3002 : Step(190): len = 501009, overlap = 165.969
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0017889
PHY-3002 : Step(191): len = 501087, overlap = 165.875
PHY-3002 : Step(192): len = 501150, overlap = 165.656
PHY-3001 : Final: Len = 501150, Over = 165.656
PHY-3001 : End incremental placement;  5.991520s wall, 5.859375s user + 0.453125s system = 6.312500s CPU (105.4%)

OPT-1001 : Total overflow 509.06 peak overflow 4.53
OPT-1001 : End high-fanout net optimization;  9.157077s wall, 9.984375s user + 0.515625s system = 10.500000s CPU (114.7%)

OPT-1001 : Current memory(MB): used = 700, reserve = 680, peak = 719.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17327/21985.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 572240, over cnt = 2511(7%), over = 8852, worst = 23
PHY-1002 : len = 616400, over cnt = 1797(5%), over = 4619, worst = 20
PHY-1002 : len = 653080, over cnt = 657(1%), over = 1616, worst = 14
PHY-1002 : len = 666192, over cnt = 367(1%), over = 898, worst = 14
PHY-1002 : len = 683488, over cnt = 25(0%), over = 45, worst = 6
PHY-1001 : End global iterations;  1.434938s wall, 2.000000s user + 0.000000s system = 2.000000s CPU (139.4%)

PHY-1001 : Congestion index: top1 = 49.94, top5 = 44.63, top10 = 41.46, top15 = 39.43.
OPT-1001 : End congestion update;  1.715002s wall, 2.296875s user + 0.000000s system = 2.296875s CPU (133.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 21983 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.908043s wall, 0.906250s user + 0.000000s system = 0.906250s CPU (99.8%)

OPT-0007 : Start: WNS 4312 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.629806s wall, 3.218750s user + 0.000000s system = 3.218750s CPU (122.4%)

OPT-1001 : Current memory(MB): used = 701, reserve = 680, peak = 719.
OPT-1001 : End physical optimization;  13.771787s wall, 15.312500s user + 0.546875s system = 15.859375s CPU (115.2%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5711 LUT to BLE ...
SYN-4008 : Packed 5711 LUT and 2724 SEQ to BLE.
SYN-4003 : Packing 9491 remaining SEQ's ...
SYN-4005 : Packed 3407 SEQ with LUT/SLICE
SYN-4006 : 95 single LUT's are left
SYN-4006 : 6084 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11795/13646 primitive instances ...
PHY-3001 : End packing;  3.158409s wall, 3.140625s user + 0.015625s system = 3.156250s CPU (99.9%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8013 instances
RUN-1001 : 3961 mslices, 3961 lslices, 59 pads, 26 brams, 0 dsps
RUN-1001 : There are total 19310 nets
RUN-1001 : 13638 nets have 2 pins
RUN-1001 : 4266 nets have [3 - 5] pins
RUN-1001 : 898 nets have [6 - 10] pins
RUN-1001 : 372 nets have [11 - 20] pins
RUN-1001 : 126 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
PHY-3001 : design contains 8011 instances, 7922 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Cell area utilization is 84%
PHY-3001 : After packing: Len = 518148, Over = 381
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7987/19310.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 648296, over cnt = 1568(4%), over = 2493, worst = 7
PHY-1002 : len = 654176, over cnt = 992(2%), over = 1348, worst = 7
PHY-1002 : len = 662264, over cnt = 490(1%), over = 656, worst = 7
PHY-1002 : len = 668168, over cnt = 207(0%), over = 290, worst = 5
PHY-1002 : len = 673856, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.184474s wall, 1.953125s user + 0.000000s system = 1.953125s CPU (164.9%)

PHY-1001 : Congestion index: top1 = 50.39, top5 = 44.57, top10 = 40.86, top15 = 38.61.
PHY-3001 : End congestion estimation;  1.527718s wall, 2.312500s user + 0.000000s system = 2.312500s CPU (151.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67059, tnet num: 19308, tinst num: 8011, tnode num: 90799, tedge num: 110483.
TMR-2509 : Cut connection from COM2/reg5_syn_216.b[0] to COM2/reg5_syn_216.f[0].
TMR-2509 : Cut connection from COM2/reg5_syn_216.a[0] to COM2/reg5_syn_216.f[0].
TMR-2509 : Cut connection from COM2/reg5_syn_216.c[0] to COM2/reg5_syn_216.f[0].
TMR-2509 : Cut connection from FMC/top_state[0]_syn_84.e[1] to FMC/top_state[0]_syn_84.f[1].
TMR-2509 : Cut connection from FMC/top_state[0]_syn_84.d[1] to FMC/top_state[0]_syn_84.f[1].
TMR-2509 : Cut connection from FMC/top_state[0]_syn_84.c[1] to FMC/top_state[0]_syn_84.f[1].
TMR-2509 : Cut connection from FMC/top_state[0]_syn_84.a[1] to FMC/top_state[0]_syn_84.f[1].
TMR-2509 : Cut connection from COM2/reg5_syn_224.a[0] to COM2/reg5_syn_224.f[0].
TMR-2509 : Cut connection from COM2/reg5_syn_224.b[0] to COM2/reg5_syn_224.f[0].
TMR-2509 : Cut connection from COM2/reg5_syn_224.c[0] to COM2/reg5_syn_224.f[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.b[1] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.b[0] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.c[1] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.c[0] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.mi[0] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.d[1] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.d[0] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.b[1] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.b[0] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.c[1] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.c[0] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.mi[0] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.d[1] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.d[0] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from DATA/reg84_syn_50.a[1] to DATA/reg84_syn_50.f[1].
TMR-2509 : Cut connection from DATA/reg84_syn_50.b[1] to DATA/reg84_syn_50.f[1].
TMR-2509 : Cut connection from DATA/reg84_syn_50.c[1] to DATA/reg84_syn_50.f[1].
TMR-2509 : Cut connection from FMC_data[11]_syn_14.b[0] to FMC_data[11]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[11]_syn_14.c[0] to FMC_data[11]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[11]_syn_14.e[0] to FMC_data[11]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[11]_syn_14.d[0] to FMC_data[11]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[11]_syn_14.b[0] to FMC_data[11]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[11]_syn_14.c[0] to FMC_data[11]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[11]_syn_14.e[0] to FMC_data[11]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[11]_syn_14.d[0] to FMC_data[11]_syn_14.f[0].
TMR-2509 : Cut connection from DATA/reg115_syn_114.a[0] to DATA/reg115_syn_114.f[0].
TMR-2509 : Cut connection from DATA/reg115_syn_114.b[0] to DATA/reg115_syn_114.f[0].
TMR-2509 : Cut connection from DATA/reg115_syn_114.c[0] to DATA/reg115_syn_114.f[0].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.b[1] to FMC_data[1]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.c[1] to FMC_data[1]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.e[1] to FMC_data[1]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.d[1] to FMC_data[1]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.b[1] to FMC_data[1]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.c[1] to FMC_data[1]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.e[1] to FMC_data[1]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.d[1] to FMC_data[1]_syn_14.f[1].
TMR-2509 : Cut connection from DATA/reg29_syn_155.a[1] to DATA/reg29_syn_155.f[1].
TMR-2509 : Cut connection from DATA/reg29_syn_155.b[1] to DATA/reg29_syn_155.f[1].
TMR-2509 : Cut connection from DATA/reg29_syn_155.c[1] to DATA/reg29_syn_155.f[1].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.b[1] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.b[0] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.c[1] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.c[0] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.mi[0] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.d[0] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.d[1] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.b[1] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.b[0] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.c[1] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.c[0] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.mi[0] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.d[0] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.d[1] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_213.a[1] to COM2/uart_com2/reg1_syn_213.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_213.b[1] to COM2/uart_com2/reg1_syn_213.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_213.c[1] to COM2/uart_com2/reg1_syn_213.f[1].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.b[0] to FMC_data[10]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.c[0] to FMC_data[10]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.e[0] to FMC_data[10]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.d[0] to FMC_data[10]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.b[0] to FMC_data[10]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.c[0] to FMC_data[10]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.e[0] to FMC_data[10]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.d[0] to FMC_data[10]_syn_14.f[0].
TMR-2509 : Cut connection from COM2/head_com2/reg3_syn_154.d[1] to COM2/head_com2/reg3_syn_154.f[1].
TMR-2509 : Cut connection from FMC/top_state[0]_syn_82.a[1] to FMC/top_state[0]_syn_82.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_323.d[1] to DATA/reg9_syn_323.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_323.b[1] to DATA/reg9_syn_323.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_323.c[1] to DATA/reg9_syn_323.f[1].
TMR-2509 : Cut connection from COM2/head_com2/reg3_syn_143.b[0] to COM2/head_com2/reg3_syn_143.f[0].
TMR-2509 : Cut connection from COM2/head_com2/reg3_syn_143.a[0] to COM2/head_com2/reg3_syn_143.f[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_221.a[0] to COM2/uart_com2/reg1_syn_221.f[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_221.b[0] to COM2/uart_com2/reg1_syn_221.f[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_221.c[0] to COM2/uart_com2/reg1_syn_221.f[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.b[1] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.b[0] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.c[1] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.c[0] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.mi[0] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.d[0] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.d[1] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.b[1] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.b[0] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.c[1] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.c[0] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.mi[0] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.d[0] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.d[1] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from DATA/reg128_syn_53.a[1] to DATA/reg128_syn_53.f[1].
TMR-2509 : Cut connection from DATA/reg128_syn_53.b[1] to DATA/reg128_syn_53.f[1].
TMR-2509 : Cut connection from DATA/reg128_syn_53.c[1] to DATA/reg128_syn_53.f[1].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.b[0] to FMC_data[1]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.c[0] to FMC_data[1]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.e[0] to FMC_data[1]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.d[0] to FMC_data[1]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.b[0] to FMC_data[1]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.c[0] to FMC_data[1]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.e[0] to FMC_data[1]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.d[0] to FMC_data[1]_syn_14.f[0].
TMR-2509 : Cut connection from DATA/reg45_syn_153.d[1] to DATA/reg45_syn_153.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg70_syn_118.d[1] to COM2/uart_com2/reg70_syn_118.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg65_syn_142.d[1] to COM2/uart_com2/reg65_syn_142.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg63_syn_139.d[1] to COM2/uart_com2/reg63_syn_139.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg7_syn_127.d[0] to COM2/uart_com2/reg7_syn_127.f[0].
TMR-2509 : Cut connection from DATA/reg36_syn_274.d[0] to DATA/reg36_syn_274.f[0].
TMR-2509 : Cut connection from DATA/reg83_syn_171.a[1] to DATA/reg83_syn_171.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_171.d[1] to DATA/reg83_syn_171.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_171.c[1] to DATA/reg83_syn_171.f[1].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.c[1] to FMC_data[15]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.b[1] to FMC_data[15]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.d[1] to FMC_data[15]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.c[1] to FMC_data[15]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.b[1] to FMC_data[15]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.d[1] to FMC_data[15]_syn_14.f[1].
TMR-2509 : Cut connection from DATA/reg18_syn_129.d[0] to DATA/reg18_syn_129.f[0].
TMR-2509 : Cut connection from DATA/reg41_syn_78.d[1] to DATA/reg41_syn_78.f[1].
TMR-2509 : Cut connection from DATA/reg13_syn_129.b[0] to DATA/reg13_syn_129.f[0].
TMR-2509 : Cut connection from DATA/reg13_syn_129.d[0] to DATA/reg13_syn_129.f[0].
TMR-2509 : Cut connection from DATA/reg13_syn_129.c[0] to DATA/reg13_syn_129.f[0].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.c[0] to FMC_data[7]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.d[0] to FMC_data[7]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.b[0] to FMC_data[7]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.c[0] to FMC_data[7]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.d[0] to FMC_data[7]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.b[0] to FMC_data[7]_syn_14.f[0].
TMR-2509 : Cut connection from DATA/reg114_syn_122.a[0] to DATA/reg114_syn_122.f[0].
TMR-2509 : Cut connection from COM2/STADOP_com2/reg3_syn_111.d[0] to COM2/STADOP_com2/reg3_syn_111.f[0].
TMR-2509 : Cut connection from DATA/reg2_syn_144.d[1] to DATA/reg2_syn_144.f[1].
TMR-2509 : Cut connection from FMC_data[12]_syn_14.b[1] to FMC_data[12]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[12]_syn_14.a[1] to FMC_data[12]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[12]_syn_14.d[1] to FMC_data[12]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[12]_syn_14.b[1] to FMC_data[12]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[12]_syn_14.a[1] to FMC_data[12]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[12]_syn_14.d[1] to FMC_data[12]_syn_14.f[1].
TMR-2509 : Cut connection from DATA/reg4_syn_185.d[1] to DATA/reg4_syn_185.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg65_syn_146.d[0] to COM2/uart_com2/reg65_syn_146.f[0].
TMR-2509 : Cut connection from DATA/reg29_syn_136.a[1] to DATA/reg29_syn_136.f[1].
TMR-2509 : Cut connection from DATA/reg29_syn_136.d[1] to DATA/reg29_syn_136.f[1].
TMR-2509 : Cut connection from DATA/reg29_syn_136.c[1] to DATA/reg29_syn_136.f[1].
TMR-2509 : Cut connection from FMC_data[5]_syn_14.c[0] to FMC_data[5]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[5]_syn_14.d[0] to FMC_data[5]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[5]_syn_14.b[0] to FMC_data[5]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[5]_syn_14.c[0] to FMC_data[5]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[5]_syn_14.d[0] to FMC_data[5]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[5]_syn_14.b[0] to FMC_data[5]_syn_14.f[0].
TMR-2509 : Cut connection from DATA/reg128_syn_51.d[1] to DATA/reg128_syn_51.f[1].
TMR-2509 : Cut connection from DATA/reg41_syn_81.d[1] to DATA/reg41_syn_81.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_200.b[1] to DATA/reg83_syn_200.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_200.d[1] to DATA/reg83_syn_200.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_200.c[1] to DATA/reg83_syn_200.f[1].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.c[0] to FMC_data[15]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.d[0] to FMC_data[15]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.b[0] to FMC_data[15]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.c[0] to FMC_data[15]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.d[0] to FMC_data[15]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.b[0] to FMC_data[15]_syn_14.f[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg65_syn_142.d[0] to COM2/uart_com2/reg65_syn_142.f[0].
TMR-2509 : Cut connection from IIC/reg8_syn_94.d[0] to IIC/reg8_syn_94.f[0].
TMR-2509 : Cut connection from COM2/STADOP_com2/reg3_syn_111.d[1] to COM2/STADOP_com2/reg3_syn_111.f[1].
TMR-2509 : Cut connection from COM2/head_com2/reg2_syn_131.d[0] to COM2/head_com2/reg2_syn_131.f[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg58_syn_124.d[1] to COM2/uart_com2/reg58_syn_124.f[1].
TMR-2509 : Cut connection from COM2/reg5_syn_216.c[1] to COM2/reg5_syn_216.f[1].
TMR-2509 : Cut connection from COM2/reg5_syn_216.b[1] to COM2/reg5_syn_216.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_337.b[0] to DATA/reg9_syn_337.f[0].
TMR-2509 : Cut connection from DATA/reg9_syn_337.c[0] to DATA/reg9_syn_337.f[0].
TMR-2509 : Cut connection from DATA/reg2_syn_154.c[1] to DATA/reg2_syn_154.f[1].
TMR-2509 : Cut connection from FMC/top_state[1]_syn_21.b[1] to FMC/top_state[1]_syn_21.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_337.d[1] to DATA/reg9_syn_337.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_337.c[1] to DATA/reg9_syn_337.f[1].
TMR-2509 : Cut connection from DATA/reg2_syn_154.d[0] to DATA/reg2_syn_154.f[0].
TMR-2509 : Cut connection from FMC_data[12]_syn_14.d[0] to FMC_data[12]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[12]_syn_14.b[0] to FMC_data[12]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[12]_syn_14.d[0] to FMC_data[12]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[12]_syn_14.b[0] to FMC_data[12]_syn_14.f[0].
TMR-2509 : Cut connection from DATA/done_div_reg_syn_108.d[0] to DATA/done_div_reg_syn_108.f[0].
TMR-2509 : Cut connection from DATA/reg83_syn_210.d[0] to DATA/reg83_syn_210.f[0].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.d[1] to FMC_data[10]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.b[1] to FMC_data[10]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.d[1] to FMC_data[10]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.b[1] to FMC_data[10]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.d[1] to FMC_data[7]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.b[1] to FMC_data[7]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.a[1] to FMC_data[7]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.d[1] to FMC_data[7]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.b[1] to FMC_data[7]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.a[1] to FMC_data[7]_syn_14.f[1].
TMR-2509 : Cut connection from DATA/reg114_syn_126.d[0] to DATA/reg114_syn_126.f[0].
TMR-2509 : Cut connection from FMC_data[5]_syn_14.b[1] to FMC_data[5]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[5]_syn_14.a[1] to FMC_data[5]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[5]_syn_14.b[1] to FMC_data[5]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[5]_syn_14.a[1] to FMC_data[5]_syn_14.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_197.d[1] to DATA/reg83_syn_197.f[1].
TMR-2509 : Cut connection from FMC/top_state[3]_syn_11.d[1] to FMC/top_state[3]_syn_11.f[1].
TMR-2507 : Eliminate loop in the timing graph, delete 203 tedges.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.764817s wall, 1.765625s user + 0.000000s system = 1.765625s CPU (100.0%)

RUN-1004 : used memory is 602 MB, reserved memory is 596 MB, peak memory is 719 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19308 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.740806s wall, 2.687500s user + 0.046875s system = 2.734375s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.37778e-05
PHY-3002 : Step(193): len = 514353, overlap = 371.25
PHY-3002 : Step(194): len = 509338, overlap = 375
PHY-3002 : Step(195): len = 505795, overlap = 381
PHY-3002 : Step(196): len = 504487, overlap = 402
PHY-3002 : Step(197): len = 501165, overlap = 406.75
PHY-3002 : Step(198): len = 499671, overlap = 410.25
PHY-3002 : Step(199): len = 497590, overlap = 417
PHY-3002 : Step(200): len = 495676, overlap = 410
PHY-3002 : Step(201): len = 494053, overlap = 415
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.75557e-05
PHY-3002 : Step(202): len = 497515, overlap = 404.75
PHY-3002 : Step(203): len = 502701, overlap = 398.75
PHY-3002 : Step(204): len = 503652, overlap = 388
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000175111
PHY-3002 : Step(205): len = 510124, overlap = 379
PHY-3002 : Step(206): len = 516682, overlap = 364
PHY-3002 : Step(207): len = 515406, overlap = 364
PHY-3002 : Step(208): len = 515215, overlap = 359.75
PHY-3002 : Step(209): len = 515987, overlap = 356.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.770057s wall, 0.796875s user + 0.953125s system = 1.750000s CPU (227.3%)

PHY-3001 : Trial Legalized: Len = 626773
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 683/19310.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 716504, over cnt = 2355(6%), over = 3897, worst = 10
PHY-1002 : len = 730264, over cnt = 1412(4%), over = 2056, worst = 7
PHY-1002 : len = 753728, over cnt = 299(0%), over = 356, worst = 5
PHY-1002 : len = 758248, over cnt = 74(0%), over = 88, worst = 3
PHY-1002 : len = 760048, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.013336s wall, 3.296875s user + 0.046875s system = 3.343750s CPU (166.1%)

PHY-1001 : Congestion index: top1 = 49.27, top5 = 44.54, top10 = 42.14, top15 = 40.56.
PHY-3001 : End congestion estimation;  2.410260s wall, 3.703125s user + 0.046875s system = 3.750000s CPU (155.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19308 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.943536s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (99.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000176424
PHY-3002 : Step(210): len = 582889, overlap = 87.5
PHY-3002 : Step(211): len = 565538, overlap = 128.25
PHY-3002 : Step(212): len = 555448, overlap = 160.25
PHY-3002 : Step(213): len = 547925, overlap = 206.75
PHY-3002 : Step(214): len = 544464, overlap = 228
PHY-3002 : Step(215): len = 542586, overlap = 248
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000352849
PHY-3002 : Step(216): len = 546084, overlap = 242.25
PHY-3002 : Step(217): len = 550068, overlap = 241
PHY-3002 : Step(218): len = 550412, overlap = 240.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(219): len = 553517, overlap = 236
PHY-3002 : Step(220): len = 561206, overlap = 239
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.032043s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (97.5%)

PHY-3001 : Legalized: Len = 602206, Over = 0
PHY-3001 : Spreading special nets. 45 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.096499s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (97.2%)

PHY-3001 : 72 instances has been re-located, deltaX = 35, deltaY = 28, maxDist = 2.
PHY-3001 : Final: Len = 603352, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67059, tnet num: 19308, tinst num: 8011, tnode num: 90799, tedge num: 110483.
TMR-2509 : Cut connection from COM2/reg5_syn_216.b[0] to COM2/reg5_syn_216.f[0].
TMR-2509 : Cut connection from COM2/reg5_syn_216.a[0] to COM2/reg5_syn_216.f[0].
TMR-2509 : Cut connection from COM2/reg5_syn_216.c[0] to COM2/reg5_syn_216.f[0].
TMR-2509 : Cut connection from FMC/top_state[0]_syn_84.e[1] to FMC/top_state[0]_syn_84.f[1].
TMR-2509 : Cut connection from FMC/top_state[0]_syn_84.d[1] to FMC/top_state[0]_syn_84.f[1].
TMR-2509 : Cut connection from FMC/top_state[0]_syn_84.c[1] to FMC/top_state[0]_syn_84.f[1].
TMR-2509 : Cut connection from FMC/top_state[0]_syn_84.a[1] to FMC/top_state[0]_syn_84.f[1].
TMR-2509 : Cut connection from COM2/reg5_syn_224.a[0] to COM2/reg5_syn_224.f[0].
TMR-2509 : Cut connection from COM2/reg5_syn_224.b[0] to COM2/reg5_syn_224.f[0].
TMR-2509 : Cut connection from COM2/reg5_syn_224.c[0] to COM2/reg5_syn_224.f[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.b[1] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.b[0] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.c[1] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.c[0] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.mi[0] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.d[1] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.d[0] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.b[1] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.b[0] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.c[1] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.c[0] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.mi[0] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.d[1] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.d[0] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from DATA/reg84_syn_50.a[1] to DATA/reg84_syn_50.f[1].
TMR-2509 : Cut connection from DATA/reg84_syn_50.b[1] to DATA/reg84_syn_50.f[1].
TMR-2509 : Cut connection from DATA/reg84_syn_50.c[1] to DATA/reg84_syn_50.f[1].
TMR-2509 : Cut connection from FMC_data[11]_syn_14.b[0] to FMC_data[11]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[11]_syn_14.c[0] to FMC_data[11]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[11]_syn_14.e[0] to FMC_data[11]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[11]_syn_14.d[0] to FMC_data[11]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[11]_syn_14.b[0] to FMC_data[11]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[11]_syn_14.c[0] to FMC_data[11]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[11]_syn_14.e[0] to FMC_data[11]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[11]_syn_14.d[0] to FMC_data[11]_syn_14.f[0].
TMR-2509 : Cut connection from DATA/reg115_syn_114.a[0] to DATA/reg115_syn_114.f[0].
TMR-2509 : Cut connection from DATA/reg115_syn_114.b[0] to DATA/reg115_syn_114.f[0].
TMR-2509 : Cut connection from DATA/reg115_syn_114.c[0] to DATA/reg115_syn_114.f[0].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.b[1] to FMC_data[1]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.c[1] to FMC_data[1]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.e[1] to FMC_data[1]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.d[1] to FMC_data[1]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.b[1] to FMC_data[1]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.c[1] to FMC_data[1]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.e[1] to FMC_data[1]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.d[1] to FMC_data[1]_syn_14.f[1].
TMR-2509 : Cut connection from DATA/reg29_syn_155.a[1] to DATA/reg29_syn_155.f[1].
TMR-2509 : Cut connection from DATA/reg29_syn_155.b[1] to DATA/reg29_syn_155.f[1].
TMR-2509 : Cut connection from DATA/reg29_syn_155.c[1] to DATA/reg29_syn_155.f[1].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.b[1] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.b[0] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.c[1] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.c[0] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.mi[0] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.d[0] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.d[1] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.b[1] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.b[0] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.c[1] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.c[0] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.mi[0] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.d[0] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.d[1] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_213.a[1] to COM2/uart_com2/reg1_syn_213.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_213.b[1] to COM2/uart_com2/reg1_syn_213.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_213.c[1] to COM2/uart_com2/reg1_syn_213.f[1].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.b[0] to FMC_data[10]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.c[0] to FMC_data[10]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.e[0] to FMC_data[10]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.d[0] to FMC_data[10]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.b[0] to FMC_data[10]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.c[0] to FMC_data[10]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.e[0] to FMC_data[10]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.d[0] to FMC_data[10]_syn_14.f[0].
TMR-2509 : Cut connection from COM2/head_com2/reg3_syn_154.d[1] to COM2/head_com2/reg3_syn_154.f[1].
TMR-2509 : Cut connection from FMC/top_state[0]_syn_82.a[1] to FMC/top_state[0]_syn_82.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_323.d[1] to DATA/reg9_syn_323.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_323.b[1] to DATA/reg9_syn_323.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_323.c[1] to DATA/reg9_syn_323.f[1].
TMR-2509 : Cut connection from COM2/head_com2/reg3_syn_143.b[0] to COM2/head_com2/reg3_syn_143.f[0].
TMR-2509 : Cut connection from COM2/head_com2/reg3_syn_143.a[0] to COM2/head_com2/reg3_syn_143.f[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_221.a[0] to COM2/uart_com2/reg1_syn_221.f[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_221.b[0] to COM2/uart_com2/reg1_syn_221.f[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_221.c[0] to COM2/uart_com2/reg1_syn_221.f[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.b[1] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.b[0] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.c[1] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.c[0] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.mi[0] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.d[0] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.d[1] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.b[1] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.b[0] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.c[1] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.c[0] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.mi[0] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.d[0] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.d[1] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from DATA/reg128_syn_53.a[1] to DATA/reg128_syn_53.f[1].
TMR-2509 : Cut connection from DATA/reg128_syn_53.b[1] to DATA/reg128_syn_53.f[1].
TMR-2509 : Cut connection from DATA/reg128_syn_53.c[1] to DATA/reg128_syn_53.f[1].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.b[0] to FMC_data[1]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.c[0] to FMC_data[1]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.e[0] to FMC_data[1]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.d[0] to FMC_data[1]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.b[0] to FMC_data[1]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.c[0] to FMC_data[1]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.e[0] to FMC_data[1]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.d[0] to FMC_data[1]_syn_14.f[0].
TMR-2509 : Cut connection from DATA/reg45_syn_153.d[1] to DATA/reg45_syn_153.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg70_syn_118.d[1] to COM2/uart_com2/reg70_syn_118.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg65_syn_142.d[1] to COM2/uart_com2/reg65_syn_142.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg63_syn_139.d[1] to COM2/uart_com2/reg63_syn_139.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg7_syn_127.d[0] to COM2/uart_com2/reg7_syn_127.f[0].
TMR-2509 : Cut connection from DATA/reg36_syn_274.d[0] to DATA/reg36_syn_274.f[0].
TMR-2509 : Cut connection from DATA/reg83_syn_171.a[1] to DATA/reg83_syn_171.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_171.d[1] to DATA/reg83_syn_171.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_171.c[1] to DATA/reg83_syn_171.f[1].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.c[1] to FMC_data[15]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.b[1] to FMC_data[15]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.d[1] to FMC_data[15]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.c[1] to FMC_data[15]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.b[1] to FMC_data[15]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.d[1] to FMC_data[15]_syn_14.f[1].
TMR-2509 : Cut connection from DATA/reg18_syn_129.d[0] to DATA/reg18_syn_129.f[0].
TMR-2509 : Cut connection from DATA/reg41_syn_78.d[1] to DATA/reg41_syn_78.f[1].
TMR-2509 : Cut connection from DATA/reg13_syn_129.b[0] to DATA/reg13_syn_129.f[0].
TMR-2509 : Cut connection from DATA/reg13_syn_129.d[0] to DATA/reg13_syn_129.f[0].
TMR-2509 : Cut connection from DATA/reg13_syn_129.c[0] to DATA/reg13_syn_129.f[0].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.c[0] to FMC_data[7]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.d[0] to FMC_data[7]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.b[0] to FMC_data[7]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.c[0] to FMC_data[7]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.d[0] to FMC_data[7]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.b[0] to FMC_data[7]_syn_14.f[0].
TMR-2509 : Cut connection from DATA/reg114_syn_122.a[0] to DATA/reg114_syn_122.f[0].
TMR-2509 : Cut connection from COM2/STADOP_com2/reg3_syn_111.d[0] to COM2/STADOP_com2/reg3_syn_111.f[0].
TMR-2509 : Cut connection from DATA/reg2_syn_144.d[1] to DATA/reg2_syn_144.f[1].
TMR-2509 : Cut connection from FMC_data[12]_syn_14.b[1] to FMC_data[12]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[12]_syn_14.a[1] to FMC_data[12]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[12]_syn_14.d[1] to FMC_data[12]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[12]_syn_14.b[1] to FMC_data[12]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[12]_syn_14.a[1] to FMC_data[12]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[12]_syn_14.d[1] to FMC_data[12]_syn_14.f[1].
TMR-2509 : Cut connection from DATA/reg4_syn_185.d[1] to DATA/reg4_syn_185.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg65_syn_146.d[0] to COM2/uart_com2/reg65_syn_146.f[0].
TMR-2509 : Cut connection from DATA/reg29_syn_136.a[1] to DATA/reg29_syn_136.f[1].
TMR-2509 : Cut connection from DATA/reg29_syn_136.d[1] to DATA/reg29_syn_136.f[1].
TMR-2509 : Cut connection from DATA/reg29_syn_136.c[1] to DATA/reg29_syn_136.f[1].
TMR-2509 : Cut connection from FMC_data[5]_syn_14.c[0] to FMC_data[5]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[5]_syn_14.d[0] to FMC_data[5]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[5]_syn_14.b[0] to FMC_data[5]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[5]_syn_14.c[0] to FMC_data[5]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[5]_syn_14.d[0] to FMC_data[5]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[5]_syn_14.b[0] to FMC_data[5]_syn_14.f[0].
TMR-2509 : Cut connection from DATA/reg128_syn_51.d[1] to DATA/reg128_syn_51.f[1].
TMR-2509 : Cut connection from DATA/reg41_syn_81.d[1] to DATA/reg41_syn_81.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_200.b[1] to DATA/reg83_syn_200.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_200.d[1] to DATA/reg83_syn_200.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_200.c[1] to DATA/reg83_syn_200.f[1].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.c[0] to FMC_data[15]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.d[0] to FMC_data[15]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.b[0] to FMC_data[15]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.c[0] to FMC_data[15]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.d[0] to FMC_data[15]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.b[0] to FMC_data[15]_syn_14.f[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg65_syn_142.d[0] to COM2/uart_com2/reg65_syn_142.f[0].
TMR-2509 : Cut connection from IIC/reg8_syn_94.d[0] to IIC/reg8_syn_94.f[0].
TMR-2509 : Cut connection from COM2/STADOP_com2/reg3_syn_111.d[1] to COM2/STADOP_com2/reg3_syn_111.f[1].
TMR-2509 : Cut connection from COM2/head_com2/reg2_syn_131.d[0] to COM2/head_com2/reg2_syn_131.f[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg58_syn_124.d[1] to COM2/uart_com2/reg58_syn_124.f[1].
TMR-2509 : Cut connection from COM2/reg5_syn_216.c[1] to COM2/reg5_syn_216.f[1].
TMR-2509 : Cut connection from COM2/reg5_syn_216.b[1] to COM2/reg5_syn_216.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_337.b[0] to DATA/reg9_syn_337.f[0].
TMR-2509 : Cut connection from DATA/reg9_syn_337.c[0] to DATA/reg9_syn_337.f[0].
TMR-2509 : Cut connection from DATA/reg2_syn_154.c[1] to DATA/reg2_syn_154.f[1].
TMR-2509 : Cut connection from FMC/top_state[1]_syn_21.b[1] to FMC/top_state[1]_syn_21.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_337.d[1] to DATA/reg9_syn_337.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_337.c[1] to DATA/reg9_syn_337.f[1].
TMR-2509 : Cut connection from DATA/reg2_syn_154.d[0] to DATA/reg2_syn_154.f[0].
TMR-2509 : Cut connection from FMC_data[12]_syn_14.d[0] to FMC_data[12]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[12]_syn_14.b[0] to FMC_data[12]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[12]_syn_14.d[0] to FMC_data[12]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[12]_syn_14.b[0] to FMC_data[12]_syn_14.f[0].
TMR-2509 : Cut connection from DATA/done_div_reg_syn_108.d[0] to DATA/done_div_reg_syn_108.f[0].
TMR-2509 : Cut connection from DATA/reg83_syn_210.d[0] to DATA/reg83_syn_210.f[0].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.d[1] to FMC_data[10]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.b[1] to FMC_data[10]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.d[1] to FMC_data[10]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.b[1] to FMC_data[10]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.d[1] to FMC_data[7]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.b[1] to FMC_data[7]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.a[1] to FMC_data[7]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.d[1] to FMC_data[7]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.b[1] to FMC_data[7]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.a[1] to FMC_data[7]_syn_14.f[1].
TMR-2509 : Cut connection from DATA/reg114_syn_126.d[0] to DATA/reg114_syn_126.f[0].
TMR-2509 : Cut connection from FMC_data[5]_syn_14.b[1] to FMC_data[5]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[5]_syn_14.a[1] to FMC_data[5]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[5]_syn_14.b[1] to FMC_data[5]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[5]_syn_14.a[1] to FMC_data[5]_syn_14.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_197.d[1] to DATA/reg83_syn_197.f[1].
TMR-2509 : Cut connection from FMC/top_state[3]_syn_11.d[1] to FMC/top_state[3]_syn_11.f[1].
TMR-2507 : Eliminate loop in the timing graph, delete 203 tedges.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.014970s wall, 2.000000s user + 0.000000s system = 2.000000s CPU (99.3%)

RUN-1004 : used memory is 628 MB, reserved memory is 620 MB, peak memory is 719 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3728/19310.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 698968, over cnt = 2189(6%), over = 3382, worst = 7
PHY-1002 : len = 710480, over cnt = 1285(3%), over = 1711, worst = 6
PHY-1002 : len = 729312, over cnt = 235(0%), over = 286, worst = 3
PHY-1002 : len = 733024, over cnt = 59(0%), over = 70, worst = 3
PHY-1002 : len = 734360, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.749717s wall, 2.953125s user + 0.078125s system = 3.031250s CPU (173.2%)

PHY-1001 : Congestion index: top1 = 46.44, top5 = 42.29, top10 = 39.94, top15 = 38.46.
PHY-1001 : End incremental global routing;  2.091393s wall, 3.296875s user + 0.078125s system = 3.375000s CPU (161.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19308 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.949054s wall, 0.921875s user + 0.031250s system = 0.953125s CPU (100.4%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 7946 has valid locations, 15 needs to be replaced
PHY-3001 : design contains 8024 instances, 7935 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 605238
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17391/19327.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 736184, over cnt = 20(0%), over = 20, worst = 1
PHY-1002 : len = 736216, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 736280, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 736328, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 736360, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.711654s wall, 0.781250s user + 0.062500s system = 0.843750s CPU (118.6%)

PHY-1001 : Congestion index: top1 = 46.51, top5 = 42.30, top10 = 39.95, top15 = 38.48.
PHY-3001 : End congestion estimation;  1.028246s wall, 1.078125s user + 0.062500s system = 1.140625s CPU (110.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67147, tnet num: 19325, tinst num: 8024, tnode num: 90906, tedge num: 110588.
TMR-2509 : Cut connection from COM2/reg5_syn_216.b[0] to COM2/reg5_syn_216.f[0].
TMR-2509 : Cut connection from COM2/reg5_syn_216.a[0] to COM2/reg5_syn_216.f[0].
TMR-2509 : Cut connection from COM2/reg5_syn_216.c[0] to COM2/reg5_syn_216.f[0].
TMR-2509 : Cut connection from FMC/top_state[0]_syn_84.e[1] to FMC/top_state[0]_syn_84.f[1].
TMR-2509 : Cut connection from FMC/top_state[0]_syn_84.d[1] to FMC/top_state[0]_syn_84.f[1].
TMR-2509 : Cut connection from FMC/top_state[0]_syn_84.c[1] to FMC/top_state[0]_syn_84.f[1].
TMR-2509 : Cut connection from FMC/top_state[0]_syn_84.a[1] to FMC/top_state[0]_syn_84.f[1].
TMR-2509 : Cut connection from COM2/reg5_syn_224.a[0] to COM2/reg5_syn_224.f[0].
TMR-2509 : Cut connection from COM2/reg5_syn_224.b[0] to COM2/reg5_syn_224.f[0].
TMR-2509 : Cut connection from COM2/reg5_syn_224.c[0] to COM2/reg5_syn_224.f[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.b[1] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.b[0] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.c[1] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.c[0] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.mi[0] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.d[1] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.d[0] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.b[1] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.b[0] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.c[1] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.c[0] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.mi[0] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.d[1] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.d[0] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from DATA/reg84_syn_50.a[1] to DATA/reg84_syn_50.f[1].
TMR-2509 : Cut connection from DATA/reg84_syn_50.b[1] to DATA/reg84_syn_50.f[1].
TMR-2509 : Cut connection from DATA/reg84_syn_50.c[1] to DATA/reg84_syn_50.f[1].
TMR-2509 : Cut connection from FMC_data[11]_syn_14.b[0] to FMC_data[11]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[11]_syn_14.c[0] to FMC_data[11]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[11]_syn_14.e[0] to FMC_data[11]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[11]_syn_14.d[0] to FMC_data[11]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[11]_syn_14.b[0] to FMC_data[11]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[11]_syn_14.c[0] to FMC_data[11]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[11]_syn_14.e[0] to FMC_data[11]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[11]_syn_14.d[0] to FMC_data[11]_syn_14.f[0].
TMR-2509 : Cut connection from DATA/reg115_syn_114.a[0] to DATA/reg115_syn_114.f[0].
TMR-2509 : Cut connection from DATA/reg115_syn_114.b[0] to DATA/reg115_syn_114.f[0].
TMR-2509 : Cut connection from DATA/reg115_syn_114.c[0] to DATA/reg115_syn_114.f[0].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.b[1] to FMC_data[1]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.c[1] to FMC_data[1]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.e[1] to FMC_data[1]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.d[1] to FMC_data[1]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.b[1] to FMC_data[1]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.c[1] to FMC_data[1]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.e[1] to FMC_data[1]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.d[1] to FMC_data[1]_syn_14.f[1].
TMR-2509 : Cut connection from DATA/reg29_syn_155.a[1] to DATA/reg29_syn_155.f[1].
TMR-2509 : Cut connection from DATA/reg29_syn_155.b[1] to DATA/reg29_syn_155.f[1].
TMR-2509 : Cut connection from DATA/reg29_syn_155.c[1] to DATA/reg29_syn_155.f[1].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.b[1] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.b[0] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.c[1] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.c[0] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.mi[0] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.d[0] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.d[1] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.b[1] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.b[0] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.c[1] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.c[0] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.mi[0] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.d[0] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.d[1] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_213.a[1] to COM2/uart_com2/reg1_syn_213.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_213.b[1] to COM2/uart_com2/reg1_syn_213.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_213.c[1] to COM2/uart_com2/reg1_syn_213.f[1].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.b[0] to FMC_data[10]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.c[0] to FMC_data[10]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.e[0] to FMC_data[10]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.d[0] to FMC_data[10]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.b[0] to FMC_data[10]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.c[0] to FMC_data[10]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.e[0] to FMC_data[10]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.d[0] to FMC_data[10]_syn_14.f[0].
TMR-2509 : Cut connection from COM2/head_com2/reg3_syn_154.d[1] to COM2/head_com2/reg3_syn_154.f[1].
TMR-2509 : Cut connection from FMC/top_state[0]_syn_82.a[1] to FMC/top_state[0]_syn_82.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_323.d[1] to DATA/reg9_syn_323.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_323.b[1] to DATA/reg9_syn_323.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_323.c[1] to DATA/reg9_syn_323.f[1].
TMR-2509 : Cut connection from COM2/head_com2/reg3_syn_143.b[0] to COM2/head_com2/reg3_syn_143.f[0].
TMR-2509 : Cut connection from COM2/head_com2/reg3_syn_143.a[0] to COM2/head_com2/reg3_syn_143.f[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_221.a[0] to COM2/uart_com2/reg1_syn_221.f[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_221.b[0] to COM2/uart_com2/reg1_syn_221.f[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_221.c[0] to COM2/uart_com2/reg1_syn_221.f[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.b[1] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.b[0] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.c[1] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.c[0] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.mi[0] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.d[0] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.d[1] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.b[1] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.b[0] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.c[1] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.c[0] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.mi[0] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.d[0] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.d[1] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from DATA/reg128_syn_53.a[1] to DATA/reg128_syn_53.f[1].
TMR-2509 : Cut connection from DATA/reg128_syn_53.b[1] to DATA/reg128_syn_53.f[1].
TMR-2509 : Cut connection from DATA/reg128_syn_53.c[1] to DATA/reg128_syn_53.f[1].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.b[0] to FMC_data[1]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.c[0] to FMC_data[1]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.e[0] to FMC_data[1]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.d[0] to FMC_data[1]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.b[0] to FMC_data[1]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.c[0] to FMC_data[1]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.e[0] to FMC_data[1]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.d[0] to FMC_data[1]_syn_14.f[0].
TMR-2509 : Cut connection from DATA/reg45_syn_153.d[1] to DATA/reg45_syn_153.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg70_syn_118.d[1] to COM2/uart_com2/reg70_syn_118.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg65_syn_142.d[1] to COM2/uart_com2/reg65_syn_142.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg63_syn_139.d[1] to COM2/uart_com2/reg63_syn_139.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg7_syn_127.d[0] to COM2/uart_com2/reg7_syn_127.f[0].
TMR-2509 : Cut connection from DATA/reg36_syn_274.d[0] to DATA/reg36_syn_274.f[0].
TMR-2509 : Cut connection from DATA/reg83_syn_171.a[1] to DATA/reg83_syn_171.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_171.d[1] to DATA/reg83_syn_171.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_171.c[1] to DATA/reg83_syn_171.f[1].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.c[1] to FMC_data[15]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.b[1] to FMC_data[15]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.d[1] to FMC_data[15]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.c[1] to FMC_data[15]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.b[1] to FMC_data[15]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.d[1] to FMC_data[15]_syn_14.f[1].
TMR-2509 : Cut connection from DATA/reg18_syn_129.d[0] to DATA/reg18_syn_129.f[0].
TMR-2509 : Cut connection from DATA/reg41_syn_78.d[1] to DATA/reg41_syn_78.f[1].
TMR-2509 : Cut connection from DATA/reg13_syn_129.b[0] to DATA/reg13_syn_129.f[0].
TMR-2509 : Cut connection from DATA/reg13_syn_129.d[0] to DATA/reg13_syn_129.f[0].
TMR-2509 : Cut connection from DATA/reg13_syn_129.c[0] to DATA/reg13_syn_129.f[0].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.c[0] to FMC_data[7]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.d[0] to FMC_data[7]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.b[0] to FMC_data[7]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.c[0] to FMC_data[7]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.d[0] to FMC_data[7]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.b[0] to FMC_data[7]_syn_14.f[0].
TMR-2509 : Cut connection from DATA/reg114_syn_122.a[0] to DATA/reg114_syn_122.f[0].
TMR-2509 : Cut connection from COM2/STADOP_com2/reg3_syn_111.d[0] to COM2/STADOP_com2/reg3_syn_111.f[0].
TMR-2509 : Cut connection from DATA/reg2_syn_144.d[1] to DATA/reg2_syn_144.f[1].
TMR-2509 : Cut connection from FMC_data[12]_syn_14.b[1] to FMC_data[12]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[12]_syn_14.a[1] to FMC_data[12]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[12]_syn_14.d[1] to FMC_data[12]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[12]_syn_14.b[1] to FMC_data[12]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[12]_syn_14.a[1] to FMC_data[12]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[12]_syn_14.d[1] to FMC_data[12]_syn_14.f[1].
TMR-2509 : Cut connection from DATA/reg4_syn_185.d[1] to DATA/reg4_syn_185.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg65_syn_146.d[0] to COM2/uart_com2/reg65_syn_146.f[0].
TMR-2509 : Cut connection from DATA/reg29_syn_136.a[1] to DATA/reg29_syn_136.f[1].
TMR-2509 : Cut connection from DATA/reg29_syn_136.d[1] to DATA/reg29_syn_136.f[1].
TMR-2509 : Cut connection from DATA/reg29_syn_136.c[1] to DATA/reg29_syn_136.f[1].
TMR-2509 : Cut connection from FMC_data[5]_syn_14.c[0] to FMC_data[5]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[5]_syn_14.d[0] to FMC_data[5]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[5]_syn_14.b[0] to FMC_data[5]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[5]_syn_14.c[0] to FMC_data[5]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[5]_syn_14.d[0] to FMC_data[5]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[5]_syn_14.b[0] to FMC_data[5]_syn_14.f[0].
TMR-2509 : Cut connection from DATA/reg128_syn_51.d[1] to DATA/reg128_syn_51.f[1].
TMR-2509 : Cut connection from DATA/reg41_syn_81.d[1] to DATA/reg41_syn_81.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_200.b[1] to DATA/reg83_syn_200.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_200.d[1] to DATA/reg83_syn_200.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_200.c[1] to DATA/reg83_syn_200.f[1].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.c[0] to FMC_data[15]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.d[0] to FMC_data[15]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.b[0] to FMC_data[15]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.c[0] to FMC_data[15]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.d[0] to FMC_data[15]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.b[0] to FMC_data[15]_syn_14.f[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg65_syn_142.d[0] to COM2/uart_com2/reg65_syn_142.f[0].
TMR-2509 : Cut connection from IIC/reg8_syn_94.d[0] to IIC/reg8_syn_94.f[0].
TMR-2509 : Cut connection from COM2/STADOP_com2/reg3_syn_111.d[1] to COM2/STADOP_com2/reg3_syn_111.f[1].
TMR-2509 : Cut connection from COM2/head_com2/reg2_syn_131.d[0] to COM2/head_com2/reg2_syn_131.f[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg58_syn_124.d[1] to COM2/uart_com2/reg58_syn_124.f[1].
TMR-2509 : Cut connection from COM2/reg5_syn_216.c[1] to COM2/reg5_syn_216.f[1].
TMR-2509 : Cut connection from COM2/reg5_syn_216.b[1] to COM2/reg5_syn_216.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_337.b[0] to DATA/reg9_syn_337.f[0].
TMR-2509 : Cut connection from DATA/reg9_syn_337.c[0] to DATA/reg9_syn_337.f[0].
TMR-2509 : Cut connection from DATA/reg2_syn_154.c[1] to DATA/reg2_syn_154.f[1].
TMR-2509 : Cut connection from FMC/top_state[1]_syn_21.b[1] to FMC/top_state[1]_syn_21.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_337.d[1] to DATA/reg9_syn_337.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_337.c[1] to DATA/reg9_syn_337.f[1].
TMR-2509 : Cut connection from DATA/reg2_syn_154.d[0] to DATA/reg2_syn_154.f[0].
TMR-2509 : Cut connection from FMC_data[12]_syn_14.d[0] to FMC_data[12]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[12]_syn_14.b[0] to FMC_data[12]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[12]_syn_14.d[0] to FMC_data[12]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[12]_syn_14.b[0] to FMC_data[12]_syn_14.f[0].
TMR-2509 : Cut connection from DATA/done_div_reg_syn_108.d[0] to DATA/done_div_reg_syn_108.f[0].
TMR-2509 : Cut connection from DATA/reg83_syn_210.d[0] to DATA/reg83_syn_210.f[0].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.d[1] to FMC_data[10]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.b[1] to FMC_data[10]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.d[1] to FMC_data[10]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.b[1] to FMC_data[10]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.d[1] to FMC_data[7]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.b[1] to FMC_data[7]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.a[1] to FMC_data[7]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.d[1] to FMC_data[7]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.b[1] to FMC_data[7]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.a[1] to FMC_data[7]_syn_14.f[1].
TMR-2509 : Cut connection from DATA/reg114_syn_126.d[0] to DATA/reg114_syn_126.f[0].
TMR-2509 : Cut connection from FMC_data[5]_syn_14.b[1] to FMC_data[5]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[5]_syn_14.a[1] to FMC_data[5]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[5]_syn_14.b[1] to FMC_data[5]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[5]_syn_14.a[1] to FMC_data[5]_syn_14.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_197.d[1] to DATA/reg83_syn_197.f[1].
TMR-2509 : Cut connection from FMC/top_state[3]_syn_11.d[1] to FMC/top_state[3]_syn_11.f[1].
TMR-2507 : Eliminate loop in the timing graph, delete 203 tedges.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.988226s wall, 2.000000s user + 0.000000s system = 2.000000s CPU (100.6%)

RUN-1004 : used memory is 668 MB, reserved memory is 655 MB, peak memory is 719 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19325 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.988674s wall, 2.968750s user + 0.031250s system = 3.000000s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(221): len = 605313, overlap = 0.25
PHY-3002 : Step(222): len = 605358, overlap = 0.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17389/19327.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 735856, over cnt = 19(0%), over = 20, worst = 2
PHY-1002 : len = 735824, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 735840, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 735872, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 735936, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.707135s wall, 0.718750s user + 0.015625s system = 0.734375s CPU (103.9%)

PHY-1001 : Congestion index: top1 = 46.70, top5 = 42.39, top10 = 39.99, top15 = 38.51.
PHY-3001 : End congestion estimation;  1.024906s wall, 1.015625s user + 0.015625s system = 1.031250s CPU (100.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19325 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.953936s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (98.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000951393
PHY-3002 : Step(223): len = 605327, overlap = 0.5
PHY-3002 : Step(224): len = 605336, overlap = 0.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006710s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (232.9%)

PHY-3001 : Legalized: Len = 605401, Over = 0
PHY-3001 : End spreading;  0.076369s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (81.8%)

PHY-3001 : Final: Len = 605401, Over = 0
PHY-3001 : End incremental placement;  6.657599s wall, 7.125000s user + 0.234375s system = 7.359375s CPU (110.5%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.226313s wall, 11.843750s user + 0.359375s system = 12.203125s CPU (119.3%)

OPT-1001 : Current memory(MB): used = 715, reserve = 696, peak = 719.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17385/19327.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 735864, over cnt = 19(0%), over = 20, worst = 2
PHY-1002 : len = 735832, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 735800, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 735920, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 735936, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.712568s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (98.7%)

PHY-1001 : Congestion index: top1 = 46.51, top5 = 42.31, top10 = 39.95, top15 = 38.48.
OPT-1001 : End congestion update;  1.035283s wall, 1.046875s user + 0.000000s system = 1.046875s CPU (101.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19325 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.798319s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.8%)

OPT-0007 : Start: WNS 4756 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.838407s wall, 1.843750s user + 0.000000s system = 1.843750s CPU (100.3%)

OPT-1001 : Current memory(MB): used = 715, reserve = 696, peak = 719.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19325 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.798891s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.7%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17404/19327.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 735936, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.119166s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (104.9%)

PHY-1001 : Congestion index: top1 = 46.51, top5 = 42.31, top10 = 39.95, top15 = 38.48.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19325 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.798795s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.8%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4756 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 46.206897
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4756ps with logic level 4 
RUN-1001 :       #2 path slack 4759ps with logic level 4 
RUN-1001 :       #3 path slack 4765ps with logic level 3 
RUN-1001 :       #4 path slack 4780ps with logic level 3 
RUN-1001 :       #5 path slack 4793ps with logic level 1 
RUN-1001 :       #6 path slack 4815ps with logic level 3 
RUN-1001 :       #7 path slack 4830ps with logic level 3 
OPT-1001 : End physical optimization;  16.372104s wall, 18.046875s user + 0.375000s system = 18.421875s CPU (112.5%)

RUN-1003 : finish command "place" in  75.375280s wall, 142.968750s user + 8.140625s system = 151.109375s CPU (200.5%)

RUN-1004 : used memory is 633 MB, reserved memory is 610 MB, peak memory is 719 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.681035s wall, 2.890625s user + 0.015625s system = 2.906250s CPU (172.9%)

RUN-1004 : used memory is 634 MB, reserved memory is 612 MB, peak memory is 719 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8026 instances
RUN-1001 : 3974 mslices, 3961 lslices, 59 pads, 26 brams, 0 dsps
RUN-1001 : There are total 19327 nets
RUN-1001 : 13642 nets have 2 pins
RUN-1001 : 4269 nets have [3 - 5] pins
RUN-1001 : 903 nets have [6 - 10] pins
RUN-1001 : 375 nets have [11 - 20] pins
RUN-1001 : 128 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67147, tnet num: 19325, tinst num: 8024, tnode num: 90906, tedge num: 110588.
TMR-2509 : Cut connection from COM2/reg5_syn_216.b[0] to COM2/reg5_syn_216.f[0].
TMR-2509 : Cut connection from COM2/reg5_syn_216.a[0] to COM2/reg5_syn_216.f[0].
TMR-2509 : Cut connection from COM2/reg5_syn_216.c[0] to COM2/reg5_syn_216.f[0].
TMR-2509 : Cut connection from FMC/top_state[0]_syn_84.e[1] to FMC/top_state[0]_syn_84.f[1].
TMR-2509 : Cut connection from FMC/top_state[0]_syn_84.d[1] to FMC/top_state[0]_syn_84.f[1].
TMR-2509 : Cut connection from FMC/top_state[0]_syn_84.c[1] to FMC/top_state[0]_syn_84.f[1].
TMR-2509 : Cut connection from FMC/top_state[0]_syn_84.a[1] to FMC/top_state[0]_syn_84.f[1].
TMR-2509 : Cut connection from COM2/reg5_syn_224.a[0] to COM2/reg5_syn_224.f[0].
TMR-2509 : Cut connection from COM2/reg5_syn_224.b[0] to COM2/reg5_syn_224.f[0].
TMR-2509 : Cut connection from COM2/reg5_syn_224.c[0] to COM2/reg5_syn_224.f[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.b[1] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.b[0] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.c[1] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.c[0] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.mi[0] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.d[1] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.d[0] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.b[1] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.b[0] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.c[1] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.c[0] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.mi[0] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.d[1] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.d[0] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from DATA/reg84_syn_50.a[1] to DATA/reg84_syn_50.f[1].
TMR-2509 : Cut connection from DATA/reg84_syn_50.b[1] to DATA/reg84_syn_50.f[1].
TMR-2509 : Cut connection from DATA/reg84_syn_50.c[1] to DATA/reg84_syn_50.f[1].
TMR-2509 : Cut connection from FMC_data[11]_syn_14.b[0] to FMC_data[11]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[11]_syn_14.c[0] to FMC_data[11]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[11]_syn_14.e[0] to FMC_data[11]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[11]_syn_14.d[0] to FMC_data[11]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[11]_syn_14.b[0] to FMC_data[11]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[11]_syn_14.c[0] to FMC_data[11]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[11]_syn_14.e[0] to FMC_data[11]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[11]_syn_14.d[0] to FMC_data[11]_syn_14.f[0].
TMR-2509 : Cut connection from DATA/reg115_syn_114.a[0] to DATA/reg115_syn_114.f[0].
TMR-2509 : Cut connection from DATA/reg115_syn_114.b[0] to DATA/reg115_syn_114.f[0].
TMR-2509 : Cut connection from DATA/reg115_syn_114.c[0] to DATA/reg115_syn_114.f[0].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.b[1] to FMC_data[1]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.c[1] to FMC_data[1]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.e[1] to FMC_data[1]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.d[1] to FMC_data[1]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.b[1] to FMC_data[1]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.c[1] to FMC_data[1]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.e[1] to FMC_data[1]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.d[1] to FMC_data[1]_syn_14.f[1].
TMR-2509 : Cut connection from DATA/reg29_syn_155.a[1] to DATA/reg29_syn_155.f[1].
TMR-2509 : Cut connection from DATA/reg29_syn_155.b[1] to DATA/reg29_syn_155.f[1].
TMR-2509 : Cut connection from DATA/reg29_syn_155.c[1] to DATA/reg29_syn_155.f[1].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.b[1] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.b[0] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.c[1] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.c[0] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.mi[0] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.d[0] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.d[1] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.b[1] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.b[0] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.c[1] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.c[0] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.mi[0] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.d[0] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.d[1] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_213.a[1] to COM2/uart_com2/reg1_syn_213.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_213.b[1] to COM2/uart_com2/reg1_syn_213.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_213.c[1] to COM2/uart_com2/reg1_syn_213.f[1].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.b[0] to FMC_data[10]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.c[0] to FMC_data[10]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.e[0] to FMC_data[10]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.d[0] to FMC_data[10]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.b[0] to FMC_data[10]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.c[0] to FMC_data[10]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.e[0] to FMC_data[10]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.d[0] to FMC_data[10]_syn_14.f[0].
TMR-2509 : Cut connection from COM2/head_com2/reg3_syn_154.d[1] to COM2/head_com2/reg3_syn_154.f[1].
TMR-2509 : Cut connection from FMC/top_state[0]_syn_82.a[1] to FMC/top_state[0]_syn_82.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_323.d[1] to DATA/reg9_syn_323.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_323.b[1] to DATA/reg9_syn_323.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_323.c[1] to DATA/reg9_syn_323.f[1].
TMR-2509 : Cut connection from COM2/head_com2/reg3_syn_143.b[0] to COM2/head_com2/reg3_syn_143.f[0].
TMR-2509 : Cut connection from COM2/head_com2/reg3_syn_143.a[0] to COM2/head_com2/reg3_syn_143.f[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_221.a[0] to COM2/uart_com2/reg1_syn_221.f[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_221.b[0] to COM2/uart_com2/reg1_syn_221.f[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_221.c[0] to COM2/uart_com2/reg1_syn_221.f[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.b[1] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.b[0] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.c[1] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.c[0] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.mi[0] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.d[0] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.d[1] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.b[1] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.b[0] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.c[1] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.c[0] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.mi[0] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.d[0] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.d[1] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from DATA/reg128_syn_53.a[1] to DATA/reg128_syn_53.f[1].
TMR-2509 : Cut connection from DATA/reg128_syn_53.b[1] to DATA/reg128_syn_53.f[1].
TMR-2509 : Cut connection from DATA/reg128_syn_53.c[1] to DATA/reg128_syn_53.f[1].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.b[0] to FMC_data[1]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.c[0] to FMC_data[1]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.e[0] to FMC_data[1]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.d[0] to FMC_data[1]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.b[0] to FMC_data[1]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.c[0] to FMC_data[1]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.e[0] to FMC_data[1]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.d[0] to FMC_data[1]_syn_14.f[0].
TMR-2509 : Cut connection from DATA/reg45_syn_153.d[1] to DATA/reg45_syn_153.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg70_syn_118.d[1] to COM2/uart_com2/reg70_syn_118.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg65_syn_142.d[1] to COM2/uart_com2/reg65_syn_142.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg63_syn_139.d[1] to COM2/uart_com2/reg63_syn_139.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg7_syn_127.d[0] to COM2/uart_com2/reg7_syn_127.f[0].
TMR-2509 : Cut connection from DATA/reg36_syn_274.d[0] to DATA/reg36_syn_274.f[0].
TMR-2509 : Cut connection from DATA/reg83_syn_171.a[1] to DATA/reg83_syn_171.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_171.d[1] to DATA/reg83_syn_171.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_171.c[1] to DATA/reg83_syn_171.f[1].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.c[1] to FMC_data[15]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.b[1] to FMC_data[15]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.d[1] to FMC_data[15]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.c[1] to FMC_data[15]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.b[1] to FMC_data[15]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.d[1] to FMC_data[15]_syn_14.f[1].
TMR-2509 : Cut connection from DATA/reg18_syn_129.d[0] to DATA/reg18_syn_129.f[0].
TMR-2509 : Cut connection from DATA/reg41_syn_78.d[1] to DATA/reg41_syn_78.f[1].
TMR-2509 : Cut connection from DATA/reg13_syn_129.b[0] to DATA/reg13_syn_129.f[0].
TMR-2509 : Cut connection from DATA/reg13_syn_129.d[0] to DATA/reg13_syn_129.f[0].
TMR-2509 : Cut connection from DATA/reg13_syn_129.c[0] to DATA/reg13_syn_129.f[0].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.c[0] to FMC_data[7]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.d[0] to FMC_data[7]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.b[0] to FMC_data[7]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.c[0] to FMC_data[7]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.d[0] to FMC_data[7]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.b[0] to FMC_data[7]_syn_14.f[0].
TMR-2509 : Cut connection from DATA/reg114_syn_122.a[0] to DATA/reg114_syn_122.f[0].
TMR-2509 : Cut connection from COM2/STADOP_com2/reg3_syn_111.d[0] to COM2/STADOP_com2/reg3_syn_111.f[0].
TMR-2509 : Cut connection from DATA/reg2_syn_144.d[1] to DATA/reg2_syn_144.f[1].
TMR-2509 : Cut connection from FMC_data[12]_syn_14.b[1] to FMC_data[12]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[12]_syn_14.a[1] to FMC_data[12]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[12]_syn_14.d[1] to FMC_data[12]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[12]_syn_14.b[1] to FMC_data[12]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[12]_syn_14.a[1] to FMC_data[12]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[12]_syn_14.d[1] to FMC_data[12]_syn_14.f[1].
TMR-2509 : Cut connection from DATA/reg4_syn_185.d[1] to DATA/reg4_syn_185.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg65_syn_146.d[0] to COM2/uart_com2/reg65_syn_146.f[0].
TMR-2509 : Cut connection from DATA/reg29_syn_136.a[1] to DATA/reg29_syn_136.f[1].
TMR-2509 : Cut connection from DATA/reg29_syn_136.d[1] to DATA/reg29_syn_136.f[1].
TMR-2509 : Cut connection from DATA/reg29_syn_136.c[1] to DATA/reg29_syn_136.f[1].
TMR-2509 : Cut connection from FMC_data[5]_syn_14.c[0] to FMC_data[5]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[5]_syn_14.d[0] to FMC_data[5]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[5]_syn_14.b[0] to FMC_data[5]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[5]_syn_14.c[0] to FMC_data[5]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[5]_syn_14.d[0] to FMC_data[5]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[5]_syn_14.b[0] to FMC_data[5]_syn_14.f[0].
TMR-2509 : Cut connection from DATA/reg128_syn_51.d[1] to DATA/reg128_syn_51.f[1].
TMR-2509 : Cut connection from DATA/reg41_syn_81.d[1] to DATA/reg41_syn_81.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_200.b[1] to DATA/reg83_syn_200.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_200.d[1] to DATA/reg83_syn_200.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_200.c[1] to DATA/reg83_syn_200.f[1].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.c[0] to FMC_data[15]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.d[0] to FMC_data[15]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.b[0] to FMC_data[15]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.c[0] to FMC_data[15]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.d[0] to FMC_data[15]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.b[0] to FMC_data[15]_syn_14.f[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg65_syn_142.d[0] to COM2/uart_com2/reg65_syn_142.f[0].
TMR-2509 : Cut connection from IIC/reg8_syn_94.d[0] to IIC/reg8_syn_94.f[0].
TMR-2509 : Cut connection from COM2/STADOP_com2/reg3_syn_111.d[1] to COM2/STADOP_com2/reg3_syn_111.f[1].
TMR-2509 : Cut connection from COM2/head_com2/reg2_syn_131.d[0] to COM2/head_com2/reg2_syn_131.f[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg58_syn_124.d[1] to COM2/uart_com2/reg58_syn_124.f[1].
TMR-2509 : Cut connection from COM2/reg5_syn_216.c[1] to COM2/reg5_syn_216.f[1].
TMR-2509 : Cut connection from COM2/reg5_syn_216.b[1] to COM2/reg5_syn_216.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_337.b[0] to DATA/reg9_syn_337.f[0].
TMR-2509 : Cut connection from DATA/reg9_syn_337.c[0] to DATA/reg9_syn_337.f[0].
TMR-2509 : Cut connection from DATA/reg2_syn_154.c[1] to DATA/reg2_syn_154.f[1].
TMR-2509 : Cut connection from FMC/top_state[1]_syn_21.b[1] to FMC/top_state[1]_syn_21.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_337.d[1] to DATA/reg9_syn_337.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_337.c[1] to DATA/reg9_syn_337.f[1].
TMR-2509 : Cut connection from DATA/reg2_syn_154.d[0] to DATA/reg2_syn_154.f[0].
TMR-2509 : Cut connection from FMC_data[12]_syn_14.d[0] to FMC_data[12]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[12]_syn_14.b[0] to FMC_data[12]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[12]_syn_14.d[0] to FMC_data[12]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[12]_syn_14.b[0] to FMC_data[12]_syn_14.f[0].
TMR-2509 : Cut connection from DATA/done_div_reg_syn_108.d[0] to DATA/done_div_reg_syn_108.f[0].
TMR-2509 : Cut connection from DATA/reg83_syn_210.d[0] to DATA/reg83_syn_210.f[0].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.d[1] to FMC_data[10]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.b[1] to FMC_data[10]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.d[1] to FMC_data[10]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.b[1] to FMC_data[10]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.d[1] to FMC_data[7]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.b[1] to FMC_data[7]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.a[1] to FMC_data[7]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.d[1] to FMC_data[7]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.b[1] to FMC_data[7]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.a[1] to FMC_data[7]_syn_14.f[1].
TMR-2509 : Cut connection from DATA/reg114_syn_126.d[0] to DATA/reg114_syn_126.f[0].
TMR-2509 : Cut connection from FMC_data[5]_syn_14.b[1] to FMC_data[5]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[5]_syn_14.a[1] to FMC_data[5]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[5]_syn_14.b[1] to FMC_data[5]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[5]_syn_14.a[1] to FMC_data[5]_syn_14.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_197.d[1] to DATA/reg83_syn_197.f[1].
TMR-2509 : Cut connection from FMC/top_state[3]_syn_11.d[1] to FMC/top_state[3]_syn_11.f[1].
TMR-2507 : Eliminate loop in the timing graph, delete 203 tedges.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.781160s wall, 1.781250s user + 0.000000s system = 1.781250s CPU (100.0%)

RUN-1004 : used memory is 644 MB, reserved memory is 640 MB, peak memory is 719 MB
PHY-1001 : 3974 mslices, 3961 lslices, 59 pads, 26 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19325 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 679184, over cnt = 2315(6%), over = 3780, worst = 9
PHY-1002 : len = 694968, over cnt = 1370(3%), over = 1886, worst = 9
PHY-1002 : len = 707736, over cnt = 662(1%), over = 937, worst = 6
PHY-1002 : len = 722344, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 722392, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.770346s wall, 3.062500s user + 0.078125s system = 3.140625s CPU (177.4%)

PHY-1001 : Congestion index: top1 = 45.95, top5 = 41.92, top10 = 39.62, top15 = 38.08.
PHY-1001 : End global routing;  2.128792s wall, 3.437500s user + 0.078125s system = 3.515625s CPU (165.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 705, reserve = 692, peak = 719.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Processed IO instance FMC_data[15]_syn_2 with INV attribute.
PHY-1001 : Processed IO instance FMC_data[14]_syn_2 with INV attribute.
PHY-1001 : Processed IO instance FMC_data[13]_syn_2 with INV attribute.
PHY-1001 : Processed IO instance FMC_data[12]_syn_2 with INV attribute.
PHY-1001 : Processed IO instance FMC_data[11]_syn_2 with INV attribute.
PHY-1001 : Processed IO instance FMC_data[10]_syn_2 with INV attribute.
PHY-1001 : Processed IO instance FMC_data[9]_syn_2 with INV attribute.
PHY-1001 : Processed IO instance FMC_data[8]_syn_2 with INV attribute.
PHY-1001 : Processed IO instance FMC_data[7]_syn_2 with INV attribute.
PHY-1001 : Processed IO instance FMC_data[6]_syn_2 with INV attribute.
PHY-1001 : Processed IO instance FMC_data[5]_syn_2 with INV attribute.
PHY-1001 : Processed IO instance FMC_data[4]_syn_2 with INV attribute.
PHY-1001 : Processed IO instance FMC_data[3]_syn_2 with INV attribute.
PHY-1001 : Processed IO instance FMC_data[2]_syn_2 with INV attribute.
PHY-1001 : Processed IO instance FMC_data[1]_syn_2 with INV attribute.
PHY-1001 : Processed IO instance FMC_data[0]_syn_2 with INV attribute.
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : clock net FMC/FMC_data_n_syn_3 will be merged with clock FMC/FMC_data_n
PHY-1001 : Current memory(MB): used = 973, reserve = 958, peak = 973.
PHY-1001 : End build detailed router design. 4.920913s wall, 4.828125s user + 0.078125s system = 4.906250s CPU (99.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 195632, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.550889s wall, 1.546875s user + 0.000000s system = 1.546875s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 1009, reserve = 994, peak = 1009.
PHY-1001 : End phase 1; 1.558058s wall, 1.562500s user + 0.000000s system = 1.562500s CPU (100.3%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 55% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.72466e+06, over cnt = 1242(0%), over = 1246, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1021, reserve = 1009, peak = 1021.
PHY-1001 : End initial routed; 18.077976s wall, 48.578125s user + 0.375000s system = 48.953125s CPU (270.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18067(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.194   |   0.000   |   0   
RUN-1001 :   Hold   |   0.122   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.683082s wall, 3.687500s user + 0.000000s system = 3.687500s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1031, reserve = 1020, peak = 1031.
PHY-1001 : End phase 2; 21.761232s wall, 52.265625s user + 0.375000s system = 52.640625s CPU (241.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.72466e+06, over cnt = 1242(0%), over = 1246, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.252437s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (99.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.71126e+06, over cnt = 339(0%), over = 340, worst = 2, crit = 0
PHY-1001 : End DR Iter 1; 0.912092s wall, 1.640625s user + 0.000000s system = 1.640625s CPU (179.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.71202e+06, over cnt = 117(0%), over = 117, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.307020s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (142.5%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.71343e+06, over cnt = 11(0%), over = 11, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.266186s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (117.4%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.71354e+06, over cnt = 6(0%), over = 6, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.222849s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (98.2%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.71364e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.189894s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (98.7%)

PHY-1001 : ===== DR Iter 6 =====
PHY-1022 : len = 1.71364e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 6; 0.265761s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (99.9%)

PHY-1001 : ===== DR Iter 7 =====
PHY-1022 : len = 1.71364e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 7; 0.396183s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (98.6%)

PHY-1001 : ===== DR Iter 8 =====
PHY-1022 : len = 1.71365e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 8; 0.173733s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (98.9%)

PHY-1001 : ==== DR Iter 9 ====
PHY-1022 : len = 1.71365e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 9; 0.182462s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (102.8%)

PHY-1001 : ==== DR Iter 10 ====
PHY-1022 : len = 1.7137e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 10; 0.162921s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (95.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18067(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.194   |   0.000   |   0   
RUN-1001 :   Hold   |   0.122   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.651926s wall, 3.656250s user + 0.000000s system = 3.656250s CPU (100.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 251 feed throughs used by 220 nets
PHY-1001 : End commit to database; 2.227195s wall, 2.218750s user + 0.015625s system = 2.234375s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 1118, reserve = 1109, peak = 1118.
PHY-1001 : End phase 3; 9.785933s wall, 10.703125s user + 0.015625s system = 10.718750s CPU (109.5%)

PHY-1003 : Routed, final wirelength = 1.7137e+06
PHY-1001 : Current memory(MB): used = 1122, reserve = 1113, peak = 1122.
PHY-1001 : End export database. 0.062995s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.2%)

PHY-1001 : End detail routing;  38.546063s wall, 69.859375s user + 0.468750s system = 70.328125s CPU (182.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67147, tnet num: 19325, tinst num: 8024, tnode num: 90906, tedge num: 110588.
TMR-2509 : Cut connection from COM2/reg5_syn_216.b[0] to COM2/reg5_syn_216.f[0].
TMR-2509 : Cut connection from COM2/reg5_syn_216.a[0] to COM2/reg5_syn_216.f[0].
TMR-2509 : Cut connection from COM2/reg5_syn_216.c[0] to COM2/reg5_syn_216.f[0].
TMR-2509 : Cut connection from FMC/top_state[0]_syn_84.e[1] to FMC/top_state[0]_syn_84.f[1].
TMR-2509 : Cut connection from FMC/top_state[0]_syn_84.d[1] to FMC/top_state[0]_syn_84.f[1].
TMR-2509 : Cut connection from FMC/top_state[0]_syn_84.c[1] to FMC/top_state[0]_syn_84.f[1].
TMR-2509 : Cut connection from FMC/top_state[0]_syn_84.a[1] to FMC/top_state[0]_syn_84.f[1].
TMR-2509 : Cut connection from COM2/reg5_syn_224.a[0] to COM2/reg5_syn_224.f[0].
TMR-2509 : Cut connection from COM2/reg5_syn_224.b[0] to COM2/reg5_syn_224.f[0].
TMR-2509 : Cut connection from COM2/reg5_syn_224.c[0] to COM2/reg5_syn_224.f[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.b[1] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.b[0] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.c[1] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.c[0] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.mi[0] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.d[1] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.d[0] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.b[1] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.b[0] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.c[1] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.c[0] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.mi[0] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.d[1] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.d[0] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from DATA/reg84_syn_50.a[1] to DATA/reg84_syn_50.f[1].
TMR-2509 : Cut connection from DATA/reg84_syn_50.b[1] to DATA/reg84_syn_50.f[1].
TMR-2509 : Cut connection from DATA/reg84_syn_50.c[1] to DATA/reg84_syn_50.f[1].
TMR-2509 : Cut connection from FMC_data[11]_syn_14.b[0] to FMC_data[11]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[11]_syn_14.c[0] to FMC_data[11]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[11]_syn_14.e[0] to FMC_data[11]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[11]_syn_14.d[0] to FMC_data[11]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[11]_syn_14.b[0] to FMC_data[11]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[11]_syn_14.c[0] to FMC_data[11]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[11]_syn_14.e[0] to FMC_data[11]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[11]_syn_14.d[0] to FMC_data[11]_syn_14.f[0].
TMR-2509 : Cut connection from DATA/reg115_syn_114.a[0] to DATA/reg115_syn_114.f[0].
TMR-2509 : Cut connection from DATA/reg115_syn_114.b[0] to DATA/reg115_syn_114.f[0].
TMR-2509 : Cut connection from DATA/reg115_syn_114.c[0] to DATA/reg115_syn_114.f[0].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.b[1] to FMC_data[1]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.c[1] to FMC_data[1]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.e[1] to FMC_data[1]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.d[1] to FMC_data[1]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.b[1] to FMC_data[1]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.c[1] to FMC_data[1]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.e[1] to FMC_data[1]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.d[1] to FMC_data[1]_syn_14.f[1].
TMR-2509 : Cut connection from DATA/reg29_syn_155.a[1] to DATA/reg29_syn_155.f[1].
TMR-2509 : Cut connection from DATA/reg29_syn_155.b[1] to DATA/reg29_syn_155.f[1].
TMR-2509 : Cut connection from DATA/reg29_syn_155.c[1] to DATA/reg29_syn_155.f[1].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.b[1] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.b[0] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.c[1] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.c[0] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.mi[0] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.d[0] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.d[1] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.b[1] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.b[0] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.c[1] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.c[0] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.mi[0] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.d[0] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.d[1] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_213.a[1] to COM2/uart_com2/reg1_syn_213.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_213.b[1] to COM2/uart_com2/reg1_syn_213.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_213.c[1] to COM2/uart_com2/reg1_syn_213.f[1].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.b[0] to FMC_data[10]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.c[0] to FMC_data[10]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.e[0] to FMC_data[10]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.d[0] to FMC_data[10]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.b[0] to FMC_data[10]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.c[0] to FMC_data[10]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.e[0] to FMC_data[10]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.d[0] to FMC_data[10]_syn_14.f[0].
TMR-2509 : Cut connection from COM2/head_com2/reg3_syn_154.d[1] to COM2/head_com2/reg3_syn_154.f[1].
TMR-2509 : Cut connection from FMC/top_state[0]_syn_82.a[1] to FMC/top_state[0]_syn_82.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_323.d[1] to DATA/reg9_syn_323.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_323.b[1] to DATA/reg9_syn_323.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_323.c[1] to DATA/reg9_syn_323.f[1].
TMR-2509 : Cut connection from COM2/head_com2/reg3_syn_143.b[0] to COM2/head_com2/reg3_syn_143.f[0].
TMR-2509 : Cut connection from COM2/head_com2/reg3_syn_143.a[0] to COM2/head_com2/reg3_syn_143.f[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_221.a[0] to COM2/uart_com2/reg1_syn_221.f[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_221.b[0] to COM2/uart_com2/reg1_syn_221.f[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_221.c[0] to COM2/uart_com2/reg1_syn_221.f[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.b[1] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.b[0] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.c[1] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.c[0] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.mi[0] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.d[0] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.d[1] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.b[1] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.b[0] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.c[1] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.c[0] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.mi[0] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.d[0] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.d[1] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from DATA/reg128_syn_53.a[1] to DATA/reg128_syn_53.f[1].
TMR-2509 : Cut connection from DATA/reg128_syn_53.b[1] to DATA/reg128_syn_53.f[1].
TMR-2509 : Cut connection from DATA/reg128_syn_53.c[1] to DATA/reg128_syn_53.f[1].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.b[0] to FMC_data[1]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.c[0] to FMC_data[1]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.e[0] to FMC_data[1]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.d[0] to FMC_data[1]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.b[0] to FMC_data[1]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.c[0] to FMC_data[1]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.e[0] to FMC_data[1]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.d[0] to FMC_data[1]_syn_14.f[0].
TMR-2509 : Cut connection from DATA/reg45_syn_153.d[1] to DATA/reg45_syn_153.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg70_syn_118.d[1] to COM2/uart_com2/reg70_syn_118.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg65_syn_142.d[1] to COM2/uart_com2/reg65_syn_142.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg63_syn_139.d[1] to COM2/uart_com2/reg63_syn_139.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg7_syn_127.d[0] to COM2/uart_com2/reg7_syn_127.f[0].
TMR-2509 : Cut connection from DATA/reg36_syn_274.d[0] to DATA/reg36_syn_274.f[0].
TMR-2509 : Cut connection from DATA/reg83_syn_171.a[1] to DATA/reg83_syn_171.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_171.d[1] to DATA/reg83_syn_171.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_171.c[1] to DATA/reg83_syn_171.f[1].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.c[1] to FMC_data[15]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.b[1] to FMC_data[15]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.d[1] to FMC_data[15]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.c[1] to FMC_data[15]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.b[1] to FMC_data[15]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.d[1] to FMC_data[15]_syn_14.f[1].
TMR-2509 : Cut connection from DATA/reg18_syn_129.d[0] to DATA/reg18_syn_129.f[0].
TMR-2509 : Cut connection from DATA/reg41_syn_78.d[1] to DATA/reg41_syn_78.f[1].
TMR-2509 : Cut connection from DATA/reg13_syn_129.b[0] to DATA/reg13_syn_129.f[0].
TMR-2509 : Cut connection from DATA/reg13_syn_129.d[0] to DATA/reg13_syn_129.f[0].
TMR-2509 : Cut connection from DATA/reg13_syn_129.c[0] to DATA/reg13_syn_129.f[0].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.c[0] to FMC_data[7]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.d[0] to FMC_data[7]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.b[0] to FMC_data[7]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.c[0] to FMC_data[7]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.d[0] to FMC_data[7]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.b[0] to FMC_data[7]_syn_14.f[0].
TMR-2509 : Cut connection from DATA/reg114_syn_122.a[0] to DATA/reg114_syn_122.f[0].
TMR-2509 : Cut connection from COM2/STADOP_com2/reg3_syn_111.d[0] to COM2/STADOP_com2/reg3_syn_111.f[0].
TMR-2509 : Cut connection from DATA/reg2_syn_144.d[1] to DATA/reg2_syn_144.f[1].
TMR-2509 : Cut connection from FMC_data[12]_syn_14.b[1] to FMC_data[12]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[12]_syn_14.a[1] to FMC_data[12]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[12]_syn_14.d[1] to FMC_data[12]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[12]_syn_14.b[1] to FMC_data[12]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[12]_syn_14.a[1] to FMC_data[12]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[12]_syn_14.d[1] to FMC_data[12]_syn_14.f[1].
TMR-2509 : Cut connection from DATA/reg4_syn_185.d[1] to DATA/reg4_syn_185.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg65_syn_146.d[0] to COM2/uart_com2/reg65_syn_146.f[0].
TMR-2509 : Cut connection from DATA/reg29_syn_136.a[1] to DATA/reg29_syn_136.f[1].
TMR-2509 : Cut connection from DATA/reg29_syn_136.d[1] to DATA/reg29_syn_136.f[1].
TMR-2509 : Cut connection from DATA/reg29_syn_136.c[1] to DATA/reg29_syn_136.f[1].
TMR-2509 : Cut connection from FMC_data[5]_syn_14.c[0] to FMC_data[5]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[5]_syn_14.d[0] to FMC_data[5]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[5]_syn_14.b[0] to FMC_data[5]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[5]_syn_14.c[0] to FMC_data[5]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[5]_syn_14.d[0] to FMC_data[5]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[5]_syn_14.b[0] to FMC_data[5]_syn_14.f[0].
TMR-2509 : Cut connection from DATA/reg128_syn_51.d[1] to DATA/reg128_syn_51.f[1].
TMR-2509 : Cut connection from DATA/reg41_syn_81.d[1] to DATA/reg41_syn_81.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_200.b[1] to DATA/reg83_syn_200.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_200.d[1] to DATA/reg83_syn_200.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_200.c[1] to DATA/reg83_syn_200.f[1].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.c[0] to FMC_data[15]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.d[0] to FMC_data[15]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.b[0] to FMC_data[15]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.c[0] to FMC_data[15]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.d[0] to FMC_data[15]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.b[0] to FMC_data[15]_syn_14.f[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg65_syn_142.d[0] to COM2/uart_com2/reg65_syn_142.f[0].
TMR-2509 : Cut connection from IIC/reg8_syn_94.d[0] to IIC/reg8_syn_94.f[0].
TMR-2509 : Cut connection from COM2/STADOP_com2/reg3_syn_111.d[1] to COM2/STADOP_com2/reg3_syn_111.f[1].
TMR-2509 : Cut connection from COM2/head_com2/reg2_syn_131.d[0] to COM2/head_com2/reg2_syn_131.f[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg58_syn_124.d[1] to COM2/uart_com2/reg58_syn_124.f[1].
TMR-2509 : Cut connection from COM2/reg5_syn_216.c[1] to COM2/reg5_syn_216.f[1].
TMR-2509 : Cut connection from COM2/reg5_syn_216.b[1] to COM2/reg5_syn_216.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_337.b[0] to DATA/reg9_syn_337.f[0].
TMR-2509 : Cut connection from DATA/reg9_syn_337.c[0] to DATA/reg9_syn_337.f[0].
TMR-2509 : Cut connection from DATA/reg2_syn_154.c[1] to DATA/reg2_syn_154.f[1].
TMR-2509 : Cut connection from FMC/top_state[1]_syn_21.b[1] to FMC/top_state[1]_syn_21.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_337.d[1] to DATA/reg9_syn_337.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_337.c[1] to DATA/reg9_syn_337.f[1].
TMR-2509 : Cut connection from DATA/reg2_syn_154.d[0] to DATA/reg2_syn_154.f[0].
TMR-2509 : Cut connection from FMC_data[12]_syn_14.d[0] to FMC_data[12]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[12]_syn_14.b[0] to FMC_data[12]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[12]_syn_14.d[0] to FMC_data[12]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[12]_syn_14.b[0] to FMC_data[12]_syn_14.f[0].
TMR-2509 : Cut connection from DATA/done_div_reg_syn_108.d[0] to DATA/done_div_reg_syn_108.f[0].
TMR-2509 : Cut connection from DATA/reg83_syn_210.d[0] to DATA/reg83_syn_210.f[0].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.d[1] to FMC_data[10]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.b[1] to FMC_data[10]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.d[1] to FMC_data[10]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.b[1] to FMC_data[10]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.d[1] to FMC_data[7]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.b[1] to FMC_data[7]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.a[1] to FMC_data[7]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.d[1] to FMC_data[7]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.b[1] to FMC_data[7]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.a[1] to FMC_data[7]_syn_14.f[1].
TMR-2509 : Cut connection from DATA/reg114_syn_126.d[0] to DATA/reg114_syn_126.f[0].
TMR-2509 : Cut connection from FMC_data[5]_syn_14.b[1] to FMC_data[5]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[5]_syn_14.a[1] to FMC_data[5]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[5]_syn_14.b[1] to FMC_data[5]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[5]_syn_14.a[1] to FMC_data[5]_syn_14.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_197.d[1] to DATA/reg83_syn_197.f[1].
TMR-2509 : Cut connection from FMC/top_state[3]_syn_11.d[1] to FMC/top_state[3]_syn_11.f[1].
TMR-2507 : Eliminate loop in the timing graph, delete 203 tedges.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.797062s wall, 1.796875s user + 0.000000s system = 1.796875s CPU (100.0%)

RUN-1004 : used memory is 1054 MB, reserved memory is 1051 MB, peak memory is 1122 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  47.102679s wall, 79.687500s user + 0.546875s system = 80.234375s CPU (170.3%)

RUN-1004 : used memory is 1053 MB, reserved memory is 1051 MB, peak memory is 1122 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8847   out of  19600   45.14%
#reg                    12315   out of  19600   62.83%
#le                     14876
  #lut only              2561   out of  14876   17.22%
  #reg only              6029   out of  14876   40.53%
  #lut&reg               6286   out of  14876   42.26%
#dsp                        0   out of     29    0.00%
#bram                      26   out of     64   40.62%
  #bram9k                  24
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       59   out of    188   31.38%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6721
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          130
#3        FMC/FMC_data_n                   GCLK               lslice             DATA/reg9_syn_318.f0      26
#4        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         F2        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         D1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         B3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         C1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         A3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         A2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         B2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         B1        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F15        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDZ        INPUT        K15        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         P2        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         N4        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         M5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         N6        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         N5        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         P5        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M3        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         M4        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         H2        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         M1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         L1        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         N1        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         N3        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         M2        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT         T5        LVCMOS33           8            NONE           OREG       
    Fog_ENY        OUTPUT         T4        LVCMOS33           8            NONE           OREG       
    Fog_ENZ        OUTPUT         T7        LVCMOS33           8            NONE           OREG       
    Fog_INT        OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         T6        LVCMOS33           8            N/A            OREG       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E1        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        G11        LVCMOS25           8            N/A            NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |14876  |7359    |1488    |12359   |26      |0       |
|  AnyFog_dataX                      |AnyFog          |214    |83      |22      |172     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |93     |60      |22      |51      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |212    |102     |22      |171     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |90     |63      |22      |49      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |213    |83      |22      |172     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |92     |67      |22      |51      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2922   |765     |39      |2845    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |62     |35      |5       |54      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |209    |65      |5       |199     |0       |0       |
|    STADOP_com2                     |STADOP          |548    |57      |0       |545     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |43      |14      |43      |0       |0       |
|    head_com2                       |uniheading      |262    |128     |5       |251     |0       |0       |
|    rmc_com2                        |Gprmc           |39     |39      |0       |31      |0       |0       |
|    uart_com2                       |Agrica          |1427   |369     |10      |1412    |0       |0       |
|  COM3                              |COM3_Control    |283    |139     |19      |239     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |62     |35      |5       |53      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |61     |40      |14      |36      |0       |0       |
|    rmc_com3                        |Gprmc           |160    |64      |0       |150     |0       |0       |
|  DATA                              |Data_Processing |8857   |4601    |1122    |7049    |0       |0       |
|    DIV_Dtemp                       |Divider         |760    |361     |84      |636     |0       |0       |
|    DIV_Utemp                       |Divider         |650    |290     |84      |528     |0       |0       |
|    DIV_accX                        |Divider         |581    |309     |84      |452     |0       |0       |
|    DIV_accY                        |Divider         |675    |330     |102     |522     |0       |0       |
|    DIV_accZ                        |Divider         |704    |377     |132     |495     |0       |0       |
|    DIV_rateX                       |Divider         |651    |352     |132     |443     |0       |0       |
|    DIV_rateY                       |Divider         |575    |359     |132     |367     |0       |0       |
|    DIV_rateZ                       |Divider         |595    |405     |132     |391     |0       |0       |
|    genclk                          |genclk          |269    |158     |89      |106     |0       |0       |
|  FMC                               |FMC_Ctrl        |388    |336     |43      |309     |0       |0       |
|  IIC                               |I2C_master      |283    |236     |11      |249     |0       |0       |
|  IMU_CTRL                          |SCHA634         |900    |629     |61      |734     |0       |0       |
|    CtrlData                        |CtrlData        |486    |433     |47      |348     |0       |0       |
|      usms                          |Time_1ms        |30     |25      |5       |21      |0       |0       |
|    SPIM                            |SPI_SCHA634     |414    |196     |14      |386     |0       |0       |
|  POWER                             |POWER_EN        |94     |56      |38      |34      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |491    |310     |89      |325     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |491    |310     |89      |325     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |204    |139     |0       |189     |0       |0       |
|        reg_inst                    |register        |201    |136     |0       |186     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |287    |171     |89      |136     |0       |0       |
|        bus_inst                    |bus_top         |79     |50      |28      |31      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |28     |18      |10      |12      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |1      |0       |0       |1       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |48     |30      |18      |16      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |121    |80      |29      |73      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13582  
    #2          2       3346   
    #3          3        619   
    #4          4        304   
    #5        5-10       953   
    #6        11-50      437   
    #7       51-100      15    
    #8       101-500      4    
    #9        >500        2    
  Average     2.12             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.245770s wall, 3.843750s user + 0.015625s system = 3.859375s CPU (171.9%)

RUN-1004 : used memory is 1054 MB, reserved memory is 1051 MB, peak memory is 1122 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67147, tnet num: 19325, tinst num: 8024, tnode num: 90906, tedge num: 110588.
TMR-2509 : Cut connection from COM2/reg5_syn_216.b[0] to COM2/reg5_syn_216.f[0].
TMR-2509 : Cut connection from COM2/reg5_syn_216.a[0] to COM2/reg5_syn_216.f[0].
TMR-2509 : Cut connection from COM2/reg5_syn_216.c[0] to COM2/reg5_syn_216.f[0].
TMR-2509 : Cut connection from FMC/top_state[0]_syn_84.e[1] to FMC/top_state[0]_syn_84.f[1].
TMR-2509 : Cut connection from FMC/top_state[0]_syn_84.d[1] to FMC/top_state[0]_syn_84.f[1].
TMR-2509 : Cut connection from FMC/top_state[0]_syn_84.c[1] to FMC/top_state[0]_syn_84.f[1].
TMR-2509 : Cut connection from FMC/top_state[0]_syn_84.a[1] to FMC/top_state[0]_syn_84.f[1].
TMR-2509 : Cut connection from COM2/reg5_syn_224.a[0] to COM2/reg5_syn_224.f[0].
TMR-2509 : Cut connection from COM2/reg5_syn_224.b[0] to COM2/reg5_syn_224.f[0].
TMR-2509 : Cut connection from COM2/reg5_syn_224.c[0] to COM2/reg5_syn_224.f[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.b[1] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.b[0] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.c[1] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.c[0] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.mi[0] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.d[1] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.d[0] to FMC_data[6]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[6]_syn_14.b[1] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.b[0] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.c[1] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.c[0] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.mi[0] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.d[1] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[6]_syn_14.d[0] to FMC_data[6]_syn_14.fx[0].
TMR-2509 : Cut connection from DATA/reg84_syn_50.a[1] to DATA/reg84_syn_50.f[1].
TMR-2509 : Cut connection from DATA/reg84_syn_50.b[1] to DATA/reg84_syn_50.f[1].
TMR-2509 : Cut connection from DATA/reg84_syn_50.c[1] to DATA/reg84_syn_50.f[1].
TMR-2509 : Cut connection from FMC_data[11]_syn_14.b[0] to FMC_data[11]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[11]_syn_14.c[0] to FMC_data[11]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[11]_syn_14.e[0] to FMC_data[11]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[11]_syn_14.d[0] to FMC_data[11]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[11]_syn_14.b[0] to FMC_data[11]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[11]_syn_14.c[0] to FMC_data[11]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[11]_syn_14.e[0] to FMC_data[11]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[11]_syn_14.d[0] to FMC_data[11]_syn_14.f[0].
TMR-2509 : Cut connection from DATA/reg115_syn_114.a[0] to DATA/reg115_syn_114.f[0].
TMR-2509 : Cut connection from DATA/reg115_syn_114.b[0] to DATA/reg115_syn_114.f[0].
TMR-2509 : Cut connection from DATA/reg115_syn_114.c[0] to DATA/reg115_syn_114.f[0].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.b[1] to FMC_data[1]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.c[1] to FMC_data[1]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.e[1] to FMC_data[1]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.d[1] to FMC_data[1]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.b[1] to FMC_data[1]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.c[1] to FMC_data[1]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.e[1] to FMC_data[1]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.d[1] to FMC_data[1]_syn_14.f[1].
TMR-2509 : Cut connection from DATA/reg29_syn_155.a[1] to DATA/reg29_syn_155.f[1].
TMR-2509 : Cut connection from DATA/reg29_syn_155.b[1] to DATA/reg29_syn_155.f[1].
TMR-2509 : Cut connection from DATA/reg29_syn_155.c[1] to DATA/reg29_syn_155.f[1].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.b[1] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.b[0] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.c[1] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.c[0] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.mi[0] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.d[0] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.d[1] to FMC_data[0]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[0]_syn_14.b[1] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.b[0] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.c[1] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.c[0] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.mi[0] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.d[0] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[0]_syn_14.d[1] to FMC_data[0]_syn_14.fx[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_213.a[1] to COM2/uart_com2/reg1_syn_213.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_213.b[1] to COM2/uart_com2/reg1_syn_213.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_213.c[1] to COM2/uart_com2/reg1_syn_213.f[1].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.b[0] to FMC_data[10]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.c[0] to FMC_data[10]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.e[0] to FMC_data[10]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.d[0] to FMC_data[10]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.b[0] to FMC_data[10]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.c[0] to FMC_data[10]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.e[0] to FMC_data[10]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.d[0] to FMC_data[10]_syn_14.f[0].
TMR-2509 : Cut connection from COM2/head_com2/reg3_syn_154.d[1] to COM2/head_com2/reg3_syn_154.f[1].
TMR-2509 : Cut connection from FMC/top_state[0]_syn_82.a[1] to FMC/top_state[0]_syn_82.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_323.d[1] to DATA/reg9_syn_323.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_323.b[1] to DATA/reg9_syn_323.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_323.c[1] to DATA/reg9_syn_323.f[1].
TMR-2509 : Cut connection from COM2/head_com2/reg3_syn_143.b[0] to COM2/head_com2/reg3_syn_143.f[0].
TMR-2509 : Cut connection from COM2/head_com2/reg3_syn_143.a[0] to COM2/head_com2/reg3_syn_143.f[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_221.a[0] to COM2/uart_com2/reg1_syn_221.f[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_221.b[0] to COM2/uart_com2/reg1_syn_221.f[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg1_syn_221.c[0] to COM2/uart_com2/reg1_syn_221.f[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.b[1] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.b[0] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.c[1] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.c[0] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.mi[0] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.d[0] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.d[1] to FMC_data[14]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[14]_syn_14.b[1] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.b[0] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.c[1] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.c[0] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.mi[0] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.d[0] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from FMC_data[14]_syn_14.d[1] to FMC_data[14]_syn_14.fx[0].
TMR-2509 : Cut connection from DATA/reg128_syn_53.a[1] to DATA/reg128_syn_53.f[1].
TMR-2509 : Cut connection from DATA/reg128_syn_53.b[1] to DATA/reg128_syn_53.f[1].
TMR-2509 : Cut connection from DATA/reg128_syn_53.c[1] to DATA/reg128_syn_53.f[1].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.b[0] to FMC_data[1]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.c[0] to FMC_data[1]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.e[0] to FMC_data[1]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.d[0] to FMC_data[1]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[1]_syn_14.b[0] to FMC_data[1]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.c[0] to FMC_data[1]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.e[0] to FMC_data[1]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[1]_syn_14.d[0] to FMC_data[1]_syn_14.f[0].
TMR-2509 : Cut connection from DATA/reg45_syn_153.d[1] to DATA/reg45_syn_153.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg70_syn_118.d[1] to COM2/uart_com2/reg70_syn_118.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg65_syn_142.d[1] to COM2/uart_com2/reg65_syn_142.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg63_syn_139.d[1] to COM2/uart_com2/reg63_syn_139.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg7_syn_127.d[0] to COM2/uart_com2/reg7_syn_127.f[0].
TMR-2509 : Cut connection from DATA/reg36_syn_274.d[0] to DATA/reg36_syn_274.f[0].
TMR-2509 : Cut connection from DATA/reg83_syn_171.a[1] to DATA/reg83_syn_171.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_171.d[1] to DATA/reg83_syn_171.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_171.c[1] to DATA/reg83_syn_171.f[1].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.c[1] to FMC_data[15]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.b[1] to FMC_data[15]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.d[1] to FMC_data[15]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.c[1] to FMC_data[15]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.b[1] to FMC_data[15]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.d[1] to FMC_data[15]_syn_14.f[1].
TMR-2509 : Cut connection from DATA/reg18_syn_129.d[0] to DATA/reg18_syn_129.f[0].
TMR-2509 : Cut connection from DATA/reg41_syn_78.d[1] to DATA/reg41_syn_78.f[1].
TMR-2509 : Cut connection from DATA/reg13_syn_129.b[0] to DATA/reg13_syn_129.f[0].
TMR-2509 : Cut connection from DATA/reg13_syn_129.d[0] to DATA/reg13_syn_129.f[0].
TMR-2509 : Cut connection from DATA/reg13_syn_129.c[0] to DATA/reg13_syn_129.f[0].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.c[0] to FMC_data[7]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.d[0] to FMC_data[7]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.b[0] to FMC_data[7]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.c[0] to FMC_data[7]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.d[0] to FMC_data[7]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.b[0] to FMC_data[7]_syn_14.f[0].
TMR-2509 : Cut connection from DATA/reg114_syn_122.a[0] to DATA/reg114_syn_122.f[0].
TMR-2509 : Cut connection from COM2/STADOP_com2/reg3_syn_111.d[0] to COM2/STADOP_com2/reg3_syn_111.f[0].
TMR-2509 : Cut connection from DATA/reg2_syn_144.d[1] to DATA/reg2_syn_144.f[1].
TMR-2509 : Cut connection from FMC_data[12]_syn_14.b[1] to FMC_data[12]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[12]_syn_14.a[1] to FMC_data[12]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[12]_syn_14.d[1] to FMC_data[12]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[12]_syn_14.b[1] to FMC_data[12]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[12]_syn_14.a[1] to FMC_data[12]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[12]_syn_14.d[1] to FMC_data[12]_syn_14.f[1].
TMR-2509 : Cut connection from DATA/reg4_syn_185.d[1] to DATA/reg4_syn_185.f[1].
TMR-2509 : Cut connection from COM2/uart_com2/reg65_syn_146.d[0] to COM2/uart_com2/reg65_syn_146.f[0].
TMR-2509 : Cut connection from DATA/reg29_syn_136.a[1] to DATA/reg29_syn_136.f[1].
TMR-2509 : Cut connection from DATA/reg29_syn_136.d[1] to DATA/reg29_syn_136.f[1].
TMR-2509 : Cut connection from DATA/reg29_syn_136.c[1] to DATA/reg29_syn_136.f[1].
TMR-2509 : Cut connection from FMC_data[5]_syn_14.c[0] to FMC_data[5]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[5]_syn_14.d[0] to FMC_data[5]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[5]_syn_14.b[0] to FMC_data[5]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[5]_syn_14.c[0] to FMC_data[5]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[5]_syn_14.d[0] to FMC_data[5]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[5]_syn_14.b[0] to FMC_data[5]_syn_14.f[0].
TMR-2509 : Cut connection from DATA/reg128_syn_51.d[1] to DATA/reg128_syn_51.f[1].
TMR-2509 : Cut connection from DATA/reg41_syn_81.d[1] to DATA/reg41_syn_81.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_200.b[1] to DATA/reg83_syn_200.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_200.d[1] to DATA/reg83_syn_200.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_200.c[1] to DATA/reg83_syn_200.f[1].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.c[0] to FMC_data[15]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.d[0] to FMC_data[15]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.b[0] to FMC_data[15]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[15]_syn_14.c[0] to FMC_data[15]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.d[0] to FMC_data[15]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[15]_syn_14.b[0] to FMC_data[15]_syn_14.f[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg65_syn_142.d[0] to COM2/uart_com2/reg65_syn_142.f[0].
TMR-2509 : Cut connection from IIC/reg8_syn_94.d[0] to IIC/reg8_syn_94.f[0].
TMR-2509 : Cut connection from COM2/STADOP_com2/reg3_syn_111.d[1] to COM2/STADOP_com2/reg3_syn_111.f[1].
TMR-2509 : Cut connection from COM2/head_com2/reg2_syn_131.d[0] to COM2/head_com2/reg2_syn_131.f[0].
TMR-2509 : Cut connection from COM2/uart_com2/reg58_syn_124.d[1] to COM2/uart_com2/reg58_syn_124.f[1].
TMR-2509 : Cut connection from COM2/reg5_syn_216.c[1] to COM2/reg5_syn_216.f[1].
TMR-2509 : Cut connection from COM2/reg5_syn_216.b[1] to COM2/reg5_syn_216.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_337.b[0] to DATA/reg9_syn_337.f[0].
TMR-2509 : Cut connection from DATA/reg9_syn_337.c[0] to DATA/reg9_syn_337.f[0].
TMR-2509 : Cut connection from DATA/reg2_syn_154.c[1] to DATA/reg2_syn_154.f[1].
TMR-2509 : Cut connection from FMC/top_state[1]_syn_21.b[1] to FMC/top_state[1]_syn_21.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_337.d[1] to DATA/reg9_syn_337.f[1].
TMR-2509 : Cut connection from DATA/reg9_syn_337.c[1] to DATA/reg9_syn_337.f[1].
TMR-2509 : Cut connection from DATA/reg2_syn_154.d[0] to DATA/reg2_syn_154.f[0].
TMR-2509 : Cut connection from FMC_data[12]_syn_14.d[0] to FMC_data[12]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[12]_syn_14.b[0] to FMC_data[12]_syn_14.Q_q[0]_D.
TMR-2509 : Cut connection from FMC_data[12]_syn_14.d[0] to FMC_data[12]_syn_14.f[0].
TMR-2509 : Cut connection from FMC_data[12]_syn_14.b[0] to FMC_data[12]_syn_14.f[0].
TMR-2509 : Cut connection from DATA/done_div_reg_syn_108.d[0] to DATA/done_div_reg_syn_108.f[0].
TMR-2509 : Cut connection from DATA/reg83_syn_210.d[0] to DATA/reg83_syn_210.f[0].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.d[1] to FMC_data[10]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.b[1] to FMC_data[10]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[10]_syn_14.d[1] to FMC_data[10]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[10]_syn_14.b[1] to FMC_data[10]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.d[1] to FMC_data[7]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.b[1] to FMC_data[7]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.a[1] to FMC_data[7]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[7]_syn_14.d[1] to FMC_data[7]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.b[1] to FMC_data[7]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[7]_syn_14.a[1] to FMC_data[7]_syn_14.f[1].
TMR-2509 : Cut connection from DATA/reg114_syn_126.d[0] to DATA/reg114_syn_126.f[0].
TMR-2509 : Cut connection from FMC_data[5]_syn_14.b[1] to FMC_data[5]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[5]_syn_14.a[1] to FMC_data[5]_syn_14.Q_q[1]_D.
TMR-2509 : Cut connection from FMC_data[5]_syn_14.b[1] to FMC_data[5]_syn_14.f[1].
TMR-2509 : Cut connection from FMC_data[5]_syn_14.a[1] to FMC_data[5]_syn_14.f[1].
TMR-2509 : Cut connection from DATA/reg83_syn_197.d[1] to DATA/reg83_syn_197.f[1].
TMR-2509 : Cut connection from FMC/top_state[3]_syn_11.d[1] to FMC/top_state[3]_syn_11.f[1].
TMR-2507 : Eliminate loop in the timing graph, delete 203 tedges.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.805839s wall, 1.796875s user + 0.000000s system = 1.796875s CPU (99.5%)

RUN-1004 : used memory is 1055 MB, reserved memory is 1053 MB, peak memory is 1122 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19325 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 4 (2 unconstrainted).
TMR-5009 WARNING: No clock constraint on 2 clock net(s): 
		FMC/FMC_data_n_syn_3
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.461071s wall, 1.437500s user + 0.031250s system = 1.468750s CPU (100.5%)

RUN-1004 : used memory is 1060 MB, reserved memory is 1056 MB, peak memory is 1122 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 40c9047af6c60b8681e7bd8c3a1ad8946a1d4edd384cda8aef5d685a0a84c04c -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8024
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19327, pip num: 145795
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 251
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3223 valid insts, and 409213 bits set as '1'.
BIT-1004 : the usercode register value: 00000000010111011011100110110011
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.344666s wall, 124.828125s user + 0.234375s system = 125.062500s CPU (1013.1%)

RUN-1004 : used memory is 1185 MB, reserved memory is 1170 MB, peak memory is 1300 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250821_100342.log"

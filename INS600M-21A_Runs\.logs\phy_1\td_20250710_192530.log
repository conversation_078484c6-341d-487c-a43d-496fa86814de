============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Jul 10 19:25:30 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(520)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.101585s wall, 1.593750s user + 3.500000s system = 5.093750s CPU (99.8%)

RUN-1004 : used memory is 80 MB, reserved memory is 40 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  2.012983s wall, 1.921875s user + 0.093750s system = 2.015625s CPU (100.1%)

RUN-1004 : used memory is 326 MB, reserved memory is 295 MB, peak memory is 330 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "read_sdc -ip Asys_fifo8x8 ../../al_ip/Asys_fifo8x8.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 95730526060544"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 95730526060544"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  9.6999999999999993 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 9.6999999999999993"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  9.6999999999999993 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 9.6999999999999993"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 10 view nodes, 47 trigger nets, 47 data nets.
KIT-1004 : Chipwatcher code = 1100100110110000
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=10,BUS_DIN_NUM=47,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb0100101,32'sb0101101,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01100110,32'sb01111010,32'sb010000000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=156) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=156) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=10,BUS_DIN_NUM=47,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb0100101,32'sb0101101,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01100110,32'sb01111010,32'sb010000000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=10,BUS_DIN_NUM=47,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb0100101,32'sb0101101,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01100110,32'sb01111010,32'sb010000000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=10,BUS_DIN_NUM=47,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb0100101,32'sb0101101,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01100110,32'sb01111010,32'sb010000000})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=156)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=156)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=10,BUS_DIN_NUM=47,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb0100101,32'sb0101101,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01100110,32'sb01111010,32'sb010000000})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=10,BUS_DIN_NUM=47,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb0100101,32'sb0101101,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01100110,32'sb01111010,32'sb010000000})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 26512/42 useful/useless nets, 23201/25 useful/useless insts
SYN-1016 : Merged 49 instances.
SYN-1032 : 26100/26 useful/useless nets, 23672/22 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 5 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 5 mux instances.
SYN-1015 : Optimize round 1, 472 better
SYN-1014 : Optimize round 2
SYN-1032 : 25689/75 useful/useless nets, 23261/80 useful/useless insts
SYN-1015 : Optimize round 2, 160 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.462844s wall, 2.390625s user + 0.062500s system = 2.453125s CPU (99.6%)

RUN-1004 : used memory is 354 MB, reserved memory is 322 MB, peak memory is 356 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 25749/367 useful/useless nets, 23362/52 useful/useless insts
SYN-1016 : Merged 57 instances.
SYN-2571 : Optimize after map_dsp, round 1, 476 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 43 instances.
SYN-2501 : Optimize round 1, 87 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 20 macro adder
SYN-1019 : Optimized 21 mux instances.
SYN-1016 : Merged 17 instances.
SYN-1032 : 26226/5 useful/useless nets, 23839/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 98607, tnet num: 26226, tinst num: 23838, tnode num: 137937, tedge num: 153625.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.253733s wall, 1.218750s user + 0.031250s system = 1.250000s CPU (99.7%)

RUN-1004 : used memory is 520 MB, reserved memory is 490 MB, peak memory is 520 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 26226 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 265 (3.48), #lev = 7 (1.86)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 265 (3.48), #lev = 7 (1.86)
SYN-3001 : Logic optimization runtime opt =   0.04 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 656 instances into 265 LUTs, name keeping = 71%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 477 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 132 adder to BLE ...
SYN-4008 : Packed 132 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.823026s wall, 4.750000s user + 0.093750s system = 4.843750s CPU (100.4%)

RUN-1004 : used memory is 388 MB, reserved memory is 370 MB, peak memory is 649 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.611060s wall, 7.437500s user + 0.187500s system = 7.625000s CPU (100.2%)

RUN-1004 : used memory is 389 MB, reserved memory is 370 MB, peak memory is 649 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/uart_com2/AGRIC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/uart_com2/AGRIC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/uart_com2/AGRIC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/uart_com2/AGRIC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/uart_com2/AGRIC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/uart_com2/AGRIC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/uart_com2/AGRIC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/uart_com2/AGRIC_data[0]
SYN-5055 WARNING: The kept net FMC/Error_state[0] will be merged to another kept net COM3/GNRMC/GNRMC_STARTdy[1]
SYN-5055 WARNING: The kept net COM3/GNRMC/GNRMC_STARTdy[1] will be merged to another kept net COM3/GNRMC/GNRMC_STARTdy[0]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-1032 : 25410/3 useful/useless nets, 22997/0 useful/useless insts
SYN-4016 : Net config_inst_syn_10 driven by BUFG (328 clock/control pins, 0 other pins).
SYN-4027 : Net COM3/GNRMC/u_fifo/wr_to_rd_cross_inst/secondary_clk_i is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net COM3/GNRMC/u_fifo/wr_to_rd_cross_inst/secondary_clk_i as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 22997 instances
RUN-0007 : 7160 luts, 14272 seqs, 956 mslices, 502 lslices, 60 pads, 42 brams, 0 dsps
RUN-1001 : There are total 25410 nets
RUN-1001 : 19640 nets have 2 pins
RUN-1001 : 4284 nets have [3 - 5] pins
RUN-1001 : 1076 nets have [6 - 10] pins
RUN-1001 : 258 nets have [11 - 20] pins
RUN-1001 : 117 nets have [21 - 99] pins
RUN-1001 : 35 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4753     
RUN-1001 :   No   |  No   |  Yes  |     735     
RUN-1001 :   No   |  Yes  |  No   |     92      
RUN-1001 :   Yes  |  No   |  No   |    8131     
RUN-1001 :   Yes  |  No   |  Yes  |     510     
RUN-1001 :   Yes  |  Yes  |  No   |     51      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  371  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 380
PHY-3001 : Initial placement ...
PHY-3001 : design contains 22995 instances, 7160 luts, 14272 seqs, 1458 slices, 291 macros(1458 instances: 956 mslices 502 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 73%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 96755, tnet num: 25408, tinst num: 22995, tnode num: 135974, tedge num: 151809.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.316503s wall, 1.250000s user + 0.046875s system = 1.296875s CPU (98.5%)

RUN-1004 : used memory is 590 MB, reserved memory is 564 MB, peak memory is 649 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 25408 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.269704s wall, 2.171875s user + 0.078125s system = 2.250000s CPU (99.1%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.92241e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 22995.
PHY-3001 : Level 1 #clusters 3167.
PHY-3001 : End clustering;  0.191562s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (146.8%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 73%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 996769, overlap = 733.75
PHY-3002 : Step(2): len = 879998, overlap = 859.844
PHY-3002 : Step(3): len = 612929, overlap = 1010.03
PHY-3002 : Step(4): len = 546264, overlap = 1085.19
PHY-3002 : Step(5): len = 433552, overlap = 1227.09
PHY-3002 : Step(6): len = 375107, overlap = 1320.03
PHY-3002 : Step(7): len = 322556, overlap = 1444.41
PHY-3002 : Step(8): len = 285092, overlap = 1544
PHY-3002 : Step(9): len = 246338, overlap = 1617.12
PHY-3002 : Step(10): len = 216351, overlap = 1655.56
PHY-3002 : Step(11): len = 185851, overlap = 1683.81
PHY-3002 : Step(12): len = 170895, overlap = 1715.25
PHY-3002 : Step(13): len = 154017, overlap = 1747.12
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.06286e-06
PHY-3002 : Step(14): len = 159564, overlap = 1710.91
PHY-3002 : Step(15): len = 200899, overlap = 1636.28
PHY-3002 : Step(16): len = 207629, overlap = 1518.28
PHY-3002 : Step(17): len = 228253, overlap = 1417.78
PHY-3002 : Step(18): len = 215754, overlap = 1390.03
PHY-3002 : Step(19): len = 214526, overlap = 1362.81
PHY-3002 : Step(20): len = 203348, overlap = 1352.41
PHY-3002 : Step(21): len = 203642, overlap = 1352.53
PHY-3002 : Step(22): len = 194931, overlap = 1334.84
PHY-3002 : Step(23): len = 193904, overlap = 1322.66
PHY-3002 : Step(24): len = 188173, overlap = 1312.34
PHY-3002 : Step(25): len = 187833, overlap = 1309.03
PHY-3002 : Step(26): len = 183246, overlap = 1309.44
PHY-3002 : Step(27): len = 184945, overlap = 1317.09
PHY-3002 : Step(28): len = 181086, overlap = 1311.44
PHY-3002 : Step(29): len = 181909, overlap = 1308.66
PHY-3002 : Step(30): len = 180880, overlap = 1306.97
PHY-3002 : Step(31): len = 180188, overlap = 1320.56
PHY-3002 : Step(32): len = 178256, overlap = 1314.5
PHY-3002 : Step(33): len = 177838, overlap = 1307.03
PHY-3002 : Step(34): len = 174801, overlap = 1274.62
PHY-3002 : Step(35): len = 175334, overlap = 1272.12
PHY-3002 : Step(36): len = 172924, overlap = 1282.25
PHY-3002 : Step(37): len = 172174, overlap = 1288.41
PHY-3002 : Step(38): len = 170240, overlap = 1297.81
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.12573e-06
PHY-3002 : Step(39): len = 178676, overlap = 1271.75
PHY-3002 : Step(40): len = 192426, overlap = 1204.5
PHY-3002 : Step(41): len = 192623, overlap = 1171.66
PHY-3002 : Step(42): len = 195990, overlap = 1140
PHY-3002 : Step(43): len = 193806, overlap = 1121.5
PHY-3002 : Step(44): len = 194444, overlap = 1116.06
PHY-3002 : Step(45): len = 194015, overlap = 1099.88
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.25145e-06
PHY-3002 : Step(46): len = 206326, overlap = 1059.5
PHY-3002 : Step(47): len = 222966, overlap = 1002.97
PHY-3002 : Step(48): len = 228672, overlap = 997.531
PHY-3002 : Step(49): len = 235449, overlap = 980.188
PHY-3002 : Step(50): len = 234442, overlap = 970.219
PHY-3002 : Step(51): len = 235638, overlap = 952.906
PHY-3002 : Step(52): len = 234397, overlap = 948.125
PHY-3002 : Step(53): len = 234717, overlap = 955.875
PHY-3002 : Step(54): len = 232450, overlap = 957.688
PHY-3002 : Step(55): len = 233041, overlap = 937.531
PHY-3002 : Step(56): len = 232195, overlap = 916.875
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 8.5029e-06
PHY-3002 : Step(57): len = 250013, overlap = 856.844
PHY-3002 : Step(58): len = 277061, overlap = 789.5
PHY-3002 : Step(59): len = 283517, overlap = 760.5
PHY-3002 : Step(60): len = 284162, overlap = 766.281
PHY-3002 : Step(61): len = 280329, overlap = 757.406
PHY-3002 : Step(62): len = 276932, overlap = 771.031
PHY-3002 : Step(63): len = 273255, overlap = 759.031
PHY-3002 : Step(64): len = 271880, overlap = 748.969
PHY-3002 : Step(65): len = 271413, overlap = 733.812
PHY-3002 : Step(66): len = 271741, overlap = 713.031
PHY-3002 : Step(67): len = 271100, overlap = 703.25
PHY-3002 : Step(68): len = 270591, overlap = 696.812
PHY-3002 : Step(69): len = 269264, overlap = 686.875
PHY-3002 : Step(70): len = 267837, overlap = 706.25
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.70058e-05
PHY-3002 : Step(71): len = 282393, overlap = 636.969
PHY-3002 : Step(72): len = 293991, overlap = 582.469
PHY-3002 : Step(73): len = 297093, overlap = 566.344
PHY-3002 : Step(74): len = 298050, overlap = 556
PHY-3002 : Step(75): len = 296701, overlap = 562.125
PHY-3002 : Step(76): len = 295447, overlap = 567.812
PHY-3002 : Step(77): len = 293694, overlap = 561.344
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.40116e-05
PHY-3002 : Step(78): len = 304711, overlap = 537.938
PHY-3002 : Step(79): len = 312582, overlap = 554.344
PHY-3002 : Step(80): len = 316235, overlap = 556.406
PHY-3002 : Step(81): len = 318537, overlap = 529.594
PHY-3002 : Step(82): len = 318048, overlap = 527.969
PHY-3002 : Step(83): len = 317967, overlap = 539.531
PHY-3002 : Step(84): len = 317065, overlap = 545.062
PHY-3002 : Step(85): len = 317585, overlap = 529.625
PHY-3002 : Step(86): len = 316668, overlap = 520.812
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 6.80232e-05
PHY-3002 : Step(87): len = 324884, overlap = 496.906
PHY-3002 : Step(88): len = 333348, overlap = 466.156
PHY-3002 : Step(89): len = 336290, overlap = 450.719
PHY-3002 : Step(90): len = 338125, overlap = 431.156
PHY-3002 : Step(91): len = 338648, overlap = 430.219
PHY-3002 : Step(92): len = 339514, overlap = 416.125
PHY-3002 : Step(93): len = 339011, overlap = 407.375
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000128177
PHY-3002 : Step(94): len = 343522, overlap = 415.5
PHY-3002 : Step(95): len = 349363, overlap = 409.094
PHY-3002 : Step(96): len = 353049, overlap = 396.75
PHY-3002 : Step(97): len = 355919, overlap = 376.844
PHY-3002 : Step(98): len = 356306, overlap = 376.312
PHY-3002 : Step(99): len = 355737, overlap = 372.781
PHY-3002 : Step(100): len = 354575, overlap = 385.594
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000230324
PHY-3002 : Step(101): len = 356738, overlap = 368.938
PHY-3002 : Step(102): len = 359512, overlap = 369.594
PHY-3002 : Step(103): len = 360885, overlap = 351.469
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000379281
PHY-3002 : Step(104): len = 362142, overlap = 345.844
PHY-3002 : Step(105): len = 366397, overlap = 332.844
PHY-3002 : Step(106): len = 367540, overlap = 331.688
PHY-3002 : Step(107): len = 368427, overlap = 324.344
PHY-3002 : Step(108): len = 369127, overlap = 307.75
PHY-3002 : Step(109): len = 369114, overlap = 298.5
PHY-3002 : Step(110): len = 368433, overlap = 297.844
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.011789s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (397.6%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 78%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/25410.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 496352, over cnt = 1638(4%), over = 7667, worst = 42
PHY-1001 : End global iterations;  0.882980s wall, 1.312500s user + 0.015625s system = 1.328125s CPU (150.4%)

PHY-1001 : Congestion index: top1 = 82.44, top5 = 60.89, top10 = 50.84, top15 = 44.58.
PHY-3001 : End congestion estimation;  1.154425s wall, 1.593750s user + 0.015625s system = 1.609375s CPU (139.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 25408 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.009753s wall, 0.968750s user + 0.046875s system = 1.015625s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000109919
PHY-3002 : Step(111): len = 411437, overlap = 273.281
PHY-3002 : Step(112): len = 434379, overlap = 267.938
PHY-3002 : Step(113): len = 423110, overlap = 275.75
PHY-3002 : Step(114): len = 422956, overlap = 277.844
PHY-3002 : Step(115): len = 430002, overlap = 276
PHY-3002 : Step(116): len = 429238, overlap = 272.219
PHY-3002 : Step(117): len = 432612, overlap = 274.188
PHY-3002 : Step(118): len = 437121, overlap = 270.406
PHY-3002 : Step(119): len = 437266, overlap = 271.438
PHY-3002 : Step(120): len = 438564, overlap = 264.906
PHY-3002 : Step(121): len = 438538, overlap = 255.625
PHY-3002 : Step(122): len = 439293, overlap = 244.312
PHY-3002 : Step(123): len = 437378, overlap = 239.344
PHY-3002 : Step(124): len = 437933, overlap = 231.562
PHY-3002 : Step(125): len = 437348, overlap = 229.75
PHY-3002 : Step(126): len = 438124, overlap = 225.188
PHY-3002 : Step(127): len = 437215, overlap = 226.812
PHY-3002 : Step(128): len = 438309, overlap = 223.438
PHY-3002 : Step(129): len = 437278, overlap = 217.688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000219838
PHY-3002 : Step(130): len = 437604, overlap = 215.156
PHY-3002 : Step(131): len = 439484, overlap = 214.406
PHY-3002 : Step(132): len = 444184, overlap = 212.406
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000439677
PHY-3002 : Step(133): len = 450796, overlap = 203.031
PHY-3002 : Step(134): len = 456706, overlap = 197.125
PHY-3002 : Step(135): len = 457508, overlap = 195.25
PHY-3002 : Step(136): len = 462222, overlap = 192.375
PHY-3002 : Step(137): len = 469204, overlap = 190.094
PHY-3002 : Step(138): len = 471346, overlap = 181.375
PHY-3002 : Step(139): len = 473995, overlap = 178.812
PHY-3002 : Step(140): len = 476576, overlap = 174.094
PHY-3002 : Step(141): len = 480200, overlap = 162.688
PHY-3002 : Step(142): len = 480872, overlap = 156.312
PHY-3002 : Step(143): len = 481424, overlap = 156.562
PHY-3002 : Step(144): len = 480107, overlap = 167.188
PHY-3002 : Step(145): len = 479024, overlap = 176.156
PHY-3002 : Step(146): len = 477397, overlap = 183.719
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000879354
PHY-3002 : Step(147): len = 479059, overlap = 178.406
PHY-3002 : Step(148): len = 482322, overlap = 176.312
PHY-3002 : Step(149): len = 486315, overlap = 167.781
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(150): len = 487514, overlap = 166.625
PHY-3002 : Step(151): len = 492935, overlap = 168.156
PHY-3002 : Step(152): len = 498591, overlap = 172.438
PHY-3002 : Step(153): len = 501182, overlap = 171.25
PHY-3002 : Step(154): len = 501857, overlap = 168.188
PHY-3002 : Step(155): len = 502989, overlap = 167.688
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 78%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 73/25410.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 588544, over cnt = 2618(7%), over = 14374, worst = 77
PHY-1001 : End global iterations;  1.070535s wall, 1.687500s user + 0.046875s system = 1.734375s CPU (162.0%)

PHY-1001 : Congestion index: top1 = 90.95, top5 = 70.28, top10 = 60.49, top15 = 54.59.
PHY-3001 : End congestion estimation;  1.486079s wall, 2.125000s user + 0.046875s system = 2.171875s CPU (146.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 25408 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.281974s wall, 1.265625s user + 0.015625s system = 1.281250s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000110036
PHY-3002 : Step(156): len = 494102, overlap = 506.5
PHY-3002 : Step(157): len = 499942, overlap = 412.438
PHY-3002 : Step(158): len = 498127, overlap = 378.906
PHY-3002 : Step(159): len = 491222, overlap = 355.406
PHY-3002 : Step(160): len = 484881, overlap = 327.562
PHY-3002 : Step(161): len = 480346, overlap = 312.094
PHY-3002 : Step(162): len = 473810, overlap = 318.75
PHY-3002 : Step(163): len = 469742, overlap = 308.688
PHY-3002 : Step(164): len = 466122, overlap = 313.719
PHY-3002 : Step(165): len = 461221, overlap = 303.406
PHY-3002 : Step(166): len = 460600, overlap = 306.406
PHY-3002 : Step(167): len = 458117, overlap = 306.438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000220073
PHY-3002 : Step(168): len = 458947, overlap = 285.969
PHY-3002 : Step(169): len = 461286, overlap = 279.875
PHY-3002 : Step(170): len = 461172, overlap = 277.594
PHY-3002 : Step(171): len = 462505, overlap = 275.188
PHY-3002 : Step(172): len = 463912, overlap = 265.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000440145
PHY-3002 : Step(173): len = 464459, overlap = 262.156
PHY-3002 : Step(174): len = 470364, overlap = 254.156
PHY-3002 : Step(175): len = 473705, overlap = 252.125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000880291
PHY-3002 : Step(176): len = 474639, overlap = 246.906
PHY-3002 : Step(177): len = 480615, overlap = 231.938
PHY-3002 : Step(178): len = 488148, overlap = 220.5
PHY-3002 : Step(179): len = 489721, overlap = 218.219
PHY-3002 : Step(180): len = 488976, overlap = 214.875
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00176058
PHY-3002 : Step(181): len = 490207, overlap = 212.031
PHY-3002 : Step(182): len = 493052, overlap = 215
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 96755, tnet num: 25408, tinst num: 22995, tnode num: 135974, tedge num: 151809.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.644140s wall, 1.562500s user + 0.078125s system = 1.640625s CPU (99.8%)

RUN-1004 : used memory is 630 MB, reserved memory is 608 MB, peak memory is 791 MB
OPT-1001 : Total overflow 644.09 peak overflow 3.91
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 489/25410.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 597816, over cnt = 3029(8%), over = 11772, worst = 29
PHY-1001 : End global iterations;  1.266081s wall, 2.140625s user + 0.046875s system = 2.187500s CPU (172.8%)

PHY-1001 : Congestion index: top1 = 67.11, top5 = 53.69, top10 = 48.14, top15 = 44.70.
PHY-1001 : End incremental global routing;  1.505359s wall, 2.390625s user + 0.046875s system = 2.437500s CPU (161.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 25408 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.082360s wall, 1.046875s user + 0.046875s system = 1.093750s CPU (101.1%)

OPT-1001 : 20 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 22912 has valid locations, 332 needs to be replaced
PHY-3001 : design contains 23307 instances, 7307 luts, 14437 seqs, 1458 slices, 291 macros(1458 instances: 956 mslices 502 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 517922
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 79%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 19524/25722.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 618936, over cnt = 3103(8%), over = 11870, worst = 29
PHY-1001 : End global iterations;  0.218176s wall, 0.421875s user + 0.015625s system = 0.437500s CPU (200.5%)

PHY-1001 : Congestion index: top1 = 67.61, top5 = 54.83, top10 = 48.96, top15 = 45.42.
PHY-3001 : End congestion estimation;  0.463007s wall, 0.656250s user + 0.015625s system = 0.671875s CPU (145.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 97914, tnet num: 25720, tinst num: 23307, tnode num: 137594, tedge num: 153503.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.611653s wall, 1.578125s user + 0.031250s system = 1.609375s CPU (99.9%)

RUN-1004 : used memory is 684 MB, reserved memory is 682 MB, peak memory is 791 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 25720 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.161614s wall, 3.125000s user + 0.046875s system = 3.171875s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(183): len = 517303, overlap = 12.8125
PHY-3002 : Step(184): len = 519261, overlap = 12.625
PHY-3002 : Step(185): len = 519841, overlap = 12.5
PHY-3002 : Step(186): len = 520872, overlap = 12.4375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.05027e-05
PHY-3002 : Step(187): len = 520897, overlap = 12.375
PHY-3002 : Step(188): len = 521412, overlap = 12.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(189): len = 521660, overlap = 12.8125
PHY-3002 : Step(190): len = 522469, overlap = 12.9375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 79%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 19541/25722.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 620784, over cnt = 3128(8%), over = 12055, worst = 29
PHY-1001 : End global iterations;  0.229824s wall, 0.375000s user + 0.015625s system = 0.390625s CPU (170.0%)

PHY-1001 : Congestion index: top1 = 68.25, top5 = 55.00, top10 = 49.27, top15 = 45.71.
PHY-3001 : End congestion estimation;  0.485542s wall, 0.640625s user + 0.015625s system = 0.656250s CPU (135.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 25720 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.197501s wall, 1.203125s user + 0.000000s system = 1.203125s CPU (100.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00045648
PHY-3002 : Step(191): len = 522400, overlap = 219.625
PHY-3002 : Step(192): len = 522892, overlap = 219.719
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000912959
PHY-3002 : Step(193): len = 523154, overlap = 219.875
PHY-3002 : Step(194): len = 523703, overlap = 219.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00182592
PHY-3002 : Step(195): len = 523908, overlap = 219.125
PHY-3002 : Step(196): len = 524209, overlap = 218.875
PHY-3001 : Final: Len = 524209, Over = 218.875
PHY-3001 : End incremental placement;  6.789813s wall, 7.390625s user + 0.437500s system = 7.828125s CPU (115.3%)

OPT-1001 : Total overflow 651.75 peak overflow 3.91
OPT-1001 : End high-fanout net optimization;  9.959851s wall, 11.406250s user + 0.531250s system = 11.937500s CPU (119.9%)

OPT-1001 : Current memory(MB): used = 795, reserve = 778, peak = 815.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 19565/25722.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 622424, over cnt = 3066(8%), over = 11032, worst = 29
PHY-1002 : len = 688288, over cnt = 2366(6%), over = 5993, worst = 22
PHY-1002 : len = 759696, over cnt = 702(1%), over = 1271, worst = 19
PHY-1002 : len = 774872, over cnt = 254(0%), over = 375, worst = 11
PHY-1002 : len = 784656, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.742875s wall, 2.578125s user + 0.000000s system = 2.578125s CPU (147.9%)

PHY-1001 : Congestion index: top1 = 54.66, top5 = 48.45, top10 = 45.14, top15 = 42.95.
OPT-1001 : End congestion update;  2.003654s wall, 2.843750s user + 0.000000s system = 2.843750s CPU (141.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 25720 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.952303s wall, 0.937500s user + 0.015625s system = 0.953125s CPU (100.1%)

OPT-0007 : Start: WNS 3192 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.962927s wall, 3.781250s user + 0.015625s system = 3.796875s CPU (128.1%)

OPT-1001 : Current memory(MB): used = 766, reserve = 751, peak = 815.
OPT-1001 : End physical optimization;  14.927623s wall, 17.265625s user + 0.640625s system = 17.906250s CPU (120.0%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 7307 LUT to BLE ...
SYN-4008 : Packed 7307 LUT and 2736 SEQ to BLE.
SYN-4003 : Packing 11701 remaining SEQ's ...
SYN-4005 : Packed 4951 SEQ with LUT/SLICE
SYN-4006 : 149 single LUT's are left
SYN-4006 : 6750 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 14057/15852 primitive instances ...
PHY-3001 : End packing;  3.502727s wall, 3.500000s user + 0.000000s system = 3.500000s CPU (99.9%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 9248 instances
RUN-1001 : 4571 mslices, 4570 lslices, 60 pads, 42 brams, 0 dsps
RUN-1001 : There are total 23110 nets
RUN-1001 : 16950 nets have 2 pins
RUN-1001 : 4478 nets have [3 - 5] pins
RUN-1001 : 1042 nets have [6 - 10] pins
RUN-1001 : 387 nets have [11 - 20] pins
RUN-1001 : 240 nets have [21 - 99] pins
RUN-1001 : 13 nets have 100+ pins
PHY-3001 : design contains 9246 instances, 9141 slices, 291 macros(1458 instances: 956 mslices 502 lslices)
PHY-3001 : Cell area utilization is 94%
PHY-3001 : After packing: Len = 559507, Over = 495.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 94%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 9238/23110.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 746568, over cnt = 2333(6%), over = 3951, worst = 10
PHY-1002 : len = 757008, over cnt = 1617(4%), over = 2241, worst = 8
PHY-1002 : len = 772392, over cnt = 898(2%), over = 1167, worst = 6
PHY-1002 : len = 787120, over cnt = 338(0%), over = 433, worst = 6
PHY-1002 : len = 797352, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.660564s wall, 2.593750s user + 0.093750s system = 2.687500s CPU (161.8%)

PHY-1001 : Congestion index: top1 = 57.28, top5 = 51.01, top10 = 46.94, top15 = 44.43.
PHY-3001 : End congestion estimation;  2.036529s wall, 2.953125s user + 0.093750s system = 3.046875s CPU (149.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82106, tnet num: 23108, tinst num: 9246, tnode num: 110830, tedge num: 134063.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.910109s wall, 1.859375s user + 0.046875s system = 1.906250s CPU (99.8%)

RUN-1004 : used memory is 676 MB, reserved memory is 669 MB, peak memory is 815 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 23108 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.050107s wall, 2.984375s user + 0.062500s system = 3.046875s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.12522e-05
PHY-3002 : Step(197): len = 551449, overlap = 478.75
PHY-3002 : Step(198): len = 546054, overlap = 485
PHY-3002 : Step(199): len = 539674, overlap = 499.75
PHY-3002 : Step(200): len = 535634, overlap = 514.5
PHY-3002 : Step(201): len = 530610, overlap = 516
PHY-3002 : Step(202): len = 527493, overlap = 524
PHY-3002 : Step(203): len = 524487, overlap = 521.75
PHY-3002 : Step(204): len = 522450, overlap = 529.5
PHY-3002 : Step(205): len = 520943, overlap = 527.25
PHY-3002 : Step(206): len = 519900, overlap = 531.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.25043e-05
PHY-3002 : Step(207): len = 525056, overlap = 517.25
PHY-3002 : Step(208): len = 529747, overlap = 507.75
PHY-3002 : Step(209): len = 528217, overlap = 506.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000165009
PHY-3002 : Step(210): len = 537687, overlap = 490.5
PHY-3002 : Step(211): len = 544960, overlap = 483
PHY-3002 : Step(212): len = 542540, overlap = 481.5
PHY-3002 : Step(213): len = 541020, overlap = 481.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.449466s wall, 0.671875s user + 0.328125s system = 1.000000s CPU (222.5%)

PHY-3001 : Trial Legalized: Len = 828233
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 94%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 657/23110.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 961472, over cnt = 3479(9%), over = 6067, worst = 8
PHY-1002 : len = 987792, over cnt = 2267(6%), over = 3247, worst = 6
PHY-1002 : len = 1.00814e+06, over cnt = 1326(3%), over = 1846, worst = 6
PHY-1002 : len = 1.03583e+06, over cnt = 379(1%), over = 516, worst = 5
PHY-1002 : len = 1.04629e+06, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.829602s wall, 4.125000s user + 0.062500s system = 4.187500s CPU (148.0%)

PHY-1001 : Congestion index: top1 = 59.29, top5 = 54.51, top10 = 51.47, top15 = 49.22.
PHY-3001 : End congestion estimation;  3.226098s wall, 4.515625s user + 0.062500s system = 4.578125s CPU (141.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 23108 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.364072s wall, 1.359375s user + 0.000000s system = 1.359375s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000472323
PHY-3002 : Step(214): len = 745184, overlap = 141
PHY-3002 : Step(215): len = 710078, overlap = 170.5
PHY-3002 : Step(216): len = 688979, overlap = 193.75
PHY-3002 : Step(217): len = 671838, overlap = 215.75
PHY-3002 : Step(218): len = 656131, overlap = 244.5
PHY-3002 : Step(219): len = 644096, overlap = 269.5
PHY-3002 : Step(220): len = 636254, overlap = 284.5
PHY-3002 : Step(221): len = 626894, overlap = 305.75
PHY-3002 : Step(222): len = 619562, overlap = 328.25
PHY-3002 : Step(223): len = 616452, overlap = 332.5
PHY-3002 : Step(224): len = 613507, overlap = 340
PHY-3002 : Step(225): len = 610432, overlap = 347.25
PHY-3002 : Step(226): len = 608003, overlap = 352.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000848311
PHY-3002 : Step(227): len = 609529, overlap = 350
PHY-3002 : Step(228): len = 611565, overlap = 345
PHY-3002 : Step(229): len = 614992, overlap = 334.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00153204
PHY-3002 : Step(230): len = 616691, overlap = 331
PHY-3002 : Step(231): len = 621347, overlap = 329.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  1.329348s wall, 1.328125s user + 0.000000s system = 1.328125s CPU (99.9%)

PHY-3001 : Legalized: Len = 706393, Over = 0
PHY-3001 : Spreading special nets. 46 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.085451s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (109.7%)

PHY-3001 : 78 instances has been re-located, deltaX = 34, deltaY = 53, maxDist = 2.
PHY-3001 : Final: Len = 707747, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82106, tnet num: 23108, tinst num: 9246, tnode num: 110830, tedge num: 134063.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.145074s wall, 2.140625s user + 0.000000s system = 2.140625s CPU (99.8%)

RUN-1004 : used memory is 678 MB, reserved memory is 686 MB, peak memory is 832 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3077/23110.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 839464, over cnt = 3330(9%), over = 5581, worst = 7
PHY-1002 : len = 860528, over cnt = 2205(6%), over = 3122, worst = 7
PHY-1002 : len = 891496, over cnt = 825(2%), over = 1046, worst = 5
PHY-1002 : len = 907488, over cnt = 196(0%), over = 221, worst = 4
PHY-1002 : len = 914552, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.506764s wall, 3.921875s user + 0.015625s system = 3.937500s CPU (157.1%)

PHY-1001 : Congestion index: top1 = 55.43, top5 = 51.22, top10 = 48.48, top15 = 46.49.
PHY-1001 : End incremental global routing;  2.844246s wall, 4.265625s user + 0.015625s system = 4.281250s CPU (150.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 23108 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.384579s wall, 1.375000s user + 0.000000s system = 1.375000s CPU (99.3%)

OPT-1001 : 0 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 9183 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 9246 instances, 9141 slices, 291 macros(1458 instances: 956 mslices 502 lslices)
PHY-3001 : End incremental placement; No cells to be placed.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  5.146286s wall, 6.703125s user + 0.015625s system = 6.718750s CPU (130.6%)

OPT-1001 : Current memory(MB): used = 791, reserve = 784, peak = 832.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 20822/23110.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 914552, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.133241s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (105.5%)

PHY-1001 : Congestion index: top1 = 55.43, top5 = 51.22, top10 = 48.48, top15 = 46.49.
OPT-1001 : End congestion update;  0.481205s wall, 0.484375s user + 0.000000s system = 0.484375s CPU (100.7%)

OPT-1001 : Update timing in Manhattan mode
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82106, tnet num: 23108, tinst num: 9246, tnode num: 110830, tedge num: 134063.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.265331s wall, 2.250000s user + 0.015625s system = 2.265625s CPU (100.0%)

RUN-1004 : used memory is 686 MB, reserved memory is 667 MB, peak memory is 832 MB
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 23108 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  3.176011s wall, 3.156250s user + 0.015625s system = 3.171875s CPU (99.9%)

OPT-0007 : Start: WNS 3092 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  3.663610s wall, 3.656250s user + 0.015625s system = 3.671875s CPU (100.2%)

OPT-1001 : Current memory(MB): used = 776, reserve = 767, peak = 832.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 23108 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.939284s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (99.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 20822/23110.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 914552, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.138229s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (101.7%)

PHY-1001 : Congestion index: top1 = 55.43, top5 = 51.22, top10 = 48.48, top15 = 46.49.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 23108 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.907753s wall, 0.906250s user + 0.000000s system = 0.906250s CPU (99.8%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3092 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 55.034483
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3092ps with logic level 8 
OPT-1001 : End physical optimization;  13.619818s wall, 15.343750s user + 0.031250s system = 15.375000s CPU (112.9%)

RUN-1003 : finish command "place" in  87.088869s wall, 159.125000s user + 7.671875s system = 166.796875s CPU (191.5%)

RUN-1004 : used memory is 698 MB, reserved memory is 704 MB, peak memory is 832 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.747259s wall, 3.109375s user + 0.031250s system = 3.140625s CPU (179.7%)

RUN-1004 : used memory is 698 MB, reserved memory is 704 MB, peak memory is 832 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 9248 instances
RUN-1001 : 4571 mslices, 4570 lslices, 60 pads, 42 brams, 0 dsps
RUN-1001 : There are total 23110 nets
RUN-1001 : 16950 nets have 2 pins
RUN-1001 : 4478 nets have [3 - 5] pins
RUN-1001 : 1042 nets have [6 - 10] pins
RUN-1001 : 387 nets have [11 - 20] pins
RUN-1001 : 240 nets have [21 - 99] pins
RUN-1001 : 13 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82106, tnet num: 23108, tinst num: 9246, tnode num: 110830, tedge num: 134063.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.996553s wall, 1.984375s user + 0.000000s system = 1.984375s CPU (99.4%)

RUN-1004 : used memory is 677 MB, reserved memory is 670 MB, peak memory is 832 MB
PHY-1001 : 4571 mslices, 4570 lslices, 60 pads, 42 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 23108 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 813824, over cnt = 3385(9%), over = 5986, worst = 7
PHY-1002 : len = 841120, over cnt = 2178(6%), over = 3142, worst = 7
PHY-1002 : len = 871392, over cnt = 1030(2%), over = 1349, worst = 6
PHY-1002 : len = 889896, over cnt = 288(0%), over = 364, worst = 4
PHY-1002 : len = 897936, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.405708s wall, 4.015625s user + 0.140625s system = 4.156250s CPU (172.8%)

PHY-1001 : Congestion index: top1 = 54.81, top5 = 50.49, top10 = 47.92, top15 = 45.95.
PHY-1001 : End global routing;  2.779452s wall, 4.375000s user + 0.156250s system = 4.531250s CPU (163.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 789, reserve = 786, peak = 832.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net COM3/GNRMC/u_fifo/wr_to_rd_cross_inst/secondary_clk_i will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 1062, reserve = 1059, peak = 1062.
PHY-1001 : End build detailed router design. 4.860401s wall, 4.812500s user + 0.046875s system = 4.859375s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 197312, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.912313s wall, 0.906250s user + 0.000000s system = 0.906250s CPU (99.3%)

PHY-1001 : Current memory(MB): used = 1099, reserve = 1096, peak = 1099.
PHY-1001 : End phase 1; 0.921832s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (100.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 51% nets.
PHY-1001 : Routed 58% nets.
PHY-1001 : Routed 67% nets.
PHY-1001 : Routed 78% nets.
PHY-1001 : Routed 94% nets.
PHY-1022 : len = 2.19632e+06, over cnt = 3713(0%), over = 3738, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1116, reserve = 1108, peak = 1116.
PHY-1001 : End initial routed; 28.846010s wall, 70.453125s user + 0.406250s system = 70.859375s CPU (245.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/21881(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.255   |   0.000   |   0   
RUN-1001 :   Hold   |   0.130   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.705864s wall, 3.703125s user + 0.000000s system = 3.703125s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 1130, reserve = 1121, peak = 1130.
PHY-1001 : End phase 2; 32.552026s wall, 74.156250s user + 0.406250s system = 74.562500s CPU (229.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 2.19632e+06, over cnt = 3713(0%), over = 3738, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.267287s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (99.4%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 2.14035e+06, over cnt = 1466(0%), over = 1475, worst = 2, crit = 0
PHY-1001 : End DR Iter 1; 3.521897s wall, 4.328125s user + 0.031250s system = 4.359375s CPU (123.8%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 2.13998e+06, over cnt = 600(0%), over = 601, worst = 2, crit = 0
PHY-1001 : End DR Iter 2; 1.404500s wall, 1.625000s user + 0.015625s system = 1.640625s CPU (116.8%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 2.14415e+06, over cnt = 176(0%), over = 176, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.934303s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (110.4%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 2.14714e+06, over cnt = 77(0%), over = 77, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.556025s wall, 0.578125s user + 0.000000s system = 0.578125s CPU (104.0%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 2.15045e+06, over cnt = 13(0%), over = 13, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.664438s wall, 0.656250s user + 0.000000s system = 0.656250s CPU (98.8%)

PHY-1001 : ===== DR Iter 6 =====
PHY-1022 : len = 2.15088e+06, over cnt = 6(0%), over = 6, worst = 1, crit = 0
PHY-1001 : End DR Iter 6; 0.396869s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (102.4%)

PHY-1001 : ===== DR Iter 7 =====
PHY-1022 : len = 2.15096e+06, over cnt = 5(0%), over = 5, worst = 1, crit = 0
PHY-1001 : End DR Iter 7; 0.555701s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (98.4%)

PHY-1001 : ===== DR Iter 8 =====
PHY-1022 : len = 2.15106e+06, over cnt = 5(0%), over = 5, worst = 1, crit = 0
PHY-1001 : End DR Iter 8; 0.194864s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (104.2%)

PHY-1001 : ==== DR Iter 9 ====
PHY-1022 : len = 2.15106e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 9; 0.189795s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (115.3%)

PHY-1001 : ==== DR Iter 10 ====
PHY-1022 : len = 2.15106e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 10; 0.198685s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (118.0%)

PHY-1001 : ==== DR Iter 11 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 11; 0.213527s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (102.4%)

PHY-1001 : ==== DR Iter 12 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 12; 0.350451s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (98.1%)

PHY-1001 : ===== DR Iter 13 =====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 13; 0.188016s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (99.7%)

PHY-1001 : ==== DR Iter 14 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 14; 0.173614s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (117.0%)

PHY-1001 : ==== DR Iter 15 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 15; 0.191606s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (106.0%)

PHY-1001 : ==== DR Iter 16 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 16; 0.215064s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (109.0%)

PHY-1001 : ==== DR Iter 17 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 17; 0.349528s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (98.3%)

PHY-1001 : ==== DR Iter 18 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 18; 0.375693s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (99.8%)

PHY-1001 : ===== DR Iter 19 =====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 19; 0.176816s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (106.0%)

PHY-1001 : ==== DR Iter 20 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 20; 0.196901s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (103.2%)

PHY-1001 : ==== DR Iter 21 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 21; 0.194464s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (96.4%)

PHY-1001 : ==== DR Iter 22 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 22; 0.216775s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (100.9%)

PHY-1001 : ==== DR Iter 23 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 23; 0.349295s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (107.4%)

PHY-1001 : ==== DR Iter 24 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 24; 0.362256s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (99.2%)

PHY-1001 : ==== DR Iter 25 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 25; 0.741391s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (99.1%)

PHY-1001 : ===== DR Iter 26 =====
PHY-1022 : len = 2.15106e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 26; 0.177025s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (123.6%)

PHY-1001 : ==== DR Iter 27 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 27; 0.177637s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (96.8%)

PHY-1001 : ==== DR Iter 28 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 28; 0.189889s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (98.7%)

PHY-1001 : ==== DR Iter 29 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 29; 0.204544s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (122.2%)

PHY-1001 : ==== DR Iter 30 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 30; 0.342158s wall, 0.359375s user + 0.015625s system = 0.375000s CPU (109.6%)

PHY-1001 : ==== DR Iter 31 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 31; 0.361533s wall, 0.343750s user + 0.015625s system = 0.359375s CPU (99.4%)

PHY-1001 : ==== DR Iter 32 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 32; 0.723749s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (99.3%)

PHY-1001 : ==== DR Iter 33 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 33; 0.764299s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (100.2%)

PHY-1001 : ===== DR Iter 34 =====
PHY-1022 : len = 2.15106e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 34; 0.176809s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (97.2%)

PHY-1001 : ==== DR Iter 35 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 35; 0.180673s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (103.8%)

PHY-1001 : ==== DR Iter 36 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 36; 0.186059s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (100.8%)

PHY-1001 : ==== DR Iter 37 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 37; 0.208620s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (104.9%)

PHY-1001 : ==== DR Iter 38 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 38; 0.355637s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (96.7%)

PHY-1001 : ==== DR Iter 39 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 39; 0.378841s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (103.1%)

PHY-1001 : ==== DR Iter 40 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 40; 0.730747s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (98.4%)

PHY-1001 : ==== DR Iter 41 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 41; 0.782199s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (99.9%)

PHY-1001 : ==== DR Iter 42 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 42; 0.785854s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (101.4%)

PHY-1001 : ===== DR Iter 43 =====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 43; 0.174669s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (116.3%)

PHY-1001 : ==== DR Iter 44 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 44; 0.177571s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (105.6%)

PHY-1001 : ==== DR Iter 45 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 45; 0.202514s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (100.3%)

PHY-1001 : ==== DR Iter 46 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 46; 0.236016s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (105.9%)

PHY-1001 : ==== DR Iter 47 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 47; 0.345877s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (99.4%)

PHY-1001 : ==== DR Iter 48 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 48; 0.363222s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (98.9%)

PHY-1001 : ==== DR Iter 49 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 49; 0.760712s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (100.6%)

PHY-1001 : ==== DR Iter 50 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 50; 0.771263s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (99.3%)

PHY-1001 : ==== DR Iter 51 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 51; 0.788324s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (101.1%)

PHY-1001 : ==== DR Iter 52 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 52; 0.780978s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (100.0%)

PHY-1001 : ===== DR Iter 53 =====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 53; 0.175643s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (124.5%)

PHY-1001 : ==== DR Iter 54 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 54; 0.176104s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (115.3%)

PHY-1001 : ==== DR Iter 55 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 55; 0.182399s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (94.2%)

PHY-1001 : ==== DR Iter 56 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 56; 0.224848s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (104.2%)

PHY-1001 : ==== DR Iter 57 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 57; 0.346764s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (99.1%)

PHY-1001 : ==== DR Iter 58 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 58; 0.376854s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (103.7%)

PHY-1001 : ==== DR Iter 59 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 59; 0.728351s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (98.7%)

PHY-1001 : ==== DR Iter 60 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 60; 0.783598s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (99.7%)

PHY-1001 : ==== DR Iter 61 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 61; 0.780333s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (100.1%)

PHY-1001 : ==== DR Iter 62 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 62; 0.791387s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (98.7%)

PHY-1001 : ==== DR Iter 63 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 63; 0.779141s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (102.3%)

PHY-1001 : ===== DR Iter 64 =====
PHY-1022 : len = 2.15106e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 64; 0.185343s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (101.2%)

PHY-1001 : ==== DR Iter 65 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 65; 0.172661s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (108.6%)

PHY-1001 : ==== DR Iter 66 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 66; 0.184074s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (101.9%)

PHY-1001 : ==== DR Iter 67 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 67; 0.211820s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (95.9%)

PHY-1001 : ==== DR Iter 68 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 68; 0.334149s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (102.9%)

PHY-1001 : ==== DR Iter 69 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 69; 0.365804s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (98.2%)

PHY-1001 : ==== DR Iter 70 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 70; 0.699869s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (100.5%)

PHY-1001 : ==== DR Iter 71 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 71; 0.780538s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (100.1%)

PHY-1001 : ==== DR Iter 72 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 72; 0.787896s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (99.2%)

PHY-1001 : ==== DR Iter 73 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 73; 0.788021s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (101.1%)

PHY-1001 : ==== DR Iter 74 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 74; 0.775008s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (98.8%)

PHY-1001 : ==== DR Iter 75 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 75; 0.770624s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (101.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/21881(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.255   |   0.000   |   0   
RUN-1001 :   Hold   |   0.130   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.685300s wall, 3.656250s user + 0.000000s system = 3.656250s CPU (99.2%)

PHY-1001 : Current memory(MB): used = 1139, reserve = 1131, peak = 1139.
PHY-1001 : End phase 3; 40.231852s wall, 41.578125s user + 0.187500s system = 41.765625s CPU (103.8%)

PHY-1001 : ===== Detail Route Phase 4 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.268532s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (98.9%)

PHY-0007 : Phase: 4; Congestion: {, , , }; Timing: {0.255ns, 0.000ns, 0}
PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.172895s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (99.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.184975s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (92.9%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.216750s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (100.9%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 2.15106e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.180897s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (103.7%)

PHY-1001 : ==== DR Iter 5 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.174810s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (98.3%)

PHY-1001 : ==== DR Iter 6 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 6; 0.188829s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (99.3%)

PHY-1001 : ==== DR Iter 7 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 7; 0.244514s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (115.0%)

PHY-1001 : ==== DR Iter 8 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 8; 0.451196s wall, 0.453125s user + 0.031250s system = 0.484375s CPU (107.4%)

PHY-1001 : ===== DR Iter 9 =====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 9; 0.171187s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (100.4%)

PHY-1001 : ==== DR Iter 10 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 10; 0.173629s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (99.0%)

PHY-1001 : ==== DR Iter 11 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 11; 0.188842s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (107.6%)

PHY-1001 : ==== DR Iter 12 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 12; 0.214017s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (94.9%)

PHY-1001 : ==== DR Iter 13 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 13; 0.330705s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (99.2%)

PHY-1001 : ==== DR Iter 14 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 14; 0.360723s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (99.6%)

PHY-1001 : ===== DR Iter 15 =====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 15; 0.175669s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (124.5%)

PHY-1001 : ==== DR Iter 16 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 16; 0.174740s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (98.4%)

PHY-1001 : ==== DR Iter 17 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 17; 0.187101s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (108.6%)

PHY-1001 : ==== DR Iter 18 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 18; 0.303669s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (108.1%)

PHY-1001 : ==== DR Iter 19 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 19; 0.417754s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (101.0%)

PHY-1001 : ==== DR Iter 20 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 20; 0.448436s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (97.6%)

PHY-1001 : ==== DR Iter 21 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 21; 0.745952s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (100.5%)

PHY-1001 : ===== DR Iter 22 =====
PHY-1022 : len = 2.15106e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 22; 0.180603s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (103.8%)

PHY-1001 : ==== DR Iter 23 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 23; 0.177812s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (96.7%)

PHY-1001 : ==== DR Iter 24 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 24; 0.181548s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (129.1%)

PHY-1001 : ==== DR Iter 25 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 25; 0.216958s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (122.4%)

PHY-1001 : ==== DR Iter 26 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 26; 0.328501s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (99.9%)

PHY-1001 : ==== DR Iter 27 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 27; 0.364395s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (98.6%)

PHY-1001 : ==== DR Iter 28 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 28; 0.994420s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (100.6%)

PHY-1001 : ==== DR Iter 29 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 29; 0.991486s wall, 0.984375s user + 0.000000s system = 0.984375s CPU (99.3%)

PHY-1001 : ===== DR Iter 30 =====
PHY-1022 : len = 2.15106e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 30; 0.183131s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (119.4%)

PHY-1001 : ==== DR Iter 31 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 31; 0.178269s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (96.4%)

PHY-1001 : ==== DR Iter 32 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 32; 0.232169s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (101.0%)

PHY-1001 : ==== DR Iter 33 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 33; 0.235671s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (99.5%)

PHY-1001 : ==== DR Iter 34 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 34; 0.346138s wall, 0.343750s user + 0.031250s system = 0.375000s CPU (108.3%)

PHY-1001 : ==== DR Iter 35 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 35; 0.421453s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (100.1%)

PHY-1001 : ==== DR Iter 36 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 36; 0.758383s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (101.0%)

PHY-1001 : ==== DR Iter 37 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 37; 0.788465s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (99.1%)

PHY-1001 : ==== DR Iter 38 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 38; 0.767105s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (99.8%)

PHY-1001 : ===== DR Iter 39 =====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 39; 0.188367s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (99.5%)

PHY-1001 : ==== DR Iter 40 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 40; 0.176216s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (115.3%)

PHY-1001 : ==== DR Iter 41 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 41; 0.181529s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (103.3%)

PHY-1001 : ==== DR Iter 42 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 42; 0.236984s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (98.9%)

PHY-1001 : ==== DR Iter 43 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 43; 0.393067s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (99.4%)

PHY-1001 : ==== DR Iter 44 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 44; 0.373764s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (100.3%)

PHY-1001 : ==== DR Iter 45 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 45; 0.826955s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (98.3%)

PHY-1001 : ==== DR Iter 46 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 46; 0.884456s wall, 0.890625s user + 0.000000s system = 0.890625s CPU (100.7%)

PHY-1001 : ==== DR Iter 47 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 47; 0.850195s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (99.2%)

PHY-1001 : ==== DR Iter 48 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 48; 0.799716s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.6%)

PHY-1001 : ===== DR Iter 49 =====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 49; 0.169895s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (119.6%)

PHY-1001 : ==== DR Iter 50 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 50; 0.173308s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (99.2%)

PHY-1001 : ==== DR Iter 51 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 51; 0.188021s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (99.7%)

PHY-1001 : ==== DR Iter 52 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 52; 0.236727s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (99.0%)

PHY-1001 : ==== DR Iter 53 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 53; 0.339913s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (101.1%)

PHY-1001 : ==== DR Iter 54 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 54; 0.370672s wall, 0.343750s user + 0.015625s system = 0.359375s CPU (97.0%)

PHY-1001 : ==== DR Iter 55 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 55; 0.726582s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (101.1%)

PHY-1001 : ==== DR Iter 56 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 56; 0.769216s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (99.5%)

PHY-1001 : ==== DR Iter 57 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 57; 0.755136s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (99.3%)

PHY-1001 : ==== DR Iter 58 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 58; 0.785678s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (101.4%)

PHY-1001 : ==== DR Iter 59 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 59; 0.793461s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (98.5%)

PHY-1001 : ===== DR Iter 60 =====
PHY-1022 : len = 2.15106e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 60; 0.174101s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (107.7%)

PHY-1001 : ==== DR Iter 61 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 61; 0.173157s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (99.3%)

PHY-1001 : ==== DR Iter 62 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 62; 0.181197s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (103.5%)

PHY-1001 : ==== DR Iter 63 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 63; 0.207725s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (97.8%)

PHY-1001 : ==== DR Iter 64 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 64; 0.333525s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (98.4%)

PHY-1001 : ==== DR Iter 65 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 65; 0.372075s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (100.8%)

PHY-1001 : ==== DR Iter 66 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 66; 0.758728s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (100.9%)

PHY-1001 : ==== DR Iter 67 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 67; 0.811308s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (100.1%)

PHY-1001 : ==== DR Iter 68 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 68; 0.829776s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (97.9%)

PHY-1001 : ==== DR Iter 69 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 69; 0.857460s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (100.2%)

PHY-1001 : ==== DR Iter 70 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 70; 0.800693s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.5%)

PHY-1001 : ==== DR Iter 71 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 71; 0.773139s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (99.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/21881(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.255   |   0.000   |   0   
RUN-1001 :   Hold   |   0.130   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.713750s wall, 3.718750s user + 0.000000s system = 3.718750s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1141, reserve = 1133, peak = 1141.
PHY-1001 : End phase 4; 33.651755s wall, 33.781250s user + 0.203125s system = 33.984375s CPU (101.0%)

PHY-1003 : Routed, final wirelength = 2.15102e+06
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 1026 feed throughs used by 742 nets
PHY-1001 : Current memory(MB): used = 1244, reserve = 1240, peak = 1244.
PHY-1001 : End export database. 2.940690s wall, 2.921875s user + 0.015625s system = 2.937500s CPU (99.9%)

PHY-1001 : Fixing routing violation through ECO...
RUN-1002 : start command "place -eco"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 9248 instances
RUN-1001 : 4571 mslices, 4570 lslices, 60 pads, 42 brams, 0 dsps
RUN-1001 : There are total 23110 nets
RUN-1001 : 16950 nets have 2 pins
RUN-1001 : 4478 nets have [3 - 5] pins
RUN-1001 : 1042 nets have [6 - 10] pins
RUN-1001 : 387 nets have [11 - 20] pins
RUN-1001 : 240 nets have [21 - 99] pins
RUN-1001 : 13 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |      0      
RUN-1001 :   No   |  No   |  Yes  |      0      
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |      0      
RUN-1001 :   Yes  |  No   |  Yes  |      0      
RUN-1001 :   Yes  |  Yes  |  No   |      0      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |   1   |     1      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 0
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: (1 20 1) is for feedthrough
PHY-3001 : eco cells: (1 25 1) is for feedthrough
PHY-3001 : eco cells: (1 52 3) is for feedthrough
PHY-3001 : eco cells: (1 53 1) is for feedthrough
PHY-3001 : eco cells: (1 54 0) is for feedthrough
PHY-3001 : eco cells: (1 58 1) is for feedthrough
PHY-3001 : eco cells: (2 5 3) is for feedthrough
PHY-3001 : eco cells: (2 12 0) is for feedthrough
PHY-3001 : eco cells: (2 13 1) is for feedthrough
PHY-3001 : eco cells: (2 15 0) is for feedthrough
PHY-3001 : eco cells: (2 20 0) is for feedthrough
PHY-3001 : eco cells: (2 45 3) is for feedthrough
PHY-3001 : eco cells: (2 52 0) is for feedthrough
PHY-3001 : eco cells: (2 53 1) is for feedthrough
PHY-3001 : eco cells: (2 56 0) is for feedthrough
PHY-3001 : eco cells: (2 56 1) is for feedthrough
PHY-3001 : eco cells: (2 65 0) is for feedthrough
PHY-3001 : eco cells: (3 4 1) is for feedthrough
PHY-3001 : eco cells: (3 5 2) is for feedthrough
PHY-3001 : eco cells: (3 21 3) is for feedthrough
PHY-3001 : eco cells: (3 23 2) is for feedthrough
PHY-3001 : eco cells: (3 25 1) is for feedthrough
PHY-3001 : eco cells: (3 26 0) is for feedthrough
PHY-3001 : eco cells: (3 41 0) is for feedthrough
PHY-3001 : eco cells: (3 46 2) is for feedthrough
PHY-3001 : eco cells: (3 54 3) is for feedthrough
PHY-3001 : eco cells: (3 55 2) is for feedthrough
PHY-3001 : eco cells: (3 57 1) is for feedthrough
PHY-3001 : eco cells: (3 57 3) is for feedthrough
PHY-3001 : eco cells: (3 63 3) is for feedthrough
PHY-3001 : eco cells: (3 66 2) is for feedthrough
PHY-3001 : eco cells: (3 67 0) is for feedthrough
PHY-3001 : eco cells: (3 67 1) is for feedthrough
PHY-3001 : eco cells: (3 68 0) is for feedthrough
PHY-3001 : eco cells: (4 9 0) is for feedthrough
PHY-3001 : eco cells: (4 18 1) is for feedthrough
PHY-3001 : eco cells: (4 23 2) is for feedthrough
PHY-3001 : eco cells: (4 25 1) is for feedthrough
PHY-3001 : eco cells: (4 26 3) is for feedthrough
PHY-3001 : eco cells: (4 43 2) is for feedthrough
PHY-3001 : eco cells: (4 65 2) is for feedthrough
PHY-3001 : eco cells: (4 67 2) is for feedthrough
PHY-3001 : eco cells: (4 68 1) is for feedthrough
PHY-3001 : eco cells: (4 70 3) is for feedthrough
PHY-3001 : eco cells: (5 6 0) is for feedthrough
PHY-3001 : eco cells: (5 12 2) is for feedthrough
PHY-3001 : eco cells: (5 13 1) is for feedthrough
PHY-3001 : eco cells: (5 22 3) is for feedthrough
PHY-3001 : eco cells: (5 23 0) is for feedthrough
PHY-3001 : eco cells: (5 25 3) is for feedthrough
PHY-3001 : eco cells: (5 33 1) is for feedthrough
PHY-3001 : eco cells: (5 56 2) is for feedthrough
PHY-3001 : eco cells: (5 66 2) is for feedthrough
PHY-3001 : eco cells: (5 67 0) is for feedthrough
PHY-3001 : eco cells: (5 67 1) is for feedthrough
PHY-3001 : eco cells: (5 68 3) is for feedthrough
PHY-3001 : eco cells: (6 10 1) is for feedthrough
PHY-3001 : eco cells: (6 13 0) is for feedthrough
PHY-3001 : eco cells: (6 15 0) is for feedthrough
PHY-3001 : eco cells: (6 16 2) is for feedthrough
PHY-3001 : eco cells: (6 17 0) is for feedthrough
PHY-3001 : eco cells: (6 21 2) is for feedthrough
PHY-3001 : eco cells: (6 21 3) is for feedthrough
PHY-3001 : eco cells: (6 27 3) is for feedthrough
PHY-3001 : eco cells: (6 33 1) is for feedthrough
PHY-3001 : eco cells: (6 40 2) is for feedthrough
PHY-3001 : eco cells: (6 62 3) is for feedthrough
PHY-3001 : eco cells: (6 64 3) is for feedthrough
PHY-3001 : eco cells: (6 66 0) is for feedthrough
PHY-3001 : eco cells: (6 67 2) is for feedthrough
PHY-3001 : eco cells: (6 69 0) is for feedthrough
PHY-3001 : eco cells: (7 12 1) is for feedthrough
PHY-3001 : eco cells: (7 21 0) is for feedthrough
PHY-3001 : eco cells: (7 21 3) is for feedthrough
PHY-3001 : eco cells: (7 22 3) is for feedthrough
PHY-3001 : eco cells: (7 31 1) is for feedthrough
PHY-3001 : eco cells: (7 44 0) is for feedthrough
PHY-3001 : eco cells: (7 46 0) is for feedthrough
PHY-3001 : eco cells: (7 64 3) is for feedthrough
PHY-3001 : eco cells: (9 14 3) is for feedthrough
PHY-3001 : eco cells: (9 15 0) is for feedthrough
PHY-3001 : eco cells: (9 22 2) is for feedthrough
PHY-3001 : eco cells: (9 24 3) is for feedthrough
PHY-3001 : eco cells: (9 32 3) is for feedthrough
PHY-3001 : eco cells: (9 43 1) is for feedthrough
PHY-3001 : eco cells: (9 44 2) is for feedthrough
PHY-3001 : eco cells: (9 46 3) is for feedthrough
PHY-3001 : eco cells: (9 65 0) is for feedthrough
PHY-3001 : eco cells: (10 19 2) is for feedthrough
PHY-3001 : eco cells: (10 21 2) is for feedthrough
PHY-3001 : eco cells: (10 40 3) is for feedthrough
PHY-3001 : eco cells: (10 44 3) is for feedthrough
PHY-3001 : eco cells: (10 51 0) is for feedthrough
PHY-3001 : eco cells: (11 3 3) is for feedthrough
PHY-3001 : eco cells: (11 13 1) is for feedthrough
PHY-3001 : eco cells: (11 14 3) is for feedthrough
PHY-3001 : eco cells: (11 18 1) is for feedthrough
PHY-3001 : eco cells: (11 19 1) is for feedthrough
PHY-3001 : eco cells: (11 21 2) is for feedthrough
PHY-3001 : eco cells: (11 23 1) is for feedthrough
PHY-3001 : eco cells: (11 23 2) is for feedthrough
PHY-3001 : eco cells: (11 26 0) is for feedthrough
PHY-3001 : eco cells: (11 26 1) is for feedthrough
PHY-3001 : eco cells: (11 35 1) is for feedthrough
PHY-3001 : eco cells: (11 51 0) is for feedthrough
PHY-3001 : eco cells: (11 59 3) is for feedthrough
PHY-3001 : eco cells: (12 9 0) is for feedthrough
PHY-3001 : eco cells: (12 10 0) is for feedthrough
PHY-3001 : eco cells: (12 10 3) is for feedthrough
PHY-3001 : eco cells: (12 12 0) is for feedthrough
PHY-3001 : eco cells: (12 13 1) is for feedthrough
PHY-3001 : eco cells: (12 14 0) is for feedthrough
PHY-3001 : eco cells: (12 14 2) is for feedthrough
PHY-3001 : eco cells: (12 16 2) is for feedthrough
PHY-3001 : eco cells: (12 19 2) is for feedthrough
PHY-3001 : eco cells: (12 20 0) is for feedthrough
PHY-3001 : eco cells: (12 22 0) is for feedthrough
PHY-3001 : eco cells: (12 24 3) is for feedthrough
PHY-3001 : eco cells: (12 28 1) is for feedthrough
PHY-3001 : eco cells: (12 34 3) is for feedthrough
PHY-3001 : eco cells: (13 6 2) is for feedthrough
PHY-3001 : eco cells: (13 14 3) is for feedthrough
PHY-3001 : eco cells: (13 15 2) is for feedthrough
PHY-3001 : eco cells: (13 16 2) is for feedthrough
PHY-3001 : eco cells: (13 16 3) is for feedthrough
PHY-3001 : eco cells: (13 18 2) is for feedthrough
PHY-3001 : eco cells: (13 19 0) is for feedthrough
PHY-3001 : eco cells: (13 20 1) is for feedthrough
PHY-3001 : eco cells: (13 21 0) is for feedthrough
PHY-3001 : eco cells: (13 22 0) is for feedthrough
PHY-3001 : eco cells: (13 22 1) is for feedthrough
PHY-3001 : eco cells: (13 25 1) is for feedthrough
PHY-3001 : eco cells: (13 25 2) is for feedthrough
PHY-3001 : eco cells: (13 26 0) is for feedthrough
PHY-3001 : eco cells: (13 26 3) is for feedthrough
PHY-3001 : eco cells: (13 27 0) is for feedthrough
PHY-3001 : eco cells: (13 27 1) is for feedthrough
PHY-3001 : eco cells: (13 28 3) is for feedthrough
PHY-3001 : eco cells: (13 30 0) is for feedthrough
PHY-3001 : eco cells: (13 33 1) is for feedthrough
PHY-3001 : eco cells: (13 43 0) is for feedthrough
PHY-3001 : eco cells: (14 10 0) is for feedthrough
PHY-3001 : eco cells: (14 11 0) is for feedthrough
PHY-3001 : eco cells: (14 11 1) is for feedthrough
PHY-3001 : eco cells: (14 11 3) is for feedthrough
PHY-3001 : eco cells: (14 12 2) is for feedthrough
PHY-3001 : eco cells: (14 13 2) is for feedthrough
PHY-3001 : eco cells: (14 14 2) is for feedthrough
PHY-3001 : eco cells: (14 16 3) is for feedthrough
PHY-3001 : eco cells: (14 17 2) is for feedthrough
PHY-3001 : eco cells: (14 18 2) is for feedthrough
PHY-3001 : eco cells: (14 18 3) is for feedthrough
PHY-3001 : eco cells: (14 20 0) is for feedthrough
PHY-3001 : eco cells: (14 22 0) is for feedthrough
PHY-3001 : eco cells: (14 22 1) is for feedthrough
PHY-3001 : eco cells: (14 23 3) is for feedthrough
PHY-3001 : eco cells: (14 25 1) is for feedthrough
PHY-3001 : eco cells: (14 26 1) is for feedthrough
PHY-3001 : eco cells: (14 26 2) is for feedthrough
PHY-3001 : eco cells: (14 29 1) is for feedthrough
PHY-3001 : eco cells: (14 29 2) is for feedthrough
PHY-3001 : eco cells: (14 34 3) is for feedthrough
PHY-3001 : eco cells: (14 40 3) is for feedthrough
PHY-3001 : eco cells: (14 45 0) is for feedthrough
PHY-3001 : eco cells: (15 6 2) is for feedthrough
PHY-3001 : eco cells: (15 9 0) is for feedthrough
PHY-3001 : eco cells: (15 17 1) is for feedthrough
PHY-3001 : eco cells: (15 21 0) is for feedthrough
PHY-3001 : eco cells: (15 23 2) is for feedthrough
PHY-3001 : eco cells: (15 24 3) is for feedthrough
PHY-3001 : eco cells: (15 25 0) is for feedthrough
PHY-3001 : eco cells: (15 26 0) is for feedthrough
PHY-3001 : eco cells: (15 45 3) is for feedthrough
PHY-3001 : eco cells: (17 12 1) is for feedthrough
PHY-3001 : eco cells: (17 12 3) is for feedthrough
PHY-3001 : eco cells: (17 20 2) is for feedthrough
PHY-3001 : eco cells: (17 21 1) is for feedthrough
PHY-3001 : eco cells: (17 24 3) is for feedthrough
PHY-3001 : eco cells: (17 25 0) is for feedthrough
PHY-3001 : eco cells: (17 29 0) is for feedthrough
PHY-3001 : eco cells: (17 29 2) is for feedthrough
PHY-3001 : eco cells: (17 41 0) is for feedthrough
PHY-3001 : eco cells: (17 47 3) is for feedthrough
PHY-3001 : eco cells: (17 63 1) is for feedthrough
PHY-3001 : eco cells: (17 69 1) is for feedthrough
PHY-3001 : eco cells: (18 4 2) is for feedthrough
PHY-3001 : eco cells: (18 6 1) is for feedthrough
PHY-3001 : eco cells: (18 6 2) is for feedthrough
PHY-3001 : eco cells: (18 7 0) is for feedthrough
PHY-3001 : eco cells: (18 7 1) is for feedthrough
PHY-3001 : eco cells: (18 8 1) is for feedthrough
PHY-3001 : eco cells: (18 12 1) is for feedthrough
PHY-3001 : eco cells: (18 13 1) is for feedthrough
PHY-3001 : eco cells: (18 13 2) is for feedthrough
PHY-3001 : eco cells: (18 13 3) is for feedthrough
PHY-3001 : eco cells: (18 17 3) is for feedthrough
PHY-3001 : eco cells: (18 18 0) is for feedthrough
PHY-3001 : eco cells: (18 18 3) is for feedthrough
PHY-3001 : eco cells: (18 19 1) is for feedthrough
PHY-3001 : eco cells: (18 21 0) is for feedthrough
PHY-3001 : eco cells: (18 21 3) is for feedthrough
PHY-3001 : eco cells: (18 23 2) is for feedthrough
PHY-3001 : eco cells: (18 24 1) is for feedthrough
PHY-3001 : eco cells: (18 26 3) is for feedthrough
PHY-3001 : eco cells: (18 28 0) is for feedthrough
PHY-3001 : eco cells: (18 28 1) is for feedthrough
PHY-3001 : eco cells: (18 29 0) is for feedthrough
PHY-3001 : eco cells: (18 29 1) is for feedthrough
PHY-3001 : eco cells: (18 30 0) is for feedthrough
PHY-3001 : eco cells: (18 30 1) is for feedthrough
PHY-3001 : eco cells: (18 33 0) is for feedthrough
PHY-3001 : eco cells: (18 50 0) is for feedthrough
PHY-3001 : eco cells: (18 60 3) is for feedthrough
PHY-3001 : eco cells: (19 2 0) is for feedthrough
PHY-3001 : eco cells: (19 3 1) is for feedthrough
PHY-3001 : eco cells: (19 5 0) is for feedthrough
PHY-3001 : eco cells: (19 6 0) is for feedthrough
PHY-3001 : eco cells: (19 7 1) is for feedthrough
PHY-3001 : eco cells: (19 8 2) is for feedthrough
PHY-3001 : eco cells: (19 9 1) is for feedthrough
PHY-3001 : eco cells: (19 9 2) is for feedthrough
PHY-3001 : eco cells: (19 10 0) is for feedthrough
PHY-3001 : eco cells: (19 10 1) is for feedthrough
PHY-3001 : eco cells: (19 11 1) is for feedthrough
PHY-3001 : eco cells: (19 12 1) is for feedthrough
PHY-3001 : eco cells: (19 13 1) is for feedthrough
PHY-3001 : eco cells: (19 15 1) is for feedthrough
PHY-3001 : eco cells: (19 19 3) is for feedthrough
PHY-3001 : eco cells: (19 21 1) is for feedthrough
PHY-3001 : eco cells: (19 25 1) is for feedthrough
PHY-3001 : eco cells: (19 26 0) is for feedthrough
PHY-3001 : eco cells: (19 26 3) is for feedthrough
PHY-3001 : eco cells: (19 28 0) is for feedthrough
PHY-3001 : eco cells: (19 28 1) is for feedthrough
PHY-3001 : eco cells: (19 29 0) is for feedthrough
PHY-3001 : eco cells: (19 32 3) is for feedthrough
PHY-3001 : eco cells: (19 37 2) is for feedthrough
PHY-3001 : eco cells: (19 44 3) is for feedthrough
PHY-3001 : eco cells: (19 50 1) is for feedthrough
PHY-3001 : eco cells: (19 57 3) is for feedthrough
PHY-3001 : eco cells: (19 63 3) is for feedthrough
PHY-3001 : eco cells: (19 65 0) is for feedthrough
PHY-3001 : eco cells: (20 1 0) is for feedthrough
PHY-3001 : eco cells: (20 2 0) is for feedthrough
PHY-3001 : eco cells: (20 2 1) is for feedthrough
PHY-3001 : eco cells: (20 2 2) is for feedthrough
PHY-3001 : eco cells: (20 4 1) is for feedthrough
PHY-3001 : eco cells: (20 5 1) is for feedthrough
PHY-3001 : eco cells: (20 8 1) is for feedthrough
PHY-3001 : eco cells: (20 8 2) is for feedthrough
PHY-3001 : eco cells: (20 9 0) is for feedthrough
PHY-3001 : eco cells: (20 9 2) is for feedthrough
PHY-3001 : eco cells: (20 9 3) is for feedthrough
PHY-3001 : eco cells: (20 10 3) is for feedthrough
PHY-3001 : eco cells: (20 11 2) is for feedthrough
PHY-3001 : eco cells: (20 13 1) is for feedthrough
PHY-3001 : eco cells: (20 14 0) is for feedthrough
PHY-3001 : eco cells: (20 15 3) is for feedthrough
PHY-3001 : eco cells: (20 16 2) is for feedthrough
PHY-3001 : eco cells: (20 18 2) is for feedthrough
PHY-3001 : eco cells: (20 23 0) is for feedthrough
PHY-3001 : eco cells: (20 25 1) is for feedthrough
PHY-3001 : eco cells: (20 30 1) is for feedthrough
PHY-3001 : eco cells: (20 33 2) is for feedthrough
PHY-3001 : eco cells: (20 39 2) is for feedthrough
PHY-3001 : eco cells: (20 54 2) is for feedthrough
PHY-3001 : eco cells: (20 66 0) is for feedthrough
PHY-3001 : eco cells: (21 1 1) is for feedthrough
PHY-3001 : eco cells: (21 3 2) is for feedthrough
PHY-3001 : eco cells: (21 3 3) is for feedthrough
PHY-3001 : eco cells: (21 4 3) is for feedthrough
PHY-3001 : eco cells: (21 8 3) is for feedthrough
PHY-3001 : eco cells: (21 10 0) is for feedthrough
PHY-3001 : eco cells: (21 11 0) is for feedthrough
PHY-3001 : eco cells: (21 11 2) is for feedthrough
PHY-3001 : eco cells: (21 12 1) is for feedthrough
PHY-3001 : eco cells: (21 13 3) is for feedthrough
PHY-3001 : eco cells: (21 14 0) is for feedthrough
PHY-3001 : eco cells: (21 14 1) is for feedthrough
PHY-3001 : eco cells: (21 14 3) is for feedthrough
PHY-3001 : eco cells: (21 15 1) is for feedthrough
PHY-3001 : eco cells: (21 16 1) is for feedthrough
PHY-3001 : eco cells: (21 17 0) is for feedthrough
PHY-3001 : eco cells: (21 22 1) is for feedthrough
PHY-3001 : eco cells: (21 22 2) is for feedthrough
PHY-3001 : eco cells: (21 24 0) is for feedthrough
PHY-3001 : eco cells: (21 24 1) is for feedthrough
PHY-3001 : eco cells: (21 24 2) is for feedthrough
PHY-3001 : eco cells: (21 25 0) is for feedthrough
PHY-3001 : eco cells: (21 26 0) is for feedthrough
PHY-3001 : eco cells: (21 26 1) is for feedthrough
PHY-3001 : eco cells: (21 29 2) is for feedthrough
PHY-3001 : eco cells: (21 36 0) is for feedthrough
PHY-3001 : eco cells: (21 41 3) is for feedthrough
PHY-3001 : eco cells: (21 42 2) is for feedthrough
PHY-3001 : eco cells: (21 49 0) is for feedthrough
PHY-3001 : eco cells: (21 52 3) is for feedthrough
PHY-3001 : eco cells: (21 53 2) is for feedthrough
PHY-3001 : eco cells: (21 55 3) is for feedthrough
PHY-3001 : eco cells: (21 61 3) is for feedthrough
PHY-3001 : eco cells: (21 64 2) is for feedthrough
PHY-3001 : eco cells: (21 67 0) is for feedthrough
PHY-3001 : eco cells: (22 1 1) is for feedthrough
PHY-3001 : eco cells: (22 1 2) is for feedthrough
PHY-3001 : eco cells: (22 2 1) is for feedthrough
PHY-3001 : eco cells: (22 4 2) is for feedthrough
PHY-3001 : eco cells: (22 5 0) is for feedthrough
PHY-3001 : eco cells: (22 5 1) is for feedthrough
PHY-3001 : eco cells: (22 6 1) is for feedthrough
PHY-3001 : eco cells: (22 6 3) is for feedthrough
PHY-3001 : eco cells: (22 7 0) is for feedthrough
PHY-3001 : eco cells: (22 7 2) is for feedthrough
PHY-3001 : eco cells: (22 7 3) is for feedthrough
PHY-3001 : eco cells: (22 8 0) is for feedthrough
PHY-3001 : eco cells: (22 8 1) is for feedthrough
PHY-3001 : eco cells: (22 9 1) is for feedthrough
PHY-3001 : eco cells: (22 9 3) is for feedthrough
PHY-3001 : eco cells: (22 10 0) is for feedthrough
PHY-3001 : eco cells: (22 10 1) is for feedthrough
PHY-3001 : eco cells: (22 13 0) is for feedthrough
PHY-3001 : eco cells: (22 13 1) is for feedthrough
PHY-3001 : eco cells: (22 13 2) is for feedthrough
PHY-3001 : eco cells: (22 14 0) is for feedthrough
PHY-3001 : eco cells: (22 14 1) is for feedthrough
PHY-3001 : eco cells: (22 14 3) is for feedthrough
PHY-3001 : eco cells: (22 17 1) is for feedthrough
PHY-3001 : eco cells: (22 17 3) is for feedthrough
PHY-3001 : eco cells: (22 18 2) is for feedthrough
PHY-3001 : eco cells: (22 20 1) is for feedthrough
PHY-3001 : eco cells: (22 23 1) is for feedthrough
PHY-3001 : eco cells: (22 25 0) is for feedthrough
PHY-3001 : eco cells: (22 26 1) is for feedthrough
PHY-3001 : eco cells: (22 29 1) is for feedthrough
PHY-3001 : eco cells: (22 43 2) is for feedthrough
PHY-3001 : eco cells: (22 46 3) is for feedthrough
PHY-3001 : eco cells: (22 47 0) is for feedthrough
PHY-3001 : eco cells: (22 52 1) is for feedthrough
PHY-3001 : eco cells: (22 56 3) is for feedthrough
PHY-3001 : eco cells: (22 65 2) is for feedthrough
PHY-3001 : eco cells: (22 66 2) is for feedthrough
PHY-3001 : eco cells: (23 2 0) is for feedthrough
PHY-3001 : eco cells: (23 4 1) is for feedthrough
PHY-3001 : eco cells: (23 6 1) is for feedthrough
PHY-3001 : eco cells: (23 7 0) is for feedthrough
PHY-3001 : eco cells: (23 7 1) is for feedthrough
PHY-3001 : eco cells: (23 7 2) is for feedthrough
PHY-3001 : eco cells: (23 8 0) is for feedthrough
PHY-3001 : eco cells: (23 9 2) is for feedthrough
PHY-3001 : eco cells: (23 10 1) is for feedthrough
PHY-3001 : eco cells: (23 11 0) is for feedthrough
PHY-3001 : eco cells: (23 12 2) is for feedthrough
PHY-3001 : eco cells: (23 13 1) is for feedthrough
PHY-3001 : eco cells: (23 14 1) is for feedthrough
PHY-3001 : eco cells: (23 15 0) is for feedthrough
PHY-3001 : eco cells: (23 17 3) is for feedthrough
PHY-3001 : eco cells: (23 18 2) is for feedthrough
PHY-3001 : eco cells: (23 20 2) is for feedthrough
PHY-3001 : eco cells: (23 20 3) is for feedthrough
PHY-3001 : eco cells: (23 23 2) is for feedthrough
PHY-3001 : eco cells: (23 24 3) is for feedthrough
PHY-3001 : eco cells: (23 25 0) is for feedthrough
PHY-3001 : eco cells: (23 25 3) is for feedthrough
PHY-3001 : eco cells: (23 26 3) is for feedthrough
PHY-3001 : eco cells: (23 27 0) is for feedthrough
PHY-3001 : eco cells: (23 30 3) is for feedthrough
PHY-3001 : eco cells: (23 32 1) is for feedthrough
PHY-3001 : eco cells: (23 41 3) is for feedthrough
PHY-3001 : eco cells: (23 65 3) is for feedthrough
PHY-3001 : eco cells: (25 2 0) is for feedthrough
PHY-3001 : eco cells: (25 3 0) is for feedthrough
PHY-3001 : eco cells: (25 4 1) is for feedthrough
PHY-3001 : eco cells: (25 5 0) is for feedthrough
PHY-3001 : eco cells: (25 6 0) is for feedthrough
PHY-3001 : eco cells: (25 6 1) is for feedthrough
PHY-3001 : eco cells: (25 8 2) is for feedthrough
PHY-3001 : eco cells: (25 8 3) is for feedthrough
PHY-3001 : eco cells: (25 9 1) is for feedthrough
PHY-3001 : eco cells: (25 10 2) is for feedthrough
PHY-3001 : eco cells: (25 12 0) is for feedthrough
PHY-3001 : eco cells: (25 12 1) is for feedthrough
PHY-3001 : eco cells: (25 12 3) is for feedthrough
PHY-3001 : eco cells: (25 13 0) is for feedthrough
PHY-3001 : eco cells: (25 13 1) is for feedthrough
PHY-3001 : eco cells: (25 15 1) is for feedthrough
PHY-3001 : eco cells: (25 16 0) is for feedthrough
PHY-3001 : eco cells: (25 16 1) is for feedthrough
PHY-3001 : eco cells: (25 17 1) is for feedthrough
PHY-3001 : eco cells: (25 17 2) is for feedthrough
PHY-3001 : eco cells: (25 18 0) is for feedthrough
PHY-3001 : eco cells: (25 24 2) is for feedthrough
PHY-3001 : eco cells: (25 26 1) is for feedthrough
PHY-3001 : eco cells: (25 34 1) is for feedthrough
PHY-3001 : eco cells: (25 62 0) is for feedthrough
PHY-3001 : eco cells: (25 63 2) is for feedthrough
PHY-3001 : eco cells: (25 66 0) is for feedthrough
PHY-3001 : eco cells: (25 68 2) is for feedthrough
PHY-3001 : eco cells: (26 6 0) is for feedthrough
PHY-3001 : eco cells: (26 6 1) is for feedthrough
PHY-3001 : eco cells: (26 6 3) is for feedthrough
PHY-3001 : eco cells: (26 7 0) is for feedthrough
PHY-3001 : eco cells: (26 7 1) is for feedthrough
PHY-3001 : eco cells: (26 7 2) is for feedthrough
PHY-3001 : eco cells: (26 8 0) is for feedthrough
PHY-3001 : eco cells: (26 8 2) is for feedthrough
PHY-3001 : eco cells: (26 8 3) is for feedthrough
PHY-3001 : eco cells: (26 9 0) is for feedthrough
PHY-3001 : eco cells: (26 9 1) is for feedthrough
PHY-3001 : eco cells: (26 9 2) is for feedthrough
PHY-3001 : eco cells: (26 9 3) is for feedthrough
PHY-3001 : eco cells: (26 10 0) is for feedthrough
PHY-3001 : eco cells: (26 10 2) is for feedthrough
PHY-3001 : eco cells: (26 11 0) is for feedthrough
PHY-3001 : eco cells: (26 11 2) is for feedthrough
PHY-3001 : eco cells: (26 12 3) is for feedthrough
PHY-3001 : eco cells: (26 14 1) is for feedthrough
PHY-3001 : eco cells: (26 15 0) is for feedthrough
PHY-3001 : eco cells: (26 15 1) is for feedthrough
PHY-3001 : eco cells: (26 16 0) is for feedthrough
PHY-3001 : eco cells: (26 16 1) is for feedthrough
PHY-3001 : eco cells: (26 17 1) is for feedthrough
PHY-3001 : eco cells: (26 18 1) is for feedthrough
PHY-3001 : eco cells: (26 18 3) is for feedthrough
PHY-3001 : eco cells: (26 19 0) is for feedthrough
PHY-3001 : eco cells: (26 19 3) is for feedthrough
PHY-3001 : eco cells: (26 25 0) is for feedthrough
PHY-3001 : eco cells: (26 27 0) is for feedthrough
PHY-3001 : eco cells: (26 33 3) is for feedthrough
PHY-3001 : eco cells: (26 36 1) is for feedthrough
PHY-3001 : eco cells: (26 44 3) is for feedthrough
PHY-3001 : eco cells: (26 45 2) is for feedthrough
PHY-3001 : eco cells: (26 46 2) is for feedthrough
PHY-3001 : eco cells: (26 54 0) is for feedthrough
PHY-3001 : eco cells: (26 65 3) is for feedthrough
PHY-3001 : eco cells: (26 68 1) is for feedthrough
PHY-3001 : eco cells: (27 1 0) is for feedthrough
PHY-3001 : eco cells: (27 2 1) is for feedthrough
PHY-3001 : eco cells: (27 3 1) is for feedthrough
PHY-3001 : eco cells: (27 3 3) is for feedthrough
PHY-3001 : eco cells: (27 4 0) is for feedthrough
PHY-3001 : eco cells: (27 4 2) is for feedthrough
PHY-3001 : eco cells: (27 4 3) is for feedthrough
PHY-3001 : eco cells: (27 5 1) is for feedthrough
PHY-3001 : eco cells: (27 5 2) is for feedthrough
PHY-3001 : eco cells: (27 6 2) is for feedthrough
PHY-3001 : eco cells: (27 8 1) is for feedthrough
PHY-3001 : eco cells: (27 10 0) is for feedthrough
PHY-3001 : eco cells: (27 10 1) is for feedthrough
PHY-3001 : eco cells: (27 11 0) is for feedthrough
PHY-3001 : eco cells: (27 12 0) is for feedthrough
PHY-3001 : eco cells: (27 12 2) is for feedthrough
PHY-3001 : eco cells: (27 12 3) is for feedthrough
PHY-3001 : eco cells: (27 13 1) is for feedthrough
PHY-3001 : eco cells: (27 14 0) is for feedthrough
PHY-3001 : eco cells: (27 16 0) is for feedthrough
PHY-3001 : eco cells: (27 16 1) is for feedthrough
PHY-3001 : eco cells: (27 17 0) is for feedthrough
PHY-3001 : eco cells: (27 17 3) is for feedthrough
PHY-3001 : eco cells: (27 18 1) is for feedthrough
PHY-3001 : eco cells: (27 19 1) is for feedthrough
PHY-3001 : eco cells: (27 19 2) is for feedthrough
PHY-3001 : eco cells: (27 20 2) is for feedthrough
PHY-3001 : eco cells: (27 23 2) is for feedthrough
PHY-3001 : eco cells: (27 25 0) is for feedthrough
PHY-3001 : eco cells: (27 28 0) is for feedthrough
PHY-3001 : eco cells: (27 29 1) is for feedthrough
PHY-3001 : eco cells: (27 31 1) is for feedthrough
PHY-3001 : eco cells: (27 35 2) is for feedthrough
PHY-3001 : eco cells: (27 43 0) is for feedthrough
PHY-3001 : eco cells: (27 45 0) is for feedthrough
PHY-3001 : eco cells: (27 45 1) is for feedthrough
PHY-3001 : eco cells: (27 65 2) is for feedthrough
PHY-3001 : eco cells: (28 2 0) is for feedthrough
PHY-3001 : eco cells: (28 2 3) is for feedthrough
PHY-3001 : eco cells: (28 4 0) is for feedthrough
PHY-3001 : eco cells: (28 4 1) is for feedthrough
PHY-3001 : eco cells: (28 4 2) is for feedthrough
PHY-3001 : eco cells: (28 5 0) is for feedthrough
PHY-3001 : eco cells: (28 5 1) is for feedthrough
PHY-3001 : eco cells: (28 5 2) is for feedthrough
PHY-3001 : eco cells: (28 6 1) is for feedthrough
PHY-3001 : eco cells: (28 7 0) is for feedthrough
PHY-3001 : eco cells: (28 7 1) is for feedthrough
PHY-3001 : eco cells: (28 7 2) is for feedthrough
PHY-3001 : eco cells: (28 8 1) is for feedthrough
PHY-3001 : eco cells: (28 8 3) is for feedthrough
PHY-3001 : eco cells: (28 9 1) is for feedthrough
PHY-3001 : eco cells: (28 10 0) is for feedthrough
PHY-3001 : eco cells: (28 10 1) is for feedthrough
PHY-3001 : eco cells: (28 10 2) is for feedthrough
PHY-3001 : eco cells: (28 11 0) is for feedthrough
PHY-3001 : eco cells: (28 13 0) is for feedthrough
PHY-3001 : eco cells: (28 13 1) is for feedthrough
PHY-3001 : eco cells: (28 13 2) is for feedthrough
PHY-3001 : eco cells: (28 13 3) is for feedthrough
PHY-3001 : eco cells: (28 14 0) is for feedthrough
PHY-3001 : eco cells: (28 14 1) is for feedthrough
PHY-3001 : eco cells: (28 15 0) is for feedthrough
PHY-3001 : eco cells: (28 17 3) is for feedthrough
PHY-3001 : eco cells: (28 18 3) is for feedthrough
PHY-3001 : eco cells: (28 19 1) is for feedthrough
PHY-3001 : eco cells: (28 20 0) is for feedthrough
PHY-3001 : eco cells: (28 20 3) is for feedthrough
PHY-3001 : eco cells: (28 21 1) is for feedthrough
PHY-3001 : eco cells: (28 21 2) is for feedthrough
PHY-3001 : eco cells: (28 22 0) is for feedthrough
PHY-3001 : eco cells: (28 22 3) is for feedthrough
PHY-3001 : eco cells: (28 23 1) is for feedthrough
PHY-3001 : eco cells: (28 24 0) is for feedthrough
PHY-3001 : eco cells: (28 25 0) is for feedthrough
PHY-3001 : eco cells: (28 34 3) is for feedthrough
PHY-3001 : eco cells: (28 35 1) is for feedthrough
PHY-3001 : eco cells: (28 44 3) is for feedthrough
PHY-3001 : eco cells: (28 45 3) is for feedthrough
PHY-3001 : eco cells: (28 52 0) is for feedthrough
PHY-3001 : eco cells: (28 57 0) is for feedthrough
PHY-3001 : eco cells: (28 64 2) is for feedthrough
PHY-3001 : eco cells: (28 64 3) is for feedthrough
PHY-3001 : eco cells: (29 2 0) is for feedthrough
PHY-3001 : eco cells: (29 3 0) is for feedthrough
PHY-3001 : eco cells: (29 3 3) is for feedthrough
PHY-3001 : eco cells: (29 4 1) is for feedthrough
PHY-3001 : eco cells: (29 5 0) is for feedthrough
PHY-3001 : eco cells: (29 6 2) is for feedthrough
PHY-3001 : eco cells: (29 7 1) is for feedthrough
PHY-3001 : eco cells: (29 7 2) is for feedthrough
PHY-3001 : eco cells: (29 8 0) is for feedthrough
PHY-3001 : eco cells: (29 8 3) is for feedthrough
PHY-3001 : eco cells: (29 9 1) is for feedthrough
PHY-3001 : eco cells: (29 9 2) is for feedthrough
PHY-3001 : eco cells: (29 10 2) is for feedthrough
PHY-3001 : eco cells: (29 11 1) is for feedthrough
PHY-3001 : eco cells: (29 13 1) is for feedthrough
PHY-3001 : eco cells: (29 14 1) is for feedthrough
PHY-3001 : eco cells: (29 15 3) is for feedthrough
PHY-3001 : eco cells: (29 16 0) is for feedthrough
PHY-3001 : eco cells: (29 17 1) is for feedthrough
PHY-3001 : eco cells: (29 17 2) is for feedthrough
PHY-3001 : eco cells: (29 18 0) is for feedthrough
PHY-3001 : eco cells: (29 18 3) is for feedthrough
PHY-3001 : eco cells: (29 22 3) is for feedthrough
PHY-3001 : eco cells: (29 23 3) is for feedthrough
PHY-3001 : eco cells: (29 24 0) is for feedthrough
PHY-3001 : eco cells: (29 24 1) is for feedthrough
PHY-3001 : eco cells: (29 25 1) is for feedthrough
PHY-3001 : eco cells: (29 26 3) is for feedthrough
PHY-3001 : eco cells: (29 28 1) is for feedthrough
PHY-3001 : eco cells: (29 33 2) is for feedthrough
PHY-3001 : eco cells: (29 34 0) is for feedthrough
PHY-3001 : eco cells: (29 35 2) is for feedthrough
PHY-3001 : eco cells: (29 36 0) is for feedthrough
PHY-3001 : eco cells: (29 36 2) is for feedthrough
PHY-3001 : eco cells: (29 37 1) is for feedthrough
PHY-3001 : eco cells: (29 44 0) is for feedthrough
PHY-3001 : eco cells: (29 44 3) is for feedthrough
PHY-3001 : eco cells: (29 46 1) is for feedthrough
PHY-3001 : eco cells: (29 56 0) is for feedthrough
PHY-3001 : eco cells: (29 67 0) is for feedthrough
PHY-3001 : eco cells: (30 3 0) is for feedthrough
PHY-3001 : eco cells: (30 3 1) is for feedthrough
PHY-3001 : eco cells: (30 4 0) is for feedthrough
PHY-3001 : eco cells: (30 5 2) is for feedthrough
PHY-3001 : eco cells: (30 6 0) is for feedthrough
PHY-3001 : eco cells: (30 6 2) is for feedthrough
PHY-3001 : eco cells: (30 7 1) is for feedthrough
PHY-3001 : eco cells: (30 8 1) is for feedthrough
PHY-3001 : eco cells: (30 8 3) is for feedthrough
PHY-3001 : eco cells: (30 9 0) is for feedthrough
PHY-3001 : eco cells: (30 9 1) is for feedthrough
PHY-3001 : eco cells: (30 9 2) is for feedthrough
PHY-3001 : eco cells: (30 10 0) is for feedthrough
PHY-3001 : eco cells: (30 11 2) is for feedthrough
PHY-3001 : eco cells: (30 12 1) is for feedthrough
PHY-3001 : eco cells: (30 13 0) is for feedthrough
PHY-3001 : eco cells: (30 13 1) is for feedthrough
PHY-3001 : eco cells: (30 17 3) is for feedthrough
PHY-3001 : eco cells: (30 18 0) is for feedthrough
PHY-3001 : eco cells: (30 18 2) is for feedthrough
PHY-3001 : eco cells: (30 18 3) is for feedthrough
PHY-3001 : eco cells: (30 20 3) is for feedthrough
PHY-3001 : eco cells: (30 21 1) is for feedthrough
PHY-3001 : eco cells: (30 22 2) is for feedthrough
PHY-3001 : eco cells: (30 24 1) is for feedthrough
PHY-3001 : eco cells: (30 25 3) is for feedthrough
PHY-3001 : eco cells: (30 27 3) is for feedthrough
PHY-3001 : eco cells: (30 57 1) is for feedthrough
PHY-3001 : eco cells: (30 60 2) is for feedthrough
PHY-3001 : eco cells: (30 63 0) is for feedthrough
PHY-3001 : eco cells: (30 64 3) is for feedthrough
PHY-3001 : eco cells: (30 66 0) is for feedthrough
PHY-3001 : eco cells: (30 66 1) is for feedthrough
PHY-3001 : eco cells: (31 2 3) is for feedthrough
PHY-3001 : eco cells: (31 3 0) is for feedthrough
PHY-3001 : eco cells: (31 3 1) is for feedthrough
PHY-3001 : eco cells: (31 4 0) is for feedthrough
PHY-3001 : eco cells: (31 5 0) is for feedthrough
PHY-3001 : eco cells: (31 6 2) is for feedthrough
PHY-3001 : eco cells: (31 8 0) is for feedthrough
PHY-3001 : eco cells: (31 8 1) is for feedthrough
PHY-3001 : eco cells: (31 8 2) is for feedthrough
PHY-3001 : eco cells: (31 9 0) is for feedthrough
PHY-3001 : eco cells: (31 9 1) is for feedthrough
PHY-3001 : eco cells: (31 10 0) is for feedthrough
PHY-3001 : eco cells: (31 10 1) is for feedthrough
PHY-3001 : eco cells: (31 10 3) is for feedthrough
PHY-3001 : eco cells: (31 11 0) is for feedthrough
PHY-3001 : eco cells: (31 11 2) is for feedthrough
PHY-3001 : eco cells: (31 12 0) is for feedthrough
PHY-3001 : eco cells: (31 13 2) is for feedthrough
PHY-3001 : eco cells: (31 15 3) is for feedthrough
PHY-3001 : eco cells: (31 22 2) is for feedthrough
PHY-3001 : eco cells: (31 58 3) is for feedthrough
PHY-3001 : eco cells: (33 3 2) is for feedthrough
PHY-3001 : eco cells: (33 4 3) is for feedthrough
PHY-3001 : eco cells: (33 5 0) is for feedthrough
PHY-3001 : eco cells: (33 7 0) is for feedthrough
PHY-3001 : eco cells: (33 7 1) is for feedthrough
PHY-3001 : eco cells: (33 7 2) is for feedthrough
PHY-3001 : eco cells: (33 7 3) is for feedthrough
PHY-3001 : eco cells: (33 8 0) is for feedthrough
PHY-3001 : eco cells: (33 11 1) is for feedthrough
PHY-3001 : eco cells: (33 12 0) is for feedthrough
PHY-3001 : eco cells: (33 13 1) is for feedthrough
PHY-3001 : eco cells: (33 13 2) is for feedthrough
PHY-3001 : eco cells: (33 14 0) is for feedthrough
PHY-3001 : eco cells: (33 15 0) is for feedthrough
PHY-3001 : eco cells: (33 15 1) is for feedthrough
PHY-3001 : eco cells: (33 15 3) is for feedthrough
PHY-3001 : eco cells: (33 16 0) is for feedthrough
PHY-3001 : eco cells: (33 16 1) is for feedthrough
PHY-3001 : eco cells: (33 17 0) is for feedthrough
PHY-3001 : eco cells: (33 17 1) is for feedthrough
PHY-3001 : eco cells: (33 17 3) is for feedthrough
PHY-3001 : eco cells: (33 18 0) is for feedthrough
PHY-3001 : eco cells: (33 19 0) is for feedthrough
PHY-3001 : eco cells: (33 24 3) is for feedthrough
PHY-3001 : eco cells: (33 26 0) is for feedthrough
PHY-3001 : eco cells: (33 59 2) is for feedthrough
PHY-3001 : eco cells: (33 65 2) is for feedthrough
PHY-3001 : eco cells: (34 1 1) is for feedthrough
PHY-3001 : eco cells: (34 4 1) is for feedthrough
PHY-3001 : eco cells: (34 4 2) is for feedthrough
PHY-3001 : eco cells: (34 5 0) is for feedthrough
PHY-3001 : eco cells: (34 6 0) is for feedthrough
PHY-3001 : eco cells: (34 6 1) is for feedthrough
PHY-3001 : eco cells: (34 6 3) is for feedthrough
PHY-3001 : eco cells: (34 7 0) is for feedthrough
PHY-3001 : eco cells: (34 7 1) is for feedthrough
PHY-3001 : eco cells: (34 7 3) is for feedthrough
PHY-3001 : eco cells: (34 8 0) is for feedthrough
PHY-3001 : eco cells: (34 8 1) is for feedthrough
PHY-3001 : eco cells: (34 9 1) is for feedthrough
PHY-3001 : eco cells: (34 11 0) is for feedthrough
PHY-3001 : eco cells: (34 11 1) is for feedthrough
PHY-3001 : eco cells: (34 12 0) is for feedthrough
PHY-3001 : eco cells: (34 12 1) is for feedthrough
PHY-3001 : eco cells: (34 13 0) is for feedthrough
PHY-3001 : eco cells: (34 14 0) is for feedthrough
PHY-3001 : eco cells: (34 14 1) is for feedthrough
PHY-3001 : eco cells: (34 14 2) is for feedthrough
PHY-3001 : eco cells: (34 15 1) is for feedthrough
PHY-3001 : eco cells: (34 15 2) is for feedthrough
PHY-3001 : eco cells: (34 16 1) is for feedthrough
PHY-3001 : eco cells: (34 17 0) is for feedthrough
PHY-3001 : eco cells: (34 17 1) is for feedthrough
PHY-3001 : eco cells: (34 18 1) is for feedthrough
PHY-3001 : eco cells: (34 18 2) is for feedthrough
PHY-3001 : eco cells: (34 19 0) is for feedthrough
PHY-3001 : eco cells: (34 19 1) is for feedthrough
PHY-3001 : eco cells: (34 21 0) is for feedthrough
PHY-3001 : eco cells: (34 27 0) is for feedthrough
PHY-3001 : eco cells: (34 45 1) is for feedthrough
PHY-3001 : eco cells: (34 48 0) is for feedthrough
PHY-3001 : eco cells: (34 48 1) is for feedthrough
PHY-3001 : eco cells: (34 57 1) is for feedthrough
PHY-3001 : eco cells: (34 58 3) is for feedthrough
PHY-3001 : eco cells: (34 67 1) is for feedthrough
PHY-3001 : eco cells: (35 1 0) is for feedthrough
PHY-3001 : eco cells: (35 2 0) is for feedthrough
PHY-3001 : eco cells: (35 2 1) is for feedthrough
PHY-3001 : eco cells: (35 2 2) is for feedthrough
PHY-3001 : eco cells: (35 3 0) is for feedthrough
PHY-3001 : eco cells: (35 3 1) is for feedthrough
PHY-3001 : eco cells: (35 5 3) is for feedthrough
PHY-3001 : eco cells: (35 6 0) is for feedthrough
PHY-3001 : eco cells: (35 6 1) is for feedthrough
PHY-3001 : eco cells: (35 7 0) is for feedthrough
PHY-3001 : eco cells: (35 7 1) is for feedthrough
PHY-3001 : eco cells: (35 7 3) is for feedthrough
PHY-3001 : eco cells: (35 8 0) is for feedthrough
PHY-3001 : eco cells: (35 8 1) is for feedthrough
PHY-3001 : eco cells: (35 9 1) is for feedthrough
PHY-3001 : eco cells: (35 11 0) is for feedthrough
PHY-3001 : eco cells: (35 11 2) is for feedthrough
PHY-3001 : eco cells: (35 12 1) is for feedthrough
PHY-3001 : eco cells: (35 13 0) is for feedthrough
PHY-3001 : eco cells: (35 13 1) is for feedthrough
PHY-3001 : eco cells: (35 13 2) is for feedthrough
PHY-3001 : eco cells: (35 14 0) is for feedthrough
PHY-3001 : eco cells: (35 14 1) is for feedthrough
PHY-3001 : eco cells: (35 16 0) is for feedthrough
PHY-3001 : eco cells: (35 17 0) is for feedthrough
PHY-3001 : eco cells: (35 17 3) is for feedthrough
PHY-3001 : eco cells: (35 18 0) is for feedthrough
PHY-3001 : eco cells: (35 18 3) is for feedthrough
PHY-3001 : eco cells: (35 19 1) is for feedthrough
PHY-3001 : eco cells: (35 19 2) is for feedthrough
PHY-3001 : eco cells: (35 19 3) is for feedthrough
PHY-3001 : eco cells: (35 20 0) is for feedthrough
PHY-3001 : eco cells: (35 21 0) is for feedthrough
PHY-3001 : eco cells: (35 47 1) is for feedthrough
PHY-3001 : eco cells: (35 48 0) is for feedthrough
PHY-3001 : eco cells: (35 55 0) is for feedthrough
PHY-3001 : eco cells: (35 61 3) is for feedthrough
PHY-3001 : eco cells: (35 67 2) is for feedthrough
PHY-3001 : eco cells: (36 2 1) is for feedthrough
PHY-3001 : eco cells: (36 2 2) is for feedthrough
PHY-3001 : eco cells: (36 3 0) is for feedthrough
PHY-3001 : eco cells: (36 4 1) is for feedthrough
PHY-3001 : eco cells: (36 5 0) is for feedthrough
PHY-3001 : eco cells: (36 5 1) is for feedthrough
PHY-3001 : eco cells: (36 6 1) is for feedthrough
PHY-3001 : eco cells: (36 7 1) is for feedthrough
PHY-3001 : eco cells: (36 8 0) is for feedthrough
PHY-3001 : eco cells: (36 8 1) is for feedthrough
PHY-3001 : eco cells: (36 10 3) is for feedthrough
PHY-3001 : eco cells: (36 12 2) is for feedthrough
PHY-3001 : eco cells: (36 13 1) is for feedthrough
PHY-3001 : eco cells: (36 14 0) is for feedthrough
PHY-3001 : eco cells: (36 14 1) is for feedthrough
PHY-3001 : eco cells: (36 14 2) is for feedthrough
PHY-3001 : eco cells: (36 15 0) is for feedthrough
PHY-3001 : eco cells: (36 17 1) is for feedthrough
PHY-3001 : eco cells: (36 18 1) is for feedthrough
PHY-3001 : eco cells: (36 19 1) is for feedthrough
PHY-3001 : eco cells: (36 21 1) is for feedthrough
PHY-3001 : eco cells: (36 25 1) is for feedthrough
PHY-3001 : eco cells: (36 57 0) is for feedthrough
PHY-3001 : eco cells: (36 61 3) is for feedthrough
PHY-3001 : eco cells: (36 63 2) is for feedthrough
PHY-3001 : eco cells: (37 3 0) is for feedthrough
PHY-3001 : eco cells: (37 3 1) is for feedthrough
PHY-3001 : eco cells: (37 5 0) is for feedthrough
PHY-3001 : eco cells: (37 7 1) is for feedthrough
PHY-3001 : eco cells: (37 8 0) is for feedthrough
PHY-3001 : eco cells: (37 11 0) is for feedthrough
PHY-3001 : eco cells: (37 13 1) is for feedthrough
PHY-3001 : eco cells: (37 15 1) is for feedthrough
PHY-3001 : eco cells: (37 16 0) is for feedthrough
PHY-3001 : eco cells: (37 16 1) is for feedthrough
PHY-3001 : eco cells: (37 18 0) is for feedthrough
PHY-3001 : eco cells: (37 18 1) is for feedthrough
PHY-3001 : eco cells: (37 18 2) is for feedthrough
PHY-3001 : eco cells: (37 19 1) is for feedthrough
PHY-3001 : eco cells: (37 38 1) is for feedthrough
PHY-3001 : eco cells: (37 39 1) is for feedthrough
PHY-3001 : eco cells: (37 52 3) is for feedthrough
PHY-3001 : eco cells: (37 63 2) is for feedthrough
PHY-3001 : eco cells: (37 66 1) is for feedthrough
PHY-3001 : eco cells: (38 4 1) is for feedthrough
PHY-3001 : eco cells: (38 4 2) is for feedthrough
PHY-3001 : eco cells: (38 5 2) is for feedthrough
PHY-3001 : eco cells: (38 6 3) is for feedthrough
PHY-3001 : eco cells: (38 7 0) is for feedthrough
PHY-3001 : eco cells: (38 7 2) is for feedthrough
PHY-3001 : eco cells: (38 8 0) is for feedthrough
PHY-3001 : eco cells: (38 9 0) is for feedthrough
PHY-3001 : eco cells: (38 9 1) is for feedthrough
PHY-3001 : eco cells: (38 9 2) is for feedthrough
PHY-3001 : eco cells: (38 10 0) is for feedthrough
PHY-3001 : eco cells: (38 12 0) is for feedthrough
PHY-3001 : eco cells: (38 12 1) is for feedthrough
PHY-3001 : eco cells: (38 14 3) is for feedthrough
PHY-3001 : eco cells: (38 15 1) is for feedthrough
PHY-3001 : eco cells: (38 16 1) is for feedthrough
PHY-3001 : eco cells: (38 17 0) is for feedthrough
PHY-3001 : eco cells: (38 17 1) is for feedthrough
PHY-3001 : eco cells: (38 38 2) is for feedthrough
PHY-3001 : eco cells: (38 39 1) is for feedthrough
PHY-3001 : eco cells: (38 39 2) is for feedthrough
PHY-3001 : eco cells: (38 45 2) is for feedthrough
PHY-3001 : eco cells: (38 52 2) is for feedthrough
PHY-3001 : eco cells: (38 64 3) is for feedthrough
PHY-3001 : eco cells: (38 65 3) is for feedthrough
PHY-3001 : eco cells: (39 7 0) is for feedthrough
PHY-3001 : eco cells: (39 7 1) is for feedthrough
PHY-3001 : eco cells: (39 7 3) is for feedthrough
PHY-3001 : eco cells: (39 8 3) is for feedthrough
PHY-3001 : eco cells: (39 9 0) is for feedthrough
PHY-3001 : eco cells: (39 9 3) is for feedthrough
PHY-3001 : eco cells: (39 10 0) is for feedthrough
PHY-3001 : eco cells: (39 11 2) is for feedthrough
PHY-3001 : eco cells: (39 13 0) is for feedthrough
PHY-3001 : eco cells: (39 13 3) is for feedthrough
PHY-3001 : eco cells: (39 14 1) is for feedthrough
PHY-3001 : eco cells: (39 15 3) is for feedthrough
PHY-3001 : eco cells: (39 18 1) is for feedthrough
PHY-3001 : eco cells: (39 43 2) is for feedthrough
PHY-3001 : eco cells: (39 61 3) is for feedthrough
PHY-3001 : eco cells: (39 66 3) is for feedthrough
PHY-3001 : eco cells: (39 67 0) is for feedthrough
PHY-3001 : eco cells: (39 68 3) is for feedthrough
PHY-3001 : eco cells: (39 69 1) is for feedthrough
PHY-3001 : eco cells: (39 69 2) is for feedthrough
PHY-3001 : eco cells: 9183 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 9246 instances, 9141 slices, 291 macros(1458 instances: 956 mslices 502 lslices)
PHY-3001 : Start timing update ...
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 23108 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.928243s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (99.3%)

PHY-3001 : End placement; No cells to be placed.
RUN-1003 : finish command "place -eco" in  1.437941s wall, 1.421875s user + 0.000000s system = 1.421875s CPU (98.9%)

RUN-1004 : used memory is 1239 MB, reserved memory is 1235 MB, peak memory is 1244 MB
RUN-1001 : Eco place succeeded
RUN-1002 : start command "route -eco"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 9248 instances
RUN-1001 : 4571 mslices, 4570 lslices, 60 pads, 42 brams, 0 dsps
RUN-1001 : There are total 23110 nets
RUN-1001 : 16950 nets have 2 pins
RUN-1001 : 4478 nets have [3 - 5] pins
RUN-1001 : 1042 nets have [6 - 10] pins
RUN-1001 : 387 nets have [11 - 20] pins
RUN-1001 : 240 nets have [21 - 99] pins
RUN-1001 : 13 nets have 100+ pins
PHY-1001 : 4571 mslices, 4570 lslices, 60 pads, 42 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 23108 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 1241, reserve = 1237, peak = 1244.
PHY-1001 : Detailed router is running in eco mode.
PHY-1001 : Refresh detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net COM3/GNRMC/u_fifo/wr_to_rd_cross_inst/secondary_clk_i will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : eco open net = 0
PHY-1001 : Current memory(MB): used = 1264, reserve = 1260, peak = 1264.
PHY-1001 : End build detailed router design. 2.542442s wall, 2.546875s user + 0.000000s system = 2.546875s CPU (100.2%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 94% nets.
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End initial clock net routed; 0.019898s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (78.5%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.033507s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (93.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.034567s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (90.4%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.027938s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (111.9%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.029112s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (107.3%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.033609s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (93.0%)

PHY-1001 : Current memory(MB): used = 1264, reserve = 1260, peak = 1264.
PHY-1001 : End phase 1; 0.214200s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (102.1%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 1264, reserve = 1260, peak = 1264.
PHY-1001 : End initial routed; 0.151392s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (92.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/21881(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.255   |   0.000   |   0   
RUN-1001 :   Hold   |   0.130   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.794957s wall, 3.796875s user + 0.000000s system = 3.796875s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1263, reserve = 1259, peak = 1264.
PHY-1001 : End phase 2; 3.946467s wall, 3.937500s user + 0.000000s system = 3.937500s CPU (99.8%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.266597s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (99.6%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.142873s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (98.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.155946s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (140.3%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.138931s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (101.2%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.151849s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (102.9%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.140695s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (100.0%)

PHY-1001 : ===== DR Iter 6 =====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 6; 0.248656s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (100.5%)

PHY-1001 : ===== DR Iter 7 =====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 7; 0.156831s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (99.6%)

PHY-1001 : ==== DR Iter 8 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 8; 0.149005s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (115.3%)

PHY-1001 : ==== DR Iter 9 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 9; 0.136626s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (102.9%)

PHY-1001 : ==== DR Iter 10 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 10; 0.246663s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (95.0%)

PHY-1001 : ==== DR Iter 11 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 11; 0.130787s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (107.5%)

PHY-1001 : ===== DR Iter 12 =====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 12; 0.135524s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (92.2%)

PHY-1001 : ==== DR Iter 13 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 13; 0.137697s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (102.1%)

PHY-1001 : ==== DR Iter 14 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 14; 0.134791s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (92.7%)

PHY-1001 : ==== DR Iter 15 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 15; 0.136246s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (114.7%)

PHY-1001 : ==== DR Iter 16 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 16; 0.133334s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (105.5%)

PHY-1001 : ==== DR Iter 17 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 17; 0.131239s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (95.2%)

PHY-1001 : ===== DR Iter 18 =====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 18; 0.139240s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (112.2%)

PHY-1001 : ==== DR Iter 19 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 19; 0.136584s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (91.5%)

PHY-1001 : ==== DR Iter 20 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 20; 0.133180s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (129.1%)

PHY-1001 : ==== DR Iter 21 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 21; 0.137539s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (102.2%)

PHY-1001 : ==== DR Iter 22 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 22; 0.135031s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (92.6%)

PHY-1001 : ==== DR Iter 23 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 23; 0.131299s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (107.1%)

PHY-1001 : ==== DR Iter 24 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 24; 0.134012s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (93.3%)

PHY-1001 : ===== DR Iter 25 =====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 25; 0.142616s wall, 0.156250s user + 0.031250s system = 0.187500s CPU (131.5%)

PHY-1001 : ==== DR Iter 26 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 26; 0.139252s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (101.0%)

PHY-1001 : ==== DR Iter 27 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 27; 0.133784s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (93.4%)

PHY-1001 : ==== DR Iter 28 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 28; 0.134722s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (104.4%)

PHY-1001 : ==== DR Iter 29 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 29; 0.240118s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (97.6%)

PHY-1001 : ==== DR Iter 30 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 30; 0.142382s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (109.7%)

PHY-1001 : ==== DR Iter 31 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 31; 0.138522s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (101.5%)

PHY-1001 : ==== DR Iter 32 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 32; 0.134059s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (104.9%)

PHY-1001 : ===== DR Iter 33 =====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 33; 0.141098s wall, 0.140625s user + 0.046875s system = 0.187500s CPU (132.9%)

PHY-1001 : ==== DR Iter 34 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 34; 0.137562s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (124.9%)

PHY-1001 : ==== DR Iter 35 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 35; 0.156011s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (130.2%)

PHY-1001 : ==== DR Iter 36 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 36; 0.140903s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (99.8%)

PHY-1001 : ==== DR Iter 37 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 37; 0.138123s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (113.1%)

PHY-1001 : ==== DR Iter 38 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 38; 0.135052s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (104.1%)

PHY-1001 : ==== DR Iter 39 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 39; 0.135963s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (91.9%)

PHY-1001 : ==== DR Iter 40 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 40; 0.134051s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (104.9%)

PHY-1001 : ==== DR Iter 41 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 41; 0.134949s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (104.2%)

PHY-1001 : ===== DR Iter 42 =====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 42; 0.143808s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (97.8%)

PHY-1001 : ==== DR Iter 43 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 43; 0.137590s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (124.9%)

PHY-1001 : ==== DR Iter 44 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 44; 0.135312s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (103.9%)

PHY-1001 : ==== DR Iter 45 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 45; 0.137106s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (102.6%)

PHY-1001 : ==== DR Iter 46 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 46; 0.133992s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (93.3%)

PHY-1001 : ==== DR Iter 47 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 47; 0.134166s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (104.8%)

PHY-1001 : ==== DR Iter 48 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 48; 0.132221s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (94.5%)

PHY-1001 : ==== DR Iter 49 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 49; 0.132566s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (106.1%)

PHY-1001 : ==== DR Iter 50 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 50; 0.132982s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (94.0%)

PHY-1001 : ==== DR Iter 51 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 51; 0.133627s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (105.2%)

PHY-1001 : ===== DR Iter 52 =====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 52; 0.145525s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (96.6%)

PHY-1001 : ==== DR Iter 53 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 53; 0.139570s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (89.6%)

PHY-1001 : ==== DR Iter 54 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 54; 0.137778s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (113.4%)

PHY-1001 : ==== DR Iter 55 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 55; 0.137178s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (102.5%)

PHY-1001 : ==== DR Iter 56 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 56; 0.155464s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (100.5%)

PHY-1001 : ==== DR Iter 57 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 57; 0.162362s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (96.2%)

PHY-1001 : ==== DR Iter 58 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 58; 0.146214s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (106.9%)

PHY-1001 : ==== DR Iter 59 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 59; 0.134551s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (92.9%)

PHY-1001 : ==== DR Iter 60 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 60; 0.141950s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (99.1%)

PHY-1001 : ==== DR Iter 61 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 61; 0.142178s wall, 0.156250s user + 0.031250s system = 0.187500s CPU (131.9%)

PHY-1001 : ==== DR Iter 62 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 62; 0.137147s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (91.1%)

PHY-1001 : ===== DR Iter 63 =====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 63; 0.146576s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (106.6%)

PHY-1001 : ==== DR Iter 64 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 64; 0.139672s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (111.9%)

PHY-1001 : ==== DR Iter 65 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 65; 0.142450s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (109.7%)

PHY-1001 : ==== DR Iter 66 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 66; 0.147201s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (116.8%)

PHY-1001 : ==== DR Iter 67 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 67; 0.139673s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (89.5%)

PHY-1001 : ==== DR Iter 68 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 68; 0.146734s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (95.8%)

PHY-1001 : ==== DR Iter 69 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 69; 0.152089s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (102.7%)

PHY-1001 : ==== DR Iter 70 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 70; 0.148870s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (94.5%)

PHY-1001 : ==== DR Iter 71 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 71; 0.144477s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (108.1%)

PHY-1001 : ==== DR Iter 72 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 72; 0.150220s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (93.6%)

PHY-1001 : ==== DR Iter 73 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 73; 0.145808s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (96.4%)

PHY-1001 : ==== DR Iter 74 ====
PHY-1022 : len = 2.15102e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 74; 0.155714s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (110.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/21881(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.255   |   0.000   |   0   
RUN-1001 :   Hold   |   0.130   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 4.527951s wall, 4.515625s user + 0.000000s system = 4.515625s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 1246, reserve = 1260, peak = 1264.
PHY-1001 : End phase 3; 15.630980s wall, 15.828125s user + 0.234375s system = 16.062500s CPU (102.8%)

PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 1026 feed throughs used by 742 nets
PHY-1001 : Current memory(MB): used = 1247, reserve = 1260, peak = 1264.
PHY-1001 : End export database. 3.474091s wall, 3.421875s user + 0.031250s system = 3.453125s CPU (99.4%)

PHY-1001 : Routing violations:
PHY-8023 ERROR: Location: (x19y12_local4), nets: COM3/GNRMC/u_fifo/ram_inst/ramread0_syn_2315[111] COM3/GNRMC/u_fifo/ram_inst/ramread0_syn_2315[101]
PHY-1001 : End of Routing Violations.
RUN-1003 : finish command "route -eco" in  27.140455s wall, 27.281250s user + 0.265625s system = 27.546875s CPU (101.5%)

RUN-1004 : used memory is 1235 MB, reserved memory is 1245 MB, peak memory is 1264 MB
RUN-8102 ERROR: Incremental route failed
PHY-1001 : Routing violations:
PHY-8023 ERROR: Location: (x19y12_local4), nets: COM3/GNRMC/u_fifo/ram_inst/ramread0_syn_2315[111] COM3/GNRMC/u_fifo/ram_inst/ramread0_syn_2315[101]
PHY-1001 : End of Routing Violations.
RUN-1003 : finish command "route" in  149.917517s wall, 194.625000s user + 1.328125s system = 195.953125s CPU (130.7%)

RUN-1004 : used memory is 1235 MB, reserved memory is 1245 MB, peak memory is 1264 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250710_192530.log"

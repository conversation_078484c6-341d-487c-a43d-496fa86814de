============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Jun 12 15:28:07 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(514)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
RUN-1001 : Project manager successfully analyzed 21 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.935457s wall, 1.640625s user + 4.281250s system = 5.921875s CPU (99.8%)

RUN-1004 : used memory is 78 MB, reserved memory is 40 MB, peak memory is 89 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.964509s wall, 1.875000s user + 0.078125s system = 1.953125s CPU (99.4%)

RUN-1004 : used memory is 298 MB, reserved memory is 267 MB, peak memory is 301 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 58 trigger nets, 58 data nets.
KIT-1004 : Chipwatcher code = 1110111010000001
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=158) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=158) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=158)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=158)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22983/23 useful/useless nets, 19699/12 useful/useless insts
SYN-1016 : Merged 29 instances.
SYN-1032 : 22587/20 useful/useless nets, 20205/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 478 better
SYN-1014 : Optimize round 2
SYN-1032 : 22203/45 useful/useless nets, 19821/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.957344s wall, 2.812500s user + 0.156250s system = 2.968750s CPU (100.4%)

RUN-1004 : used memory is 327 MB, reserved memory is 294 MB, peak memory is 328 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22275/441 useful/useless nets, 19944/63 useful/useless insts
SYN-1016 : Merged 69 instances.
SYN-2571 : Optimize after map_dsp, round 1, 573 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 56 instances.
SYN-2501 : Optimize round 1, 114 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 22779/5 useful/useless nets, 20448/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83920, tnet num: 22779, tinst num: 20447, tnode num: 117803, tedge num: 130708.
TMR-2508 : Levelizing timing graph completed, there are 85 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.340379s wall, 1.296875s user + 0.031250s system = 1.328125s CPU (99.1%)

RUN-1004 : used memory is 470 MB, reserved memory is 439 MB, peak memory is 470 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22779 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 282 (3.29), #lev = 7 (1.66)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 282 (3.29), #lev = 7 (1.66)
SYN-3001 : Logic optimization runtime opt =   0.04 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 614 instances into 282 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 487 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 160 adder to BLE ...
SYN-4008 : Packed 160 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  5.562168s wall, 5.421875s user + 0.125000s system = 5.546875s CPU (99.7%)

RUN-1004 : used memory is 353 MB, reserved memory is 324 MB, peak memory is 581 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  8.909622s wall, 8.609375s user + 0.296875s system = 8.906250s CPU (100.0%)

RUN-1004 : used memory is 354 MB, reserved memory is 324 MB, peak memory is 581 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/rmc_com2/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/rmc_com2/GPRMC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/rmc_com2/GPRMC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/rmc_com2/GPRMC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/rmc_com2/GPRMC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/rmc_com2/GPRMC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/rmc_com2/GPRMC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/rmc_com2/GPRMC_data[0]
SYN-5055 WARNING: The kept net COM2/rx_err will be merged to another kept net COM2/rmc_com2/GPRMC_err
SYN-5055 WARNING: The kept net FMC/Error_state[0] will be merged to another kept net COM2/rmc_com2/GPRMC_sub_state_cnt0[3]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (335 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19772 instances
RUN-0007 : 5682 luts, 12543 seqs, 943 mslices, 491 lslices, 56 pads, 52 brams, 0 dsps
RUN-1001 : There are total 22127 nets
RUN-1001 : 16539 nets have 2 pins
RUN-1001 : 4433 nets have [3 - 5] pins
RUN-1001 : 770 nets have [6 - 10] pins
RUN-1001 : 261 nets have [11 - 20] pins
RUN-1001 : 100 nets have [21 - 99] pins
RUN-1001 : 24 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4743     
RUN-1001 :   No   |  No   |  Yes  |     645     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    6556     
RUN-1001 :   Yes  |  No   |  Yes  |     499     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  118  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 126
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19770 instances, 5682 luts, 12543 seqs, 1434 slices, 280 macros(1434 instances: 943 mslices 491 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1131 pins
PHY-3001 : Huge net DATA/done_div with 1644 pins
PHY-0007 : Cell area utilization is 64%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82447, tnet num: 22125, tinst num: 19770, tnode num: 116465, tedge num: 129554.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.297059s wall, 1.250000s user + 0.046875s system = 1.296875s CPU (100.0%)

RUN-1004 : used memory is 531 MB, reserved memory is 504 MB, peak memory is 581 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22125 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.283777s wall, 2.187500s user + 0.093750s system = 2.281250s CPU (99.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.40484e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19770.
PHY-3001 : Level 1 #clusters 2106.
PHY-3001 : End clustering;  0.163139s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (143.7%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 64%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 882252, overlap = 680.312
PHY-3002 : Step(2): len = 788210, overlap = 783.562
PHY-3002 : Step(3): len = 532026, overlap = 929.344
PHY-3002 : Step(4): len = 458704, overlap = 1009.28
PHY-3002 : Step(5): len = 361456, overlap = 1086.81
PHY-3002 : Step(6): len = 320160, overlap = 1161.62
PHY-3002 : Step(7): len = 271630, overlap = 1250.44
PHY-3002 : Step(8): len = 245583, overlap = 1312.62
PHY-3002 : Step(9): len = 221061, overlap = 1379.19
PHY-3002 : Step(10): len = 198056, overlap = 1421.25
PHY-3002 : Step(11): len = 184443, overlap = 1445.38
PHY-3002 : Step(12): len = 171045, overlap = 1477.38
PHY-3002 : Step(13): len = 152231, overlap = 1531.88
PHY-3002 : Step(14): len = 142722, overlap = 1546.28
PHY-3002 : Step(15): len = 128893, overlap = 1556.12
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.32644e-07
PHY-3002 : Step(16): len = 132313, overlap = 1547.91
PHY-3002 : Step(17): len = 169822, overlap = 1495.62
PHY-3002 : Step(18): len = 181453, overlap = 1381
PHY-3002 : Step(19): len = 184596, overlap = 1316.91
PHY-3002 : Step(20): len = 183004, overlap = 1276.47
PHY-3002 : Step(21): len = 181086, overlap = 1263.06
PHY-3002 : Step(22): len = 177775, overlap = 1240.44
PHY-3002 : Step(23): len = 174892, overlap = 1241.59
PHY-3002 : Step(24): len = 171440, overlap = 1239.53
PHY-3002 : Step(25): len = 168715, overlap = 1228.12
PHY-3002 : Step(26): len = 167613, overlap = 1233.19
PHY-3002 : Step(27): len = 164460, overlap = 1242.75
PHY-3002 : Step(28): len = 163590, overlap = 1250.72
PHY-3002 : Step(29): len = 162288, overlap = 1255.47
PHY-3002 : Step(30): len = 161918, overlap = 1262.44
PHY-3002 : Step(31): len = 161527, overlap = 1273.97
PHY-3002 : Step(32): len = 160358, overlap = 1283.44
PHY-3002 : Step(33): len = 159390, overlap = 1281.38
PHY-3002 : Step(34): len = 159766, overlap = 1249.19
PHY-3002 : Step(35): len = 159496, overlap = 1233.59
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 1.66529e-06
PHY-3002 : Step(36): len = 163518, overlap = 1222.75
PHY-3002 : Step(37): len = 174828, overlap = 1160.28
PHY-3002 : Step(38): len = 177909, overlap = 1099.84
PHY-3002 : Step(39): len = 180644, overlap = 1079.59
PHY-3002 : Step(40): len = 182065, overlap = 1053.56
PHY-3002 : Step(41): len = 183667, overlap = 1032.75
PHY-3002 : Step(42): len = 182653, overlap = 1047.56
PHY-3002 : Step(43): len = 182791, overlap = 1051.16
PHY-3002 : Step(44): len = 181534, overlap = 1047.09
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 3.33058e-06
PHY-3002 : Step(45): len = 192194, overlap = 985.25
PHY-3002 : Step(46): len = 206756, overlap = 882.656
PHY-3002 : Step(47): len = 212063, overlap = 851.125
PHY-3002 : Step(48): len = 214912, overlap = 812.5
PHY-3002 : Step(49): len = 215376, overlap = 790.344
PHY-3002 : Step(50): len = 214527, overlap = 811.562
PHY-3002 : Step(51): len = 212934, overlap = 837.219
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 6.66115e-06
PHY-3002 : Step(52): len = 225472, overlap = 790.156
PHY-3002 : Step(53): len = 240032, overlap = 737
PHY-3002 : Step(54): len = 244435, overlap = 702.312
PHY-3002 : Step(55): len = 246013, overlap = 705.438
PHY-3002 : Step(56): len = 245106, overlap = 684.469
PHY-3002 : Step(57): len = 242794, overlap = 696.812
PHY-3002 : Step(58): len = 241021, overlap = 716.75
PHY-3002 : Step(59): len = 239958, overlap = 724.594
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.33223e-05
PHY-3002 : Step(60): len = 254651, overlap = 647.312
PHY-3002 : Step(61): len = 269159, overlap = 550.188
PHY-3002 : Step(62): len = 270744, overlap = 541.562
PHY-3002 : Step(63): len = 270761, overlap = 523.031
PHY-3002 : Step(64): len = 269578, overlap = 519.781
PHY-3002 : Step(65): len = 268888, overlap = 519.344
PHY-3002 : Step(66): len = 266556, overlap = 520.094
PHY-3002 : Step(67): len = 265987, overlap = 522.25
PHY-3002 : Step(68): len = 264641, overlap = 519.094
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 2.66446e-05
PHY-3002 : Step(69): len = 278548, overlap = 490.094
PHY-3002 : Step(70): len = 289586, overlap = 447.094
PHY-3002 : Step(71): len = 291458, overlap = 433.406
PHY-3002 : Step(72): len = 291311, overlap = 432.469
PHY-3002 : Step(73): len = 290850, overlap = 427.219
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 5.32892e-05
PHY-3002 : Step(74): len = 297722, overlap = 418.531
PHY-3002 : Step(75): len = 305771, overlap = 399.906
PHY-3002 : Step(76): len = 309891, overlap = 359.594
PHY-3002 : Step(77): len = 310553, overlap = 349.469
PHY-3002 : Step(78): len = 308669, overlap = 359.625
PHY-3002 : Step(79): len = 307274, overlap = 354.75
PHY-3002 : Step(80): len = 305347, overlap = 350.406
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000106578
PHY-3002 : Step(81): len = 310154, overlap = 334.875
PHY-3002 : Step(82): len = 316036, overlap = 313
PHY-3002 : Step(83): len = 319222, overlap = 289.406
PHY-3002 : Step(84): len = 321519, overlap = 291.031
PHY-3002 : Step(85): len = 321135, overlap = 295.281
PHY-3002 : Step(86): len = 321212, overlap = 274.156
PHY-3002 : Step(87): len = 320684, overlap = 271.375
PHY-3002 : Step(88): len = 321266, overlap = 271.188
PHY-3002 : Step(89): len = 319914, overlap = 262.156
PHY-3002 : Step(90): len = 319311, overlap = 275.312
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000201647
PHY-3002 : Step(91): len = 322900, overlap = 271.688
PHY-3002 : Step(92): len = 325348, overlap = 267.125
PHY-3002 : Step(93): len = 325641, overlap = 283.906
PHY-3002 : Step(94): len = 326435, overlap = 284.281
PHY-3002 : Step(95): len = 326592, overlap = 287.156
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000359908
PHY-3002 : Step(96): len = 328280, overlap = 284.125
PHY-3002 : Step(97): len = 334013, overlap = 274.125
PHY-3002 : Step(98): len = 336537, overlap = 264.188
PHY-3002 : Step(99): len = 337957, overlap = 242.406
PHY-3002 : Step(100): len = 338752, overlap = 245.719
PHY-3002 : Step(101): len = 338723, overlap = 264.438
PHY-3002 : Step(102): len = 338917, overlap = 260.906
PHY-3002 : Step(103): len = 338348, overlap = 270.25
PHY-3002 : Step(104): len = 338878, overlap = 262.062
PHY-3002 : Step(105): len = 338205, overlap = 238.062
PHY-3002 : Step(106): len = 338626, overlap = 232.406
PHY-3002 : Step(107): len = 338132, overlap = 228.969
PHY-3002 : Step(108): len = 338141, overlap = 225.906
PHY-3002 : Step(109): len = 338491, overlap = 245.781
PHY-3002 : Step(110): len = 338336, overlap = 241.844
PHY-3002 : Step(111): len = 338435, overlap = 233.094
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.013332s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (117.2%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/22127.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 452360, over cnt = 1298(3%), over = 5932, worst = 37
PHY-1001 : End global iterations;  0.935774s wall, 1.171875s user + 0.031250s system = 1.203125s CPU (128.6%)

PHY-1001 : Congestion index: top1 = 75.82, top5 = 54.81, top10 = 45.11, top15 = 39.19.
PHY-3001 : End congestion estimation;  1.225946s wall, 1.421875s user + 0.062500s system = 1.484375s CPU (121.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22125 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.036557s wall, 1.000000s user + 0.046875s system = 1.046875s CPU (101.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.93164e-05
PHY-3002 : Step(112): len = 384518, overlap = 152.031
PHY-3002 : Step(113): len = 393171, overlap = 140.688
PHY-3002 : Step(114): len = 391439, overlap = 144.062
PHY-3002 : Step(115): len = 388560, overlap = 143.75
PHY-3002 : Step(116): len = 394060, overlap = 137.281
PHY-3002 : Step(117): len = 399227, overlap = 140.75
PHY-3002 : Step(118): len = 399461, overlap = 142.969
PHY-3002 : Step(119): len = 401958, overlap = 139.25
PHY-3002 : Step(120): len = 403063, overlap = 144.438
PHY-3002 : Step(121): len = 404133, overlap = 147.125
PHY-3002 : Step(122): len = 406625, overlap = 156.781
PHY-3002 : Step(123): len = 408022, overlap = 167.469
PHY-3002 : Step(124): len = 409184, overlap = 171.031
PHY-3002 : Step(125): len = 410685, overlap = 168.344
PHY-3002 : Step(126): len = 412610, overlap = 169.844
PHY-3002 : Step(127): len = 415156, overlap = 172.219
PHY-3002 : Step(128): len = 416025, overlap = 178.812
PHY-3002 : Step(129): len = 416702, overlap = 183.906
PHY-3002 : Step(130): len = 418639, overlap = 183.719
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000198633
PHY-3002 : Step(131): len = 417429, overlap = 182.281
PHY-3002 : Step(132): len = 418918, overlap = 180.688
PHY-3002 : Step(133): len = 420026, overlap = 179.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000397266
PHY-3002 : Step(134): len = 423974, overlap = 172.375
PHY-3002 : Step(135): len = 429496, overlap = 171.562
PHY-3002 : Step(136): len = 435303, overlap = 166.344
PHY-3002 : Step(137): len = 439171, overlap = 149.125
PHY-3002 : Step(138): len = 442128, overlap = 144.219
PHY-3002 : Step(139): len = 441929, overlap = 145.188
PHY-3002 : Step(140): len = 442307, overlap = 143.062
PHY-3002 : Step(141): len = 443173, overlap = 140.344
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000794531
PHY-3002 : Step(142): len = 442789, overlap = 134.438
PHY-3002 : Step(143): len = 444406, overlap = 128.812
PHY-3002 : Step(144): len = 447025, overlap = 132.375
PHY-3002 : Step(145): len = 448367, overlap = 132.469
PHY-3002 : Step(146): len = 448569, overlap = 134.844
PHY-3002 : Step(147): len = 450142, overlap = 134.25
PHY-3002 : Step(148): len = 451062, overlap = 132.781
PHY-3002 : Step(149): len = 450369, overlap = 131.312
PHY-3002 : Step(150): len = 449596, overlap = 126.375
PHY-3002 : Step(151): len = 450236, overlap = 126.656
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(152): len = 450770, overlap = 128.031
PHY-3002 : Step(153): len = 453219, overlap = 128.844
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 61/22127.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 527744, over cnt = 2177(6%), over = 10581, worst = 66
PHY-1001 : End global iterations;  1.251103s wall, 1.859375s user + 0.031250s system = 1.890625s CPU (151.1%)

PHY-1001 : Congestion index: top1 = 81.94, top5 = 62.06, top10 = 51.79, top15 = 46.11.
PHY-3001 : End congestion estimation;  1.542480s wall, 2.156250s user + 0.031250s system = 2.187500s CPU (141.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22125 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.269367s wall, 1.250000s user + 0.015625s system = 1.265625s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.68637e-05
PHY-3002 : Step(154): len = 459162, overlap = 369.969
PHY-3002 : Step(155): len = 465288, overlap = 296.938
PHY-3002 : Step(156): len = 456861, overlap = 280.625
PHY-3002 : Step(157): len = 452965, overlap = 273.812
PHY-3002 : Step(158): len = 451683, overlap = 263.094
PHY-3002 : Step(159): len = 452293, overlap = 248.969
PHY-3002 : Step(160): len = 449796, overlap = 245.594
PHY-3002 : Step(161): len = 448376, overlap = 238.688
PHY-3002 : Step(162): len = 448418, overlap = 223.562
PHY-3002 : Step(163): len = 447123, overlap = 214.062
PHY-3002 : Step(164): len = 446424, overlap = 205.906
PHY-3002 : Step(165): len = 446951, overlap = 204.875
PHY-3002 : Step(166): len = 445714, overlap = 206.594
PHY-3002 : Step(167): len = 443777, overlap = 210.406
PHY-3002 : Step(168): len = 443217, overlap = 210.438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000193727
PHY-3002 : Step(169): len = 443275, overlap = 203.594
PHY-3002 : Step(170): len = 444965, overlap = 194.125
PHY-3002 : Step(171): len = 446762, overlap = 188.094
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000387455
PHY-3002 : Step(172): len = 449140, overlap = 177.344
PHY-3002 : Step(173): len = 455489, overlap = 169.094
PHY-3002 : Step(174): len = 460888, overlap = 159.906
PHY-3002 : Step(175): len = 462913, overlap = 159.281
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82447, tnet num: 22125, tinst num: 19770, tnode num: 116465, tedge num: 129554.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.741848s wall, 1.656250s user + 0.078125s system = 1.734375s CPU (99.6%)

RUN-1004 : used memory is 570 MB, reserved memory is 546 MB, peak memory is 708 MB
OPT-1001 : Total overflow 556.56 peak overflow 3.53
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 367/22127.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 550160, over cnt = 2575(7%), over = 9545, worst = 28
PHY-1001 : End global iterations;  1.300253s wall, 2.093750s user + 0.031250s system = 2.125000s CPU (163.4%)

PHY-1001 : Congestion index: top1 = 61.12, top5 = 49.38, top10 = 44.37, top15 = 41.15.
PHY-1001 : End incremental global routing;  1.569009s wall, 2.359375s user + 0.031250s system = 2.390625s CPU (152.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22125 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.087054s wall, 1.062500s user + 0.031250s system = 1.093750s CPU (100.6%)

OPT-1001 : 19 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 56 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19692 has valid locations, 268 needs to be replaced
PHY-3001 : design contains 20019 instances, 5789 luts, 12685 seqs, 1434 slices, 280 macros(1434 instances: 943 mslices 491 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 479636
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17229/22376.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 565592, over cnt = 2616(7%), over = 9655, worst = 28
PHY-1001 : End global iterations;  0.203445s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (176.6%)

PHY-1001 : Congestion index: top1 = 61.68, top5 = 49.76, top10 = 44.64, top15 = 41.35.
PHY-3001 : End congestion estimation;  0.464790s wall, 0.609375s user + 0.015625s system = 0.625000s CPU (134.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83295, tnet num: 22374, tinst num: 20019, tnode num: 117662, tedge num: 130752.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.711993s wall, 1.687500s user + 0.015625s system = 1.703125s CPU (99.5%)

RUN-1004 : used memory is 616 MB, reserved memory is 606 MB, peak memory is 708 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22374 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.836440s wall, 2.812500s user + 0.015625s system = 2.828125s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(176): len = 479306, overlap = 5.6875
PHY-3002 : Step(177): len = 479406, overlap = 5.6875
PHY-3002 : Step(178): len = 479590, overlap = 5.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17311/22376.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 564336, over cnt = 2629(7%), over = 9668, worst = 28
PHY-1001 : End global iterations;  0.191265s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (155.2%)

PHY-1001 : Congestion index: top1 = 61.51, top5 = 49.67, top10 = 44.58, top15 = 41.37.
PHY-3001 : End congestion estimation;  0.459442s wall, 0.562500s user + 0.000000s system = 0.562500s CPU (122.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22374 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.096337s wall, 1.093750s user + 0.031250s system = 1.125000s CPU (102.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000547856
PHY-3002 : Step(179): len = 479704, overlap = 161.344
PHY-3002 : Step(180): len = 480663, overlap = 161.375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00109571
PHY-3002 : Step(181): len = 480718, overlap = 161.406
PHY-3002 : Step(182): len = 481249, overlap = 161.094
PHY-3001 : Final: Len = 481249, Over = 161.094
PHY-3001 : End incremental placement;  5.790792s wall, 6.656250s user + 0.203125s system = 6.859375s CPU (118.5%)

OPT-1001 : Total overflow 561.62 peak overflow 3.53
OPT-1001 : End high-fanout net optimization;  9.044251s wall, 10.859375s user + 0.281250s system = 11.140625s CPU (123.2%)

OPT-1001 : Current memory(MB): used = 714, reserve = 695, peak = 731.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17268/22376.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 566560, over cnt = 2603(7%), over = 9164, worst = 28
PHY-1002 : len = 609304, over cnt = 1955(5%), over = 5219, worst = 18
PHY-1002 : len = 656816, over cnt = 675(1%), over = 1556, worst = 18
PHY-1002 : len = 670776, over cnt = 345(0%), over = 721, worst = 18
PHY-1002 : len = 684328, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.562110s wall, 2.187500s user + 0.000000s system = 2.187500s CPU (140.0%)

PHY-1001 : Congestion index: top1 = 50.45, top5 = 44.51, top10 = 41.60, top15 = 39.55.
OPT-1001 : End congestion update;  1.838231s wall, 2.453125s user + 0.000000s system = 2.453125s CPU (133.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22374 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.953059s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (100.0%)

OPT-0007 : Start: WNS 4567 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.797770s wall, 3.421875s user + 0.000000s system = 3.421875s CPU (122.3%)

OPT-1001 : Current memory(MB): used = 688, reserve = 670, peak = 731.
OPT-1001 : End physical optimization;  13.929180s wall, 16.437500s user + 0.390625s system = 16.828125s CPU (120.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5789 LUT to BLE ...
SYN-4008 : Packed 5789 LUT and 2885 SEQ to BLE.
SYN-4003 : Packing 9800 remaining SEQ's ...
SYN-4005 : Packed 3236 SEQ with LUT/SLICE
SYN-4006 : 181 single LUT's are left
SYN-4006 : 6564 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 12353/14020 primitive instances ...
PHY-3001 : End packing;  3.442465s wall, 3.453125s user + 0.000000s system = 3.453125s CPU (100.3%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8286 instances
RUN-1001 : 4087 mslices, 4086 lslices, 56 pads, 52 brams, 0 dsps
RUN-1001 : There are total 19542 nets
RUN-1001 : 13614 nets have 2 pins
RUN-1001 : 4552 nets have [3 - 5] pins
RUN-1001 : 830 nets have [6 - 10] pins
RUN-1001 : 383 nets have [11 - 20] pins
RUN-1001 : 153 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
PHY-3001 : design contains 8284 instances, 8173 slices, 280 macros(1434 instances: 943 mslices 491 lslices)
PHY-3001 : Cell area utilization is 87%
PHY-3001 : After packing: Len = 500417, Over = 377.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7935/19542.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 643776, over cnt = 1565(4%), over = 2534, worst = 7
PHY-1002 : len = 650560, over cnt = 964(2%), over = 1335, worst = 7
PHY-1002 : len = 662096, over cnt = 367(1%), over = 470, worst = 6
PHY-1002 : len = 667840, over cnt = 127(0%), over = 152, worst = 5
PHY-1002 : len = 670856, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.309417s wall, 2.093750s user + 0.078125s system = 2.171875s CPU (165.9%)

PHY-1001 : Congestion index: top1 = 51.59, top5 = 44.74, top10 = 41.24, top15 = 38.94.
PHY-3001 : End congestion estimation;  1.663660s wall, 2.437500s user + 0.078125s system = 2.515625s CPU (151.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68651, tnet num: 19540, tinst num: 8284, tnode num: 93641, tedge num: 113063.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.846837s wall, 1.812500s user + 0.031250s system = 1.843750s CPU (99.8%)

RUN-1004 : used memory is 612 MB, reserved memory is 604 MB, peak memory is 731 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19540 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.865519s wall, 2.828125s user + 0.046875s system = 2.875000s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.07674e-05
PHY-3002 : Step(183): len = 502072, overlap = 371
PHY-3002 : Step(184): len = 501103, overlap = 390
PHY-3002 : Step(185): len = 502388, overlap = 396.5
PHY-3002 : Step(186): len = 505066, overlap = 409
PHY-3002 : Step(187): len = 503293, overlap = 401.5
PHY-3002 : Step(188): len = 503872, overlap = 405.25
PHY-3002 : Step(189): len = 501064, overlap = 408.5
PHY-3002 : Step(190): len = 499864, overlap = 411.25
PHY-3002 : Step(191): len = 498062, overlap = 409.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000101535
PHY-3002 : Step(192): len = 501314, overlap = 395.75
PHY-3002 : Step(193): len = 505336, overlap = 387
PHY-3002 : Step(194): len = 506539, overlap = 374
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000203069
PHY-3002 : Step(195): len = 513075, overlap = 356
PHY-3002 : Step(196): len = 522481, overlap = 343
PHY-3002 : Step(197): len = 521738, overlap = 344.25
PHY-3002 : Step(198): len = 521446, overlap = 344
PHY-3002 : Step(199): len = 521250, overlap = 341.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.744701s wall, 0.750000s user + 0.781250s system = 1.531250s CPU (205.6%)

PHY-3001 : Trial Legalized: Len = 629454
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 688/19542.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 722504, over cnt = 2488(7%), over = 4071, worst = 8
PHY-1002 : len = 733128, over cnt = 1767(5%), over = 2640, worst = 6
PHY-1002 : len = 759280, over cnt = 522(1%), over = 704, worst = 5
PHY-1002 : len = 767112, over cnt = 195(0%), over = 263, worst = 4
PHY-1002 : len = 771256, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.186080s wall, 3.562500s user + 0.031250s system = 3.593750s CPU (164.4%)

PHY-1001 : Congestion index: top1 = 50.00, top5 = 45.10, top10 = 42.50, top15 = 40.82.
PHY-3001 : End congestion estimation;  2.588296s wall, 3.937500s user + 0.046875s system = 3.984375s CPU (153.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19540 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.010258s wall, 1.000000s user + 0.015625s system = 1.015625s CPU (100.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000180142
PHY-3002 : Step(200): len = 586700, overlap = 87
PHY-3002 : Step(201): len = 568333, overlap = 127
PHY-3002 : Step(202): len = 556991, overlap = 176.25
PHY-3002 : Step(203): len = 550268, overlap = 220.75
PHY-3002 : Step(204): len = 546989, overlap = 242.5
PHY-3002 : Step(205): len = 544333, overlap = 253.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000360283
PHY-3002 : Step(206): len = 548461, overlap = 250.25
PHY-3002 : Step(207): len = 552755, overlap = 244
PHY-3002 : Step(208): len = 553945, overlap = 246
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(209): len = 556183, overlap = 244.25
PHY-3002 : Step(210): len = 561193, overlap = 243.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.031785s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (98.3%)

PHY-3001 : Legalized: Len = 598669, Over = 0
PHY-3001 : Spreading special nets. 46 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.091206s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (102.8%)

PHY-3001 : 69 instances has been re-located, deltaX = 29, deltaY = 46, maxDist = 3.
PHY-3001 : Final: Len = 600023, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68651, tnet num: 19540, tinst num: 8284, tnode num: 93641, tedge num: 113063.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.090100s wall, 2.078125s user + 0.015625s system = 2.093750s CPU (100.2%)

RUN-1004 : used memory is 617 MB, reserved memory is 610 MB, peak memory is 731 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 4110/19542.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 705144, over cnt = 2291(6%), over = 3653, worst = 8
PHY-1002 : len = 716152, over cnt = 1547(4%), over = 2213, worst = 8
PHY-1002 : len = 735528, over cnt = 589(1%), over = 812, worst = 8
PHY-1002 : len = 746112, over cnt = 111(0%), over = 133, worst = 4
PHY-1002 : len = 748376, over cnt = 11(0%), over = 11, worst = 1
PHY-1001 : End global iterations;  1.982909s wall, 3.093750s user + 0.046875s system = 3.140625s CPU (158.4%)

PHY-1001 : Congestion index: top1 = 49.33, top5 = 44.05, top10 = 41.44, top15 = 39.67.
PHY-1001 : End incremental global routing;  2.323155s wall, 3.437500s user + 0.046875s system = 3.484375s CPU (150.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19540 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.012552s wall, 1.000000s user + 0.015625s system = 1.015625s CPU (100.3%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 56 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8223 has valid locations, 17 needs to be replaced
PHY-3001 : design contains 8299 instances, 8188 slices, 280 macros(1434 instances: 943 mslices 491 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 603640
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17627/19563.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 752744, over cnt = 49(0%), over = 62, worst = 3
PHY-1002 : len = 752656, over cnt = 40(0%), over = 40, worst = 1
PHY-1002 : len = 752888, over cnt = 13(0%), over = 13, worst = 1
PHY-1002 : len = 752992, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 753104, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.757448s wall, 0.796875s user + 0.015625s system = 0.812500s CPU (107.3%)

PHY-1001 : Congestion index: top1 = 49.53, top5 = 44.14, top10 = 41.48, top15 = 39.72.
PHY-3001 : End congestion estimation;  1.082186s wall, 1.109375s user + 0.031250s system = 1.140625s CPU (105.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68779, tnet num: 19561, tinst num: 8299, tnode num: 93800, tedge num: 113252.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.047292s wall, 2.031250s user + 0.015625s system = 2.046875s CPU (100.0%)

RUN-1004 : used memory is 653 MB, reserved memory is 636 MB, peak memory is 731 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19561 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.070308s wall, 3.062500s user + 0.015625s system = 3.078125s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(211): len = 603230, overlap = 0.75
PHY-3002 : Step(212): len = 603324, overlap = 0.25
PHY-3002 : Step(213): len = 603054, overlap = 0
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17616/19563.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 750512, over cnt = 32(0%), over = 45, worst = 5
PHY-1002 : len = 750432, over cnt = 27(0%), over = 29, worst = 2
PHY-1002 : len = 750792, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 750808, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 751032, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.741839s wall, 0.781250s user + 0.015625s system = 0.796875s CPU (107.4%)

PHY-1001 : Congestion index: top1 = 49.57, top5 = 44.33, top10 = 41.63, top15 = 39.83.
PHY-3001 : End congestion estimation;  1.064700s wall, 1.093750s user + 0.015625s system = 1.109375s CPU (104.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19561 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.986931s wall, 0.984375s user + 0.000000s system = 0.984375s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000661791
PHY-3002 : Step(214): len = 603078, overlap = 2.75
PHY-3002 : Step(215): len = 603052, overlap = 2.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007435s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (210.2%)

PHY-3001 : Legalized: Len = 603161, Over = 0
PHY-3001 : End spreading;  0.073982s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (105.6%)

PHY-3001 : Final: Len = 603161, Over = 0
PHY-3001 : End incremental placement;  6.889325s wall, 7.093750s user + 0.156250s system = 7.250000s CPU (105.2%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.772569s wall, 12.250000s user + 0.218750s system = 12.468750s CPU (115.7%)

OPT-1001 : Current memory(MB): used = 729, reserve = 715, peak = 734.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17621/19563.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 751824, over cnt = 19(0%), over = 26, worst = 3
PHY-1002 : len = 751776, over cnt = 12(0%), over = 13, worst = 2
PHY-1002 : len = 751952, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 751984, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.585527s wall, 0.609375s user + 0.015625s system = 0.625000s CPU (106.7%)

PHY-1001 : Congestion index: top1 = 49.48, top5 = 44.28, top10 = 41.58, top15 = 39.77.
OPT-1001 : End congestion update;  0.909281s wall, 0.921875s user + 0.015625s system = 0.937500s CPU (103.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19561 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.832530s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (101.3%)

OPT-0007 : Start: WNS 4742 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.746959s wall, 1.765625s user + 0.015625s system = 1.781250s CPU (102.0%)

OPT-1001 : Current memory(MB): used = 729, reserve = 715, peak = 734.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19561 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.831994s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (99.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17642/19563.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 751984, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.129560s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (108.5%)

PHY-1001 : Congestion index: top1 = 49.48, top5 = 44.28, top10 = 41.58, top15 = 39.77.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19561 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.862391s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (99.7%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4742 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 49.103448
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4742ps with logic level 8 
RUN-1001 :       #2 path slack 4742ps with logic level 8 
RUN-1001 :       #3 path slack 4840ps with logic level 8 
RUN-1001 :       #4 path slack 4840ps with logic level 8 
OPT-1001 : End physical optimization;  17.041457s wall, 18.593750s user + 0.265625s system = 18.859375s CPU (110.7%)

RUN-1003 : finish command "place" in  74.838723s wall, 137.921875s user + 7.203125s system = 145.125000s CPU (193.9%)

RUN-1004 : used memory is 645 MB, reserved memory is 632 MB, peak memory is 734 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.887113s wall, 3.265625s user + 0.031250s system = 3.296875s CPU (174.7%)

RUN-1004 : used memory is 645 MB, reserved memory is 632 MB, peak memory is 734 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8301 instances
RUN-1001 : 4094 mslices, 4094 lslices, 56 pads, 52 brams, 0 dsps
RUN-1001 : There are total 19563 nets
RUN-1001 : 13619 nets have 2 pins
RUN-1001 : 4552 nets have [3 - 5] pins
RUN-1001 : 840 nets have [6 - 10] pins
RUN-1001 : 389 nets have [11 - 20] pins
RUN-1001 : 153 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68779, tnet num: 19561, tinst num: 8299, tnode num: 93800, tedge num: 113252.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.026361s wall, 2.031250s user + 0.000000s system = 2.031250s CPU (100.2%)

RUN-1004 : used memory is 627 MB, reserved memory is 614 MB, peak memory is 734 MB
PHY-1001 : 4094 mslices, 4094 lslices, 56 pads, 52 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19561 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 683984, over cnt = 2406(6%), over = 4038, worst = 8
PHY-1002 : len = 698760, over cnt = 1582(4%), over = 2353, worst = 8
PHY-1002 : len = 714968, over cnt = 755(2%), over = 1123, worst = 6
PHY-1002 : len = 736120, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 736232, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.092452s wall, 3.390625s user + 0.000000s system = 3.390625s CPU (162.0%)

PHY-1001 : Congestion index: top1 = 50.11, top5 = 44.35, top10 = 41.56, top15 = 39.66.
PHY-1001 : End global routing;  2.473434s wall, 3.765625s user + 0.000000s system = 3.765625s CPU (152.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 710, reserve = 700, peak = 734.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 982, reserve = 971, peak = 982.
PHY-1001 : End build detailed router design. 5.161484s wall, 5.062500s user + 0.093750s system = 5.156250s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 194504, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.994627s wall, 0.953125s user + 0.031250s system = 0.984375s CPU (99.0%)

PHY-1001 : Current memory(MB): used = 1019, reserve = 1009, peak = 1019.
PHY-1001 : End phase 1; 1.002209s wall, 0.968750s user + 0.031250s system = 1.000000s CPU (99.8%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.77522e+06, over cnt = 1485(0%), over = 1493, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1034, reserve = 1022, peak = 1034.
PHY-1001 : End initial routed; 21.882656s wall, 48.375000s user + 0.515625s system = 48.890625s CPU (223.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18351(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.723   |   0.000   |   0   
RUN-1001 :   Hold   |   0.130   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.697832s wall, 3.703125s user + 0.000000s system = 3.703125s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1046, reserve = 1034, peak = 1046.
PHY-1001 : End phase 2; 25.580667s wall, 52.078125s user + 0.515625s system = 52.593750s CPU (205.6%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.77522e+06, over cnt = 1485(0%), over = 1493, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.255867s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (103.8%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.76122e+06, over cnt = 509(0%), over = 512, worst = 2, crit = 0
PHY-1001 : End DR Iter 1; 1.156111s wall, 1.671875s user + 0.000000s system = 1.671875s CPU (144.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.76222e+06, over cnt = 161(0%), over = 161, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.481039s wall, 0.640625s user + 0.000000s system = 0.640625s CPU (133.2%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.76342e+06, over cnt = 15(0%), over = 15, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.413677s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (124.6%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.7637e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.230798s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (94.8%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.76381e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.188324s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (99.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18351(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.466   |   0.000   |   0   
RUN-1001 :   Hold   |   0.130   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.614457s wall, 3.609375s user + 0.000000s system = 3.609375s CPU (99.9%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 374 feed throughs used by 319 nets
PHY-1001 : End commit to database; 2.283836s wall, 2.250000s user + 0.015625s system = 2.265625s CPU (99.2%)

PHY-1001 : Current memory(MB): used = 1134, reserve = 1125, peak = 1134.
PHY-1001 : End phase 3; 9.137628s wall, 9.906250s user + 0.015625s system = 9.921875s CPU (108.6%)

PHY-1003 : Routed, final wirelength = 1.76381e+06
PHY-1001 : Current memory(MB): used = 1138, reserve = 1129, peak = 1138.
PHY-1001 : End export database. 0.062625s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.8%)

PHY-1001 : End detail routing;  41.389940s wall, 68.515625s user + 0.671875s system = 69.187500s CPU (167.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68779, tnet num: 19561, tinst num: 8299, tnode num: 93800, tedge num: 113252.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.751572s wall, 1.750000s user + 0.000000s system = 1.750000s CPU (99.9%)

RUN-1004 : used memory is 1068 MB, reserved memory is 1070 MB, peak memory is 1138 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  50.484935s wall, 78.906250s user + 0.671875s system = 79.578125s CPU (157.6%)

RUN-1004 : used memory is 1068 MB, reserved memory is 1070 MB, peak memory is 1138 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8800   out of  19600   44.90%
#reg                    12790   out of  19600   65.26%
#le                     15335
  #lut only              2545   out of  15335   16.60%
  #reg only              6535   out of  15335   42.61%
  #lut&reg               6255   out of  15335   40.79%
#dsp                        0   out of     29    0.00%
#bram                      52   out of     64   81.25%
  #bram9k                  52
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       56   out of    188   29.79%
  #ireg                     9
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6979
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          194
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT        E11        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT        A13        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT        F10        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         E8        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         E7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT        A10        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C6        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         D6        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         C7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT        E12        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         F1        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         A7        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H13        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J11        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT        E13        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         R7        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         M7        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         P7        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         T8        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         P8        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         N8        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M9        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         T9        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         R9        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         T6        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         P9        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         N9        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT        P11        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         T5        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         B2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        L10        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        E15        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        R14        LVCMOS33           8            N/A            OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT         R2        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT        N12        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        B10        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         J6        LVCMOS33           8            N/A            NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        R15        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E6        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15335  |7366    |1434    |12832   |52      |0       |
|  AnyFog_dataX                      |AnyFog          |221    |107     |22      |183     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |84     |58      |22      |50      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |225    |111     |22      |188     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |87     |64      |22      |52      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |223    |116     |22      |183     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |87     |61      |22      |50      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |3481   |856     |34      |3401    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |729    |65      |5       |719     |0       |0       |
|    STADOP_com2                     |STADOP          |558    |50      |0       |554     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |47      |14      |41      |0       |0       |
|    head_com2                       |uniheading      |269    |78      |5       |254     |0       |0       |
|    rmc_com2                        |Gprmc           |136    |37      |0       |131     |0       |0       |
|    uart_com2                       |Agrica          |1439   |294     |10      |1417    |0       |0       |
|  DATA                              |Data_Processing |8584   |4219    |1062    |6900    |0       |0       |
|    DIV_Dtemp                       |Divider         |796    |302     |84      |672     |0       |0       |
|    DIV_Utemp                       |Divider         |627    |308     |84      |505     |0       |0       |
|    DIV_accX                        |Divider         |614    |337     |84      |483     |0       |0       |
|    DIV_accY                        |Divider         |614    |335     |111     |444     |0       |0       |
|    DIV_accZ                        |Divider         |691    |360     |132     |483     |0       |0       |
|    DIV_rateX                       |Divider         |672    |398     |132     |466     |0       |0       |
|    DIV_rateY                       |Divider         |566    |344     |132     |363     |0       |0       |
|    DIV_rateZ                       |Divider         |575    |378     |132     |365     |0       |0       |
|    genclk                          |genclk          |90     |60      |20      |57      |0       |0       |
|  FMC                               |FMC_Ctrl        |550    |487     |43      |382     |0       |0       |
|  IIC                               |I2C_master      |297    |272     |11      |256     |0       |0       |
|  IMU_CTRL                          |SCHA634         |910    |689     |61      |745     |0       |0       |
|    CtrlData                        |CtrlData        |461    |407     |47      |334     |0       |0       |
|      usms                          |Time_1ms        |27     |22      |5       |19      |0       |0       |
|    SPIM                            |SPI_SCHA634     |449    |282     |14      |411     |0       |0       |
|  POWER                             |POWER_EN        |102    |44      |38      |42      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |730    |461     |119     |498     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |730    |461     |119     |498     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |311    |170     |0       |296     |0       |0       |
|        reg_inst                    |register        |307    |167     |0       |292     |0       |0       |
|        tap_inst                    |tap             |4      |3       |0       |4       |0       |0       |
|      trigger_inst                  |trigger         |419    |291     |119     |202     |0       |0       |
|        bus_inst                    |bus_top         |176    |114     |62      |63      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |26     |16      |10      |10      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |52     |34      |18      |19      |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |97     |63      |34      |33      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |167    |133     |29      |110     |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13562  
    #2          2       3580   
    #3          3        692   
    #4          4        280   
    #5        5-10       918   
    #6        11-50      439   
    #7       51-100      25    
    #8       101-500      4    
    #9        >500        2    
  Average     2.15             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.139552s wall, 3.765625s user + 0.000000s system = 3.765625s CPU (176.0%)

RUN-1004 : used memory is 1069 MB, reserved memory is 1070 MB, peak memory is 1138 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68779, tnet num: 19561, tinst num: 8299, tnode num: 93800, tedge num: 113252.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.755037s wall, 1.750000s user + 0.000000s system = 1.750000s CPU (99.7%)

RUN-1004 : used memory is 1070 MB, reserved memory is 1072 MB, peak memory is 1138 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19561 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.582731s wall, 1.578125s user + 0.015625s system = 1.593750s CPU (100.7%)

RUN-1004 : used memory is 1075 MB, reserved memory is 1076 MB, peak memory is 1138 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 28b752c1c0e7d7ba92ac5beed3c46754b7b56ac64969e4b0149cfdbc1d295011 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8299
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19563, pip num: 149851
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 374
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3234 valid insts, and 417764 bits set as '1'.
BIT-1004 : the usercode register value: 00000000000000001110111010000001
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  13.203167s wall, 127.187500s user + 0.343750s system = 127.531250s CPU (965.9%)

RUN-1004 : used memory is 1206 MB, reserved memory is 1193 MB, peak memory is 1321 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250612_152807.log"

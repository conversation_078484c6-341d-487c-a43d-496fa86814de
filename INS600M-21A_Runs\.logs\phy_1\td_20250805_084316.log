============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Tue Aug  5 08:43:16 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.720468s wall, 1.718750s user + 3.968750s system = 5.687500s CPU (99.4%)

RUN-1004 : used memory is 78 MB, reserved memory is 40 MB, peak memory is 89 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.875943s wall, 1.812500s user + 0.062500s system = 1.875000s CPU (99.9%)

RUN-1004 : used memory is 299 MB, reserved memory is 269 MB, peak memory is 302 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 2 view nodes, 24 trigger nets, 24 data nets.
KIT-1004 : Chipwatcher code = 1010010110001101
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=78) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=78) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=78)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=78)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22225/12 useful/useless nets, 19240/4 useful/useless insts
SYN-1016 : Merged 17 instances.
SYN-1032 : 22001/16 useful/useless nets, 19564/12 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 2 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 2 mux instances.
SYN-1015 : Optimize round 1, 279 better
SYN-1014 : Optimize round 2
SYN-1032 : 21792/30 useful/useless nets, 19355/32 useful/useless insts
SYN-1015 : Optimize round 2, 64 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.364390s wall, 2.359375s user + 0.015625s system = 2.375000s CPU (100.4%)

RUN-1004 : used memory is 323 MB, reserved memory is 291 MB, peak memory is 325 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 21816/155 useful/useless nets, 19400/29 useful/useless insts
SYN-1016 : Merged 21 instances.
SYN-2571 : Optimize after map_dsp, round 1, 205 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 24 instances.
SYN-2501 : Optimize round 1, 50 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 14 macro adder
SYN-1019 : Optimized 9 mux instances.
SYN-1016 : Merged 10 instances.
SYN-1032 : 22187/5 useful/useless nets, 19771/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80441, tnet num: 22187, tinst num: 19770, tnode num: 113051, tedge num: 125682.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.346089s wall, 1.281250s user + 0.062500s system = 1.343750s CPU (99.8%)

RUN-1004 : used memory is 460 MB, reserved memory is 429 MB, peak memory is 460 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22187 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 173 (3.57), #lev = 7 (1.79)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 173 (3.57), #lev = 7 (1.79)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 387 instances into 173 LUTs, name keeping = 77%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 290 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 108 adder to BLE ...
SYN-4008 : Packed 108 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.755967s wall, 4.578125s user + 0.156250s system = 4.734375s CPU (99.5%)

RUN-1004 : used memory is 357 MB, reserved memory is 340 MB, peak memory is 566 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.481049s wall, 7.250000s user + 0.218750s system = 7.468750s CPU (99.8%)

RUN-1004 : used memory is 357 MB, reserved memory is 340 MB, peak memory is 566 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (177 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19079 instances
RUN-0007 : 5518 luts, 12041 seqs, 937 mslices, 494 lslices, 60 pads, 24 brams, 0 dsps
RUN-1001 : There are total 21523 nets
RUN-1001 : 16218 nets have 2 pins
RUN-1001 : 4138 nets have [3 - 5] pins
RUN-1001 : 818 nets have [6 - 10] pins
RUN-1001 : 225 nets have [11 - 20] pins
RUN-1001 : 106 nets have [21 - 99] pins
RUN-1001 : 18 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4792     
RUN-1001 :   No   |  No   |  Yes  |     631     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     349     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19077 instances, 5518 luts, 12041 seqs, 1431 slices, 283 macros(1431 instances: 937 mslices 494 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 61%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 79004, tnet num: 21521, tinst num: 19077, tnode num: 111208, tedge num: 124086.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.286230s wall, 1.234375s user + 0.031250s system = 1.265625s CPU (98.4%)

RUN-1004 : used memory is 519 MB, reserved memory is 492 MB, peak memory is 566 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21521 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.212636s wall, 2.125000s user + 0.062500s system = 2.187500s CPU (98.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.56751e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19077.
PHY-3001 : Level 1 #clusters 2159.
PHY-3001 : End clustering;  0.153218s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (112.2%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 61%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 858354, overlap = 563.469
PHY-3002 : Step(2): len = 774689, overlap = 610.625
PHY-3002 : Step(3): len = 489226, overlap = 797.656
PHY-3002 : Step(4): len = 432477, overlap = 871.875
PHY-3002 : Step(5): len = 347427, overlap = 994.75
PHY-3002 : Step(6): len = 309532, overlap = 1047.38
PHY-3002 : Step(7): len = 259925, overlap = 1111.41
PHY-3002 : Step(8): len = 228323, overlap = 1165.78
PHY-3002 : Step(9): len = 204241, overlap = 1201.44
PHY-3002 : Step(10): len = 185464, overlap = 1237.12
PHY-3002 : Step(11): len = 168587, overlap = 1272.06
PHY-3002 : Step(12): len = 156131, overlap = 1315.84
PHY-3002 : Step(13): len = 146413, overlap = 1365.53
PHY-3002 : Step(14): len = 133469, overlap = 1391.97
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.42209e-06
PHY-3002 : Step(15): len = 140557, overlap = 1345.25
PHY-3002 : Step(16): len = 193464, overlap = 1206.84
PHY-3002 : Step(17): len = 204796, overlap = 1098.25
PHY-3002 : Step(18): len = 206552, overlap = 1026.44
PHY-3002 : Step(19): len = 201446, overlap = 980.312
PHY-3002 : Step(20): len = 194584, overlap = 974
PHY-3002 : Step(21): len = 189015, overlap = 954.594
PHY-3002 : Step(22): len = 183781, overlap = 943.5
PHY-3002 : Step(23): len = 180105, overlap = 945.969
PHY-3002 : Step(24): len = 178047, overlap = 952.844
PHY-3002 : Step(25): len = 174902, overlap = 964.188
PHY-3002 : Step(26): len = 173430, overlap = 947.406
PHY-3002 : Step(27): len = 172499, overlap = 933.188
PHY-3002 : Step(28): len = 171347, overlap = 926.125
PHY-3002 : Step(29): len = 170342, overlap = 912.469
PHY-3002 : Step(30): len = 169978, overlap = 910.75
PHY-3002 : Step(31): len = 168778, overlap = 906.156
PHY-3002 : Step(32): len = 167664, overlap = 887.156
PHY-3002 : Step(33): len = 166820, overlap = 883.125
PHY-3002 : Step(34): len = 165031, overlap = 873.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.84419e-06
PHY-3002 : Step(35): len = 173428, overlap = 856.031
PHY-3002 : Step(36): len = 187428, overlap = 822.438
PHY-3002 : Step(37): len = 189675, overlap = 772.156
PHY-3002 : Step(38): len = 191553, overlap = 748.75
PHY-3002 : Step(39): len = 190911, overlap = 742.312
PHY-3002 : Step(40): len = 190497, overlap = 766.625
PHY-3002 : Step(41): len = 189291, overlap = 760.062
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 5.68838e-06
PHY-3002 : Step(42): len = 200273, overlap = 725.562
PHY-3002 : Step(43): len = 212337, overlap = 696.281
PHY-3002 : Step(44): len = 218298, overlap = 696.188
PHY-3002 : Step(45): len = 220251, overlap = 685.625
PHY-3002 : Step(46): len = 219438, overlap = 687.688
PHY-3002 : Step(47): len = 218152, overlap = 702.188
PHY-3002 : Step(48): len = 216535, overlap = 690.125
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 1.13768e-05
PHY-3002 : Step(49): len = 228237, overlap = 664.219
PHY-3002 : Step(50): len = 241265, overlap = 592.688
PHY-3002 : Step(51): len = 246830, overlap = 551.531
PHY-3002 : Step(52): len = 248726, overlap = 558.844
PHY-3002 : Step(53): len = 247137, overlap = 555.562
PHY-3002 : Step(54): len = 246117, overlap = 561.812
PHY-3002 : Step(55): len = 243696, overlap = 574.219
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 2.27535e-05
PHY-3002 : Step(56): len = 254414, overlap = 529.906
PHY-3002 : Step(57): len = 268230, overlap = 449.562
PHY-3002 : Step(58): len = 272764, overlap = 418.375
PHY-3002 : Step(59): len = 273818, overlap = 400.844
PHY-3002 : Step(60): len = 272832, overlap = 395.094
PHY-3002 : Step(61): len = 271378, overlap = 387.844
PHY-3002 : Step(62): len = 268900, overlap = 379.312
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 4.5507e-05
PHY-3002 : Step(63): len = 276963, overlap = 335.781
PHY-3002 : Step(64): len = 286696, overlap = 298.562
PHY-3002 : Step(65): len = 290662, overlap = 273.906
PHY-3002 : Step(66): len = 292299, overlap = 245.594
PHY-3002 : Step(67): len = 290604, overlap = 242.219
PHY-3002 : Step(68): len = 288165, overlap = 248.062
PHY-3002 : Step(69): len = 286460, overlap = 247.125
PHY-3002 : Step(70): len = 285440, overlap = 242.219
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 9.1014e-05
PHY-3002 : Step(71): len = 292323, overlap = 232.125
PHY-3002 : Step(72): len = 298107, overlap = 221.094
PHY-3002 : Step(73): len = 299513, overlap = 206.656
PHY-3002 : Step(74): len = 300762, overlap = 199.969
PHY-3002 : Step(75): len = 299932, overlap = 179.062
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000182028
PHY-3002 : Step(76): len = 302640, overlap = 188.906
PHY-3002 : Step(77): len = 307383, overlap = 178.781
PHY-3002 : Step(78): len = 310360, overlap = 166.562
PHY-3002 : Step(79): len = 312851, overlap = 142.906
PHY-3002 : Step(80): len = 313106, overlap = 137.156
PHY-3002 : Step(81): len = 312689, overlap = 136.438
PHY-3002 : Step(82): len = 312229, overlap = 147.312
PHY-3002 : Step(83): len = 312192, overlap = 170.719
PHY-3002 : Step(84): len = 312634, overlap = 168.594
PHY-3002 : Step(85): len = 312276, overlap = 168.625
PHY-3002 : Step(86): len = 311463, overlap = 175.25
PHY-3002 : Step(87): len = 310524, overlap = 171.375
PHY-3002 : Step(88): len = 310206, overlap = 171.875
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000338085
PHY-3002 : Step(89): len = 311593, overlap = 166.969
PHY-3002 : Step(90): len = 314837, overlap = 150.125
PHY-3002 : Step(91): len = 316236, overlap = 155
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000633382
PHY-3002 : Step(92): len = 316832, overlap = 161.281
PHY-3002 : Step(93): len = 320052, overlap = 154.594
PHY-3002 : Step(94): len = 320833, overlap = 151.719
PHY-3002 : Step(95): len = 321818, overlap = 154.344
PHY-3002 : Step(96): len = 321862, overlap = 147.281
PHY-3002 : Step(97): len = 321755, overlap = 149.031
PHY-3002 : Step(98): len = 321935, overlap = 147.031
PHY-3002 : Step(99): len = 322220, overlap = 135.594
PHY-3002 : Step(100): len = 322537, overlap = 137
PHY-3002 : Step(101): len = 322849, overlap = 133.5
PHY-3002 : Step(102): len = 323549, overlap = 135.875
PHY-3002 : Step(103): len = 323495, overlap = 141.781
PHY-3002 : Step(104): len = 323370, overlap = 140.219
PHY-3002 : Step(105): len = 323461, overlap = 134.281
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.015225s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21523.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 419760, over cnt = 1109(3%), over = 5066, worst = 42
PHY-1001 : End global iterations;  0.756759s wall, 1.062500s user + 0.078125s system = 1.140625s CPU (150.7%)

PHY-1001 : Congestion index: top1 = 75.47, top5 = 51.83, top10 = 41.51, top15 = 35.80.
PHY-3001 : End congestion estimation;  0.986669s wall, 1.265625s user + 0.093750s system = 1.359375s CPU (137.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21521 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.951078s wall, 0.937500s user + 0.015625s system = 0.953125s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000102353
PHY-3002 : Step(106): len = 362516, overlap = 125.531
PHY-3002 : Step(107): len = 381091, overlap = 126.812
PHY-3002 : Step(108): len = 381049, overlap = 115.25
PHY-3002 : Step(109): len = 376781, overlap = 114.906
PHY-3002 : Step(110): len = 381820, overlap = 110.875
PHY-3002 : Step(111): len = 384836, overlap = 111.219
PHY-3002 : Step(112): len = 387021, overlap = 103.75
PHY-3002 : Step(113): len = 393590, overlap = 97.8125
PHY-3002 : Step(114): len = 393088, overlap = 105.719
PHY-3002 : Step(115): len = 392067, overlap = 113.375
PHY-3002 : Step(116): len = 393341, overlap = 111.125
PHY-3002 : Step(117): len = 394842, overlap = 113.531
PHY-3002 : Step(118): len = 395708, overlap = 112.562
PHY-3002 : Step(119): len = 397472, overlap = 114.781
PHY-3002 : Step(120): len = 397180, overlap = 114.875
PHY-3002 : Step(121): len = 397602, overlap = 114.25
PHY-3002 : Step(122): len = 399351, overlap = 116.812
PHY-3002 : Step(123): len = 397978, overlap = 118.125
PHY-3002 : Step(124): len = 397640, overlap = 119.031
PHY-3002 : Step(125): len = 398865, overlap = 119.156
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000204705
PHY-3002 : Step(126): len = 398790, overlap = 117.5
PHY-3002 : Step(127): len = 400132, overlap = 116.406
PHY-3002 : Step(128): len = 401916, overlap = 115.875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(129): len = 408450, overlap = 101.844
PHY-3002 : Step(130): len = 417272, overlap = 94.6875
PHY-3002 : Step(131): len = 422297, overlap = 96.0938
PHY-3002 : Step(132): len = 423288, overlap = 94.7812
PHY-3002 : Step(133): len = 424336, overlap = 94.1875
PHY-3002 : Step(134): len = 426288, overlap = 97.375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 90/21523.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 485872, over cnt = 2115(6%), over = 9643, worst = 44
PHY-1001 : End global iterations;  1.111620s wall, 2.000000s user + 0.093750s system = 2.093750s CPU (188.4%)

PHY-1001 : Congestion index: top1 = 73.38, top5 = 56.26, top10 = 48.07, top15 = 43.16.
PHY-3001 : End congestion estimation;  1.442588s wall, 2.328125s user + 0.109375s system = 2.437500s CPU (169.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21521 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.003706s wall, 0.984375s user + 0.015625s system = 1.000000s CPU (99.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000105953
PHY-3002 : Step(135): len = 431192, overlap = 358.875
PHY-3002 : Step(136): len = 435532, overlap = 318
PHY-3002 : Step(137): len = 432071, overlap = 298.781
PHY-3002 : Step(138): len = 429102, overlap = 279.969
PHY-3002 : Step(139): len = 428513, overlap = 259.219
PHY-3002 : Step(140): len = 427563, overlap = 244.562
PHY-3002 : Step(141): len = 423846, overlap = 238.438
PHY-3002 : Step(142): len = 421203, overlap = 229.375
PHY-3002 : Step(143): len = 420599, overlap = 218.719
PHY-3002 : Step(144): len = 417088, overlap = 217.844
PHY-3002 : Step(145): len = 415071, overlap = 219.5
PHY-3002 : Step(146): len = 413602, overlap = 228.281
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000211905
PHY-3002 : Step(147): len = 414814, overlap = 223.688
PHY-3002 : Step(148): len = 417226, overlap = 214.188
PHY-3002 : Step(149): len = 419353, overlap = 212.531
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00042381
PHY-3002 : Step(150): len = 422758, overlap = 201.531
PHY-3002 : Step(151): len = 430009, overlap = 184
PHY-3002 : Step(152): len = 433842, overlap = 171.031
PHY-3002 : Step(153): len = 434397, overlap = 169.344
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 79004, tnet num: 21521, tinst num: 19077, tnode num: 111208, tedge num: 124086.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.673068s wall, 1.578125s user + 0.078125s system = 1.656250s CPU (99.0%)

RUN-1004 : used memory is 557 MB, reserved memory is 532 MB, peak memory is 688 MB
OPT-1001 : Total overflow 537.38 peak overflow 4.34
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 674/21523.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 508208, over cnt = 2275(6%), over = 8026, worst = 27
PHY-1001 : End global iterations;  1.128687s wall, 1.921875s user + 0.046875s system = 1.968750s CPU (174.4%)

PHY-1001 : Congestion index: top1 = 56.25, top5 = 46.19, top10 = 41.30, top15 = 38.14.
PHY-1001 : End incremental global routing;  1.470298s wall, 2.234375s user + 0.046875s system = 2.281250s CPU (155.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21521 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.059687s wall, 1.015625s user + 0.046875s system = 1.062500s CPU (100.3%)

OPT-1001 : 17 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 18997 has valid locations, 217 needs to be replaced
PHY-3001 : design contains 19277 instances, 5607 luts, 12152 seqs, 1431 slices, 283 macros(1431 instances: 937 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 449483
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16639/21723.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 518152, over cnt = 2283(6%), over = 8028, worst = 28
PHY-1001 : End global iterations;  0.205883s wall, 0.390625s user + 0.015625s system = 0.406250s CPU (197.3%)

PHY-1001 : Congestion index: top1 = 55.95, top5 = 46.27, top10 = 41.41, top15 = 38.29.
PHY-3001 : End congestion estimation;  0.467222s wall, 0.656250s user + 0.015625s system = 0.671875s CPU (143.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 79649, tnet num: 21721, tinst num: 19277, tnode num: 112108, tedge num: 124976.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.626389s wall, 2.500000s user + 0.062500s system = 2.562500s CPU (97.6%)

RUN-1004 : used memory is 601 MB, reserved memory is 596 MB, peak memory is 689 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21721 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.853122s wall, 3.703125s user + 0.093750s system = 3.796875s CPU (98.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(154): len = 449547, overlap = 4.625
PHY-3002 : Step(155): len = 450639, overlap = 4.375
PHY-3002 : Step(156): len = 451225, overlap = 4.375
PHY-3002 : Step(157): len = 452076, overlap = 4.3125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(158): len = 452125, overlap = 4.3125
PHY-3002 : Step(159): len = 452883, overlap = 4.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(160): len = 453031, overlap = 4.25
PHY-3002 : Step(161): len = 453642, overlap = 4.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 16647/21723.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 518624, over cnt = 2292(6%), over = 8071, worst = 28
PHY-1001 : End global iterations;  0.215642s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (130.4%)

PHY-1001 : Congestion index: top1 = 56.42, top5 = 46.60, top10 = 41.66, top15 = 38.51.
PHY-3001 : End congestion estimation;  0.525868s wall, 0.578125s user + 0.000000s system = 0.578125s CPU (109.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21721 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.053217s wall, 1.046875s user + 0.000000s system = 1.046875s CPU (99.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00119657
PHY-3002 : Step(162): len = 453235, overlap = 171.312
PHY-3002 : Step(163): len = 453062, overlap = 171.219
PHY-3002 : Step(164): len = 453110, overlap = 171.656
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00239313
PHY-3002 : Step(165): len = 453177, overlap = 171.594
PHY-3002 : Step(166): len = 453355, overlap = 171.344
PHY-3001 : Final: Len = 453355, Over = 171.344
PHY-3001 : End incremental placement;  7.287653s wall, 7.843750s user + 0.500000s system = 8.343750s CPU (114.5%)

OPT-1001 : Total overflow 541.69 peak overflow 4.34
OPT-1001 : End high-fanout net optimization;  10.391800s wall, 11.828125s user + 0.593750s system = 12.421875s CPU (119.5%)

OPT-1001 : Current memory(MB): used = 692, reserve = 672, peak = 708.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16666/21723.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 519392, over cnt = 2236(6%), over = 7614, worst = 26
PHY-1002 : len = 557992, over cnt = 1568(4%), over = 3904, worst = 18
PHY-1002 : len = 589784, over cnt = 643(1%), over = 1576, worst = 18
PHY-1002 : len = 607272, over cnt = 266(0%), over = 538, worst = 11
PHY-1002 : len = 616352, over cnt = 1(0%), over = 1, worst = 1
PHY-1001 : End global iterations;  1.211902s wall, 1.937500s user + 0.000000s system = 1.937500s CPU (159.9%)

PHY-1001 : Congestion index: top1 = 48.04, top5 = 42.02, top10 = 38.81, top15 = 36.74.
OPT-1001 : End congestion update;  1.477536s wall, 2.203125s user + 0.000000s system = 2.203125s CPU (149.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 21721 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.921951s wall, 0.906250s user + 0.015625s system = 0.921875s CPU (100.0%)

OPT-0007 : Start: WNS 3819 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.405515s wall, 3.109375s user + 0.015625s system = 3.125000s CPU (129.9%)

OPT-1001 : Current memory(MB): used = 669, reserve = 651, peak = 708.
OPT-1001 : End physical optimization;  14.798126s wall, 16.984375s user + 0.703125s system = 17.687500s CPU (119.5%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5607 LUT to BLE ...
SYN-4008 : Packed 5607 LUT and 2681 SEQ to BLE.
SYN-4003 : Packing 9471 remaining SEQ's ...
SYN-4005 : Packed 3300 SEQ with LUT/SLICE
SYN-4006 : 125 single LUT's are left
SYN-4006 : 6171 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11778/13562 primitive instances ...
PHY-3001 : End packing;  2.837024s wall, 2.828125s user + 0.000000s system = 2.828125s CPU (99.7%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 7893 instances
RUN-1001 : 3902 mslices, 3902 lslices, 60 pads, 24 brams, 0 dsps
RUN-1001 : There are total 19096 nets
RUN-1001 : 13466 nets have 2 pins
RUN-1001 : 4257 nets have [3 - 5] pins
RUN-1001 : 876 nets have [6 - 10] pins
RUN-1001 : 367 nets have [11 - 20] pins
RUN-1001 : 121 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 7891 instances, 7804 slices, 283 macros(1431 instances: 937 mslices 494 lslices)
PHY-3001 : Cell area utilization is 83%
PHY-3001 : After packing: Len = 471043, Over = 374
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 83%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7753/19096.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 590480, over cnt = 1480(4%), over = 2414, worst = 8
PHY-1002 : len = 597464, over cnt = 916(2%), over = 1288, worst = 6
PHY-1002 : len = 605680, over cnt = 471(1%), over = 669, worst = 6
PHY-1002 : len = 614288, over cnt = 101(0%), over = 143, worst = 6
PHY-1002 : len = 616896, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.237707s wall, 1.953125s user + 0.046875s system = 2.000000s CPU (161.6%)

PHY-1001 : Congestion index: top1 = 50.04, top5 = 42.70, top10 = 38.90, top15 = 36.65.
PHY-3001 : End congestion estimation;  1.565092s wall, 2.281250s user + 0.046875s system = 2.328125s CPU (148.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 65926, tnet num: 19094, tinst num: 7891, tnode num: 89422, tedge num: 108578.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.725162s wall, 1.703125s user + 0.000000s system = 1.703125s CPU (98.7%)

RUN-1004 : used memory is 596 MB, reserved memory is 595 MB, peak memory is 708 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19094 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.691976s wall, 2.671875s user + 0.000000s system = 2.671875s CPU (99.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.95111e-05
PHY-3002 : Step(167): len = 474711, overlap = 359.75
PHY-3002 : Step(168): len = 476289, overlap = 363.25
PHY-3002 : Step(169): len = 476390, overlap = 366
PHY-3002 : Step(170): len = 478689, overlap = 374
PHY-3002 : Step(171): len = 478391, overlap = 382.5
PHY-3002 : Step(172): len = 477983, overlap = 382.5
PHY-3002 : Step(173): len = 477513, overlap = 383.25
PHY-3002 : Step(174): len = 475545, overlap = 380
PHY-3002 : Step(175): len = 473954, overlap = 378.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.90221e-05
PHY-3002 : Step(176): len = 478612, overlap = 367
PHY-3002 : Step(177): len = 482297, overlap = 360.75
PHY-3002 : Step(178): len = 481722, overlap = 358
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000198044
PHY-3002 : Step(179): len = 487868, overlap = 350.75
PHY-3002 : Step(180): len = 496092, overlap = 333.75
PHY-3002 : Step(181): len = 495672, overlap = 332.75
PHY-3002 : Step(182): len = 494206, overlap = 329.5
PHY-3002 : Step(183): len = 494040, overlap = 330.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.813441s wall, 0.937500s user + 1.031250s system = 1.968750s CPU (242.0%)

PHY-3001 : Trial Legalized: Len = 600580
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 83%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 531/19096.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 679856, over cnt = 2250(6%), over = 3722, worst = 8
PHY-1002 : len = 693656, over cnt = 1376(3%), over = 1944, worst = 6
PHY-1002 : len = 708336, over cnt = 612(1%), over = 839, worst = 5
PHY-1002 : len = 716440, over cnt = 307(0%), over = 396, worst = 4
PHY-1002 : len = 723296, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.811787s wall, 3.171875s user + 0.031250s system = 3.203125s CPU (176.8%)

PHY-1001 : Congestion index: top1 = 49.05, top5 = 44.06, top10 = 41.54, top15 = 39.68.
PHY-3001 : End congestion estimation;  2.173609s wall, 3.531250s user + 0.031250s system = 3.562500s CPU (163.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19094 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.922409s wall, 0.906250s user + 0.000000s system = 0.906250s CPU (98.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000175287
PHY-3002 : Step(184): len = 558365, overlap = 83.5
PHY-3002 : Step(185): len = 541175, overlap = 119.25
PHY-3002 : Step(186): len = 530252, overlap = 158
PHY-3002 : Step(187): len = 524498, overlap = 200
PHY-3002 : Step(188): len = 520746, overlap = 220.25
PHY-3002 : Step(189): len = 518864, overlap = 234.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000350574
PHY-3002 : Step(190): len = 522668, overlap = 231.25
PHY-3002 : Step(191): len = 526330, overlap = 226.5
PHY-3002 : Step(192): len = 526721, overlap = 227.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(193): len = 528959, overlap = 224.25
PHY-3002 : Step(194): len = 533771, overlap = 221.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.034219s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (137.0%)

PHY-3001 : Legalized: Len = 571562, Over = 0
PHY-3001 : Spreading special nets. 25 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.078114s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (100.0%)

PHY-3001 : 38 instances has been re-located, deltaX = 13, deltaY = 17, maxDist = 1.
PHY-3001 : Final: Len = 572052, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 65926, tnet num: 19094, tinst num: 7891, tnode num: 89422, tedge num: 108578.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.974374s wall, 1.953125s user + 0.015625s system = 1.968750s CPU (99.7%)

RUN-1004 : used memory is 601 MB, reserved memory is 601 MB, peak memory is 708 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 4155/19096.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 659304, over cnt = 2094(5%), over = 3370, worst = 7
PHY-1002 : len = 671104, over cnt = 1242(3%), over = 1764, worst = 7
PHY-1002 : len = 685968, over cnt = 478(1%), over = 679, worst = 5
PHY-1002 : len = 694928, over cnt = 84(0%), over = 101, worst = 5
PHY-1002 : len = 696960, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.664762s wall, 2.812500s user + 0.109375s system = 2.921875s CPU (175.5%)

PHY-1001 : Congestion index: top1 = 48.36, top5 = 42.74, top10 = 40.10, top15 = 38.32.
PHY-1001 : End incremental global routing;  1.985928s wall, 3.125000s user + 0.109375s system = 3.234375s CPU (162.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19094 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.983546s wall, 0.984375s user + 0.000000s system = 0.984375s CPU (100.1%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 7827 has valid locations, 9 needs to be replaced
PHY-3001 : design contains 7899 instances, 7812 slices, 283 macros(1431 instances: 937 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 573339
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 83%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17129/19103.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 698080, over cnt = 11(0%), over = 12, worst = 2
PHY-1002 : len = 698072, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 698088, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 698120, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.508258s wall, 0.500000s user + 0.000000s system = 0.500000s CPU (98.4%)

PHY-1001 : Congestion index: top1 = 48.36, top5 = 42.73, top10 = 40.09, top15 = 38.31.
PHY-3001 : End congestion estimation;  0.811516s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (100.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 65973, tnet num: 19101, tinst num: 7899, tnode num: 89477, tedge num: 108632.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.943833s wall, 1.937500s user + 0.000000s system = 1.937500s CPU (99.7%)

RUN-1004 : used memory is 629 MB, reserved memory is 615 MB, peak memory is 708 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19101 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.910879s wall, 2.890625s user + 0.015625s system = 2.906250s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(195): len = 573278, overlap = 0.5
PHY-3002 : Step(196): len = 573226, overlap = 0.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 83%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17128/19103.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 698056, over cnt = 13(0%), over = 15, worst = 2
PHY-1002 : len = 698056, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 698088, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 698112, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 698144, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.677652s wall, 0.687500s user + 0.046875s system = 0.734375s CPU (108.4%)

PHY-1001 : Congestion index: top1 = 48.56, top5 = 42.86, top10 = 40.17, top15 = 38.38.
PHY-3001 : End congestion estimation;  0.983386s wall, 1.000000s user + 0.046875s system = 1.046875s CPU (106.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19101 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.922961s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000508517
PHY-3002 : Step(197): len = 573262, overlap = 0.75
PHY-3002 : Step(198): len = 573287, overlap = 0.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007324s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 573305, Over = 0
PHY-3001 : End spreading;  0.069793s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (111.9%)

PHY-3001 : Final: Len = 573305, Over = 0
PHY-3001 : End incremental placement;  6.274518s wall, 6.296875s user + 0.156250s system = 6.453125s CPU (102.8%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  9.750120s wall, 11.015625s user + 0.265625s system = 11.281250s CPU (115.7%)

OPT-1001 : Current memory(MB): used = 703, reserve = 688, peak = 708.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17126/19103.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 698008, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 697976, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 698000, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 698032, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.516520s wall, 0.531250s user + 0.000000s system = 0.531250s CPU (102.9%)

PHY-1001 : Congestion index: top1 = 48.45, top5 = 42.76, top10 = 40.12, top15 = 38.36.
OPT-1001 : End congestion update;  0.822649s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (100.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19101 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.776040s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (100.7%)

OPT-0007 : Start: WNS 3820 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.603445s wall, 1.609375s user + 0.000000s system = 1.609375s CPU (100.4%)

OPT-1001 : Current memory(MB): used = 702, reserve = 687, peak = 708.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19101 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.785273s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (99.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17137/19103.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 698032, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.121261s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (103.1%)

PHY-1001 : Congestion index: top1 = 48.45, top5 = 42.76, top10 = 40.12, top15 = 38.36.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19101 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.770222s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (99.4%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3820 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 48.103448
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3820ps with logic level 4 
OPT-1001 : End physical optimization;  15.570020s wall, 16.828125s user + 0.281250s system = 17.109375s CPU (109.9%)

RUN-1003 : finish command "place" in  65.234042s wall, 105.546875s user + 6.546875s system = 112.093750s CPU (171.8%)

RUN-1004 : used memory is 586 MB, reserved memory is 577 MB, peak memory is 708 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.553879s wall, 2.640625s user + 0.000000s system = 2.640625s CPU (169.9%)

RUN-1004 : used memory is 586 MB, reserved memory is 578 MB, peak memory is 708 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 7901 instances
RUN-1001 : 3902 mslices, 3910 lslices, 60 pads, 24 brams, 0 dsps
RUN-1001 : There are total 19103 nets
RUN-1001 : 13466 nets have 2 pins
RUN-1001 : 4257 nets have [3 - 5] pins
RUN-1001 : 879 nets have [6 - 10] pins
RUN-1001 : 372 nets have [11 - 20] pins
RUN-1001 : 120 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 65973, tnet num: 19101, tinst num: 7899, tnode num: 89477, tedge num: 108632.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.642725s wall, 1.640625s user + 0.000000s system = 1.640625s CPU (99.9%)

RUN-1004 : used memory is 583 MB, reserved memory is 567 MB, peak memory is 708 MB
PHY-1001 : 3902 mslices, 3910 lslices, 60 pads, 24 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19101 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 641696, over cnt = 2210(6%), over = 3749, worst = 9
PHY-1002 : len = 656800, over cnt = 1383(3%), over = 2033, worst = 6
PHY-1002 : len = 673480, over cnt = 612(1%), over = 881, worst = 5
PHY-1002 : len = 687544, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 687592, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.679742s wall, 2.906250s user + 0.078125s system = 2.984375s CPU (177.7%)

PHY-1001 : Congestion index: top1 = 48.34, top5 = 42.74, top10 = 39.90, top15 = 38.06.
PHY-1001 : End global routing;  2.016984s wall, 3.234375s user + 0.093750s system = 3.328125s CPU (165.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 687, reserve = 676, peak = 708.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 957, reserve = 945, peak = 957.
PHY-1001 : End build detailed router design. 4.541429s wall, 4.515625s user + 0.031250s system = 4.546875s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 186136, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.884008s wall, 0.875000s user + 0.000000s system = 0.875000s CPU (99.0%)

PHY-1001 : Current memory(MB): used = 992, reserve = 982, peak = 992.
PHY-1001 : End phase 1; 0.890825s wall, 0.890625s user + 0.000000s system = 0.890625s CPU (100.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 55% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.60021e+06, over cnt = 1192(0%), over = 1196, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1007, reserve = 997, peak = 1007.
PHY-1001 : End initial routed; 13.473170s wall, 39.640625s user + 0.375000s system = 40.015625s CPU (297.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17893(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.297   |   0.000   |   0   
RUN-1001 :   Hold   |   0.241   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.427761s wall, 3.421875s user + 0.000000s system = 3.421875s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 1025, reserve = 1015, peak = 1025.
PHY-1001 : End phase 2; 16.901112s wall, 43.062500s user + 0.375000s system = 43.437500s CPU (257.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.60021e+06, over cnt = 1192(0%), over = 1196, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.243447s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (96.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.58918e+06, over cnt = 370(0%), over = 370, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.703299s wall, 1.171875s user + 0.015625s system = 1.187500s CPU (168.8%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.58966e+06, over cnt = 70(0%), over = 70, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.353292s wall, 0.468750s user + 0.000000s system = 0.468750s CPU (132.7%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.59059e+06, over cnt = 8(0%), over = 8, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.207307s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (128.1%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.59067e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.183481s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (102.2%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.59067e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.181739s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (103.2%)

PHY-1001 : ===== DR Iter 6 =====
PHY-1022 : len = 1.59073e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 6; 0.176015s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (97.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17893(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.297   |   0.000   |   0   
RUN-1001 :   Hold   |   0.241   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.477444s wall, 3.468750s user + 0.000000s system = 3.468750s CPU (99.8%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 277 feed throughs used by 242 nets
PHY-1001 : End commit to database; 2.076455s wall, 2.046875s user + 0.031250s system = 2.078125s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1113, reserve = 1106, peak = 1113.
PHY-1001 : End phase 3; 8.075245s wall, 8.656250s user + 0.046875s system = 8.703125s CPU (107.8%)

PHY-1003 : Routed, final wirelength = 1.59073e+06
PHY-1001 : Current memory(MB): used = 1117, reserve = 1110, peak = 1117.
PHY-1001 : End export database. 0.059026s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (79.4%)

PHY-1001 : End detail routing;  30.883222s wall, 57.593750s user + 0.468750s system = 58.062500s CPU (188.0%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 65973, tnet num: 19101, tinst num: 7899, tnode num: 89477, tedge num: 108632.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.635053s wall, 1.625000s user + 0.000000s system = 1.625000s CPU (99.4%)

RUN-1004 : used memory is 1045 MB, reserved memory is 1042 MB, peak memory is 1117 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  38.815686s wall, 66.718750s user + 0.578125s system = 67.296875s CPU (173.4%)

RUN-1004 : used memory is 1045 MB, reserved memory is 1042 MB, peak memory is 1117 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8622   out of  19600   43.99%
#reg                    12250   out of  19600   62.50%
#le                     14745
  #lut only              2495   out of  14745   16.92%
  #reg only              6123   out of  14745   41.53%
  #lut&reg               6127   out of  14745   41.55%
#dsp                        0   out of     29    0.00%
#bram                      24   out of     64   37.50%
  #bram9k                  22
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6692
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          105
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         M3        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D14        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         P6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT        D16        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        R15        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |14745  |7191    |1431    |12294   |24      |0       |
|  AnyFog_dataX                      |AnyFog          |205    |82      |22      |166     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |54      |22      |47      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |209    |86      |22      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |56      |22      |50      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |204    |110     |22      |169     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |60      |22      |51      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2924   |613     |39      |2835    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |65     |41      |5       |56      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |213    |70      |5       |201     |0       |0       |
|    STADOP_com2                     |STADOP          |548    |98      |0       |537     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |64     |47      |14      |41      |0       |0       |
|    head_com2                       |uniheading      |268    |104     |5       |255     |0       |0       |
|    rmc_com2                        |Gprmc           |40     |40      |0       |37      |0       |0       |
|    uart_com2                       |Agrica          |1427   |198     |10      |1409    |0       |0       |
|  COM3                              |COM3_Control    |272    |139     |19      |234     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |59     |45      |5       |49      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |61     |36      |14      |39      |0       |0       |
|    rmc_com3                        |Gprmc           |152    |58      |0       |146     |0       |0       |
|  DATA                              |Data_Processing |8678   |4476    |1065    |6981    |0       |0       |
|    DIV_Dtemp                       |Divider         |767    |344     |84      |644     |0       |0       |
|    DIV_Utemp                       |Divider         |620    |299     |84      |487     |0       |0       |
|    DIV_accX                        |Divider         |607    |307     |84      |480     |0       |0       |
|    DIV_accY                        |Divider         |641    |336     |114     |467     |0       |0       |
|    DIV_accZ                        |Divider         |645    |405     |132     |434     |0       |0       |
|    DIV_rateX                       |Divider         |702    |366     |132     |494     |0       |0       |
|    DIV_rateY                       |Divider         |602    |348     |132     |398     |0       |0       |
|    DIV_rateZ                       |Divider         |602    |359     |132     |400     |0       |0       |
|    genclk                          |genclk          |83     |58      |20      |50      |0       |0       |
|  FMC                               |FMC_Ctrl        |465    |412     |43      |352     |0       |0       |
|  IIC                               |I2C_master      |313    |265     |11      |272     |0       |0       |
|  IMU_CTRL                          |SCHA634         |898    |623     |61      |721     |0       |0       |
|    CtrlData                        |CtrlData        |467    |408     |47      |328     |0       |0       |
|      usms                          |Time_1ms        |27     |21      |5       |17      |0       |0       |
|    SPIM                            |SPI_SCHA634     |431    |215     |14      |393     |0       |0       |
|  POWER                             |POWER_EN        |98     |53      |38      |37      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |464    |332     |89      |295     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |464    |332     |89      |295     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |182    |150     |0       |165     |0       |0       |
|        reg_inst                    |register        |180    |148     |0       |163     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |282    |182     |89      |130     |0       |0       |
|        bus_inst                    |bus_top         |78     |50      |28      |29      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |26     |16      |10      |10      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |52     |34      |18      |19      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |121    |91      |29      |73      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13405  
    #2          2       3347   
    #3          3        637   
    #4          4        273   
    #5        5-10       934   
    #6        11-50      429   
    #7       51-100       8    
    #8       101-500      3    
    #9        >500        2    
  Average     2.10             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  1.950088s wall, 3.296875s user + 0.000000s system = 3.296875s CPU (169.1%)

RUN-1004 : used memory is 1045 MB, reserved memory is 1043 MB, peak memory is 1117 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 65973, tnet num: 19101, tinst num: 7899, tnode num: 89477, tedge num: 108632.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.698207s wall, 1.703125s user + 0.000000s system = 1.703125s CPU (100.3%)

RUN-1004 : used memory is 1047 MB, reserved memory is 1044 MB, peak memory is 1117 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19101 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.419524s wall, 1.421875s user + 0.000000s system = 1.421875s CPU (100.2%)

RUN-1004 : used memory is 1051 MB, reserved memory is 1047 MB, peak memory is 1117 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 057bf4ae9dc0d18f7126c20ce7aec8c31cdd0d41110c6b742e6e5620711a0ef6 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 7899
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19103, pip num: 140682
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 277
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3245 valid insts, and 396716 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101000001010010110001101
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.362897s wall, 125.078125s user + 0.218750s system = 125.296875s CPU (1013.5%)

RUN-1004 : used memory is 1174 MB, reserved memory is 1160 MB, peak memory is 1288 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250805_084316.log"

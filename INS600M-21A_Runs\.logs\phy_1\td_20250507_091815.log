============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Wed May  7 09:18:15 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(247)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(252)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(273)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(278)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(421)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(428)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(435)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(442)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(449)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(456)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(463)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(470)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(647)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(652)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(680)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(685)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(919)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(926)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(933)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(940)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(947)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(952)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(959)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(966)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(973)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(980)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(102)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 102 in ../../Src/INS600M-21A.v(106)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(516)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
RUN-1001 : Project manager successfully analyzed 21 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.101037s wall, 1.312500s user + 3.796875s system = 5.109375s CPU (100.2%)

RUN-1004 : used memory is 77 MB, reserved memory is 39 MB, peak memory is 89 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.667240s wall, 1.578125s user + 0.093750s system = 1.671875s CPU (100.3%)

RUN-1004 : used memory is 297 MB, reserved memory is 267 MB, peak memory is 300 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 7 view nodes, 47 trigger nets, 47 data nets.
KIT-1004 : Chipwatcher code = 0001111110010110
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=144) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=144) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=144)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=144)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22880/31 useful/useless nets, 19664/17 useful/useless insts
SYN-1016 : Merged 36 instances.
SYN-1032 : 22505/22 useful/useless nets, 20124/18 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 4 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 4 mux instances.
SYN-1015 : Optimize round 1, 443 better
SYN-1014 : Optimize round 2
SYN-1032 : 22133/60 useful/useless nets, 19752/64 useful/useless insts
SYN-1015 : Optimize round 2, 128 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.222296s wall, 2.187500s user + 0.031250s system = 2.218750s CPU (99.8%)

RUN-1004 : used memory is 325 MB, reserved memory is 293 MB, peak memory is 327 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 67 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22193/367 useful/useless nets, 19853/52 useful/useless insts
SYN-1016 : Merged 57 instances.
SYN-2571 : Optimize after map_dsp, round 1, 476 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 44 instances.
SYN-2501 : Optimize round 1, 90 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 18 macro adder
SYN-1019 : Optimized 17 mux instances.
SYN-1016 : Merged 12 instances.
SYN-1032 : 22664/5 useful/useless nets, 20324/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 83242, tnet num: 22664, tinst num: 20323, tnode num: 116864, tedge num: 129642.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.117821s wall, 1.093750s user + 0.031250s system = 1.125000s CPU (100.6%)

RUN-1004 : used memory is 466 MB, reserved memory is 435 MB, peak memory is 466 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22664 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 257 (3.41), #lev = 7 (1.78)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 257 (3.41), #lev = 7 (1.78)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 596 instances into 257 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 450 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 140 adder to BLE ...
SYN-4008 : Packed 140 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.293950s wall, 4.234375s user + 0.062500s system = 4.296875s CPU (100.1%)

RUN-1004 : used memory is 352 MB, reserved memory is 323 MB, peak memory is 576 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  6.820165s wall, 6.703125s user + 0.125000s system = 6.828125s CPU (100.1%)

RUN-1004 : used memory is 352 MB, reserved memory is 323 MB, peak memory is 576 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/rmc_com2/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/rmc_com2/GPRMC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/rmc_com2/GPRMC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/rmc_com2/GPRMC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/rmc_com2/GPRMC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/rmc_com2/GPRMC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/rmc_com2/GPRMC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/rmc_com2/GPRMC_data[0]
SYN-5055 WARNING: The kept net COM2/rx_err will be merged to another kept net COM2/rmc_com2/GPRMC_err
SYN-5055 WARNING: The kept net FMC/Error_state[0] will be merged to another kept net COM2/rmc_com2/GPRMC_sub_state_cnt0[3]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (306 clock/control pins, 0 other pins).
SYN-4027 : Net DATA/DIV_Dtemp/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net DATA/DIV_Dtemp/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19651 instances
RUN-0007 : 5646 luts, 12473 seqs, 933 mslices, 491 lslices, 61 pads, 42 brams, 0 dsps
RUN-1001 : There are total 22015 nets
RUN-1001 : 16520 nets have 2 pins
RUN-1001 : 4356 nets have [3 - 5] pins
RUN-1001 : 785 nets have [6 - 10] pins
RUN-1001 : 224 nets have [11 - 20] pins
RUN-1001 : 106 nets have [21 - 99] pins
RUN-1001 : 24 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4744     
RUN-1001 :   No   |  No   |  Yes  |     628     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    6531     
RUN-1001 :   Yes  |  No   |  Yes  |     470     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19649 instances, 5646 luts, 12473 seqs, 1424 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1131 pins
PHY-3001 : Huge net DATA/done_div with 1650 pins
PHY-0007 : Cell area utilization is 64%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 81773, tnet num: 22013, tinst num: 19649, tnode num: 115493, tedge num: 128453.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.118539s wall, 1.109375s user + 0.015625s system = 1.125000s CPU (100.6%)

RUN-1004 : used memory is 529 MB, reserved memory is 503 MB, peak memory is 576 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22013 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.900739s wall, 1.875000s user + 0.031250s system = 1.906250s CPU (100.3%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.39864e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19649.
PHY-3001 : Level 1 #clusters 2113.
PHY-3001 : End clustering;  0.137618s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (136.2%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 64%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 877381, overlap = 647
PHY-3002 : Step(2): len = 799907, overlap = 695.812
PHY-3002 : Step(3): len = 535099, overlap = 905.219
PHY-3002 : Step(4): len = 465625, overlap = 961.406
PHY-3002 : Step(5): len = 369708, overlap = 1045.19
PHY-3002 : Step(6): len = 325622, overlap = 1090.22
PHY-3002 : Step(7): len = 271644, overlap = 1157.84
PHY-3002 : Step(8): len = 246550, overlap = 1227.41
PHY-3002 : Step(9): len = 213053, overlap = 1267.31
PHY-3002 : Step(10): len = 199372, overlap = 1310.91
PHY-3002 : Step(11): len = 181730, overlap = 1352.66
PHY-3002 : Step(12): len = 165915, overlap = 1399.53
PHY-3002 : Step(13): len = 149402, overlap = 1429.06
PHY-3002 : Step(14): len = 137336, overlap = 1471.38
PHY-3002 : Step(15): len = 129883, overlap = 1478.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.19264e-06
PHY-3002 : Step(16): len = 138376, overlap = 1451.31
PHY-3002 : Step(17): len = 188953, overlap = 1314.12
PHY-3002 : Step(18): len = 193914, overlap = 1186.66
PHY-3002 : Step(19): len = 195466, overlap = 1123.38
PHY-3002 : Step(20): len = 192182, overlap = 1082
PHY-3002 : Step(21): len = 188341, overlap = 1045.62
PHY-3002 : Step(22): len = 185348, overlap = 1034.22
PHY-3002 : Step(23): len = 182203, overlap = 1043
PHY-3002 : Step(24): len = 177304, overlap = 1037.06
PHY-3002 : Step(25): len = 175271, overlap = 1035.19
PHY-3002 : Step(26): len = 172235, overlap = 1027.25
PHY-3002 : Step(27): len = 170627, overlap = 1053.31
PHY-3002 : Step(28): len = 168632, overlap = 1044.47
PHY-3002 : Step(29): len = 167014, overlap = 1051.59
PHY-3002 : Step(30): len = 165794, overlap = 1038.19
PHY-3002 : Step(31): len = 165972, overlap = 1039.22
PHY-3002 : Step(32): len = 164800, overlap = 1012.06
PHY-3002 : Step(33): len = 164401, overlap = 990.5
PHY-3002 : Step(34): len = 162684, overlap = 990.594
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.38527e-06
PHY-3002 : Step(35): len = 168498, overlap = 986.156
PHY-3002 : Step(36): len = 181132, overlap = 900.375
PHY-3002 : Step(37): len = 183315, overlap = 892.125
PHY-3002 : Step(38): len = 185244, overlap = 879.094
PHY-3002 : Step(39): len = 185340, overlap = 884.688
PHY-3002 : Step(40): len = 185531, overlap = 869.688
PHY-3002 : Step(41): len = 183495, overlap = 860.281
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.77054e-06
PHY-3002 : Step(42): len = 193775, overlap = 847.688
PHY-3002 : Step(43): len = 206296, overlap = 831.094
PHY-3002 : Step(44): len = 209379, overlap = 781.438
PHY-3002 : Step(45): len = 210695, overlap = 758.5
PHY-3002 : Step(46): len = 210312, overlap = 732.781
PHY-3002 : Step(47): len = 209367, overlap = 717.719
PHY-3002 : Step(48): len = 208756, overlap = 716.438
PHY-3002 : Step(49): len = 207247, overlap = 720.531
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 9.54108e-06
PHY-3002 : Step(50): len = 217932, overlap = 692.812
PHY-3002 : Step(51): len = 231630, overlap = 644.281
PHY-3002 : Step(52): len = 239436, overlap = 614.781
PHY-3002 : Step(53): len = 242482, overlap = 594.062
PHY-3002 : Step(54): len = 240876, overlap = 575.75
PHY-3002 : Step(55): len = 239051, overlap = 574.562
PHY-3002 : Step(56): len = 236851, overlap = 549.25
PHY-3002 : Step(57): len = 236127, overlap = 539.562
PHY-3002 : Step(58): len = 235553, overlap = 532.594
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.90822e-05
PHY-3002 : Step(59): len = 245521, overlap = 523.531
PHY-3002 : Step(60): len = 257986, overlap = 504.25
PHY-3002 : Step(61): len = 262671, overlap = 481.125
PHY-3002 : Step(62): len = 265304, overlap = 485.375
PHY-3002 : Step(63): len = 264194, overlap = 467.469
PHY-3002 : Step(64): len = 262772, overlap = 468.938
PHY-3002 : Step(65): len = 260836, overlap = 477.5
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.81643e-05
PHY-3002 : Step(66): len = 269404, overlap = 452.156
PHY-3002 : Step(67): len = 278424, overlap = 436.781
PHY-3002 : Step(68): len = 281888, overlap = 409
PHY-3002 : Step(69): len = 283851, overlap = 394.438
PHY-3002 : Step(70): len = 282666, overlap = 400.875
PHY-3002 : Step(71): len = 281536, overlap = 391.312
PHY-3002 : Step(72): len = 280324, overlap = 406.188
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 7.63287e-05
PHY-3002 : Step(73): len = 287653, overlap = 390.344
PHY-3002 : Step(74): len = 296009, overlap = 362.781
PHY-3002 : Step(75): len = 299392, overlap = 334
PHY-3002 : Step(76): len = 300617, overlap = 350.406
PHY-3002 : Step(77): len = 298950, overlap = 365.625
PHY-3002 : Step(78): len = 297726, overlap = 369.875
PHY-3002 : Step(79): len = 296402, overlap = 359.125
PHY-3002 : Step(80): len = 295844, overlap = 365.719
PHY-3002 : Step(81): len = 294881, overlap = 363.969
PHY-3002 : Step(82): len = 294638, overlap = 376.75
PHY-3002 : Step(83): len = 293537, overlap = 374.344
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000152657
PHY-3002 : Step(84): len = 297330, overlap = 364.5
PHY-3002 : Step(85): len = 301854, overlap = 345.094
PHY-3002 : Step(86): len = 303958, overlap = 323.406
PHY-3002 : Step(87): len = 305481, overlap = 318.938
PHY-3002 : Step(88): len = 306079, overlap = 316.969
PHY-3002 : Step(89): len = 307192, overlap = 293.781
PHY-3002 : Step(90): len = 305799, overlap = 284.344
PHY-3002 : Step(91): len = 306534, overlap = 284.875
PHY-3002 : Step(92): len = 306810, overlap = 286.688
PHY-3002 : Step(93): len = 308334, overlap = 280.094
PHY-3002 : Step(94): len = 306693, overlap = 284.562
PHY-3002 : Step(95): len = 305593, overlap = 274.719
PHY-3002 : Step(96): len = 305932, overlap = 279.469
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000288147
PHY-3002 : Step(97): len = 307851, overlap = 284.844
PHY-3002 : Step(98): len = 311416, overlap = 279.344
PHY-3002 : Step(99): len = 312881, overlap = 283.344
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000484814
PHY-3002 : Step(100): len = 313675, overlap = 265.094
PHY-3002 : Step(101): len = 315452, overlap = 248.719
PHY-3002 : Step(102): len = 315665, overlap = 254.312
PHY-3002 : Step(103): len = 318038, overlap = 266.531
PHY-3002 : Step(104): len = 318919, overlap = 269.25
PHY-3002 : Step(105): len = 319382, overlap = 277.281
PHY-3002 : Step(106): len = 319381, overlap = 274.281
PHY-3002 : Step(107): len = 318354, overlap = 260.281
PHY-3002 : Step(108): len = 318356, overlap = 268.375
PHY-3002 : Step(109): len = 318352, overlap = 257.719
PHY-3002 : Step(110): len = 318489, overlap = 259.938
PHY-3002 : Step(111): len = 318161, overlap = 273.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.012187s wall, 0.000000s user + 0.015625s system = 0.015625s CPU (128.2%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/22015.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 431320, over cnt = 1170(3%), over = 5531, worst = 48
PHY-1001 : End global iterations;  0.784036s wall, 0.984375s user + 0.046875s system = 1.031250s CPU (131.5%)

PHY-1001 : Congestion index: top1 = 76.55, top5 = 52.84, top10 = 43.25, top15 = 37.67.
PHY-3001 : End congestion estimation;  1.055601s wall, 1.250000s user + 0.046875s system = 1.296875s CPU (122.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22013 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.833733s wall, 0.812500s user + 0.015625s system = 0.828125s CPU (99.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.09605e-05
PHY-3002 : Step(112): len = 362676, overlap = 185.844
PHY-3002 : Step(113): len = 374705, overlap = 171.875
PHY-3002 : Step(114): len = 377520, overlap = 168.219
PHY-3002 : Step(115): len = 377079, overlap = 161.312
PHY-3002 : Step(116): len = 382121, overlap = 157.406
PHY-3002 : Step(117): len = 387439, overlap = 159.375
PHY-3002 : Step(118): len = 390040, overlap = 149.781
PHY-3002 : Step(119): len = 393512, overlap = 150.469
PHY-3002 : Step(120): len = 397758, overlap = 150.344
PHY-3002 : Step(121): len = 399534, overlap = 144.031
PHY-3002 : Step(122): len = 400669, overlap = 145.406
PHY-3002 : Step(123): len = 401013, overlap = 152.5
PHY-3002 : Step(124): len = 403191, overlap = 150.406
PHY-3002 : Step(125): len = 404734, overlap = 146.062
PHY-3002 : Step(126): len = 405661, overlap = 144
PHY-3002 : Step(127): len = 408648, overlap = 137.625
PHY-3002 : Step(128): len = 408204, overlap = 136.156
PHY-3002 : Step(129): len = 406919, overlap = 132.812
PHY-3002 : Step(130): len = 407275, overlap = 135.25
PHY-3002 : Step(131): len = 407144, overlap = 135.094
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000181921
PHY-3002 : Step(132): len = 408163, overlap = 121.188
PHY-3002 : Step(133): len = 410862, overlap = 118.438
PHY-3002 : Step(134): len = 414057, overlap = 111.625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000363842
PHY-3002 : Step(135): len = 420664, overlap = 109.594
PHY-3002 : Step(136): len = 428544, overlap = 107.781
PHY-3002 : Step(137): len = 431799, overlap = 103.062
PHY-3002 : Step(138): len = 436077, overlap = 100.125
PHY-3002 : Step(139): len = 438338, overlap = 99.0312
PHY-3002 : Step(140): len = 438081, overlap = 100.281
PHY-3002 : Step(141): len = 438301, overlap = 101.938
PHY-3002 : Step(142): len = 438487, overlap = 104.812
PHY-3002 : Step(143): len = 437375, overlap = 114.844
PHY-3002 : Step(144): len = 437971, overlap = 118.531
PHY-3002 : Step(145): len = 439439, overlap = 120.719
PHY-3002 : Step(146): len = 440468, overlap = 125.969
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000727684
PHY-3002 : Step(147): len = 440846, overlap = 128.688
PHY-3002 : Step(148): len = 443916, overlap = 130.656
PHY-3002 : Step(149): len = 446738, overlap = 124.656
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.0012522
PHY-3002 : Step(150): len = 446455, overlap = 126.219
PHY-3002 : Step(151): len = 452541, overlap = 126.25
PHY-3002 : Step(152): len = 463753, overlap = 113.062
PHY-3002 : Step(153): len = 472725, overlap = 122.156
PHY-3002 : Step(154): len = 475973, overlap = 127.469
PHY-3002 : Step(155): len = 473485, overlap = 128.906
PHY-3002 : Step(156): len = 470715, overlap = 129.125
PHY-3002 : Step(157): len = 468575, overlap = 132.25
PHY-3002 : Step(158): len = 466819, overlap = 131.156
PHY-3002 : Step(159): len = 465207, overlap = 129.562
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 52/22015.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 534280, over cnt = 2134(6%), over = 10846, worst = 58
PHY-1001 : End global iterations;  0.930554s wall, 1.546875s user + 0.000000s system = 1.546875s CPU (166.2%)

PHY-1001 : Congestion index: top1 = 91.01, top5 = 65.03, top10 = 54.19, top15 = 48.16.
PHY-3001 : End congestion estimation;  1.196251s wall, 1.828125s user + 0.000000s system = 1.828125s CPU (152.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22013 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.848438s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (99.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.72591e-05
PHY-3002 : Step(160): len = 470401, overlap = 398.25
PHY-3002 : Step(161): len = 473633, overlap = 328.969
PHY-3002 : Step(162): len = 471001, overlap = 272
PHY-3002 : Step(163): len = 464214, overlap = 257.188
PHY-3002 : Step(164): len = 459164, overlap = 249.594
PHY-3002 : Step(165): len = 459137, overlap = 250.844
PHY-3002 : Step(166): len = 454182, overlap = 257.375
PHY-3002 : Step(167): len = 451790, overlap = 253.438
PHY-3002 : Step(168): len = 450103, overlap = 250.656
PHY-3002 : Step(169): len = 447651, overlap = 249.031
PHY-3002 : Step(170): len = 447597, overlap = 253.156
PHY-3002 : Step(171): len = 446095, overlap = 246.844
PHY-3002 : Step(172): len = 444708, overlap = 243.469
PHY-3002 : Step(173): len = 443574, overlap = 239.781
PHY-3002 : Step(174): len = 443879, overlap = 241.094
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000194518
PHY-3002 : Step(175): len = 443714, overlap = 235.531
PHY-3002 : Step(176): len = 444795, overlap = 228
PHY-3002 : Step(177): len = 445021, overlap = 221.406
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000389036
PHY-3002 : Step(178): len = 447963, overlap = 204.312
PHY-3002 : Step(179): len = 454001, overlap = 191.312
PHY-3002 : Step(180): len = 457597, overlap = 178.219
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000778073
PHY-3002 : Step(181): len = 458791, overlap = 174.75
PHY-3002 : Step(182): len = 462508, overlap = 162.781
PHY-3002 : Step(183): len = 467115, overlap = 162.844
PHY-3002 : Step(184): len = 467822, overlap = 157.594
PHY-3002 : Step(185): len = 467019, overlap = 154.719
PHY-3002 : Step(186): len = 467120, overlap = 152.188
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00150598
PHY-3002 : Step(187): len = 467962, overlap = 146.75
PHY-3002 : Step(188): len = 470831, overlap = 149.344
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 81773, tnet num: 22013, tinst num: 19649, tnode num: 115493, tedge num: 128453.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.391086s wall, 1.375000s user + 0.015625s system = 1.390625s CPU (100.0%)

RUN-1004 : used memory is 567 MB, reserved memory is 543 MB, peak memory is 703 MB
OPT-1001 : Total overflow 508.28 peak overflow 4.06
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 347/22015.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 558120, over cnt = 2556(7%), over = 9002, worst = 24
PHY-1001 : End global iterations;  1.140861s wall, 2.015625s user + 0.046875s system = 2.062500s CPU (180.8%)

PHY-1001 : Congestion index: top1 = 57.35, top5 = 48.15, top10 = 43.51, top15 = 40.30.
PHY-1001 : End incremental global routing;  1.349062s wall, 2.234375s user + 0.046875s system = 2.281250s CPU (169.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22013 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.873995s wall, 0.843750s user + 0.031250s system = 0.875000s CPU (100.1%)

OPT-1001 : 17 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 61 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19568 has valid locations, 272 needs to be replaced
PHY-3001 : design contains 19904 instances, 5764 luts, 12610 seqs, 1424 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 490970
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17565/22270.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 577824, over cnt = 2611(7%), over = 9132, worst = 24
PHY-1001 : End global iterations;  0.184587s wall, 0.234375s user + 0.046875s system = 0.281250s CPU (152.4%)

PHY-1001 : Congestion index: top1 = 57.44, top5 = 48.69, top10 = 43.98, top15 = 40.85.
PHY-3001 : End congestion estimation;  0.400189s wall, 0.453125s user + 0.046875s system = 0.500000s CPU (124.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 82623, tnet num: 22268, tinst num: 19904, tnode num: 116668, tedge num: 129643.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.437512s wall, 1.406250s user + 0.031250s system = 1.437500s CPU (100.0%)

RUN-1004 : used memory is 612 MB, reserved memory is 602 MB, peak memory is 705 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22268 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.343515s wall, 2.312500s user + 0.031250s system = 2.343750s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(189): len = 490947, overlap = 3.375
PHY-3002 : Step(190): len = 491763, overlap = 3.4375
PHY-3002 : Step(191): len = 492417, overlap = 3.4375
PHY-3002 : Step(192): len = 492977, overlap = 3.4375
PHY-3002 : Step(193): len = 493815, overlap = 3.375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17598/22270.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 576400, over cnt = 2623(7%), over = 9205, worst = 24
PHY-1001 : End global iterations;  0.177375s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (105.7%)

PHY-1001 : Congestion index: top1 = 57.72, top5 = 48.86, top10 = 44.22, top15 = 41.08.
PHY-3001 : End congestion estimation;  0.392899s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (103.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22268 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.878775s wall, 0.890625s user + 0.000000s system = 0.890625s CPU (101.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000743838
PHY-3002 : Step(194): len = 493872, overlap = 152.281
PHY-3002 : Step(195): len = 494149, overlap = 152.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00148768
PHY-3002 : Step(196): len = 494420, overlap = 152.656
PHY-3002 : Step(197): len = 494677, overlap = 152.625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00284248
PHY-3002 : Step(198): len = 494718, overlap = 152.625
PHY-3002 : Step(199): len = 494961, overlap = 152.906
PHY-3001 : Final: Len = 494961, Over = 152.906
PHY-3001 : End incremental placement;  5.022162s wall, 5.125000s user + 0.187500s system = 5.312500s CPU (105.8%)

OPT-1001 : Total overflow 514.34 peak overflow 4.06
OPT-1001 : End high-fanout net optimization;  7.720809s wall, 8.828125s user + 0.281250s system = 9.109375s CPU (118.0%)

OPT-1001 : Current memory(MB): used = 709, reserve = 690, peak = 725.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17603/22270.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 578456, over cnt = 2571(7%), over = 8606, worst = 24
PHY-1002 : len = 626592, over cnt = 1741(4%), over = 4186, worst = 24
PHY-1002 : len = 664384, over cnt = 674(1%), over = 1386, worst = 13
PHY-1002 : len = 685288, over cnt = 85(0%), over = 111, worst = 6
PHY-1002 : len = 687832, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.240984s wall, 1.906250s user + 0.000000s system = 1.906250s CPU (153.6%)

PHY-1001 : Congestion index: top1 = 48.79, top5 = 43.84, top10 = 40.80, top15 = 38.80.
OPT-1001 : End congestion update;  1.461621s wall, 2.125000s user + 0.000000s system = 2.125000s CPU (145.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22268 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.776951s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (100.6%)

OPT-0007 : Start: WNS 4069 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.243946s wall, 2.906250s user + 0.000000s system = 2.906250s CPU (129.5%)

OPT-1001 : Current memory(MB): used = 704, reserve = 684, peak = 725.
OPT-1001 : End physical optimization;  11.652848s wall, 13.578125s user + 0.296875s system = 13.875000s CPU (119.1%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5764 LUT to BLE ...
SYN-4008 : Packed 5764 LUT and 2862 SEQ to BLE.
SYN-4003 : Packing 9748 remaining SEQ's ...
SYN-4005 : Packed 3345 SEQ with LUT/SLICE
SYN-4006 : 80 single LUT's are left
SYN-4006 : 6403 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 12167/13819 primitive instances ...
PHY-3001 : End packing;  2.666826s wall, 2.671875s user + 0.000000s system = 2.671875s CPU (100.2%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8255 instances
RUN-1001 : 4073 mslices, 4074 lslices, 61 pads, 42 brams, 0 dsps
RUN-1001 : There are total 19450 nets
RUN-1001 : 13605 nets have 2 pins
RUN-1001 : 4450 nets have [3 - 5] pins
RUN-1001 : 872 nets have [6 - 10] pins
RUN-1001 : 363 nets have [11 - 20] pins
RUN-1001 : 150 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
PHY-3001 : design contains 8253 instances, 8147 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Cell area utilization is 86%
PHY-3001 : After packing: Len = 512985, Over = 372
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7892/19450.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 651736, over cnt = 1668(4%), over = 2729, worst = 9
PHY-1002 : len = 658584, over cnt = 1116(3%), over = 1595, worst = 7
PHY-1002 : len = 674440, over cnt = 341(0%), over = 435, worst = 5
PHY-1002 : len = 678896, over cnt = 144(0%), over = 189, worst = 5
PHY-1002 : len = 682608, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.153859s wall, 1.734375s user + 0.015625s system = 1.750000s CPU (151.7%)

PHY-1001 : Congestion index: top1 = 50.06, top5 = 44.03, top10 = 40.65, top15 = 38.58.
PHY-3001 : End congestion estimation;  1.437660s wall, 2.015625s user + 0.015625s system = 2.031250s CPU (141.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68224, tnet num: 19448, tinst num: 8253, tnode num: 93064, tedge num: 112282.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.596721s wall, 1.500000s user + 0.093750s system = 1.593750s CPU (99.8%)

RUN-1004 : used memory is 599 MB, reserved memory is 579 MB, peak memory is 725 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19448 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.412434s wall, 2.296875s user + 0.109375s system = 2.406250s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.32391e-05
PHY-3002 : Step(200): len = 514501, overlap = 349.75
PHY-3002 : Step(201): len = 511159, overlap = 360.75
PHY-3002 : Step(202): len = 509372, overlap = 370
PHY-3002 : Step(203): len = 509960, overlap = 378.25
PHY-3002 : Step(204): len = 509848, overlap = 393.25
PHY-3002 : Step(205): len = 507817, overlap = 399
PHY-3002 : Step(206): len = 506340, overlap = 409.25
PHY-3002 : Step(207): len = 503989, overlap = 415.25
PHY-3002 : Step(208): len = 501759, overlap = 423.5
PHY-3002 : Step(209): len = 499799, overlap = 423.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000106478
PHY-3002 : Step(210): len = 503831, overlap = 413.75
PHY-3002 : Step(211): len = 508047, overlap = 403.75
PHY-3002 : Step(212): len = 509002, overlap = 399.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000212957
PHY-3002 : Step(213): len = 514099, overlap = 391.5
PHY-3002 : Step(214): len = 520470, overlap = 384.75
PHY-3002 : Step(215): len = 520307, overlap = 378.25
PHY-3002 : Step(216): len = 520874, overlap = 371
PHY-3002 : Step(217): len = 522131, overlap = 367.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.601112s wall, 0.734375s user + 0.765625s system = 1.500000s CPU (249.5%)

PHY-3001 : Trial Legalized: Len = 636960
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 543/19450.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 730800, over cnt = 2434(6%), over = 3951, worst = 8
PHY-1002 : len = 745608, over cnt = 1470(4%), over = 2086, worst = 6
PHY-1002 : len = 763944, over cnt = 582(1%), over = 772, worst = 6
PHY-1002 : len = 770208, over cnt = 298(0%), over = 395, worst = 6
PHY-1002 : len = 776760, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.807822s wall, 2.984375s user + 0.015625s system = 3.000000s CPU (165.9%)

PHY-1001 : Congestion index: top1 = 49.29, top5 = 45.03, top10 = 42.57, top15 = 40.85.
PHY-3001 : End congestion estimation;  2.129234s wall, 3.312500s user + 0.015625s system = 3.328125s CPU (156.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19448 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.810450s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000186991
PHY-3002 : Step(218): len = 593122, overlap = 82.25
PHY-3002 : Step(219): len = 573644, overlap = 123.75
PHY-3002 : Step(220): len = 560897, overlap = 169
PHY-3002 : Step(221): len = 552411, overlap = 211
PHY-3002 : Step(222): len = 547604, overlap = 241.75
PHY-3002 : Step(223): len = 544518, overlap = 257.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000373983
PHY-3002 : Step(224): len = 548514, overlap = 254.5
PHY-3002 : Step(225): len = 551965, overlap = 252.25
PHY-3002 : Step(226): len = 551248, overlap = 252
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(227): len = 554478, overlap = 248.75
PHY-3002 : Step(228): len = 561956, overlap = 244.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.032310s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (96.7%)

PHY-3001 : Legalized: Len = 601500, Over = 0
PHY-3001 : Spreading special nets. 39 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.073324s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (106.5%)

PHY-3001 : 54 instances has been re-located, deltaX = 9, deltaY = 38, maxDist = 2.
PHY-3001 : Final: Len = 602446, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68224, tnet num: 19448, tinst num: 8253, tnode num: 93064, tedge num: 112282.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.831490s wall, 1.781250s user + 0.046875s system = 1.828125s CPU (99.8%)

RUN-1004 : used memory is 607 MB, reserved memory is 604 MB, peak memory is 725 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 4252/19450.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 708880, over cnt = 2194(6%), over = 3512, worst = 7
PHY-1002 : len = 722536, over cnt = 1227(3%), over = 1700, worst = 7
PHY-1002 : len = 738744, over cnt = 409(1%), over = 499, worst = 4
PHY-1002 : len = 741824, over cnt = 245(0%), over = 304, worst = 4
PHY-1002 : len = 746368, over cnt = 53(0%), over = 71, worst = 3
PHY-1001 : End global iterations;  1.590973s wall, 2.531250s user + 0.015625s system = 2.546875s CPU (160.1%)

PHY-1001 : Congestion index: top1 = 47.93, top5 = 43.42, top10 = 40.81, top15 = 39.13.
PHY-1001 : End incremental global routing;  1.869072s wall, 2.812500s user + 0.015625s system = 2.828125s CPU (151.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19448 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.814272s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (99.8%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 61 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8187 has valid locations, 13 needs to be replaced
PHY-3001 : design contains 8264 instances, 8158 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 604801
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17566/19459.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 748864, over cnt = 79(0%), over = 99, worst = 3
PHY-1002 : len = 749072, over cnt = 33(0%), over = 38, worst = 3
PHY-1002 : len = 749576, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 749608, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 749608, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.662261s wall, 0.718750s user + 0.015625s system = 0.734375s CPU (110.9%)

PHY-1001 : Congestion index: top1 = 47.91, top5 = 43.44, top10 = 40.82, top15 = 39.16.
PHY-3001 : End congestion estimation;  0.926867s wall, 0.984375s user + 0.015625s system = 1.000000s CPU (107.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68304, tnet num: 19457, tinst num: 8264, tnode num: 93158, tedge num: 112380.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.814849s wall, 1.796875s user + 0.015625s system = 1.812500s CPU (99.9%)

RUN-1004 : used memory is 642 MB, reserved memory is 628 MB, peak memory is 725 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19457 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.642955s wall, 2.625000s user + 0.015625s system = 2.640625s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(229): len = 604601, overlap = 0.25
PHY-3002 : Step(230): len = 604403, overlap = 0
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17559/19459.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 748472, over cnt = 27(0%), over = 31, worst = 3
PHY-1002 : len = 748480, over cnt = 17(0%), over = 17, worst = 1
PHY-1002 : len = 748584, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 748696, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 748728, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.639654s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (107.5%)

PHY-1001 : Congestion index: top1 = 47.89, top5 = 43.41, top10 = 40.82, top15 = 39.16.
PHY-3001 : End congestion estimation;  0.902324s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (105.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19457 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.782237s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00112895
PHY-3002 : Step(231): len = 604434, overlap = 2.25
PHY-3002 : Step(232): len = 604411, overlap = 1.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005249s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 604506, Over = 0
PHY-3001 : End spreading;  0.061147s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.2%)

PHY-3001 : Final: Len = 604506, Over = 0
PHY-3001 : End incremental placement;  5.800065s wall, 5.859375s user + 0.046875s system = 5.906250s CPU (101.8%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  8.920787s wall, 9.906250s user + 0.062500s system = 9.968750s CPU (111.7%)

OPT-1001 : Current memory(MB): used = 718, reserve = 703, peak = 725.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17561/19459.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 748952, over cnt = 16(0%), over = 18, worst = 3
PHY-1002 : len = 749000, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 749120, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.374607s wall, 0.406250s user + 0.015625s system = 0.421875s CPU (112.6%)

PHY-1001 : Congestion index: top1 = 47.80, top5 = 43.34, top10 = 40.77, top15 = 39.12.
OPT-1001 : End congestion update;  0.641595s wall, 0.671875s user + 0.015625s system = 0.687500s CPU (107.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19457 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.678032s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (101.4%)

OPT-0007 : Start: WNS 4239 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.323759s wall, 1.359375s user + 0.015625s system = 1.375000s CPU (103.9%)

OPT-1001 : Current memory(MB): used = 718, reserve = 703, peak = 725.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19457 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.678160s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (99.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17577/19459.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 749120, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.110931s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (98.6%)

PHY-1001 : Congestion index: top1 = 47.80, top5 = 43.34, top10 = 40.77, top15 = 39.12.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19457 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.680803s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (98.7%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4239 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 47.482759
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4239ps with logic level 4 
RUN-1001 :       #2 path slack 4282ps with logic level 4 
OPT-1001 : End physical optimization;  14.051683s wall, 15.015625s user + 0.125000s system = 15.140625s CPU (107.7%)

RUN-1003 : finish command "place" in  71.410999s wall, 136.500000s user + 9.156250s system = 145.656250s CPU (204.0%)

RUN-1004 : used memory is 632 MB, reserved memory is 610 MB, peak memory is 725 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.509749s wall, 2.546875s user + 0.015625s system = 2.562500s CPU (169.7%)

RUN-1004 : used memory is 633 MB, reserved memory is 612 MB, peak memory is 725 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8266 instances
RUN-1001 : 4073 mslices, 4085 lslices, 61 pads, 42 brams, 0 dsps
RUN-1001 : There are total 19459 nets
RUN-1001 : 13604 nets have 2 pins
RUN-1001 : 4448 nets have [3 - 5] pins
RUN-1001 : 874 nets have [6 - 10] pins
RUN-1001 : 374 nets have [11 - 20] pins
RUN-1001 : 149 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68304, tnet num: 19457, tinst num: 8264, tnode num: 93158, tedge num: 112380.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.586544s wall, 1.593750s user + 0.000000s system = 1.593750s CPU (100.5%)

RUN-1004 : used memory is 638 MB, reserved memory is 629 MB, peak memory is 725 MB
PHY-1001 : 4073 mslices, 4085 lslices, 61 pads, 42 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19457 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 689160, over cnt = 2338(6%), over = 3914, worst = 9
PHY-1002 : len = 706920, over cnt = 1322(3%), over = 1881, worst = 7
PHY-1002 : len = 723656, over cnt = 484(1%), over = 643, worst = 5
PHY-1002 : len = 733584, over cnt = 30(0%), over = 52, worst = 4
PHY-1002 : len = 734520, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.654812s wall, 2.796875s user + 0.046875s system = 2.843750s CPU (171.8%)

PHY-1001 : Congestion index: top1 = 47.61, top5 = 42.76, top10 = 40.27, top15 = 38.64.
PHY-1001 : End global routing;  1.947945s wall, 3.078125s user + 0.046875s system = 3.125000s CPU (160.4%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 706, reserve = 693, peak = 725.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net DATA/DIV_Dtemp/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 976, reserve = 960, peak = 976.
PHY-1001 : End build detailed router design. 4.176805s wall, 4.125000s user + 0.062500s system = 4.187500s CPU (100.3%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 192672, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.727345s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (98.8%)

PHY-1001 : Current memory(MB): used = 1013, reserve = 998, peak = 1013.
PHY-1001 : End phase 1; 0.734940s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (99.9%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.83134e+06, over cnt = 1459(0%), over = 1461, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1028, reserve = 1013, peak = 1028.
PHY-1001 : End initial routed; 17.891878s wall, 46.781250s user + 0.625000s system = 47.406250s CPU (265.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18254(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.201   |   0.000   |   0   
RUN-1001 :   Hold   |   0.139   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.133523s wall, 3.125000s user + 0.000000s system = 3.125000s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 1040, reserve = 1026, peak = 1040.
PHY-1001 : End phase 2; 21.025554s wall, 49.906250s user + 0.625000s system = 50.531250s CPU (240.3%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.83134e+06, over cnt = 1459(0%), over = 1461, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.214409s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (94.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.81274e+06, over cnt = 427(0%), over = 427, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 1.067890s wall, 1.750000s user + 0.000000s system = 1.750000s CPU (163.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.81091e+06, over cnt = 98(0%), over = 98, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.552584s wall, 0.734375s user + 0.031250s system = 0.765625s CPU (138.6%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.81189e+06, over cnt = 11(0%), over = 11, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.244495s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (121.4%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.81213e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.169448s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (101.4%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.81213e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.175481s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (97.9%)

PHY-1001 : ===== DR Iter 6 =====
PHY-1022 : len = 1.81213e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 6; 0.249896s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (100.0%)

PHY-1001 : ===== DR Iter 7 =====
PHY-1022 : len = 1.81213e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 7; 0.408158s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (99.5%)

PHY-1001 : ===== DR Iter 8 =====
PHY-1022 : len = 1.81213e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 8; 0.153351s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (101.9%)

PHY-1001 : ==== DR Iter 9 ====
PHY-1022 : len = 1.81213e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 9; 0.152464s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (92.2%)

PHY-1001 : ==== DR Iter 10 ====
PHY-1022 : len = 1.81213e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 10; 0.189444s wall, 0.250000s user + 0.031250s system = 0.281250s CPU (148.5%)

PHY-1001 : ==== DR Iter 11 ====
PHY-1022 : len = 1.81213e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 11; 0.177230s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (97.0%)

PHY-1001 : ==== DR Iter 12 ====
PHY-1022 : len = 1.81213e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 12; 0.316794s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (98.6%)

PHY-1001 : ===== DR Iter 13 =====
PHY-1022 : len = 1.81214e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 13; 0.167385s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (102.7%)

PHY-1001 : ==== DR Iter 14 ====
PHY-1022 : len = 1.81216e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 14; 0.134915s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (104.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18254(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.201   |   0.000   |   0   
RUN-1001 :   Hold   |   0.139   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.101589s wall, 3.109375s user + 0.000000s system = 3.109375s CPU (100.3%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 353 feed throughs used by 295 nets
PHY-1001 : End commit to database; 2.159295s wall, 2.125000s user + 0.031250s system = 2.156250s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 1128, reserve = 1117, peak = 1128.
PHY-1001 : End phase 3; 10.134336s wall, 11.046875s user + 0.093750s system = 11.140625s CPU (109.9%)

PHY-1003 : Routed, final wirelength = 1.81216e+06
PHY-1001 : Current memory(MB): used = 1132, reserve = 1121, peak = 1132.
PHY-1001 : End export database. 0.055923s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (111.8%)

PHY-1001 : End detail routing;  36.511916s wall, 66.234375s user + 0.796875s system = 67.031250s CPU (183.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68304, tnet num: 19457, tinst num: 8264, tnode num: 93158, tedge num: 112380.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.749145s wall, 1.750000s user + 0.000000s system = 1.750000s CPU (100.0%)

RUN-1004 : used memory is 1064 MB, reserved memory is 1058 MB, peak memory is 1132 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  44.186884s wall, 75.046875s user + 0.843750s system = 75.890625s CPU (171.7%)

RUN-1004 : used memory is 1063 MB, reserved memory is 1059 MB, peak memory is 1132 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        67
  #input                   26
  #output                  39
  #inout                    2

Utilization Statistics
#lut                     8756   out of  19600   44.67%
#reg                    12710   out of  19600   64.85%
#le                     15125
  #lut only              2415   out of  15125   15.97%
  #reg only              6369   out of  15125   42.11%
  #lut&reg               6341   out of  15125   41.92%
#dsp                        0   out of     29    0.00%
#bram                      42   out of     64   65.62%
  #bram9k                  42
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       61   out of    188   32.45%
  #ireg                     9
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet              Type               DriverType         Driver                    Fanout
#1        DATA/DIV_Dtemp/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6990
#2        config_inst_syn_9     GCLK               config             config_inst.jtck          176
#3        clk_in_dup_1          GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         L4        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          NONE       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D14        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    GNSS_RMC       OUTPUT         L3        LVCMOS33           8            NONE           NONE       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         T6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT        D16        LVCMOS33           8            NONE           NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15125  |7332    |1424    |12752   |42      |0       |
|  AnyFog_dataX                      |AnyFog          |205    |81      |22      |171     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |83     |52      |22      |49      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |206    |80      |22      |171     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |84     |57      |22      |49      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |205    |92      |22      |169     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |83     |58      |22      |47      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |3490   |1029    |34      |3394    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |739    |113     |5       |724     |0       |0       |
|    STADOP_com2                     |STADOP          |556    |57      |0       |551     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |63     |42      |14      |38      |0       |0       |
|    head_com2                       |uniheading      |271    |88      |5       |259     |0       |0       |
|    rmc_com2                        |Gprmc           |170    |74      |0       |155     |0       |0       |
|    uart_com2                       |Agrica          |1403   |367     |10      |1379    |0       |0       |
|  DATA                              |Data_Processing |8653   |4360    |1062    |6974    |0       |0       |
|    DIV_Dtemp                       |Divider         |804    |306     |84      |681     |0       |0       |
|    DIV_Utemp                       |Divider         |648    |312     |84      |525     |0       |0       |
|    DIV_accX                        |Divider         |608    |287     |84      |482     |0       |0       |
|    DIV_accY                        |Divider         |640    |314     |111     |472     |0       |0       |
|    DIV_accZ                        |Divider         |669    |383     |132     |465     |0       |0       |
|    DIV_rateX                       |Divider         |661    |411     |132     |451     |0       |0       |
|    DIV_rateY                       |Divider         |619    |420     |132     |411     |0       |0       |
|    DIV_rateZ                       |Divider         |556    |345     |132     |351     |0       |0       |
|    genclk                          |genclk          |79     |47      |20      |46      |0       |0       |
|  FMC                               |FMC_Ctrl        |424    |372     |43      |341     |0       |0       |
|  IIC                               |I2C_master      |264    |210     |11      |239     |0       |0       |
|  IMU_CTRL                          |SCHA634         |888    |654     |61      |722     |0       |0       |
|    CtrlData                        |CtrlData        |467    |419     |47      |333     |0       |0       |
|      usms                          |Time_1ms        |30     |25      |5       |22      |0       |0       |
|    SPIM                            |SPI_SCHA634     |421    |235     |14      |389     |0       |0       |
|  POWER                             |POWER_EN        |98     |52      |38      |39      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |672    |402     |109     |470     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |672    |402     |109     |470     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |308    |172     |0       |291     |0       |0       |
|        reg_inst                    |register        |305    |169     |0       |288     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |364    |230     |109     |179     |0       |0       |
|        bus_inst                    |bus_top         |147    |92      |52      |58      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |25     |15      |10      |9       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |16     |10      |6       |8       |0       |0       |
|          BUS_DETECTOR[5]$bus_nodes |bus_det         |51     |33      |18      |18      |0       |0       |
|          BUS_DETECTOR[6]$bus_nodes |bus_det         |51     |30      |18      |19      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |134    |97      |29      |85      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13542  
    #2          2       3509   
    #3          3        683   
    #4          4        256   
    #5        5-10       919   
    #6        11-50      465   
    #7       51-100      14    
    #8       101-500      3    
    #9        >500        2    
  Average     2.15             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  1.806533s wall, 3.125000s user + 0.000000s system = 3.125000s CPU (173.0%)

RUN-1004 : used memory is 1063 MB, reserved memory is 1059 MB, peak memory is 1132 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68304, tnet num: 19457, tinst num: 8264, tnode num: 93158, tedge num: 112380.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.603371s wall, 1.609375s user + 0.000000s system = 1.609375s CPU (100.4%)

RUN-1004 : used memory is 1065 MB, reserved memory is 1061 MB, peak memory is 1132 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19457 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.241480s wall, 1.218750s user + 0.015625s system = 1.234375s CPU (99.4%)

RUN-1004 : used memory is 1070 MB, reserved memory is 1066 MB, peak memory is 1132 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 8f169df8acfcb835781910b1ac29267cd80e16d8a5e1b8cf9d032ec93c0a2865 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8264
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19459, pip num: 150511
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 353
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3263 valid insts, and 419113 bits set as '1'.
BIT-1004 : the usercode register value: 00000000110101000001111110010110
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  10.785154s wall, 108.687500s user + 0.171875s system = 108.859375s CPU (1009.3%)

RUN-1004 : used memory is 1197 MB, reserved memory is 1182 MB, peak memory is 1312 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250507_091815.log"

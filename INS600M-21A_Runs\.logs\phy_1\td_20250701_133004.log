============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Tue Jul  1 13:30:04 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(519)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
RUN-1001 : Project manager successfully analyzed 22 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.543077s wall, 1.546875s user + 4.000000s system = 5.546875s CPU (100.1%)

RUN-1004 : used memory is 82 MB, reserved memory is 40 MB, peak memory is 94 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.777637s wall, 1.703125s user + 0.078125s system = 1.781250s CPU (100.2%)

RUN-1004 : used memory is 306 MB, reserved memory is 271 MB, peak memory is 309 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 58 trigger nets, 58 data nets.
KIT-1004 : Chipwatcher code = 1110111010000001
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=158) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=158) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=158)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=158)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 23384/23 useful/useless nets, 20088/12 useful/useless insts
SYN-1016 : Merged 28 instances.
SYN-1032 : 22989/20 useful/useless nets, 20595/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 478 better
SYN-1014 : Optimize round 2
SYN-1032 : 22605/45 useful/useless nets, 20211/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.431984s wall, 2.390625s user + 0.046875s system = 2.437500s CPU (100.2%)

RUN-1004 : used memory is 331 MB, reserved memory is 298 MB, peak memory is 333 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22677/441 useful/useless nets, 20334/63 useful/useless insts
SYN-1016 : Merged 69 instances.
SYN-2571 : Optimize after map_dsp, round 1, 573 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 56 instances.
SYN-2501 : Optimize round 1, 114 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 23181/5 useful/useless nets, 20838/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 85647, tnet num: 23181, tinst num: 20837, tnode num: 120331, tedge num: 133394.
TMR-2508 : Levelizing timing graph completed, there are 85 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.188306s wall, 1.125000s user + 0.062500s system = 1.187500s CPU (99.9%)

RUN-1004 : used memory is 478 MB, reserved memory is 446 MB, peak memory is 478 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 23181 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 282 (3.29), #lev = 7 (1.66)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 282 (3.29), #lev = 7 (1.66)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 614 instances into 282 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 487 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 160 adder to BLE ...
SYN-4008 : Packed 160 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.588053s wall, 4.453125s user + 0.125000s system = 4.578125s CPU (99.8%)

RUN-1004 : used memory is 360 MB, reserved memory is 333 MB, peak memory is 590 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.331849s wall, 7.125000s user + 0.203125s system = 7.328125s CPU (99.9%)

RUN-1004 : used memory is 360 MB, reserved memory is 334 MB, peak memory is 590 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[7] will be merged to another kept net COM3/rmc_com3/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[6] will be merged to another kept net COM3/rmc_com3/GPRMC_data[6]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[5] will be merged to another kept net COM3/rmc_com3/GPRMC_data[5]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[4] will be merged to another kept net COM3/rmc_com3/GPRMC_data[4]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[3] will be merged to another kept net COM3/rmc_com3/GPRMC_data[3]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[2] will be merged to another kept net COM3/rmc_com3/GPRMC_data[2]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[1] will be merged to another kept net COM3/rmc_com3/GPRMC_data[1]
SYN-5055 WARNING: The kept net FMC/Error_state[0] will be merged to another kept net COM3/rmc_com3/GPRMC_sub_state_cnt0[3]
SYN-5055 WARNING: The kept net COM3/rmc_com3/GPRMC_sub_state_cnt0[3] will be merged to another kept net COM3/rmc_com3/GPRMC_sub_state_cnt0[2]
SYN-5055 WARNING: The kept net COM3/rmc_com3/GPRMC_sub_state_cnt0[2] will be merged to another kept net COM3/rmc_com3/GPRMC_sub_state_cnt0[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (335 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 20162 instances
RUN-0007 : 5793 luts, 12807 seqs, 951 mslices, 494 lslices, 60 pads, 52 brams, 0 dsps
RUN-1001 : There are total 22529 nets
RUN-1001 : 16836 nets have 2 pins
RUN-1001 : 4511 nets have [3 - 5] pins
RUN-1001 : 804 nets have [6 - 10] pins
RUN-1001 : 250 nets have [11 - 20] pins
RUN-1001 : 104 nets have [21 - 99] pins
RUN-1001 : 24 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4743     
RUN-1001 :   No   |  No   |  Yes  |     670     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    6787     
RUN-1001 :   Yes  |  No   |  Yes  |     507     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  117  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 125
PHY-3001 : Initial placement ...
PHY-3001 : design contains 20160 instances, 5793 luts, 12807 seqs, 1445 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1195 pins
PHY-3001 : Huge net DATA/done_div with 1714 pins
PHY-0007 : Cell area utilization is 65%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 84174, tnet num: 22527, tinst num: 20160, tnode num: 118993, tedge num: 132240.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.166195s wall, 1.156250s user + 0.000000s system = 1.156250s CPU (99.1%)

RUN-1004 : used memory is 539 MB, reserved memory is 512 MB, peak memory is 590 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22527 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.009948s wall, 1.968750s user + 0.031250s system = 2.000000s CPU (99.5%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.50077e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 20160.
PHY-3001 : Level 1 #clusters 2190.
PHY-3001 : End clustering;  0.139656s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (145.4%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 65%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 879074, overlap = 651.438
PHY-3002 : Step(2): len = 794548, overlap = 743.906
PHY-3002 : Step(3): len = 539989, overlap = 896.969
PHY-3002 : Step(4): len = 462875, overlap = 991.344
PHY-3002 : Step(5): len = 373135, overlap = 1108.59
PHY-3002 : Step(6): len = 341483, overlap = 1169.66
PHY-3002 : Step(7): len = 283834, overlap = 1259.56
PHY-3002 : Step(8): len = 252837, overlap = 1295.62
PHY-3002 : Step(9): len = 227837, overlap = 1334.56
PHY-3002 : Step(10): len = 212483, overlap = 1381.59
PHY-3002 : Step(11): len = 188391, overlap = 1424.78
PHY-3002 : Step(12): len = 174045, overlap = 1459.16
PHY-3002 : Step(13): len = 159697, overlap = 1467.03
PHY-3002 : Step(14): len = 148797, overlap = 1471.66
PHY-3002 : Step(15): len = 134638, overlap = 1488.28
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.51421e-07
PHY-3002 : Step(16): len = 140898, overlap = 1483.44
PHY-3002 : Step(17): len = 182085, overlap = 1416.69
PHY-3002 : Step(18): len = 188505, overlap = 1316.34
PHY-3002 : Step(19): len = 187688, overlap = 1262.09
PHY-3002 : Step(20): len = 186237, overlap = 1246.19
PHY-3002 : Step(21): len = 182503, overlap = 1212.66
PHY-3002 : Step(22): len = 176181, overlap = 1192.81
PHY-3002 : Step(23): len = 173132, overlap = 1187.69
PHY-3002 : Step(24): len = 170078, overlap = 1183.53
PHY-3002 : Step(25): len = 167529, overlap = 1179.53
PHY-3002 : Step(26): len = 165738, overlap = 1188
PHY-3002 : Step(27): len = 163515, overlap = 1206.72
PHY-3002 : Step(28): len = 162159, overlap = 1204.53
PHY-3002 : Step(29): len = 161675, overlap = 1191.75
PHY-3002 : Step(30): len = 161255, overlap = 1187.81
PHY-3002 : Step(31): len = 160872, overlap = 1198.56
PHY-3002 : Step(32): len = 159692, overlap = 1203.5
PHY-3002 : Step(33): len = 159083, overlap = 1233.56
PHY-3002 : Step(34): len = 157844, overlap = 1239.81
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 1.70284e-06
PHY-3002 : Step(35): len = 162886, overlap = 1200.38
PHY-3002 : Step(36): len = 175275, overlap = 1158.62
PHY-3002 : Step(37): len = 178032, overlap = 1129.16
PHY-3002 : Step(38): len = 179159, overlap = 1114.75
PHY-3002 : Step(39): len = 179937, overlap = 1105.75
PHY-3002 : Step(40): len = 179907, overlap = 1096.91
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 3.40569e-06
PHY-3002 : Step(41): len = 188344, overlap = 1046.06
PHY-3002 : Step(42): len = 203293, overlap = 945.281
PHY-3002 : Step(43): len = 210181, overlap = 886.844
PHY-3002 : Step(44): len = 212889, overlap = 848.406
PHY-3002 : Step(45): len = 212902, overlap = 818.438
PHY-3002 : Step(46): len = 211869, overlap = 824.281
PHY-3002 : Step(47): len = 210187, overlap = 827.375
PHY-3002 : Step(48): len = 209446, overlap = 830.844
PHY-3002 : Step(49): len = 208253, overlap = 837.312
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 6.81137e-06
PHY-3002 : Step(50): len = 219115, overlap = 826.688
PHY-3002 : Step(51): len = 234144, overlap = 770
PHY-3002 : Step(52): len = 239285, overlap = 748.031
PHY-3002 : Step(53): len = 241134, overlap = 746.406
PHY-3002 : Step(54): len = 242173, overlap = 737.906
PHY-3002 : Step(55): len = 241340, overlap = 718.094
PHY-3002 : Step(56): len = 239018, overlap = 730.719
PHY-3002 : Step(57): len = 238383, overlap = 729.812
PHY-3002 : Step(58): len = 237096, overlap = 732.969
PHY-3002 : Step(59): len = 235596, overlap = 736.062
PHY-3002 : Step(60): len = 234094, overlap = 733.969
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.36227e-05
PHY-3002 : Step(61): len = 244412, overlap = 690.969
PHY-3002 : Step(62): len = 257814, overlap = 636.531
PHY-3002 : Step(63): len = 263143, overlap = 602.219
PHY-3002 : Step(64): len = 265318, overlap = 564.125
PHY-3002 : Step(65): len = 263904, overlap = 560.062
PHY-3002 : Step(66): len = 262627, overlap = 573.75
PHY-3002 : Step(67): len = 260068, overlap = 581.688
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 2.72455e-05
PHY-3002 : Step(68): len = 267983, overlap = 530.781
PHY-3002 : Step(69): len = 278057, overlap = 489.562
PHY-3002 : Step(70): len = 283983, overlap = 441.188
PHY-3002 : Step(71): len = 286243, overlap = 427.031
PHY-3002 : Step(72): len = 285129, overlap = 444.719
PHY-3002 : Step(73): len = 283356, overlap = 455.406
PHY-3002 : Step(74): len = 280792, overlap = 454.875
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 5.4491e-05
PHY-3002 : Step(75): len = 288181, overlap = 431.781
PHY-3002 : Step(76): len = 295980, overlap = 398.812
PHY-3002 : Step(77): len = 298098, overlap = 402.562
PHY-3002 : Step(78): len = 298280, overlap = 386.938
PHY-3002 : Step(79): len = 296644, overlap = 380.906
PHY-3002 : Step(80): len = 294936, overlap = 366.781
PHY-3002 : Step(81): len = 292268, overlap = 365.688
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000106093
PHY-3002 : Step(82): len = 296438, overlap = 352.25
PHY-3002 : Step(83): len = 302956, overlap = 351.406
PHY-3002 : Step(84): len = 305292, overlap = 348.344
PHY-3002 : Step(85): len = 306358, overlap = 350.5
PHY-3002 : Step(86): len = 305682, overlap = 351.438
PHY-3002 : Step(87): len = 304814, overlap = 352.062
PHY-3002 : Step(88): len = 303514, overlap = 366
PHY-3002 : Step(89): len = 304822, overlap = 360.594
PHY-3002 : Step(90): len = 303867, overlap = 364.719
PHY-3002 : Step(91): len = 303749, overlap = 357.688
PHY-3002 : Step(92): len = 302968, overlap = 362.25
PHY-3002 : Step(93): len = 303674, overlap = 359.812
PHY-3002 : Step(94): len = 302591, overlap = 349.938
PHY-3002 : Step(95): len = 302486, overlap = 338.25
PHY-3002 : Step(96): len = 301916, overlap = 338.594
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000209574
PHY-3002 : Step(97): len = 304507, overlap = 317.438
PHY-3002 : Step(98): len = 308280, overlap = 312.531
PHY-3002 : Step(99): len = 309573, overlap = 310.188
PHY-3002 : Step(100): len = 310605, overlap = 301.094
PHY-3002 : Step(101): len = 310044, overlap = 294.844
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000383705
PHY-3002 : Step(102): len = 311208, overlap = 285.75
PHY-3002 : Step(103): len = 314972, overlap = 289.906
PHY-3002 : Step(104): len = 317340, overlap = 283.094
PHY-3002 : Step(105): len = 320266, overlap = 302.344
PHY-3002 : Step(106): len = 320630, overlap = 305.031
PHY-3002 : Step(107): len = 321585, overlap = 324.469
PHY-3002 : Step(108): len = 321659, overlap = 328.656
PHY-3002 : Step(109): len = 320101, overlap = 339.188
PHY-3002 : Step(110): len = 319920, overlap = 346.344
PHY-3002 : Step(111): len = 319669, overlap = 341.531
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.013137s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (118.9%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/22529.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 429872, over cnt = 1285(3%), over = 6192, worst = 37
PHY-1001 : End global iterations;  0.819678s wall, 1.078125s user + 0.093750s system = 1.171875s CPU (143.0%)

PHY-1001 : Congestion index: top1 = 74.50, top5 = 55.58, top10 = 45.08, top15 = 39.09.
PHY-3001 : End congestion estimation;  1.035077s wall, 1.281250s user + 0.109375s system = 1.390625s CPU (134.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22527 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.921470s wall, 0.906250s user + 0.000000s system = 0.906250s CPU (98.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.57449e-05
PHY-3002 : Step(112): len = 362775, overlap = 247.375
PHY-3002 : Step(113): len = 378884, overlap = 225.406
PHY-3002 : Step(114): len = 376827, overlap = 216.719
PHY-3002 : Step(115): len = 378160, overlap = 221.844
PHY-3002 : Step(116): len = 386279, overlap = 215.688
PHY-3002 : Step(117): len = 392219, overlap = 197.031
PHY-3002 : Step(118): len = 398903, overlap = 175.656
PHY-3002 : Step(119): len = 403804, overlap = 160.375
PHY-3002 : Step(120): len = 403568, overlap = 140.906
PHY-3002 : Step(121): len = 405886, overlap = 134.938
PHY-3002 : Step(122): len = 408995, overlap = 130.812
PHY-3002 : Step(123): len = 409254, overlap = 120.625
PHY-3002 : Step(124): len = 410770, overlap = 116
PHY-3002 : Step(125): len = 413684, overlap = 113.312
PHY-3002 : Step(126): len = 414092, overlap = 115.062
PHY-3002 : Step(127): len = 415663, overlap = 118.281
PHY-3002 : Step(128): len = 414971, overlap = 119.188
PHY-3002 : Step(129): len = 414582, overlap = 122.094
PHY-3002 : Step(130): len = 415234, overlap = 119.812
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00017149
PHY-3002 : Step(131): len = 415566, overlap = 118.906
PHY-3002 : Step(132): len = 417096, overlap = 119.812
PHY-3002 : Step(133): len = 419881, overlap = 117.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000326866
PHY-3002 : Step(134): len = 425820, overlap = 127.656
PHY-3002 : Step(135): len = 433785, overlap = 131.219
PHY-3002 : Step(136): len = 435015, overlap = 134.688
PHY-3002 : Step(137): len = 436950, overlap = 135.188
PHY-3002 : Step(138): len = 439130, overlap = 135.375
PHY-3002 : Step(139): len = 438793, overlap = 130.938
PHY-3002 : Step(140): len = 441580, overlap = 126.875
PHY-3002 : Step(141): len = 442265, overlap = 134.688
PHY-3002 : Step(142): len = 442985, overlap = 134.938
PHY-3002 : Step(143): len = 441937, overlap = 136.188
PHY-3002 : Step(144): len = 441713, overlap = 146.469
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000653732
PHY-3002 : Step(145): len = 442239, overlap = 142.938
PHY-3002 : Step(146): len = 446199, overlap = 143.906
PHY-3002 : Step(147): len = 451789, overlap = 144.406
PHY-3002 : Step(148): len = 453203, overlap = 144.906
PHY-3002 : Step(149): len = 457276, overlap = 149.562
PHY-3002 : Step(150): len = 461909, overlap = 156.656
PHY-3002 : Step(151): len = 460446, overlap = 150.844
PHY-3002 : Step(152): len = 459508, overlap = 146.406
PHY-3002 : Step(153): len = 458739, overlap = 137.812
PHY-3002 : Step(154): len = 457318, overlap = 134.625
PHY-3002 : Step(155): len = 455676, overlap = 135.312
PHY-3002 : Step(156): len = 455463, overlap = 133.219
PHY-3002 : Step(157): len = 455378, overlap = 126.5
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(158): len = 455319, overlap = 128.781
PHY-3002 : Step(159): len = 456284, overlap = 128.469
PHY-3002 : Step(160): len = 458886, overlap = 125.688
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 26/22529.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 527088, over cnt = 2197(6%), over = 11935, worst = 41
PHY-1001 : End global iterations;  1.008937s wall, 1.687500s user + 0.015625s system = 1.703125s CPU (168.8%)

PHY-1001 : Congestion index: top1 = 93.53, top5 = 68.29, top10 = 57.42, top15 = 50.59.
PHY-3001 : End congestion estimation;  1.292373s wall, 1.984375s user + 0.015625s system = 2.000000s CPU (154.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22527 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.940690s wall, 0.906250s user + 0.031250s system = 0.937500s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.02452e-05
PHY-3002 : Step(161): len = 467620, overlap = 443.688
PHY-3002 : Step(162): len = 475334, overlap = 393.094
PHY-3002 : Step(163): len = 467295, overlap = 364.875
PHY-3002 : Step(164): len = 460324, overlap = 344.781
PHY-3002 : Step(165): len = 456819, overlap = 333.531
PHY-3002 : Step(166): len = 451126, overlap = 330.719
PHY-3002 : Step(167): len = 448148, overlap = 312.875
PHY-3002 : Step(168): len = 445495, overlap = 293.406
PHY-3002 : Step(169): len = 441632, overlap = 287.781
PHY-3002 : Step(170): len = 440065, overlap = 285.719
PHY-3002 : Step(171): len = 437858, overlap = 281.875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00018049
PHY-3002 : Step(172): len = 438551, overlap = 269.375
PHY-3002 : Step(173): len = 439714, overlap = 258.469
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000335834
PHY-3002 : Step(174): len = 442964, overlap = 251.875
PHY-3002 : Step(175): len = 449394, overlap = 222.781
PHY-3002 : Step(176): len = 450151, overlap = 215.094
PHY-3002 : Step(177): len = 451774, overlap = 205.281
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000671669
PHY-3002 : Step(178): len = 452910, overlap = 201.531
PHY-3002 : Step(179): len = 456352, overlap = 189.844
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 84174, tnet num: 22527, tinst num: 20160, tnode num: 118993, tedge num: 132240.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.502684s wall, 1.484375s user + 0.015625s system = 1.500000s CPU (99.8%)

RUN-1004 : used memory is 579 MB, reserved memory is 554 MB, peak memory is 718 MB
OPT-1001 : Total overflow 586.62 peak overflow 4.09
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 660/22529.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 538272, over cnt = 2574(7%), over = 9761, worst = 27
PHY-1001 : End global iterations;  1.202198s wall, 1.718750s user + 0.015625s system = 1.734375s CPU (144.3%)

PHY-1001 : Congestion index: top1 = 60.24, top5 = 49.59, top10 = 44.29, top15 = 41.06.
PHY-1001 : End incremental global routing;  1.420202s wall, 1.937500s user + 0.015625s system = 1.953125s CPU (137.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22527 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.137764s wall, 1.140625s user + 0.000000s system = 1.140625s CPU (100.3%)

OPT-1001 : 17 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 20080 has valid locations, 278 needs to be replaced
PHY-3001 : design contains 20421 instances, 5907 luts, 12954 seqs, 1445 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 473943
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 72%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17693/22790.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 553128, over cnt = 2595(7%), over = 9764, worst = 27
PHY-1001 : End global iterations;  0.184056s wall, 0.265625s user + 0.046875s system = 0.312500s CPU (169.8%)

PHY-1001 : Congestion index: top1 = 60.06, top5 = 49.57, top10 = 44.40, top15 = 41.22.
PHY-3001 : End congestion estimation;  0.425895s wall, 0.531250s user + 0.046875s system = 0.578125s CPU (135.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 85050, tnet num: 22788, tinst num: 20421, tnode num: 120224, tedge num: 133470.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.524255s wall, 1.484375s user + 0.031250s system = 1.515625s CPU (99.4%)

RUN-1004 : used memory is 625 MB, reserved memory is 613 MB, peak memory is 718 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22788 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.532939s wall, 2.484375s user + 0.046875s system = 2.531250s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(180): len = 473417, overlap = 6.1875
PHY-3002 : Step(181): len = 474420, overlap = 6.125
PHY-3002 : Step(182): len = 475439, overlap = 6
PHY-3002 : Step(183): len = 475883, overlap = 6.125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(184): len = 476111, overlap = 6
PHY-3002 : Step(185): len = 476709, overlap = 6
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(186): len = 476877, overlap = 5.9375
PHY-3002 : Step(187): len = 477477, overlap = 5.9375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 72%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17709/22790.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 552960, over cnt = 2619(7%), over = 9878, worst = 27
PHY-1001 : End global iterations;  0.186110s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (100.7%)

PHY-1001 : Congestion index: top1 = 60.69, top5 = 49.77, top10 = 44.65, top15 = 41.41.
PHY-3001 : End congestion estimation;  0.423891s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (99.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22788 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.121407s wall, 1.109375s user + 0.015625s system = 1.125000s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000770672
PHY-3002 : Step(188): len = 477393, overlap = 192.438
PHY-3002 : Step(189): len = 477577, overlap = 191.844
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00154134
PHY-3002 : Step(190): len = 477678, overlap = 191.875
PHY-3002 : Step(191): len = 477916, overlap = 191.812
PHY-3001 : Final: Len = 477916, Over = 191.812
PHY-3001 : End incremental placement;  5.641573s wall, 5.828125s user + 0.343750s system = 6.171875s CPU (109.4%)

OPT-1001 : Total overflow 591.78 peak overflow 4.09
OPT-1001 : End high-fanout net optimization;  8.743032s wall, 9.531250s user + 0.359375s system = 9.890625s CPU (113.1%)

OPT-1001 : Current memory(MB): used = 722, reserve = 703, peak = 739.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17766/22790.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 554648, over cnt = 2575(7%), over = 9416, worst = 27
PHY-1002 : len = 602600, over cnt = 1881(5%), over = 5106, worst = 27
PHY-1002 : len = 647384, over cnt = 785(2%), over = 2003, worst = 27
PHY-1002 : len = 669288, over cnt = 248(0%), over = 742, worst = 18
PHY-1002 : len = 682360, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.360726s wall, 2.062500s user + 0.046875s system = 2.109375s CPU (155.0%)

PHY-1001 : Congestion index: top1 = 52.39, top5 = 45.22, top10 = 41.87, top15 = 39.78.
OPT-1001 : End congestion update;  1.638531s wall, 2.343750s user + 0.046875s system = 2.390625s CPU (145.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22788 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.148951s wall, 1.140625s user + 0.000000s system = 1.140625s CPU (99.3%)

OPT-0007 : Start: WNS 3998 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.794373s wall, 3.484375s user + 0.046875s system = 3.531250s CPU (126.4%)

OPT-1001 : Current memory(MB): used = 698, reserve = 681, peak = 739.
OPT-1001 : End physical optimization;  13.349680s wall, 14.953125s user + 0.421875s system = 15.375000s CPU (115.2%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5907 LUT to BLE ...
SYN-4008 : Packed 5907 LUT and 2888 SEQ to BLE.
SYN-4003 : Packing 10066 remaining SEQ's ...
SYN-4005 : Packed 3359 SEQ with LUT/SLICE
SYN-4006 : 170 single LUT's are left
SYN-4006 : 6707 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 12614/14296 primitive instances ...
PHY-3001 : End packing;  3.177248s wall, 3.171875s user + 0.000000s system = 3.171875s CPU (99.8%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8374 instances
RUN-1001 : 4129 mslices, 4128 lslices, 60 pads, 52 brams, 0 dsps
RUN-1001 : There are total 19949 nets
RUN-1001 : 13894 nets have 2 pins
RUN-1001 : 4631 nets have [3 - 5] pins
RUN-1001 : 867 nets have [6 - 10] pins
RUN-1001 : 423 nets have [11 - 20] pins
RUN-1001 : 124 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
PHY-3001 : design contains 8372 instances, 8257 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Cell area utilization is 87%
PHY-3001 : After packing: Len = 494783, Over = 419
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 8248/19949.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 637000, over cnt = 1693(4%), over = 2797, worst = 9
PHY-1002 : len = 642760, over cnt = 1163(3%), over = 1721, worst = 7
PHY-1002 : len = 655688, over cnt = 503(1%), over = 702, worst = 7
PHY-1002 : len = 663368, over cnt = 203(0%), over = 275, worst = 5
PHY-1002 : len = 668088, over cnt = 36(0%), over = 44, worst = 2
PHY-1001 : End global iterations;  1.140100s wall, 1.859375s user + 0.078125s system = 1.937500s CPU (169.9%)

PHY-1001 : Congestion index: top1 = 52.80, top5 = 45.30, top10 = 41.58, top15 = 39.25.
PHY-3001 : End congestion estimation;  1.454513s wall, 2.171875s user + 0.078125s system = 2.250000s CPU (154.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 70058, tnet num: 19947, tinst num: 8372, tnode num: 95473, tedge num: 115244.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.754576s wall, 1.718750s user + 0.031250s system = 1.750000s CPU (99.7%)

RUN-1004 : used memory is 618 MB, reserved memory is 609 MB, peak memory is 739 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19947 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.653390s wall, 2.625000s user + 0.031250s system = 2.656250s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.55167e-05
PHY-3002 : Step(192): len = 497060, overlap = 404.75
PHY-3002 : Step(193): len = 495982, overlap = 414.25
PHY-3002 : Step(194): len = 496188, overlap = 433.5
PHY-3002 : Step(195): len = 500073, overlap = 433.25
PHY-3002 : Step(196): len = 500032, overlap = 443.25
PHY-3002 : Step(197): len = 499603, overlap = 436.75
PHY-3002 : Step(198): len = 497616, overlap = 441.5
PHY-3002 : Step(199): len = 495720, overlap = 445.25
PHY-3002 : Step(200): len = 493285, overlap = 443
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.10334e-05
PHY-3002 : Step(201): len = 497687, overlap = 433.5
PHY-3002 : Step(202): len = 502168, overlap = 420.5
PHY-3002 : Step(203): len = 501579, overlap = 416
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000182067
PHY-3002 : Step(204): len = 509591, overlap = 394
PHY-3002 : Step(205): len = 518897, overlap = 380.75
PHY-3002 : Step(206): len = 516848, overlap = 375.5
PHY-3002 : Step(207): len = 514782, overlap = 381.25
PHY-3002 : Step(208): len = 514461, overlap = 382.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.615708s wall, 0.578125s user + 0.671875s system = 1.250000s CPU (203.0%)

PHY-3001 : Trial Legalized: Len = 638945
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 514/19949.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 733728, over cnt = 2519(7%), over = 4088, worst = 8
PHY-1002 : len = 750560, over cnt = 1407(3%), over = 1930, worst = 7
PHY-1002 : len = 770832, over cnt = 336(0%), over = 450, worst = 7
PHY-1002 : len = 774536, over cnt = 187(0%), over = 263, worst = 7
PHY-1002 : len = 779416, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.862783s wall, 3.156250s user + 0.015625s system = 3.171875s CPU (170.3%)

PHY-1001 : Congestion index: top1 = 50.15, top5 = 45.64, top10 = 42.92, top15 = 41.11.
PHY-3001 : End congestion estimation;  2.209136s wall, 3.515625s user + 0.015625s system = 3.531250s CPU (159.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19947 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.879841s wall, 0.875000s user + 0.000000s system = 0.875000s CPU (99.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000214419
PHY-3002 : Step(209): len = 591762, overlap = 81
PHY-3002 : Step(210): len = 572125, overlap = 123.5
PHY-3002 : Step(211): len = 558465, overlap = 175
PHY-3002 : Step(212): len = 550623, overlap = 215.75
PHY-3002 : Step(213): len = 546086, overlap = 243.75
PHY-3002 : Step(214): len = 543576, overlap = 263
PHY-3002 : Step(215): len = 542919, overlap = 269
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000428838
PHY-3002 : Step(216): len = 548468, overlap = 261.75
PHY-3002 : Step(217): len = 552765, overlap = 256.75
PHY-3002 : Step(218): len = 551933, overlap = 257
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(219): len = 556149, overlap = 255.25
PHY-3002 : Step(220): len = 563455, overlap = 246
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.032225s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (97.0%)

PHY-3001 : Legalized: Len = 608293, Over = 0
PHY-3001 : Spreading special nets. 35 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.076014s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (102.8%)

PHY-3001 : 49 instances has been re-located, deltaX = 16, deltaY = 30, maxDist = 2.
PHY-3001 : Final: Len = 608941, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 70058, tnet num: 19947, tinst num: 8372, tnode num: 95473, tedge num: 115244.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.059280s wall, 2.046875s user + 0.015625s system = 2.062500s CPU (100.2%)

RUN-1004 : used memory is 630 MB, reserved memory is 633 MB, peak memory is 739 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3598/19949.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 712384, over cnt = 2345(6%), over = 3678, worst = 7
PHY-1002 : len = 724176, over cnt = 1425(4%), over = 1958, worst = 7
PHY-1002 : len = 745728, over cnt = 321(0%), over = 406, worst = 5
PHY-1002 : len = 750928, over cnt = 74(0%), over = 98, worst = 3
PHY-1002 : len = 752544, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.834211s wall, 3.156250s user + 0.031250s system = 3.187500s CPU (173.8%)

PHY-1001 : Congestion index: top1 = 48.34, top5 = 44.12, top10 = 41.47, top15 = 39.66.
PHY-1001 : End incremental global routing;  2.216950s wall, 3.531250s user + 0.031250s system = 3.562500s CPU (160.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19947 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.927129s wall, 0.890625s user + 0.031250s system = 0.921875s CPU (99.4%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8307 has valid locations, 17 needs to be replaced
PHY-3001 : design contains 8387 instances, 8272 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 612908
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17982/19972.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 757816, over cnt = 37(0%), over = 43, worst = 3
PHY-1002 : len = 757672, over cnt = 34(0%), over = 34, worst = 1
PHY-1002 : len = 757784, over cnt = 22(0%), over = 22, worst = 1
PHY-1002 : len = 757992, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 758160, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.699696s wall, 0.765625s user + 0.046875s system = 0.812500s CPU (116.1%)

PHY-1001 : Congestion index: top1 = 48.64, top5 = 44.39, top10 = 41.65, top15 = 39.82.
PHY-3001 : End congestion estimation;  0.999581s wall, 1.062500s user + 0.046875s system = 1.109375s CPU (111.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 70171, tnet num: 19970, tinst num: 8387, tnode num: 95616, tedge num: 115415.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.964257s wall, 1.968750s user + 0.000000s system = 1.968750s CPU (100.2%)

RUN-1004 : used memory is 661 MB, reserved memory is 653 MB, peak memory is 739 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19970 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.859617s wall, 2.812500s user + 0.046875s system = 2.859375s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(221): len = 612747, overlap = 0.5
PHY-3002 : Step(222): len = 612583, overlap = 1
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17977/19972.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 755904, over cnt = 24(0%), over = 44, worst = 5
PHY-1002 : len = 756088, over cnt = 20(0%), over = 25, worst = 4
PHY-1002 : len = 756208, over cnt = 10(0%), over = 10, worst = 1
PHY-1002 : len = 756248, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 756360, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.697882s wall, 0.734375s user + 0.015625s system = 0.750000s CPU (107.5%)

PHY-1001 : Congestion index: top1 = 48.36, top5 = 44.23, top10 = 41.56, top15 = 39.75.
PHY-3001 : End congestion estimation;  0.983911s wall, 1.031250s user + 0.015625s system = 1.046875s CPU (106.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19970 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.874704s wall, 0.859375s user + 0.015625s system = 0.875000s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.0014678
PHY-3002 : Step(223): len = 612575, overlap = 2
PHY-3002 : Step(224): len = 612559, overlap = 2
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005999s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 612612, Over = 0
PHY-3001 : End spreading;  0.083020s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (94.1%)

PHY-3001 : Final: Len = 612612, Over = 0
PHY-3001 : End incremental placement;  6.411343s wall, 6.281250s user + 0.203125s system = 6.484375s CPU (101.1%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.021108s wall, 11.171875s user + 0.265625s system = 11.437500s CPU (114.1%)

OPT-1001 : Current memory(MB): used = 736, reserve = 724, peak = 742.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17977/19972.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 756592, over cnt = 29(0%), over = 41, worst = 5
PHY-1002 : len = 756536, over cnt = 16(0%), over = 18, worst = 2
PHY-1002 : len = 756560, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 756584, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 756616, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.677210s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (101.5%)

PHY-1001 : Congestion index: top1 = 48.62, top5 = 44.14, top10 = 41.49, top15 = 39.67.
OPT-1001 : End congestion update;  0.974646s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (102.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19970 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.725968s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (99.0%)

OPT-0007 : Start: WNS 4272 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.705202s wall, 1.718750s user + 0.000000s system = 1.718750s CPU (100.8%)

OPT-1001 : Current memory(MB): used = 737, reserve = 724, peak = 742.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19970 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.750874s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (99.9%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17997/19972.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 756616, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.121306s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (90.2%)

PHY-1001 : Congestion index: top1 = 48.62, top5 = 44.14, top10 = 41.49, top15 = 39.67.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19970 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.759483s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (100.8%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4272 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 48.103448
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4272ps with logic level 7 
RUN-1001 :       #2 path slack 4322ps with logic level 7 
RUN-1001 :       #3 path slack 4372ps with logic level 7 
OPT-1001 : End physical optimization;  15.962408s wall, 17.281250s user + 0.281250s system = 17.562500s CPU (110.0%)

RUN-1003 : finish command "place" in  75.806131s wall, 126.421875s user + 7.687500s system = 134.109375s CPU (176.9%)

RUN-1004 : used memory is 614 MB, reserved memory is 592 MB, peak memory is 742 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.576851s wall, 2.765625s user + 0.031250s system = 2.796875s CPU (177.4%)

RUN-1004 : used memory is 614 MB, reserved memory is 593 MB, peak memory is 742 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8389 instances
RUN-1001 : 4129 mslices, 4143 lslices, 60 pads, 52 brams, 0 dsps
RUN-1001 : There are total 19972 nets
RUN-1001 : 13903 nets have 2 pins
RUN-1001 : 4631 nets have [3 - 5] pins
RUN-1001 : 875 nets have [6 - 10] pins
RUN-1001 : 428 nets have [11 - 20] pins
RUN-1001 : 125 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 70171, tnet num: 19970, tinst num: 8387, tnode num: 95616, tedge num: 115415.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.729959s wall, 1.718750s user + 0.000000s system = 1.718750s CPU (99.4%)

RUN-1004 : used memory is 623 MB, reserved memory is 637 MB, peak memory is 742 MB
PHY-1001 : 4129 mslices, 4143 lslices, 60 pads, 52 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19970 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 694336, over cnt = 2421(6%), over = 4082, worst = 8
PHY-1002 : len = 710056, over cnt = 1543(4%), over = 2223, worst = 8
PHY-1002 : len = 726912, over cnt = 717(2%), over = 1015, worst = 7
PHY-1002 : len = 738904, over cnt = 171(0%), over = 272, worst = 5
PHY-1002 : len = 743848, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.643015s wall, 3.015625s user + 0.031250s system = 3.046875s CPU (185.4%)

PHY-1001 : Congestion index: top1 = 48.77, top5 = 43.63, top10 = 41.04, top15 = 39.31.
PHY-1001 : End global routing;  1.986176s wall, 3.359375s user + 0.031250s system = 3.390625s CPU (170.7%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 718, reserve = 710, peak = 742.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 987, reserve = 975, peak = 987.
PHY-1001 : End build detailed router design. 4.436265s wall, 4.375000s user + 0.062500s system = 4.437500s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 193432, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.832569s wall, 0.796875s user + 0.031250s system = 0.828125s CPU (99.5%)

PHY-1001 : Current memory(MB): used = 1024, reserve = 1013, peak = 1024.
PHY-1001 : End phase 1; 0.840195s wall, 0.812500s user + 0.031250s system = 0.843750s CPU (100.4%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.81222e+06, over cnt = 1447(0%), over = 1452, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1040, reserve = 1029, peak = 1040.
PHY-1001 : End initial routed; 21.351205s wall, 51.578125s user + 0.640625s system = 52.218750s CPU (244.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18748(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.187   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.383718s wall, 3.390625s user + 0.000000s system = 3.390625s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 1051, reserve = 1040, peak = 1051.
PHY-1001 : End phase 2; 24.735064s wall, 54.968750s user + 0.640625s system = 55.609375s CPU (224.8%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.81222e+06, over cnt = 1447(0%), over = 1452, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.236382s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (105.8%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.7966e+06, over cnt = 472(0%), over = 473, worst = 2, crit = 0
PHY-1001 : End DR Iter 1; 0.852527s wall, 1.656250s user + 0.000000s system = 1.656250s CPU (194.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.79703e+06, over cnt = 131(0%), over = 131, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.436838s wall, 0.609375s user + 0.000000s system = 0.609375s CPU (139.5%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.79837e+06, over cnt = 12(0%), over = 12, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.406999s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (111.3%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.79866e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.232292s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (107.6%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.79874e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.155923s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (100.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18748(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.779   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.396273s wall, 3.375000s user + 0.000000s system = 3.375000s CPU (99.4%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 380 feed throughs used by 327 nets
PHY-1001 : End commit to database; 2.157848s wall, 2.140625s user + 0.015625s system = 2.156250s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 1140, reserve = 1131, peak = 1140.
PHY-1001 : End phase 3; 8.377933s wall, 9.375000s user + 0.015625s system = 9.390625s CPU (112.1%)

PHY-1003 : Routed, final wirelength = 1.79874e+06
PHY-1001 : Current memory(MB): used = 1144, reserve = 1136, peak = 1144.
PHY-1001 : End export database. 0.161928s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (96.5%)

PHY-1001 : End detail routing;  38.953908s wall, 70.093750s user + 0.750000s system = 70.843750s CPU (181.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 70171, tnet num: 19970, tinst num: 8387, tnode num: 95616, tedge num: 115415.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.687465s wall, 1.687500s user + 0.000000s system = 1.687500s CPU (100.0%)

RUN-1004 : used memory is 1073 MB, reserved memory is 1072 MB, peak memory is 1144 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  46.899927s wall, 79.375000s user + 0.812500s system = 80.187500s CPU (171.0%)

RUN-1004 : used memory is 1073 MB, reserved memory is 1073 MB, peak memory is 1144 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8946   out of  19600   45.64%
#reg                    13060   out of  19600   66.63%
#le                     15597
  #lut only              2537   out of  15597   16.27%
  #reg only              6651   out of  15597   42.64%
  #lut&reg               6409   out of  15597   41.09%
#dsp                        0   out of     29    0.00%
#bram                      52   out of     64   81.25%
  #bram9k                  52
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                    10
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    7069
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          188
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         M3        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         P6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15597  |7501    |1445    |13103   |52      |0       |
|  AnyFog_dataX                      |AnyFog          |202    |99      |22      |168     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |52      |22      |52      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |220    |90      |22      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |104    |71      |22      |57      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |208    |105     |22      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |53      |22      |51      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |3451   |844     |34      |3381    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |741    |91      |5       |724     |0       |0       |
|    STADOP_com2                     |STADOP          |554    |50      |0       |552     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |43      |14      |43      |0       |0       |
|    head_com2                       |uniheading      |264    |97      |5       |253     |0       |0       |
|    uart_com2                       |Agrica          |1537   |273     |10      |1519    |0       |0       |
|  COM3                              |COM3_Control    |203    |120     |14      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |67     |47      |14      |40      |0       |0       |
|    rmc_com3                        |Gprmc           |136    |73      |0       |133     |0       |0       |
|  DATA                              |Data_Processing |8768   |4423    |1059    |7082    |0       |0       |
|    DIV_Dtemp                       |Divider         |821    |348     |84      |696     |0       |0       |
|    DIV_Utemp                       |Divider         |598    |298     |84      |463     |0       |0       |
|    DIV_accX                        |Divider         |602    |287     |84      |474     |0       |0       |
|    DIV_accY                        |Divider         |693    |370     |108     |527     |0       |0       |
|    DIV_accZ                        |Divider         |630    |335     |132     |423     |0       |0       |
|    DIV_rateX                       |Divider         |713    |378     |132     |508     |0       |0       |
|    DIV_rateY                       |Divider         |552    |345     |132     |348     |0       |0       |
|    DIV_rateZ                       |Divider         |569    |333     |132     |360     |0       |0       |
|    genclk                          |genclk          |86     |57      |20      |53      |0       |0       |
|  FMC                               |FMC_Ctrl        |482    |425     |43      |361     |0       |0       |
|  IIC                               |I2C_master      |315    |252     |11      |269     |0       |0       |
|  IMU_CTRL                          |SCHA634         |895    |662     |61      |718     |0       |0       |
|    CtrlData                        |CtrlData        |488    |435     |47      |338     |0       |0       |
|      usms                          |Time_1ms        |31     |26      |5       |22      |0       |0       |
|    SPIM                            |SPI_SCHA634     |407    |227     |14      |380     |0       |0       |
|  POWER                             |POWER_EN        |100    |45      |38      |39      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |734    |428     |119     |504     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |734    |428     |119     |504     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |333    |168     |0       |317     |0       |0       |
|        reg_inst                    |register        |332    |167     |0       |316     |0       |0       |
|        tap_inst                    |tap             |1      |1       |0       |1       |0       |0       |
|      trigger_inst                  |trigger         |401    |260     |119     |187     |0       |0       |
|        bus_inst                    |bus_top         |178    |116     |62      |65      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |26     |16      |10      |10      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |49     |31      |18      |16      |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |99     |65      |34      |35      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |149    |106     |29      |96      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13842  
    #2          2       3643   
    #3          3        701   
    #4          4        287   
    #5        5-10       957   
    #6        11-50      452   
    #7       51-100      19    
    #8       101-500      4    
    #9        >500        2    
  Average     2.15             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  1.978574s wall, 3.500000s user + 0.046875s system = 3.546875s CPU (179.3%)

RUN-1004 : used memory is 1074 MB, reserved memory is 1074 MB, peak memory is 1144 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 70171, tnet num: 19970, tinst num: 8387, tnode num: 95616, tedge num: 115415.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.875872s wall, 1.875000s user + 0.000000s system = 1.875000s CPU (100.0%)

RUN-1004 : used memory is 1075 MB, reserved memory is 1076 MB, peak memory is 1144 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19970 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.405981s wall, 1.390625s user + 0.015625s system = 1.406250s CPU (100.0%)

RUN-1004 : used memory is 1080 MB, reserved memory is 1080 MB, peak memory is 1144 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 28b752c1c0e7d7ba92ac5beed3c46754b7b56ac64969e4b0149cfdbc1d295011 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8387
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19972, pip num: 153424
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 380
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3262 valid insts, and 427649 bits set as '1'.
BIT-1004 : the usercode register value: 00000000000000001110111010000001
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  10.632893s wall, 98.500000s user + 0.265625s system = 98.765625s CPU (928.9%)

RUN-1004 : used memory is 1208 MB, reserved memory is 1199 MB, peak memory is 1323 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250701_133004.log"

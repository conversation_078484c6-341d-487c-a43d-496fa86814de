============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Aug 21 11:41:29 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.862578s wall, 1.609375s user + 4.234375s system = 5.843750s CPU (99.7%)

RUN-1004 : used memory is 79 MB, reserved memory is 41 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.986848s wall, 1.859375s user + 0.125000s system = 1.984375s CPU (99.9%)

RUN-1004 : used memory is 303 MB, reserved memory is 271 MB, peak memory is 306 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 6 view nodes, 44 trigger nets, 44 data nets.
KIT-1004 : Chipwatcher code = 0011001100100101
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=6,BUS_DIN_NUM=44,BUS_CTRL_NUM=112,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb010,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010,32'sb011100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100,32'sb01001100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=134) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=134) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=6,BUS_DIN_NUM=44,BUS_CTRL_NUM=112,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb010,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010,32'sb011100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100,32'sb01001100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=6,BUS_DIN_NUM=44,BUS_CTRL_NUM=112,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb010,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010,32'sb011100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100,32'sb01001100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=6,BUS_DIN_NUM=44,BUS_CTRL_NUM=112,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb010,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010,32'sb011100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100,32'sb01001100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=134)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=134)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=6,BUS_DIN_NUM=44,BUS_CTRL_NUM=112,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb010,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010,32'sb011100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100,32'sb01001100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=6,BUS_DIN_NUM=44,BUS_CTRL_NUM=112,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb010,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010,32'sb011100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100,32'sb01001100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22930/28 useful/useless nets, 19644/14 useful/useless insts
SYN-1016 : Merged 32 instances.
SYN-1032 : 22579/20 useful/useless nets, 20085/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 4 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 4 mux instances.
SYN-1015 : Optimize round 1, 419 better
SYN-1014 : Optimize round 2
SYN-1032 : 22226/60 useful/useless nets, 19732/64 useful/useless insts
SYN-1015 : Optimize round 2, 128 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.645518s wall, 2.578125s user + 0.062500s system = 2.640625s CPU (99.8%)

RUN-1004 : used memory is 330 MB, reserved memory is 296 MB, peak memory is 331 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22274/301 useful/useless nets, 19819/49 useful/useless insts
SYN-1016 : Merged 45 instances.
SYN-2571 : Optimize after map_dsp, round 1, 395 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 43 instances.
SYN-2501 : Optimize round 1, 87 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 18 macro adder
SYN-1019 : Optimized 17 mux instances.
SYN-1016 : Merged 16 instances.
SYN-1032 : 22736/5 useful/useless nets, 20281/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83076, tnet num: 22736, tinst num: 20280, tnode num: 116218, tedge num: 129870.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.281909s wall, 1.265625s user + 0.015625s system = 1.281250s CPU (99.9%)

RUN-1004 : used memory is 471 MB, reserved memory is 439 MB, peak memory is 471 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22736 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 246 (3.44), #lev = 6 (1.80)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 246 (3.44), #lev = 6 (1.80)
SYN-3001 : Logic optimization runtime opt =   0.04 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 564 instances into 246 LUTs, name keeping = 72%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 426 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 128 adder to BLE ...
SYN-4008 : Packed 128 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.941157s wall, 4.875000s user + 0.062500s system = 4.937500s CPU (99.9%)

RUN-1004 : used memory is 354 MB, reserved memory is 319 MB, peak memory is 579 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.943592s wall, 7.812500s user + 0.140625s system = 7.953125s CPU (100.1%)

RUN-1004 : used memory is 354 MB, reserved memory is 320 MB, peak memory is 579 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (287 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19503 instances
RUN-0007 : 5677 luts, 12218 seqs, 983 mslices, 519 lslices, 59 pads, 42 brams, 0 dsps
RUN-1001 : There are total 21966 nets
RUN-1001 : 16489 nets have 2 pins
RUN-1001 : 4277 nets have [3 - 5] pins
RUN-1001 : 817 nets have [6 - 10] pins
RUN-1001 : 253 nets have [11 - 20] pins
RUN-1001 : 108 nets have [21 - 99] pins
RUN-1001 : 22 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4783     
RUN-1001 :   No   |  No   |  Yes  |     707     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     459     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19501 instances, 5677 luts, 12218 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81415, tnet num: 21964, tinst num: 19501, tnode num: 114367, tedge num: 128171.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.324709s wall, 1.296875s user + 0.031250s system = 1.328125s CPU (100.3%)

RUN-1004 : used memory is 530 MB, reserved memory is 502 MB, peak memory is 579 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21964 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.286320s wall, 2.250000s user + 0.046875s system = 2.296875s CPU (100.5%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.58311e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19501.
PHY-3001 : Level 1 #clusters 2153.
PHY-3001 : End clustering;  0.283592s wall, 0.437500s user + 0.015625s system = 0.453125s CPU (159.8%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 886089, overlap = 618.906
PHY-3002 : Step(2): len = 804068, overlap = 679.469
PHY-3002 : Step(3): len = 532967, overlap = 859.906
PHY-3002 : Step(4): len = 480543, overlap = 918.938
PHY-3002 : Step(5): len = 386839, overlap = 1003.78
PHY-3002 : Step(6): len = 343089, overlap = 1053.69
PHY-3002 : Step(7): len = 286744, overlap = 1130.41
PHY-3002 : Step(8): len = 257331, overlap = 1174.75
PHY-3002 : Step(9): len = 225275, overlap = 1236.47
PHY-3002 : Step(10): len = 210687, overlap = 1303
PHY-3002 : Step(11): len = 187356, overlap = 1364.25
PHY-3002 : Step(12): len = 180035, overlap = 1374
PHY-3002 : Step(13): len = 164415, overlap = 1399.06
PHY-3002 : Step(14): len = 155109, overlap = 1419.34
PHY-3002 : Step(15): len = 143022, overlap = 1440.28
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.06464e-06
PHY-3002 : Step(16): len = 143444, overlap = 1415.78
PHY-3002 : Step(17): len = 182006, overlap = 1343.88
PHY-3002 : Step(18): len = 189982, overlap = 1249.25
PHY-3002 : Step(19): len = 193656, overlap = 1163.53
PHY-3002 : Step(20): len = 191416, overlap = 1137
PHY-3002 : Step(21): len = 186809, overlap = 1115.62
PHY-3002 : Step(22): len = 182765, overlap = 1106.75
PHY-3002 : Step(23): len = 178829, overlap = 1087.47
PHY-3002 : Step(24): len = 174217, overlap = 1087.94
PHY-3002 : Step(25): len = 170327, overlap = 1092.25
PHY-3002 : Step(26): len = 168546, overlap = 1085.09
PHY-3002 : Step(27): len = 167150, overlap = 1086.31
PHY-3002 : Step(28): len = 165221, overlap = 1070.12
PHY-3002 : Step(29): len = 165173, overlap = 1063.72
PHY-3002 : Step(30): len = 164647, overlap = 1065.41
PHY-3002 : Step(31): len = 164410, overlap = 1074.47
PHY-3002 : Step(32): len = 163025, overlap = 1080.91
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.12928e-06
PHY-3002 : Step(33): len = 172031, overlap = 1067.12
PHY-3002 : Step(34): len = 187298, overlap = 1028
PHY-3002 : Step(35): len = 191207, overlap = 983.219
PHY-3002 : Step(36): len = 193459, overlap = 961.219
PHY-3002 : Step(37): len = 194630, overlap = 951.25
PHY-3002 : Step(38): len = 194087, overlap = 943.406
PHY-3002 : Step(39): len = 192369, overlap = 937.031
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.25856e-06
PHY-3002 : Step(40): len = 204128, overlap = 931
PHY-3002 : Step(41): len = 220639, overlap = 861.531
PHY-3002 : Step(42): len = 224359, overlap = 835.375
PHY-3002 : Step(43): len = 225783, overlap = 826.781
PHY-3002 : Step(44): len = 224410, overlap = 811
PHY-3002 : Step(45): len = 222537, overlap = 779.969
PHY-3002 : Step(46): len = 221068, overlap = 784.438
PHY-3002 : Step(47): len = 219286, overlap = 782.406
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 8.51713e-06
PHY-3002 : Step(48): len = 231224, overlap = 749.875
PHY-3002 : Step(49): len = 244950, overlap = 670.719
PHY-3002 : Step(50): len = 248868, overlap = 628.812
PHY-3002 : Step(51): len = 252075, overlap = 600.562
PHY-3002 : Step(52): len = 251638, overlap = 582.188
PHY-3002 : Step(53): len = 250877, overlap = 577.656
PHY-3002 : Step(54): len = 248471, overlap = 569.875
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.70343e-05
PHY-3002 : Step(55): len = 260848, overlap = 546.562
PHY-3002 : Step(56): len = 277303, overlap = 469.844
PHY-3002 : Step(57): len = 281489, overlap = 445.062
PHY-3002 : Step(58): len = 282895, overlap = 439.344
PHY-3002 : Step(59): len = 281149, overlap = 439.938
PHY-3002 : Step(60): len = 278590, overlap = 450.875
PHY-3002 : Step(61): len = 276102, overlap = 479.688
PHY-3002 : Step(62): len = 275227, overlap = 482.031
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.40685e-05
PHY-3002 : Step(63): len = 285300, overlap = 450.781
PHY-3002 : Step(64): len = 297137, overlap = 401.281
PHY-3002 : Step(65): len = 300854, overlap = 378.188
PHY-3002 : Step(66): len = 303449, overlap = 373.406
PHY-3002 : Step(67): len = 301487, overlap = 369.312
PHY-3002 : Step(68): len = 299472, overlap = 346.75
PHY-3002 : Step(69): len = 297223, overlap = 339.75
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 6.8137e-05
PHY-3002 : Step(70): len = 303960, overlap = 342.906
PHY-3002 : Step(71): len = 312833, overlap = 323.938
PHY-3002 : Step(72): len = 316651, overlap = 328.219
PHY-3002 : Step(73): len = 318124, overlap = 325.844
PHY-3002 : Step(74): len = 316296, overlap = 316.719
PHY-3002 : Step(75): len = 315759, overlap = 305.969
PHY-3002 : Step(76): len = 313929, overlap = 290.469
PHY-3002 : Step(77): len = 314455, overlap = 286
PHY-3002 : Step(78): len = 313718, overlap = 290.812
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000136274
PHY-3002 : Step(79): len = 317678, overlap = 280.906
PHY-3002 : Step(80): len = 323969, overlap = 273.625
PHY-3002 : Step(81): len = 327208, overlap = 276.188
PHY-3002 : Step(82): len = 328994, overlap = 272.562
PHY-3002 : Step(83): len = 327834, overlap = 267.125
PHY-3002 : Step(84): len = 326893, overlap = 260
PHY-3002 : Step(85): len = 325327, overlap = 246.719
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000262683
PHY-3002 : Step(86): len = 328503, overlap = 251.375
PHY-3002 : Step(87): len = 331457, overlap = 252.812
PHY-3002 : Step(88): len = 332542, overlap = 241.781
PHY-3002 : Step(89): len = 335003, overlap = 240.719
PHY-3002 : Step(90): len = 335323, overlap = 235.438
PHY-3002 : Step(91): len = 336037, overlap = 238.281
PHY-3002 : Step(92): len = 334973, overlap = 241.406
PHY-3002 : Step(93): len = 334765, overlap = 242.562
PHY-3002 : Step(94): len = 334634, overlap = 245.875
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000469924
PHY-3002 : Step(95): len = 336288, overlap = 234.844
PHY-3002 : Step(96): len = 341059, overlap = 234.344
PHY-3002 : Step(97): len = 343373, overlap = 237.312
PHY-3002 : Step(98): len = 343679, overlap = 224.625
PHY-3002 : Step(99): len = 343964, overlap = 210.844
PHY-3002 : Step(100): len = 344239, overlap = 212.469
PHY-3002 : Step(101): len = 344432, overlap = 211.656
PHY-3002 : Step(102): len = 343603, overlap = 208.531
PHY-3002 : Step(103): len = 343586, overlap = 207.156
PHY-3002 : Step(104): len = 343199, overlap = 209.25
PHY-3001 : :::11::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(105): len = 343771, overlap = 208.125
PHY-3002 : Step(106): len = 346813, overlap = 202.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.020924s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (224.0%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21966.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 455312, over cnt = 1229(3%), over = 5330, worst = 41
PHY-1001 : End global iterations;  0.887861s wall, 1.203125s user + 0.109375s system = 1.312500s CPU (147.8%)

PHY-1001 : Congestion index: top1 = 75.06, top5 = 52.63, top10 = 43.18, top15 = 37.77.
PHY-3001 : End congestion estimation;  1.126021s wall, 1.437500s user + 0.109375s system = 1.546875s CPU (137.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21964 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.182475s wall, 1.187500s user + 0.000000s system = 1.187500s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00011132
PHY-3002 : Step(107): len = 393863, overlap = 138.438
PHY-3002 : Step(108): len = 407958, overlap = 127.125
PHY-3002 : Step(109): len = 406212, overlap = 121.719
PHY-3002 : Step(110): len = 404318, overlap = 120.312
PHY-3002 : Step(111): len = 411540, overlap = 105.719
PHY-3002 : Step(112): len = 417459, overlap = 96.9062
PHY-3002 : Step(113): len = 421873, overlap = 86.0625
PHY-3002 : Step(114): len = 428719, overlap = 77.6875
PHY-3002 : Step(115): len = 432815, overlap = 68.375
PHY-3002 : Step(116): len = 434080, overlap = 68.75
PHY-3002 : Step(117): len = 437802, overlap = 64.0312
PHY-3002 : Step(118): len = 440262, overlap = 68.0625
PHY-3002 : Step(119): len = 441707, overlap = 64.5938
PHY-3002 : Step(120): len = 442824, overlap = 65.5312
PHY-3002 : Step(121): len = 445718, overlap = 69
PHY-3002 : Step(122): len = 446953, overlap = 76.0312
PHY-3002 : Step(123): len = 447718, overlap = 78.625
PHY-3002 : Step(124): len = 450542, overlap = 77.4062
PHY-3002 : Step(125): len = 450981, overlap = 79.0625
PHY-3002 : Step(126): len = 451321, overlap = 77.25
PHY-3002 : Step(127): len = 453464, overlap = 79.9688
PHY-3002 : Step(128): len = 452719, overlap = 83.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000222639
PHY-3002 : Step(129): len = 452844, overlap = 83.5312
PHY-3002 : Step(130): len = 454778, overlap = 82.125
PHY-3002 : Step(131): len = 456731, overlap = 86.5312
PHY-3002 : Step(132): len = 459346, overlap = 85.9062
PHY-3002 : Step(133): len = 461799, overlap = 85.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000445278
PHY-3002 : Step(134): len = 459925, overlap = 87.9062
PHY-3002 : Step(135): len = 463766, overlap = 86.4688
PHY-3002 : Step(136): len = 472241, overlap = 74.9688
PHY-3002 : Step(137): len = 479721, overlap = 75.2812
PHY-3002 : Step(138): len = 477641, overlap = 74.4375
PHY-3002 : Step(139): len = 476432, overlap = 77.0312
PHY-3002 : Step(140): len = 475706, overlap = 74.375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 39/21966.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 541496, over cnt = 2172(6%), over = 10162, worst = 39
PHY-1001 : End global iterations;  1.104877s wall, 1.937500s user + 0.000000s system = 1.937500s CPU (175.4%)

PHY-1001 : Congestion index: top1 = 79.27, top5 = 59.22, top10 = 50.17, top15 = 44.90.
PHY-3001 : End congestion estimation;  1.407682s wall, 2.234375s user + 0.015625s system = 2.250000s CPU (159.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21964 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.046639s wall, 1.031250s user + 0.015625s system = 1.046875s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000112172
PHY-3002 : Step(141): len = 478998, overlap = 366.625
PHY-3002 : Step(142): len = 483744, overlap = 321.719
PHY-3002 : Step(143): len = 482854, overlap = 286.969
PHY-3002 : Step(144): len = 479194, overlap = 259.719
PHY-3002 : Step(145): len = 475106, overlap = 247.375
PHY-3002 : Step(146): len = 473262, overlap = 245
PHY-3002 : Step(147): len = 470764, overlap = 225.094
PHY-3002 : Step(148): len = 469044, overlap = 231.594
PHY-3002 : Step(149): len = 465870, overlap = 235.625
PHY-3002 : Step(150): len = 464175, overlap = 232.75
PHY-3002 : Step(151): len = 461927, overlap = 233.062
PHY-3002 : Step(152): len = 460580, overlap = 238.219
PHY-3002 : Step(153): len = 459861, overlap = 244.188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000224343
PHY-3002 : Step(154): len = 458951, overlap = 230.344
PHY-3002 : Step(155): len = 460523, overlap = 221.031
PHY-3002 : Step(156): len = 462416, overlap = 212.594
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000448687
PHY-3002 : Step(157): len = 463583, overlap = 203.688
PHY-3002 : Step(158): len = 470228, overlap = 185.875
PHY-3002 : Step(159): len = 475265, overlap = 174
PHY-3002 : Step(160): len = 475460, overlap = 168.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000897373
PHY-3002 : Step(161): len = 476091, overlap = 170.125
PHY-3002 : Step(162): len = 480675, overlap = 155.094
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81415, tnet num: 21964, tinst num: 19501, tnode num: 114367, tedge num: 128171.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.652669s wall, 1.593750s user + 0.062500s system = 1.656250s CPU (100.2%)

RUN-1004 : used memory is 569 MB, reserved memory is 544 MB, peak memory is 704 MB
OPT-1001 : Total overflow 503.22 peak overflow 4.25
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 519/21966.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 562344, over cnt = 2496(7%), over = 8950, worst = 27
PHY-1001 : End global iterations;  1.327413s wall, 1.843750s user + 0.015625s system = 1.859375s CPU (140.1%)

PHY-1001 : Congestion index: top1 = 60.15, top5 = 49.23, top10 = 44.22, top15 = 41.10.
PHY-1001 : End incremental global routing;  1.616872s wall, 2.140625s user + 0.015625s system = 2.156250s CPU (133.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21964 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.089182s wall, 1.078125s user + 0.015625s system = 1.093750s CPU (100.4%)

OPT-1001 : 18 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19421 has valid locations, 244 needs to be replaced
PHY-3001 : design contains 19727 instances, 5768 luts, 12353 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 496325
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17365/22192.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 573672, over cnt = 2533(7%), over = 9001, worst = 27
PHY-1001 : End global iterations;  0.204153s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (114.8%)

PHY-1001 : Congestion index: top1 = 60.37, top5 = 49.55, top10 = 44.53, top15 = 41.40.
PHY-3001 : End congestion estimation;  0.475964s wall, 0.500000s user + 0.000000s system = 0.500000s CPU (105.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82150, tnet num: 22190, tinst num: 19727, tnode num: 115423, tedge num: 129189.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.645864s wall, 1.640625s user + 0.015625s system = 1.656250s CPU (100.6%)

RUN-1004 : used memory is 614 MB, reserved memory is 608 MB, peak memory is 705 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22190 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.781380s wall, 2.734375s user + 0.046875s system = 2.781250s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(163): len = 495989, overlap = 1.3125
PHY-3002 : Step(164): len = 496503, overlap = 1.25
PHY-3002 : Step(165): len = 497718, overlap = 1.1875
PHY-3002 : Step(166): len = 498701, overlap = 1.375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17395/22192.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 573072, over cnt = 2551(7%), over = 9117, worst = 27
PHY-1001 : End global iterations;  0.201400s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (155.2%)

PHY-1001 : Congestion index: top1 = 60.71, top5 = 49.86, top10 = 44.83, top15 = 41.62.
PHY-3001 : End congestion estimation;  0.476139s wall, 0.578125s user + 0.015625s system = 0.593750s CPU (124.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22190 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.074468s wall, 1.062500s user + 0.000000s system = 1.062500s CPU (98.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00057836
PHY-3002 : Step(167): len = 498540, overlap = 158.469
PHY-3002 : Step(168): len = 498814, overlap = 158.312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00115672
PHY-3002 : Step(169): len = 499140, overlap = 158.188
PHY-3002 : Step(170): len = 499723, overlap = 158.219
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00228457
PHY-3002 : Step(171): len = 499716, overlap = 158.031
PHY-3002 : Step(172): len = 500044, overlap = 157
PHY-3001 : Final: Len = 500044, Over = 157
PHY-3001 : End incremental placement;  5.883265s wall, 6.625000s user + 0.312500s system = 6.937500s CPU (117.9%)

OPT-1001 : Total overflow 509.16 peak overflow 4.25
OPT-1001 : End high-fanout net optimization;  9.190982s wall, 10.625000s user + 0.343750s system = 10.968750s CPU (119.3%)

OPT-1001 : Current memory(MB): used = 707, reserve = 687, peak = 724.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17382/22192.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 575192, over cnt = 2496(7%), over = 8548, worst = 27
PHY-1002 : len = 615808, over cnt = 1831(5%), over = 4732, worst = 17
PHY-1002 : len = 649736, over cnt = 1004(2%), over = 2316, worst = 16
PHY-1002 : len = 664928, over cnt = 550(1%), over = 1231, worst = 16
PHY-1002 : len = 687440, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.505301s wall, 2.000000s user + 0.015625s system = 2.015625s CPU (133.9%)

PHY-1001 : Congestion index: top1 = 51.08, top5 = 45.05, top10 = 41.76, top15 = 39.69.
OPT-1001 : End congestion update;  1.778840s wall, 2.281250s user + 0.015625s system = 2.296875s CPU (129.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22190 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.930473s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (99.1%)

OPT-0007 : Start: WNS 4019 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.715251s wall, 3.203125s user + 0.015625s system = 3.218750s CPU (118.5%)

OPT-1001 : Current memory(MB): used = 704, reserve = 683, peak = 724.
OPT-1001 : End physical optimization;  13.903198s wall, 15.906250s user + 0.453125s system = 16.359375s CPU (117.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5768 LUT to BLE ...
SYN-4008 : Packed 5768 LUT and 2728 SEQ to BLE.
SYN-4003 : Packing 9625 remaining SEQ's ...
SYN-4005 : Packed 3410 SEQ with LUT/SLICE
SYN-4006 : 149 single LUT's are left
SYN-4006 : 6215 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11983/13855 primitive instances ...
PHY-3001 : End packing;  3.046517s wall, 3.046875s user + 0.015625s system = 3.062500s CPU (100.5%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8145 instances
RUN-1001 : 4020 mslices, 4019 lslices, 59 pads, 42 brams, 0 dsps
RUN-1001 : There are total 19514 nets
RUN-1001 : 13722 nets have 2 pins
RUN-1001 : 4365 nets have [3 - 5] pins
RUN-1001 : 881 nets have [6 - 10] pins
RUN-1001 : 408 nets have [11 - 20] pins
RUN-1001 : 129 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8143 instances, 8039 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 515744, Over = 383.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7828/19514.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 649952, over cnt = 1626(4%), over = 2643, worst = 8
PHY-1002 : len = 657272, over cnt = 1019(2%), over = 1419, worst = 8
PHY-1002 : len = 666936, over cnt = 510(1%), over = 661, worst = 6
PHY-1002 : len = 674872, over cnt = 148(0%), over = 197, worst = 6
PHY-1002 : len = 678552, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.351858s wall, 1.984375s user + 0.000000s system = 1.984375s CPU (146.8%)

PHY-1001 : Congestion index: top1 = 51.29, top5 = 44.86, top10 = 41.49, top15 = 39.26.
PHY-3001 : End congestion estimation;  1.706110s wall, 2.343750s user + 0.000000s system = 2.343750s CPU (137.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68193, tnet num: 19512, tinst num: 8143, tnode num: 92403, tedge num: 112389.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.822598s wall, 1.812500s user + 0.015625s system = 1.828125s CPU (100.3%)

RUN-1004 : used memory is 602 MB, reserved memory is 580 MB, peak memory is 724 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19512 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.823622s wall, 2.796875s user + 0.031250s system = 2.828125s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.05178e-05
PHY-3002 : Step(173): len = 520227, overlap = 364
PHY-3002 : Step(174): len = 520443, overlap = 362.5
PHY-3002 : Step(175): len = 522228, overlap = 385.25
PHY-3002 : Step(176): len = 521589, overlap = 393.5
PHY-3002 : Step(177): len = 520212, overlap = 393.5
PHY-3002 : Step(178): len = 519720, overlap = 392.5
PHY-3002 : Step(179): len = 517061, overlap = 393.75
PHY-3002 : Step(180): len = 515921, overlap = 399.25
PHY-3002 : Step(181): len = 513967, overlap = 404.5
PHY-3002 : Step(182): len = 513133, overlap = 399.5
PHY-3002 : Step(183): len = 511314, overlap = 400.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000101036
PHY-3002 : Step(184): len = 515874, overlap = 390
PHY-3002 : Step(185): len = 520438, overlap = 376.5
PHY-3002 : Step(186): len = 523090, overlap = 373.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000202071
PHY-3002 : Step(187): len = 528123, overlap = 368.25
PHY-3002 : Step(188): len = 538055, overlap = 354.25
PHY-3002 : Step(189): len = 540364, overlap = 340
PHY-3002 : Step(190): len = 539516, overlap = 328.5
PHY-3002 : Step(191): len = 538885, overlap = 331
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.780496s wall, 0.906250s user + 0.750000s system = 1.656250s CPU (212.2%)

PHY-3001 : Trial Legalized: Len = 647888
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 598/19514.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 736032, over cnt = 2494(7%), over = 3968, worst = 9
PHY-1002 : len = 749456, over cnt = 1535(4%), over = 2181, worst = 6
PHY-1002 : len = 766168, over cnt = 672(1%), over = 872, worst = 5
PHY-1002 : len = 773784, over cnt = 345(0%), over = 450, worst = 5
PHY-1002 : len = 783912, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.105430s wall, 3.468750s user + 0.031250s system = 3.500000s CPU (166.2%)

PHY-1001 : Congestion index: top1 = 49.89, top5 = 45.36, top10 = 42.88, top15 = 41.14.
PHY-3001 : End congestion estimation;  2.493893s wall, 3.859375s user + 0.031250s system = 3.890625s CPU (156.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19512 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.964279s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (98.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000179946
PHY-3002 : Step(192): len = 605864, overlap = 79.5
PHY-3002 : Step(193): len = 587180, overlap = 125
PHY-3002 : Step(194): len = 575893, overlap = 163
PHY-3002 : Step(195): len = 568428, overlap = 199.25
PHY-3002 : Step(196): len = 564821, overlap = 229.25
PHY-3002 : Step(197): len = 562217, overlap = 240
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000359893
PHY-3002 : Step(198): len = 566611, overlap = 234.5
PHY-3002 : Step(199): len = 570720, overlap = 234
PHY-3002 : Step(200): len = 570205, overlap = 237.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000702052
PHY-3002 : Step(201): len = 573627, overlap = 232.5
PHY-3002 : Step(202): len = 582529, overlap = 228.25
PHY-3002 : Step(203): len = 586187, overlap = 230.25
PHY-3002 : Step(204): len = 585455, overlap = 232.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.033987s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (91.9%)

PHY-3001 : Legalized: Len = 626226, Over = 0
PHY-3001 : Spreading special nets. 51 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.088566s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (105.9%)

PHY-3001 : 78 instances has been re-located, deltaX = 26, deltaY = 38, maxDist = 2.
PHY-3001 : Final: Len = 627012, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68193, tnet num: 19512, tinst num: 8143, tnode num: 92403, tedge num: 112389.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.120631s wall, 2.093750s user + 0.031250s system = 2.125000s CPU (100.2%)

RUN-1004 : used memory is 613 MB, reserved memory is 613 MB, peak memory is 724 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3631/19514.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 723976, over cnt = 2263(6%), over = 3513, worst = 8
PHY-1002 : len = 735432, over cnt = 1274(3%), over = 1798, worst = 7
PHY-1002 : len = 751032, over cnt = 472(1%), over = 609, worst = 5
PHY-1002 : len = 754384, over cnt = 307(0%), over = 390, worst = 5
PHY-1002 : len = 761376, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.885273s wall, 3.125000s user + 0.015625s system = 3.140625s CPU (166.6%)

PHY-1001 : Congestion index: top1 = 48.21, top5 = 43.43, top10 = 40.99, top15 = 39.34.
PHY-1001 : End incremental global routing;  2.222694s wall, 3.453125s user + 0.015625s system = 3.468750s CPU (156.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19512 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.010972s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (98.9%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8080 has valid locations, 7 needs to be replaced
PHY-3001 : design contains 8149 instances, 8045 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 628383
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17610/19519.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 762384, over cnt = 11(0%), over = 13, worst = 2
PHY-1002 : len = 762400, over cnt = 4(0%), over = 5, worst = 2
PHY-1002 : len = 762416, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 762448, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 762480, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.737234s wall, 0.750000s user + 0.031250s system = 0.781250s CPU (106.0%)

PHY-1001 : Congestion index: top1 = 48.19, top5 = 43.44, top10 = 41.01, top15 = 39.36.
PHY-3001 : End congestion estimation;  1.064730s wall, 1.078125s user + 0.031250s system = 1.109375s CPU (104.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68228, tnet num: 19517, tinst num: 8149, tnode num: 92444, tedge num: 112429.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.123018s wall, 2.125000s user + 0.000000s system = 2.125000s CPU (100.1%)

RUN-1004 : used memory is 646 MB, reserved memory is 627 MB, peak memory is 724 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19517 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.137975s wall, 3.125000s user + 0.015625s system = 3.140625s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(205): len = 628338, overlap = 0
PHY-3002 : Step(206): len = 628343, overlap = 0
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17606/19519.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 762336, over cnt = 11(0%), over = 14, worst = 2
PHY-1002 : len = 762328, over cnt = 5(0%), over = 7, worst = 2
PHY-1002 : len = 762392, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 762408, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.546472s wall, 0.562500s user + 0.000000s system = 0.562500s CPU (102.9%)

PHY-1001 : Congestion index: top1 = 48.25, top5 = 43.47, top10 = 41.01, top15 = 39.36.
PHY-3001 : End congestion estimation;  0.872041s wall, 0.890625s user + 0.000000s system = 0.890625s CPU (102.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19517 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.022036s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (99.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000442223
PHY-3002 : Step(207): len = 628332, overlap = 0.75
PHY-3002 : Step(208): len = 628320, overlap = 0.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007511s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 628313, Over = 0
PHY-3001 : End spreading;  0.072708s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (107.4%)

PHY-3001 : Final: Len = 628313, Over = 0
PHY-3001 : End incremental placement;  6.724933s wall, 6.921875s user + 0.125000s system = 7.046875s CPU (104.8%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.501790s wall, 12.078125s user + 0.140625s system = 12.218750s CPU (116.3%)

OPT-1001 : Current memory(MB): used = 718, reserve = 703, peak = 724.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17606/19519.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 762408, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 762408, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 762424, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 762440, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.545423s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (100.3%)

PHY-1001 : Congestion index: top1 = 48.34, top5 = 43.49, top10 = 41.04, top15 = 39.38.
OPT-1001 : End congestion update;  0.876823s wall, 0.875000s user + 0.000000s system = 0.875000s CPU (99.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19517 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.824117s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (100.5%)

OPT-0007 : Start: WNS 4052 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.706097s wall, 1.703125s user + 0.000000s system = 1.703125s CPU (99.8%)

OPT-1001 : Current memory(MB): used = 718, reserve = 703, peak = 724.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19517 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.817229s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (99.4%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17616/19519.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 762440, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.122995s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (101.6%)

PHY-1001 : Congestion index: top1 = 48.34, top5 = 43.49, top10 = 41.04, top15 = 39.38.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19517 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.822356s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (100.7%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4052 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 47.896552
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4052ps with logic level 4 
RUN-1001 :       #2 path slack 4109ps with logic level 4 
OPT-1001 : End physical optimization;  16.687828s wall, 18.203125s user + 0.203125s system = 18.406250s CPU (110.3%)

RUN-1003 : finish command "place" in  71.844235s wall, 134.609375s user + 7.218750s system = 141.828125s CPU (197.4%)

RUN-1004 : used memory is 635 MB, reserved memory is 620 MB, peak memory is 724 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.779142s wall, 3.093750s user + 0.000000s system = 3.093750s CPU (173.9%)

RUN-1004 : used memory is 635 MB, reserved memory is 621 MB, peak memory is 724 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8151 instances
RUN-1001 : 4020 mslices, 4025 lslices, 59 pads, 42 brams, 0 dsps
RUN-1001 : There are total 19519 nets
RUN-1001 : 13722 nets have 2 pins
RUN-1001 : 4363 nets have [3 - 5] pins
RUN-1001 : 884 nets have [6 - 10] pins
RUN-1001 : 413 nets have [11 - 20] pins
RUN-1001 : 128 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68228, tnet num: 19517, tinst num: 8149, tnode num: 92444, tedge num: 112429.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.817838s wall, 1.812500s user + 0.015625s system = 1.828125s CPU (100.6%)

RUN-1004 : used memory is 618 MB, reserved memory is 597 MB, peak memory is 724 MB
PHY-1001 : 4020 mslices, 4025 lslices, 59 pads, 42 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19517 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 704368, over cnt = 2414(6%), over = 3912, worst = 8
PHY-1002 : len = 719312, over cnt = 1461(4%), over = 2037, worst = 6
PHY-1002 : len = 736352, over cnt = 569(1%), over = 794, worst = 5
PHY-1002 : len = 748048, over cnt = 65(0%), over = 92, worst = 4
PHY-1002 : len = 750032, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.893626s wall, 3.218750s user + 0.062500s system = 3.281250s CPU (173.3%)

PHY-1001 : Congestion index: top1 = 47.72, top5 = 43.27, top10 = 40.80, top15 = 39.14.
PHY-1001 : End global routing;  2.258024s wall, 3.593750s user + 0.062500s system = 3.656250s CPU (161.9%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 704, reserve = 691, peak = 724.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 969, reserve = 956, peak = 969.
PHY-1001 : End build detailed router design. 4.833097s wall, 4.796875s user + 0.031250s system = 4.828125s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 192928, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.972574s wall, 0.953125s user + 0.015625s system = 0.968750s CPU (99.6%)

PHY-1001 : Current memory(MB): used = 1005, reserve = 993, peak = 1005.
PHY-1001 : End phase 1; 0.980921s wall, 0.968750s user + 0.015625s system = 0.984375s CPU (100.4%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 55% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.77833e+06, over cnt = 1230(0%), over = 1238, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1021, reserve = 1008, peak = 1021.
PHY-1001 : End initial routed; 19.331924s wall, 48.718750s user + 0.453125s system = 49.171875s CPU (254.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18247(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.323   |   0.000   |   0   
RUN-1001 :   Hold   |   0.112   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.628668s wall, 3.609375s user + 0.015625s system = 3.625000s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 1035, reserve = 1022, peak = 1035.
PHY-1001 : End phase 2; 22.960762s wall, 52.328125s user + 0.468750s system = 52.796875s CPU (229.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.77833e+06, over cnt = 1230(0%), over = 1238, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.258598s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (96.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.76839e+06, over cnt = 418(0%), over = 418, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.673568s wall, 1.218750s user + 0.015625s system = 1.234375s CPU (183.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.76794e+06, over cnt = 59(0%), over = 59, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.405376s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (127.2%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.76842e+06, over cnt = 14(0%), over = 14, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.283403s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (115.8%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.76879e+06, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.224096s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (97.6%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.76882e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.219710s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (99.6%)

PHY-1001 : ===== DR Iter 6 =====
PHY-1022 : len = 1.76882e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 6; 0.289709s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (102.5%)

PHY-1001 : ===== DR Iter 7 =====
PHY-1022 : len = 1.76882e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 7; 0.413116s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (102.1%)

PHY-1001 : ===== DR Iter 8 =====
PHY-1022 : len = 1.76883e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 8; 0.184146s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (101.8%)

PHY-1001 : ==== DR Iter 9 ====
PHY-1022 : len = 1.76885e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 9; 0.185701s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (101.0%)

PHY-1001 : ==== DR Iter 10 ====
PHY-1022 : len = 1.76886e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 10; 0.167663s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (121.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18247(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.323   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.639116s wall, 3.640625s user + 0.000000s system = 3.640625s CPU (100.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 302 feed throughs used by 259 nets
PHY-1001 : End commit to database; 2.271025s wall, 2.265625s user + 0.000000s system = 2.265625s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 1123, reserve = 1113, peak = 1123.
PHY-1001 : End phase 3; 9.765808s wall, 10.468750s user + 0.031250s system = 10.500000s CPU (107.5%)

PHY-1003 : Routed, final wirelength = 1.76886e+06
PHY-1001 : Current memory(MB): used = 1128, reserve = 1118, peak = 1128.
PHY-1001 : End export database. 0.181099s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (94.9%)

PHY-1001 : End detail routing;  39.161156s wall, 69.171875s user + 0.546875s system = 69.718750s CPU (178.0%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68228, tnet num: 19517, tinst num: 8149, tnode num: 92444, tedge num: 112429.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.818602s wall, 1.812500s user + 0.000000s system = 1.812500s CPU (99.7%)

RUN-1004 : used memory is 1059 MB, reserved memory is 1060 MB, peak memory is 1128 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  47.850790s wall, 79.140625s user + 0.656250s system = 79.796875s CPU (166.8%)

RUN-1004 : used memory is 1058 MB, reserved memory is 1061 MB, peak memory is 1128 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8912   out of  19600   45.47%
#reg                    12447   out of  19600   63.51%
#le                     15076
  #lut only              2629   out of  15076   17.44%
  #reg only              6164   out of  15076   40.89%
  #lut&reg               6283   out of  15076   41.68%
#dsp                        0   out of     29    0.00%
#bram                      42   out of     64   65.62%
  #bram9k                  40
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       59   out of    188   31.38%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6802
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          170
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         F2        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         D1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         B3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         C1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         A3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         A2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         B2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         B1        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F15        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDZ        INPUT        K15        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         P2        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         N4        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         M5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         N6        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         N5        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         P5        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M3        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         M4        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         H2        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         M1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         L1        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         N1        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         N3        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         M2        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT         T5        LVCMOS33           8            NONE           OREG       
    Fog_ENY        OUTPUT         T4        LVCMOS33           8            NONE           OREG       
    Fog_ENZ        OUTPUT         T7        LVCMOS33           8            NONE           OREG       
    Fog_INT        OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         T6        LVCMOS33           8            N/A            OREG       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E1        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        G11        LVCMOS25           8            N/A            NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15076  |7410    |1502    |12491   |42      |0       |
|  AnyFog_dataX                      |AnyFog          |213    |88      |22      |175     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |91     |62      |22      |53      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |208    |71      |22      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |53      |22      |51      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |208    |97      |22      |169     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |57      |22      |47      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2918   |633     |39      |2832    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |61     |37      |5       |53      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |211    |78      |5       |199     |0       |0       |
|    STADOP_com2                     |STADOP          |553    |79      |0       |549     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |66     |42      |14      |42      |0       |0       |
|    head_com2                       |uniheading      |273    |96      |5       |259     |0       |0       |
|    rmc_com2                        |Gprmc           |36     |32      |0       |33      |0       |0       |
|    uart_com2                       |Agrica          |1410   |255     |10      |1389    |0       |0       |
|  COM3                              |COM3_Control    |276    |117     |19      |236     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |62     |35      |5       |53      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |61     |38      |14      |38      |0       |0       |
|    rmc_com3                        |Gprmc           |153    |44      |0       |145     |0       |0       |
|  DATA                              |Data_Processing |8857   |4633    |1122    |7054    |0       |0       |
|    DIV_Dtemp                       |Divider         |762    |325     |84      |630     |0       |0       |
|    DIV_Utemp                       |Divider         |590    |341     |84      |467     |0       |0       |
|    DIV_accX                        |Divider         |622    |399     |84      |493     |0       |0       |
|    DIV_accY                        |Divider         |635    |360     |102     |480     |0       |0       |
|    DIV_accZ                        |Divider         |659    |349     |132     |456     |0       |0       |
|    DIV_rateX                       |Divider         |704    |370     |132     |499     |0       |0       |
|    DIV_rateY                       |Divider         |622    |361     |132     |415     |0       |0       |
|    DIV_rateZ                       |Divider         |588    |335     |132     |380     |0       |0       |
|    genclk                          |genclk          |265    |170     |89      |104     |0       |0       |
|  FMC                               |FMC_Ctrl        |467    |416     |43      |347     |0       |0       |
|  IIC                               |I2C_master      |288    |255     |11      |253     |0       |0       |
|  IMU_CTRL                          |SCHA634         |903    |650     |61      |728     |0       |0       |
|    CtrlData                        |CtrlData        |478    |428     |47      |333     |0       |0       |
|      usms                          |Time_1ms        |29     |24      |5       |19      |0       |0       |
|    SPIM                            |SPI_SCHA634     |425    |222     |14      |395     |0       |0       |
|  POWER                             |POWER_EN        |93     |51      |38      |33      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |623    |387     |103     |425     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |623    |387     |103     |425     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |277    |163     |0       |260     |0       |0       |
|        reg_inst                    |register        |275    |161     |0       |258     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |346    |224     |103     |165     |0       |0       |
|        bus_inst                    |bus_top         |126    |78      |46      |44      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |24     |14      |10      |7       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |46     |26      |18      |13      |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |3      |3       |0       |3       |0       |0       |
|          BUS_DETECTOR[5]$bus_nodes |bus_det         |51     |33      |18      |19      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |142    |96      |29      |90      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13662  
    #2          2       3371   
    #3          3        678   
    #4          4        314   
    #5        5-10       955   
    #6        11-50      456   
    #7       51-100      14    
    #8       101-500      3    
    #9        >500        2    
  Average     2.14             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.118420s wall, 3.734375s user + 0.015625s system = 3.750000s CPU (177.0%)

RUN-1004 : used memory is 1059 MB, reserved memory is 1061 MB, peak memory is 1128 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68228, tnet num: 19517, tinst num: 8149, tnode num: 92444, tedge num: 112429.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.956297s wall, 1.953125s user + 0.000000s system = 1.953125s CPU (99.8%)

RUN-1004 : used memory is 1061 MB, reserved memory is 1063 MB, peak memory is 1128 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19517 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.550152s wall, 1.546875s user + 0.000000s system = 1.546875s CPU (99.8%)

RUN-1004 : used memory is 1067 MB, reserved memory is 1068 MB, peak memory is 1128 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: f3079da8db411685d3a4edefe8bb3dcbac6fc7d0ea3a55ab1cf6d41c3626be53 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8149
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19519, pip num: 149248
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 302
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3236 valid insts, and 417388 bits set as '1'.
BIT-1004 : the usercode register value: 00000000010100110011001100100101
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.934958s wall, 126.921875s user + 0.140625s system = 127.062500s CPU (982.3%)

RUN-1004 : used memory is 1196 MB, reserved memory is 1181 MB, peak memory is 1311 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250821_114129.log"

============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Wed Jun 18 10:05:57 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(516)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
RUN-1001 : Project manager successfully analyzed 21 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.561507s wall, 1.656250s user + 3.906250s system = 5.562500s CPU (100.0%)

RUN-1004 : used memory is 78 MB, reserved memory is 39 MB, peak memory is 90 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.739274s wall, 1.687500s user + 0.000000s system = 1.687500s CPU (97.0%)

RUN-1004 : used memory is 278 MB, reserved memory is 246 MB, peak memory is 281 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 58 trigger nets, 58 data nets.
KIT-1004 : Chipwatcher code = 1110111010000001
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=158) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=158) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=158)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=158)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 20439/23 useful/useless nets, 17162/12 useful/useless insts
SYN-1016 : Merged 28 instances.
SYN-1032 : 20044/20 useful/useless nets, 17669/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 478 better
SYN-1014 : Optimize round 2
SYN-1032 : 19660/45 useful/useless nets, 17285/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.484352s wall, 2.390625s user + 0.093750s system = 2.484375s CPU (100.0%)

RUN-1004 : used memory is 305 MB, reserved memory is 271 MB, peak memory is 307 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 19732/441 useful/useless nets, 17408/63 useful/useless insts
SYN-1016 : Merged 69 instances.
SYN-2571 : Optimize after map_dsp, round 1, 573 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 56 instances.
SYN-2501 : Optimize round 1, 114 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 20236/5 useful/useless nets, 17912/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 73807, tnet num: 20236, tinst num: 17911, tnode num: 101716, tedge num: 115694.
TMR-2508 : Levelizing timing graph completed, there are 85 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.202801s wall, 1.171875s user + 0.031250s system = 1.203125s CPU (100.0%)

RUN-1004 : used memory is 431 MB, reserved memory is 397 MB, peak memory is 431 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 20236 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 282 (3.29), #lev = 7 (1.66)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 282 (3.29), #lev = 7 (1.66)
SYN-3001 : Logic optimization runtime opt =   0.04 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 614 instances into 282 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 487 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 160 adder to BLE ...
SYN-4008 : Packed 160 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.507951s wall, 4.453125s user + 0.078125s system = 4.531250s CPU (100.5%)

RUN-1004 : used memory is 339 MB, reserved memory is 321 MB, peak memory is 526 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.332589s wall, 7.156250s user + 0.187500s system = 7.343750s CPU (100.2%)

RUN-1004 : used memory is 340 MB, reserved memory is 322 MB, peak memory is 526 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/uart_com2/AGRIC_data[7] will be merged to another kept net COM2/rmc_com2/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM2/uart_com2/AGRIC_data[6] will be merged to another kept net COM2/rmc_com2/GPRMC_data[6]
SYN-5055 WARNING: The kept net COM2/uart_com2/AGRIC_data[5] will be merged to another kept net COM2/rmc_com2/GPRMC_data[5]
SYN-5055 WARNING: The kept net COM2/uart_com2/AGRIC_data[4] will be merged to another kept net COM2/rmc_com2/GPRMC_data[4]
SYN-5055 WARNING: The kept net COM2/uart_com2/AGRIC_data[3] will be merged to another kept net COM2/rmc_com2/GPRMC_data[3]
SYN-5055 WARNING: The kept net COM2/uart_com2/AGRIC_data[2] will be merged to another kept net COM2/rmc_com2/GPRMC_data[2]
SYN-5055 WARNING: The kept net COM2/uart_com2/AGRIC_data[1] will be merged to another kept net COM2/rmc_com2/GPRMC_data[1]
SYN-5055 WARNING: The kept net COM2/uart_com2/AGRIC_data[0] will be merged to another kept net COM2/rmc_com2/GPRMC_data[0]
SYN-5055 WARNING: The kept net COM2/rx_err will be merged to another kept net COM2/rmc_com2/GPRMC_err
SYN-5055 WARNING: The kept net FMC/Error_state[0] will be merged to another kept net COM2/rmc_com2/GPRMC_sub_state_cnt0[3]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (335 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 17156 instances
RUN-0007 : 5217 luts, 10394 seqs, 943 mslices, 489 lslices, 56 pads, 52 brams, 0 dsps
RUN-1001 : There are total 19508 nets
RUN-1001 : 14420 nets have 2 pins
RUN-1001 : 3923 nets have [3 - 5] pins
RUN-1001 : 797 nets have [6 - 10] pins
RUN-1001 : 256 nets have [11 - 20] pins
RUN-1001 : 90 nets have [21 - 99] pins
RUN-1001 : 22 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4437     
RUN-1001 :   No   |  No   |  Yes  |     670     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    4680     
RUN-1001 :   Yes  |  No   |  Yes  |     507     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  105  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 113
PHY-3001 : Initial placement ...
PHY-3001 : design contains 17154 instances, 5217 luts, 10394 seqs, 1432 slices, 281 macros(1432 instances: 943 mslices 489 lslices)
PHY-3001 : Huge net DATA/done_div with 1324 pins
PHY-0007 : Cell area utilization is 53%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 72174, tnet num: 19506, tinst num: 17154, tnode num: 100058, tedge num: 114232.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.233163s wall, 1.218750s user + 0.015625s system = 1.234375s CPU (100.1%)

RUN-1004 : used memory is 486 MB, reserved memory is 456 MB, peak memory is 526 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 19506 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.078559s wall, 2.031250s user + 0.046875s system = 2.078125s CPU (100.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.13473e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 17154.
PHY-3001 : Level 1 #clusters 1811.
PHY-3001 : End clustering;  0.144194s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (97.5%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 53%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 790144, overlap = 583.875
PHY-3002 : Step(2): len = 709567, overlap = 653.531
PHY-3002 : Step(3): len = 468117, overlap = 841.812
PHY-3002 : Step(4): len = 403653, overlap = 916.219
PHY-3002 : Step(5): len = 318793, overlap = 1039.94
PHY-3002 : Step(6): len = 288294, overlap = 1081.66
PHY-3002 : Step(7): len = 242998, overlap = 1139.41
PHY-3002 : Step(8): len = 220633, overlap = 1180.06
PHY-3002 : Step(9): len = 198030, overlap = 1232.22
PHY-3002 : Step(10): len = 185517, overlap = 1255.25
PHY-3002 : Step(11): len = 167116, overlap = 1291.66
PHY-3002 : Step(12): len = 160943, overlap = 1340.19
PHY-3002 : Step(13): len = 144718, overlap = 1386.12
PHY-3002 : Step(14): len = 135772, overlap = 1400
PHY-3002 : Step(15): len = 122142, overlap = 1424.88
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.87546e-07
PHY-3002 : Step(16): len = 124494, overlap = 1405.44
PHY-3002 : Step(17): len = 148743, overlap = 1359.53
PHY-3002 : Step(18): len = 158667, overlap = 1295.72
PHY-3002 : Step(19): len = 167628, overlap = 1202.06
PHY-3002 : Step(20): len = 167613, overlap = 1177.94
PHY-3002 : Step(21): len = 164371, overlap = 1163.03
PHY-3002 : Step(22): len = 162161, overlap = 1166.78
PHY-3002 : Step(23): len = 159296, overlap = 1162.72
PHY-3002 : Step(24): len = 157996, overlap = 1150.22
PHY-3002 : Step(25): len = 156292, overlap = 1137.78
PHY-3002 : Step(26): len = 154379, overlap = 1139.75
PHY-3002 : Step(27): len = 153270, overlap = 1140.28
PHY-3002 : Step(28): len = 151933, overlap = 1129.41
PHY-3002 : Step(29): len = 150792, overlap = 1122.66
PHY-3002 : Step(30): len = 148829, overlap = 1119.88
PHY-3002 : Step(31): len = 147928, overlap = 1102.19
PHY-3002 : Step(32): len = 146454, overlap = 1101.53
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 1.57509e-06
PHY-3002 : Step(33): len = 154249, overlap = 1072.75
PHY-3002 : Step(34): len = 167484, overlap = 1020.06
PHY-3002 : Step(35): len = 169155, overlap = 969.812
PHY-3002 : Step(36): len = 172596, overlap = 927.469
PHY-3002 : Step(37): len = 173558, overlap = 915.062
PHY-3002 : Step(38): len = 174330, overlap = 905.844
PHY-3002 : Step(39): len = 172264, overlap = 924.531
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 3.15018e-06
PHY-3002 : Step(40): len = 180516, overlap = 907.969
PHY-3002 : Step(41): len = 192520, overlap = 844.969
PHY-3002 : Step(42): len = 197399, overlap = 779.5
PHY-3002 : Step(43): len = 200531, overlap = 734.875
PHY-3002 : Step(44): len = 200576, overlap = 747.625
PHY-3002 : Step(45): len = 200356, overlap = 732.375
PHY-3002 : Step(46): len = 199502, overlap = 726.656
PHY-3002 : Step(47): len = 199558, overlap = 716.031
PHY-3002 : Step(48): len = 199265, overlap = 715.562
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 6.30036e-06
PHY-3002 : Step(49): len = 206852, overlap = 679.062
PHY-3002 : Step(50): len = 220116, overlap = 593.25
PHY-3002 : Step(51): len = 224630, overlap = 580.5
PHY-3002 : Step(52): len = 227687, overlap = 574.375
PHY-3002 : Step(53): len = 228837, overlap = 566
PHY-3002 : Step(54): len = 228716, overlap = 556.438
PHY-3002 : Step(55): len = 228308, overlap = 543.875
PHY-3002 : Step(56): len = 228298, overlap = 540.188
PHY-3002 : Step(57): len = 226869, overlap = 525.344
PHY-3002 : Step(58): len = 226621, overlap = 524.156
PHY-3002 : Step(59): len = 226905, overlap = 528.25
PHY-3002 : Step(60): len = 227902, overlap = 540.812
PHY-3002 : Step(61): len = 226623, overlap = 552.75
PHY-3002 : Step(62): len = 225381, overlap = 566.781
PHY-3002 : Step(63): len = 224893, overlap = 554.094
PHY-3002 : Step(64): len = 224338, overlap = 556.094
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.26007e-05
PHY-3002 : Step(65): len = 234670, overlap = 500.938
PHY-3002 : Step(66): len = 244342, overlap = 461.25
PHY-3002 : Step(67): len = 248771, overlap = 421.375
PHY-3002 : Step(68): len = 251005, overlap = 402.531
PHY-3002 : Step(69): len = 250782, overlap = 408.219
PHY-3002 : Step(70): len = 250185, overlap = 424.75
PHY-3002 : Step(71): len = 248414, overlap = 433.906
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 2.52015e-05
PHY-3002 : Step(72): len = 258881, overlap = 405.125
PHY-3002 : Step(73): len = 270113, overlap = 363.25
PHY-3002 : Step(74): len = 274682, overlap = 353.25
PHY-3002 : Step(75): len = 275467, overlap = 337.594
PHY-3002 : Step(76): len = 273851, overlap = 337.969
PHY-3002 : Step(77): len = 272007, overlap = 338.562
PHY-3002 : Step(78): len = 270032, overlap = 333.406
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 5.04029e-05
PHY-3002 : Step(79): len = 277229, overlap = 319.125
PHY-3002 : Step(80): len = 286265, overlap = 278.781
PHY-3002 : Step(81): len = 290106, overlap = 278.125
PHY-3002 : Step(82): len = 290845, overlap = 281.5
PHY-3002 : Step(83): len = 289597, overlap = 278.469
PHY-3002 : Step(84): len = 289459, overlap = 274.875
PHY-3002 : Step(85): len = 288426, overlap = 279
PHY-3002 : Step(86): len = 289732, overlap = 277.844
PHY-3002 : Step(87): len = 288051, overlap = 274.5
PHY-3002 : Step(88): len = 288196, overlap = 277.844
PHY-3002 : Step(89): len = 286474, overlap = 275.344
PHY-3002 : Step(90): len = 286501, overlap = 280.531
PHY-3002 : Step(91): len = 285832, overlap = 268.781
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000100806
PHY-3002 : Step(92): len = 291325, overlap = 248.406
PHY-3002 : Step(93): len = 298066, overlap = 236.594
PHY-3002 : Step(94): len = 300666, overlap = 220.656
PHY-3002 : Step(95): len = 301730, overlap = 221.531
PHY-3002 : Step(96): len = 301025, overlap = 207
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000201612
PHY-3002 : Step(97): len = 303617, overlap = 208
PHY-3002 : Step(98): len = 310375, overlap = 199.156
PHY-3002 : Step(99): len = 314449, overlap = 181.344
PHY-3002 : Step(100): len = 314892, overlap = 178.219
PHY-3002 : Step(101): len = 314330, overlap = 174.938
PHY-3002 : Step(102): len = 313865, overlap = 178.906
PHY-3002 : Step(103): len = 313737, overlap = 173.219
PHY-3002 : Step(104): len = 313923, overlap = 173.844
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000335434
PHY-3002 : Step(105): len = 315750, overlap = 170.5
PHY-3002 : Step(106): len = 318615, overlap = 165.875
PHY-3002 : Step(107): len = 320131, overlap = 161.969
PHY-3002 : Step(108): len = 323596, overlap = 163.594
PHY-3002 : Step(109): len = 325763, overlap = 162.562
PHY-3002 : Step(110): len = 326685, overlap = 160.75
PHY-3002 : Step(111): len = 324575, overlap = 164.688
PHY-3002 : Step(112): len = 323123, overlap = 169.062
PHY-3002 : Step(113): len = 321466, overlap = 177.375
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.013855s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (112.8%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 60%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/19508.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 420288, over cnt = 1200(3%), over = 5313, worst = 33
PHY-1001 : End global iterations;  0.725331s wall, 1.125000s user + 0.062500s system = 1.187500s CPU (163.7%)

PHY-1001 : Congestion index: top1 = 67.84, top5 = 50.15, top10 = 41.63, top15 = 36.42.
PHY-3001 : End congestion estimation;  0.963215s wall, 1.343750s user + 0.062500s system = 1.406250s CPU (146.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19506 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.877961s wall, 0.875000s user + 0.015625s system = 0.890625s CPU (101.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.63565e-05
PHY-3002 : Step(114): len = 359947, overlap = 82.5312
PHY-3002 : Step(115): len = 366996, overlap = 72.3125
PHY-3002 : Step(116): len = 365889, overlap = 67.6875
PHY-3002 : Step(117): len = 365451, overlap = 68.125
PHY-3002 : Step(118): len = 368739, overlap = 64.4375
PHY-3002 : Step(119): len = 370739, overlap = 59.4375
PHY-3002 : Step(120): len = 373390, overlap = 57.3125
PHY-3002 : Step(121): len = 375644, overlap = 53.9062
PHY-3002 : Step(122): len = 376692, overlap = 48.2188
PHY-3002 : Step(123): len = 376890, overlap = 47.5625
PHY-3002 : Step(124): len = 379212, overlap = 48.5938
PHY-3002 : Step(125): len = 379373, overlap = 50.4375
PHY-3002 : Step(126): len = 379292, overlap = 50.8438
PHY-3002 : Step(127): len = 380156, overlap = 56.5312
PHY-3002 : Step(128): len = 380322, overlap = 59.1875
PHY-3002 : Step(129): len = 381291, overlap = 64.375
PHY-3002 : Step(130): len = 382125, overlap = 67.0938
PHY-3002 : Step(131): len = 381268, overlap = 67.125
PHY-3002 : Step(132): len = 381802, overlap = 69.0312
PHY-3002 : Step(133): len = 380989, overlap = 74.0312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000192713
PHY-3002 : Step(134): len = 380809, overlap = 76.75
PHY-3002 : Step(135): len = 381754, overlap = 75.6562
PHY-3002 : Step(136): len = 382528, overlap = 76.4688
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000343129
PHY-3002 : Step(137): len = 388429, overlap = 77.5625
PHY-3002 : Step(138): len = 398988, overlap = 73.5938
PHY-3002 : Step(139): len = 403271, overlap = 72.7188
PHY-3002 : Step(140): len = 407275, overlap = 72.375
PHY-3002 : Step(141): len = 407718, overlap = 69.625
PHY-3002 : Step(142): len = 408787, overlap = 71.9062
PHY-3002 : Step(143): len = 409487, overlap = 74
PHY-3002 : Step(144): len = 410706, overlap = 73.3125
PHY-3002 : Step(145): len = 408304, overlap = 77.3125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000686259
PHY-3002 : Step(146): len = 408636, overlap = 71.5625
PHY-3002 : Step(147): len = 411440, overlap = 67.1562
PHY-3002 : Step(148): len = 413854, overlap = 63.4375
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00123339
PHY-3002 : Step(149): len = 415781, overlap = 62.7188
PHY-3002 : Step(150): len = 423142, overlap = 61.3125
PHY-3002 : Step(151): len = 432870, overlap = 60.7812
PHY-3002 : Step(152): len = 433619, overlap = 60.2812
PHY-3002 : Step(153): len = 433129, overlap = 61.9688
PHY-3002 : Step(154): len = 432483, overlap = 61.8438
PHY-3002 : Step(155): len = 432267, overlap = 61.7188
PHY-3002 : Step(156): len = 432382, overlap = 59.9688
PHY-3002 : Step(157): len = 431249, overlap = 57.8438
PHY-3002 : Step(158): len = 431310, overlap = 59.6875
PHY-3002 : Step(159): len = 431450, overlap = 60.9688
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(160): len = 431651, overlap = 61.875
PHY-3002 : Step(161): len = 434513, overlap = 59.8125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 60%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 88/19508.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 489640, over cnt = 1910(5%), over = 9001, worst = 42
PHY-1001 : End global iterations;  0.874232s wall, 1.390625s user + 0.093750s system = 1.484375s CPU (169.8%)

PHY-1001 : Congestion index: top1 = 73.64, top5 = 55.82, top10 = 48.00, top15 = 43.09.
PHY-3001 : End congestion estimation;  1.134687s wall, 1.656250s user + 0.093750s system = 1.750000s CPU (154.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19506 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.897934s wall, 0.890625s user + 0.000000s system = 0.890625s CPU (99.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000125071
PHY-3002 : Step(162): len = 437621, overlap = 285.344
PHY-3002 : Step(163): len = 437910, overlap = 246.156
PHY-3002 : Step(164): len = 430447, overlap = 218.094
PHY-3002 : Step(165): len = 424880, overlap = 198.25
PHY-3002 : Step(166): len = 420642, overlap = 181.844
PHY-3002 : Step(167): len = 417037, overlap = 153.188
PHY-3002 : Step(168): len = 411662, overlap = 156.219
PHY-3002 : Step(169): len = 407494, overlap = 149.031
PHY-3002 : Step(170): len = 405471, overlap = 145.688
PHY-3002 : Step(171): len = 402758, overlap = 154.062
PHY-3002 : Step(172): len = 399050, overlap = 154.812
PHY-3002 : Step(173): len = 396411, overlap = 152.562
PHY-3002 : Step(174): len = 394367, overlap = 156.562
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000250142
PHY-3002 : Step(175): len = 393365, overlap = 148.938
PHY-3002 : Step(176): len = 394702, overlap = 143.594
PHY-3002 : Step(177): len = 396035, overlap = 135.781
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000500284
PHY-3002 : Step(178): len = 398322, overlap = 128.875
PHY-3002 : Step(179): len = 404613, overlap = 112.906
PHY-3002 : Step(180): len = 407586, overlap = 107.312
PHY-3002 : Step(181): len = 406946, overlap = 104.312
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00100057
PHY-3002 : Step(182): len = 407873, overlap = 101.531
PHY-3002 : Step(183): len = 411948, overlap = 92.875
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 72174, tnet num: 19506, tinst num: 17154, tnode num: 100058, tedge num: 114232.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.427005s wall, 1.390625s user + 0.031250s system = 1.421875s CPU (99.6%)

RUN-1004 : used memory is 527 MB, reserved memory is 499 MB, peak memory is 643 MB
OPT-1001 : Total overflow 445.41 peak overflow 3.25
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 288/19508.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 482384, over cnt = 2107(5%), over = 7051, worst = 21
PHY-1001 : End global iterations;  0.994461s wall, 1.609375s user + 0.093750s system = 1.703125s CPU (171.3%)

PHY-1001 : Congestion index: top1 = 52.26, top5 = 43.66, top10 = 39.06, top15 = 36.12.
PHY-1001 : End incremental global routing;  1.225885s wall, 1.828125s user + 0.093750s system = 1.921875s CPU (156.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19506 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.952676s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (100.0%)

OPT-1001 : 13 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 56 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 17082 has valid locations, 187 needs to be replaced
PHY-3001 : design contains 17328 instances, 5266 luts, 10519 seqs, 1432 slices, 281 macros(1432 instances: 943 mslices 489 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 422393
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 61%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 15209/19682.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 491000, over cnt = 2110(5%), over = 7054, worst = 21
PHY-1001 : End global iterations;  0.161059s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (174.6%)

PHY-1001 : Congestion index: top1 = 52.54, top5 = 43.84, top10 = 39.23, top15 = 36.30.
PHY-3001 : End congestion estimation;  0.395337s wall, 0.484375s user + 0.015625s system = 0.500000s CPU (126.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 72757, tnet num: 19680, tinst num: 17328, tnode num: 100952, tedge num: 115050.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.414620s wall, 1.406250s user + 0.031250s system = 1.437500s CPU (101.6%)

RUN-1004 : used memory is 564 MB, reserved memory is 541 MB, peak memory is 644 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19680 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.387673s wall, 2.375000s user + 0.031250s system = 2.406250s CPU (100.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(184): len = 422169, overlap = 0
PHY-3002 : Step(185): len = 423513, overlap = 0
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 61%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 15247/19682.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 491184, over cnt = 2137(6%), over = 7106, worst = 21
PHY-1001 : End global iterations;  0.162605s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (124.9%)

PHY-1001 : Congestion index: top1 = 52.84, top5 = 44.12, top10 = 39.48, top15 = 36.51.
PHY-3001 : End congestion estimation;  0.458894s wall, 0.500000s user + 0.015625s system = 0.515625s CPU (112.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19680 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.932681s wall, 0.890625s user + 0.015625s system = 0.906250s CPU (97.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000741039
PHY-3002 : Step(186): len = 423900, overlap = 94.5312
PHY-3002 : Step(187): len = 425001, overlap = 93.9062
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00148208
PHY-3002 : Step(188): len = 425149, overlap = 93.75
PHY-3002 : Step(189): len = 425623, overlap = 93.6875
PHY-3001 : Final: Len = 425623, Over = 93.6875
PHY-3001 : End incremental placement;  4.906713s wall, 5.328125s user + 0.203125s system = 5.531250s CPU (112.7%)

OPT-1001 : Total overflow 448.69 peak overflow 3.25
OPT-1001 : End high-fanout net optimization;  7.595016s wall, 8.703125s user + 0.296875s system = 9.000000s CPU (118.5%)

OPT-1001 : Current memory(MB): used = 647, reserve = 624, peak = 661.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 15214/19682.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 492728, over cnt = 2102(5%), over = 6718, worst = 21
PHY-1002 : len = 531000, over cnt = 1323(3%), over = 2951, worst = 14
PHY-1002 : len = 554520, over cnt = 559(1%), over = 1132, worst = 12
PHY-1002 : len = 568728, over cnt = 80(0%), over = 142, worst = 9
PHY-1002 : len = 570736, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.198507s wall, 1.718750s user + 0.046875s system = 1.765625s CPU (147.3%)

PHY-1001 : Congestion index: top1 = 45.54, top5 = 39.87, top10 = 36.94, top15 = 34.97.
OPT-1001 : End congestion update;  1.441680s wall, 1.968750s user + 0.046875s system = 2.015625s CPU (139.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19680 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.954271s wall, 0.937500s user + 0.015625s system = 0.953125s CPU (99.9%)

OPT-0007 : Start: WNS 4295 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.401241s wall, 2.921875s user + 0.062500s system = 2.984375s CPU (124.3%)

OPT-1001 : Current memory(MB): used = 625, reserve = 602, peak = 661.
OPT-1001 : End physical optimization;  11.718506s wall, 13.468750s user + 0.390625s system = 13.859375s CPU (118.3%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5266 LUT to BLE ...
SYN-4008 : Packed 5266 LUT and 2595 SEQ to BLE.
SYN-4003 : Packing 7924 remaining SEQ's ...
SYN-4005 : Packed 2985 SEQ with LUT/SLICE
SYN-4006 : 186 single LUT's are left
SYN-4006 : 4939 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 10205/11942 primitive instances ...
PHY-3001 : End packing;  2.444172s wall, 2.437500s user + 0.000000s system = 2.437500s CPU (99.7%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 7076 instances
RUN-1001 : 3482 mslices, 3481 lslices, 56 pads, 52 brams, 0 dsps
RUN-1001 : There are total 17136 nets
RUN-1001 : 11860 nets have 2 pins
RUN-1001 : 3987 nets have [3 - 5] pins
RUN-1001 : 839 nets have [6 - 10] pins
RUN-1001 : 327 nets have [11 - 20] pins
RUN-1001 : 113 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
PHY-3001 : design contains 7074 instances, 6963 slices, 281 macros(1432 instances: 943 mslices 489 lslices)
PHY-3001 : Cell area utilization is 76%
PHY-3001 : After packing: Len = 441315, Over = 277.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 76%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 6799/17136.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 550288, over cnt = 1266(3%), over = 1984, worst = 7
PHY-1002 : len = 555056, over cnt = 819(2%), over = 1152, worst = 7
PHY-1002 : len = 565448, over cnt = 250(0%), over = 330, worst = 7
PHY-1002 : len = 569352, over cnt = 62(0%), over = 72, worst = 3
PHY-1002 : len = 570656, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.140248s wall, 2.015625s user + 0.062500s system = 2.078125s CPU (182.3%)

PHY-1001 : Congestion index: top1 = 46.34, top5 = 40.50, top10 = 37.06, top15 = 34.88.
PHY-3001 : End congestion estimation;  1.453509s wall, 2.328125s user + 0.062500s system = 2.390625s CPU (164.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 60071, tnet num: 17134, tinst num: 7074, tnode num: 80621, tedge num: 99724.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.628792s wall, 1.593750s user + 0.031250s system = 1.625000s CPU (99.8%)

RUN-1004 : used memory is 554 MB, reserved memory is 543 MB, peak memory is 661 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 17134 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.481896s wall, 2.421875s user + 0.062500s system = 2.484375s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.32307e-05
PHY-3002 : Step(190): len = 447364, overlap = 263.5
PHY-3002 : Step(191): len = 447939, overlap = 269.25
PHY-3002 : Step(192): len = 448484, overlap = 276
PHY-3002 : Step(193): len = 449795, overlap = 289
PHY-3002 : Step(194): len = 447538, overlap = 288
PHY-3002 : Step(195): len = 446882, overlap = 297.75
PHY-3002 : Step(196): len = 444548, overlap = 301.75
PHY-3002 : Step(197): len = 443482, overlap = 293.5
PHY-3002 : Step(198): len = 442155, overlap = 293.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000106461
PHY-3002 : Step(199): len = 447455, overlap = 280
PHY-3002 : Step(200): len = 452401, overlap = 273.75
PHY-3002 : Step(201): len = 452151, overlap = 275.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(202): len = 461399, overlap = 258.5
PHY-3002 : Step(203): len = 469003, overlap = 246
PHY-3002 : Step(204): len = 466769, overlap = 248.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.966201s wall, 1.156250s user + 1.140625s system = 2.296875s CPU (237.7%)

PHY-3001 : Trial Legalized: Len = 556225
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 75%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 535/17136.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 635024, over cnt = 2021(5%), over = 3313, worst = 7
PHY-1002 : len = 647848, over cnt = 1226(3%), over = 1728, worst = 6
PHY-1002 : len = 663384, over cnt = 348(0%), over = 485, worst = 4
PHY-1002 : len = 668984, over cnt = 79(0%), over = 111, worst = 4
PHY-1002 : len = 670640, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.680961s wall, 2.828125s user + 0.062500s system = 2.890625s CPU (172.0%)

PHY-1001 : Congestion index: top1 = 44.53, top5 = 40.50, top10 = 38.16, top15 = 36.61.
PHY-3001 : End congestion estimation;  2.022834s wall, 3.156250s user + 0.062500s system = 3.218750s CPU (159.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 17134 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.845098s wall, 0.812500s user + 0.015625s system = 0.828125s CPU (98.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000168434
PHY-3002 : Step(205): len = 519427, overlap = 52
PHY-3002 : Step(206): len = 503848, overlap = 82.5
PHY-3002 : Step(207): len = 494568, overlap = 111
PHY-3002 : Step(208): len = 489353, overlap = 136.5
PHY-3002 : Step(209): len = 486635, overlap = 154.5
PHY-3002 : Step(210): len = 484961, overlap = 167.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000336869
PHY-3002 : Step(211): len = 489400, overlap = 158
PHY-3002 : Step(212): len = 494418, overlap = 150
PHY-3002 : Step(213): len = 497160, overlap = 152.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(214): len = 500225, overlap = 151.5
PHY-3002 : Step(215): len = 507336, overlap = 142.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.035443s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (88.2%)

PHY-3001 : Legalized: Len = 538863, Over = 0
PHY-3001 : Spreading special nets. 35 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.075400s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (103.6%)

PHY-3001 : 48 instances has been re-located, deltaX = 10, deltaY = 31, maxDist = 2.
PHY-3001 : Final: Len = 539433, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 60071, tnet num: 17134, tinst num: 7074, tnode num: 80621, tedge num: 99724.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.786959s wall, 1.781250s user + 0.015625s system = 1.796875s CPU (100.6%)

RUN-1004 : used memory is 568 MB, reserved memory is 568 MB, peak memory is 661 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3692/17136.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 630632, over cnt = 1854(5%), over = 2920, worst = 7
PHY-1002 : len = 642456, over cnt = 957(2%), over = 1265, worst = 7
PHY-1002 : len = 654432, over cnt = 271(0%), over = 340, worst = 5
PHY-1002 : len = 658992, over cnt = 26(0%), over = 34, worst = 3
PHY-1002 : len = 659672, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.530787s wall, 2.718750s user + 0.000000s system = 2.718750s CPU (177.6%)

PHY-1001 : Congestion index: top1 = 44.25, top5 = 39.66, top10 = 37.20, top15 = 35.58.
PHY-1001 : End incremental global routing;  1.820796s wall, 3.000000s user + 0.000000s system = 3.000000s CPU (164.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 17134 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.857833s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (98.4%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 56 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 7014 has valid locations, 6 needs to be replaced
PHY-3001 : design contains 7079 instances, 6968 slices, 281 macros(1432 instances: 943 mslices 489 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 540268
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 76%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 15316/17140.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 660376, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 660368, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 660400, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.378787s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (99.0%)

PHY-1001 : Congestion index: top1 = 44.25, top5 = 39.64, top10 = 37.21, top15 = 35.59.
PHY-3001 : End congestion estimation;  0.992973s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (100.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 60095, tnet num: 17138, tinst num: 7079, tnode num: 80655, tedge num: 99757.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.665653s wall, 1.656250s user + 0.015625s system = 1.671875s CPU (100.4%)

RUN-1004 : used memory is 588 MB, reserved memory is 570 MB, peak memory is 661 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 17138 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.403345s wall, 2.375000s user + 0.031250s system = 2.406250s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(216): len = 540212, overlap = 0
PHY-3002 : Step(217): len = 540205, overlap = 0
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 76%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 15317/17140.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 660224, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 660232, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 660272, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 660272, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.423430s wall, 0.437500s user + 0.046875s system = 0.484375s CPU (114.4%)

PHY-1001 : Congestion index: top1 = 44.22, top5 = 39.64, top10 = 37.20, top15 = 35.58.
PHY-3001 : End congestion estimation;  0.672656s wall, 0.687500s user + 0.046875s system = 0.734375s CPU (109.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 17138 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.726457s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (98.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000524344
PHY-3002 : Step(218): len = 540177, overlap = 1.25
PHY-3002 : Step(219): len = 540222, overlap = 0.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005376s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (290.6%)

PHY-3001 : Legalized: Len = 540222, Over = 0
PHY-3001 : End spreading;  0.053219s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.1%)

PHY-3001 : Final: Len = 540222, Over = 0
PHY-3001 : End incremental placement;  6.414201s wall, 6.312500s user + 0.250000s system = 6.562500s CPU (102.3%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  9.646186s wall, 10.718750s user + 0.250000s system = 10.968750s CPU (113.7%)

OPT-1001 : Current memory(MB): used = 656, reserve = 636, peak = 661.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 15314/17140.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 660296, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 660304, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 660304, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.317745s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (98.3%)

PHY-1001 : Congestion index: top1 = 44.25, top5 = 39.64, top10 = 37.19, top15 = 35.57.
OPT-1001 : End congestion update;  0.571119s wall, 0.578125s user + 0.000000s system = 0.578125s CPU (101.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 17138 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.626347s wall, 0.625000s user + 0.000000s system = 0.625000s CPU (99.8%)

OPT-0007 : Start: WNS 4235 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.200886s wall, 1.203125s user + 0.000000s system = 1.203125s CPU (100.2%)

OPT-1001 : Current memory(MB): used = 656, reserve = 636, peak = 661.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 17138 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.611885s wall, 0.609375s user + 0.000000s system = 0.609375s CPU (99.6%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 15321/17140.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 660304, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.100488s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (108.8%)

PHY-1001 : Congestion index: top1 = 44.25, top5 = 39.64, top10 = 37.19, top15 = 35.57.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 17138 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.606836s wall, 0.609375s user + 0.000000s system = 0.609375s CPU (100.4%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4235 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 43.793103
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4235ps with logic level 5 
RUN-1001 :       #2 path slack 4321ps with logic level 5 
RUN-1001 :       #3 path slack 4322ps with logic level 5 
OPT-1001 : End physical optimization;  14.406599s wall, 15.468750s user + 0.265625s system = 15.734375s CPU (109.2%)

RUN-1003 : finish command "place" in  62.837881s wall, 120.328125s user + 8.390625s system = 128.718750s CPU (204.8%)

RUN-1004 : used memory is 548 MB, reserved memory is 521 MB, peak memory is 661 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.394939s wall, 2.406250s user + 0.000000s system = 2.406250s CPU (172.5%)

RUN-1004 : used memory is 548 MB, reserved memory is 522 MB, peak memory is 661 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 7081 instances
RUN-1001 : 3487 mslices, 3481 lslices, 56 pads, 52 brams, 0 dsps
RUN-1001 : There are total 17140 nets
RUN-1001 : 11859 nets have 2 pins
RUN-1001 : 3987 nets have [3 - 5] pins
RUN-1001 : 842 nets have [6 - 10] pins
RUN-1001 : 329 nets have [11 - 20] pins
RUN-1001 : 113 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 60095, tnet num: 17138, tinst num: 7079, tnode num: 80655, tedge num: 99757.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.486210s wall, 1.468750s user + 0.000000s system = 1.468750s CPU (98.8%)

RUN-1004 : used memory is 565 MB, reserved memory is 551 MB, peak memory is 661 MB
PHY-1001 : 3487 mslices, 3481 lslices, 56 pads, 52 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 17138 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 612992, over cnt = 1978(5%), over = 3299, worst = 7
PHY-1002 : len = 628448, over cnt = 1087(3%), over = 1499, worst = 6
PHY-1002 : len = 643256, over cnt = 276(0%), over = 369, worst = 6
PHY-1002 : len = 648688, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.335088s wall, 2.406250s user + 0.062500s system = 2.468750s CPU (184.9%)

PHY-1001 : Congestion index: top1 = 43.51, top5 = 39.37, top10 = 36.88, top15 = 35.24.
PHY-1001 : End global routing;  1.626194s wall, 2.687500s user + 0.062500s system = 2.750000s CPU (169.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 640, reserve = 627, peak = 661.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 914, reserve = 899, peak = 914.
PHY-1001 : End build detailed router design. 4.401824s wall, 4.359375s user + 0.031250s system = 4.390625s CPU (99.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 181664, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.819722s wall, 0.812500s user + 0.015625s system = 0.828125s CPU (101.0%)

PHY-1001 : Current memory(MB): used = 949, reserve = 935, peak = 949.
PHY-1001 : End phase 1; 0.832475s wall, 0.812500s user + 0.015625s system = 0.828125s CPU (99.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 53% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 75% nets.
PHY-1001 : Routed 92% nets.
PHY-1022 : len = 1.63945e+06, over cnt = 943(0%), over = 946, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 960, reserve = 944, peak = 960.
PHY-1001 : End initial routed; 17.472829s wall, 45.765625s user + 0.406250s system = 46.171875s CPU (264.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/15931(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.303   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 2.768106s wall, 2.765625s user + 0.000000s system = 2.765625s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 967, reserve = 951, peak = 967.
PHY-1001 : End phase 2; 20.241081s wall, 48.531250s user + 0.406250s system = 48.937500s CPU (241.8%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.63945e+06, over cnt = 943(0%), over = 946, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.197468s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (102.9%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.63236e+06, over cnt = 300(0%), over = 301, worst = 2, crit = 0
PHY-1001 : End DR Iter 1; 0.472523s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (198.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.63335e+06, over cnt = 80(0%), over = 81, worst = 2, crit = 0
PHY-1001 : End DR Iter 2; 0.230899s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (135.3%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.6343e+06, over cnt = 9(0%), over = 9, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.186799s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (100.4%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.63456e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 4; 0.135236s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (92.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/15931(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.303   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 2.755001s wall, 2.734375s user + 0.000000s system = 2.734375s CPU (99.3%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 214 feed throughs used by 189 nets
PHY-1001 : End commit to database; 1.886202s wall, 1.828125s user + 0.046875s system = 1.875000s CPU (99.4%)

PHY-1001 : Current memory(MB): used = 1050, reserve = 1036, peak = 1050.
PHY-1001 : End phase 3; 6.274598s wall, 6.765625s user + 0.046875s system = 6.812500s CPU (108.6%)

PHY-1003 : Routed, final wirelength = 1.63456e+06
PHY-1001 : Current memory(MB): used = 1053, reserve = 1040, peak = 1053.
PHY-1001 : End export database. 0.047436s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.8%)

PHY-1001 : End detail routing;  32.161149s wall, 60.875000s user + 0.500000s system = 61.375000s CPU (190.8%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 60095, tnet num: 17138, tinst num: 7079, tnode num: 80655, tedge num: 99757.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.410506s wall, 1.421875s user + 0.000000s system = 1.421875s CPU (100.8%)

RUN-1004 : used memory is 992 MB, reserved memory is 983 MB, peak memory is 1053 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  38.760606s wall, 68.484375s user + 0.593750s system = 69.078125s CPU (178.2%)

RUN-1004 : used memory is 992 MB, reserved memory is 984 MB, peak memory is 1053 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8160   out of  19600   41.63%
#reg                    10614   out of  19600   54.15%
#le                     13099
  #lut only              2485   out of  13099   18.97%
  #reg only              4939   out of  13099   37.71%
  #lut&reg               5675   out of  13099   43.32%
#dsp                        0   out of     29    0.00%
#bram                      52   out of     64   81.25%
  #bram9k                  52
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       56   out of    188   29.79%
  #ireg                    10
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    5750
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          194
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT        E11        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT        A13        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT        F10        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         E8        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         E7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT        A10        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C6        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         D6        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         C7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT        E12        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         F1        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         A7        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H13        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J11        LVCMOS33          N/A          PULLUP          IREG       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT        E13        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         R7        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         M7        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         P7        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         T8        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         P8        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         N8        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M9        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         T9        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         R9        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         T6        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         P9        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         N9        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT        P11        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         T5        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         B2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        L10        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        E15        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        R14        LVCMOS33           8            N/A            OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT         R2        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT        N12        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        B10        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         J6        LVCMOS33           8            N/A            NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        R15        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E6        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |13099  |6728    |1432    |10657   |52      |0       |
|  AnyFog_dataX                      |AnyFog          |227    |112     |22      |188     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |93     |65      |22      |54      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |221    |144     |22      |184     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |85     |63      |22      |50      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |218    |131     |22      |182     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |84     |53      |22      |48      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |1792   |413     |34      |1742    |0       |0       |
|    STADOP_com2                     |STADOP          |583    |109     |5       |574     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |45      |14      |43      |0       |0       |
|    head_com2                       |uniheading      |268    |76      |5       |259     |0       |0       |
|    rmc_com2                        |Gprmc           |31     |24      |0       |31      |0       |0       |
|    uart_com2                       |Agrica          |845    |159     |10      |835     |0       |0       |
|  COM3                              |COM3_Control    |208    |135     |14      |184     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |61     |46      |14      |39      |0       |0       |
|    rmc_com3                        |Gprmc           |147    |89      |0       |145     |0       |0       |
|  DATA                              |Data_Processing |7932   |4033    |1059    |6252    |0       |0       |
|    DIV_Dtemp                       |Divider         |766    |312     |84      |640     |0       |0       |
|    DIV_Utemp                       |Divider         |608    |307     |84      |466     |0       |0       |
|    DIV_accX                        |Divider         |658    |312     |84      |530     |0       |0       |
|    DIV_accY                        |Divider         |660    |327     |108     |494     |0       |0       |
|    DIV_accZ                        |Divider         |654    |371     |132     |448     |0       |0       |
|    DIV_rateX                       |Divider         |679    |423     |132     |473     |0       |0       |
|    DIV_rateY                       |Divider         |583    |365     |132     |381     |0       |0       |
|    DIV_rateZ                       |Divider         |580    |358     |132     |375     |0       |0       |
|    genclk                          |genclk          |86     |61      |20      |53      |0       |0       |
|  FMC                               |FMC_Ctrl        |492    |455     |30      |335     |0       |0       |
|  IIC                               |I2C_master      |297    |230     |11      |268     |0       |0       |
|  IMU_CTRL                          |SCHA634         |879    |604     |61      |736     |0       |0       |
|    CtrlData                        |CtrlData        |453    |399     |47      |338     |0       |0       |
|      usms                          |Time_1ms        |28     |22      |5       |20      |0       |0       |
|    SPIM                            |SPI_SCHA634     |426    |205     |14      |398     |0       |0       |
|  POWER                             |POWER_EN        |96     |48      |38      |36      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |733    |423     |119     |503     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |733    |423     |119     |503     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |334    |173     |0       |317     |0       |0       |
|        reg_inst                    |register        |332    |171     |0       |315     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |399    |250     |119     |186     |0       |0       |
|        bus_inst                    |bus_top         |176    |114     |62      |64      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |26     |16      |10      |10      |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |50     |32      |18      |18      |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |100    |66      |34      |36      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |145    |99      |29      |92      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       11802  
    #2          2       3066   
    #3          3        674   
    #4          4        247   
    #5        5-10       913   
    #6        11-50      352   
    #7       51-100      19    
    #8       101-500      6    
  Average     2.16             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  1.735017s wall, 2.953125s user + 0.015625s system = 2.968750s CPU (171.1%)

RUN-1004 : used memory is 993 MB, reserved memory is 984 MB, peak memory is 1053 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 60095, tnet num: 17138, tinst num: 7079, tnode num: 80655, tedge num: 99757.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.429298s wall, 1.437500s user + 0.000000s system = 1.437500s CPU (100.6%)

RUN-1004 : used memory is 994 MB, reserved memory is 986 MB, peak memory is 1053 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 17138 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.110194s wall, 1.093750s user + 0.015625s system = 1.109375s CPU (99.9%)

RUN-1004 : used memory is 1000 MB, reserved memory is 990 MB, peak memory is 1053 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 28b752c1c0e7d7ba92ac5beed3c46754b7b56ac64969e4b0149cfdbc1d295011 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 7079
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 17140, pip num: 132774
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 214
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3234 valid insts, and 374446 bits set as '1'.
BIT-1004 : the usercode register value: 00000000000000001110111010000001
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  9.840703s wall, 97.875000s user + 0.218750s system = 98.093750s CPU (996.8%)

RUN-1004 : used memory is 1117 MB, reserved memory is 1099 MB, peak memory is 1232 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250618_100557.log"

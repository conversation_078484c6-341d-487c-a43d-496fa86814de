============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Tue Aug 19 20:00:34 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.321684s wall, 1.359375s user + 3.968750s system = 5.328125s CPU (100.1%)

RUN-1004 : used memory is 78 MB, reserved memory is 40 MB, peak memory is 90 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.775998s wall, 1.687500s user + 0.078125s system = 1.765625s CPU (99.4%)

RUN-1004 : used memory is 302 MB, reserved memory is 271 MB, peak memory is 305 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 2 view nodes, 24 trigger nets, 24 data nets.
KIT-1004 : Chipwatcher code = 1010010110001101
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=78) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=78) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=78)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=78)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22395/12 useful/useless nets, 19354/4 useful/useless insts
SYN-1016 : Merged 17 instances.
SYN-1032 : 22171/16 useful/useless nets, 19678/12 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 2 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 2 mux instances.
SYN-1015 : Optimize round 1, 279 better
SYN-1014 : Optimize round 2
SYN-1032 : 21962/30 useful/useless nets, 19469/32 useful/useless insts
SYN-1015 : Optimize round 2, 64 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  1.994120s wall, 1.968750s user + 0.031250s system = 2.000000s CPU (100.3%)

RUN-1004 : used memory is 326 MB, reserved memory is 293 MB, peak memory is 328 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 21986/155 useful/useless nets, 19514/29 useful/useless insts
SYN-1016 : Merged 21 instances.
SYN-2571 : Optimize after map_dsp, round 1, 205 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 24 instances.
SYN-2501 : Optimize round 1, 50 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 14 macro adder
SYN-1019 : Optimized 9 mux instances.
SYN-1016 : Merged 10 instances.
SYN-1032 : 22357/5 useful/useless nets, 19885/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81090, tnet num: 22357, tinst num: 19884, tnode num: 113763, tedge num: 126844.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.129671s wall, 1.125000s user + 0.015625s system = 1.140625s CPU (101.0%)

RUN-1004 : used memory is 463 MB, reserved memory is 432 MB, peak memory is 463 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22357 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 173 (3.57), #lev = 7 (1.79)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 173 (3.57), #lev = 7 (1.79)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 387 instances into 173 LUTs, name keeping = 77%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 290 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 108 adder to BLE ...
SYN-4008 : Packed 108 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  3.945208s wall, 3.875000s user + 0.078125s system = 3.953125s CPU (100.2%)

RUN-1004 : used memory is 349 MB, reserved memory is 313 MB, peak memory is 571 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  6.242134s wall, 6.093750s user + 0.171875s system = 6.265625s CPU (100.4%)

RUN-1004 : used memory is 349 MB, reserved memory is 314 MB, peak memory is 571 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (177 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19233 instances
RUN-0007 : 5565 luts, 12091 seqs, 973 mslices, 515 lslices, 60 pads, 24 brams, 0 dsps
RUN-1001 : There are total 21713 nets
RUN-1001 : 16363 nets have 2 pins
RUN-1001 : 4173 nets have [3 - 5] pins
RUN-1001 : 815 nets have [6 - 10] pins
RUN-1001 : 237 nets have [11 - 20] pins
RUN-1001 : 107 nets have [21 - 99] pins
RUN-1001 : 18 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4792     
RUN-1001 :   No   |  No   |  Yes  |     681     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     349     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19231 instances, 5565 luts, 12091 seqs, 1488 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 79733, tnet num: 21711, tinst num: 19231, tnode num: 112080, tedge num: 125348.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.191253s wall, 1.187500s user + 0.000000s system = 1.187500s CPU (99.7%)

RUN-1004 : used memory is 522 MB, reserved memory is 494 MB, peak memory is 571 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21711 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.999717s wall, 2.000000s user + 0.000000s system = 2.000000s CPU (100.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.55059e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19231.
PHY-3001 : Level 1 #clusters 2135.
PHY-3001 : End clustering;  0.131782s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (142.3%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 867451, overlap = 585.219
PHY-3002 : Step(2): len = 783004, overlap = 635.562
PHY-3002 : Step(3): len = 507801, overlap = 809.75
PHY-3002 : Step(4): len = 441706, overlap = 880.25
PHY-3002 : Step(5): len = 356111, overlap = 971.438
PHY-3002 : Step(6): len = 309039, overlap = 1032.94
PHY-3002 : Step(7): len = 259213, overlap = 1126.19
PHY-3002 : Step(8): len = 237118, overlap = 1184.53
PHY-3002 : Step(9): len = 205927, overlap = 1225.97
PHY-3002 : Step(10): len = 190085, overlap = 1258.81
PHY-3002 : Step(11): len = 170919, overlap = 1296.88
PHY-3002 : Step(12): len = 158204, overlap = 1324.88
PHY-3002 : Step(13): len = 144385, overlap = 1348
PHY-3002 : Step(14): len = 140037, overlap = 1370.41
PHY-3002 : Step(15): len = 128894, overlap = 1389
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.45036e-06
PHY-3002 : Step(16): len = 134869, overlap = 1362.59
PHY-3002 : Step(17): len = 189744, overlap = 1233.69
PHY-3002 : Step(18): len = 202267, overlap = 1097.5
PHY-3002 : Step(19): len = 208709, overlap = 1037.91
PHY-3002 : Step(20): len = 202450, overlap = 988.062
PHY-3002 : Step(21): len = 197468, overlap = 977.844
PHY-3002 : Step(22): len = 190217, overlap = 953.062
PHY-3002 : Step(23): len = 185760, overlap = 949.531
PHY-3002 : Step(24): len = 179204, overlap = 927.906
PHY-3002 : Step(25): len = 177420, overlap = 931.688
PHY-3002 : Step(26): len = 173904, overlap = 929.5
PHY-3002 : Step(27): len = 171678, overlap = 940.938
PHY-3002 : Step(28): len = 169535, overlap = 959.312
PHY-3002 : Step(29): len = 168373, overlap = 960.469
PHY-3002 : Step(30): len = 168276, overlap = 954.719
PHY-3002 : Step(31): len = 167688, overlap = 967.938
PHY-3002 : Step(32): len = 167118, overlap = 980.281
PHY-3002 : Step(33): len = 165718, overlap = 994.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.90072e-06
PHY-3002 : Step(34): len = 173724, overlap = 969.844
PHY-3002 : Step(35): len = 187058, overlap = 884.75
PHY-3002 : Step(36): len = 190534, overlap = 861.562
PHY-3002 : Step(37): len = 192285, overlap = 853.219
PHY-3002 : Step(38): len = 192618, overlap = 854.062
PHY-3002 : Step(39): len = 192730, overlap = 858.594
PHY-3002 : Step(40): len = 192357, overlap = 862.688
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 5.80145e-06
PHY-3002 : Step(41): len = 200602, overlap = 810.812
PHY-3002 : Step(42): len = 215734, overlap = 684.469
PHY-3002 : Step(43): len = 221744, overlap = 630.062
PHY-3002 : Step(44): len = 224220, overlap = 626.406
PHY-3002 : Step(45): len = 224194, overlap = 644
PHY-3002 : Step(46): len = 223007, overlap = 688.438
PHY-3002 : Step(47): len = 221141, overlap = 684.938
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 1.16029e-05
PHY-3002 : Step(48): len = 233126, overlap = 638.625
PHY-3002 : Step(49): len = 246351, overlap = 576.375
PHY-3002 : Step(50): len = 250849, overlap = 576.656
PHY-3002 : Step(51): len = 252639, overlap = 559.5
PHY-3002 : Step(52): len = 250346, overlap = 545.781
PHY-3002 : Step(53): len = 248939, overlap = 553.062
PHY-3002 : Step(54): len = 247713, overlap = 540.844
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 2.32058e-05
PHY-3002 : Step(55): len = 258772, overlap = 505.281
PHY-3002 : Step(56): len = 271076, overlap = 450.781
PHY-3002 : Step(57): len = 276283, overlap = 425.5
PHY-3002 : Step(58): len = 277119, overlap = 407.531
PHY-3002 : Step(59): len = 274740, overlap = 409.906
PHY-3002 : Step(60): len = 272828, overlap = 402.625
PHY-3002 : Step(61): len = 271192, overlap = 411.5
PHY-3002 : Step(62): len = 270742, overlap = 395.625
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 4.64116e-05
PHY-3002 : Step(63): len = 278738, overlap = 374.281
PHY-3002 : Step(64): len = 289239, overlap = 310.719
PHY-3002 : Step(65): len = 294115, overlap = 283.125
PHY-3002 : Step(66): len = 295344, overlap = 277.812
PHY-3002 : Step(67): len = 294094, overlap = 310.781
PHY-3002 : Step(68): len = 293200, overlap = 311.938
PHY-3002 : Step(69): len = 291828, overlap = 304.531
PHY-3002 : Step(70): len = 292031, overlap = 313.094
PHY-3002 : Step(71): len = 290837, overlap = 301.156
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 9.28232e-05
PHY-3002 : Step(72): len = 297086, overlap = 275.719
PHY-3002 : Step(73): len = 304763, overlap = 238
PHY-3002 : Step(74): len = 307717, overlap = 223.594
PHY-3002 : Step(75): len = 308867, overlap = 226.469
PHY-3002 : Step(76): len = 307796, overlap = 220.156
PHY-3002 : Step(77): len = 306435, overlap = 227.938
PHY-3002 : Step(78): len = 305094, overlap = 235.125
PHY-3002 : Step(79): len = 305832, overlap = 229.75
PHY-3002 : Step(80): len = 306371, overlap = 231.062
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000185646
PHY-3002 : Step(81): len = 310034, overlap = 222.719
PHY-3002 : Step(82): len = 314969, overlap = 229.406
PHY-3002 : Step(83): len = 317497, overlap = 205.75
PHY-3002 : Step(84): len = 319649, overlap = 189.094
PHY-3002 : Step(85): len = 319098, overlap = 182.594
PHY-3002 : Step(86): len = 318914, overlap = 160.938
PHY-3002 : Step(87): len = 318096, overlap = 171.438
PHY-3002 : Step(88): len = 319295, overlap = 163.719
PHY-3002 : Step(89): len = 318097, overlap = 163.5
PHY-3002 : Step(90): len = 318987, overlap = 173.25
PHY-3002 : Step(91): len = 318681, overlap = 176.062
PHY-3002 : Step(92): len = 318780, overlap = 171.969
PHY-3002 : Step(93): len = 317681, overlap = 166.438
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000371293
PHY-3002 : Step(94): len = 320281, overlap = 165.625
PHY-3002 : Step(95): len = 323916, overlap = 159.25
PHY-3002 : Step(96): len = 324854, overlap = 162.469
PHY-3002 : Step(97): len = 326465, overlap = 164.5
PHY-3002 : Step(98): len = 326645, overlap = 173.656
PHY-3002 : Step(99): len = 326731, overlap = 155.312
PHY-3002 : Step(100): len = 325647, overlap = 155.906
PHY-3002 : Step(101): len = 326151, overlap = 144.5
PHY-3002 : Step(102): len = 327044, overlap = 152.281
PHY-3002 : Step(103): len = 326345, overlap = 159.875
PHY-3002 : Step(104): len = 326262, overlap = 161.844
PHY-3002 : Step(105): len = 326097, overlap = 167.75
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(106): len = 327273, overlap = 168.75
PHY-3002 : Step(107): len = 328735, overlap = 165.188
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.012052s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (259.3%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21713.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 424392, over cnt = 1130(3%), over = 5132, worst = 39
PHY-1001 : End global iterations;  0.762438s wall, 1.015625s user + 0.062500s system = 1.078125s CPU (141.4%)

PHY-1001 : Congestion index: top1 = 75.11, top5 = 52.11, top10 = 42.14, top15 = 36.57.
PHY-3001 : End congestion estimation;  0.972432s wall, 1.218750s user + 0.062500s system = 1.281250s CPU (131.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21711 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.865596s wall, 0.859375s user + 0.015625s system = 0.875000s CPU (101.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000100682
PHY-3002 : Step(108): len = 368482, overlap = 144.844
PHY-3002 : Step(109): len = 380718, overlap = 130.188
PHY-3002 : Step(110): len = 382326, overlap = 117.312
PHY-3002 : Step(111): len = 381929, overlap = 106
PHY-3002 : Step(112): len = 386297, overlap = 95.9688
PHY-3002 : Step(113): len = 391233, overlap = 93.9062
PHY-3002 : Step(114): len = 396330, overlap = 94.5938
PHY-3002 : Step(115): len = 398496, overlap = 102.5
PHY-3002 : Step(116): len = 401235, overlap = 101.125
PHY-3002 : Step(117): len = 407567, overlap = 105.062
PHY-3002 : Step(118): len = 407148, overlap = 104.25
PHY-3002 : Step(119): len = 406903, overlap = 110.469
PHY-3002 : Step(120): len = 406700, overlap = 111.188
PHY-3002 : Step(121): len = 407574, overlap = 111.656
PHY-3002 : Step(122): len = 408521, overlap = 116.688
PHY-3002 : Step(123): len = 407970, overlap = 114.031
PHY-3002 : Step(124): len = 408625, overlap = 112.688
PHY-3002 : Step(125): len = 408616, overlap = 112.719
PHY-3002 : Step(126): len = 409259, overlap = 109.906
PHY-3002 : Step(127): len = 409759, overlap = 113
PHY-3002 : Step(128): len = 410505, overlap = 112.625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000201364
PHY-3002 : Step(129): len = 410696, overlap = 111.938
PHY-3002 : Step(130): len = 411665, overlap = 111.344
PHY-3002 : Step(131): len = 413794, overlap = 110.625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000394784
PHY-3002 : Step(132): len = 416590, overlap = 103.219
PHY-3002 : Step(133): len = 423413, overlap = 95.25
PHY-3002 : Step(134): len = 427082, overlap = 96.5
PHY-3002 : Step(135): len = 430013, overlap = 101.781
PHY-3002 : Step(136): len = 432723, overlap = 105
PHY-3002 : Step(137): len = 433487, overlap = 101.406
PHY-3002 : Step(138): len = 435387, overlap = 101.688
PHY-3002 : Step(139): len = 438221, overlap = 98.5625
PHY-3002 : Step(140): len = 437128, overlap = 92.0938
PHY-3002 : Step(141): len = 436806, overlap = 88.375
PHY-3002 : Step(142): len = 436133, overlap = 91.1875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(143): len = 435870, overlap = 85.7188
PHY-3002 : Step(144): len = 439154, overlap = 87.625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 40/21713.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 506784, over cnt = 2174(6%), over = 9752, worst = 48
PHY-1001 : End global iterations;  0.978441s wall, 1.593750s user + 0.046875s system = 1.640625s CPU (167.7%)

PHY-1001 : Congestion index: top1 = 74.87, top5 = 57.21, top10 = 49.05, top15 = 44.14.
PHY-3001 : End congestion estimation;  1.242967s wall, 1.859375s user + 0.046875s system = 1.906250s CPU (153.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21711 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.891132s wall, 0.875000s user + 0.031250s system = 0.906250s CPU (101.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000103908
PHY-3002 : Step(145): len = 447857, overlap = 322.562
PHY-3002 : Step(146): len = 453724, overlap = 282.906
PHY-3002 : Step(147): len = 449429, overlap = 256.344
PHY-3002 : Step(148): len = 444836, overlap = 240.75
PHY-3002 : Step(149): len = 443886, overlap = 220
PHY-3002 : Step(150): len = 441346, overlap = 222.812
PHY-3002 : Step(151): len = 438843, overlap = 211.031
PHY-3002 : Step(152): len = 438772, overlap = 215.469
PHY-3002 : Step(153): len = 436805, overlap = 221.812
PHY-3002 : Step(154): len = 435939, overlap = 215.219
PHY-3002 : Step(155): len = 434277, overlap = 206.281
PHY-3002 : Step(156): len = 432444, overlap = 203.562
PHY-3002 : Step(157): len = 431223, overlap = 196.312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000207816
PHY-3002 : Step(158): len = 431401, overlap = 190.281
PHY-3002 : Step(159): len = 433305, overlap = 189.406
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000392131
PHY-3002 : Step(160): len = 435479, overlap = 187.094
PHY-3002 : Step(161): len = 446075, overlap = 157.938
PHY-3002 : Step(162): len = 451031, overlap = 146.125
PHY-3002 : Step(163): len = 449379, overlap = 144.156
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000784261
PHY-3002 : Step(164): len = 450151, overlap = 138
PHY-3002 : Step(165): len = 452184, overlap = 135.281
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 79733, tnet num: 21711, tinst num: 19231, tnode num: 112080, tedge num: 125348.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.439209s wall, 1.421875s user + 0.031250s system = 1.453125s CPU (101.0%)

RUN-1004 : used memory is 561 MB, reserved memory is 535 MB, peak memory is 693 MB
OPT-1001 : Total overflow 510.34 peak overflow 3.25
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 622/21713.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 531040, over cnt = 2413(6%), over = 8445, worst = 31
PHY-1001 : End global iterations;  1.137113s wall, 1.828125s user + 0.000000s system = 1.828125s CPU (160.8%)

PHY-1001 : Congestion index: top1 = 56.70, top5 = 47.37, top10 = 42.47, top15 = 39.36.
PHY-1001 : End incremental global routing;  1.370837s wall, 2.062500s user + 0.000000s system = 2.062500s CPU (150.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21711 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.877280s wall, 0.843750s user + 0.031250s system = 0.875000s CPU (99.7%)

OPT-1001 : 18 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19150 has valid locations, 225 needs to be replaced
PHY-3001 : design contains 19438 instances, 5656 luts, 12207 seqs, 1488 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 467035
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16930/21920.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 541240, over cnt = 2429(6%), over = 8512, worst = 31
PHY-1001 : End global iterations;  0.181965s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (111.6%)

PHY-1001 : Congestion index: top1 = 57.16, top5 = 47.63, top10 = 42.64, top15 = 39.55.
PHY-3001 : End congestion estimation;  0.400211s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (105.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80401, tnet num: 21918, tinst num: 19438, tnode num: 113013, tedge num: 126270.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.411562s wall, 1.375000s user + 0.031250s system = 1.406250s CPU (99.6%)

RUN-1004 : used memory is 605 MB, reserved memory is 594 MB, peak memory is 694 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21918 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.338582s wall, 2.296875s user + 0.046875s system = 2.343750s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(166): len = 467336, overlap = 4.5
PHY-3002 : Step(167): len = 468348, overlap = 4.4375
PHY-3002 : Step(168): len = 468999, overlap = 4.5625
PHY-3002 : Step(169): len = 469508, overlap = 4.6875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 16960/21920.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 541176, over cnt = 2456(6%), over = 8584, worst = 31
PHY-1001 : End global iterations;  0.164425s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (123.5%)

PHY-1001 : Congestion index: top1 = 57.48, top5 = 47.98, top10 = 42.90, top15 = 39.76.
PHY-3001 : End congestion estimation;  0.434479s wall, 0.437500s user + 0.031250s system = 0.468750s CPU (107.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21918 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.921593s wall, 0.890625s user + 0.031250s system = 0.921875s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000636447
PHY-3002 : Step(170): len = 469471, overlap = 136.812
PHY-3002 : Step(171): len = 469699, overlap = 137.062
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00127289
PHY-3002 : Step(172): len = 469990, overlap = 136.25
PHY-3002 : Step(173): len = 470234, overlap = 136.469
PHY-3001 : Final: Len = 470234, Over = 136.469
PHY-3001 : End incremental placement;  4.920616s wall, 4.953125s user + 0.312500s system = 5.265625s CPU (107.0%)

OPT-1001 : Total overflow 514.47 peak overflow 3.25
OPT-1001 : End high-fanout net optimization;  7.627325s wall, 8.343750s user + 0.343750s system = 8.687500s CPU (113.9%)

OPT-1001 : Current memory(MB): used = 699, reserve = 679, peak = 715.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16958/21920.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 543448, over cnt = 2422(6%), over = 8109, worst = 31
PHY-1002 : len = 586304, over cnt = 1645(4%), over = 4135, worst = 19
PHY-1002 : len = 619048, over cnt = 672(1%), over = 1606, worst = 12
PHY-1002 : len = 631120, over cnt = 333(0%), over = 837, worst = 12
PHY-1002 : len = 645320, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.043468s wall, 1.718750s user + 0.031250s system = 1.750000s CPU (167.7%)

PHY-1001 : Congestion index: top1 = 49.33, top5 = 43.30, top10 = 39.96, top15 = 37.94.
OPT-1001 : End congestion update;  1.263674s wall, 1.937500s user + 0.031250s system = 1.968750s CPU (155.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 21918 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.808881s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (98.5%)

OPT-0007 : Start: WNS 3887 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.078278s wall, 2.750000s user + 0.031250s system = 2.781250s CPU (133.8%)

OPT-1001 : Current memory(MB): used = 697, reserve = 677, peak = 715.
OPT-1001 : End physical optimization;  11.480211s wall, 12.843750s user + 0.406250s system = 13.250000s CPU (115.4%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5656 LUT to BLE ...
SYN-4008 : Packed 5656 LUT and 2730 SEQ to BLE.
SYN-4003 : Packing 9477 remaining SEQ's ...
SYN-4005 : Packed 3356 SEQ with LUT/SLICE
SYN-4006 : 100 single LUT's are left
SYN-4006 : 6121 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11777/13618 primitive instances ...
PHY-3001 : End packing;  2.656687s wall, 2.656250s user + 0.000000s system = 2.656250s CPU (100.0%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8049 instances
RUN-1001 : 3980 mslices, 3980 lslices, 60 pads, 24 brams, 0 dsps
RUN-1001 : There are total 19242 nets
RUN-1001 : 13555 nets have 2 pins
RUN-1001 : 4287 nets have [3 - 5] pins
RUN-1001 : 879 nets have [6 - 10] pins
RUN-1001 : 382 nets have [11 - 20] pins
RUN-1001 : 130 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8047 instances, 7960 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 487550, Over = 352.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7654/19242.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 611768, over cnt = 1525(4%), over = 2454, worst = 8
PHY-1002 : len = 617376, over cnt = 1038(2%), over = 1448, worst = 8
PHY-1002 : len = 627832, over cnt = 456(1%), over = 589, worst = 6
PHY-1002 : len = 634344, over cnt = 140(0%), over = 172, worst = 5
PHY-1002 : len = 637840, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.082553s wall, 1.906250s user + 0.015625s system = 1.921875s CPU (177.5%)

PHY-1001 : Congestion index: top1 = 50.39, top5 = 43.55, top10 = 39.92, top15 = 37.57.
PHY-3001 : End congestion estimation;  1.375524s wall, 2.187500s user + 0.015625s system = 2.203125s CPU (160.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66699, tnet num: 19240, tinst num: 8047, tnode num: 90453, tedge num: 109948.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.643481s wall, 1.625000s user + 0.031250s system = 1.656250s CPU (100.8%)

RUN-1004 : used memory is 593 MB, reserved memory is 575 MB, peak memory is 715 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19240 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.485197s wall, 2.453125s user + 0.046875s system = 2.500000s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.06776e-05
PHY-3002 : Step(174): len = 489523, overlap = 330.75
PHY-3002 : Step(175): len = 488485, overlap = 339.25
PHY-3002 : Step(176): len = 490985, overlap = 357.75
PHY-3002 : Step(177): len = 491799, overlap = 378
PHY-3002 : Step(178): len = 489446, overlap = 381.75
PHY-3002 : Step(179): len = 488373, overlap = 382.5
PHY-3002 : Step(180): len = 485619, overlap = 380.5
PHY-3002 : Step(181): len = 483899, overlap = 378
PHY-3002 : Step(182): len = 482110, overlap = 375.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000101355
PHY-3002 : Step(183): len = 486148, overlap = 364.75
PHY-3002 : Step(184): len = 489652, overlap = 356.5
PHY-3002 : Step(185): len = 490238, overlap = 357.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00020271
PHY-3002 : Step(186): len = 496684, overlap = 347
PHY-3002 : Step(187): len = 506898, overlap = 330.25
PHY-3002 : Step(188): len = 506839, overlap = 330.25
PHY-3002 : Step(189): len = 506431, overlap = 327.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.601614s wall, 0.781250s user + 0.703125s system = 1.484375s CPU (246.7%)

PHY-3001 : Trial Legalized: Len = 609950
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 572/19242.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 693184, over cnt = 2255(6%), over = 3772, worst = 7
PHY-1002 : len = 708840, over cnt = 1329(3%), over = 1842, worst = 5
PHY-1002 : len = 722328, over cnt = 593(1%), over = 798, worst = 5
PHY-1002 : len = 731752, over cnt = 189(0%), over = 223, worst = 5
PHY-1002 : len = 735616, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.768929s wall, 3.078125s user + 0.015625s system = 3.093750s CPU (174.9%)

PHY-1001 : Congestion index: top1 = 49.48, top5 = 44.67, top10 = 41.81, top15 = 39.97.
PHY-3001 : End congestion estimation;  2.085175s wall, 3.390625s user + 0.031250s system = 3.421875s CPU (164.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19240 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.785442s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000190361
PHY-3002 : Step(190): len = 568468, overlap = 76.25
PHY-3002 : Step(191): len = 549447, overlap = 124.25
PHY-3002 : Step(192): len = 537892, overlap = 171.75
PHY-3002 : Step(193): len = 531003, overlap = 207.5
PHY-3002 : Step(194): len = 527181, overlap = 233
PHY-3002 : Step(195): len = 525322, overlap = 243.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000380722
PHY-3002 : Step(196): len = 529876, overlap = 238
PHY-3002 : Step(197): len = 534069, overlap = 228.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000761444
PHY-3002 : Step(198): len = 535797, overlap = 226.5
PHY-3002 : Step(199): len = 543930, overlap = 216.25
PHY-3002 : Step(200): len = 546348, overlap = 218
PHY-3002 : Step(201): len = 547123, overlap = 218.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.029886s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (52.3%)

PHY-3001 : Legalized: Len = 586210, Over = 0
PHY-3001 : Spreading special nets. 30 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.068373s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (114.3%)

PHY-3001 : 43 instances has been re-located, deltaX = 12, deltaY = 30, maxDist = 2.
PHY-3001 : Final: Len = 586716, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66699, tnet num: 19240, tinst num: 8047, tnode num: 90453, tedge num: 109948.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.829439s wall, 1.843750s user + 0.000000s system = 1.843750s CPU (100.8%)

RUN-1004 : used memory is 599 MB, reserved memory is 591 MB, peak memory is 715 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 4100/19242.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 681000, over cnt = 2107(5%), over = 3360, worst = 8
PHY-1002 : len = 693496, over cnt = 1240(3%), over = 1678, worst = 8
PHY-1002 : len = 709632, over cnt = 347(0%), over = 435, worst = 6
PHY-1002 : len = 716064, over cnt = 51(0%), over = 60, worst = 3
PHY-1002 : len = 717160, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.472404s wall, 2.609375s user + 0.015625s system = 2.625000s CPU (178.3%)

PHY-1001 : Congestion index: top1 = 47.63, top5 = 42.78, top10 = 40.11, top15 = 38.39.
PHY-1001 : End incremental global routing;  1.745625s wall, 2.890625s user + 0.015625s system = 2.906250s CPU (166.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19240 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.811087s wall, 0.781250s user + 0.031250s system = 0.812500s CPU (100.2%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 7983 has valid locations, 17 needs to be replaced
PHY-3001 : design contains 8063 instances, 7976 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 588159
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17289/19257.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 718744, over cnt = 24(0%), over = 27, worst = 2
PHY-1002 : len = 718776, over cnt = 15(0%), over = 15, worst = 1
PHY-1002 : len = 718848, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 718976, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.515203s wall, 0.515625s user + 0.015625s system = 0.531250s CPU (103.1%)

PHY-1001 : Congestion index: top1 = 47.78, top5 = 42.83, top10 = 40.20, top15 = 38.48.
PHY-3001 : End congestion estimation;  0.800654s wall, 0.796875s user + 0.015625s system = 0.812500s CPU (101.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66794, tnet num: 19255, tinst num: 8063, tnode num: 90564, tedge num: 110058.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.799408s wall, 1.796875s user + 0.000000s system = 1.796875s CPU (99.9%)

RUN-1004 : used memory is 625 MB, reserved memory is 602 MB, peak memory is 715 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19255 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.625152s wall, 2.609375s user + 0.015625s system = 2.625000s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(202): len = 588252, overlap = 0.5
PHY-3002 : Step(203): len = 588276, overlap = 0.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17286/19257.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 718664, over cnt = 17(0%), over = 18, worst = 2
PHY-1002 : len = 718664, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 718728, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 718784, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.496732s wall, 0.500000s user + 0.000000s system = 0.500000s CPU (100.7%)

PHY-1001 : Congestion index: top1 = 47.89, top5 = 42.91, top10 = 40.22, top15 = 38.51.
PHY-3001 : End congestion estimation;  0.786277s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (103.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19255 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.801118s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000424765
PHY-3002 : Step(204): len = 588322, overlap = 0.5
PHY-3002 : Step(205): len = 588397, overlap = 0.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005201s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 588461, Over = 0
PHY-3001 : End spreading;  0.059253s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.5%)

PHY-3001 : Final: Len = 588461, Over = 0
PHY-3001 : End incremental placement;  5.647424s wall, 5.718750s user + 0.046875s system = 5.765625s CPU (102.1%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  8.628026s wall, 9.796875s user + 0.093750s system = 9.890625s CPU (114.6%)

OPT-1001 : Current memory(MB): used = 706, reserve = 691, peak = 715.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17288/19257.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 719064, over cnt = 17(0%), over = 20, worst = 2
PHY-1002 : len = 719024, over cnt = 10(0%), over = 10, worst = 1
PHY-1002 : len = 719104, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 719200, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 719200, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.658895s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (102.0%)

PHY-1001 : Congestion index: top1 = 47.82, top5 = 42.87, top10 = 40.20, top15 = 38.47.
OPT-1001 : End congestion update;  0.928800s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (102.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19255 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.747745s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (96.1%)

OPT-0007 : Start: WNS 3960 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.681480s wall, 1.687500s user + 0.000000s system = 1.687500s CPU (100.4%)

OPT-1001 : Current memory(MB): used = 706, reserve = 691, peak = 715.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19255 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.732758s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (100.2%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17305/19257.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 719200, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.115880s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (94.4%)

PHY-1001 : Congestion index: top1 = 47.82, top5 = 42.87, top10 = 40.20, top15 = 38.47.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19255 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.738619s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (99.4%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3960 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 47.482759
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3960ps with logic level 8 
OPT-1001 : End physical optimization;  14.266941s wall, 15.453125s user + 0.093750s system = 15.546875s CPU (109.0%)

RUN-1003 : finish command "place" in  66.718303s wall, 126.656250s user + 7.453125s system = 134.109375s CPU (201.0%)

RUN-1004 : used memory is 625 MB, reserved memory is 603 MB, peak memory is 715 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.638090s wall, 2.828125s user + 0.000000s system = 2.828125s CPU (172.6%)

RUN-1004 : used memory is 625 MB, reserved memory is 604 MB, peak memory is 715 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8065 instances
RUN-1001 : 3980 mslices, 3996 lslices, 60 pads, 24 brams, 0 dsps
RUN-1001 : There are total 19257 nets
RUN-1001 : 13559 nets have 2 pins
RUN-1001 : 4288 nets have [3 - 5] pins
RUN-1001 : 888 nets have [6 - 10] pins
RUN-1001 : 382 nets have [11 - 20] pins
RUN-1001 : 131 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66794, tnet num: 19255, tinst num: 8063, tnode num: 90564, tedge num: 110058.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.721056s wall, 1.718750s user + 0.000000s system = 1.718750s CPU (99.9%)

RUN-1004 : used memory is 634 MB, reserved memory is 626 MB, peak memory is 715 MB
PHY-1001 : 3980 mslices, 3996 lslices, 60 pads, 24 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19255 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 661032, over cnt = 2228(6%), over = 3787, worst = 9
PHY-1002 : len = 674992, over cnt = 1447(4%), over = 2135, worst = 9
PHY-1002 : len = 692848, over cnt = 562(1%), over = 809, worst = 7
PHY-1002 : len = 705600, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 705680, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.652865s wall, 3.109375s user + 0.062500s system = 3.171875s CPU (191.9%)

PHY-1001 : Congestion index: top1 = 46.62, top5 = 42.35, top10 = 39.72, top15 = 38.00.
PHY-1001 : End global routing;  1.986504s wall, 3.406250s user + 0.078125s system = 3.484375s CPU (175.4%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 698, reserve = 685, peak = 715.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 964, reserve = 953, peak = 964.
PHY-1001 : End build detailed router design. 5.060887s wall, 4.968750s user + 0.078125s system = 5.046875s CPU (99.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 191064, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.025705s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (100.5%)

PHY-1001 : Current memory(MB): used = 1000, reserve = 989, peak = 1000.
PHY-1001 : End phase 1; 1.039312s wall, 1.046875s user + 0.000000s system = 1.046875s CPU (100.7%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 55% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.66209e+06, over cnt = 1257(0%), over = 1263, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1014, reserve = 1000, peak = 1014.
PHY-1001 : End initial routed; 15.655880s wall, 46.625000s user + 0.468750s system = 47.093750s CPU (300.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17996(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.991   |   0.000   |   0   
RUN-1001 :   Hold   |   0.137   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.402878s wall, 3.390625s user + 0.000000s system = 3.390625s CPU (99.6%)

PHY-1001 : Current memory(MB): used = 1025, reserve = 1012, peak = 1025.
PHY-1001 : End phase 2; 19.058908s wall, 50.015625s user + 0.468750s system = 50.484375s CPU (264.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.66209e+06, over cnt = 1257(0%), over = 1263, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.232026s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (101.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.65052e+06, over cnt = 391(0%), over = 391, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.793118s wall, 1.453125s user + 0.000000s system = 1.453125s CPU (183.2%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.65186e+06, over cnt = 75(0%), over = 75, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.382573s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (142.9%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.65231e+06, over cnt = 14(0%), over = 14, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.265998s wall, 0.328125s user + 0.031250s system = 0.359375s CPU (135.1%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.65256e+06, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.197676s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (94.9%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.65245e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.144135s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (97.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17996(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.845   |   0.000   |   0   
RUN-1001 :   Hold   |   0.137   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.272358s wall, 3.265625s user + 0.000000s system = 3.265625s CPU (99.8%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 300 feed throughs used by 252 nets
PHY-1001 : End commit to database; 2.063097s wall, 2.031250s user + 0.031250s system = 2.062500s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1110, reserve = 1100, peak = 1110.
PHY-1001 : End phase 3; 7.869514s wall, 8.703125s user + 0.062500s system = 8.765625s CPU (111.4%)

PHY-1003 : Routed, final wirelength = 1.65245e+06
PHY-1001 : Current memory(MB): used = 1114, reserve = 1105, peak = 1114.
PHY-1001 : End export database. 0.057999s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (80.8%)

PHY-1001 : End detail routing;  33.490264s wall, 65.187500s user + 0.609375s system = 65.796875s CPU (196.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66794, tnet num: 19255, tinst num: 8063, tnode num: 90564, tedge num: 110058.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.626357s wall, 1.625000s user + 0.000000s system = 1.625000s CPU (99.9%)

RUN-1004 : used memory is 1047 MB, reserved memory is 1048 MB, peak memory is 1114 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  41.236139s wall, 74.343750s user + 0.703125s system = 75.046875s CPU (182.0%)

RUN-1004 : used memory is 1046 MB, reserved memory is 1048 MB, peak memory is 1114 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8805   out of  19600   44.92%
#reg                    12301   out of  19600   62.76%
#le                     14883
  #lut only              2582   out of  14883   17.35%
  #reg only              6078   out of  14883   40.84%
  #lut&reg               6223   out of  14883   41.81%
#dsp                        0   out of     29    0.00%
#bram                      24   out of     64   37.50%
  #bram9k                  22
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6806
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          106
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         M3        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         P6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |14883  |7317    |1488    |12345   |24      |0       |
|  AnyFog_dataX                      |AnyFog          |206    |96      |22      |171     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |84     |62      |22      |49      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |212    |99      |22      |175     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |90     |66      |22      |53      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |206    |112     |22      |169     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |89     |61      |22      |52      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2949   |683     |39      |2851    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |63     |38      |5       |54      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |219    |109     |5       |204     |0       |0       |
|    STADOP_com2                     |STADOP          |561    |97      |0       |554     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |47      |14      |43      |0       |0       |
|    head_com2                       |uniheading      |263    |91      |5       |251     |0       |0       |
|    rmc_com2                        |Gprmc           |35     |34      |0       |29      |0       |0       |
|    uart_com2                       |Agrica          |1436   |255     |10      |1409    |0       |0       |
|  COM3                              |COM3_Control    |279    |146     |19      |237     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |62     |46      |5       |53      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |60     |38      |14      |37      |0       |0       |
|    rmc_com3                        |Gprmc           |157    |62      |0       |147     |0       |0       |
|  DATA                              |Data_Processing |8859   |4589    |1122    |7038    |0       |0       |
|    DIV_Dtemp                       |Divider         |785    |375     |84      |656     |0       |0       |
|    DIV_Utemp                       |Divider         |603    |307     |84      |471     |0       |0       |
|    DIV_accX                        |Divider         |621    |283     |84      |492     |0       |0       |
|    DIV_accY                        |Divider         |638    |309     |102     |486     |0       |0       |
|    DIV_accZ                        |Divider         |628    |369     |132     |423     |0       |0       |
|    DIV_rateX                       |Divider         |677    |353     |132     |473     |0       |0       |
|    DIV_rateY                       |Divider         |601    |363     |132     |394     |0       |0       |
|    DIV_rateZ                       |Divider         |625    |427     |132     |418     |0       |0       |
|    genclk                          |genclk          |262    |159     |89      |101     |0       |0       |
|  FMC                               |FMC_Ctrl        |427    |374     |43      |336     |0       |0       |
|  IIC                               |I2C_master      |268    |224     |11      |245     |0       |0       |
|  IMU_CTRL                          |SCHA634         |887    |615     |61      |716     |0       |0       |
|    CtrlData                        |CtrlData        |470    |413     |47      |333     |0       |0       |
|      usms                          |Time_1ms        |29     |24      |5       |21      |0       |0       |
|    SPIM                            |SPI_SCHA634     |417    |202     |14      |383     |0       |0       |
|  POWER                             |POWER_EN        |94     |49      |38      |35      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |474    |322     |89      |306     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |474    |322     |89      |306     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |187    |151     |0       |170     |0       |0       |
|        reg_inst                    |register        |185    |149     |0       |168     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |287    |171     |89      |136     |0       |0       |
|        bus_inst                    |bus_top         |79     |51      |28      |30      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |26     |16      |10      |9       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |53     |35      |18      |21      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |128    |85      |29      |80      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13498  
    #2          2       3364   
    #3          3        629   
    #4          4        295   
    #5        5-10       946   
    #6        11-50      447   
    #7       51-100       8    
    #8       101-500      3    
    #9        >500        2    
  Average     2.11             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  1.976141s wall, 3.406250s user + 0.015625s system = 3.421875s CPU (173.2%)

RUN-1004 : used memory is 1047 MB, reserved memory is 1049 MB, peak memory is 1114 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66794, tnet num: 19255, tinst num: 8063, tnode num: 90564, tedge num: 110058.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.605910s wall, 1.609375s user + 0.000000s system = 1.609375s CPU (100.2%)

RUN-1004 : used memory is 1049 MB, reserved memory is 1050 MB, peak memory is 1114 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19255 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.230188s wall, 1.234375s user + 0.000000s system = 1.234375s CPU (100.3%)

RUN-1004 : used memory is 1053 MB, reserved memory is 1053 MB, peak memory is 1114 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 057bf4ae9dc0d18f7126c20ce7aec8c31cdd0d41110c6b742e6e5620711a0ef6 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8063
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19257, pip num: 143273
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 300
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3248 valid insts, and 403909 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101000001010010110001101
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  10.587814s wall, 105.656250s user + 0.171875s system = 105.828125s CPU (999.5%)

RUN-1004 : used memory is 1180 MB, reserved memory is 1168 MB, peak memory is 1294 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250819_200034.log"

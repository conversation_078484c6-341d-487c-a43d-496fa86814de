============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Jul 10 16:40:46 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(520)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.114280s wall, 1.406250s user + 3.718750s system = 5.125000s CPU (100.2%)

RUN-1004 : used memory is 80 MB, reserved memory is 42 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.954989s wall, 1.890625s user + 0.062500s system = 1.953125s CPU (99.9%)

RUN-1004 : used memory is 302 MB, reserved memory is 270 MB, peak memory is 305 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "read_sdc -ip Asys_fifo8x8 ../../al_ip/Asys_fifo8x8.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 95185065213952"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 95185065213952"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  9.6999999999999993 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 9.6999999999999993"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  9.6999999999999993 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 9.6999999999999993"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 14 view nodes, 53 trigger nets, 53 data nets.
KIT-1004 : Chipwatcher code = 1010011101000100
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=14,BUS_DIN_NUM=53,BUS_CTRL_NUM=162,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb011,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011101,32'sb011110,32'sb011111,32'sb0100000,32'sb0100001,32'sb0100010,32'sb0101010,32'sb0110010,32'sb0110011,32'sb0110100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01001010,32'sb01010000,32'sb01010110,32'sb01011100,32'sb01100010,32'sb01101000,32'sb01111100,32'sb010010000,32'sb010010110,32'sb010011100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=184) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=184) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=14,BUS_DIN_NUM=53,BUS_CTRL_NUM=162,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb011,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011101,32'sb011110,32'sb011111,32'sb0100000,32'sb0100001,32'sb0100010,32'sb0101010,32'sb0110010,32'sb0110011,32'sb0110100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01001010,32'sb01010000,32'sb01010110,32'sb01011100,32'sb01100010,32'sb01101000,32'sb01111100,32'sb010010000,32'sb010010110,32'sb010011100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=14,BUS_DIN_NUM=53,BUS_CTRL_NUM=162,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb011,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011101,32'sb011110,32'sb011111,32'sb0100000,32'sb0100001,32'sb0100010,32'sb0101010,32'sb0110010,32'sb0110011,32'sb0110100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01001010,32'sb01010000,32'sb01010110,32'sb01011100,32'sb01100010,32'sb01101000,32'sb01111100,32'sb010010000,32'sb010010110,32'sb010011100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb011) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=14,BUS_DIN_NUM=53,BUS_CTRL_NUM=162,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb011,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011101,32'sb011110,32'sb011111,32'sb0100000,32'sb0100001,32'sb0100010,32'sb0101010,32'sb0110010,32'sb0110011,32'sb0110100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01001010,32'sb01010000,32'sb01010110,32'sb01011100,32'sb01100010,32'sb01101000,32'sb01111100,32'sb010010000,32'sb010010110,32'sb010011100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=184)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=184)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=14,BUS_DIN_NUM=53,BUS_CTRL_NUM=162,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb011,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011101,32'sb011110,32'sb011111,32'sb0100000,32'sb0100001,32'sb0100010,32'sb0101010,32'sb0110010,32'sb0110011,32'sb0110100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01001010,32'sb01010000,32'sb01010110,32'sb01011100,32'sb01100010,32'sb01101000,32'sb01111100,32'sb010010000,32'sb010010110,32'sb010011100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=14,BUS_DIN_NUM=53,BUS_CTRL_NUM=162,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb011,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011101,32'sb011110,32'sb011111,32'sb0100000,32'sb0100001,32'sb0100010,32'sb0101010,32'sb0110010,32'sb0110011,32'sb0110100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01001010,32'sb01010000,32'sb01010110,32'sb01011100,32'sb01100010,32'sb01101000,32'sb01111100,32'sb010010000,32'sb010010110,32'sb010011100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb011)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 23087/56 useful/useless nets, 19642/36 useful/useless insts
SYN-1016 : Merged 65 instances.
SYN-1032 : 22603/32 useful/useless nets, 20159/28 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 6 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 6 mux instances.
SYN-1015 : Optimize round 1, 540 better
SYN-1014 : Optimize round 2
SYN-1032 : 22122/90 useful/useless nets, 19678/96 useful/useless insts
SYN-1015 : Optimize round 2, 192 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.435701s wall, 2.328125s user + 0.109375s system = 2.437500s CPU (100.1%)

RUN-1004 : used memory is 332 MB, reserved memory is 297 MB, peak memory is 334 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22182/373 useful/useless nets, 19785/58 useful/useless insts
SYN-1016 : Merged 57 instances.
SYN-2571 : Optimize after map_dsp, round 1, 488 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 46 instances.
SYN-2501 : Optimize round 1, 93 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 22 macro adder
SYN-1019 : Optimized 25 mux instances.
SYN-1016 : Merged 18 instances.
SYN-1032 : 22701/5 useful/useless nets, 20304/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83071, tnet num: 22701, tinst num: 20303, tnode num: 116477, tedge num: 129562.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.191508s wall, 1.187500s user + 0.015625s system = 1.203125s CPU (101.0%)

RUN-1004 : used memory is 472 MB, reserved memory is 440 MB, peak memory is 472 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22701 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 291 (3.50), #lev = 6 (1.86)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 291 (3.50), #lev = 6 (1.86)
SYN-3001 : Logic optimization runtime opt =   0.04 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 763 instances into 291 LUTs, name keeping = 72%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 543 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 142 adder to BLE ...
SYN-4008 : Packed 142 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.439823s wall, 4.343750s user + 0.109375s system = 4.453125s CPU (100.3%)

RUN-1004 : used memory is 369 MB, reserved memory is 356 MB, peak memory is 581 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.182332s wall, 6.921875s user + 0.250000s system = 7.171875s CPU (99.9%)

RUN-1004 : used memory is 369 MB, reserved memory is 356 MB, peak memory is 581 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/uart_com2/AGRIC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/uart_com2/AGRIC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/uart_com2/AGRIC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/uart_com2/AGRIC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/uart_com2/AGRIC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/uart_com2/AGRIC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/uart_com2/AGRIC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/uart_com2/AGRIC_data[0]
SYN-5055 WARNING: The kept net COM3/rmc_com3/GPRMC_end will be merged to another kept net COM3/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM3/rmc_com3/GPRMC_sta will be merged to another kept net COM3/GNRMC/GPRMC_sta
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (381 clock/control pins, 0 other pins).
SYN-4027 : Net COM3/GNRMC/u_fifo/wr_to_rd_cross_inst/secondary_clk_i is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net COM3/GNRMC/u_fifo/wr_to_rd_cross_inst/secondary_clk_i as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19372 instances
RUN-0007 : 5502 luts, 12291 seqs, 962 mslices, 502 lslices, 60 pads, 50 brams, 0 dsps
RUN-1001 : There are total 21793 nets
RUN-1001 : 16297 nets have 2 pins
RUN-1001 : 4322 nets have [3 - 5] pins
RUN-1001 : 805 nets have [6 - 10] pins
RUN-1001 : 244 nets have [11 - 20] pins
RUN-1001 : 103 nets have [21 - 99] pins
RUN-1001 : 22 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4769     
RUN-1001 :   No   |  No   |  Yes  |     748     
RUN-1001 :   No   |  Yes  |  No   |     85      
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     555     
RUN-1001 :   Yes  |  Yes  |  No   |     51      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 124
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19370 instances, 5502 luts, 12291 seqs, 1464 slices, 293 macros(1464 instances: 962 mslices 502 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 63%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80998, tnet num: 21791, tinst num: 19370, tnode num: 114343, tedge num: 127524.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.181413s wall, 1.171875s user + 0.000000s system = 1.171875s CPU (99.2%)

RUN-1004 : used memory is 532 MB, reserved memory is 504 MB, peak memory is 581 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21791 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.987891s wall, 1.984375s user + 0.000000s system = 1.984375s CPU (99.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.67568e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19370.
PHY-3001 : Level 1 #clusters 2150.
PHY-3001 : End clustering;  0.141030s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (144.0%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 63%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 858450, overlap = 678.438
PHY-3002 : Step(2): len = 792797, overlap = 744.344
PHY-3002 : Step(3): len = 523847, overlap = 893
PHY-3002 : Step(4): len = 447642, overlap = 951.438
PHY-3002 : Step(5): len = 358010, overlap = 1020.12
PHY-3002 : Step(6): len = 318047, overlap = 1054.84
PHY-3002 : Step(7): len = 268479, overlap = 1122.75
PHY-3002 : Step(8): len = 237877, overlap = 1165.28
PHY-3002 : Step(9): len = 213232, overlap = 1202.56
PHY-3002 : Step(10): len = 199684, overlap = 1269
PHY-3002 : Step(11): len = 179762, overlap = 1307.69
PHY-3002 : Step(12): len = 164961, overlap = 1346.22
PHY-3002 : Step(13): len = 152801, overlap = 1370.75
PHY-3002 : Step(14): len = 141094, overlap = 1376.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.1457e-06
PHY-3002 : Step(15): len = 143347, overlap = 1363.03
PHY-3002 : Step(16): len = 182983, overlap = 1249.41
PHY-3002 : Step(17): len = 194481, overlap = 1119.47
PHY-3002 : Step(18): len = 195741, overlap = 1079.69
PHY-3002 : Step(19): len = 196487, overlap = 1070.66
PHY-3002 : Step(20): len = 189184, overlap = 1053.41
PHY-3002 : Step(21): len = 183078, overlap = 1056.94
PHY-3002 : Step(22): len = 176695, overlap = 1068.94
PHY-3002 : Step(23): len = 172878, overlap = 1102.59
PHY-3002 : Step(24): len = 168258, overlap = 1112.78
PHY-3002 : Step(25): len = 166432, overlap = 1107.22
PHY-3002 : Step(26): len = 164290, overlap = 1103.25
PHY-3002 : Step(27): len = 162102, overlap = 1107.66
PHY-3002 : Step(28): len = 160598, overlap = 1116
PHY-3002 : Step(29): len = 158650, overlap = 1126.25
PHY-3002 : Step(30): len = 158203, overlap = 1126.69
PHY-3002 : Step(31): len = 156811, overlap = 1132.97
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.29141e-06
PHY-3002 : Step(32): len = 164100, overlap = 1085.5
PHY-3002 : Step(33): len = 176931, overlap = 994.125
PHY-3002 : Step(34): len = 179966, overlap = 966.688
PHY-3002 : Step(35): len = 182512, overlap = 957.906
PHY-3002 : Step(36): len = 183214, overlap = 941.875
PHY-3002 : Step(37): len = 182774, overlap = 935.344
PHY-3002 : Step(38): len = 182058, overlap = 944.062
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.58281e-06
PHY-3002 : Step(39): len = 192346, overlap = 917.031
PHY-3002 : Step(40): len = 206508, overlap = 846.188
PHY-3002 : Step(41): len = 210865, overlap = 800.344
PHY-3002 : Step(42): len = 213471, overlap = 760.156
PHY-3002 : Step(43): len = 212925, overlap = 747.688
PHY-3002 : Step(44): len = 211697, overlap = 751.781
PHY-3002 : Step(45): len = 210244, overlap = 763.062
PHY-3002 : Step(46): len = 209603, overlap = 789.031
PHY-3002 : Step(47): len = 209216, overlap = 814.938
PHY-3002 : Step(48): len = 208875, overlap = 810.438
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 9.16562e-06
PHY-3002 : Step(49): len = 217318, overlap = 774.281
PHY-3002 : Step(50): len = 233029, overlap = 711.062
PHY-3002 : Step(51): len = 238323, overlap = 647.562
PHY-3002 : Step(52): len = 240266, overlap = 631.125
PHY-3002 : Step(53): len = 239063, overlap = 620.562
PHY-3002 : Step(54): len = 237360, overlap = 621.688
PHY-3002 : Step(55): len = 235911, overlap = 640.938
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.83312e-05
PHY-3002 : Step(56): len = 245250, overlap = 585.938
PHY-3002 : Step(57): len = 256711, overlap = 566.219
PHY-3002 : Step(58): len = 260750, overlap = 525.938
PHY-3002 : Step(59): len = 263810, overlap = 490.812
PHY-3002 : Step(60): len = 262754, overlap = 473.625
PHY-3002 : Step(61): len = 261805, overlap = 469.531
PHY-3002 : Step(62): len = 260240, overlap = 472.812
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.66625e-05
PHY-3002 : Step(63): len = 270021, overlap = 441.75
PHY-3002 : Step(64): len = 279500, overlap = 397.594
PHY-3002 : Step(65): len = 281963, overlap = 385.562
PHY-3002 : Step(66): len = 283281, overlap = 368.844
PHY-3002 : Step(67): len = 282688, overlap = 366.344
PHY-3002 : Step(68): len = 281695, overlap = 357.688
PHY-3002 : Step(69): len = 280260, overlap = 352.562
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 7.3325e-05
PHY-3002 : Step(70): len = 285552, overlap = 336.406
PHY-3002 : Step(71): len = 293481, overlap = 325.625
PHY-3002 : Step(72): len = 296839, overlap = 320.938
PHY-3002 : Step(73): len = 298774, overlap = 303.062
PHY-3002 : Step(74): len = 298167, overlap = 312.75
PHY-3002 : Step(75): len = 296744, overlap = 315.281
PHY-3002 : Step(76): len = 294661, overlap = 302.812
PHY-3002 : Step(77): len = 293109, overlap = 306.125
PHY-3002 : Step(78): len = 292868, overlap = 312.094
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.00014665
PHY-3002 : Step(79): len = 296447, overlap = 297.062
PHY-3002 : Step(80): len = 302106, overlap = 293.781
PHY-3002 : Step(81): len = 304599, overlap = 293.75
PHY-3002 : Step(82): len = 307280, overlap = 274.938
PHY-3002 : Step(83): len = 307992, overlap = 261.312
PHY-3002 : Step(84): len = 307269, overlap = 264.281
PHY-3002 : Step(85): len = 306409, overlap = 268.906
PHY-3002 : Step(86): len = 306190, overlap = 284.688
PHY-3002 : Step(87): len = 305974, overlap = 252.969
PHY-3002 : Step(88): len = 306927, overlap = 251.594
PHY-3002 : Step(89): len = 306813, overlap = 257.125
PHY-3002 : Step(90): len = 306828, overlap = 242.688
PHY-3002 : Step(91): len = 306164, overlap = 238.25
PHY-3002 : Step(92): len = 304884, overlap = 238.75
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000263275
PHY-3002 : Step(93): len = 306814, overlap = 235.688
PHY-3002 : Step(94): len = 310435, overlap = 228.812
PHY-3002 : Step(95): len = 311557, overlap = 234.062
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000444171
PHY-3002 : Step(96): len = 312097, overlap = 237.5
PHY-3002 : Step(97): len = 315948, overlap = 224.656
PHY-3002 : Step(98): len = 316755, overlap = 225.469
PHY-3002 : Step(99): len = 316916, overlap = 224.656
PHY-3002 : Step(100): len = 317277, overlap = 226.438
PHY-3002 : Step(101): len = 317665, overlap = 237.375
PHY-3002 : Step(102): len = 317025, overlap = 231
PHY-3002 : Step(103): len = 317579, overlap = 233.469
PHY-3002 : Step(104): len = 319250, overlap = 225.75
PHY-3002 : Step(105): len = 319899, overlap = 231.094
PHY-3002 : Step(106): len = 319905, overlap = 237.75
PHY-3002 : Step(107): len = 319076, overlap = 231.406
PHY-3002 : Step(108): len = 318958, overlap = 233.625
PHY-3002 : Step(109): len = 318357, overlap = 233.844
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.010655s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (146.6%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21793.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 433640, over cnt = 1187(3%), over = 5394, worst = 43
PHY-1001 : End global iterations;  0.850176s wall, 1.093750s user + 0.031250s system = 1.125000s CPU (132.3%)

PHY-1001 : Congestion index: top1 = 74.98, top5 = 53.52, top10 = 43.74, top15 = 37.92.
PHY-3001 : End congestion estimation;  1.097281s wall, 1.343750s user + 0.031250s system = 1.375000s CPU (125.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21791 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.831163s wall, 0.765625s user + 0.046875s system = 0.812500s CPU (97.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.57339e-05
PHY-3002 : Step(110): len = 363373, overlap = 179
PHY-3002 : Step(111): len = 379868, overlap = 162.406
PHY-3002 : Step(112): len = 379425, overlap = 152.844
PHY-3002 : Step(113): len = 379243, overlap = 150.812
PHY-3002 : Step(114): len = 385958, overlap = 143.781
PHY-3002 : Step(115): len = 389885, overlap = 138.969
PHY-3002 : Step(116): len = 393224, overlap = 132.438
PHY-3002 : Step(117): len = 398181, overlap = 127.875
PHY-3002 : Step(118): len = 401566, overlap = 119.781
PHY-3002 : Step(119): len = 406339, overlap = 118.844
PHY-3002 : Step(120): len = 410047, overlap = 130.312
PHY-3002 : Step(121): len = 410962, overlap = 136.031
PHY-3002 : Step(122): len = 414030, overlap = 136.969
PHY-3002 : Step(123): len = 417416, overlap = 141.656
PHY-3002 : Step(124): len = 418369, overlap = 150.188
PHY-3002 : Step(125): len = 420683, overlap = 157.438
PHY-3002 : Step(126): len = 420147, overlap = 154.844
PHY-3002 : Step(127): len = 420037, overlap = 154.812
PHY-3002 : Step(128): len = 420557, overlap = 154
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000171468
PHY-3002 : Step(129): len = 420592, overlap = 154.219
PHY-3002 : Step(130): len = 421690, overlap = 153.5
PHY-3002 : Step(131): len = 422555, overlap = 151.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000308367
PHY-3002 : Step(132): len = 428338, overlap = 148.469
PHY-3002 : Step(133): len = 434145, overlap = 149.875
PHY-3002 : Step(134): len = 436757, overlap = 148.125
PHY-3002 : Step(135): len = 438600, overlap = 145.5
PHY-3002 : Step(136): len = 440021, overlap = 141.719
PHY-3002 : Step(137): len = 441829, overlap = 135
PHY-3002 : Step(138): len = 442378, overlap = 128.844
PHY-3002 : Step(139): len = 442898, overlap = 123.281
PHY-3002 : Step(140): len = 444628, overlap = 117.312
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 76/21793.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 517344, over cnt = 2123(6%), over = 9804, worst = 29
PHY-1001 : End global iterations;  0.998176s wall, 1.687500s user + 0.031250s system = 1.718750s CPU (172.2%)

PHY-1001 : Congestion index: top1 = 76.42, top5 = 58.38, top10 = 50.08, top15 = 45.01.
PHY-3001 : End congestion estimation;  1.264047s wall, 1.953125s user + 0.031250s system = 1.984375s CPU (157.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21791 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.871703s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (98.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000101795
PHY-3002 : Step(141): len = 450990, overlap = 405.875
PHY-3002 : Step(142): len = 461586, overlap = 331.531
PHY-3002 : Step(143): len = 457681, overlap = 300.406
PHY-3002 : Step(144): len = 454364, overlap = 284.906
PHY-3002 : Step(145): len = 452372, overlap = 262.312
PHY-3002 : Step(146): len = 451836, overlap = 240.625
PHY-3002 : Step(147): len = 449149, overlap = 225.875
PHY-3002 : Step(148): len = 447881, overlap = 219.938
PHY-3002 : Step(149): len = 448255, overlap = 207.531
PHY-3002 : Step(150): len = 445152, overlap = 209.25
PHY-3002 : Step(151): len = 443234, overlap = 208.938
PHY-3002 : Step(152): len = 440995, overlap = 217.875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00020359
PHY-3002 : Step(153): len = 441957, overlap = 205.969
PHY-3002 : Step(154): len = 444199, overlap = 203.031
PHY-3002 : Step(155): len = 446495, overlap = 197.562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00040718
PHY-3002 : Step(156): len = 448345, overlap = 190.406
PHY-3002 : Step(157): len = 452316, overlap = 184.312
PHY-3002 : Step(158): len = 456692, overlap = 166.812
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00081436
PHY-3002 : Step(159): len = 457717, overlap = 163.438
PHY-3002 : Step(160): len = 461502, overlap = 151.375
PHY-3002 : Step(161): len = 465418, overlap = 147.062
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00160716
PHY-3002 : Step(162): len = 466025, overlap = 142.156
PHY-3002 : Step(163): len = 467664, overlap = 143.031
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80998, tnet num: 21791, tinst num: 19370, tnode num: 114343, tedge num: 127524.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.394550s wall, 1.390625s user + 0.000000s system = 1.390625s CPU (99.7%)

RUN-1004 : used memory is 570 MB, reserved memory is 544 MB, peak memory is 703 MB
OPT-1001 : Total overflow 513.62 peak overflow 4.62
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 656/21793.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 555504, over cnt = 2512(7%), over = 8621, worst = 25
PHY-1001 : End global iterations;  1.194160s wall, 1.828125s user + 0.031250s system = 1.859375s CPU (155.7%)

PHY-1001 : Congestion index: top1 = 58.73, top5 = 48.60, top10 = 43.39, top15 = 40.17.
PHY-1001 : End incremental global routing;  1.409805s wall, 2.046875s user + 0.031250s system = 2.078125s CPU (147.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21791 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.903384s wall, 0.875000s user + 0.015625s system = 0.890625s CPU (98.6%)

OPT-1001 : 14 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19293 has valid locations, 253 needs to be replaced
PHY-3001 : design contains 19609 instances, 5593 luts, 12439 seqs, 1464 slices, 293 macros(1464 instances: 962 mslices 502 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 482143
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17026/22032.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 567384, over cnt = 2520(7%), over = 8666, worst = 25
PHY-1001 : End global iterations;  0.167710s wall, 0.296875s user + 0.046875s system = 0.343750s CPU (205.0%)

PHY-1001 : Congestion index: top1 = 58.56, top5 = 48.63, top10 = 43.53, top15 = 40.37.
PHY-3001 : End congestion estimation;  0.385785s wall, 0.531250s user + 0.046875s system = 0.578125s CPU (149.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81775, tnet num: 22030, tinst num: 19609, tnode num: 115476, tedge num: 128600.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.432436s wall, 1.390625s user + 0.031250s system = 1.421875s CPU (99.3%)

RUN-1004 : used memory is 615 MB, reserved memory is 612 MB, peak memory is 706 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22030 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.368777s wall, 2.296875s user + 0.062500s system = 2.359375s CPU (99.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(164): len = 483309, overlap = 2.96875
PHY-3002 : Step(165): len = 485395, overlap = 2.96875
PHY-3002 : Step(166): len = 486361, overlap = 2.84375
PHY-3002 : Step(167): len = 486792, overlap = 3.03125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17049/22032.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 569144, over cnt = 2544(7%), over = 8719, worst = 25
PHY-1001 : End global iterations;  0.177618s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (132.0%)

PHY-1001 : Congestion index: top1 = 59.07, top5 = 48.94, top10 = 43.75, top15 = 40.58.
PHY-3001 : End congestion estimation;  0.403550s wall, 0.453125s user + 0.015625s system = 0.468750s CPU (116.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22030 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.932207s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000708156
PHY-3002 : Step(168): len = 486284, overlap = 146.469
PHY-3002 : Step(169): len = 486213, overlap = 146.094
PHY-3002 : Step(170): len = 486361, overlap = 146
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00141631
PHY-3002 : Step(171): len = 486595, overlap = 146.438
PHY-3002 : Step(172): len = 486866, overlap = 146.094
PHY-3001 : Final: Len = 486866, Over = 146.094
PHY-3001 : End incremental placement;  5.042873s wall, 5.718750s user + 0.265625s system = 5.984375s CPU (118.7%)

OPT-1001 : Total overflow 520.69 peak overflow 4.62
OPT-1001 : End high-fanout net optimization;  7.855467s wall, 9.281250s user + 0.312500s system = 9.593750s CPU (122.1%)

OPT-1001 : Current memory(MB): used = 707, reserve = 686, peak = 723.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17061/22032.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 569800, over cnt = 2486(7%), over = 8245, worst = 25
PHY-1002 : len = 606120, over cnt = 1769(5%), over = 4809, worst = 25
PHY-1002 : len = 650240, over cnt = 725(2%), over = 1617, worst = 16
PHY-1002 : len = 672184, over cnt = 150(0%), over = 297, worst = 16
PHY-1002 : len = 676640, over cnt = 1(0%), over = 1, worst = 1
PHY-1001 : End global iterations;  1.140968s wall, 1.671875s user + 0.046875s system = 1.718750s CPU (150.6%)

PHY-1001 : Congestion index: top1 = 50.56, top5 = 44.60, top10 = 41.32, top15 = 39.27.
OPT-1001 : End congestion update;  1.372787s wall, 1.906250s user + 0.046875s system = 1.953125s CPU (142.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22030 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.811390s wall, 0.796875s user + 0.015625s system = 0.812500s CPU (100.1%)

OPT-0007 : Start: WNS 3671 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.189193s wall, 2.703125s user + 0.062500s system = 2.765625s CPU (126.3%)

OPT-1001 : Current memory(MB): used = 683, reserve = 667, peak = 723.
OPT-1001 : End physical optimization;  11.751283s wall, 13.843750s user + 0.390625s system = 14.234375s CPU (121.1%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5593 LUT to BLE ...
SYN-4008 : Packed 5593 LUT and 2673 SEQ to BLE.
SYN-4003 : Packing 9766 remaining SEQ's ...
SYN-4005 : Packed 3322 SEQ with LUT/SLICE
SYN-4006 : 114 single LUT's are left
SYN-4006 : 6444 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 12037/13862 primitive instances ...
PHY-3001 : End packing;  2.697222s wall, 2.703125s user + 0.000000s system = 2.703125s CPU (100.2%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8172 instances
RUN-1001 : 4029 mslices, 4028 lslices, 60 pads, 50 brams, 0 dsps
RUN-1001 : There are total 19428 nets
RUN-1001 : 13577 nets have 2 pins
RUN-1001 : 4455 nets have [3 - 5] pins
RUN-1001 : 877 nets have [6 - 10] pins
RUN-1001 : 390 nets have [11 - 20] pins
RUN-1001 : 119 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
PHY-3001 : design contains 8170 instances, 8057 slices, 293 macros(1464 instances: 962 mslices 502 lslices)
PHY-3001 : Cell area utilization is 86%
PHY-3001 : After packing: Len = 505838, Over = 388.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7873/19428.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 644048, over cnt = 1535(4%), over = 2403, worst = 8
PHY-1002 : len = 649448, over cnt = 1022(2%), over = 1394, worst = 5
PHY-1002 : len = 662560, over cnt = 329(0%), over = 400, worst = 5
PHY-1002 : len = 667824, over cnt = 72(0%), over = 72, worst = 1
PHY-1002 : len = 669440, over cnt = 3(0%), over = 3, worst = 1
PHY-1001 : End global iterations;  1.122837s wall, 1.937500s user + 0.015625s system = 1.953125s CPU (173.9%)

PHY-1001 : Congestion index: top1 = 49.98, top5 = 43.98, top10 = 40.64, top15 = 38.49.
PHY-3001 : End congestion estimation;  1.415210s wall, 2.234375s user + 0.015625s system = 2.250000s CPU (159.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67861, tnet num: 19426, tinst num: 8170, tnode num: 92393, tedge num: 111868.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.647025s wall, 1.640625s user + 0.000000s system = 1.640625s CPU (99.6%)

RUN-1004 : used memory is 602 MB, reserved memory is 588 MB, peak memory is 723 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19426 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.539055s wall, 2.515625s user + 0.015625s system = 2.531250s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.62492e-05
PHY-3002 : Step(173): len = 509756, overlap = 366.25
PHY-3002 : Step(174): len = 507549, overlap = 376.75
PHY-3002 : Step(175): len = 507813, overlap = 390.75
PHY-3002 : Step(176): len = 509766, overlap = 400.75
PHY-3002 : Step(177): len = 509320, overlap = 408.75
PHY-3002 : Step(178): len = 507899, overlap = 415.75
PHY-3002 : Step(179): len = 505837, overlap = 418.75
PHY-3002 : Step(180): len = 503315, overlap = 425.75
PHY-3002 : Step(181): len = 500743, overlap = 426.25
PHY-3002 : Step(182): len = 499254, overlap = 432
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.24984e-05
PHY-3002 : Step(183): len = 502518, overlap = 418.5
PHY-3002 : Step(184): len = 506744, overlap = 408.5
PHY-3002 : Step(185): len = 507232, overlap = 400
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000184997
PHY-3002 : Step(186): len = 515518, overlap = 382
PHY-3002 : Step(187): len = 524468, overlap = 367
PHY-3002 : Step(188): len = 522624, overlap = 363.25
PHY-3002 : Step(189): len = 521112, overlap = 357
PHY-3002 : Step(190): len = 520888, overlap = 350.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.592030s wall, 0.593750s user + 0.593750s system = 1.187500s CPU (200.6%)

PHY-3001 : Trial Legalized: Len = 630452
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 507/19428.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 720104, over cnt = 2504(7%), over = 4035, worst = 8
PHY-1002 : len = 735296, over cnt = 1509(4%), over = 2118, worst = 7
PHY-1002 : len = 751744, over cnt = 677(1%), over = 944, worst = 6
PHY-1002 : len = 764640, over cnt = 195(0%), over = 273, worst = 4
PHY-1002 : len = 769400, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.850833s wall, 2.796875s user + 0.015625s system = 2.812500s CPU (152.0%)

PHY-1001 : Congestion index: top1 = 50.60, top5 = 46.06, top10 = 43.34, top15 = 41.41.
PHY-3001 : End congestion estimation;  2.171487s wall, 3.125000s user + 0.015625s system = 3.140625s CPU (144.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19426 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.809322s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (98.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000183336
PHY-3002 : Step(191): len = 588943, overlap = 82.25
PHY-3002 : Step(192): len = 570528, overlap = 131.25
PHY-3002 : Step(193): len = 558336, overlap = 189.5
PHY-3002 : Step(194): len = 552019, overlap = 224.5
PHY-3002 : Step(195): len = 548152, overlap = 249.25
PHY-3002 : Step(196): len = 546186, overlap = 268.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000366673
PHY-3002 : Step(197): len = 550288, overlap = 260
PHY-3002 : Step(198): len = 553897, overlap = 254.75
PHY-3002 : Step(199): len = 553679, overlap = 253.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(200): len = 556639, overlap = 249
PHY-3002 : Step(201): len = 561603, overlap = 249
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.031281s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (99.9%)

PHY-3001 : Legalized: Len = 606194, Over = 0
PHY-3001 : Spreading special nets. 36 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.072383s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (107.9%)

PHY-3001 : 60 instances has been re-located, deltaX = 26, deltaY = 28, maxDist = 1.
PHY-3001 : Final: Len = 607304, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67861, tnet num: 19426, tinst num: 8170, tnode num: 92393, tedge num: 111868.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.908776s wall, 1.890625s user + 0.015625s system = 1.906250s CPU (99.9%)

RUN-1004 : used memory is 596 MB, reserved memory is 574 MB, peak memory is 723 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 4111/19428.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 713840, over cnt = 2229(6%), over = 3506, worst = 8
PHY-1002 : len = 726232, over cnt = 1334(3%), over = 1776, worst = 6
PHY-1002 : len = 741640, over cnt = 466(1%), over = 586, worst = 5
PHY-1002 : len = 745712, over cnt = 275(0%), over = 333, worst = 5
PHY-1002 : len = 752552, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.614439s wall, 2.593750s user + 0.093750s system = 2.687500s CPU (166.5%)

PHY-1001 : Congestion index: top1 = 48.88, top5 = 44.82, top10 = 42.09, top15 = 40.27.
PHY-1001 : End incremental global routing;  1.896699s wall, 2.875000s user + 0.093750s system = 2.968750s CPU (156.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19426 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.830940s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (101.5%)

OPT-1001 : 3 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8104 has valid locations, 18 needs to be replaced
PHY-3001 : design contains 8185 instances, 8072 slices, 293 macros(1464 instances: 962 mslices 502 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 610712
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17486/19440.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 756128, over cnt = 37(0%), over = 44, worst = 4
PHY-1002 : len = 756176, over cnt = 26(0%), over = 26, worst = 1
PHY-1002 : len = 756264, over cnt = 19(0%), over = 19, worst = 1
PHY-1002 : len = 756504, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 756672, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.700230s wall, 0.718750s user + 0.015625s system = 0.734375s CPU (104.9%)

PHY-1001 : Congestion index: top1 = 49.27, top5 = 44.94, top10 = 42.19, top15 = 40.38.
PHY-3001 : End congestion estimation;  0.978600s wall, 1.000000s user + 0.015625s system = 1.015625s CPU (103.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67973, tnet num: 19438, tinst num: 8185, tnode num: 92528, tedge num: 112002.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.882299s wall, 1.859375s user + 0.015625s system = 1.875000s CPU (99.6%)

RUN-1004 : used memory is 643 MB, reserved memory is 632 MB, peak memory is 723 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19438 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.734668s wall, 2.687500s user + 0.046875s system = 2.734375s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(202): len = 610712, overlap = 0.25
PHY-3002 : Step(203): len = 610712, overlap = 0.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17502/19440.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 756672, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.111502s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (98.1%)

PHY-1001 : Congestion index: top1 = 49.27, top5 = 44.94, top10 = 42.19, top15 = 40.38.
PHY-3001 : End congestion estimation;  0.395445s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (102.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19438 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.817659s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (99.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00152897
PHY-3002 : Step(204): len = 610854, overlap = 2.5
PHY-3002 : Step(205): len = 610611, overlap = 2.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006102s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (256.1%)

PHY-3001 : Legalized: Len = 610627, Over = 0
PHY-3001 : End spreading;  0.060757s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (77.2%)

PHY-3001 : Final: Len = 610627, Over = 0
PHY-3001 : End incremental placement;  5.529616s wall, 5.500000s user + 0.125000s system = 5.625000s CPU (101.7%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  8.720940s wall, 9.671875s user + 0.218750s system = 9.890625s CPU (113.4%)

OPT-1001 : Current memory(MB): used = 717, reserve = 701, peak = 723.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17477/19440.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 755928, over cnt = 39(0%), over = 43, worst = 3
PHY-1002 : len = 755944, over cnt = 17(0%), over = 17, worst = 1
PHY-1002 : len = 755984, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 756040, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 756136, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.674616s wall, 0.703125s user + 0.015625s system = 0.718750s CPU (106.5%)

PHY-1001 : Congestion index: top1 = 49.18, top5 = 44.88, top10 = 42.20, top15 = 40.37.
OPT-1001 : End congestion update;  0.951060s wall, 0.984375s user + 0.015625s system = 1.000000s CPU (105.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19438 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.768698s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (99.6%)

OPT-0007 : Start: WNS 3696 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.724038s wall, 1.765625s user + 0.015625s system = 1.781250s CPU (103.3%)

OPT-1001 : Current memory(MB): used = 717, reserve = 701, peak = 723.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19438 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.765770s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (95.9%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17502/19440.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 756136, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.112855s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (96.9%)

PHY-1001 : Congestion index: top1 = 49.18, top5 = 44.88, top10 = 42.20, top15 = 40.37.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19438 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.700999s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (100.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3696 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 48.793103
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3696ps with logic level 1 
RUN-1001 :       #2 path slack 3696ps with logic level 1 
RUN-1001 :       #3 path slack 3746ps with logic level 1 
RUN-1001 :       #4 path slack 3746ps with logic level 1 
RUN-1001 :       #5 path slack 3746ps with logic level 1 
RUN-1001 :       #6 path slack 3755ps with logic level 1 
RUN-1001 :       #7 path slack 3790ps with logic level 4 
OPT-1001 : End physical optimization;  14.468689s wall, 15.390625s user + 0.265625s system = 15.656250s CPU (108.2%)

RUN-1003 : finish command "place" in  66.523966s wall, 107.171875s user + 6.406250s system = 113.578125s CPU (170.7%)

RUN-1004 : used memory is 566 MB, reserved memory is 542 MB, peak memory is 723 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.604800s wall, 2.750000s user + 0.000000s system = 2.750000s CPU (171.4%)

RUN-1004 : used memory is 567 MB, reserved memory is 543 MB, peak memory is 723 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8187 instances
RUN-1001 : 4044 mslices, 4028 lslices, 60 pads, 50 brams, 0 dsps
RUN-1001 : There are total 19440 nets
RUN-1001 : 13572 nets have 2 pins
RUN-1001 : 4457 nets have [3 - 5] pins
RUN-1001 : 885 nets have [6 - 10] pins
RUN-1001 : 397 nets have [11 - 20] pins
RUN-1001 : 119 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67973, tnet num: 19438, tinst num: 8185, tnode num: 92528, tedge num: 112002.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.693116s wall, 1.671875s user + 0.015625s system = 1.687500s CPU (99.7%)

RUN-1004 : used memory is 594 MB, reserved memory is 587 MB, peak memory is 723 MB
PHY-1001 : 4044 mslices, 4028 lslices, 60 pads, 50 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19438 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 692096, over cnt = 2366(6%), over = 4021, worst = 8
PHY-1002 : len = 710416, over cnt = 1416(4%), over = 1986, worst = 6
PHY-1002 : len = 727472, over cnt = 572(1%), over = 771, worst = 6
PHY-1002 : len = 741336, over cnt = 17(0%), over = 19, worst = 2
PHY-1002 : len = 742024, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.740370s wall, 2.875000s user + 0.062500s system = 2.937500s CPU (168.8%)

PHY-1001 : Congestion index: top1 = 48.66, top5 = 44.42, top10 = 41.73, top15 = 39.89.
PHY-1001 : End global routing;  2.054797s wall, 3.187500s user + 0.062500s system = 3.250000s CPU (158.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 699, reserve = 689, peak = 723.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net COM3/GNRMC/u_fifo/wr_to_rd_cross_inst/secondary_clk_i will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 971, reserve = 959, peak = 971.
PHY-1001 : End build detailed router design. 4.494550s wall, 4.453125s user + 0.046875s system = 4.500000s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 187240, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.783689s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 1007, reserve = 996, peak = 1007.
PHY-1001 : End phase 1; 0.791350s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (98.7%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 55% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.83799e+06, over cnt = 1351(0%), over = 1358, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1022, reserve = 1012, peak = 1022.
PHY-1001 : End initial routed; 22.730570s wall, 54.593750s user + 0.437500s system = 55.031250s CPU (242.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18207(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.544   |   0.000   |   0   
RUN-1001 :   Hold   |   0.132   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.163655s wall, 3.156250s user + 0.000000s system = 3.156250s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 1041, reserve = 1030, peak = 1041.
PHY-1001 : End phase 2; 25.894362s wall, 57.750000s user + 0.437500s system = 58.187500s CPU (224.7%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.83799e+06, over cnt = 1351(0%), over = 1358, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.218599s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (100.1%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.82597e+06, over cnt = 437(0%), over = 437, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.813559s wall, 1.250000s user + 0.000000s system = 1.250000s CPU (153.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.82621e+06, over cnt = 82(0%), over = 82, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.418828s wall, 0.593750s user + 0.000000s system = 0.593750s CPU (141.8%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.82696e+06, over cnt = 25(0%), over = 25, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.266256s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (117.4%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.82744e+06, over cnt = 5(0%), over = 5, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.193558s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (104.9%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.82763e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.209038s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (97.2%)

PHY-1001 : ===== DR Iter 6 =====
PHY-1022 : len = 1.82763e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 6; 0.234643s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (99.9%)

PHY-1001 : ===== DR Iter 7 =====
PHY-1022 : len = 1.82763e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 7; 0.358245s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (100.3%)

PHY-1001 : ===== DR Iter 8 =====
PHY-1022 : len = 1.82768e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 8; 0.154657s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (101.0%)

PHY-1001 : ==== DR Iter 9 ====
PHY-1022 : len = 1.82772e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 9; 0.143098s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (98.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18207(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.978   |   0.000   |   0   
RUN-1001 :   Hold   |   0.132   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.170149s wall, 3.156250s user + 0.015625s system = 3.171875s CPU (100.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 332 feed throughs used by 281 nets
PHY-1001 : End commit to database; 2.203071s wall, 2.171875s user + 0.031250s system = 2.203125s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1119, reserve = 1111, peak = 1119.
PHY-1001 : End phase 3; 8.882941s wall, 9.500000s user + 0.046875s system = 9.546875s CPU (107.5%)

PHY-1003 : Routed, final wirelength = 1.82772e+06
PHY-1001 : Current memory(MB): used = 1123, reserve = 1115, peak = 1123.
PHY-1001 : End export database. 0.061220s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.1%)

PHY-1001 : End detail routing;  40.518244s wall, 72.953125s user + 0.531250s system = 73.484375s CPU (181.4%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67973, tnet num: 19438, tinst num: 8185, tnode num: 92528, tedge num: 112002.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.628213s wall, 1.625000s user + 0.000000s system = 1.625000s CPU (99.8%)

RUN-1004 : used memory is 1007 MB, reserved memory is 1013 MB, peak memory is 1123 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  48.353470s wall, 81.890625s user + 0.625000s system = 82.515625s CPU (170.7%)

RUN-1004 : used memory is 1012 MB, reserved memory is 1020 MB, peak memory is 1123 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8684   out of  19600   44.31%
#reg                    12550   out of  19600   64.03%
#le                     15085
  #lut only              2535   out of  15085   16.80%
  #reg only              6401   out of  15085   42.43%
  #lut&reg               6149   out of  15085   40.76%
#dsp                        0   out of     29    0.00%
#bram                      50   out of     64   78.12%
  #bram9k                  50
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                                                 Type               DriverType         Driver                    Fanout
#1        COM3/GNRMC/u_fifo/wr_to_rd_cross_inst/secondary_clk_i    GCLK               pll                CLK100M/pll_inst.clkc0    6855
#2        config_inst_syn_9                                        GCLK               config             config_inst.jtck          212
#3        clk_in_dup_1                                             GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         M3        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         P6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+------------------------------------------------------------------------------------------------------------------------------------------+
|Instance                             |Module                                         |le     |lut     |ripple  |seq     |bram    |dsp     |
+------------------------------------------------------------------------------------------------------------------------------------------+
|top                                  |INS600M_21A                                    |15085  |7220    |1464    |12594   |50      |0       |
|  AnyFog_dataX                       |AnyFog                                         |208    |91      |22      |174     |0       |0       |
|    UART_RX_COM3                     |UART_RX115200E                                 |86     |60      |22      |52      |0       |0       |
|  AnyFog_dataY                       |AnyFog                                         |204    |101     |22      |169     |0       |0       |
|    UART_RX_COM3                     |UART_RX115200E                                 |85     |59      |22      |50      |0       |0       |
|  AnyFog_dataZ                       |AnyFog                                         |207    |86      |22      |171     |0       |0       |
|    UART_RX_COM3                     |UART_RX115200E                                 |85     |58      |22      |49      |0       |0       |
|  CLK100M                            |global_clock                                   |0      |0       |0       |0       |0       |0       |
|  COM2                               |COM2_Control                                   |2836   |511     |34      |2764    |0       |0       |
|    PPPNAV_com2                      |PPPNAV                                         |222    |102     |5       |209     |0       |0       |
|    STADOP_com2                      |STADOP                                         |550    |93      |0       |545     |0       |0       |
|    UART_RX_COM3                     |UART_RX460800                                  |63     |46      |14      |41      |0       |0       |
|    head_com2                        |uniheading                                     |270    |65      |5       |256     |0       |0       |
|    uart_com2                        |Agrica                                         |1421   |191     |10      |1403    |0       |0       |
|  COM3                               |COM3_Control                                   |389    |190     |44      |325     |2       |0       |
|    GNRMC                            |GNRMC_Tx                                       |176    |81      |30      |139     |2       |0       |
|      u_fifo                         |Asys_fifo8x8                                   |120    |54      |25      |91      |1       |0       |
|        ram_inst                     |ram_infer_Asys_fifo8x8                         |0      |0       |0       |0       |1       |0       |
|        rd_to_wr_cross_inst          |fifo_cross_domain_addr_process_al_Asys_fifo8x8 |32     |13      |0       |32      |0       |0       |
|        wr_to_rd_cross_inst          |fifo_cross_domain_addr_process_al_Asys_fifo8x8 |32     |15      |0       |32      |0       |0       |
|    UART_RX_COM3                     |UART_RX460800                                  |61     |43      |14      |39      |0       |0       |
|    rmc_com3                         |Gprmc                                          |152    |66      |0       |147     |0       |0       |
|  DATA                               |Data_Processing                                |8699   |4441    |1056    |7012    |0       |0       |
|    DIV_Dtemp                        |Divider                                        |795    |336     |84      |662     |0       |0       |
|    DIV_Utemp                        |Divider                                        |600    |314     |84      |476     |0       |0       |
|    DIV_accX                         |Divider                                        |622    |316     |84      |496     |0       |0       |
|    DIV_accY                         |Divider                                        |635    |284     |105     |474     |0       |0       |
|    DIV_accZ                         |Divider                                        |605    |388     |132     |401     |0       |0       |
|    DIV_rateX                        |Divider                                        |653    |364     |132     |444     |0       |0       |
|    DIV_rateY                        |Divider                                        |634    |394     |132     |423     |0       |0       |
|    DIV_rateZ                        |Divider                                        |647    |410     |132     |443     |0       |0       |
|    genclk                           |genclk                                         |76     |46      |20      |43      |0       |0       |
|  FMC                                |FMC_Ctrl                                       |437    |385     |43      |339     |0       |0       |
|  IIC                                |I2C_master                                     |303    |260     |11      |253     |0       |0       |
|  IMU_CTRL                           |SCHA634                                        |909    |663     |61      |725     |0       |0       |
|    CtrlData                         |CtrlData                                       |476    |419     |47      |333     |0       |0       |
|      usms                           |Time_1ms                                       |29     |23      |5       |21      |0       |0       |
|    SPIM                             |SPI_SCHA634                                    |433    |244     |14      |392     |0       |0       |
|  POWER                              |POWER_EN                                       |100    |49      |38      |38      |0       |0       |
|  cw_top                             |CW_TOP_WRAPPER                                 |776    |437     |111     |563     |0       |0       |
|    wrapper_cwc_top                  |cwc_top                                        |776    |437     |111     |563     |0       |0       |
|      cfg_int_inst                   |cwc_cfg_int                                    |375    |153     |0       |360     |0       |0       |
|        reg_inst                     |register                                       |373    |151     |0       |358     |0       |0       |
|        tap_inst                     |tap                                            |2      |2       |0       |2       |0       |0       |
|      trigger_inst                   |trigger                                        |401    |284     |111     |203     |0       |0       |
|        bus_inst                     |bus_top                                        |176    |122     |54      |76      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes  |bus_det                                        |28     |18      |10      |10      |0       |0       |
|          BUS_DETECTOR[10]$bus_nodes |bus_det                                        |25     |15      |10      |9       |0       |0       |
|          BUS_DETECTOR[11]$bus_nodes |bus_det                                        |13     |13      |0       |3       |0       |0       |
|          BUS_DETECTOR[12]$bus_nodes |bus_det                                        |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[13]$bus_nodes |bus_det                                        |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes  |bus_det                                        |52     |34      |18      |20      |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes  |bus_det                                        |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes  |bus_det                                        |15     |9       |6       |7       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes  |bus_det                                        |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[5]$bus_nodes  |bus_det                                        |3      |3       |0       |3       |0       |0       |
|          BUS_DETECTOR[6]$bus_nodes  |bus_det                                        |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[7]$bus_nodes  |bus_det                                        |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[8]$bus_nodes  |bus_det                                        |3      |3       |0       |3       |0       |0       |
|          BUS_DETECTOR[9]$bus_nodes  |bus_det                                        |28     |18      |10      |12      |0       |0       |
|        emb_ctrl_inst                |emb_ctrl                                       |136    |102     |29      |87      |0       |0       |
+------------------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13511  
    #2          2       3484   
    #3          3        698   
    #4          4        275   
    #5        5-10       960   
    #6        11-50      424   
    #7       51-100      17    
    #8       101-500      4    
    #9        >500        2    
  Average     2.14             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  1.997943s wall, 3.468750s user + 0.000000s system = 3.468750s CPU (173.6%)

RUN-1004 : used memory is 1013 MB, reserved memory is 1020 MB, peak memory is 1123 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67973, tnet num: 19438, tinst num: 8185, tnode num: 92528, tedge num: 112002.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.607752s wall, 1.609375s user + 0.000000s system = 1.609375s CPU (100.1%)

RUN-1004 : used memory is 1015 MB, reserved memory is 1022 MB, peak memory is 1123 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19438 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.262878s wall, 1.250000s user + 0.015625s system = 1.265625s CPU (100.2%)

RUN-1004 : used memory is 1053 MB, reserved memory is 1048 MB, peak memory is 1123 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 1c20620e118e075ff35a1cd93778bfb6257150a0937144a4d9e00bcc01d776fb -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8185
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19440, pip num: 150060
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 332
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3261 valid insts, and 416741 bits set as '1'.
BIT-1004 : the usercode register value: 00000000100000011010011101000100
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  10.540628s wall, 102.500000s user + 0.171875s system = 102.671875s CPU (974.1%)

RUN-1004 : used memory is 1196 MB, reserved memory is 1182 MB, peak memory is 1311 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250710_164046.log"

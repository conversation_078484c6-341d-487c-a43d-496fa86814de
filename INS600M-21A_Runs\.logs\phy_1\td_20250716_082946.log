============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Wed Jul 16 08:29:46 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  6.114103s wall, 1.812500s user + 4.250000s system = 6.062500s CPU (99.2%)

RUN-1004 : used memory is 78 MB, reserved memory is 40 MB, peak memory is 89 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.761484s wall, 1.656250s user + 0.109375s system = 1.765625s CPU (100.2%)

RUN-1004 : used memory is 299 MB, reserved memory is 268 MB, peak memory is 302 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 2 view nodes, 24 trigger nets, 24 data nets.
KIT-1004 : Chipwatcher code = 1010010110001101
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=78) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=78) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=78)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=78)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22204/12 useful/useless nets, 19219/4 useful/useless insts
SYN-1016 : Merged 17 instances.
SYN-1032 : 21980/16 useful/useless nets, 19543/12 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 2 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 2 mux instances.
SYN-1015 : Optimize round 1, 279 better
SYN-1014 : Optimize round 2
SYN-1032 : 21771/30 useful/useless nets, 19334/32 useful/useless insts
SYN-1015 : Optimize round 2, 64 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.071712s wall, 2.046875s user + 0.015625s system = 2.062500s CPU (99.6%)

RUN-1004 : used memory is 324 MB, reserved memory is 291 MB, peak memory is 326 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 21795/155 useful/useless nets, 19379/29 useful/useless insts
SYN-1016 : Merged 21 instances.
SYN-2571 : Optimize after map_dsp, round 1, 205 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 24 instances.
SYN-2501 : Optimize round 1, 50 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 14 macro adder
SYN-1019 : Optimized 9 mux instances.
SYN-1016 : Merged 10 instances.
SYN-1032 : 22166/5 useful/useless nets, 19750/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80402, tnet num: 22166, tinst num: 19749, tnode num: 113002, tedge num: 125646.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.064874s wall, 1.031250s user + 0.031250s system = 1.062500s CPU (99.8%)

RUN-1004 : used memory is 460 MB, reserved memory is 429 MB, peak memory is 460 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22166 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 173 (3.57), #lev = 7 (1.79)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 173 (3.57), #lev = 7 (1.79)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 387 instances into 173 LUTs, name keeping = 77%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 290 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 108 adder to BLE ...
SYN-4008 : Packed 108 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  3.803908s wall, 3.671875s user + 0.140625s system = 3.812500s CPU (100.2%)

RUN-1004 : used memory is 346 MB, reserved memory is 311 MB, peak memory is 567 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  6.142488s wall, 5.953125s user + 0.187500s system = 6.140625s CPU (100.0%)

RUN-1004 : used memory is 347 MB, reserved memory is 312 MB, peak memory is 567 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (177 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19058 instances
RUN-0007 : 5502 luts, 12036 seqs, 937 mslices, 494 lslices, 60 pads, 24 brams, 0 dsps
RUN-1001 : There are total 21502 nets
RUN-1001 : 16194 nets have 2 pins
RUN-1001 : 4140 nets have [3 - 5] pins
RUN-1001 : 816 nets have [6 - 10] pins
RUN-1001 : 224 nets have [11 - 20] pins
RUN-1001 : 110 nets have [21 - 99] pins
RUN-1001 : 18 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4787     
RUN-1001 :   No   |  No   |  Yes  |     631     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     349     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19056 instances, 5502 luts, 12036 seqs, 1431 slices, 283 macros(1431 instances: 937 mslices 494 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 61%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 78965, tnet num: 21500, tinst num: 19056, tnode num: 111159, tedge num: 124050.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.096022s wall, 1.078125s user + 0.015625s system = 1.093750s CPU (99.8%)

RUN-1004 : used memory is 518 MB, reserved memory is 491 MB, peak memory is 567 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21500 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.846154s wall, 1.828125s user + 0.015625s system = 1.843750s CPU (99.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.53283e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19056.
PHY-3001 : Level 1 #clusters 2163.
PHY-3001 : End clustering;  0.119886s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (182.5%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 61%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 844426, overlap = 608.344
PHY-3002 : Step(2): len = 759718, overlap = 652.188
PHY-3002 : Step(3): len = 489291, overlap = 846.688
PHY-3002 : Step(4): len = 427259, overlap = 919.812
PHY-3002 : Step(5): len = 340329, overlap = 987.438
PHY-3002 : Step(6): len = 303317, overlap = 1051.03
PHY-3002 : Step(7): len = 253913, overlap = 1117.94
PHY-3002 : Step(8): len = 228108, overlap = 1149.22
PHY-3002 : Step(9): len = 203251, overlap = 1195.38
PHY-3002 : Step(10): len = 188907, overlap = 1202.81
PHY-3002 : Step(11): len = 171569, overlap = 1257.53
PHY-3002 : Step(12): len = 160779, overlap = 1294.41
PHY-3002 : Step(13): len = 149863, overlap = 1313.09
PHY-3002 : Step(14): len = 137380, overlap = 1350.34
PHY-3002 : Step(15): len = 131162, overlap = 1362.97
PHY-3002 : Step(16): len = 121861, overlap = 1376.97
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.28455e-06
PHY-3002 : Step(17): len = 127847, overlap = 1359.16
PHY-3002 : Step(18): len = 178884, overlap = 1244.94
PHY-3002 : Step(19): len = 194216, overlap = 1157.12
PHY-3002 : Step(20): len = 195991, overlap = 1086.31
PHY-3002 : Step(21): len = 190489, overlap = 1052.84
PHY-3002 : Step(22): len = 182821, overlap = 1043.56
PHY-3002 : Step(23): len = 177933, overlap = 1038.59
PHY-3002 : Step(24): len = 173651, overlap = 1033.5
PHY-3002 : Step(25): len = 170946, overlap = 1029.44
PHY-3002 : Step(26): len = 167513, overlap = 1019.5
PHY-3002 : Step(27): len = 164837, overlap = 1006.06
PHY-3002 : Step(28): len = 162321, overlap = 1024.34
PHY-3002 : Step(29): len = 162850, overlap = 1017.22
PHY-3002 : Step(30): len = 161932, overlap = 1020.94
PHY-3002 : Step(31): len = 162110, overlap = 1011.56
PHY-3002 : Step(32): len = 162367, overlap = 1007.47
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.56909e-06
PHY-3002 : Step(33): len = 171590, overlap = 985.531
PHY-3002 : Step(34): len = 189007, overlap = 905.688
PHY-3002 : Step(35): len = 193396, overlap = 863.25
PHY-3002 : Step(36): len = 196230, overlap = 853.781
PHY-3002 : Step(37): len = 195858, overlap = 856
PHY-3002 : Step(38): len = 194906, overlap = 854.531
PHY-3002 : Step(39): len = 192633, overlap = 860.875
PHY-3002 : Step(40): len = 192080, overlap = 866.875
PHY-3002 : Step(41): len = 190873, overlap = 880.594
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 5.13818e-06
PHY-3002 : Step(42): len = 200567, overlap = 832.062
PHY-3002 : Step(43): len = 214718, overlap = 738.438
PHY-3002 : Step(44): len = 218689, overlap = 683.562
PHY-3002 : Step(45): len = 220579, overlap = 679.781
PHY-3002 : Step(46): len = 219867, overlap = 686.375
PHY-3002 : Step(47): len = 219445, overlap = 696.812
PHY-3002 : Step(48): len = 217424, overlap = 702.375
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 1.02764e-05
PHY-3002 : Step(49): len = 230082, overlap = 688.594
PHY-3002 : Step(50): len = 243938, overlap = 640.094
PHY-3002 : Step(51): len = 248502, overlap = 614.906
PHY-3002 : Step(52): len = 251375, overlap = 585.656
PHY-3002 : Step(53): len = 250417, overlap = 581.688
PHY-3002 : Step(54): len = 249299, overlap = 553.656
PHY-3002 : Step(55): len = 247469, overlap = 545.438
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 2.05527e-05
PHY-3002 : Step(56): len = 259531, overlap = 531.875
PHY-3002 : Step(57): len = 273050, overlap = 486.5
PHY-3002 : Step(58): len = 277338, overlap = 426.5
PHY-3002 : Step(59): len = 279613, overlap = 388.75
PHY-3002 : Step(60): len = 279473, overlap = 382
PHY-3002 : Step(61): len = 278994, overlap = 369.156
PHY-3002 : Step(62): len = 277673, overlap = 365.969
PHY-3002 : Step(63): len = 276376, overlap = 378.406
PHY-3002 : Step(64): len = 275857, overlap = 396.656
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 4.11055e-05
PHY-3002 : Step(65): len = 286942, overlap = 352.094
PHY-3002 : Step(66): len = 297799, overlap = 337.594
PHY-3002 : Step(67): len = 298719, overlap = 328.625
PHY-3002 : Step(68): len = 298599, overlap = 304.094
PHY-3002 : Step(69): len = 297132, overlap = 281.375
PHY-3002 : Step(70): len = 296187, overlap = 261.906
PHY-3002 : Step(71): len = 294577, overlap = 236.5
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 8.22109e-05
PHY-3002 : Step(72): len = 302369, overlap = 224
PHY-3002 : Step(73): len = 310782, overlap = 227.688
PHY-3002 : Step(74): len = 312959, overlap = 211.062
PHY-3002 : Step(75): len = 314369, overlap = 204.125
PHY-3002 : Step(76): len = 313902, overlap = 205.844
PHY-3002 : Step(77): len = 312966, overlap = 215.812
PHY-3002 : Step(78): len = 309922, overlap = 218.75
PHY-3002 : Step(79): len = 309364, overlap = 219.094
PHY-3002 : Step(80): len = 307308, overlap = 215.25
PHY-3002 : Step(81): len = 307401, overlap = 216.906
PHY-3002 : Step(82): len = 306641, overlap = 212.062
PHY-3002 : Step(83): len = 307154, overlap = 207.375
PHY-3002 : Step(84): len = 306328, overlap = 233
PHY-3002 : Step(85): len = 306644, overlap = 251.312
PHY-3002 : Step(86): len = 305618, overlap = 238.125
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000164422
PHY-3002 : Step(87): len = 310138, overlap = 244.406
PHY-3002 : Step(88): len = 315824, overlap = 237.25
PHY-3002 : Step(89): len = 318089, overlap = 232.938
PHY-3002 : Step(90): len = 320399, overlap = 218.75
PHY-3002 : Step(91): len = 320317, overlap = 214.875
PHY-3002 : Step(92): len = 320895, overlap = 216.531
PHY-3002 : Step(93): len = 319427, overlap = 213.625
PHY-3002 : Step(94): len = 319646, overlap = 204.156
PHY-3002 : Step(95): len = 319114, overlap = 189.906
PHY-3002 : Step(96): len = 319821, overlap = 191.031
PHY-3002 : Step(97): len = 318421, overlap = 198.438
PHY-3002 : Step(98): len = 318678, overlap = 198.406
PHY-3002 : Step(99): len = 318345, overlap = 217.062
PHY-3002 : Step(100): len = 319095, overlap = 213.875
PHY-3002 : Step(101): len = 317064, overlap = 212.25
PHY-3002 : Step(102): len = 316965, overlap = 214.188
PHY-3002 : Step(103): len = 316651, overlap = 217
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000328844
PHY-3002 : Step(104): len = 320465, overlap = 208.844
PHY-3002 : Step(105): len = 323948, overlap = 202.406
PHY-3002 : Step(106): len = 324832, overlap = 195.969
PHY-3002 : Step(107): len = 326034, overlap = 194.344
PHY-3002 : Step(108): len = 326828, overlap = 196.156
PHY-3002 : Step(109): len = 327865, overlap = 199.062
PHY-3002 : Step(110): len = 326268, overlap = 190.625
PHY-3002 : Step(111): len = 325811, overlap = 194.031
PHY-3002 : Step(112): len = 325888, overlap = 189.562
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000623918
PHY-3002 : Step(113): len = 327376, overlap = 190.844
PHY-3002 : Step(114): len = 330401, overlap = 184.688
PHY-3002 : Step(115): len = 331791, overlap = 187.531
PHY-3002 : Step(116): len = 333488, overlap = 167.969
PHY-3002 : Step(117): len = 333377, overlap = 166.625
PHY-3002 : Step(118): len = 333305, overlap = 162.625
PHY-3002 : Step(119): len = 332618, overlap = 156.312
PHY-3002 : Step(120): len = 332542, overlap = 151.594
PHY-3002 : Step(121): len = 332346, overlap = 150.781
PHY-3002 : Step(122): len = 332542, overlap = 151.938
PHY-3002 : Step(123): len = 332507, overlap = 152.875
PHY-3001 : :::11::: Try harder cell spreading with beta_ = 0.00104108
PHY-3002 : Step(124): len = 333295, overlap = 151.781
PHY-3002 : Step(125): len = 336670, overlap = 146.781
PHY-3002 : Step(126): len = 337354, overlap = 145.312
PHY-3002 : Step(127): len = 337732, overlap = 149.625
PHY-3002 : Step(128): len = 337814, overlap = 149.312
PHY-3002 : Step(129): len = 338044, overlap = 146.156
PHY-3002 : Step(130): len = 337901, overlap = 143.531
PHY-3002 : Step(131): len = 337885, overlap = 147.344
PHY-3002 : Step(132): len = 338065, overlap = 138.562
PHY-3002 : Step(133): len = 338455, overlap = 137.438
PHY-3002 : Step(134): len = 338593, overlap = 132.438
PHY-3002 : Step(135): len = 338686, overlap = 132.625
PHY-3002 : Step(136): len = 338640, overlap = 129.406
PHY-3002 : Step(137): len = 338618, overlap = 129.062
PHY-3002 : Step(138): len = 338541, overlap = 125.812
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.011847s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (131.9%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21502.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 437936, over cnt = 1135(3%), over = 5110, worst = 44
PHY-1001 : End global iterations;  0.809068s wall, 1.000000s user + 0.046875s system = 1.046875s CPU (129.4%)

PHY-1001 : Congestion index: top1 = 71.81, top5 = 50.47, top10 = 41.64, top15 = 36.24.
PHY-3001 : End congestion estimation;  0.995951s wall, 1.203125s user + 0.046875s system = 1.250000s CPU (125.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21500 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.801001s wall, 0.781250s user + 0.015625s system = 0.796875s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000108842
PHY-3002 : Step(139): len = 377280, overlap = 109.656
PHY-3002 : Step(140): len = 391846, overlap = 114.969
PHY-3002 : Step(141): len = 394098, overlap = 112.938
PHY-3002 : Step(142): len = 392178, overlap = 96.2188
PHY-3002 : Step(143): len = 396308, overlap = 81.0625
PHY-3002 : Step(144): len = 404074, overlap = 78.125
PHY-3002 : Step(145): len = 404045, overlap = 81.6562
PHY-3002 : Step(146): len = 407447, overlap = 84.5938
PHY-3002 : Step(147): len = 411069, overlap = 84.2188
PHY-3002 : Step(148): len = 412618, overlap = 79.4062
PHY-3002 : Step(149): len = 415500, overlap = 80
PHY-3002 : Step(150): len = 415736, overlap = 78.75
PHY-3002 : Step(151): len = 416449, overlap = 86.7812
PHY-3002 : Step(152): len = 417425, overlap = 88.9375
PHY-3002 : Step(153): len = 417438, overlap = 92.6875
PHY-3002 : Step(154): len = 419809, overlap = 100.656
PHY-3002 : Step(155): len = 421581, overlap = 104.062
PHY-3002 : Step(156): len = 419569, overlap = 108.469
PHY-3002 : Step(157): len = 419011, overlap = 110.531
PHY-3002 : Step(158): len = 419315, overlap = 110.906
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000217684
PHY-3002 : Step(159): len = 419929, overlap = 111.719
PHY-3002 : Step(160): len = 421833, overlap = 110.5
PHY-3002 : Step(161): len = 423375, overlap = 105.312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000426453
PHY-3002 : Step(162): len = 432050, overlap = 104.062
PHY-3002 : Step(163): len = 438826, overlap = 98.7188
PHY-3002 : Step(164): len = 440560, overlap = 99.5938
PHY-3002 : Step(165): len = 442499, overlap = 100.344
PHY-3002 : Step(166): len = 442794, overlap = 101.062
PHY-3002 : Step(167): len = 441917, overlap = 98.375
PHY-3002 : Step(168): len = 442657, overlap = 91.875
PHY-3002 : Step(169): len = 443491, overlap = 90.0938
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 99/21502.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 510944, over cnt = 2050(5%), over = 9304, worst = 43
PHY-1001 : End global iterations;  0.978854s wall, 1.625000s user + 0.031250s system = 1.656250s CPU (169.2%)

PHY-1001 : Congestion index: top1 = 72.11, top5 = 56.50, top10 = 48.55, top15 = 43.79.
PHY-3001 : End congestion estimation;  1.218695s wall, 1.859375s user + 0.031250s system = 1.890625s CPU (155.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21500 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.836536s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (99.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000101232
PHY-3002 : Step(170): len = 448291, overlap = 312.688
PHY-3002 : Step(171): len = 451534, overlap = 266.281
PHY-3002 : Step(172): len = 444924, overlap = 258.469
PHY-3002 : Step(173): len = 440739, overlap = 251.188
PHY-3002 : Step(174): len = 438319, overlap = 228
PHY-3002 : Step(175): len = 436413, overlap = 219.281
PHY-3002 : Step(176): len = 435749, overlap = 212.531
PHY-3002 : Step(177): len = 431672, overlap = 212.5
PHY-3002 : Step(178): len = 429465, overlap = 211.562
PHY-3002 : Step(179): len = 429152, overlap = 218
PHY-3002 : Step(180): len = 427290, overlap = 217.75
PHY-3002 : Step(181): len = 425221, overlap = 212.5
PHY-3002 : Step(182): len = 425076, overlap = 210.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000202464
PHY-3002 : Step(183): len = 425083, overlap = 202.25
PHY-3002 : Step(184): len = 426218, overlap = 194.219
PHY-3002 : Step(185): len = 426619, overlap = 193.344
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000404929
PHY-3002 : Step(186): len = 430566, overlap = 186.5
PHY-3002 : Step(187): len = 438339, overlap = 171.062
PHY-3002 : Step(188): len = 439662, overlap = 165.188
PHY-3002 : Step(189): len = 440326, overlap = 159.219
PHY-3002 : Step(190): len = 439437, overlap = 155.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000809858
PHY-3002 : Step(191): len = 441011, overlap = 150.062
PHY-3002 : Step(192): len = 444571, overlap = 139.125
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 78965, tnet num: 21500, tinst num: 19056, tnode num: 111159, tedge num: 124050.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.408717s wall, 1.375000s user + 0.031250s system = 1.406250s CPU (99.8%)

RUN-1004 : used memory is 559 MB, reserved memory is 539 MB, peak memory is 691 MB
OPT-1001 : Total overflow 488.75 peak overflow 4.06
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 618/21502.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 521000, over cnt = 2297(6%), over = 8300, worst = 21
PHY-1001 : End global iterations;  1.174553s wall, 1.812500s user + 0.046875s system = 1.859375s CPU (158.3%)

PHY-1001 : Congestion index: top1 = 57.48, top5 = 46.93, top10 = 42.00, top15 = 38.85.
PHY-1001 : End incremental global routing;  1.394760s wall, 2.031250s user + 0.046875s system = 2.078125s CPU (149.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21500 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.879613s wall, 0.875000s user + 0.000000s system = 0.875000s CPU (99.5%)

OPT-1001 : 14 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 18979 has valid locations, 207 needs to be replaced
PHY-3001 : design contains 19249 instances, 5583 luts, 12148 seqs, 1431 slices, 283 macros(1431 instances: 937 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 458893
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16781/21695.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 531528, over cnt = 2328(6%), over = 8313, worst = 21
PHY-1001 : End global iterations;  0.182363s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (137.1%)

PHY-1001 : Congestion index: top1 = 57.28, top5 = 47.08, top10 = 42.22, top15 = 39.07.
PHY-3001 : End congestion estimation;  0.390208s wall, 0.437500s user + 0.015625s system = 0.453125s CPU (116.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 79578, tnet num: 21693, tinst num: 19249, tnode num: 112030, tedge num: 124890.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.469728s wall, 1.421875s user + 0.046875s system = 1.468750s CPU (99.9%)

RUN-1004 : used memory is 601 MB, reserved memory is 584 MB, peak memory is 691 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21693 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.428801s wall, 2.343750s user + 0.078125s system = 2.421875s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(193): len = 458895, overlap = 0.25
PHY-3002 : Step(194): len = 459817, overlap = 0.375
PHY-3002 : Step(195): len = 460002, overlap = 0.375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 16819/21695.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 532688, over cnt = 2331(6%), over = 8356, worst = 21
PHY-1001 : End global iterations;  0.171843s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (118.2%)

PHY-1001 : Congestion index: top1 = 57.35, top5 = 47.26, top10 = 42.38, top15 = 39.20.
PHY-3001 : End congestion estimation;  0.396752s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (106.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21693 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.907416s wall, 0.906250s user + 0.000000s system = 0.906250s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000588212
PHY-3002 : Step(196): len = 460056, overlap = 141.062
PHY-3002 : Step(197): len = 460345, overlap = 141
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00117642
PHY-3002 : Step(198): len = 460551, overlap = 141.188
PHY-3002 : Step(199): len = 460551, overlap = 141.188
PHY-3001 : Final: Len = 460551, Over = 141.188
PHY-3001 : End incremental placement;  4.929877s wall, 5.187500s user + 0.218750s system = 5.406250s CPU (109.7%)

OPT-1001 : Total overflow 493.09 peak overflow 4.12
OPT-1001 : End high-fanout net optimization;  7.657223s wall, 8.625000s user + 0.265625s system = 8.890625s CPU (116.1%)

OPT-1001 : Current memory(MB): used = 695, reserve = 676, peak = 711.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16829/21695.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 533840, over cnt = 2271(6%), over = 7890, worst = 21
PHY-1002 : len = 578008, over cnt = 1456(4%), over = 3653, worst = 17
PHY-1002 : len = 614960, over cnt = 465(1%), over = 915, worst = 13
PHY-1002 : len = 622056, over cnt = 208(0%), over = 432, worst = 10
PHY-1002 : len = 629712, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.161861s wall, 1.671875s user + 0.000000s system = 1.671875s CPU (143.9%)

PHY-1001 : Congestion index: top1 = 48.77, top5 = 42.28, top10 = 39.21, top15 = 37.13.
OPT-1001 : End congestion update;  1.403273s wall, 1.890625s user + 0.000000s system = 1.890625s CPU (134.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 21693 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.803356s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (101.1%)

OPT-0007 : Start: WNS 4169 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.211569s wall, 2.703125s user + 0.000000s system = 2.703125s CPU (122.2%)

OPT-1001 : Current memory(MB): used = 692, reserve = 673, peak = 711.
OPT-1001 : End physical optimization;  11.588072s wall, 13.046875s user + 0.375000s system = 13.421875s CPU (115.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5583 LUT to BLE ...
SYN-4008 : Packed 5583 LUT and 2676 SEQ to BLE.
SYN-4003 : Packing 9472 remaining SEQ's ...
SYN-4005 : Packed 3285 SEQ with LUT/SLICE
SYN-4006 : 128 single LUT's are left
SYN-4006 : 6187 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11770/13554 primitive instances ...
PHY-3001 : End packing;  2.612259s wall, 2.609375s user + 0.000000s system = 2.609375s CPU (99.9%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 7974 instances
RUN-1001 : 3943 mslices, 3942 lslices, 60 pads, 24 brams, 0 dsps
RUN-1001 : There are total 19072 nets
RUN-1001 : 13419 nets have 2 pins
RUN-1001 : 4277 nets have [3 - 5] pins
RUN-1001 : 881 nets have [6 - 10] pins
RUN-1001 : 355 nets have [11 - 20] pins
RUN-1001 : 131 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 7972 instances, 7885 slices, 283 macros(1431 instances: 937 mslices 494 lslices)
PHY-3001 : Cell area utilization is 84%
PHY-3001 : After packing: Len = 478570, Over = 351
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7627/19072.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 601456, over cnt = 1434(4%), over = 2285, worst = 7
PHY-1002 : len = 608624, over cnt = 863(2%), over = 1175, worst = 6
PHY-1002 : len = 620544, over cnt = 247(0%), over = 322, worst = 5
PHY-1002 : len = 625048, over cnt = 50(0%), over = 63, worst = 4
PHY-1002 : len = 626416, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.150644s wall, 1.625000s user + 0.031250s system = 1.656250s CPU (143.9%)

PHY-1001 : Congestion index: top1 = 49.48, top5 = 42.75, top10 = 38.90, top15 = 36.69.
PHY-3001 : End congestion estimation;  1.456590s wall, 1.937500s user + 0.031250s system = 1.968750s CPU (135.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66047, tnet num: 19070, tinst num: 7972, tnode num: 89711, tedge num: 108809.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.638328s wall, 1.593750s user + 0.046875s system = 1.640625s CPU (100.1%)

RUN-1004 : used memory is 590 MB, reserved memory is 577 MB, peak memory is 711 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19070 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.490169s wall, 2.406250s user + 0.078125s system = 2.484375s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.19844e-05
PHY-3002 : Step(200): len = 482500, overlap = 338.5
PHY-3002 : Step(201): len = 480488, overlap = 360
PHY-3002 : Step(202): len = 481948, overlap = 362.25
PHY-3002 : Step(203): len = 485803, overlap = 368
PHY-3002 : Step(204): len = 485796, overlap = 379.5
PHY-3002 : Step(205): len = 486427, overlap = 386.5
PHY-3002 : Step(206): len = 484972, overlap = 390
PHY-3002 : Step(207): len = 483028, overlap = 393.25
PHY-3002 : Step(208): len = 481278, overlap = 399.5
PHY-3002 : Step(209): len = 480064, overlap = 399.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000103969
PHY-3002 : Step(210): len = 485420, overlap = 394.75
PHY-3002 : Step(211): len = 489580, overlap = 386.75
PHY-3002 : Step(212): len = 489818, overlap = 377.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000207938
PHY-3002 : Step(213): len = 496477, overlap = 363.5
PHY-3002 : Step(214): len = 506568, overlap = 344.25
PHY-3002 : Step(215): len = 506165, overlap = 337.5
PHY-3002 : Step(216): len = 505153, overlap = 336.5
PHY-3002 : Step(217): len = 505150, overlap = 340.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.635734s wall, 0.687500s user + 0.656250s system = 1.343750s CPU (211.4%)

PHY-3001 : Trial Legalized: Len = 615586
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 83%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 485/19072.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 697296, over cnt = 2271(6%), over = 3681, worst = 6
PHY-1002 : len = 711840, over cnt = 1343(3%), over = 1867, worst = 6
PHY-1002 : len = 728592, over cnt = 453(1%), over = 632, worst = 6
PHY-1002 : len = 737520, over cnt = 127(0%), over = 153, worst = 3
PHY-1002 : len = 740144, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.743863s wall, 3.140625s user + 0.000000s system = 3.140625s CPU (180.1%)

PHY-1001 : Congestion index: top1 = 48.30, top5 = 44.01, top10 = 41.47, top15 = 39.68.
PHY-3001 : End congestion estimation;  2.085196s wall, 3.468750s user + 0.015625s system = 3.484375s CPU (167.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19070 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.850765s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (99.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000175035
PHY-3002 : Step(218): len = 572694, overlap = 82.5
PHY-3002 : Step(219): len = 554112, overlap = 120.5
PHY-3002 : Step(220): len = 542000, overlap = 162
PHY-3002 : Step(221): len = 533860, overlap = 201.75
PHY-3002 : Step(222): len = 529794, overlap = 226.25
PHY-3002 : Step(223): len = 527289, overlap = 245.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00035007
PHY-3002 : Step(224): len = 532559, overlap = 241
PHY-3002 : Step(225): len = 537810, overlap = 229.75
PHY-3002 : Step(226): len = 537331, overlap = 225
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000700141
PHY-3002 : Step(227): len = 540901, overlap = 222
PHY-3002 : Step(228): len = 549402, overlap = 220.25
PHY-3002 : Step(229): len = 553700, overlap = 217.75
PHY-3002 : Step(230): len = 554499, overlap = 222.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.030529s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (102.4%)

PHY-3001 : Legalized: Len = 594312, Over = 0
PHY-3001 : Spreading special nets. 32 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.073556s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (106.2%)

PHY-3001 : 48 instances has been re-located, deltaX = 16, deltaY = 26, maxDist = 2.
PHY-3001 : Final: Len = 594926, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66047, tnet num: 19070, tinst num: 7972, tnode num: 89711, tedge num: 108809.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.890297s wall, 1.875000s user + 0.015625s system = 1.890625s CPU (100.0%)

RUN-1004 : used memory is 621 MB, reserved memory is 608 MB, peak memory is 711 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3845/19072.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 686192, over cnt = 2050(5%), over = 3228, worst = 7
PHY-1002 : len = 695128, over cnt = 1355(3%), over = 1920, worst = 7
PHY-1002 : len = 711816, over cnt = 553(1%), over = 712, worst = 7
PHY-1002 : len = 714720, over cnt = 397(1%), over = 524, worst = 5
PHY-1002 : len = 723336, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.573414s wall, 2.421875s user + 0.031250s system = 2.453125s CPU (155.9%)

PHY-1001 : Congestion index: top1 = 47.09, top5 = 42.49, top10 = 40.14, top15 = 38.52.
PHY-1001 : End incremental global routing;  1.869270s wall, 2.718750s user + 0.031250s system = 2.750000s CPU (147.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19070 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.820390s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (99.0%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 7907 has valid locations, 15 needs to be replaced
PHY-3001 : design contains 7985 instances, 7898 slices, 283 macros(1431 instances: 937 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 596592
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17189/19083.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 724824, over cnt = 34(0%), over = 36, worst = 2
PHY-1002 : len = 724816, over cnt = 15(0%), over = 15, worst = 1
PHY-1002 : len = 724936, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 724992, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 725040, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.650276s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (103.3%)

PHY-1001 : Congestion index: top1 = 47.28, top5 = 42.59, top10 = 40.20, top15 = 38.59.
PHY-3001 : End congestion estimation;  0.920139s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (103.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66138, tnet num: 19081, tinst num: 7985, tnode num: 89820, tedge num: 108920.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.780914s wall, 1.765625s user + 0.015625s system = 1.781250s CPU (100.0%)

RUN-1004 : used memory is 662 MB, reserved memory is 656 MB, peak memory is 711 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19081 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.591147s wall, 2.578125s user + 0.015625s system = 2.593750s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(231): len = 596486, overlap = 0
PHY-3002 : Step(232): len = 596473, overlap = 0.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17186/19083.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 724160, over cnt = 24(0%), over = 30, worst = 2
PHY-1002 : len = 724208, over cnt = 14(0%), over = 17, worst = 2
PHY-1002 : len = 724224, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 724344, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 724408, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.649226s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (103.5%)

PHY-1001 : Congestion index: top1 = 47.13, top5 = 42.66, top10 = 40.24, top15 = 38.57.
PHY-3001 : End congestion estimation;  0.919081s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (103.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19081 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.801222s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00096718
PHY-3002 : Step(233): len = 596422, overlap = 1.75
PHY-3002 : Step(234): len = 596415, overlap = 2
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006002s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (260.3%)

PHY-3001 : Legalized: Len = 596488, Over = 0
PHY-3001 : End spreading;  0.059010s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.9%)

PHY-3001 : Final: Len = 596488, Over = 0
PHY-3001 : End incremental placement;  5.798503s wall, 6.000000s user + 0.062500s system = 6.062500s CPU (104.6%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  8.933484s wall, 10.109375s user + 0.093750s system = 10.203125s CPU (114.2%)

OPT-1001 : Current memory(MB): used = 708, reserve = 690, peak = 712.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17187/19083.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 724688, over cnt = 8(0%), over = 9, worst = 2
PHY-1002 : len = 724688, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 724696, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.388956s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (104.4%)

PHY-1001 : Congestion index: top1 = 47.03, top5 = 42.56, top10 = 40.18, top15 = 38.57.
OPT-1001 : End congestion update;  0.648806s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (103.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19081 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.686148s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (100.2%)

OPT-0007 : Start: WNS 4197 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.339065s wall, 1.359375s user + 0.000000s system = 1.359375s CPU (101.5%)

OPT-1001 : Current memory(MB): used = 708, reserve = 690, peak = 712.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19081 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.674619s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (101.9%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17202/19083.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 724696, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.110914s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (98.6%)

PHY-1001 : Congestion index: top1 = 47.03, top5 = 42.56, top10 = 40.18, top15 = 38.57.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19081 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.680723s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (98.7%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4197 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 46.586207
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4197ps with logic level 4 
OPT-1001 : End physical optimization;  14.129137s wall, 15.437500s user + 0.109375s system = 15.546875s CPU (110.0%)

RUN-1003 : finish command "place" in  62.560413s wall, 114.093750s user + 6.687500s system = 120.781250s CPU (193.1%)

RUN-1004 : used memory is 626 MB, reserved memory is 605 MB, peak memory is 712 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.475487s wall, 2.578125s user + 0.015625s system = 2.593750s CPU (175.8%)

RUN-1004 : used memory is 627 MB, reserved memory is 605 MB, peak memory is 712 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 7987 instances
RUN-1001 : 3946 mslices, 3952 lslices, 60 pads, 24 brams, 0 dsps
RUN-1001 : There are total 19083 nets
RUN-1001 : 13417 nets have 2 pins
RUN-1001 : 4278 nets have [3 - 5] pins
RUN-1001 : 888 nets have [6 - 10] pins
RUN-1001 : 358 nets have [11 - 20] pins
RUN-1001 : 133 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66138, tnet num: 19081, tinst num: 7985, tnode num: 89820, tedge num: 108920.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.534358s wall, 1.531250s user + 0.000000s system = 1.531250s CPU (99.8%)

RUN-1004 : used memory is 636 MB, reserved memory is 630 MB, peak memory is 712 MB
PHY-1001 : 3946 mslices, 3952 lslices, 60 pads, 24 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19081 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 666792, over cnt = 2188(6%), over = 3608, worst = 8
PHY-1002 : len = 683192, over cnt = 1275(3%), over = 1775, worst = 6
PHY-1002 : len = 695920, over cnt = 637(1%), over = 840, worst = 6
PHY-1002 : len = 708984, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 709200, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.561295s wall, 2.671875s user + 0.046875s system = 2.718750s CPU (174.1%)

PHY-1001 : Congestion index: top1 = 47.07, top5 = 42.35, top10 = 39.75, top15 = 38.02.
PHY-1001 : End global routing;  1.853832s wall, 2.937500s user + 0.062500s system = 3.000000s CPU (161.8%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 697, reserve = 685, peak = 712.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 965, reserve = 951, peak = 965.
PHY-1001 : End build detailed router design. 4.213092s wall, 4.140625s user + 0.078125s system = 4.218750s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 191976, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.764263s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 1001, reserve = 987, peak = 1001.
PHY-1001 : End phase 1; 0.771355s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (99.3%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.65865e+06, over cnt = 1246(0%), over = 1251, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1015, reserve = 1002, peak = 1015.
PHY-1001 : End initial routed; 14.226312s wall, 43.250000s user + 0.531250s system = 43.781250s CPU (307.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17873(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.123   |   0.000   |   0   
RUN-1001 :   Hold   |   0.122   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.055843s wall, 3.046875s user + 0.000000s system = 3.046875s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 1028, reserve = 1015, peak = 1028.
PHY-1001 : End phase 2; 17.282289s wall, 46.296875s user + 0.531250s system = 46.828125s CPU (271.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.65865e+06, over cnt = 1246(0%), over = 1251, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.211141s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (96.2%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.64874e+06, over cnt = 438(0%), over = 439, worst = 2, crit = 0
PHY-1001 : End DR Iter 1; 0.467594s wall, 0.890625s user + 0.000000s system = 0.890625s CPU (190.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.65017e+06, over cnt = 67(0%), over = 67, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.318917s wall, 0.531250s user + 0.015625s system = 0.546875s CPU (171.5%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.65056e+06, over cnt = 25(0%), over = 25, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.182806s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (128.2%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.65116e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 4; 0.158031s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (108.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17873(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.111   |   0.000   |   0   
RUN-1001 :   Hold   |   0.122   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.046789s wall, 3.046875s user + 0.000000s system = 3.046875s CPU (100.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 305 feed throughs used by 274 nets
PHY-1001 : End commit to database; 1.995140s wall, 1.968750s user + 0.031250s system = 2.000000s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 1111, reserve = 1101, peak = 1111.
PHY-1001 : End phase 3; 6.798779s wall, 7.453125s user + 0.046875s system = 7.500000s CPU (110.3%)

PHY-1003 : Routed, final wirelength = 1.65116e+06
PHY-1001 : Current memory(MB): used = 1115, reserve = 1105, peak = 1115.
PHY-1001 : End export database. 0.055168s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (85.0%)

PHY-1001 : End detail routing;  29.513457s wall, 59.109375s user + 0.656250s system = 59.765625s CPU (202.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66138, tnet num: 19081, tinst num: 7985, tnode num: 89820, tedge num: 108920.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.587222s wall, 1.593750s user + 0.000000s system = 1.593750s CPU (100.4%)

RUN-1004 : used memory is 1048 MB, reserved memory is 1047 MB, peak memory is 1115 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  36.737943s wall, 67.375000s user + 0.765625s system = 68.140625s CPU (185.5%)

RUN-1004 : used memory is 1047 MB, reserved memory is 1047 MB, peak memory is 1115 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8610   out of  19600   43.93%
#reg                    12248   out of  19600   62.49%
#le                     14743
  #lut only              2495   out of  14743   16.92%
  #reg only              6133   out of  14743   41.60%
  #lut&reg               6115   out of  14743   41.48%
#dsp                        0   out of     29    0.00%
#bram                      24   out of     64   37.50%
  #bram9k                  22
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6774
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          107
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         M3        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D14        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         P6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT        D16        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        R15        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |14743  |7179    |1431    |12292   |24      |0       |
|  AnyFog_dataX                      |AnyFog          |208    |70      |22      |172     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |55      |22      |50      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |217    |101     |22      |174     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |93     |60      |22      |50      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |213    |97      |22      |172     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |91     |63      |22      |50      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2936   |675     |39      |2848    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |63     |34      |5       |53      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |215    |81      |5       |205     |0       |0       |
|    STADOP_com2                     |STADOP          |535    |106     |0       |534     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |63     |48      |14      |40      |0       |0       |
|    head_com2                       |uniheading      |264    |63      |5       |252     |0       |0       |
|    rmc_com2                        |Gprmc           |44     |44      |0       |33      |0       |0       |
|    uart_com2                       |Agrica          |1427   |256     |10      |1406    |0       |0       |
|  COM3                              |COM3_Control    |278    |164     |19      |235     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |63     |35      |5       |54      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |40      |14      |39      |0       |0       |
|    rmc_com3                        |Gprmc           |150    |89      |0       |142     |0       |0       |
|  DATA                              |Data_Processing |8665   |4465    |1065    |6971    |0       |0       |
|    DIV_Dtemp                       |Divider         |788    |387     |84      |662     |0       |0       |
|    DIV_Utemp                       |Divider         |601    |313     |84      |479     |0       |0       |
|    DIV_accX                        |Divider         |560    |352     |84      |432     |0       |0       |
|    DIV_accY                        |Divider         |648    |323     |114     |468     |0       |0       |
|    DIV_accZ                        |Divider         |639    |396     |132     |432     |0       |0       |
|    DIV_rateX                       |Divider         |723    |361     |132     |519     |0       |0       |
|    DIV_rateY                       |Divider         |567    |336     |132     |365     |0       |0       |
|    DIV_rateZ                       |Divider         |634    |354     |132     |428     |0       |0       |
|    genclk                          |genclk          |74     |41      |20      |41      |0       |0       |
|  FMC                               |FMC_Ctrl        |420    |368     |43      |330     |0       |0       |
|  IIC                               |I2C_master      |325    |251     |11      |272     |0       |0       |
|  IMU_CTRL                          |SCHA634         |919    |664     |61      |736     |0       |0       |
|    CtrlData                        |CtrlData        |473    |418     |47      |333     |0       |0       |
|      usms                          |Time_1ms        |27     |22      |5       |19      |0       |0       |
|    SPIM                            |SPI_SCHA634     |446    |246     |14      |403     |0       |0       |
|  POWER                             |POWER_EN        |95     |49      |38      |35      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |455    |273     |89      |291     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |455    |273     |89      |291     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |169    |101     |0       |160     |0       |0       |
|        reg_inst                    |register        |166    |98      |0       |157     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |286    |172     |89      |131     |0       |0       |
|        bus_inst                    |bus_top         |79     |50      |28      |30      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |27     |16      |10      |10      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |52     |34      |18      |20      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |126    |87      |29      |76      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13356  
    #2          2       3392   
    #3          3        624   
    #4          4        262   
    #5        5-10       940   
    #6        11-50      432   
    #7       51-100       7    
    #8       101-500      3    
    #9        >500        2    
  Average     2.11             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  1.803324s wall, 3.125000s user + 0.000000s system = 3.125000s CPU (173.3%)

RUN-1004 : used memory is 1048 MB, reserved memory is 1048 MB, peak memory is 1115 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66138, tnet num: 19081, tinst num: 7985, tnode num: 89820, tedge num: 108920.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.510341s wall, 1.500000s user + 0.000000s system = 1.500000s CPU (99.3%)

RUN-1004 : used memory is 1049 MB, reserved memory is 1050 MB, peak memory is 1115 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19081 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.163275s wall, 1.171875s user + 0.000000s system = 1.171875s CPU (100.7%)

RUN-1004 : used memory is 1054 MB, reserved memory is 1053 MB, peak memory is 1115 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 057bf4ae9dc0d18f7126c20ce7aec8c31cdd0d41110c6b742e6e5620711a0ef6 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 7985
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19083, pip num: 142380
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 305
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3234 valid insts, and 399791 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101000001010010110001101
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  9.616532s wall, 96.687500s user + 0.156250s system = 96.843750s CPU (1007.1%)

RUN-1004 : used memory is 1173 MB, reserved memory is 1158 MB, peak memory is 1287 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250716_082946.log"

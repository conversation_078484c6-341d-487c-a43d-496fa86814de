============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Aug 21 10:41:59 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.707610s wall, 1.609375s user + 4.078125s system = 5.687500s CPU (99.6%)

RUN-1004 : used memory is 79 MB, reserved memory is 39 MB, peak memory is 90 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.958779s wall, 1.843750s user + 0.109375s system = 1.953125s CPU (99.7%)

RUN-1004 : used memory is 302 MB, reserved memory is 271 MB, peak memory is 305 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 28 trigger nets, 28 data nets.
KIT-1004 : Chipwatcher code = 0010010110000101
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=28,BUS_CTRL_NUM=76,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01011,32'sb01100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100010,32'sb0101000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=98) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=98) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=28,BUS_CTRL_NUM=76,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01011,32'sb01100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100010,32'sb0101000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=28,BUS_CTRL_NUM=76,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01011,32'sb01100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100010,32'sb0101000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=28,BUS_CTRL_NUM=76,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01011,32'sb01100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100010,32'sb0101000})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=98)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=98)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=28,BUS_CTRL_NUM=76,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01011,32'sb01100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100010,32'sb0101000})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=28,BUS_CTRL_NUM=76,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01011,32'sb01100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100010,32'sb0101000})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22574/23 useful/useless nets, 19449/12 useful/useless insts
SYN-1016 : Merged 29 instances.
SYN-1032 : 22298/20 useful/useless nets, 19805/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 328 better
SYN-1014 : Optimize round 2
SYN-1032 : 22034/45 useful/useless nets, 19541/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.454802s wall, 2.406250s user + 0.046875s system = 2.453125s CPU (99.9%)

RUN-1004 : used memory is 327 MB, reserved memory is 294 MB, peak memory is 329 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22070/222 useful/useless nets, 19601/33 useful/useless insts
SYN-1016 : Merged 33 instances.
SYN-2571 : Optimize after map_dsp, round 1, 288 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 27 instances.
SYN-2501 : Optimize round 1, 55 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 15 instances.
SYN-1032 : 22455/5 useful/useless nets, 19986/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81553, tnet num: 22455, tinst num: 19985, tnode num: 114360, tedge num: 127519.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.276046s wall, 1.281250s user + 0.000000s system = 1.281250s CPU (100.4%)

RUN-1004 : used memory is 466 MB, reserved memory is 434 MB, peak memory is 466 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22455 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 194 (3.61), #lev = 7 (1.97)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 194 (3.61), #lev = 7 (1.97)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 467 instances into 194 LUTs, name keeping = 72%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 337 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 108 adder to BLE ...
SYN-4008 : Packed 108 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.678543s wall, 4.625000s user + 0.062500s system = 4.687500s CPU (100.2%)

RUN-1004 : used memory is 365 MB, reserved memory is 353 MB, peak memory is 573 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.482004s wall, 7.343750s user + 0.156250s system = 7.500000s CPU (100.2%)

RUN-1004 : used memory is 366 MB, reserved memory is 354 MB, peak memory is 573 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (215 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19275 instances
RUN-0007 : 5567 luts, 12129 seqs, 973 mslices, 515 lslices, 59 pads, 27 brams, 0 dsps
RUN-1001 : There are total 21752 nets
RUN-1001 : 16374 nets have 2 pins
RUN-1001 : 4193 nets have [3 - 5] pins
RUN-1001 : 819 nets have [6 - 10] pins
RUN-1001 : 240 nets have [11 - 20] pins
RUN-1001 : 105 nets have [21 - 99] pins
RUN-1001 : 21 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4783     
RUN-1001 :   No   |  No   |  Yes  |     690     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     387     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19273 instances, 5567 luts, 12129 seqs, 1488 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80046, tnet num: 21750, tinst num: 19273, tnode num: 112574, tedge num: 125888.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.263001s wall, 1.218750s user + 0.031250s system = 1.250000s CPU (99.0%)

RUN-1004 : used memory is 525 MB, reserved memory is 497 MB, peak memory is 573 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21750 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.202052s wall, 2.156250s user + 0.031250s system = 2.187500s CPU (99.3%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.57441e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19273.
PHY-3001 : Level 1 #clusters 2162.
PHY-3001 : End clustering;  0.157515s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (129.0%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 858659, overlap = 580.438
PHY-3002 : Step(2): len = 775734, overlap = 637.469
PHY-3002 : Step(3): len = 505805, overlap = 819.344
PHY-3002 : Step(4): len = 431701, overlap = 900.594
PHY-3002 : Step(5): len = 347594, overlap = 981.531
PHY-3002 : Step(6): len = 314505, overlap = 1033
PHY-3002 : Step(7): len = 270512, overlap = 1111.47
PHY-3002 : Step(8): len = 245328, overlap = 1182.25
PHY-3002 : Step(9): len = 217566, overlap = 1234.97
PHY-3002 : Step(10): len = 199899, overlap = 1281
PHY-3002 : Step(11): len = 174224, overlap = 1302.25
PHY-3002 : Step(12): len = 164638, overlap = 1327.22
PHY-3002 : Step(13): len = 148909, overlap = 1348.03
PHY-3002 : Step(14): len = 138525, overlap = 1373.09
PHY-3002 : Step(15): len = 129245, overlap = 1403.03
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.49071e-07
PHY-3002 : Step(16): len = 132147, overlap = 1376.38
PHY-3002 : Step(17): len = 165591, overlap = 1294.25
PHY-3002 : Step(18): len = 171201, overlap = 1235.22
PHY-3002 : Step(19): len = 173056, overlap = 1158.56
PHY-3002 : Step(20): len = 171874, overlap = 1121.41
PHY-3002 : Step(21): len = 166954, overlap = 1118.31
PHY-3002 : Step(22): len = 164072, overlap = 1127.28
PHY-3002 : Step(23): len = 159043, overlap = 1143.41
PHY-3002 : Step(24): len = 156642, overlap = 1128.69
PHY-3002 : Step(25): len = 154220, overlap = 1131.5
PHY-3002 : Step(26): len = 153462, overlap = 1156.12
PHY-3002 : Step(27): len = 152620, overlap = 1166.47
PHY-3002 : Step(28): len = 152455, overlap = 1165.47
PHY-3002 : Step(29): len = 153072, overlap = 1152.5
PHY-3002 : Step(30): len = 152692, overlap = 1156.72
PHY-3002 : Step(31): len = 152694, overlap = 1160.84
PHY-3002 : Step(32): len = 152206, overlap = 1162.69
PHY-3002 : Step(33): len = 151065, overlap = 1179.22
PHY-3002 : Step(34): len = 150650, overlap = 1179.5
PHY-3002 : Step(35): len = 150443, overlap = 1173.97
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 1.89814e-06
PHY-3002 : Step(36): len = 155050, overlap = 1141.5
PHY-3002 : Step(37): len = 167250, overlap = 1106.06
PHY-3002 : Step(38): len = 170299, overlap = 1084
PHY-3002 : Step(39): len = 173093, overlap = 1042.25
PHY-3002 : Step(40): len = 173395, overlap = 1065.25
PHY-3002 : Step(41): len = 173541, overlap = 1049.19
PHY-3002 : Step(42): len = 172945, overlap = 1043.47
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 3.79628e-06
PHY-3002 : Step(43): len = 182854, overlap = 992.031
PHY-3002 : Step(44): len = 200852, overlap = 894.938
PHY-3002 : Step(45): len = 205386, overlap = 861.688
PHY-3002 : Step(46): len = 205630, overlap = 848.188
PHY-3002 : Step(47): len = 203967, overlap = 833.312
PHY-3002 : Step(48): len = 202592, overlap = 820.812
PHY-3002 : Step(49): len = 200994, overlap = 818
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 7.59257e-06
PHY-3002 : Step(50): len = 213966, overlap = 777.062
PHY-3002 : Step(51): len = 230136, overlap = 715.375
PHY-3002 : Step(52): len = 236749, overlap = 670.875
PHY-3002 : Step(53): len = 240075, overlap = 655.344
PHY-3002 : Step(54): len = 238307, overlap = 656.844
PHY-3002 : Step(55): len = 235908, overlap = 667.031
PHY-3002 : Step(56): len = 233692, overlap = 689.406
PHY-3002 : Step(57): len = 232357, overlap = 678.594
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.51851e-05
PHY-3002 : Step(58): len = 244300, overlap = 645.875
PHY-3002 : Step(59): len = 260115, overlap = 551.75
PHY-3002 : Step(60): len = 264309, overlap = 500.469
PHY-3002 : Step(61): len = 266464, overlap = 476.5
PHY-3002 : Step(62): len = 264491, overlap = 476.75
PHY-3002 : Step(63): len = 263669, overlap = 459.875
PHY-3002 : Step(64): len = 260453, overlap = 460.312
PHY-3002 : Step(65): len = 258261, overlap = 450.594
PHY-3002 : Step(66): len = 256247, overlap = 453.031
PHY-3002 : Step(67): len = 256093, overlap = 454.312
PHY-3002 : Step(68): len = 255338, overlap = 458.25
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.03703e-05
PHY-3002 : Step(69): len = 266822, overlap = 420
PHY-3002 : Step(70): len = 279172, overlap = 366.031
PHY-3002 : Step(71): len = 280164, overlap = 339.594
PHY-3002 : Step(72): len = 281756, overlap = 333.781
PHY-3002 : Step(73): len = 280139, overlap = 338.625
PHY-3002 : Step(74): len = 279062, overlap = 346.688
PHY-3002 : Step(75): len = 277060, overlap = 357.969
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 6.07405e-05
PHY-3002 : Step(76): len = 284415, overlap = 333.938
PHY-3002 : Step(77): len = 294088, overlap = 325.781
PHY-3002 : Step(78): len = 297593, overlap = 293.75
PHY-3002 : Step(79): len = 298699, overlap = 286.656
PHY-3002 : Step(80): len = 297963, overlap = 301.094
PHY-3002 : Step(81): len = 297181, overlap = 304.344
PHY-3002 : Step(82): len = 293206, overlap = 315.062
PHY-3002 : Step(83): len = 293272, overlap = 324.125
PHY-3002 : Step(84): len = 292005, overlap = 320.969
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000121481
PHY-3002 : Step(85): len = 298868, overlap = 306.094
PHY-3002 : Step(86): len = 305250, overlap = 286.094
PHY-3002 : Step(87): len = 306831, overlap = 279.125
PHY-3002 : Step(88): len = 307955, overlap = 275.219
PHY-3002 : Step(89): len = 307301, overlap = 272.188
PHY-3002 : Step(90): len = 305841, overlap = 258.844
PHY-3002 : Step(91): len = 304215, overlap = 253.625
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000225065
PHY-3002 : Step(92): len = 307409, overlap = 250.594
PHY-3002 : Step(93): len = 311803, overlap = 242.531
PHY-3002 : Step(94): len = 312961, overlap = 230.688
PHY-3002 : Step(95): len = 313626, overlap = 214.312
PHY-3002 : Step(96): len = 312686, overlap = 215.281
PHY-3002 : Step(97): len = 312268, overlap = 207.5
PHY-3002 : Step(98): len = 311500, overlap = 196.062
PHY-3002 : Step(99): len = 313079, overlap = 199.969
PHY-3002 : Step(100): len = 312773, overlap = 208.656
PHY-3002 : Step(101): len = 312548, overlap = 211.875
PHY-3002 : Step(102): len = 311041, overlap = 218.531
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000379101
PHY-3002 : Step(103): len = 312397, overlap = 202.688
PHY-3002 : Step(104): len = 314387, overlap = 200.375
PHY-3002 : Step(105): len = 315183, overlap = 196.344
PHY-3001 : :::11::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(106): len = 316249, overlap = 199.562
PHY-3002 : Step(107): len = 318898, overlap = 199
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.014559s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (214.6%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21752.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 417560, over cnt = 1191(3%), over = 5292, worst = 57
PHY-1001 : End global iterations;  0.837156s wall, 1.171875s user + 0.062500s system = 1.234375s CPU (147.4%)

PHY-1001 : Congestion index: top1 = 75.39, top5 = 52.46, top10 = 42.82, top15 = 37.00.
PHY-3001 : End congestion estimation;  1.067998s wall, 1.375000s user + 0.093750s system = 1.468750s CPU (137.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21750 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.977012s wall, 0.937500s user + 0.031250s system = 0.968750s CPU (99.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.5931e-05
PHY-3002 : Step(108): len = 360339, overlap = 179.156
PHY-3002 : Step(109): len = 372703, overlap = 184.781
PHY-3002 : Step(110): len = 374462, overlap = 183.062
PHY-3002 : Step(111): len = 374172, overlap = 175.156
PHY-3002 : Step(112): len = 380175, overlap = 158.281
PHY-3002 : Step(113): len = 388544, overlap = 145
PHY-3002 : Step(114): len = 398904, overlap = 140.312
PHY-3002 : Step(115): len = 403924, overlap = 134.312
PHY-3002 : Step(116): len = 410206, overlap = 128.438
PHY-3002 : Step(117): len = 416986, overlap = 129.875
PHY-3002 : Step(118): len = 420794, overlap = 123.906
PHY-3002 : Step(119): len = 422932, overlap = 116.531
PHY-3002 : Step(120): len = 427175, overlap = 115.688
PHY-3002 : Step(121): len = 431352, overlap = 111.406
PHY-3002 : Step(122): len = 431846, overlap = 112.688
PHY-3002 : Step(123): len = 433987, overlap = 109.281
PHY-3002 : Step(124): len = 437379, overlap = 107.094
PHY-3002 : Step(125): len = 437569, overlap = 104.031
PHY-3002 : Step(126): len = 439690, overlap = 101.344
PHY-3002 : Step(127): len = 441664, overlap = 102.781
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000191862
PHY-3002 : Step(128): len = 441846, overlap = 99.4688
PHY-3002 : Step(129): len = 444388, overlap = 98.0312
PHY-3002 : Step(130): len = 446533, overlap = 101.625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(131): len = 452962, overlap = 101.406
PHY-3002 : Step(132): len = 464733, overlap = 103.125
PHY-3002 : Step(133): len = 467647, overlap = 93.7188
PHY-3002 : Step(134): len = 467067, overlap = 90.0625
PHY-3002 : Step(135): len = 468761, overlap = 83.1875
PHY-3002 : Step(136): len = 472262, overlap = 81.7188
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 57/21752.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 534448, over cnt = 2286(6%), over = 10386, worst = 37
PHY-1001 : End global iterations;  1.033725s wall, 1.609375s user + 0.000000s system = 1.609375s CPU (155.7%)

PHY-1001 : Congestion index: top1 = 74.91, top5 = 57.12, top10 = 49.67, top15 = 45.26.
PHY-3001 : End congestion estimation;  1.373269s wall, 1.953125s user + 0.000000s system = 1.953125s CPU (142.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21750 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.228261s wall, 1.234375s user + 0.015625s system = 1.250000s CPU (101.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000103046
PHY-3002 : Step(137): len = 477405, overlap = 355.812
PHY-3002 : Step(138): len = 481805, overlap = 294.594
PHY-3002 : Step(139): len = 472911, overlap = 266.969
PHY-3002 : Step(140): len = 468599, overlap = 255.969
PHY-3002 : Step(141): len = 467308, overlap = 231.219
PHY-3002 : Step(142): len = 463095, overlap = 220.906
PHY-3002 : Step(143): len = 459802, overlap = 212.562
PHY-3002 : Step(144): len = 457859, overlap = 215.438
PHY-3002 : Step(145): len = 454790, overlap = 214.5
PHY-3002 : Step(146): len = 452996, overlap = 225.469
PHY-3002 : Step(147): len = 451597, overlap = 225.781
PHY-3002 : Step(148): len = 449540, overlap = 219.844
PHY-3002 : Step(149): len = 449311, overlap = 219.031
PHY-3002 : Step(150): len = 446208, overlap = 229.188
PHY-3002 : Step(151): len = 444272, overlap = 228.625
PHY-3002 : Step(152): len = 442370, overlap = 233.344
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000206091
PHY-3002 : Step(153): len = 442686, overlap = 224.406
PHY-3002 : Step(154): len = 445413, overlap = 212.156
PHY-3002 : Step(155): len = 447767, overlap = 205.969
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000412183
PHY-3002 : Step(156): len = 450620, overlap = 195.062
PHY-3002 : Step(157): len = 458795, overlap = 176.875
PHY-3002 : Step(158): len = 465494, overlap = 165.719
PHY-3002 : Step(159): len = 467010, overlap = 167.312
PHY-3002 : Step(160): len = 467583, overlap = 163.25
PHY-3002 : Step(161): len = 468737, overlap = 158.375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000824365
PHY-3002 : Step(162): len = 469054, overlap = 157.125
PHY-3002 : Step(163): len = 472792, overlap = 145.531
PHY-3002 : Step(164): len = 475182, overlap = 140.688
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80046, tnet num: 21750, tinst num: 19273, tnode num: 112574, tedge num: 125888.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.551144s wall, 1.500000s user + 0.046875s system = 1.546875s CPU (99.7%)

RUN-1004 : used memory is 563 MB, reserved memory is 539 MB, peak memory is 696 MB
OPT-1001 : Total overflow 493.62 peak overflow 4.06
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 334/21752.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 548984, over cnt = 2519(7%), over = 8895, worst = 26
PHY-1001 : End global iterations;  1.219274s wall, 1.937500s user + 0.109375s system = 2.046875s CPU (167.9%)

PHY-1001 : Congestion index: top1 = 56.34, top5 = 46.91, top10 = 42.47, top15 = 39.83.
PHY-1001 : End incremental global routing;  1.480437s wall, 2.187500s user + 0.109375s system = 2.296875s CPU (155.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21750 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.058995s wall, 1.062500s user + 0.000000s system = 1.062500s CPU (100.3%)

OPT-1001 : 18 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19193 has valid locations, 228 needs to be replaced
PHY-3001 : design contains 19483 instances, 5659 luts, 12247 seqs, 1488 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 490947
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17193/21962.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 561904, over cnt = 2512(7%), over = 8992, worst = 26
PHY-1001 : End global iterations;  0.192919s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (129.6%)

PHY-1001 : Congestion index: top1 = 57.22, top5 = 47.55, top10 = 42.91, top15 = 40.26.
PHY-3001 : End congestion estimation;  0.449204s wall, 0.468750s user + 0.046875s system = 0.515625s CPU (114.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80721, tnet num: 21960, tinst num: 19483, tnode num: 113520, tedge num: 126818.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.576563s wall, 1.515625s user + 0.046875s system = 1.562500s CPU (99.1%)

RUN-1004 : used memory is 607 MB, reserved memory is 606 MB, peak memory is 698 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21960 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.673524s wall, 2.562500s user + 0.093750s system = 2.656250s CPU (99.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(165): len = 490643, overlap = 6.4375
PHY-3002 : Step(166): len = 491707, overlap = 6.375
PHY-3002 : Step(167): len = 492295, overlap = 6.1875
PHY-3002 : Step(168): len = 492731, overlap = 6.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(169): len = 492686, overlap = 6.1875
PHY-3002 : Step(170): len = 492789, overlap = 6.3125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(171): len = 492773, overlap = 6.125
PHY-3002 : Step(172): len = 493616, overlap = 5.9375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17202/21962.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 561952, over cnt = 2568(7%), over = 9061, worst = 26
PHY-1001 : End global iterations;  0.193793s wall, 0.203125s user + 0.046875s system = 0.250000s CPU (129.0%)

PHY-1001 : Congestion index: top1 = 56.96, top5 = 47.52, top10 = 42.98, top15 = 40.39.
PHY-3001 : End congestion estimation;  0.456845s wall, 0.468750s user + 0.046875s system = 0.515625s CPU (112.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21960 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.048728s wall, 1.031250s user + 0.015625s system = 1.046875s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000770582
PHY-3002 : Step(173): len = 493427, overlap = 143.062
PHY-3002 : Step(174): len = 493548, overlap = 142.719
PHY-3001 : Final: Len = 493548, Over = 142.719
PHY-3001 : End incremental placement;  5.650573s wall, 5.718750s user + 0.484375s system = 6.203125s CPU (109.8%)

OPT-1001 : Total overflow 498.22 peak overflow 4.06
OPT-1001 : End high-fanout net optimization;  8.741780s wall, 9.703125s user + 0.593750s system = 10.296875s CPU (117.8%)

OPT-1001 : Current memory(MB): used = 700, reserve = 679, peak = 716.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17253/21962.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 563264, over cnt = 2528(7%), over = 8645, worst = 26
PHY-1002 : len = 601184, over cnt = 1816(5%), over = 4936, worst = 20
PHY-1002 : len = 640056, over cnt = 891(2%), over = 2070, worst = 19
PHY-1002 : len = 672528, over cnt = 144(0%), over = 255, worst = 12
PHY-1002 : len = 676344, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.239326s wall, 1.953125s user + 0.000000s system = 1.953125s CPU (157.6%)

PHY-1001 : Congestion index: top1 = 49.96, top5 = 44.39, top10 = 41.22, top15 = 39.22.
OPT-1001 : End congestion update;  1.517225s wall, 2.234375s user + 0.000000s system = 2.234375s CPU (147.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 21960 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.931279s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (100.7%)

OPT-0007 : Start: WNS 4567 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.454499s wall, 3.171875s user + 0.000000s system = 3.171875s CPU (129.2%)

OPT-1001 : Current memory(MB): used = 674, reserve = 655, peak = 716.
OPT-1001 : End physical optimization;  13.079814s wall, 14.734375s user + 0.656250s system = 15.390625s CPU (117.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5659 LUT to BLE ...
SYN-4008 : Packed 5659 LUT and 2728 SEQ to BLE.
SYN-4003 : Packing 9519 remaining SEQ's ...
SYN-4005 : Packed 3316 SEQ with LUT/SLICE
SYN-4006 : 143 single LUT's are left
SYN-4006 : 6203 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11862/13705 primitive instances ...
PHY-3001 : End packing;  2.866228s wall, 2.875000s user + 0.000000s system = 2.875000s CPU (100.3%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8093 instances
RUN-1001 : 4001 mslices, 4001 lslices, 59 pads, 27 brams, 0 dsps
RUN-1001 : There are total 19287 nets
RUN-1001 : 13559 nets have 2 pins
RUN-1001 : 4321 nets have [3 - 5] pins
RUN-1001 : 883 nets have [6 - 10] pins
RUN-1001 : 381 nets have [11 - 20] pins
RUN-1001 : 134 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8091 instances, 8002 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 510222, Over = 376
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7817/19287.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 637864, over cnt = 1605(4%), over = 2576, worst = 7
PHY-1002 : len = 643768, over cnt = 1103(3%), over = 1527, worst = 7
PHY-1002 : len = 654928, over cnt = 435(1%), over = 591, worst = 6
PHY-1002 : len = 663304, over cnt = 106(0%), over = 122, worst = 4
PHY-1002 : len = 665032, over cnt = 30(0%), over = 33, worst = 2
PHY-1001 : End global iterations;  1.348256s wall, 2.078125s user + 0.078125s system = 2.156250s CPU (159.9%)

PHY-1001 : Congestion index: top1 = 50.22, top5 = 43.58, top10 = 40.43, top15 = 38.39.
PHY-3001 : End congestion estimation;  1.699590s wall, 2.421875s user + 0.078125s system = 2.500000s CPU (147.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66997, tnet num: 19285, tinst num: 8091, tnode num: 90907, tedge num: 110477.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.774982s wall, 1.765625s user + 0.015625s system = 1.781250s CPU (100.4%)

RUN-1004 : used memory is 598 MB, reserved memory is 589 MB, peak memory is 716 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19285 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.759661s wall, 2.750000s user + 0.015625s system = 2.765625s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.85264e-05
PHY-3002 : Step(175): len = 514229, overlap = 360.5
PHY-3002 : Step(176): len = 513700, overlap = 372
PHY-3002 : Step(177): len = 513466, overlap = 394
PHY-3002 : Step(178): len = 512721, overlap = 400.25
PHY-3002 : Step(179): len = 510590, overlap = 404.5
PHY-3002 : Step(180): len = 508171, overlap = 406.25
PHY-3002 : Step(181): len = 506718, overlap = 395.5
PHY-3002 : Step(182): len = 503882, overlap = 396
PHY-3002 : Step(183): len = 502151, overlap = 398
PHY-3002 : Step(184): len = 499915, overlap = 393.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.70528e-05
PHY-3002 : Step(185): len = 504394, overlap = 378.5
PHY-3002 : Step(186): len = 509453, overlap = 370
PHY-3002 : Step(187): len = 510440, overlap = 364
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000194106
PHY-3002 : Step(188): len = 519402, overlap = 352.5
PHY-3002 : Step(189): len = 532108, overlap = 336.25
PHY-3002 : Step(190): len = 529676, overlap = 341.75
PHY-3002 : Step(191): len = 527234, overlap = 336.75
PHY-3002 : Step(192): len = 526929, overlap = 342.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.729471s wall, 0.781250s user + 1.046875s system = 1.828125s CPU (250.6%)

PHY-3001 : Trial Legalized: Len = 638147
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 549/19287.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 720776, over cnt = 2355(6%), over = 3736, worst = 8
PHY-1002 : len = 732584, over cnt = 1531(4%), over = 2183, worst = 7
PHY-1002 : len = 749040, over cnt = 689(1%), over = 919, worst = 6
PHY-1002 : len = 761968, over cnt = 97(0%), over = 137, worst = 6
PHY-1002 : len = 764128, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.977442s wall, 3.140625s user + 0.031250s system = 3.171875s CPU (160.4%)

PHY-1001 : Congestion index: top1 = 50.19, top5 = 45.14, top10 = 42.57, top15 = 40.85.
PHY-3001 : End congestion estimation;  2.355824s wall, 3.531250s user + 0.031250s system = 3.562500s CPU (151.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19285 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.939218s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000200209
PHY-3002 : Step(193): len = 597159, overlap = 75.75
PHY-3002 : Step(194): len = 578601, overlap = 118.75
PHY-3002 : Step(195): len = 567404, overlap = 159
PHY-3002 : Step(196): len = 559944, overlap = 197.5
PHY-3002 : Step(197): len = 556781, overlap = 225.25
PHY-3002 : Step(198): len = 554650, overlap = 231.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000400418
PHY-3002 : Step(199): len = 558707, overlap = 228.75
PHY-3002 : Step(200): len = 564550, overlap = 228.25
PHY-3002 : Step(201): len = 565652, overlap = 229.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000726295
PHY-3002 : Step(202): len = 567383, overlap = 226
PHY-3002 : Step(203): len = 574212, overlap = 220.5
PHY-3002 : Step(204): len = 584695, overlap = 223
PHY-3002 : Step(205): len = 582518, overlap = 225.75
PHY-3002 : Step(206): len = 581545, overlap = 225
PHY-3002 : Step(207): len = 581775, overlap = 227.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.036014s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (130.2%)

PHY-3001 : Legalized: Len = 619071, Over = 0
PHY-3001 : Spreading special nets. 28 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.084185s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (92.8%)

PHY-3001 : 40 instances has been re-located, deltaX = 14, deltaY = 25, maxDist = 2.
PHY-3001 : Final: Len = 619535, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66997, tnet num: 19285, tinst num: 8091, tnode num: 90907, tedge num: 110477.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.052780s wall, 2.031250s user + 0.015625s system = 2.046875s CPU (99.7%)

RUN-1004 : used memory is 601 MB, reserved memory is 588 MB, peak memory is 716 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3378/19287.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 709480, over cnt = 2210(6%), over = 3454, worst = 7
PHY-1002 : len = 722832, over cnt = 1177(3%), over = 1558, worst = 5
PHY-1002 : len = 732120, over cnt = 607(1%), over = 829, worst = 5
PHY-1002 : len = 741560, over cnt = 163(0%), over = 211, worst = 5
PHY-1002 : len = 745848, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.782776s wall, 2.812500s user + 0.078125s system = 2.890625s CPU (162.1%)

PHY-1001 : Congestion index: top1 = 48.36, top5 = 43.73, top10 = 41.04, top15 = 39.33.
PHY-1001 : End incremental global routing;  2.116086s wall, 3.125000s user + 0.078125s system = 3.203125s CPU (151.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19285 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.003730s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (99.6%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8028 has valid locations, 12 needs to be replaced
PHY-3001 : design contains 8102 instances, 8013 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 621058
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17372/19297.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 747640, over cnt = 17(0%), over = 17, worst = 1
PHY-1002 : len = 747608, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 747688, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 747736, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.551912s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (99.1%)

PHY-1001 : Congestion index: top1 = 48.36, top5 = 43.73, top10 = 41.07, top15 = 39.35.
PHY-3001 : End congestion estimation;  0.873071s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (98.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67062, tnet num: 19295, tinst num: 8102, tnode num: 90983, tedge num: 110552.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.016285s wall, 1.984375s user + 0.031250s system = 2.015625s CPU (100.0%)

RUN-1004 : used memory is 639 MB, reserved memory is 616 MB, peak memory is 716 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19295 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.007657s wall, 2.984375s user + 0.031250s system = 3.015625s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(208): len = 621180, overlap = 0.25
PHY-3002 : Step(209): len = 621249, overlap = 0.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17369/19297.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 747088, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 747024, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 747040, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 747056, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 747120, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.720046s wall, 0.687500s user + 0.031250s system = 0.718750s CPU (99.8%)

PHY-1001 : Congestion index: top1 = 48.25, top5 = 43.72, top10 = 41.08, top15 = 39.35.
PHY-3001 : End congestion estimation;  1.031666s wall, 1.000000s user + 0.031250s system = 1.031250s CPU (100.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19295 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.346617s wall, 1.328125s user + 0.000000s system = 1.328125s CPU (98.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000570024
PHY-3002 : Step(210): len = 621236, overlap = 2
PHY-3002 : Step(211): len = 621235, overlap = 1
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006754s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (231.3%)

PHY-3001 : Legalized: Len = 621267, Over = 0
PHY-3001 : End spreading;  0.074279s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (84.1%)

PHY-3001 : Final: Len = 621267, Over = 0
PHY-3001 : End incremental placement;  6.905333s wall, 6.812500s user + 0.109375s system = 6.921875s CPU (100.2%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.541356s wall, 11.453125s user + 0.187500s system = 11.640625s CPU (110.4%)

OPT-1001 : Current memory(MB): used = 711, reserve = 695, peak = 716.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17369/19297.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 747256, over cnt = 10(0%), over = 13, worst = 2
PHY-1002 : len = 747192, over cnt = 11(0%), over = 12, worst = 2
PHY-1002 : len = 747264, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 747296, over cnt = 1(0%), over = 1, worst = 1
PHY-1001 : End global iterations;  0.540896s wall, 0.546875s user + 0.015625s system = 0.562500s CPU (104.0%)

PHY-1001 : Congestion index: top1 = 48.30, top5 = 43.75, top10 = 41.11, top15 = 39.36.
OPT-1001 : End congestion update;  0.859164s wall, 0.859375s user + 0.015625s system = 0.875000s CPU (101.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19295 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.797566s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.9%)

OPT-0007 : Start: WNS 4560 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.661667s wall, 1.671875s user + 0.015625s system = 1.687500s CPU (101.6%)

OPT-1001 : Current memory(MB): used = 711, reserve = 695, peak = 716.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19295 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.792195s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (98.6%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17383/19297.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 747296, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 747288, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.247520s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (101.0%)

PHY-1001 : Congestion index: top1 = 48.30, top5 = 43.75, top10 = 41.11, top15 = 39.36.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19295 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.798753s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.8%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4560 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 47.965517
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4560ps with logic level 4 
RUN-1001 :       #2 path slack 4568ps with logic level 5 
RUN-1001 :       #3 path slack 4585ps with logic level 5 
RUN-1001 :       #4 path slack 4605ps with logic level 5 
RUN-1001 :       #5 path slack 4622ps with logic level 5 
OPT-1001 : End physical optimization;  16.669193s wall, 17.578125s user + 0.234375s system = 17.812500s CPU (106.9%)

RUN-1003 : finish command "place" in  71.896811s wall, 135.656250s user + 7.718750s system = 143.375000s CPU (199.4%)

RUN-1004 : used memory is 629 MB, reserved memory is 607 MB, peak memory is 716 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.707069s wall, 2.937500s user + 0.031250s system = 2.968750s CPU (173.9%)

RUN-1004 : used memory is 630 MB, reserved memory is 608 MB, peak memory is 716 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8104 instances
RUN-1001 : 4012 mslices, 4001 lslices, 59 pads, 27 brams, 0 dsps
RUN-1001 : There are total 19297 nets
RUN-1001 : 13560 nets have 2 pins
RUN-1001 : 4322 nets have [3 - 5] pins
RUN-1001 : 887 nets have [6 - 10] pins
RUN-1001 : 384 nets have [11 - 20] pins
RUN-1001 : 135 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67062, tnet num: 19295, tinst num: 8102, tnode num: 90983, tedge num: 110552.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.737477s wall, 1.734375s user + 0.000000s system = 1.734375s CPU (99.8%)

RUN-1004 : used memory is 639 MB, reserved memory is 628 MB, peak memory is 716 MB
PHY-1001 : 4012 mslices, 4001 lslices, 59 pads, 27 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19295 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 690088, over cnt = 2330(6%), over = 3814, worst = 7
PHY-1002 : len = 707112, over cnt = 1309(3%), over = 1790, worst = 6
PHY-1002 : len = 720856, over cnt = 604(1%), over = 776, worst = 5
PHY-1002 : len = 732720, over cnt = 5(0%), over = 6, worst = 2
PHY-1002 : len = 732848, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.764487s wall, 3.156250s user + 0.062500s system = 3.218750s CPU (182.4%)

PHY-1001 : Congestion index: top1 = 47.72, top5 = 43.25, top10 = 40.60, top15 = 38.88.
PHY-1001 : End global routing;  2.128416s wall, 3.531250s user + 0.062500s system = 3.593750s CPU (168.8%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 697, reserve = 683, peak = 716.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 967, reserve = 952, peak = 967.
PHY-1001 : End build detailed router design. 4.629645s wall, 4.593750s user + 0.031250s system = 4.625000s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 188352, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.944057s wall, 0.890625s user + 0.062500s system = 0.953125s CPU (101.0%)

PHY-1001 : Current memory(MB): used = 1003, reserve = 988, peak = 1003.
PHY-1001 : End phase 1; 0.951292s wall, 0.890625s user + 0.062500s system = 0.953125s CPU (100.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.6765e+06, over cnt = 1281(0%), over = 1281, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 1019, reserve = 1004, peak = 1019.
PHY-1001 : End initial routed; 15.276843s wall, 45.656250s user + 0.437500s system = 46.093750s CPU (301.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18037(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.693   |   0.000   |   0   
RUN-1001 :   Hold   |   0.120   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.570965s wall, 3.562500s user + 0.000000s system = 3.562500s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 1030, reserve = 1016, peak = 1030.
PHY-1001 : End phase 2; 18.847982s wall, 49.218750s user + 0.437500s system = 49.656250s CPU (263.5%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.6765e+06, over cnt = 1281(0%), over = 1281, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.255082s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (98.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.66629e+06, over cnt = 471(0%), over = 471, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.700204s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (145.0%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.66479e+06, over cnt = 108(0%), over = 108, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.420758s wall, 0.625000s user + 0.000000s system = 0.625000s CPU (148.5%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.66558e+06, over cnt = 10(0%), over = 10, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.262533s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (119.0%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.66573e+06, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.188727s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (107.6%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.66581e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.184112s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (101.8%)

PHY-1001 : ===== DR Iter 6 =====
PHY-1022 : len = 1.66586e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 6; 0.192469s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (105.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18037(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.959   |   0.000   |   0   
RUN-1001 :   Hold   |   0.120   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.480209s wall, 3.484375s user + 0.000000s system = 3.484375s CPU (100.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 306 feed throughs used by 262 nets
PHY-1001 : End commit to database; 2.206842s wall, 2.156250s user + 0.031250s system = 2.187500s CPU (99.1%)

PHY-1001 : Current memory(MB): used = 1115, reserve = 1103, peak = 1115.
PHY-1001 : End phase 3; 8.415305s wall, 8.937500s user + 0.031250s system = 8.968750s CPU (106.6%)

PHY-1003 : Routed, final wirelength = 1.66586e+06
PHY-1001 : Current memory(MB): used = 1119, reserve = 1108, peak = 1119.
PHY-1001 : End export database. 0.062021s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.8%)

PHY-1001 : End detail routing;  33.327007s wall, 64.140625s user + 0.562500s system = 64.703125s CPU (194.1%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67062, tnet num: 19295, tinst num: 8102, tnode num: 90983, tedge num: 110552.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.748081s wall, 1.750000s user + 0.000000s system = 1.750000s CPU (100.1%)

RUN-1004 : used memory is 1051 MB, reserved memory is 1049 MB, peak memory is 1119 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  41.653724s wall, 73.843750s user + 0.640625s system = 74.484375s CPU (178.8%)

RUN-1004 : used memory is 1051 MB, reserved memory is 1049 MB, peak memory is 1119 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8799   out of  19600   44.89%
#reg                    12341   out of  19600   62.96%
#le                     14944
  #lut only              2603   out of  14944   17.42%
  #reg only              6145   out of  14944   41.12%
  #lut&reg               6196   out of  14944   41.46%
#dsp                        0   out of     29    0.00%
#bram                      27   out of     64   42.19%
  #bram9k                  25
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       59   out of    188   31.38%
  #ireg                     9
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6810
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          124
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         F2        LVCMOS33          N/A          PULLUP          NONE       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         D1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         B3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         C1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         A3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         A2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         B2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         B1        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F15        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDZ        INPUT        K15        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         P2        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         N4        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         M5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         N6        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         N5        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         P5        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M3        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         M4        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         H2        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         M1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         L1        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         N1        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         N3        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         M2        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT         T5        LVCMOS33           8            NONE           OREG       
    Fog_ENY        OUTPUT         T4        LVCMOS33           8            NONE           OREG       
    Fog_ENZ        OUTPUT         T7        LVCMOS33           8            NONE           OREG       
    Fog_INT        OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         T6        LVCMOS33           8            N/A            OREG       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E1        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        G11        LVCMOS25           8            N/A            NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |14944  |7311    |1488    |12384   |27      |0       |
|  AnyFog_dataX                      |AnyFog          |210    |86      |22      |163     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |92     |60      |22      |45      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |209    |75      |22      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |56      |22      |50      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |207    |99      |22      |172     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |88     |55      |22      |53      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2908   |679     |39      |2837    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |63     |37      |5       |55      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |202    |49      |5       |192     |0       |0       |
|    STADOP_com2                     |STADOP          |545    |98      |0       |544     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |41      |14      |43      |0       |0       |
|    head_com2                       |uniheading      |257    |56      |5       |248     |0       |0       |
|    rmc_com2                        |Gprmc           |31     |31      |0       |28      |0       |0       |
|    uart_com2                       |Agrica          |1431   |349     |10      |1413    |0       |0       |
|  COM3                              |COM3_Control    |276    |124     |19      |237     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |60     |35      |5       |51      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |60     |44      |14      |37      |0       |0       |
|    rmc_com3                        |Gprmc           |156    |45      |0       |149     |0       |0       |
|  DATA                              |Data_Processing |8818   |4520    |1122    |7026    |0       |0       |
|    DIV_Dtemp                       |Divider         |793    |312     |84      |670     |0       |0       |
|    DIV_Utemp                       |Divider         |645    |306     |84      |520     |0       |0       |
|    DIV_accX                        |Divider         |573    |313     |84      |448     |0       |0       |
|    DIV_accY                        |Divider         |615    |370     |102     |463     |0       |0       |
|    DIV_accZ                        |Divider         |657    |373     |132     |454     |0       |0       |
|    DIV_rateX                       |Divider         |656    |363     |132     |453     |0       |0       |
|    DIV_rateY                       |Divider         |614    |387     |132     |407     |0       |0       |
|    DIV_rateZ                       |Divider         |557    |347     |132     |354     |0       |0       |
|    genclk                          |genclk          |263    |158     |89      |102     |0       |0       |
|  FMC                               |FMC_Ctrl        |476    |425     |43      |349     |0       |0       |
|  IIC                               |I2C_master      |303    |256     |11      |267     |0       |0       |
|  IMU_CTRL                          |SCHA634         |932    |686     |61      |735     |0       |0       |
|    CtrlData                        |CtrlData        |491    |437     |47      |336     |0       |0       |
|      usms                          |Time_1ms        |29     |24      |5       |21      |0       |0       |
|    SPIM                            |SPI_SCHA634     |441    |249     |14      |399     |0       |0       |
|  POWER                             |POWER_EN        |97     |51      |38      |38      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |504    |310     |89      |340     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |504    |310     |89      |340     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |217    |121     |0       |204     |0       |0       |
|        reg_inst                    |register        |215    |119     |0       |202     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |287    |189     |89      |136     |0       |0       |
|        bus_inst                    |bus_top         |84     |56      |28      |35      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |30     |20      |10      |13      |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |51     |33      |18      |19      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |120    |88      |29      |72      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13500  
    #2          2       3397   
    #3          3        620   
    #4          4        305   
    #5        5-10       950   
    #6        11-50      449   
    #7       51-100       7    
    #8       101-500      3    
    #9        >500        2    
  Average     2.12             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.109630s wall, 3.625000s user + 0.000000s system = 3.625000s CPU (171.8%)

RUN-1004 : used memory is 1051 MB, reserved memory is 1050 MB, peak memory is 1119 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67062, tnet num: 19295, tinst num: 8102, tnode num: 90983, tedge num: 110552.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.824211s wall, 1.828125s user + 0.000000s system = 1.828125s CPU (100.2%)

RUN-1004 : used memory is 1053 MB, reserved memory is 1052 MB, peak memory is 1119 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19295 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.454767s wall, 1.421875s user + 0.015625s system = 1.437500s CPU (98.8%)

RUN-1004 : used memory is 1057 MB, reserved memory is 1055 MB, peak memory is 1119 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: f93483d4eefe0fc495c71cfd33cdfdfd7fe3ec43aef27c1d437b0386330ceaf7 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8102
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19297, pip num: 145159
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 306
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3209 valid insts, and 407295 bits set as '1'.
BIT-1004 : the usercode register value: 00000000000010010010010110000101
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.398242s wall, 124.640625s user + 0.093750s system = 124.734375s CPU (1006.1%)

RUN-1004 : used memory is 1178 MB, reserved memory is 1162 MB, peak memory is 1292 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250821_104159.log"

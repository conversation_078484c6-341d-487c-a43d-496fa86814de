============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Aug 21 15:19:19 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.975766s wall, 1.546875s user + 4.437500s system = 5.984375s CPU (100.1%)

RUN-1004 : used memory is 79 MB, reserved memory is 40 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.982908s wall, 1.890625s user + 0.078125s system = 1.968750s CPU (99.3%)

RUN-1004 : used memory is 303 MB, reserved memory is 270 MB, peak memory is 306 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 42 trigger nets, 42 data nets.
KIT-1004 : Chipwatcher code = 1000110011001111
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb011001,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0111110,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=126) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=126) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb011001,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0111110,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb011001,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0111110,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb011001,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0111110,32'sb01000100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=126)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=126)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb011001,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0111110,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb011001,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0111110,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22792/23 useful/useless nets, 19541/12 useful/useless insts
SYN-1016 : Merged 29 instances.
SYN-1032 : 22460/20 useful/useless nets, 19967/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 398 better
SYN-1014 : Optimize round 2
SYN-1032 : 22140/45 useful/useless nets, 19647/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.622017s wall, 2.484375s user + 0.156250s system = 2.640625s CPU (100.7%)

RUN-1004 : used memory is 330 MB, reserved memory is 295 MB, peak memory is 331 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22188/299 useful/useless nets, 19732/47 useful/useless insts
SYN-1016 : Merged 45 instances.
SYN-2571 : Optimize after map_dsp, round 1, 391 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 40 instances.
SYN-2501 : Optimize round 1, 82 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 22636/5 useful/useless nets, 20180/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82604, tnet num: 22636, tinst num: 20179, tnode num: 115683, tedge num: 129149.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.318388s wall, 1.296875s user + 0.015625s system = 1.312500s CPU (99.6%)

RUN-1004 : used memory is 470 MB, reserved memory is 437 MB, peak memory is 470 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22636 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 237 (3.41), #lev = 7 (1.79)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 237 (3.41), #lev = 7 (1.79)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 534 instances into 237 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 407 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 128 adder to BLE ...
SYN-4008 : Packed 128 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.976074s wall, 4.843750s user + 0.140625s system = 4.984375s CPU (100.2%)

RUN-1004 : used memory is 354 MB, reserved memory is 317 MB, peak memory is 578 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.958108s wall, 7.671875s user + 0.312500s system = 7.984375s CPU (100.3%)

RUN-1004 : used memory is 354 MB, reserved memory is 317 MB, peak memory is 578 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (271 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19423 instances
RUN-0007 : 5617 luts, 12199 seqs, 983 mslices, 519 lslices, 60 pads, 40 brams, 0 dsps
RUN-1001 : There are total 21887 nets
RUN-1001 : 16428 nets have 2 pins
RUN-1001 : 4262 nets have [3 - 5] pins
RUN-1001 : 820 nets have [6 - 10] pins
RUN-1001 : 251 nets have [11 - 20] pins
RUN-1001 : 104 nets have [21 - 99] pins
RUN-1001 : 22 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4783     
RUN-1001 :   No   |  No   |  Yes  |     704     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     443     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19421 instances, 5617 luts, 12199 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80989, tnet num: 21885, tinst num: 19421, tnode num: 113859, tedge num: 127481.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.252105s wall, 1.203125s user + 0.046875s system = 1.250000s CPU (99.8%)

RUN-1004 : used memory is 530 MB, reserved memory is 501 MB, peak memory is 578 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21885 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.183296s wall, 2.125000s user + 0.062500s system = 2.187500s CPU (100.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.68087e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19421.
PHY-3001 : Level 1 #clusters 2187.
PHY-3001 : End clustering;  0.172847s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (189.8%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 888060, overlap = 637.469
PHY-3002 : Step(2): len = 815298, overlap = 677.625
PHY-3002 : Step(3): len = 525671, overlap = 844.594
PHY-3002 : Step(4): len = 455257, overlap = 923.844
PHY-3002 : Step(5): len = 368863, overlap = 1025.03
PHY-3002 : Step(6): len = 331197, overlap = 1088.09
PHY-3002 : Step(7): len = 281541, overlap = 1161.06
PHY-3002 : Step(8): len = 243552, overlap = 1237.38
PHY-3002 : Step(9): len = 220152, overlap = 1310.41
PHY-3002 : Step(10): len = 197345, overlap = 1356.41
PHY-3002 : Step(11): len = 177319, overlap = 1392.72
PHY-3002 : Step(12): len = 163171, overlap = 1409.66
PHY-3002 : Step(13): len = 147439, overlap = 1412.84
PHY-3002 : Step(14): len = 135229, overlap = 1434.66
PHY-3002 : Step(15): len = 128619, overlap = 1453.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.2098e-06
PHY-3002 : Step(16): len = 138246, overlap = 1417.47
PHY-3002 : Step(17): len = 186929, overlap = 1289.69
PHY-3002 : Step(18): len = 193312, overlap = 1173.84
PHY-3002 : Step(19): len = 193155, overlap = 1128.41
PHY-3002 : Step(20): len = 191639, overlap = 1095.16
PHY-3002 : Step(21): len = 188595, overlap = 1054.34
PHY-3002 : Step(22): len = 182805, overlap = 1029.69
PHY-3002 : Step(23): len = 178865, overlap = 1040.91
PHY-3002 : Step(24): len = 173192, overlap = 1049.38
PHY-3002 : Step(25): len = 170212, overlap = 1041.69
PHY-3002 : Step(26): len = 165899, overlap = 1066.97
PHY-3002 : Step(27): len = 164590, overlap = 1051.69
PHY-3002 : Step(28): len = 162824, overlap = 1042.09
PHY-3002 : Step(29): len = 162048, overlap = 1034.62
PHY-3002 : Step(30): len = 161264, overlap = 1042.91
PHY-3002 : Step(31): len = 161030, overlap = 1073.78
PHY-3002 : Step(32): len = 160876, overlap = 1057.22
PHY-3002 : Step(33): len = 160089, overlap = 1060
PHY-3002 : Step(34): len = 159620, overlap = 1055.5
PHY-3002 : Step(35): len = 159096, overlap = 1053.22
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.4196e-06
PHY-3002 : Step(36): len = 164851, overlap = 1032.94
PHY-3002 : Step(37): len = 178768, overlap = 980.219
PHY-3002 : Step(38): len = 182073, overlap = 950.375
PHY-3002 : Step(39): len = 184401, overlap = 933.438
PHY-3002 : Step(40): len = 183780, overlap = 918.156
PHY-3002 : Step(41): len = 182833, overlap = 887.562
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.8392e-06
PHY-3002 : Step(42): len = 194377, overlap = 868.469
PHY-3002 : Step(43): len = 209538, overlap = 784.688
PHY-3002 : Step(44): len = 214191, overlap = 764.375
PHY-3002 : Step(45): len = 216017, overlap = 743.031
PHY-3002 : Step(46): len = 214497, overlap = 720.062
PHY-3002 : Step(47): len = 213164, overlap = 678.906
PHY-3002 : Step(48): len = 211958, overlap = 668.406
PHY-3002 : Step(49): len = 210912, overlap = 670.188
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 9.6784e-06
PHY-3002 : Step(50): len = 221270, overlap = 645.75
PHY-3002 : Step(51): len = 233487, overlap = 590.906
PHY-3002 : Step(52): len = 238677, overlap = 562.875
PHY-3002 : Step(53): len = 242382, overlap = 565.844
PHY-3002 : Step(54): len = 242696, overlap = 558.188
PHY-3002 : Step(55): len = 242218, overlap = 558.406
PHY-3002 : Step(56): len = 239926, overlap = 553.188
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.93568e-05
PHY-3002 : Step(57): len = 251929, overlap = 539.406
PHY-3002 : Step(58): len = 262558, overlap = 546.469
PHY-3002 : Step(59): len = 266721, overlap = 520.969
PHY-3002 : Step(60): len = 269582, overlap = 506.344
PHY-3002 : Step(61): len = 268072, overlap = 483.5
PHY-3002 : Step(62): len = 267217, overlap = 476.281
PHY-3002 : Step(63): len = 265202, overlap = 486.469
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.87136e-05
PHY-3002 : Step(64): len = 276550, overlap = 445.781
PHY-3002 : Step(65): len = 286945, overlap = 410.875
PHY-3002 : Step(66): len = 290549, overlap = 406.594
PHY-3002 : Step(67): len = 292581, overlap = 424.469
PHY-3002 : Step(68): len = 291899, overlap = 411.625
PHY-3002 : Step(69): len = 290176, overlap = 406.5
PHY-3002 : Step(70): len = 288752, overlap = 390.781
PHY-3002 : Step(71): len = 287406, overlap = 388.5
PHY-3002 : Step(72): len = 286178, overlap = 405.312
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 7.74272e-05
PHY-3002 : Step(73): len = 293661, overlap = 387.125
PHY-3002 : Step(74): len = 300413, overlap = 372.25
PHY-3002 : Step(75): len = 303361, overlap = 364.188
PHY-3002 : Step(76): len = 304268, overlap = 362.594
PHY-3002 : Step(77): len = 302583, overlap = 353.906
PHY-3002 : Step(78): len = 301873, overlap = 348.469
PHY-3002 : Step(79): len = 300213, overlap = 342.625
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000148988
PHY-3002 : Step(80): len = 304614, overlap = 331.531
PHY-3002 : Step(81): len = 310469, overlap = 338.625
PHY-3002 : Step(82): len = 313203, overlap = 328.625
PHY-3002 : Step(83): len = 315142, overlap = 321.5
PHY-3002 : Step(84): len = 315490, overlap = 324.656
PHY-3002 : Step(85): len = 315298, overlap = 324.375
PHY-3002 : Step(86): len = 314987, overlap = 318.25
PHY-3002 : Step(87): len = 315207, overlap = 298.719
PHY-3002 : Step(88): len = 314454, overlap = 294.656
PHY-3002 : Step(89): len = 314450, overlap = 276.094
PHY-3002 : Step(90): len = 314670, overlap = 257.531
PHY-3002 : Step(91): len = 314192, overlap = 258.031
PHY-3002 : Step(92): len = 313439, overlap = 281.625
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000297975
PHY-3002 : Step(93): len = 316285, overlap = 268.438
PHY-3002 : Step(94): len = 320575, overlap = 252.75
PHY-3002 : Step(95): len = 321792, overlap = 247.344
PHY-3002 : Step(96): len = 322409, overlap = 240.531
PHY-3002 : Step(97): len = 322349, overlap = 245.156
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000493632
PHY-3002 : Step(98): len = 323658, overlap = 244.406
PHY-3002 : Step(99): len = 327472, overlap = 250.656
PHY-3002 : Step(100): len = 328699, overlap = 248.781
PHY-3002 : Step(101): len = 329651, overlap = 250.812
PHY-3002 : Step(102): len = 330703, overlap = 262
PHY-3002 : Step(103): len = 330634, overlap = 274
PHY-3002 : Step(104): len = 330884, overlap = 264.625
PHY-3002 : Step(105): len = 330095, overlap = 272.344
PHY-3002 : Step(106): len = 328996, overlap = 272.375
PHY-3002 : Step(107): len = 328179, overlap = 265.656
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.012982s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (120.4%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21887.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 434592, over cnt = 1200(3%), over = 5250, worst = 38
PHY-1001 : End global iterations;  0.892159s wall, 1.140625s user + 0.046875s system = 1.187500s CPU (133.1%)

PHY-1001 : Congestion index: top1 = 71.40, top5 = 50.83, top10 = 41.69, top15 = 36.64.
PHY-3001 : End congestion estimation;  1.136322s wall, 1.375000s user + 0.046875s system = 1.421875s CPU (125.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21885 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.019570s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (101.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000124621
PHY-3002 : Step(108): len = 371769, overlap = 190.875
PHY-3002 : Step(109): len = 390558, overlap = 180.812
PHY-3002 : Step(110): len = 392294, overlap = 160.438
PHY-3002 : Step(111): len = 388044, overlap = 146.75
PHY-3002 : Step(112): len = 390276, overlap = 141.031
PHY-3002 : Step(113): len = 398307, overlap = 126.375
PHY-3002 : Step(114): len = 406014, overlap = 122.5
PHY-3002 : Step(115): len = 407868, overlap = 118.844
PHY-3002 : Step(116): len = 408867, overlap = 117.688
PHY-3002 : Step(117): len = 411488, overlap = 116.562
PHY-3002 : Step(118): len = 411374, overlap = 110.25
PHY-3002 : Step(119): len = 412469, overlap = 111.469
PHY-3002 : Step(120): len = 413136, overlap = 107.5
PHY-3002 : Step(121): len = 414303, overlap = 106.875
PHY-3002 : Step(122): len = 416050, overlap = 107.594
PHY-3002 : Step(123): len = 417695, overlap = 106.219
PHY-3002 : Step(124): len = 420394, overlap = 105.719
PHY-3002 : Step(125): len = 422377, overlap = 112.125
PHY-3002 : Step(126): len = 425297, overlap = 114.719
PHY-3002 : Step(127): len = 425133, overlap = 112.594
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000249242
PHY-3002 : Step(128): len = 425627, overlap = 113.062
PHY-3002 : Step(129): len = 427714, overlap = 113.375
PHY-3002 : Step(130): len = 429085, overlap = 113.344
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(131): len = 432528, overlap = 115.406
PHY-3002 : Step(132): len = 438945, overlap = 114.438
PHY-3002 : Step(133): len = 441525, overlap = 114.969
PHY-3002 : Step(134): len = 443813, overlap = 111.969
PHY-3002 : Step(135): len = 443975, overlap = 110.969
PHY-3002 : Step(136): len = 444513, overlap = 104.531
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 49/21887.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 507256, over cnt = 2168(6%), over = 9668, worst = 46
PHY-1001 : End global iterations;  1.158021s wall, 1.937500s user + 0.078125s system = 2.015625s CPU (174.1%)

PHY-1001 : Congestion index: top1 = 76.64, top5 = 58.70, top10 = 49.75, top15 = 44.42.
PHY-3001 : End congestion estimation;  1.450955s wall, 2.218750s user + 0.078125s system = 2.296875s CPU (158.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21885 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.070153s wall, 1.062500s user + 0.015625s system = 1.078125s CPU (100.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.76128e-05
PHY-3002 : Step(137): len = 451588, overlap = 357.406
PHY-3002 : Step(138): len = 456829, overlap = 293.438
PHY-3002 : Step(139): len = 453859, overlap = 278.281
PHY-3002 : Step(140): len = 450187, overlap = 264.781
PHY-3002 : Step(141): len = 447226, overlap = 248
PHY-3002 : Step(142): len = 445614, overlap = 238.688
PHY-3002 : Step(143): len = 444449, overlap = 234.031
PHY-3002 : Step(144): len = 443756, overlap = 214.906
PHY-3002 : Step(145): len = 442184, overlap = 211.656
PHY-3002 : Step(146): len = 441776, overlap = 200.781
PHY-3002 : Step(147): len = 440098, overlap = 196.969
PHY-3002 : Step(148): len = 438383, overlap = 191.344
PHY-3002 : Step(149): len = 437978, overlap = 191
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000195226
PHY-3002 : Step(150): len = 438215, overlap = 183.844
PHY-3002 : Step(151): len = 441113, overlap = 178.281
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000384901
PHY-3002 : Step(152): len = 445276, overlap = 167.938
PHY-3002 : Step(153): len = 451946, overlap = 161.062
PHY-3002 : Step(154): len = 456612, overlap = 150.438
PHY-3002 : Step(155): len = 458199, overlap = 143.031
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000769802
PHY-3002 : Step(156): len = 459032, overlap = 143.688
PHY-3002 : Step(157): len = 461251, overlap = 138.156
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80989, tnet num: 21885, tinst num: 19421, tnode num: 113859, tedge num: 127481.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.622347s wall, 1.593750s user + 0.031250s system = 1.625000s CPU (100.2%)

RUN-1004 : used memory is 570 MB, reserved memory is 543 MB, peak memory is 703 MB
OPT-1001 : Total overflow 493.97 peak overflow 3.81
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 563/21887.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 540888, over cnt = 2515(7%), over = 8604, worst = 25
PHY-1001 : End global iterations;  1.222441s wall, 1.968750s user + 0.031250s system = 2.000000s CPU (163.6%)

PHY-1001 : Congestion index: top1 = 57.67, top5 = 47.33, top10 = 42.44, top15 = 39.45.
PHY-1001 : End incremental global routing;  1.509117s wall, 2.250000s user + 0.031250s system = 2.281250s CPU (151.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21885 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.069586s wall, 1.046875s user + 0.015625s system = 1.062500s CPU (99.3%)

OPT-1001 : 17 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19341 has valid locations, 245 needs to be replaced
PHY-3001 : design contains 19649 instances, 5710 luts, 12334 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 475856
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17001/22115.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 550504, over cnt = 2523(7%), over = 8637, worst = 25
PHY-1001 : End global iterations;  0.191951s wall, 0.265625s user + 0.031250s system = 0.296875s CPU (154.7%)

PHY-1001 : Congestion index: top1 = 58.10, top5 = 47.91, top10 = 42.87, top15 = 39.85.
PHY-3001 : End congestion estimation;  0.576134s wall, 0.656250s user + 0.031250s system = 0.687500s CPU (119.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81749, tnet num: 22113, tinst num: 19649, tnode num: 114939, tedge num: 128545.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.620064s wall, 1.609375s user + 0.015625s system = 1.625000s CPU (100.3%)

RUN-1004 : used memory is 613 MB, reserved memory is 603 MB, peak memory is 703 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22113 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.743155s wall, 2.703125s user + 0.046875s system = 2.750000s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(158): len = 476139, overlap = 3.4375
PHY-3002 : Step(159): len = 477691, overlap = 3.625
PHY-3002 : Step(160): len = 479142, overlap = 3.375
PHY-3002 : Step(161): len = 480296, overlap = 3.5625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17030/22115.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 552232, over cnt = 2552(7%), over = 8761, worst = 25
PHY-1001 : End global iterations;  0.193146s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (105.2%)

PHY-1001 : Congestion index: top1 = 58.79, top5 = 48.15, top10 = 43.22, top15 = 40.19.
PHY-3001 : End congestion estimation;  0.459872s wall, 0.484375s user + 0.000000s system = 0.484375s CPU (105.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22113 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.095541s wall, 1.078125s user + 0.015625s system = 1.093750s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000669726
PHY-3002 : Step(162): len = 479683, overlap = 140.844
PHY-3002 : Step(163): len = 479550, overlap = 141.219
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00133945
PHY-3002 : Step(164): len = 480057, overlap = 140.719
PHY-3002 : Step(165): len = 480639, overlap = 140.156
PHY-3001 : Final: Len = 480639, Over = 140.156
PHY-3001 : End incremental placement;  5.849323s wall, 6.015625s user + 0.187500s system = 6.203125s CPU (106.0%)

OPT-1001 : Total overflow 498.88 peak overflow 3.81
OPT-1001 : End high-fanout net optimization;  9.006555s wall, 10.015625s user + 0.234375s system = 10.250000s CPU (113.8%)

OPT-1001 : Current memory(MB): used = 706, reserve = 685, peak = 723.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17015/22115.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 553904, over cnt = 2517(7%), over = 8176, worst = 25
PHY-1002 : len = 585080, over cnt = 1803(5%), over = 4907, worst = 25
PHY-1002 : len = 628136, over cnt = 785(2%), over = 1795, worst = 20
PHY-1002 : len = 642040, over cnt = 428(1%), over = 927, worst = 20
PHY-1002 : len = 646720, over cnt = 287(0%), over = 645, worst = 12
PHY-1001 : End global iterations;  1.325538s wall, 1.984375s user + 0.000000s system = 1.984375s CPU (149.7%)

PHY-1001 : Congestion index: top1 = 50.17, top5 = 43.75, top10 = 40.41, top15 = 38.31.
OPT-1001 : End congestion update;  1.587286s wall, 2.250000s user + 0.000000s system = 2.250000s CPU (141.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22113 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.944621s wall, 0.921875s user + 0.015625s system = 0.937500s CPU (99.2%)

OPT-0007 : Start: WNS 3769 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.539582s wall, 3.187500s user + 0.015625s system = 3.203125s CPU (126.1%)

OPT-1001 : Current memory(MB): used = 681, reserve = 660, peak = 723.
OPT-1001 : End physical optimization;  13.500248s wall, 15.265625s user + 0.312500s system = 15.578125s CPU (115.4%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5710 LUT to BLE ...
SYN-4008 : Packed 5710 LUT and 2728 SEQ to BLE.
SYN-4003 : Packing 9606 remaining SEQ's ...
SYN-4005 : Packed 3415 SEQ with LUT/SLICE
SYN-4006 : 109 single LUT's are left
SYN-4006 : 6191 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11901/13772 primitive instances ...
PHY-3001 : End packing;  3.418723s wall, 3.421875s user + 0.015625s system = 3.437500s CPU (100.5%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8138 instances
RUN-1001 : 4016 mslices, 4017 lslices, 60 pads, 40 brams, 0 dsps
RUN-1001 : There are total 19438 nets
RUN-1001 : 13617 nets have 2 pins
RUN-1001 : 4410 nets have [3 - 5] pins
RUN-1001 : 882 nets have [6 - 10] pins
RUN-1001 : 392 nets have [11 - 20] pins
RUN-1001 : 128 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8136 instances, 8033 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 496279, Over = 365.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7765/19438.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 618128, over cnt = 1676(4%), over = 2788, worst = 9
PHY-1002 : len = 625688, over cnt = 1096(3%), over = 1607, worst = 7
PHY-1002 : len = 638616, over cnt = 418(1%), over = 595, worst = 7
PHY-1002 : len = 645352, over cnt = 142(0%), over = 202, worst = 6
PHY-1002 : len = 649128, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.386536s wall, 2.156250s user + 0.000000s system = 2.156250s CPU (155.5%)

PHY-1001 : Congestion index: top1 = 50.34, top5 = 43.83, top10 = 40.35, top15 = 38.10.
PHY-3001 : End congestion estimation;  1.764106s wall, 2.531250s user + 0.000000s system = 2.531250s CPU (143.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67838, tnet num: 19436, tinst num: 8136, tnode num: 91998, tedge num: 111890.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.090671s wall, 2.046875s user + 0.046875s system = 2.093750s CPU (100.1%)

RUN-1004 : used memory is 605 MB, reserved memory is 596 MB, peak memory is 723 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19436 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.149899s wall, 3.062500s user + 0.078125s system = 3.140625s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.70487e-05
PHY-3002 : Step(166): len = 497714, overlap = 346.25
PHY-3002 : Step(167): len = 496140, overlap = 352.25
PHY-3002 : Step(168): len = 494767, overlap = 348
PHY-3002 : Step(169): len = 493801, overlap = 359.75
PHY-3002 : Step(170): len = 491487, overlap = 358.5
PHY-3002 : Step(171): len = 491056, overlap = 374
PHY-3002 : Step(172): len = 489327, overlap = 377.75
PHY-3002 : Step(173): len = 487801, overlap = 391.25
PHY-3002 : Step(174): len = 485703, overlap = 399.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.40974e-05
PHY-3002 : Step(175): len = 489974, overlap = 392.25
PHY-3002 : Step(176): len = 494498, overlap = 383.25
PHY-3002 : Step(177): len = 494623, overlap = 384.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000188195
PHY-3002 : Step(178): len = 503599, overlap = 373
PHY-3002 : Step(179): len = 513121, overlap = 361
PHY-3002 : Step(180): len = 510709, overlap = 359.25
PHY-3002 : Step(181): len = 508450, overlap = 356.25
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(182): len = 513472, overlap = 346.75
PHY-3002 : Step(183): len = 518742, overlap = 336.5
PHY-3002 : Step(184): len = 524148, overlap = 325.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.902057s wall, 1.046875s user + 1.078125s system = 2.125000s CPU (235.6%)

PHY-3001 : Trial Legalized: Len = 622605
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 592/19438.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 709928, over cnt = 2336(6%), over = 3795, worst = 8
PHY-1002 : len = 724600, over cnt = 1413(4%), over = 1971, worst = 8
PHY-1002 : len = 744144, over cnt = 419(1%), over = 580, worst = 5
PHY-1002 : len = 751664, over cnt = 73(0%), over = 86, worst = 3
PHY-1002 : len = 753456, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.082276s wall, 3.312500s user + 0.062500s system = 3.375000s CPU (162.1%)

PHY-1001 : Congestion index: top1 = 48.75, top5 = 44.34, top10 = 41.88, top15 = 40.19.
PHY-3001 : End congestion estimation;  2.468556s wall, 3.687500s user + 0.062500s system = 3.750000s CPU (151.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19436 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.998527s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00019926
PHY-3002 : Step(185): len = 579477, overlap = 79.5
PHY-3002 : Step(186): len = 559906, overlap = 124.5
PHY-3002 : Step(187): len = 547252, overlap = 177.5
PHY-3002 : Step(188): len = 540721, overlap = 209
PHY-3002 : Step(189): len = 537450, overlap = 229.25
PHY-3002 : Step(190): len = 535234, overlap = 238.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000398521
PHY-3002 : Step(191): len = 540158, overlap = 232.75
PHY-3002 : Step(192): len = 544587, overlap = 230.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000797042
PHY-3002 : Step(193): len = 544984, overlap = 225.5
PHY-3002 : Step(194): len = 554285, overlap = 220.5
PHY-3002 : Step(195): len = 558702, overlap = 223
PHY-3002 : Step(196): len = 558563, overlap = 219.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.032953s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (142.2%)

PHY-3001 : Legalized: Len = 598541, Over = 0
PHY-3001 : Spreading special nets. 35 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.084937s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (92.0%)

PHY-3001 : 50 instances has been re-located, deltaX = 22, deltaY = 30, maxDist = 2.
PHY-3001 : Final: Len = 599611, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67838, tnet num: 19436, tinst num: 8136, tnode num: 91998, tedge num: 111890.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.302152s wall, 2.312500s user + 0.000000s system = 2.312500s CPU (100.4%)

RUN-1004 : used memory is 606 MB, reserved memory is 596 MB, peak memory is 723 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3937/19438.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 695272, over cnt = 2188(6%), over = 3394, worst = 7
PHY-1002 : len = 706016, over cnt = 1297(3%), over = 1804, worst = 5
PHY-1002 : len = 722024, over cnt = 430(1%), over = 579, worst = 5
PHY-1002 : len = 729528, over cnt = 82(0%), over = 109, worst = 5
PHY-1002 : len = 731688, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.948786s wall, 3.093750s user + 0.046875s system = 3.140625s CPU (161.2%)

PHY-1001 : Congestion index: top1 = 48.58, top5 = 43.65, top10 = 40.80, top15 = 39.02.
PHY-1001 : End incremental global routing;  2.282890s wall, 3.421875s user + 0.046875s system = 3.468750s CPU (151.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19436 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.982629s wall, 0.968750s user + 0.015625s system = 0.984375s CPU (100.2%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8072 has valid locations, 7 needs to be replaced
PHY-3001 : design contains 8142 instances, 8039 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 602715
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17426/19443.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 735248, over cnt = 17(0%), over = 22, worst = 3
PHY-1002 : len = 735288, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 735432, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 735440, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 735448, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.695114s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (98.9%)

PHY-1001 : Congestion index: top1 = 48.58, top5 = 43.73, top10 = 40.86, top15 = 39.09.
PHY-3001 : End congestion estimation;  1.018059s wall, 1.000000s user + 0.015625s system = 1.015625s CPU (99.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67879, tnet num: 19441, tinst num: 8142, tnode num: 92045, tedge num: 111936.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.329441s wall, 2.312500s user + 0.015625s system = 2.328125s CPU (99.9%)

RUN-1004 : used memory is 643 MB, reserved memory is 627 MB, peak memory is 723 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19441 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.381963s wall, 3.359375s user + 0.031250s system = 3.390625s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(197): len = 602377, overlap = 0
PHY-3002 : Step(198): len = 602116, overlap = 0
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17422/19443.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 733944, over cnt = 19(0%), over = 26, worst = 4
PHY-1002 : len = 733960, over cnt = 12(0%), over = 12, worst = 1
PHY-1002 : len = 733984, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 734072, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.610749s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (112.6%)

PHY-1001 : Congestion index: top1 = 48.58, top5 = 43.67, top10 = 40.90, top15 = 39.14.
PHY-3001 : End congestion estimation;  0.987813s wall, 1.062500s user + 0.000000s system = 1.062500s CPU (107.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19441 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.060816s wall, 1.062500s user + 0.000000s system = 1.062500s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000685329
PHY-3002 : Step(199): len = 601936, overlap = 1
PHY-3002 : Step(200): len = 601969, overlap = 0.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008226s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 602022, Over = 0
PHY-3001 : End spreading;  0.083128s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (94.0%)

PHY-3001 : Final: Len = 602022, Over = 0
PHY-3001 : End incremental placement;  7.105923s wall, 7.234375s user + 0.125000s system = 7.359375s CPU (103.6%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.913205s wall, 12.171875s user + 0.187500s system = 12.359375s CPU (113.3%)

OPT-1001 : Current memory(MB): used = 716, reserve = 701, peak = 723.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17422/19443.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 733744, over cnt = 18(0%), over = 27, worst = 4
PHY-1002 : len = 733768, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 733808, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 733840, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.600166s wall, 0.593750s user + 0.015625s system = 0.609375s CPU (101.5%)

PHY-1001 : Congestion index: top1 = 48.69, top5 = 43.65, top10 = 40.86, top15 = 39.09.
OPT-1001 : End congestion update;  0.967181s wall, 0.953125s user + 0.015625s system = 0.968750s CPU (100.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19441 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.813731s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (99.8%)

OPT-0007 : Start: WNS 3640 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.786221s wall, 1.765625s user + 0.015625s system = 1.781250s CPU (99.7%)

OPT-1001 : Current memory(MB): used = 716, reserve = 701, peak = 723.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19441 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.822579s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (100.7%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17432/19443.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 733840, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.127224s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (98.3%)

PHY-1001 : Congestion index: top1 = 48.69, top5 = 43.65, top10 = 40.86, top15 = 39.09.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19441 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.846885s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (99.6%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3640 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 48.241379
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3640ps with logic level 4 
OPT-1001 : End physical optimization;  17.397592s wall, 18.625000s user + 0.218750s system = 18.843750s CPU (108.3%)

RUN-1003 : finish command "place" in  70.698853s wall, 119.187500s user + 7.406250s system = 126.593750s CPU (179.1%)

RUN-1004 : used memory is 599 MB, reserved memory is 575 MB, peak memory is 723 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.791328s wall, 3.109375s user + 0.031250s system = 3.140625s CPU (175.3%)

RUN-1004 : used memory is 600 MB, reserved memory is 575 MB, peak memory is 723 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8144 instances
RUN-1001 : 4022 mslices, 4017 lslices, 60 pads, 40 brams, 0 dsps
RUN-1001 : There are total 19443 nets
RUN-1001 : 13616 nets have 2 pins
RUN-1001 : 4408 nets have [3 - 5] pins
RUN-1001 : 886 nets have [6 - 10] pins
RUN-1001 : 397 nets have [11 - 20] pins
RUN-1001 : 127 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67879, tnet num: 19441, tinst num: 8142, tnode num: 92045, tedge num: 111936.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.952619s wall, 1.953125s user + 0.000000s system = 1.953125s CPU (100.0%)

RUN-1004 : used memory is 585 MB, reserved memory is 562 MB, peak memory is 723 MB
PHY-1001 : 4022 mslices, 4017 lslices, 60 pads, 40 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19441 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 676640, over cnt = 2281(6%), over = 3751, worst = 9
PHY-1002 : len = 693288, over cnt = 1238(3%), over = 1713, worst = 6
PHY-1002 : len = 706848, over cnt = 506(1%), over = 703, worst = 5
PHY-1002 : len = 718320, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.821934s wall, 3.062500s user + 0.015625s system = 3.078125s CPU (168.9%)

PHY-1001 : Congestion index: top1 = 47.78, top5 = 42.93, top10 = 40.29, top15 = 38.57.
PHY-1001 : End global routing;  2.195570s wall, 3.453125s user + 0.015625s system = 3.468750s CPU (158.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 698, reserve = 684, peak = 723.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 969, reserve = 957, peak = 969.
PHY-1001 : End build detailed router design. 4.920055s wall, 4.890625s user + 0.031250s system = 4.921875s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 193984, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.961582s wall, 0.953125s user + 0.015625s system = 0.968750s CPU (100.7%)

PHY-1001 : Current memory(MB): used = 1005, reserve = 994, peak = 1005.
PHY-1001 : End phase 1; 0.968830s wall, 0.953125s user + 0.015625s system = 0.968750s CPU (100.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.7701e+06, over cnt = 1380(0%), over = 1383, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1022, reserve = 1011, peak = 1022.
PHY-1001 : End initial routed; 17.989029s wall, 48.484375s user + 0.343750s system = 48.828125s CPU (271.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18170(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.211   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.497488s wall, 3.484375s user + 0.000000s system = 3.484375s CPU (99.6%)

PHY-1001 : Current memory(MB): used = 1033, reserve = 1022, peak = 1033.
PHY-1001 : End phase 2; 21.486692s wall, 51.968750s user + 0.343750s system = 52.312500s CPU (243.5%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.7701e+06, over cnt = 1380(0%), over = 1383, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.253167s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (98.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.75636e+06, over cnt = 435(0%), over = 435, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.908001s wall, 1.625000s user + 0.015625s system = 1.640625s CPU (180.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.75694e+06, over cnt = 70(0%), over = 70, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.456923s wall, 0.562500s user + 0.000000s system = 0.562500s CPU (123.1%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.75804e+06, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.226741s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (117.1%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.75826e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 4; 0.171115s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (100.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18170(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.211   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.542813s wall, 3.531250s user + 0.000000s system = 3.531250s CPU (99.7%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 315 feed throughs used by 264 nets
PHY-1001 : End commit to database; 2.306595s wall, 2.265625s user + 0.031250s system = 2.296875s CPU (99.6%)

PHY-1001 : Current memory(MB): used = 1124, reserve = 1116, peak = 1124.
PHY-1001 : End phase 3; 8.352195s wall, 9.171875s user + 0.046875s system = 9.218750s CPU (110.4%)

PHY-1003 : Routed, final wirelength = 1.75826e+06
PHY-1001 : Current memory(MB): used = 1128, reserve = 1120, peak = 1128.
PHY-1001 : End export database. 0.064701s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (120.7%)

PHY-1001 : End detail routing;  36.244616s wall, 67.515625s user + 0.437500s system = 67.953125s CPU (187.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67879, tnet num: 19441, tinst num: 8142, tnode num: 92045, tedge num: 111936.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.881999s wall, 1.875000s user + 0.000000s system = 1.875000s CPU (99.6%)

RUN-1004 : used memory is 1061 MB, reserved memory is 1057 MB, peak memory is 1128 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  45.122186s wall, 77.609375s user + 0.484375s system = 78.093750s CPU (173.1%)

RUN-1004 : used memory is 1060 MB, reserved memory is 1057 MB, peak memory is 1128 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8857   out of  19600   45.19%
#reg                    12428   out of  19600   63.41%
#le                     15007
  #lut only              2579   out of  15007   17.19%
  #reg only              6150   out of  15007   40.98%
  #lut&reg               6278   out of  15007   41.83%
#dsp                        0   out of     29    0.00%
#bram                      40   out of     64   62.50%
  #bram9k                  38
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6819
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          152
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         M3        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         P6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15007  |7355    |1502    |12472   |40      |0       |
|  AnyFog_dataX                      |AnyFog          |205    |103     |22      |169     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |84     |61      |22      |48      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |207    |80      |22      |172     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |87     |54      |22      |52      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |207    |112     |22      |170     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |89     |67      |22      |52      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2938   |633     |39      |2846    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |62     |38      |5       |52      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |211    |71      |5       |200     |0       |0       |
|    STADOP_com2                     |STADOP          |549    |90      |0       |543     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |63     |46      |14      |40      |0       |0       |
|    head_com2                       |uniheading      |273    |88      |5       |259     |0       |0       |
|    rmc_com2                        |Gprmc           |45     |45      |0       |35      |0       |0       |
|    uart_com2                       |Agrica          |1425   |245     |10      |1407    |0       |0       |
|  COM3                              |COM3_Control    |280    |147     |19      |238     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |62     |35      |5       |53      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |60     |45      |14      |37      |0       |0       |
|    rmc_com3                        |Gprmc           |158    |67      |0       |148     |0       |0       |
|  DATA                              |Data_Processing |8827   |4582    |1122    |7045    |0       |0       |
|    DIV_Dtemp                       |Divider         |816    |339     |84      |689     |0       |0       |
|    DIV_Utemp                       |Divider         |612    |306     |84      |477     |0       |0       |
|    DIV_accX                        |Divider         |597    |323     |84      |474     |0       |0       |
|    DIV_accY                        |Divider         |595    |322     |102     |441     |0       |0       |
|    DIV_accZ                        |Divider         |703    |420     |132     |499     |0       |0       |
|    DIV_rateX                       |Divider         |662    |370     |132     |457     |0       |0       |
|    DIV_rateY                       |Divider         |593    |397     |132     |389     |0       |0       |
|    DIV_rateZ                       |Divider         |578    |368     |132     |376     |0       |0       |
|    genclk                          |genclk          |264    |168     |89      |103     |0       |0       |
|  FMC                               |FMC_Ctrl        |436    |381     |43      |341     |0       |0       |
|  IIC                               |I2C_master      |300    |255     |11      |254     |0       |0       |
|  IMU_CTRL                          |SCHA634         |881    |662     |61      |713     |0       |0       |
|    CtrlData                        |CtrlData        |457    |405     |47      |325     |0       |0       |
|      usms                          |Time_1ms        |27     |22      |5       |19      |0       |0       |
|    SPIM                            |SPI_SCHA634     |424    |257     |14      |388     |0       |0       |
|  POWER                             |POWER_EN        |101    |48      |38      |42      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |609    |352     |103     |422     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |609    |352     |103     |422     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |261    |112     |0       |252     |0       |0       |
|        reg_inst                    |register        |259    |110     |0       |250     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |348    |240     |103     |170     |0       |0       |
|        bus_inst                    |bus_top         |134    |88      |46      |54      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |27     |17      |10      |11      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |51     |33      |18      |19      |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |3      |3       |0       |3       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |51     |33      |18      |19      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |137    |103     |29      |86      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13555  
    #2          2       3424   
    #3          3        676   
    #4          4        308   
    #5        5-10       959   
    #6        11-50      440   
    #7       51-100      11    
    #8       101-500      3    
    #9        >500        2    
  Average     2.14             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.273728s wall, 3.921875s user + 0.031250s system = 3.953125s CPU (173.9%)

RUN-1004 : used memory is 1061 MB, reserved memory is 1058 MB, peak memory is 1128 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67879, tnet num: 19441, tinst num: 8142, tnode num: 92045, tedge num: 111936.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.813541s wall, 1.812500s user + 0.000000s system = 1.812500s CPU (99.9%)

RUN-1004 : used memory is 1062 MB, reserved memory is 1059 MB, peak memory is 1128 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19441 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.455953s wall, 1.453125s user + 0.000000s system = 1.453125s CPU (99.8%)

RUN-1004 : used memory is 1067 MB, reserved memory is 1063 MB, peak memory is 1128 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 19285304e968e394d67a8f065444a1f66c49f77adeb419c41e6b7764d8228547 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8142
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19443, pip num: 148137
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 315
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3257 valid insts, and 414302 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011111001000110011001111
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.535002s wall, 125.921875s user + 0.187500s system = 126.109375s CPU (1006.1%)

RUN-1004 : used memory is 1188 MB, reserved memory is 1177 MB, peak memory is 1303 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250821_151919.log"

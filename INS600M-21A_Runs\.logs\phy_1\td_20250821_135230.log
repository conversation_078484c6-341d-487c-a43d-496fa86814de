============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Aug 21 13:52:30 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-5007 WARNING: data object 'write_en' is already declared in ../../Src/FMC/FMC_Ctrl.v(114)
HDL-1007 : previous declaration of 'write_en' is from here in ../../Src/FMC/FMC_Ctrl.v(113)
HDL-5007 WARNING: second declaration of 'write_en' is ignored in ../../Src/FMC/FMC_Ctrl.v(114)
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.220209s wall, 1.343750s user + 3.890625s system = 5.234375s CPU (100.3%)

RUN-1004 : used memory is 79 MB, reserved memory is 40 MB, peak memory is 90 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.758551s wall, 1.687500s user + 0.078125s system = 1.765625s CPU (100.4%)

RUN-1004 : used memory is 302 MB, reserved memory is 270 MB, peak memory is 305 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 42 trigger nets, 42 data nets.
KIT-1004 : Chipwatcher code = 0000101011000101
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=126) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=126) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=126)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=126)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22772/23 useful/useless nets, 19520/12 useful/useless insts
SYN-1016 : Merged 29 instances.
SYN-1032 : 22440/20 useful/useless nets, 19946/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 398 better
SYN-1014 : Optimize round 2
SYN-1032 : 22120/45 useful/useless nets, 19626/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.095066s wall, 2.000000s user + 0.093750s system = 2.093750s CPU (99.9%)

RUN-1004 : used memory is 328 MB, reserved memory is 295 MB, peak memory is 330 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22168/299 useful/useless nets, 19711/47 useful/useless insts
SYN-1016 : Merged 45 instances.
SYN-2571 : Optimize after map_dsp, round 1, 391 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 40 instances.
SYN-2501 : Optimize round 1, 82 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 22616/5 useful/useless nets, 20159/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82498, tnet num: 22616, tinst num: 20158, tnode num: 115578, tedge num: 128979.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.121983s wall, 1.093750s user + 0.031250s system = 1.125000s CPU (100.3%)

RUN-1004 : used memory is 468 MB, reserved memory is 436 MB, peak memory is 468 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22616 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 234 (3.43), #lev = 7 (1.79)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 234 (3.43), #lev = 7 (1.79)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 534 instances into 234 LUTs, name keeping = 75%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 407 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 128 adder to BLE ...
SYN-4008 : Packed 128 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.035023s wall, 3.953125s user + 0.078125s system = 4.031250s CPU (99.9%)

RUN-1004 : used memory is 364 MB, reserved memory is 348 MB, peak memory is 577 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  6.422193s wall, 6.234375s user + 0.187500s system = 6.421875s CPU (100.0%)

RUN-1004 : used memory is 364 MB, reserved memory is 348 MB, peak memory is 577 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (271 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19399 instances
RUN-0007 : 5593 luts, 12200 seqs, 983 mslices, 519 lslices, 59 pads, 40 brams, 0 dsps
RUN-1001 : There are total 21864 nets
RUN-1001 : 16396 nets have 2 pins
RUN-1001 : 4284 nets have [3 - 5] pins
RUN-1001 : 812 nets have [6 - 10] pins
RUN-1001 : 245 nets have [11 - 20] pins
RUN-1001 : 105 nets have [21 - 99] pins
RUN-1001 : 22 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4784     
RUN-1001 :   No   |  No   |  Yes  |     704     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     443     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19397 instances, 5593 luts, 12200 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80875, tnet num: 21862, tinst num: 19397, tnode num: 113746, tedge num: 127301.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.121502s wall, 1.093750s user + 0.031250s system = 1.125000s CPU (100.3%)

RUN-1004 : used memory is 528 MB, reserved memory is 501 MB, peak memory is 577 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21862 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.912158s wall, 1.859375s user + 0.046875s system = 1.906250s CPU (99.7%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.63919e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19397.
PHY-3001 : Level 1 #clusters 2170.
PHY-3001 : End clustering;  0.141471s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (176.7%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 887589, overlap = 656.594
PHY-3002 : Step(2): len = 807135, overlap = 722.25
PHY-3002 : Step(3): len = 544748, overlap = 891.281
PHY-3002 : Step(4): len = 484744, overlap = 945.781
PHY-3002 : Step(5): len = 380750, overlap = 1026.91
PHY-3002 : Step(6): len = 336794, overlap = 1089.12
PHY-3002 : Step(7): len = 279489, overlap = 1161.06
PHY-3002 : Step(8): len = 253642, overlap = 1212.31
PHY-3002 : Step(9): len = 216997, overlap = 1266.22
PHY-3002 : Step(10): len = 200690, overlap = 1306.47
PHY-3002 : Step(11): len = 178185, overlap = 1359.94
PHY-3002 : Step(12): len = 164783, overlap = 1380.91
PHY-3002 : Step(13): len = 148969, overlap = 1426.06
PHY-3002 : Step(14): len = 139948, overlap = 1442.22
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.09251e-06
PHY-3002 : Step(15): len = 142273, overlap = 1430.88
PHY-3002 : Step(16): len = 182732, overlap = 1305.12
PHY-3002 : Step(17): len = 192553, overlap = 1251.75
PHY-3002 : Step(18): len = 196311, overlap = 1163.03
PHY-3002 : Step(19): len = 192413, overlap = 1141.12
PHY-3002 : Step(20): len = 189124, overlap = 1119.19
PHY-3002 : Step(21): len = 184032, overlap = 1114.94
PHY-3002 : Step(22): len = 180288, overlap = 1126.31
PHY-3002 : Step(23): len = 176562, overlap = 1098.09
PHY-3002 : Step(24): len = 174089, overlap = 1090.75
PHY-3002 : Step(25): len = 171855, overlap = 1086.91
PHY-3002 : Step(26): len = 170534, overlap = 1092.94
PHY-3002 : Step(27): len = 168486, overlap = 1110.25
PHY-3002 : Step(28): len = 167829, overlap = 1119.12
PHY-3002 : Step(29): len = 166810, overlap = 1134.09
PHY-3002 : Step(30): len = 166678, overlap = 1135.5
PHY-3002 : Step(31): len = 166164, overlap = 1132.53
PHY-3002 : Step(32): len = 166188, overlap = 1120.34
PHY-3002 : Step(33): len = 166094, overlap = 1102.12
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.18502e-06
PHY-3002 : Step(34): len = 175773, overlap = 1087.72
PHY-3002 : Step(35): len = 191781, overlap = 983.719
PHY-3002 : Step(36): len = 195196, overlap = 925.406
PHY-3002 : Step(37): len = 198049, overlap = 917.344
PHY-3002 : Step(38): len = 197671, overlap = 904.562
PHY-3002 : Step(39): len = 196944, overlap = 893.312
PHY-3002 : Step(40): len = 195189, overlap = 895.188
PHY-3002 : Step(41): len = 195583, overlap = 902.062
PHY-3002 : Step(42): len = 194515, overlap = 925.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.37005e-06
PHY-3002 : Step(43): len = 207225, overlap = 877.812
PHY-3002 : Step(44): len = 221883, overlap = 800.875
PHY-3002 : Step(45): len = 227460, overlap = 770.469
PHY-3002 : Step(46): len = 230732, overlap = 758.312
PHY-3002 : Step(47): len = 230703, overlap = 749.844
PHY-3002 : Step(48): len = 229889, overlap = 770.062
PHY-3002 : Step(49): len = 229094, overlap = 774.438
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 8.74009e-06
PHY-3002 : Step(50): len = 241576, overlap = 710.562
PHY-3002 : Step(51): len = 256669, overlap = 646.906
PHY-3002 : Step(52): len = 261957, overlap = 604.656
PHY-3002 : Step(53): len = 264481, overlap = 601.281
PHY-3002 : Step(54): len = 263824, overlap = 613.594
PHY-3002 : Step(55): len = 262809, overlap = 624.531
PHY-3002 : Step(56): len = 261836, overlap = 626.375
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.74802e-05
PHY-3002 : Step(57): len = 273700, overlap = 582.5
PHY-3002 : Step(58): len = 290006, overlap = 487.594
PHY-3002 : Step(59): len = 295207, overlap = 473.281
PHY-3002 : Step(60): len = 296859, overlap = 445.812
PHY-3002 : Step(61): len = 294530, overlap = 428.156
PHY-3002 : Step(62): len = 292581, overlap = 425.469
PHY-3002 : Step(63): len = 291472, overlap = 422.531
PHY-3002 : Step(64): len = 291947, overlap = 403.406
PHY-3002 : Step(65): len = 289812, overlap = 400.594
PHY-3002 : Step(66): len = 288840, overlap = 399.125
PHY-3002 : Step(67): len = 286518, overlap = 400.031
PHY-3002 : Step(68): len = 286767, overlap = 399.281
PHY-3002 : Step(69): len = 285556, overlap = 408.531
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.49604e-05
PHY-3002 : Step(70): len = 297002, overlap = 386.906
PHY-3002 : Step(71): len = 307963, overlap = 373.656
PHY-3002 : Step(72): len = 311591, overlap = 370.031
PHY-3002 : Step(73): len = 312821, overlap = 356.438
PHY-3002 : Step(74): len = 311651, overlap = 361.625
PHY-3002 : Step(75): len = 309973, overlap = 368.594
PHY-3002 : Step(76): len = 308701, overlap = 382.062
PHY-3002 : Step(77): len = 307784, overlap = 377.812
PHY-3002 : Step(78): len = 306344, overlap = 384.219
PHY-3002 : Step(79): len = 306314, overlap = 389.062
PHY-3002 : Step(80): len = 305121, overlap = 380.281
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 6.99208e-05
PHY-3002 : Step(81): len = 313768, overlap = 381.344
PHY-3002 : Step(82): len = 320186, overlap = 367.219
PHY-3002 : Step(83): len = 322359, overlap = 353.281
PHY-3002 : Step(84): len = 324081, overlap = 342.188
PHY-3002 : Step(85): len = 324057, overlap = 337.25
PHY-3002 : Step(86): len = 324602, overlap = 326
PHY-3002 : Step(87): len = 324147, overlap = 328.625
PHY-3002 : Step(88): len = 325787, overlap = 318.625
PHY-3002 : Step(89): len = 324317, overlap = 323.062
PHY-3002 : Step(90): len = 323897, overlap = 321.906
PHY-3002 : Step(91): len = 322903, overlap = 315.531
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000139842
PHY-3002 : Step(92): len = 327857, overlap = 309.125
PHY-3002 : Step(93): len = 332999, overlap = 298.469
PHY-3002 : Step(94): len = 334910, overlap = 294.844
PHY-3002 : Step(95): len = 336539, overlap = 282.906
PHY-3002 : Step(96): len = 336333, overlap = 285.062
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000279683
PHY-3002 : Step(97): len = 338660, overlap = 274.812
PHY-3002 : Step(98): len = 343596, overlap = 251.469
PHY-3002 : Step(99): len = 346105, overlap = 248.156
PHY-3002 : Step(100): len = 347562, overlap = 224.125
PHY-3002 : Step(101): len = 347902, overlap = 222.438
PHY-3002 : Step(102): len = 347249, overlap = 242.719
PHY-3002 : Step(103): len = 346397, overlap = 235.719
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(104): len = 347675, overlap = 232.688
PHY-3002 : Step(105): len = 349900, overlap = 241.594
PHY-3002 : Step(106): len = 350674, overlap = 233.719
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.012610s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (123.9%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21864.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 458200, over cnt = 1198(3%), over = 5434, worst = 62
PHY-1001 : End global iterations;  0.784444s wall, 1.031250s user + 0.046875s system = 1.078125s CPU (137.4%)

PHY-1001 : Congestion index: top1 = 78.75, top5 = 53.61, top10 = 43.89, top15 = 38.24.
PHY-3001 : End congestion estimation;  1.004123s wall, 1.250000s user + 0.046875s system = 1.296875s CPU (129.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21862 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.831527s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (99.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00010502
PHY-3002 : Step(107): len = 401593, overlap = 159.344
PHY-3002 : Step(108): len = 415556, overlap = 145.938
PHY-3002 : Step(109): len = 414718, overlap = 149.625
PHY-3002 : Step(110): len = 413284, overlap = 133.875
PHY-3002 : Step(111): len = 419263, overlap = 121.469
PHY-3002 : Step(112): len = 426177, overlap = 117.844
PHY-3002 : Step(113): len = 427925, overlap = 116.062
PHY-3002 : Step(114): len = 428077, overlap = 109.344
PHY-3002 : Step(115): len = 429456, overlap = 98.8125
PHY-3002 : Step(116): len = 430841, overlap = 95.3125
PHY-3002 : Step(117): len = 431230, overlap = 92.6562
PHY-3002 : Step(118): len = 432629, overlap = 92.0938
PHY-3002 : Step(119): len = 433884, overlap = 97.0938
PHY-3002 : Step(120): len = 434973, overlap = 98.4375
PHY-3002 : Step(121): len = 437011, overlap = 100.344
PHY-3002 : Step(122): len = 438031, overlap = 101.625
PHY-3002 : Step(123): len = 439549, overlap = 106.594
PHY-3002 : Step(124): len = 442200, overlap = 111.25
PHY-3002 : Step(125): len = 443178, overlap = 114.344
PHY-3002 : Step(126): len = 443998, overlap = 107.125
PHY-3002 : Step(127): len = 446373, overlap = 108.156
PHY-3002 : Step(128): len = 446116, overlap = 112.719
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(129): len = 446154, overlap = 111
PHY-3002 : Step(130): len = 446617, overlap = 110.781
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 47/21864.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 514944, over cnt = 2108(5%), over = 9748, worst = 44
PHY-1001 : End global iterations;  1.066138s wall, 1.640625s user + 0.062500s system = 1.703125s CPU (159.7%)

PHY-1001 : Congestion index: top1 = 77.22, top5 = 59.69, top10 = 50.50, top15 = 45.11.
PHY-3001 : End congestion estimation;  1.297955s wall, 1.875000s user + 0.062500s system = 1.937500s CPU (149.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21862 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.851794s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (100.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.27363e-05
PHY-3002 : Step(131): len = 453675, overlap = 386.594
PHY-3002 : Step(132): len = 465855, overlap = 336.594
PHY-3002 : Step(133): len = 458841, overlap = 327.188
PHY-3002 : Step(134): len = 455811, overlap = 315.938
PHY-3002 : Step(135): len = 456809, overlap = 297.781
PHY-3002 : Step(136): len = 455672, overlap = 282.812
PHY-3002 : Step(137): len = 454178, overlap = 256.406
PHY-3002 : Step(138): len = 454151, overlap = 252.656
PHY-3002 : Step(139): len = 452746, overlap = 248.594
PHY-3002 : Step(140): len = 451809, overlap = 249.281
PHY-3002 : Step(141): len = 450169, overlap = 246.188
PHY-3002 : Step(142): len = 448998, overlap = 246.125
PHY-3002 : Step(143): len = 448708, overlap = 247.562
PHY-3002 : Step(144): len = 446495, overlap = 249.188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000165473
PHY-3002 : Step(145): len = 446956, overlap = 249.094
PHY-3002 : Step(146): len = 447894, overlap = 251.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000321748
PHY-3002 : Step(147): len = 450912, overlap = 230.25
PHY-3002 : Step(148): len = 459371, overlap = 220.844
PHY-3002 : Step(149): len = 461634, overlap = 212.031
PHY-3002 : Step(150): len = 461832, overlap = 213.625
PHY-3002 : Step(151): len = 463214, overlap = 208.969
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000643496
PHY-3002 : Step(152): len = 465200, overlap = 209.906
PHY-3002 : Step(153): len = 467297, overlap = 203.688
PHY-3002 : Step(154): len = 472340, overlap = 197.031
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00117665
PHY-3002 : Step(155): len = 472796, overlap = 197.656
PHY-3002 : Step(156): len = 478418, overlap = 173.719
PHY-3002 : Step(157): len = 483516, overlap = 163
PHY-3002 : Step(158): len = 485703, overlap = 156.812
PHY-3002 : Step(159): len = 486629, overlap = 158.312
PHY-3002 : Step(160): len = 487107, overlap = 157.281
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00219151
PHY-3002 : Step(161): len = 487328, overlap = 157.125
PHY-3002 : Step(162): len = 488509, overlap = 154.406
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80875, tnet num: 21862, tinst num: 19397, tnode num: 113746, tedge num: 127301.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.415817s wall, 1.406250s user + 0.015625s system = 1.421875s CPU (100.4%)

RUN-1004 : used memory is 569 MB, reserved memory is 543 MB, peak memory is 702 MB
OPT-1001 : Total overflow 506.19 peak overflow 4.16
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 472/21864.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 570728, over cnt = 2510(7%), over = 8802, worst = 28
PHY-1001 : End global iterations;  1.214686s wall, 1.781250s user + 0.015625s system = 1.796875s CPU (147.9%)

PHY-1001 : Congestion index: top1 = 58.73, top5 = 48.36, top10 = 43.54, top15 = 40.55.
PHY-1001 : End incremental global routing;  1.509083s wall, 2.062500s user + 0.015625s system = 2.078125s CPU (137.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21862 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.889055s wall, 0.828125s user + 0.062500s system = 0.890625s CPU (100.2%)

OPT-1001 : 16 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19319 has valid locations, 235 needs to be replaced
PHY-3001 : design contains 19616 instances, 5680 luts, 12332 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 503350
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17285/22083.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 582080, over cnt = 2509(7%), over = 8842, worst = 28
PHY-1001 : End global iterations;  0.173734s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (134.9%)

PHY-1001 : Congestion index: top1 = 58.77, top5 = 48.52, top10 = 43.74, top15 = 40.77.
PHY-3001 : End congestion estimation;  0.421068s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (107.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81599, tnet num: 22081, tinst num: 19616, tnode num: 114785, tedge num: 128311.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.438793s wall, 1.359375s user + 0.093750s system = 1.453125s CPU (101.0%)

RUN-1004 : used memory is 613 MB, reserved memory is 606 MB, peak memory is 705 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22081 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.380100s wall, 2.250000s user + 0.140625s system = 2.390625s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(163): len = 503423, overlap = 1.9375
PHY-3002 : Step(164): len = 504471, overlap = 1.9375
PHY-3002 : Step(165): len = 505412, overlap = 2
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17308/22083.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 582000, over cnt = 2547(7%), over = 8930, worst = 28
PHY-1001 : End global iterations;  0.175390s wall, 0.187500s user + 0.046875s system = 0.234375s CPU (133.6%)

PHY-1001 : Congestion index: top1 = 58.84, top5 = 48.90, top10 = 44.09, top15 = 41.03.
PHY-3001 : End congestion estimation;  0.413052s wall, 0.437500s user + 0.046875s system = 0.484375s CPU (117.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22081 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.888117s wall, 0.875000s user + 0.000000s system = 0.875000s CPU (98.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000623818
PHY-3002 : Step(166): len = 505463, overlap = 157.906
PHY-3002 : Step(167): len = 505720, overlap = 157.281
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00124764
PHY-3002 : Step(168): len = 505923, overlap = 157.656
PHY-3002 : Step(169): len = 506388, overlap = 157.031
PHY-3001 : Final: Len = 506388, Over = 157.031
PHY-3001 : End incremental placement;  4.865481s wall, 5.203125s user + 0.265625s system = 5.468750s CPU (112.4%)

OPT-1001 : Total overflow 512.38 peak overflow 4.28
OPT-1001 : End high-fanout net optimization;  7.722173s wall, 8.718750s user + 0.343750s system = 9.062500s CPU (117.4%)

OPT-1001 : Current memory(MB): used = 706, reserve = 686, peak = 723.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17319/22083.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 584464, over cnt = 2485(7%), over = 8448, worst = 28
PHY-1002 : len = 625744, over cnt = 1729(4%), over = 4494, worst = 22
PHY-1002 : len = 670840, over cnt = 618(1%), over = 1318, worst = 16
PHY-1002 : len = 686392, over cnt = 177(0%), over = 347, worst = 10
PHY-1002 : len = 691136, over cnt = 13(0%), over = 20, worst = 3
PHY-1001 : End global iterations;  1.203419s wall, 1.812500s user + 0.000000s system = 1.812500s CPU (150.6%)

PHY-1001 : Congestion index: top1 = 51.08, top5 = 44.34, top10 = 40.99, top15 = 38.95.
OPT-1001 : End congestion update;  1.479750s wall, 2.093750s user + 0.000000s system = 2.093750s CPU (141.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22081 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.913327s wall, 0.906250s user + 0.000000s system = 0.906250s CPU (99.2%)

OPT-0007 : Start: WNS 4369 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.397997s wall, 3.000000s user + 0.000000s system = 3.000000s CPU (125.1%)

OPT-1001 : Current memory(MB): used = 706, reserve = 685, peak = 723.
OPT-1001 : End physical optimization;  11.832277s wall, 13.578125s user + 0.390625s system = 13.968750s CPU (118.1%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5680 LUT to BLE ...
SYN-4008 : Packed 5680 LUT and 2726 SEQ to BLE.
SYN-4003 : Packing 9606 remaining SEQ's ...
SYN-4005 : Packed 3377 SEQ with LUT/SLICE
SYN-4006 : 109 single LUT's are left
SYN-4006 : 6229 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11909/13779 primitive instances ...
PHY-3001 : End packing;  2.613647s wall, 2.609375s user + 0.000000s system = 2.609375s CPU (99.8%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8130 instances
RUN-1001 : 4013 mslices, 4013 lslices, 59 pads, 40 brams, 0 dsps
RUN-1001 : There are total 19409 nets
RUN-1001 : 13599 nets have 2 pins
RUN-1001 : 4403 nets have [3 - 5] pins
RUN-1001 : 885 nets have [6 - 10] pins
RUN-1001 : 380 nets have [11 - 20] pins
RUN-1001 : 133 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8128 instances, 8026 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 522796, Over = 394.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7636/19409.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 652832, over cnt = 1599(4%), over = 2666, worst = 9
PHY-1002 : len = 660720, over cnt = 1106(3%), over = 1580, worst = 9
PHY-1002 : len = 676088, over cnt = 345(0%), over = 457, worst = 6
PHY-1002 : len = 680936, over cnt = 135(0%), over = 181, worst = 5
PHY-1002 : len = 684488, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.198150s wall, 1.875000s user + 0.046875s system = 1.921875s CPU (160.4%)

PHY-1001 : Congestion index: top1 = 51.19, top5 = 44.64, top10 = 41.05, top15 = 38.79.
PHY-3001 : End congestion estimation;  1.498172s wall, 2.187500s user + 0.046875s system = 2.234375s CPU (149.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67741, tnet num: 19407, tinst num: 8128, tnode num: 91897, tedge num: 111750.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.629412s wall, 1.625000s user + 0.000000s system = 1.625000s CPU (99.7%)

RUN-1004 : used memory is 599 MB, reserved memory is 583 MB, peak memory is 723 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19407 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.448796s wall, 2.406250s user + 0.046875s system = 2.453125s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.0512e-05
PHY-3002 : Step(170): len = 526620, overlap = 357.5
PHY-3002 : Step(171): len = 527276, overlap = 363
PHY-3002 : Step(172): len = 527123, overlap = 378.5
PHY-3002 : Step(173): len = 529132, overlap = 379
PHY-3002 : Step(174): len = 527273, overlap = 382.5
PHY-3002 : Step(175): len = 527210, overlap = 379.25
PHY-3002 : Step(176): len = 526532, overlap = 377.75
PHY-3002 : Step(177): len = 525271, overlap = 394.75
PHY-3002 : Step(178): len = 523757, overlap = 395.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000101024
PHY-3002 : Step(179): len = 529169, overlap = 385.5
PHY-3002 : Step(180): len = 533675, overlap = 376
PHY-3002 : Step(181): len = 533339, overlap = 377.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000202048
PHY-3002 : Step(182): len = 540410, overlap = 367
PHY-3002 : Step(183): len = 551110, overlap = 352
PHY-3002 : Step(184): len = 549986, overlap = 350.25
PHY-3002 : Step(185): len = 548404, overlap = 342
PHY-3002 : Step(186): len = 548639, overlap = 337.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.610275s wall, 0.656250s user + 0.718750s system = 1.375000s CPU (225.3%)

PHY-3001 : Trial Legalized: Len = 654802
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 559/19409.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 743016, over cnt = 2375(6%), over = 3934, worst = 9
PHY-1002 : len = 759120, over cnt = 1370(3%), over = 1952, worst = 8
PHY-1002 : len = 777952, over cnt = 442(1%), over = 603, worst = 8
PHY-1002 : len = 786296, over cnt = 84(0%), over = 118, worst = 8
PHY-1002 : len = 789072, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.812524s wall, 3.078125s user + 0.046875s system = 3.125000s CPU (172.4%)

PHY-1001 : Congestion index: top1 = 52.09, top5 = 46.78, top10 = 43.74, top15 = 41.80.
PHY-3001 : End congestion estimation;  2.150605s wall, 3.421875s user + 0.046875s system = 3.468750s CPU (161.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19407 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.803961s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000189631
PHY-3002 : Step(187): len = 614239, overlap = 78.25
PHY-3002 : Step(188): len = 595599, overlap = 128.75
PHY-3002 : Step(189): len = 583964, overlap = 171.75
PHY-3002 : Step(190): len = 577159, overlap = 199.5
PHY-3002 : Step(191): len = 572781, overlap = 232.25
PHY-3002 : Step(192): len = 570828, overlap = 240.75
PHY-3002 : Step(193): len = 569545, overlap = 252.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000379261
PHY-3002 : Step(194): len = 575218, overlap = 244
PHY-3002 : Step(195): len = 581092, overlap = 237.25
PHY-3002 : Step(196): len = 582813, overlap = 236
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(197): len = 586132, overlap = 233.75
PHY-3002 : Step(198): len = 592425, overlap = 232.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.032200s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (97.1%)

PHY-3001 : Legalized: Len = 632335, Over = 0
PHY-3001 : Spreading special nets. 45 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.072408s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (107.9%)

PHY-3001 : 66 instances has been re-located, deltaX = 23, deltaY = 36, maxDist = 2.
PHY-3001 : Final: Len = 633695, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67741, tnet num: 19407, tinst num: 8128, tnode num: 91897, tedge num: 111750.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.883790s wall, 1.875000s user + 0.000000s system = 1.875000s CPU (99.5%)

RUN-1004 : used memory is 616 MB, reserved memory is 615 MB, peak memory is 723 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 4058/19409.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 732392, over cnt = 2250(6%), over = 3537, worst = 7
PHY-1002 : len = 745728, over cnt = 1272(3%), over = 1670, worst = 6
PHY-1002 : len = 760200, over cnt = 508(1%), over = 631, worst = 6
PHY-1002 : len = 768392, over cnt = 142(0%), over = 160, worst = 4
PHY-1002 : len = 771016, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.579011s wall, 2.562500s user + 0.000000s system = 2.562500s CPU (162.3%)

PHY-1001 : Congestion index: top1 = 51.01, top5 = 45.67, top10 = 42.68, top15 = 40.78.
PHY-1001 : End incremental global routing;  1.859426s wall, 2.843750s user + 0.000000s system = 2.843750s CPU (152.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19407 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.816632s wall, 0.781250s user + 0.031250s system = 0.812500s CPU (99.5%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8064 has valid locations, 14 needs to be replaced
PHY-3001 : design contains 8140 instances, 8038 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 637275
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17485/19431.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 775480, over cnt = 25(0%), over = 30, worst = 4
PHY-1002 : len = 775432, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 775496, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 775576, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.507640s wall, 0.500000s user + 0.000000s system = 0.500000s CPU (98.5%)

PHY-1001 : Congestion index: top1 = 51.10, top5 = 45.86, top10 = 42.86, top15 = 40.96.
PHY-3001 : End congestion estimation;  0.778357s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (100.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67844, tnet num: 19429, tinst num: 8140, tnode num: 92026, tedge num: 111909.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.819405s wall, 1.812500s user + 0.000000s system = 1.812500s CPU (99.6%)

RUN-1004 : used memory is 645 MB, reserved memory is 632 MB, peak memory is 723 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19429 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.654747s wall, 2.656250s user + 0.000000s system = 2.656250s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(199): len = 636877, overlap = 0.75
PHY-3002 : Step(200): len = 636662, overlap = 1
PHY-3002 : Step(201): len = 636628, overlap = 1.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17483/19431.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 773440, over cnt = 31(0%), over = 39, worst = 4
PHY-1002 : len = 773504, over cnt = 13(0%), over = 13, worst = 1
PHY-1002 : len = 773536, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 773600, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 773696, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.649637s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (103.4%)

PHY-1001 : Congestion index: top1 = 50.95, top5 = 45.65, top10 = 42.68, top15 = 40.86.
PHY-3001 : End congestion estimation;  0.926884s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (102.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19429 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.812085s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00117568
PHY-3002 : Step(202): len = 636552, overlap = 2.5
PHY-3002 : Step(203): len = 636576, overlap = 2
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006657s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 636555, Over = 0
PHY-3001 : End spreading;  0.060645s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.1%)

PHY-3001 : Final: Len = 636555, Over = 0
PHY-3001 : End incremental placement;  5.775328s wall, 6.000000s user + 0.031250s system = 6.031250s CPU (104.4%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  8.897248s wall, 10.062500s user + 0.062500s system = 10.125000s CPU (113.8%)

OPT-1001 : Current memory(MB): used = 720, reserve = 703, peak = 725.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17483/19431.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 773344, over cnt = 11(0%), over = 23, worst = 4
PHY-1002 : len = 773416, over cnt = 4(0%), over = 6, worst = 2
PHY-1002 : len = 773448, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.375935s wall, 0.390625s user + 0.015625s system = 0.406250s CPU (108.1%)

PHY-1001 : Congestion index: top1 = 50.99, top5 = 45.71, top10 = 42.71, top15 = 40.82.
OPT-1001 : End congestion update;  0.647824s wall, 0.671875s user + 0.015625s system = 0.687500s CPU (106.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19429 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.685631s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (100.3%)

OPT-0007 : Start: WNS 4358 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.337836s wall, 1.359375s user + 0.015625s system = 1.375000s CPU (102.8%)

OPT-1001 : Current memory(MB): used = 720, reserve = 703, peak = 725.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19429 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.693531s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (99.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17497/19431.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 773448, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.114842s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (95.2%)

PHY-1001 : Congestion index: top1 = 50.99, top5 = 45.71, top10 = 42.71, top15 = 40.82.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19429 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.692739s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (97.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4358 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 50.517241
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4358ps with logic level 5 
OPT-1001 : End physical optimization;  14.149365s wall, 15.343750s user + 0.109375s system = 15.453125s CPU (109.2%)

RUN-1003 : finish command "place" in  67.417996s wall, 120.281250s user + 7.468750s system = 127.750000s CPU (189.5%)

RUN-1004 : used memory is 600 MB, reserved memory is 588 MB, peak memory is 725 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.555871s wall, 2.671875s user + 0.000000s system = 2.671875s CPU (171.7%)

RUN-1004 : used memory is 600 MB, reserved memory is 589 MB, peak memory is 725 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8142 instances
RUN-1001 : 4022 mslices, 4016 lslices, 59 pads, 40 brams, 0 dsps
RUN-1001 : There are total 19431 nets
RUN-1001 : 13610 nets have 2 pins
RUN-1001 : 4402 nets have [3 - 5] pins
RUN-1001 : 889 nets have [6 - 10] pins
RUN-1001 : 388 nets have [11 - 20] pins
RUN-1001 : 133 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67844, tnet num: 19429, tinst num: 8140, tnode num: 92026, tedge num: 111909.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.636992s wall, 1.625000s user + 0.000000s system = 1.625000s CPU (99.3%)

RUN-1004 : used memory is 594 MB, reserved memory is 582 MB, peak memory is 725 MB
PHY-1001 : 4022 mslices, 4016 lslices, 59 pads, 40 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19429 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 713504, over cnt = 2379(6%), over = 3905, worst = 7
PHY-1002 : len = 730624, over cnt = 1465(4%), over = 2017, worst = 6
PHY-1002 : len = 747536, over cnt = 645(1%), over = 805, worst = 5
PHY-1002 : len = 760272, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 760384, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.643155s wall, 2.812500s user + 0.031250s system = 2.843750s CPU (173.1%)

PHY-1001 : Congestion index: top1 = 51.34, top5 = 45.69, top10 = 42.55, top15 = 40.52.
PHY-1001 : End global routing;  1.951340s wall, 3.125000s user + 0.031250s system = 3.156250s CPU (161.7%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 700, reserve = 688, peak = 725.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 971, reserve = 958, peak = 971.
PHY-1001 : End build detailed router design. 4.257792s wall, 4.187500s user + 0.078125s system = 4.265625s CPU (100.2%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 190512, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.767002s wall, 0.750000s user + 0.015625s system = 0.765625s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 1007, reserve = 995, peak = 1007.
PHY-1001 : End phase 1; 0.774504s wall, 0.750000s user + 0.015625s system = 0.765625s CPU (98.9%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.74817e+06, over cnt = 1410(0%), over = 1422, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1023, reserve = 1010, peak = 1023.
PHY-1001 : End initial routed; 16.214145s wall, 46.296875s user + 0.562500s system = 46.859375s CPU (289.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18159(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.100   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.099740s wall, 3.109375s user + 0.000000s system = 3.109375s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 1041, reserve = 1029, peak = 1041.
PHY-1001 : End phase 2; 19.314016s wall, 49.406250s user + 0.562500s system = 49.968750s CPU (258.7%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.74817e+06, over cnt = 1410(0%), over = 1422, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.215082s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (101.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.73185e+06, over cnt = 500(0%), over = 500, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 1.202963s wall, 2.171875s user + 0.000000s system = 2.171875s CPU (180.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.73155e+06, over cnt = 143(0%), over = 143, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.418648s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (130.6%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.7329e+06, over cnt = 21(0%), over = 21, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.366259s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (123.7%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.7332e+06, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.339543s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (101.2%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.73323e+06, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.317798s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (98.3%)

PHY-1001 : ===== DR Iter 6 =====
PHY-1022 : len = 1.73322e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 6; 0.329857s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (99.5%)

PHY-1001 : ===== DR Iter 7 =====
PHY-1022 : len = 1.73322e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 7; 0.534803s wall, 0.531250s user + 0.000000s system = 0.531250s CPU (99.3%)

PHY-1001 : ===== DR Iter 8 =====
PHY-1022 : len = 1.73325e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 8; 0.152661s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (102.4%)

PHY-1001 : ==== DR Iter 9 ====
PHY-1022 : len = 1.73323e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 9; 0.165767s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (103.7%)

PHY-1001 : ==== DR Iter 10 ====
PHY-1022 : len = 1.73326e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 10; 0.140922s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (99.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18159(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.100   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.123570s wall, 3.109375s user + 0.015625s system = 3.125000s CPU (100.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 361 feed throughs used by 289 nets
PHY-1001 : End commit to database; 2.177558s wall, 2.171875s user + 0.000000s system = 2.171875s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 1124, reserve = 1115, peak = 1124.
PHY-1001 : End phase 3; 9.977489s wall, 11.125000s user + 0.015625s system = 11.140625s CPU (111.7%)

PHY-1003 : Routed, final wirelength = 1.73326e+06
PHY-1001 : Current memory(MB): used = 1128, reserve = 1119, peak = 1128.
PHY-1001 : End export database. 0.058399s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (107.0%)

PHY-1001 : End detail routing;  34.761216s wall, 65.890625s user + 0.687500s system = 66.578125s CPU (191.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67844, tnet num: 19429, tinst num: 8140, tnode num: 92026, tedge num: 111909.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.588578s wall, 1.578125s user + 0.000000s system = 1.578125s CPU (99.3%)

RUN-1004 : used memory is 1057 MB, reserved memory is 1053 MB, peak memory is 1128 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  42.278456s wall, 74.531250s user + 0.765625s system = 75.296875s CPU (178.1%)

RUN-1004 : used memory is 1056 MB, reserved memory is 1052 MB, peak memory is 1128 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8851   out of  19600   45.16%
#reg                    12440   out of  19600   63.47%
#le                     15025
  #lut only              2585   out of  15025   17.20%
  #reg only              6174   out of  15025   41.09%
  #lut&reg               6266   out of  15025   41.70%
#dsp                        0   out of     29    0.00%
#bram                      40   out of     64   62.50%
  #bram9k                  38
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       59   out of    188   31.38%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6821
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          157
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         F2        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         D1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         B3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         C1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         A3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         A2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         B2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         B1        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F15        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDZ        INPUT        K15        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         P2        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         N4        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         M5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         N6        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         N5        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         P5        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M3        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         M4        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         H2        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         M1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         L1        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         N1        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         N3        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         M2        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT         T5        LVCMOS33           8            NONE           OREG       
    Fog_ENY        OUTPUT         T4        LVCMOS33           8            NONE           OREG       
    Fog_ENZ        OUTPUT         T7        LVCMOS33           8            NONE           OREG       
    Fog_INT        OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         T6        LVCMOS33           8            N/A            OREG       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E1        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        G11        LVCMOS25           8            N/A            NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15025  |7349    |1502    |12484   |40      |0       |
|  AnyFog_dataX                      |AnyFog          |207    |80      |22      |171     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |55      |22      |50      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |208    |101     |22      |172     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |87     |63      |22      |51      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |204    |100     |22      |169     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |60      |22      |51      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2902   |650     |39      |2824    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |61     |35      |5       |53      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |208    |59      |5       |200     |0       |0       |
|    STADOP_com2                     |STADOP          |534    |117     |0       |529     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |68     |43      |14      |45      |0       |0       |
|    head_com2                       |uniheading      |271    |55      |5       |262     |0       |0       |
|    rmc_com2                        |Gprmc           |35     |30      |0       |29      |0       |0       |
|    uart_com2                       |Agrica          |1413   |285     |10      |1394    |0       |0       |
|  COM3                              |COM3_Control    |274    |129     |19      |236     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |61     |35      |5       |52      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |60     |39      |14      |38      |0       |0       |
|    rmc_com3                        |Gprmc           |153    |55      |0       |146     |0       |0       |
|  DATA                              |Data_Processing |8809   |4464    |1122    |7036    |0       |0       |
|    DIV_Dtemp                       |Divider         |787    |308     |84      |663     |0       |0       |
|    DIV_Utemp                       |Divider         |602    |297     |84      |476     |0       |0       |
|    DIV_accX                        |Divider         |617    |290     |84      |495     |0       |0       |
|    DIV_accY                        |Divider         |598    |359     |102     |443     |0       |0       |
|    DIV_accZ                        |Divider         |689    |374     |132     |482     |0       |0       |
|    DIV_rateX                       |Divider         |673    |369     |132     |469     |0       |0       |
|    DIV_rateY                       |Divider         |586    |385     |132     |382     |0       |0       |
|    DIV_rateZ                       |Divider         |606    |388     |132     |399     |0       |0       |
|    genclk                          |genclk          |264    |165     |89      |104     |0       |0       |
|  FMC                               |FMC_Ctrl        |495    |445     |43      |368     |0       |0       |
|  IIC                               |I2C_master      |277    |246     |11      |255     |0       |0       |
|  IMU_CTRL                          |SCHA634         |928    |654     |61      |744     |0       |0       |
|    CtrlData                        |CtrlData        |465    |410     |47      |329     |0       |0       |
|      usms                          |Time_1ms        |29     |23      |5       |21      |0       |0       |
|    SPIM                            |SPI_SCHA634     |463    |244     |14      |415     |0       |0       |
|  POWER                             |POWER_EN        |97     |57      |38      |38      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |613    |419     |103     |417     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |613    |419     |103     |417     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |265    |182     |0       |248     |0       |0       |
|        reg_inst                    |register        |263    |180     |0       |246     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |348    |237     |103     |169     |0       |0       |
|        bus_inst                    |bus_top         |133    |87      |46      |52      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |29     |19      |10      |13      |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |51     |33      |18      |19      |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |51     |33      |18      |18      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |139    |107     |29      |87      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13550  
    #2          2       3411   
    #3          3        685   
    #4          4        306   
    #5        5-10       966   
    #6        11-50      436   
    #7       51-100       8    
    #8       101-500      3    
    #9        >500        2    
  Average     2.14             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  1.891654s wall, 3.296875s user + 0.000000s system = 3.296875s CPU (174.3%)

RUN-1004 : used memory is 1057 MB, reserved memory is 1053 MB, peak memory is 1128 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67844, tnet num: 19429, tinst num: 8140, tnode num: 92026, tedge num: 111909.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.595395s wall, 1.609375s user + 0.000000s system = 1.609375s CPU (100.9%)

RUN-1004 : used memory is 1059 MB, reserved memory is 1055 MB, peak memory is 1128 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19429 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.238071s wall, 1.218750s user + 0.015625s system = 1.234375s CPU (99.7%)

RUN-1004 : used memory is 1064 MB, reserved memory is 1060 MB, peak memory is 1128 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 19285304e968e394d67a8f065444a1f66c49f77adeb419c41e6b7764d8228547 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8140
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19431, pip num: 148205
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 361
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3221 valid insts, and 414816 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011111000000101011000101
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  10.465063s wall, 104.265625s user + 0.156250s system = 104.421875s CPU (997.8%)

RUN-1004 : used memory is 1190 MB, reserved memory is 1175 MB, peak memory is 1305 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250821_135230.log"

============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Aug 21 10:15:10 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.818684s wall, 1.484375s user + 4.328125s system = 5.812500s CPU (99.9%)

RUN-1004 : used memory is 80 MB, reserved memory is 42 MB, peak memory is 90 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.910199s wall, 1.859375s user + 0.046875s system = 1.906250s CPU (99.8%)

RUN-1004 : used memory is 302 MB, reserved memory is 270 MB, peak memory is 305 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 27 trigger nets, 27 data nets.
KIT-1004 : Chipwatcher code = 1011100110110011
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=27,BUS_CTRL_NUM=74,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=96) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=96) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=27,BUS_CTRL_NUM=74,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=27,BUS_CTRL_NUM=74,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=27,BUS_CTRL_NUM=74,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=96)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=96)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=27,BUS_CTRL_NUM=74,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=27,BUS_CTRL_NUM=74,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22556/21 useful/useless nets, 19438/13 useful/useless insts
SYN-1016 : Merged 30 instances.
SYN-1032 : 22283/22 useful/useless nets, 19788/18 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 2 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 2 mux instances.
SYN-1015 : Optimize round 1, 321 better
SYN-1014 : Optimize round 2
SYN-1032 : 22041/30 useful/useless nets, 19546/32 useful/useless insts
SYN-1015 : Optimize round 2, 64 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.647858s wall, 2.609375s user + 0.046875s system = 2.656250s CPU (100.3%)

RUN-1004 : used memory is 326 MB, reserved memory is 294 MB, peak memory is 328 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22077/221 useful/useless nets, 19605/32 useful/useless insts
SYN-1016 : Merged 33 instances.
SYN-2571 : Optimize after map_dsp, round 1, 286 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 24 instances.
SYN-2501 : Optimize round 1, 50 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 14 macro adder
SYN-1019 : Optimized 9 mux instances.
SYN-1016 : Merged 10 instances.
SYN-1032 : 22454/5 useful/useless nets, 19982/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81557, tnet num: 22454, tinst num: 19981, tnode num: 114356, tedge num: 127539.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.236019s wall, 1.234375s user + 0.000000s system = 1.234375s CPU (99.9%)

RUN-1004 : used memory is 465 MB, reserved memory is 433 MB, peak memory is 465 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22454 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 188 (3.61), #lev = 7 (1.95)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 188 (3.61), #lev = 7 (1.95)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 462 instances into 188 LUTs, name keeping = 75%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 332 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 108 adder to BLE ...
SYN-4008 : Packed 108 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.656509s wall, 4.625000s user + 0.031250s system = 4.656250s CPU (100.0%)

RUN-1004 : used memory is 350 MB, reserved memory is 315 MB, peak memory is 573 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.651445s wall, 7.531250s user + 0.125000s system = 7.656250s CPU (100.1%)

RUN-1004 : used memory is 351 MB, reserved memory is 315 MB, peak memory is 573 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (210 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19270 instances
RUN-0007 : 5563 luts, 12129 seqs, 973 mslices, 515 lslices, 59 pads, 26 brams, 0 dsps
RUN-1001 : There are total 21750 nets
RUN-1001 : 16373 nets have 2 pins
RUN-1001 : 4201 nets have [3 - 5] pins
RUN-1001 : 811 nets have [6 - 10] pins
RUN-1001 : 237 nets have [11 - 20] pins
RUN-1001 : 109 nets have [21 - 99] pins
RUN-1001 : 19 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4788     
RUN-1001 :   No   |  No   |  Yes  |     690     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     382     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19268 instances, 5563 luts, 12129 seqs, 1488 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80035, tnet num: 21748, tinst num: 19268, tnode num: 112550, tedge num: 125875.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.283904s wall, 1.281250s user + 0.000000s system = 1.281250s CPU (99.8%)

RUN-1004 : used memory is 524 MB, reserved memory is 495 MB, peak memory is 573 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21748 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.251151s wall, 2.234375s user + 0.015625s system = 2.250000s CPU (99.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.57437e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19268.
PHY-3001 : Level 1 #clusters 2149.
PHY-3001 : End clustering;  0.182302s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (171.4%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 856929, overlap = 614.938
PHY-3002 : Step(2): len = 786814, overlap = 662.688
PHY-3002 : Step(3): len = 509334, overlap = 842.531
PHY-3002 : Step(4): len = 438147, overlap = 907
PHY-3002 : Step(5): len = 351766, overlap = 981.906
PHY-3002 : Step(6): len = 314406, overlap = 1053.41
PHY-3002 : Step(7): len = 271084, overlap = 1122.53
PHY-3002 : Step(8): len = 243248, overlap = 1194.94
PHY-3002 : Step(9): len = 216169, overlap = 1268.69
PHY-3002 : Step(10): len = 198284, overlap = 1308.31
PHY-3002 : Step(11): len = 184450, overlap = 1351.94
PHY-3002 : Step(12): len = 165939, overlap = 1375.62
PHY-3002 : Step(13): len = 154034, overlap = 1393.19
PHY-3002 : Step(14): len = 142490, overlap = 1418.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.12768e-06
PHY-3002 : Step(15): len = 143141, overlap = 1413.53
PHY-3002 : Step(16): len = 183403, overlap = 1318.84
PHY-3002 : Step(17): len = 196679, overlap = 1249.31
PHY-3002 : Step(18): len = 195737, overlap = 1168.72
PHY-3002 : Step(19): len = 194283, overlap = 1144.69
PHY-3002 : Step(20): len = 187382, overlap = 1100.28
PHY-3002 : Step(21): len = 184558, overlap = 1072.59
PHY-3002 : Step(22): len = 179021, overlap = 1054.44
PHY-3002 : Step(23): len = 175471, overlap = 1040.44
PHY-3002 : Step(24): len = 171941, overlap = 1049.72
PHY-3002 : Step(25): len = 170458, overlap = 1046.16
PHY-3002 : Step(26): len = 168882, overlap = 1031.34
PHY-3002 : Step(27): len = 168508, overlap = 1035.75
PHY-3002 : Step(28): len = 166701, overlap = 1019.28
PHY-3002 : Step(29): len = 167227, overlap = 1021.16
PHY-3002 : Step(30): len = 167396, overlap = 1013
PHY-3002 : Step(31): len = 165839, overlap = 1010.44
PHY-3002 : Step(32): len = 164468, overlap = 1040.78
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.25536e-06
PHY-3002 : Step(33): len = 172856, overlap = 1001.78
PHY-3002 : Step(34): len = 186911, overlap = 958.688
PHY-3002 : Step(35): len = 191463, overlap = 930.219
PHY-3002 : Step(36): len = 193024, overlap = 907.031
PHY-3002 : Step(37): len = 192691, overlap = 903.656
PHY-3002 : Step(38): len = 192781, overlap = 911.75
PHY-3002 : Step(39): len = 190907, overlap = 923.25
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.51072e-06
PHY-3002 : Step(40): len = 202824, overlap = 866.875
PHY-3002 : Step(41): len = 218369, overlap = 802.156
PHY-3002 : Step(42): len = 223125, overlap = 757.938
PHY-3002 : Step(43): len = 223732, overlap = 734.906
PHY-3002 : Step(44): len = 223592, overlap = 730
PHY-3002 : Step(45): len = 223292, overlap = 736.281
PHY-3002 : Step(46): len = 221907, overlap = 727.188
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 9.02145e-06
PHY-3002 : Step(47): len = 233000, overlap = 727.656
PHY-3002 : Step(48): len = 247796, overlap = 701.844
PHY-3002 : Step(49): len = 253083, overlap = 654.312
PHY-3002 : Step(50): len = 255478, overlap = 643.188
PHY-3002 : Step(51): len = 255301, overlap = 619.312
PHY-3002 : Step(52): len = 253493, overlap = 613.5
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.80429e-05
PHY-3002 : Step(53): len = 264545, overlap = 600.719
PHY-3002 : Step(54): len = 275194, overlap = 514.219
PHY-3002 : Step(55): len = 280652, overlap = 433
PHY-3002 : Step(56): len = 283893, overlap = 406.031
PHY-3002 : Step(57): len = 283586, overlap = 389.5
PHY-3002 : Step(58): len = 281837, overlap = 374.25
PHY-3002 : Step(59): len = 279512, overlap = 384.469
PHY-3002 : Step(60): len = 278339, overlap = 385.469
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.60858e-05
PHY-3002 : Step(61): len = 288485, overlap = 349.812
PHY-3002 : Step(62): len = 299950, overlap = 365.562
PHY-3002 : Step(63): len = 303277, overlap = 376.875
PHY-3002 : Step(64): len = 304394, overlap = 362.188
PHY-3002 : Step(65): len = 303483, overlap = 358.781
PHY-3002 : Step(66): len = 302431, overlap = 350.125
PHY-3002 : Step(67): len = 300568, overlap = 348.688
PHY-3002 : Step(68): len = 300445, overlap = 308.75
PHY-3002 : Step(69): len = 299035, overlap = 310.875
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 7.21716e-05
PHY-3002 : Step(70): len = 306670, overlap = 287.406
PHY-3002 : Step(71): len = 314648, overlap = 260.25
PHY-3002 : Step(72): len = 318908, overlap = 250.656
PHY-3002 : Step(73): len = 319668, overlap = 246.375
PHY-3002 : Step(74): len = 318042, overlap = 251.344
PHY-3002 : Step(75): len = 316405, overlap = 265.375
PHY-3002 : Step(76): len = 314856, overlap = 271.062
PHY-3002 : Step(77): len = 315285, overlap = 266.406
PHY-3002 : Step(78): len = 314044, overlap = 272.188
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000144343
PHY-3002 : Step(79): len = 317233, overlap = 277.625
PHY-3002 : Step(80): len = 322014, overlap = 255.75
PHY-3002 : Step(81): len = 323677, overlap = 251.344
PHY-3002 : Step(82): len = 324945, overlap = 247.156
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000285272
PHY-3002 : Step(83): len = 326524, overlap = 222.062
PHY-3002 : Step(84): len = 330036, overlap = 199.688
PHY-3002 : Step(85): len = 330588, overlap = 203.844
PHY-3002 : Step(86): len = 332234, overlap = 193.625
PHY-3002 : Step(87): len = 333266, overlap = 168.844
PHY-3002 : Step(88): len = 333890, overlap = 171.562
PHY-3002 : Step(89): len = 332493, overlap = 162.125
PHY-3002 : Step(90): len = 332762, overlap = 140.969
PHY-3002 : Step(91): len = 332923, overlap = 145.094
PHY-3002 : Step(92): len = 333294, overlap = 153.281
PHY-3002 : Step(93): len = 332269, overlap = 153.094
PHY-3002 : Step(94): len = 332705, overlap = 163.25
PHY-3002 : Step(95): len = 332514, overlap = 167.625
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000570544
PHY-3002 : Step(96): len = 334232, overlap = 167.031
PHY-3002 : Step(97): len = 337788, overlap = 177.25
PHY-3002 : Step(98): len = 339356, overlap = 182.938
PHY-3001 : :::11::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(99): len = 339490, overlap = 181.5
PHY-3002 : Step(100): len = 340401, overlap = 181.812
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.012932s wall, 0.031250s user + 0.031250s system = 0.062500s CPU (483.3%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21750.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 441904, over cnt = 1212(3%), over = 5452, worst = 39
PHY-1001 : End global iterations;  0.923736s wall, 1.234375s user + 0.000000s system = 1.234375s CPU (133.6%)

PHY-1001 : Congestion index: top1 = 73.90, top5 = 52.77, top10 = 43.54, top15 = 37.93.
PHY-3001 : End congestion estimation;  1.179659s wall, 1.500000s user + 0.000000s system = 1.500000s CPU (127.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21748 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.979828s wall, 0.953125s user + 0.031250s system = 0.984375s CPU (100.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000125124
PHY-3002 : Step(101): len = 382170, overlap = 149.688
PHY-3002 : Step(102): len = 391968, overlap = 126.531
PHY-3002 : Step(103): len = 395822, overlap = 115.438
PHY-3002 : Step(104): len = 397886, overlap = 117.156
PHY-3002 : Step(105): len = 402774, overlap = 115.375
PHY-3002 : Step(106): len = 409295, overlap = 112.062
PHY-3002 : Step(107): len = 414043, overlap = 109.844
PHY-3002 : Step(108): len = 417890, overlap = 117
PHY-3002 : Step(109): len = 418896, overlap = 119.562
PHY-3002 : Step(110): len = 422198, overlap = 116.281
PHY-3002 : Step(111): len = 424351, overlap = 111.156
PHY-3002 : Step(112): len = 424107, overlap = 109.688
PHY-3002 : Step(113): len = 425271, overlap = 105.062
PHY-3002 : Step(114): len = 426958, overlap = 98.125
PHY-3002 : Step(115): len = 427193, overlap = 102.062
PHY-3002 : Step(116): len = 429000, overlap = 101.719
PHY-3002 : Step(117): len = 428189, overlap = 100.719
PHY-3002 : Step(118): len = 427135, overlap = 100.875
PHY-3002 : Step(119): len = 427830, overlap = 101.594
PHY-3002 : Step(120): len = 427689, overlap = 99.3125
PHY-3002 : Step(121): len = 427196, overlap = 103.25
PHY-3002 : Step(122): len = 427895, overlap = 104.562
PHY-3002 : Step(123): len = 427023, overlap = 105.562
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(124): len = 428000, overlap = 103.844
PHY-3002 : Step(125): len = 429609, overlap = 102.125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 73/21750.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 496312, over cnt = 2127(6%), over = 9177, worst = 40
PHY-1001 : End global iterations;  1.188377s wall, 1.875000s user + 0.078125s system = 1.953125s CPU (164.4%)

PHY-1001 : Congestion index: top1 = 72.69, top5 = 55.76, top10 = 47.83, top15 = 42.99.
PHY-3001 : End congestion estimation;  1.490342s wall, 2.187500s user + 0.078125s system = 2.265625s CPU (152.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21748 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.017649s wall, 1.000000s user + 0.015625s system = 1.015625s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.12766e-05
PHY-3002 : Step(126): len = 434509, overlap = 373.688
PHY-3002 : Step(127): len = 443106, overlap = 315.531
PHY-3002 : Step(128): len = 439682, overlap = 299.688
PHY-3002 : Step(129): len = 438549, overlap = 285.5
PHY-3002 : Step(130): len = 438710, overlap = 269.094
PHY-3002 : Step(131): len = 438701, overlap = 252.25
PHY-3002 : Step(132): len = 437085, overlap = 241.406
PHY-3002 : Step(133): len = 436457, overlap = 240.625
PHY-3002 : Step(134): len = 437248, overlap = 239.625
PHY-3002 : Step(135): len = 435607, overlap = 240.812
PHY-3002 : Step(136): len = 433189, overlap = 247.781
PHY-3002 : Step(137): len = 432558, overlap = 248.031
PHY-3002 : Step(138): len = 431868, overlap = 250.625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000182553
PHY-3002 : Step(139): len = 432000, overlap = 239.25
PHY-3002 : Step(140): len = 433867, overlap = 235.094
PHY-3002 : Step(141): len = 435266, overlap = 230.625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000365107
PHY-3002 : Step(142): len = 437537, overlap = 224.969
PHY-3002 : Step(143): len = 443792, overlap = 213.812
PHY-3002 : Step(144): len = 449256, overlap = 203.531
PHY-3002 : Step(145): len = 451557, overlap = 195.156
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000730213
PHY-3002 : Step(146): len = 452938, overlap = 194.719
PHY-3002 : Step(147): len = 458107, overlap = 171.531
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80035, tnet num: 21748, tinst num: 19268, tnode num: 112550, tedge num: 125875.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.600210s wall, 1.546875s user + 0.046875s system = 1.593750s CPU (99.6%)

RUN-1004 : used memory is 563 MB, reserved memory is 537 MB, peak memory is 696 MB
OPT-1001 : Total overflow 514.62 peak overflow 5.59
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 566/21750.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 537728, over cnt = 2495(7%), over = 8547, worst = 25
PHY-1001 : End global iterations;  1.420973s wall, 2.125000s user + 0.000000s system = 2.125000s CPU (149.5%)

PHY-1001 : Congestion index: top1 = 58.90, top5 = 48.95, top10 = 43.57, top15 = 40.31.
PHY-1001 : End incremental global routing;  1.689120s wall, 2.406250s user + 0.000000s system = 2.406250s CPU (142.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21748 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.081449s wall, 1.031250s user + 0.046875s system = 1.078125s CPU (99.7%)

OPT-1001 : 15 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19191 has valid locations, 208 needs to be replaced
PHY-3001 : design contains 19461 instances, 5637 luts, 12248 seqs, 1488 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 472215
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17173/21943.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 548736, over cnt = 2533(7%), over = 8637, worst = 24
PHY-1001 : End global iterations;  0.189851s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (131.7%)

PHY-1001 : Congestion index: top1 = 59.48, top5 = 49.38, top10 = 44.03, top15 = 40.72.
PHY-3001 : End congestion estimation;  0.452724s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (113.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80656, tnet num: 21941, tinst num: 19461, tnode num: 113447, tedge num: 126731.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.611316s wall, 1.593750s user + 0.031250s system = 1.625000s CPU (100.8%)

RUN-1004 : used memory is 607 MB, reserved memory is 601 MB, peak memory is 697 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21941 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.718032s wall, 2.687500s user + 0.046875s system = 2.734375s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(148): len = 472455, overlap = 5.4375
PHY-3002 : Step(149): len = 473532, overlap = 5.5
PHY-3002 : Step(150): len = 474564, overlap = 5.5
PHY-3002 : Step(151): len = 475279, overlap = 5.375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(152): len = 475091, overlap = 5.4375
PHY-3002 : Step(153): len = 475069, overlap = 5.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(154): len = 475332, overlap = 5.4375
PHY-3002 : Step(155): len = 475933, overlap = 5.375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17183/21943.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 549928, over cnt = 2531(7%), over = 8629, worst = 24
PHY-1001 : End global iterations;  0.221509s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (98.8%)

PHY-1001 : Congestion index: top1 = 59.68, top5 = 49.43, top10 = 43.94, top15 = 40.64.
PHY-3001 : End congestion estimation;  0.484491s wall, 0.500000s user + 0.000000s system = 0.500000s CPU (103.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21941 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.071063s wall, 1.062500s user + 0.000000s system = 1.062500s CPU (99.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00149298
PHY-3002 : Step(156): len = 475582, overlap = 173.375
PHY-3002 : Step(157): len = 475634, overlap = 173.094
PHY-3002 : Step(158): len = 475861, overlap = 173.062
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00298596
PHY-3002 : Step(159): len = 475978, overlap = 173.156
PHY-3002 : Step(160): len = 476164, overlap = 173.094
PHY-3001 : Final: Len = 476164, Over = 173.094
PHY-3001 : End incremental placement;  6.045764s wall, 6.453125s user + 0.421875s system = 6.875000s CPU (113.7%)

OPT-1001 : Total overflow 518.50 peak overflow 5.59
OPT-1001 : End high-fanout net optimization;  9.370144s wall, 10.609375s user + 0.468750s system = 11.078125s CPU (118.2%)

OPT-1001 : Current memory(MB): used = 699, reserve = 679, peak = 716.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17189/21943.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 551672, over cnt = 2473(7%), over = 8104, worst = 24
PHY-1002 : len = 597880, over cnt = 1727(4%), over = 4041, worst = 20
PHY-1002 : len = 630032, over cnt = 745(2%), over = 1621, worst = 19
PHY-1002 : len = 654680, over cnt = 177(0%), over = 294, worst = 8
PHY-1002 : len = 659272, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.480618s wall, 1.953125s user + 0.000000s system = 1.953125s CPU (131.9%)

PHY-1001 : Congestion index: top1 = 51.66, top5 = 45.18, top10 = 41.66, top15 = 39.37.
OPT-1001 : End congestion update;  1.776906s wall, 2.265625s user + 0.000000s system = 2.265625s CPU (127.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 21941 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.988079s wall, 0.984375s user + 0.000000s system = 0.984375s CPU (99.6%)

OPT-0007 : Start: WNS 4237 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.771323s wall, 3.250000s user + 0.000000s system = 3.250000s CPU (117.3%)

OPT-1001 : Current memory(MB): used = 699, reserve = 678, peak = 716.
OPT-1001 : End physical optimization;  14.078053s wall, 15.875000s user + 0.562500s system = 16.437500s CPU (116.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5637 LUT to BLE ...
SYN-4008 : Packed 5637 LUT and 2731 SEQ to BLE.
SYN-4003 : Packing 9517 remaining SEQ's ...
SYN-4005 : Packed 3269 SEQ with LUT/SLICE
SYN-4006 : 157 single LUT's are left
SYN-4006 : 6248 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11885/13727 primitive instances ...
PHY-3001 : End packing;  3.344918s wall, 3.343750s user + 0.000000s system = 3.343750s CPU (100.0%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8069 instances
RUN-1001 : 3989 mslices, 3990 lslices, 59 pads, 26 brams, 0 dsps
RUN-1001 : There are total 19266 nets
RUN-1001 : 13555 nets have 2 pins
RUN-1001 : 4335 nets have [3 - 5] pins
RUN-1001 : 859 nets have [6 - 10] pins
RUN-1001 : 376 nets have [11 - 20] pins
RUN-1001 : 132 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8067 instances, 7979 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 493903, Over = 373.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7838/19266.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 625536, over cnt = 1557(4%), over = 2482, worst = 9
PHY-1002 : len = 630464, over cnt = 1009(2%), over = 1448, worst = 9
PHY-1002 : len = 641624, over cnt = 480(1%), over = 627, worst = 5
PHY-1002 : len = 650408, over cnt = 112(0%), over = 133, worst = 4
PHY-1002 : len = 653184, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.307257s wall, 2.046875s user + 0.031250s system = 2.078125s CPU (159.0%)

PHY-1001 : Congestion index: top1 = 51.83, top5 = 44.84, top10 = 41.25, top15 = 38.89.
PHY-3001 : End congestion estimation;  1.709378s wall, 2.468750s user + 0.031250s system = 2.500000s CPU (146.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66903, tnet num: 19264, tinst num: 8067, tnode num: 90773, tedge num: 110379.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.170881s wall, 2.156250s user + 0.015625s system = 2.171875s CPU (100.0%)

RUN-1004 : used memory is 603 MB, reserved memory is 596 MB, peak memory is 716 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19264 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.320676s wall, 3.296875s user + 0.015625s system = 3.312500s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.1213e-05
PHY-3002 : Step(161): len = 497399, overlap = 360.75
PHY-3002 : Step(162): len = 495958, overlap = 365.75
PHY-3002 : Step(163): len = 496679, overlap = 370.25
PHY-3002 : Step(164): len = 498423, overlap = 383.75
PHY-3002 : Step(165): len = 497930, overlap = 390.5
PHY-3002 : Step(166): len = 498375, overlap = 386.5
PHY-3002 : Step(167): len = 495921, overlap = 384.25
PHY-3002 : Step(168): len = 493136, overlap = 378.75
PHY-3002 : Step(169): len = 491639, overlap = 384.5
PHY-3002 : Step(170): len = 490385, overlap = 384
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000102426
PHY-3002 : Step(171): len = 495343, overlap = 367.25
PHY-3002 : Step(172): len = 500498, overlap = 352
PHY-3002 : Step(173): len = 501309, overlap = 347.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000204852
PHY-3002 : Step(174): len = 511086, overlap = 334.75
PHY-3002 : Step(175): len = 523015, overlap = 320.75
PHY-3002 : Step(176): len = 520570, overlap = 316.5
PHY-3002 : Step(177): len = 518468, overlap = 303.5
PHY-3002 : Step(178): len = 518066, overlap = 302
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(179): len = 523925, overlap = 303.5
PHY-3002 : Step(180): len = 529827, overlap = 290.75
PHY-3002 : Step(181): len = 535191, overlap = 281.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.854236s wall, 0.984375s user + 0.984375s system = 1.968750s CPU (230.5%)

PHY-3001 : Trial Legalized: Len = 624966
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 521/19266.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 708248, over cnt = 2344(6%), over = 3726, worst = 7
PHY-1002 : len = 723200, over cnt = 1358(3%), over = 1861, worst = 7
PHY-1002 : len = 742632, over cnt = 317(0%), over = 407, worst = 5
PHY-1002 : len = 747264, over cnt = 114(0%), over = 126, worst = 3
PHY-1002 : len = 748840, over cnt = 65(0%), over = 72, worst = 3
PHY-1001 : End global iterations;  1.946330s wall, 3.531250s user + 0.000000s system = 3.531250s CPU (181.4%)

PHY-1001 : Congestion index: top1 = 50.15, top5 = 44.89, top10 = 41.85, top15 = 39.87.
PHY-3001 : End congestion estimation;  2.358541s wall, 3.937500s user + 0.000000s system = 3.937500s CPU (166.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19264 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.946575s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (99.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000167024
PHY-3002 : Step(182): len = 584552, overlap = 78.25
PHY-3002 : Step(183): len = 566490, overlap = 117.75
PHY-3002 : Step(184): len = 554189, overlap = 173.25
PHY-3002 : Step(185): len = 547415, overlap = 201.5
PHY-3002 : Step(186): len = 544519, overlap = 226
PHY-3002 : Step(187): len = 542352, overlap = 237.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000334047
PHY-3002 : Step(188): len = 547003, overlap = 224.75
PHY-3002 : Step(189): len = 550822, overlap = 227
PHY-3002 : Step(190): len = 550016, overlap = 232.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(191): len = 553597, overlap = 227.5
PHY-3002 : Step(192): len = 559881, overlap = 222.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.034028s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (137.8%)

PHY-3001 : Legalized: Len = 595630, Over = 0
PHY-3001 : Spreading special nets. 39 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.087763s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (89.0%)

PHY-3001 : 62 instances has been re-located, deltaX = 28, deltaY = 33, maxDist = 2.
PHY-3001 : Final: Len = 596780, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66903, tnet num: 19264, tinst num: 8067, tnode num: 90773, tedge num: 110379.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.089136s wall, 2.046875s user + 0.046875s system = 2.093750s CPU (100.2%)

RUN-1004 : used memory is 611 MB, reserved memory is 614 MB, peak memory is 716 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 4329/19266.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 692104, over cnt = 2130(6%), over = 3299, worst = 7
PHY-1002 : len = 704096, over cnt = 1229(3%), over = 1613, worst = 6
PHY-1002 : len = 719144, over cnt = 377(1%), over = 489, worst = 6
PHY-1002 : len = 724528, over cnt = 137(0%), over = 165, worst = 4
PHY-1002 : len = 727944, over cnt = 1(0%), over = 1, worst = 1
PHY-1001 : End global iterations;  1.719357s wall, 3.031250s user + 0.015625s system = 3.046875s CPU (177.2%)

PHY-1001 : Congestion index: top1 = 48.02, top5 = 42.82, top10 = 40.05, top15 = 38.38.
PHY-1001 : End incremental global routing;  2.055683s wall, 3.359375s user + 0.015625s system = 3.375000s CPU (164.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19264 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.970329s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (99.8%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8004 has valid locations, 7 needs to be replaced
PHY-3001 : design contains 8073 instances, 7985 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 599339
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17268/19277.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 731528, over cnt = 13(0%), over = 18, worst = 5
PHY-1002 : len = 731512, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 731544, over cnt = 10(0%), over = 10, worst = 1
PHY-1002 : len = 731656, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 731800, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.700410s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (104.8%)

PHY-1001 : Congestion index: top1 = 48.06, top5 = 42.90, top10 = 40.14, top15 = 38.49.
PHY-3001 : End congestion estimation;  1.021305s wall, 1.031250s user + 0.015625s system = 1.046875s CPU (102.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66956, tnet num: 19275, tinst num: 8073, tnode num: 90838, tedge num: 110461.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.961741s wall, 1.953125s user + 0.015625s system = 1.968750s CPU (100.4%)

RUN-1004 : used memory is 632 MB, reserved memory is 616 MB, peak memory is 716 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19275 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.957983s wall, 2.937500s user + 0.031250s system = 2.968750s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(193): len = 599369, overlap = 0.25
PHY-3002 : Step(194): len = 599034, overlap = 0
PHY-3002 : Step(195): len = 599064, overlap = 0
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17266/19277.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 730072, over cnt = 22(0%), over = 29, worst = 4
PHY-1002 : len = 730152, over cnt = 20(0%), over = 23, worst = 3
PHY-1002 : len = 730296, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 730408, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 730440, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.705070s wall, 0.734375s user + 0.015625s system = 0.750000s CPU (106.4%)

PHY-1001 : Congestion index: top1 = 48.02, top5 = 42.87, top10 = 40.15, top15 = 38.49.
PHY-3001 : End congestion estimation;  1.019416s wall, 1.046875s user + 0.015625s system = 1.062500s CPU (104.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19275 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.950242s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00128147
PHY-3002 : Step(196): len = 598918, overlap = 1
PHY-3002 : Step(197): len = 598830, overlap = 0.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006775s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (230.6%)

PHY-3001 : Legalized: Len = 598832, Over = 0
PHY-3001 : End spreading;  0.072290s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (108.1%)

PHY-3001 : Final: Len = 598832, Over = 0
PHY-3001 : End incremental placement;  6.586064s wall, 6.703125s user + 0.109375s system = 6.812500s CPU (103.4%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.616111s wall, 12.203125s user + 0.125000s system = 12.328125s CPU (116.1%)

OPT-1001 : Current memory(MB): used = 708, reserve = 695, peak = 716.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17268/19277.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 730184, over cnt = 6(0%), over = 12, worst = 4
PHY-1002 : len = 730216, over cnt = 4(0%), over = 7, worst = 4
PHY-1002 : len = 730304, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 730320, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.535169s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (102.2%)

PHY-1001 : Congestion index: top1 = 48.02, top5 = 42.86, top10 = 40.11, top15 = 38.46.
OPT-1001 : End congestion update;  0.853684s wall, 0.875000s user + 0.000000s system = 0.875000s CPU (102.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19275 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.803671s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.2%)

OPT-0007 : Start: WNS 4232 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.662804s wall, 1.687500s user + 0.000000s system = 1.687500s CPU (101.5%)

OPT-1001 : Current memory(MB): used = 708, reserve = 695, peak = 716.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19275 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.805205s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17274/19277.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 730320, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.127078s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (98.4%)

PHY-1001 : Congestion index: top1 = 48.02, top5 = 42.86, top10 = 40.11, top15 = 38.46.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19275 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.812685s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (100.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4232 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 47.620690
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4232ps with logic level 8 
RUN-1001 :       #2 path slack 4257ps with logic level 8 
OPT-1001 : End physical optimization;  16.700320s wall, 18.437500s user + 0.171875s system = 18.609375s CPU (111.4%)

RUN-1003 : finish command "place" in  71.392640s wall, 123.609375s user + 7.343750s system = 130.953125s CPU (183.4%)

RUN-1004 : used memory is 593 MB, reserved memory is 569 MB, peak memory is 716 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.662915s wall, 2.890625s user + 0.000000s system = 2.890625s CPU (173.8%)

RUN-1004 : used memory is 593 MB, reserved memory is 570 MB, peak memory is 716 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8075 instances
RUN-1001 : 3989 mslices, 3996 lslices, 59 pads, 26 brams, 0 dsps
RUN-1001 : There are total 19277 nets
RUN-1001 : 13558 nets have 2 pins
RUN-1001 : 4334 nets have [3 - 5] pins
RUN-1001 : 866 nets have [6 - 10] pins
RUN-1001 : 378 nets have [11 - 20] pins
RUN-1001 : 132 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66956, tnet num: 19275, tinst num: 8073, tnode num: 90838, tedge num: 110461.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.714656s wall, 1.703125s user + 0.000000s system = 1.703125s CPU (99.3%)

RUN-1004 : used memory is 605 MB, reserved memory is 603 MB, peak memory is 716 MB
PHY-1001 : 3989 mslices, 3996 lslices, 59 pads, 26 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19275 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 672464, over cnt = 2291(6%), over = 3719, worst = 7
PHY-1002 : len = 686416, over cnt = 1474(4%), over = 2076, worst = 6
PHY-1002 : len = 698712, over cnt = 862(2%), over = 1167, worst = 6
PHY-1002 : len = 717392, over cnt = 23(0%), over = 27, worst = 2
PHY-1002 : len = 718408, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.817447s wall, 3.109375s user + 0.000000s system = 3.109375s CPU (171.1%)

PHY-1001 : Congestion index: top1 = 48.06, top5 = 43.07, top10 = 40.21, top15 = 38.44.
PHY-1001 : End global routing;  2.173665s wall, 3.453125s user + 0.000000s system = 3.453125s CPU (158.9%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 696, reserve = 685, peak = 716.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 964, reserve = 951, peak = 964.
PHY-1001 : End build detailed router design. 4.773333s wall, 4.718750s user + 0.046875s system = 4.765625s CPU (99.8%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 190928, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.941819s wall, 0.921875s user + 0.031250s system = 0.953125s CPU (101.2%)

PHY-1001 : Current memory(MB): used = 1001, reserve = 988, peak = 1001.
PHY-1001 : End phase 1; 0.949185s wall, 0.921875s user + 0.031250s system = 0.953125s CPU (100.4%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.66498e+06, over cnt = 1148(0%), over = 1149, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1019, reserve = 1005, peak = 1019.
PHY-1001 : End initial routed; 14.700099s wall, 43.937500s user + 0.437500s system = 44.375000s CPU (301.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18017(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.776   |   0.000   |   0   
RUN-1001 :   Hold   |   0.112   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.595524s wall, 3.593750s user + 0.000000s system = 3.593750s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1028, reserve = 1014, peak = 1028.
PHY-1001 : End phase 2; 18.295792s wall, 47.531250s user + 0.437500s system = 47.968750s CPU (262.2%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.66498e+06, over cnt = 1148(0%), over = 1149, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.261887s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (95.5%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.65393e+06, over cnt = 401(0%), over = 402, worst = 2, crit = 0
PHY-1001 : End DR Iter 1; 0.751811s wall, 1.250000s user + 0.015625s system = 1.265625s CPU (168.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.65416e+06, over cnt = 97(0%), over = 97, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.324038s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (159.1%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.655e+06, over cnt = 8(0%), over = 8, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.256802s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (103.4%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.65521e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.174627s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (98.4%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.65521e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.161630s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (106.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18017(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.776   |   0.000   |   0   
RUN-1001 :   Hold   |   0.112   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.637817s wall, 3.640625s user + 0.000000s system = 3.640625s CPU (100.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 292 feed throughs used by 253 nets
PHY-1001 : End commit to database; 2.342069s wall, 2.328125s user + 0.015625s system = 2.343750s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1117, reserve = 1106, peak = 1117.
PHY-1001 : End phase 3; 8.465941s wall, 9.156250s user + 0.031250s system = 9.187500s CPU (108.5%)

PHY-1003 : Routed, final wirelength = 1.65521e+06
PHY-1001 : Current memory(MB): used = 1121, reserve = 1110, peak = 1121.
PHY-1001 : End export database. 0.061361s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.9%)

PHY-1001 : End detail routing;  32.982153s wall, 62.812500s user + 0.562500s system = 63.375000s CPU (192.1%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66956, tnet num: 19275, tinst num: 8073, tnode num: 90838, tedge num: 110461.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.780465s wall, 1.781250s user + 0.000000s system = 1.781250s CPU (100.0%)

RUN-1004 : used memory is 1053 MB, reserved memory is 1048 MB, peak memory is 1121 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  41.396940s wall, 72.515625s user + 0.562500s system = 73.078125s CPU (176.5%)

RUN-1004 : used memory is 1052 MB, reserved memory is 1049 MB, peak memory is 1121 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8769   out of  19600   44.74%
#reg                    12348   out of  19600   63.00%
#le                     14963
  #lut only              2615   out of  14963   17.48%
  #reg only              6194   out of  14963   41.40%
  #lut&reg               6154   out of  14963   41.13%
#dsp                        0   out of     29    0.00%
#bram                      26   out of     64   40.62%
  #bram9k                  24
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       59   out of    188   31.38%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6783
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          127
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         F2        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         D1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         B3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         C1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         A3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         A2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         B2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         B1        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F15        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDZ        INPUT        K15        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         P2        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         N4        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         M5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         N6        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         N5        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         P5        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M3        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         M4        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         H2        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         M1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         L1        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         N1        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         N3        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         M2        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT         T5        LVCMOS33           8            NONE           OREG       
    Fog_ENY        OUTPUT         T4        LVCMOS33           8            NONE           OREG       
    Fog_ENZ        OUTPUT         T7        LVCMOS33           8            NONE           OREG       
    Fog_INT        OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         T6        LVCMOS33           8            N/A            OREG       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E1        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        G11        LVCMOS25           8            N/A            NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |14963  |7281    |1488    |12392   |26      |0       |
|  AnyFog_dataX                      |AnyFog          |208    |70      |22      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |55      |22      |51      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |208    |76      |22      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |53      |22      |51      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |208    |79      |22      |172     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |54      |22      |50      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2906   |581     |39      |2828    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |61     |37      |5       |52      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |213    |79      |5       |204     |0       |0       |
|    STADOP_com2                     |STADOP          |537    |77      |0       |532     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |44      |14      |42      |0       |0       |
|    head_com2                       |uniheading      |256    |62      |5       |247     |0       |0       |
|    rmc_com2                        |Gprmc           |32     |32      |0       |26      |0       |0       |
|    uart_com2                       |Agrica          |1437   |231     |10      |1420    |0       |0       |
|  COM3                              |COM3_Control    |272    |169     |19      |237     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |64     |39      |5       |55      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |61     |39      |14      |39      |0       |0       |
|    rmc_com3                        |Gprmc           |147    |91      |0       |143     |0       |0       |
|  DATA                              |Data_Processing |8809   |4547    |1122    |7013    |0       |0       |
|    DIV_Dtemp                       |Divider         |720    |321     |84      |592     |0       |0       |
|    DIV_Utemp                       |Divider         |579    |313     |84      |453     |0       |0       |
|    DIV_accX                        |Divider         |580    |314     |84      |450     |0       |0       |
|    DIV_accY                        |Divider         |652    |314     |102     |495     |0       |0       |
|    DIV_accZ                        |Divider         |592    |403     |132     |384     |0       |0       |
|    DIV_rateX                       |Divider         |761    |357     |132     |557     |0       |0       |
|    DIV_rateY                       |Divider         |630    |347     |132     |424     |0       |0       |
|    DIV_rateZ                       |Divider         |611    |363     |132     |408     |0       |0       |
|    genclk                          |genclk          |275    |176     |89      |107     |0       |0       |
|  FMC                               |FMC_Ctrl        |502    |448     |43      |372     |0       |0       |
|  IIC                               |I2C_master      |313    |256     |11      |269     |0       |0       |
|  IMU_CTRL                          |SCHA634         |930    |700     |61      |731     |0       |0       |
|    CtrlData                        |CtrlData        |489    |435     |47      |332     |0       |0       |
|      usms                          |Time_1ms        |28     |23      |5       |20      |0       |0       |
|    SPIM                            |SPI_SCHA634     |441    |265     |14      |399     |0       |0       |
|  POWER                             |POWER_EN        |97     |50      |38      |38      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |498    |301     |89      |330     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |498    |301     |89      |330     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |204    |120     |0       |189     |0       |0       |
|        reg_inst                    |register        |203    |119     |0       |188     |0       |0       |
|        tap_inst                    |tap             |1      |1       |0       |1       |0       |0       |
|      trigger_inst                  |trigger         |294    |181     |89      |141     |0       |0       |
|        bus_inst                    |bus_top         |86     |58      |28      |36      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |26     |16      |10      |9       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |4      |4       |0       |4       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |3      |3       |0       |3       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |51     |33      |18      |18      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |126    |84      |29      |78      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13498  
    #2          2       3401   
    #3          3        628   
    #4          4        305   
    #5        5-10       924   
    #6        11-50      442   
    #7       51-100      10    
    #8       101-500      3    
    #9        >500        2    
  Average     2.12             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.248779s wall, 3.984375s user + 0.015625s system = 4.000000s CPU (177.9%)

RUN-1004 : used memory is 1053 MB, reserved memory is 1049 MB, peak memory is 1121 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66956, tnet num: 19275, tinst num: 8073, tnode num: 90838, tedge num: 110461.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.715928s wall, 1.718750s user + 0.000000s system = 1.718750s CPU (100.2%)

RUN-1004 : used memory is 1054 MB, reserved memory is 1050 MB, peak memory is 1121 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19275 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.527087s wall, 1.531250s user + 0.000000s system = 1.531250s CPU (100.3%)

RUN-1004 : used memory is 1059 MB, reserved memory is 1054 MB, peak memory is 1121 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 40c9047af6c60b8681e7bd8c3a1ad8946a1d4edd384cda8aef5d685a0a84c04c -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8073
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19277, pip num: 144145
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 292
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3220 valid insts, and 405150 bits set as '1'.
BIT-1004 : the usercode register value: 00000000010111011011100110110011
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.517110s wall, 123.843750s user + 0.140625s system = 123.984375s CPU (990.5%)

RUN-1004 : used memory is 1184 MB, reserved memory is 1168 MB, peak memory is 1298 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250821_101510.log"

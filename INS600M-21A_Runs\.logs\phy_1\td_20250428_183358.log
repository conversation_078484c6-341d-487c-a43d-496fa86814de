============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.5_SP3/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     51593
   Run Date =   Mon Apr 28 18:33:58 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.584157s wall, 1.500000s user + 0.093750s system = 1.593750s CPU (100.6%)

RUN-1004 : used memory is 285 MB, reserved memory is 257 MB, peak memory is 288 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 -duty_cycle 50.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 7 view nodes, 47 trigger nets, 47 data nets.
KIT-1004 : Chipwatcher code = 0001111110010110
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.5_SP3/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=144) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=144) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.5_SP3/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=144)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=144)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22885/32 useful/useless nets, 19661/17 useful/useless insts
SYN-1016 : Merged 36 instances.
SYN-1032 : 22510/22 useful/useless nets, 20121/18 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 4 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 4 mux instances.
SYN-1015 : Optimize round 1, 442 better
SYN-1014 : Optimize round 2
SYN-1032 : 22139/60 useful/useless nets, 19750/64 useful/useless insts
SYN-1015 : Optimize round 2, 128 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.164160s wall, 2.109375s user + 0.046875s system = 2.156250s CPU (99.6%)

RUN-1004 : used memory is 323 MB, reserved memory is 295 MB, peak memory is 325 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.5_SP3/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |    high    |      medium      |   *    
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 67 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22199/367 useful/useless nets, 19851/52 useful/useless insts
SYN-1016 : Merged 57 instances.
SYN-2571 : Optimize after map_dsp, round 1, 476 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 44 instances.
SYN-2501 : Optimize round 1, 90 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 18 macro adder
SYN-1019 : Optimized 17 mux instances.
SYN-1016 : Merged 190 instances.
SYN-1032 : 22682/5 useful/useless nets, 20334/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1002 : start command "get_pins config_inst.jtck"
RUN-1002 : start command "create_clock -name jtck -period 100 "
RUN-1102 : create_clock: clock name: jtck, type: 0, period: 100000, rise: 0, fall: 50000.
RUN-1002 : start command "get_clocks jtck"
RUN-1002 : start command "set_clock_uncertainty -hold 0.1 "
RUN-1002 : start command "get_clocks jtck"
RUN-1002 : start command "set_clock_groups -exclusive -group "
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 83764, tnet num: 22682, tinst num: 20333, tnode num: 117388, tedge num: 130593.
TMR-2508 : Levelizing timing graph completed, there are 51 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.084977s wall, 1.046875s user + 0.046875s system = 1.093750s CPU (100.8%)

RUN-1004 : used memory is 465 MB, reserved memory is 438 MB, peak memory is 465 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22682 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 308 (4.01), #lev = 8 (2.75)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 276 (3.98), #lev = 8 (2.93)
SYN-3001 : Logic optimization runtime opt =   0.05 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 748 instances into 276 LUTs, name keeping = 40%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 450 DFF/LATCH to SEQ ...
SYN-4009 : Pack 4 carry chain into lslice
SYN-4007 : Packing 67 adder to BLE ...
SYN-4008 : Packed 67 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  5.363261s wall, 5.234375s user + 0.140625s system = 5.375000s CPU (100.2%)

RUN-1004 : used memory is 354 MB, reserved memory is 331 MB, peak memory is 582 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.815409s wall, 7.609375s user + 0.203125s system = 7.812500s CPU (100.0%)

RUN-1004 : used memory is 355 MB, reserved memory is 331 MB, peak memory is 582 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.5_SP3/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/rmc_com2/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/rmc_com2/GPRMC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/rmc_com2/GPRMC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/rmc_com2/GPRMC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/rmc_com2/GPRMC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/rmc_com2/GPRMC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/rmc_com2/GPRMC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/rmc_com2/GPRMC_data[0]
SYN-5055 WARNING: The kept net COM2/rx_err will be merged to another kept net COM2/rmc_com2/GPRMC_err
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sub_state_cnt0[3] will be merged to another kept net COM2/rmc_com2/GPRMC_sub_state_cnt0[2]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (306 clock/control pins, 0 other pins).
SYN-4027 : Net DATA/DIV_Dtemp/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net DATA/DIV_Dtemp/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19614 instances
RUN-0007 : 5719 luts, 12472 seqs, 864 mslices, 451 lslices, 61 pads, 42 brams, 0 dsps
RUN-1001 : There are total 21986 nets
RUN-1001 : 16211 nets have 2 pins
RUN-1001 : 4601 nets have [3 - 5] pins
RUN-1001 : 786 nets have [6 - 10] pins
RUN-1001 : 254 nets have [11 - 20] pins
RUN-1001 : 109 nets have [21 - 99] pins
RUN-1001 : 25 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4743     
RUN-1001 :   No   |  No   |  Yes  |     628     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    6531     
RUN-1001 :   Yes  |  No   |  Yes  |     470     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  106  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 114
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19612 instances, 5719 luts, 12472 seqs, 1315 slices, 262 macros(1315 instances: 864 mslices 451 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1131 pins
PHY-3001 : Huge net DATA/done_div with 1650 pins
PHY-0007 : Cell area utilization is 64%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 82134, tnet num: 21984, tinst num: 19612, tnode num: 115856, tedge num: 129102.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.129966s wall, 1.125000s user + 0.000000s system = 1.125000s CPU (99.6%)

RUN-1004 : used memory is 529 MB, reserved memory is 505 MB, peak memory is 582 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21984 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.956026s wall, 1.921875s user + 0.031250s system = 1.953125s CPU (99.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.31643e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19612.
PHY-3001 : Level 1 #clusters 1989.
PHY-3001 : End clustering;  0.148542s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (168.3%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 64%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 829853, overlap = 665.688
PHY-3002 : Step(2): len = 761153, overlap = 743.375
PHY-3002 : Step(3): len = 503174, overlap = 891.344
PHY-3002 : Step(4): len = 435033, overlap = 981.125
PHY-3002 : Step(5): len = 349867, overlap = 1056.69
PHY-3002 : Step(6): len = 311323, overlap = 1108
PHY-3002 : Step(7): len = 266772, overlap = 1174.44
PHY-3002 : Step(8): len = 239437, overlap = 1221.78
PHY-3002 : Step(9): len = 213166, overlap = 1272.81
PHY-3002 : Step(10): len = 199407, overlap = 1333.38
PHY-3002 : Step(11): len = 181345, overlap = 1370.34
PHY-3002 : Step(12): len = 168359, overlap = 1412.06
PHY-3002 : Step(13): len = 156126, overlap = 1438.59
PHY-3002 : Step(14): len = 146626, overlap = 1459.41
PHY-3002 : Step(15): len = 132776, overlap = 1480.28
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.31055e-06
PHY-3002 : Step(16): len = 140735, overlap = 1459.19
PHY-3002 : Step(17): len = 196630, overlap = 1315.38
PHY-3002 : Step(18): len = 211929, overlap = 1197.91
PHY-3002 : Step(19): len = 213834, overlap = 1096.22
PHY-3002 : Step(20): len = 208713, overlap = 1082.81
PHY-3002 : Step(21): len = 204024, overlap = 1062.94
PHY-3002 : Step(22): len = 197513, overlap = 1056.09
PHY-3002 : Step(23): len = 190790, overlap = 1058.78
PHY-3002 : Step(24): len = 184107, overlap = 1087.72
PHY-3002 : Step(25): len = 179705, overlap = 1079.5
PHY-3002 : Step(26): len = 176685, overlap = 1074.38
PHY-3002 : Step(27): len = 174208, overlap = 1094.94
PHY-3002 : Step(28): len = 172178, overlap = 1099.22
PHY-3002 : Step(29): len = 171232, overlap = 1105.62
PHY-3002 : Step(30): len = 169583, overlap = 1104.97
PHY-3002 : Step(31): len = 168184, overlap = 1109.34
PHY-3002 : Step(32): len = 166776, overlap = 1103.56
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.6211e-06
PHY-3002 : Step(33): len = 174901, overlap = 1056.5
PHY-3002 : Step(34): len = 190436, overlap = 991.5
PHY-3002 : Step(35): len = 194768, overlap = 944.375
PHY-3002 : Step(36): len = 199399, overlap = 950.562
PHY-3002 : Step(37): len = 200918, overlap = 921.469
PHY-3002 : Step(38): len = 200167, overlap = 907.062
PHY-3002 : Step(39): len = 198103, overlap = 909.875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 5.24219e-06
PHY-3002 : Step(40): len = 211310, overlap = 888.188
PHY-3002 : Step(41): len = 226410, overlap = 801.125
PHY-3002 : Step(42): len = 232267, overlap = 781.25
PHY-3002 : Step(43): len = 235624, overlap = 762.875
PHY-3002 : Step(44): len = 236133, overlap = 745.844
PHY-3002 : Step(45): len = 234547, overlap = 724.75
PHY-3002 : Step(46): len = 232912, overlap = 713.719
PHY-3002 : Step(47): len = 231582, overlap = 719.156
PHY-3002 : Step(48): len = 229917, overlap = 714.344
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 1.04844e-05
PHY-3002 : Step(49): len = 241277, overlap = 665.906
PHY-3002 : Step(50): len = 259695, overlap = 601.094
PHY-3002 : Step(51): len = 264185, overlap = 575
PHY-3002 : Step(52): len = 266917, overlap = 558.312
PHY-3002 : Step(53): len = 265673, overlap = 559.219
PHY-3002 : Step(54): len = 264154, overlap = 548.594
PHY-3002 : Step(55): len = 261622, overlap = 546.719
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 2.09688e-05
PHY-3002 : Step(56): len = 273953, overlap = 503.219
PHY-3002 : Step(57): len = 289429, overlap = 436.594
PHY-3002 : Step(58): len = 293197, overlap = 419.031
PHY-3002 : Step(59): len = 294252, overlap = 413.625
PHY-3002 : Step(60): len = 293201, overlap = 409.5
PHY-3002 : Step(61): len = 292203, overlap = 410.781
PHY-3002 : Step(62): len = 289812, overlap = 401.875
PHY-3002 : Step(63): len = 288786, overlap = 422.688
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 4.19375e-05
PHY-3002 : Step(64): len = 299224, overlap = 398.688
PHY-3002 : Step(65): len = 310988, overlap = 386.188
PHY-3002 : Step(66): len = 314186, overlap = 383.938
PHY-3002 : Step(67): len = 314772, overlap = 389.094
PHY-3002 : Step(68): len = 313552, overlap = 385.031
PHY-3002 : Step(69): len = 311938, overlap = 371.188
PHY-3002 : Step(70): len = 310487, overlap = 364.656
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 8.38751e-05
PHY-3002 : Step(71): len = 317366, overlap = 349.656
PHY-3002 : Step(72): len = 326187, overlap = 313.688
PHY-3002 : Step(73): len = 330044, overlap = 320.375
PHY-3002 : Step(74): len = 330717, overlap = 323.406
PHY-3002 : Step(75): len = 329617, overlap = 324.531
PHY-3002 : Step(76): len = 328866, overlap = 317.75
PHY-3002 : Step(77): len = 327370, overlap = 333.656
PHY-3002 : Step(78): len = 327437, overlap = 334.656
PHY-3002 : Step(79): len = 327121, overlap = 339.844
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.00016775
PHY-3002 : Step(80): len = 331128, overlap = 330.312
PHY-3002 : Step(81): len = 337930, overlap = 301.781
PHY-3002 : Step(82): len = 339767, overlap = 294.375
PHY-3002 : Step(83): len = 341035, overlap = 289.781
PHY-3002 : Step(84): len = 339978, overlap = 274.75
PHY-3002 : Step(85): len = 339499, overlap = 257.344
PHY-3002 : Step(86): len = 338677, overlap = 260.625
PHY-3002 : Step(87): len = 338919, overlap = 246.875
PHY-3002 : Step(88): len = 338833, overlap = 249.969
PHY-3002 : Step(89): len = 339460, overlap = 255.062
PHY-3002 : Step(90): len = 337690, overlap = 254
PHY-3002 : Step(91): len = 337478, overlap = 262.312
PHY-3002 : Step(92): len = 336846, overlap = 262.688
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000316497
PHY-3002 : Step(93): len = 339342, overlap = 267.938
PHY-3002 : Step(94): len = 343720, overlap = 274.812
PHY-3002 : Step(95): len = 345669, overlap = 259.562
PHY-3002 : Step(96): len = 346271, overlap = 252.969
PHY-3002 : Step(97): len = 345790, overlap = 253.844
PHY-3002 : Step(98): len = 345653, overlap = 266.969
PHY-3002 : Step(99): len = 346551, overlap = 270.594
PHY-3002 : Step(100): len = 346883, overlap = 273.719
PHY-3002 : Step(101): len = 346568, overlap = 265.281
PHY-3002 : Step(102): len = 346575, overlap = 271.906
PHY-3002 : Step(103): len = 346896, overlap = 258.812
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000512092
PHY-3002 : Step(104): len = 347985, overlap = 251.469
PHY-3002 : Step(105): len = 349972, overlap = 241.812
PHY-3002 : Step(106): len = 351558, overlap = 244.625
PHY-3001 : :::11::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(107): len = 351960, overlap = 242.281
PHY-3002 : Step(108): len = 352881, overlap = 240.906
PHY-3001 : Before Legalized: Len = 375084
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.011969s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (130.5%)

PHY-3001 : After Legalized: Len = 382532, Over = 149.188
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21986.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 471624, over cnt = 1247(3%), over = 5779, worst = 35
PHY-1001 : End global iterations;  0.777469s wall, 1.078125s user + 0.000000s system = 1.078125s CPU (138.7%)

PHY-1001 : Congestion index: top1 = 81.53, top5 = 55.25, top10 = 45.26, top15 = 39.36.
PHY-3001 : End congestion estimation;  0.988405s wall, 1.296875s user + 0.000000s system = 1.296875s CPU (131.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21984 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.854853s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (98.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000106087
PHY-3002 : Step(109): len = 395684, overlap = 197.438
PHY-3002 : Step(110): len = 413781, overlap = 190.156
PHY-3002 : Step(111): len = 415589, overlap = 181.531
PHY-3002 : Step(112): len = 416786, overlap = 163.75
PHY-3002 : Step(113): len = 425747, overlap = 148.312
PHY-3002 : Step(114): len = 430960, overlap = 146.625
PHY-3002 : Step(115): len = 432818, overlap = 149.031
PHY-3002 : Step(116): len = 438634, overlap = 141.594
PHY-3002 : Step(117): len = 441017, overlap = 139.938
PHY-3002 : Step(118): len = 442744, overlap = 133.969
PHY-3002 : Step(119): len = 445799, overlap = 131.688
PHY-3002 : Step(120): len = 447624, overlap = 128.188
PHY-3002 : Step(121): len = 449706, overlap = 130.688
PHY-3002 : Step(122): len = 451257, overlap = 129.5
PHY-3002 : Step(123): len = 452177, overlap = 128.281
PHY-3002 : Step(124): len = 454551, overlap = 125.656
PHY-3002 : Step(125): len = 455390, overlap = 123.25
PHY-3002 : Step(126): len = 455597, overlap = 122.219
PHY-3002 : Step(127): len = 457048, overlap = 118.125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000212174
PHY-3002 : Step(128): len = 457043, overlap = 120.25
PHY-3002 : Step(129): len = 458109, overlap = 119.656
PHY-3002 : Step(130): len = 459910, overlap = 121.438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000382637
PHY-3002 : Step(131): len = 465139, overlap = 117.188
PHY-3002 : Step(132): len = 474048, overlap = 110.438
PHY-3002 : Step(133): len = 476616, overlap = 106.469
PHY-3002 : Step(134): len = 480746, overlap = 104.312
PHY-3002 : Step(135): len = 481627, overlap = 102.438
PHY-3002 : Step(136): len = 482551, overlap = 97.9375
PHY-3002 : Step(137): len = 484133, overlap = 98.125
PHY-3002 : Step(138): len = 485042, overlap = 102.438
PHY-3002 : Step(139): len = 485746, overlap = 105.344
PHY-3002 : Step(140): len = 485413, overlap = 110.219
PHY-3002 : Step(141): len = 484296, overlap = 119.75
PHY-3002 : Step(142): len = 482723, overlap = 128.062
PHY-3002 : Step(143): len = 483028, overlap = 129.781
PHY-3002 : Step(144): len = 484663, overlap = 131.875
PHY-3002 : Step(145): len = 484946, overlap = 128.469
PHY-3002 : Step(146): len = 486609, overlap = 123.625
PHY-3002 : Step(147): len = 489961, overlap = 120.469
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000765274
PHY-3002 : Step(148): len = 488749, overlap = 112.219
PHY-3002 : Step(149): len = 490703, overlap = 109.531
PHY-3002 : Step(150): len = 492619, overlap = 107.531
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00146532
PHY-3002 : Step(151): len = 494133, overlap = 108.219
PHY-3002 : Step(152): len = 499847, overlap = 109.156
PHY-3002 : Step(153): len = 506081, overlap = 102.938
PHY-3002 : Step(154): len = 508974, overlap = 101.344
PHY-3002 : Step(155): len = 511200, overlap = 95.375
PHY-3002 : Step(156): len = 512527, overlap = 92.75
PHY-3002 : Step(157): len = 512089, overlap = 90.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 34/21986.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 589792, over cnt = 2381(6%), over = 11081, worst = 42
PHY-1001 : End global iterations;  1.151079s wall, 1.765625s user + 0.015625s system = 1.781250s CPU (154.7%)

PHY-1001 : Congestion index: top1 = 84.87, top5 = 63.49, top10 = 54.70, top15 = 49.24.
PHY-3001 : End congestion estimation;  1.409554s wall, 2.015625s user + 0.031250s system = 2.046875s CPU (145.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21984 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.879643s wall, 0.875000s user + 0.000000s system = 0.875000s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000115558
PHY-3002 : Step(158): len = 518813, overlap = 297.688
PHY-3002 : Step(159): len = 522076, overlap = 243.531
PHY-3002 : Step(160): len = 512842, overlap = 225.906
PHY-3002 : Step(161): len = 505058, overlap = 215.688
PHY-3002 : Step(162): len = 499181, overlap = 194.094
PHY-3002 : Step(163): len = 496150, overlap = 189.656
PHY-3002 : Step(164): len = 492566, overlap = 178.219
PHY-3002 : Step(165): len = 488746, overlap = 183.406
PHY-3002 : Step(166): len = 485895, overlap = 183.469
PHY-3002 : Step(167): len = 484153, overlap = 191.969
PHY-3002 : Step(168): len = 479762, overlap = 189.469
PHY-3002 : Step(169): len = 476713, overlap = 195.062
PHY-3002 : Step(170): len = 475245, overlap = 197.531
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000231117
PHY-3002 : Step(171): len = 474448, overlap = 188.188
PHY-3002 : Step(172): len = 476647, overlap = 185.438
PHY-3002 : Step(173): len = 478776, overlap = 180.469
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000462233
PHY-3002 : Step(174): len = 480372, overlap = 174.469
PHY-3002 : Step(175): len = 487220, overlap = 166.031
PHY-3002 : Step(176): len = 492350, overlap = 164.438
PHY-3002 : Step(177): len = 492831, overlap = 161
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000924467
PHY-3002 : Step(178): len = 493965, overlap = 159.125
PHY-3002 : Step(179): len = 500117, overlap = 141.5
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 82134, tnet num: 21984, tinst num: 19612, tnode num: 115856, tedge num: 129102.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.358280s wall, 1.328125s user + 0.031250s system = 1.359375s CPU (100.1%)

RUN-1004 : used memory is 571 MB, reserved memory is 550 MB, peak memory is 710 MB
OPT-1001 : Total overflow 508.16 peak overflow 4.31
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 508/21986.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 592936, over cnt = 2622(7%), over = 9494, worst = 24
PHY-1001 : End global iterations;  1.206941s wall, 1.906250s user + 0.046875s system = 1.953125s CPU (161.8%)

PHY-1001 : Congestion index: top1 = 59.38, top5 = 50.86, top10 = 45.93, top15 = 42.83.
PHY-1001 : End incremental global routing;  1.503848s wall, 2.203125s user + 0.046875s system = 2.250000s CPU (149.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21984 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.054713s wall, 1.000000s user + 0.062500s system = 1.062500s CPU (100.7%)

OPT-1001 : 25 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 61 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19523 has valid locations, 298 needs to be replaced
PHY-3001 : design contains 19885 instances, 5844 luts, 12620 seqs, 1315 slices, 262 macros(1315 instances: 864 mslices 451 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 522044
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17731/22259.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 610640, over cnt = 2661(7%), over = 9505, worst = 24
PHY-1001 : End global iterations;  0.205779s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (113.9%)

PHY-1001 : Congestion index: top1 = 59.53, top5 = 50.91, top10 = 46.24, top15 = 43.15.
PHY-3001 : End congestion estimation;  0.427258s wall, 0.437500s user + 0.015625s system = 0.453125s CPU (106.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 83050, tnet num: 22257, tinst num: 19885, tnode num: 117119, tedge num: 130388.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.379732s wall, 1.359375s user + 0.015625s system = 1.375000s CPU (99.7%)

RUN-1004 : used memory is 617 MB, reserved memory is 616 MB, peak memory is 713 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22257 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.339739s wall, 2.296875s user + 0.046875s system = 2.343750s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(180): len = 521466, overlap = 2
PHY-3002 : Step(181): len = 521923, overlap = 2.0625
PHY-3002 : Step(182): len = 522272, overlap = 2.25
PHY-3002 : Step(183): len = 522687, overlap = 2.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17784/22259.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 607744, over cnt = 2674(7%), over = 9676, worst = 24
PHY-1001 : End global iterations;  0.191228s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (106.2%)

PHY-1001 : Congestion index: top1 = 60.34, top5 = 51.54, top10 = 46.72, top15 = 43.50.
PHY-3001 : End congestion estimation;  0.414135s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (101.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22257 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.910774s wall, 0.906250s user + 0.015625s system = 0.921875s CPU (101.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00065967
PHY-3002 : Step(184): len = 522830, overlap = 144.656
PHY-3002 : Step(185): len = 523503, overlap = 144.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00131934
PHY-3002 : Step(186): len = 523544, overlap = 144.031
PHY-3002 : Step(187): len = 523878, overlap = 143.688
PHY-3001 : Final: Len = 523878, Over = 143.688
PHY-3001 : End incremental placement;  4.921589s wall, 5.140625s user + 0.250000s system = 5.390625s CPU (109.5%)

OPT-1001 : Total overflow 512.88 peak overflow 4.31
OPT-1001 : End high-fanout net optimization;  7.970776s wall, 8.984375s user + 0.375000s system = 9.359375s CPU (117.4%)

OPT-1001 : Current memory(MB): used = 718, reserve = 702, peak = 735.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17778/22259.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 611552, over cnt = 2608(7%), over = 8932, worst = 24
PHY-1002 : len = 649120, over cnt = 1917(5%), over = 5242, worst = 24
PHY-1002 : len = 690448, over cnt = 942(2%), over = 2376, worst = 24
PHY-1002 : len = 712600, over cnt = 453(1%), over = 1044, worst = 14
PHY-1002 : len = 728584, over cnt = 59(0%), over = 152, worst = 10
PHY-1001 : End global iterations;  1.303605s wall, 1.859375s user + 0.015625s system = 1.875000s CPU (143.8%)

PHY-1001 : Congestion index: top1 = 52.07, top5 = 45.91, top10 = 42.88, top15 = 40.95.
OPT-1001 : End congestion update;  1.549609s wall, 2.093750s user + 0.015625s system = 2.109375s CPU (136.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22257 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.803592s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (101.1%)

OPT-0007 : Start: WNS 4119 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.359709s wall, 2.906250s user + 0.015625s system = 2.921875s CPU (123.8%)

OPT-1001 : Current memory(MB): used = 693, reserve = 682, peak = 735.
OPT-1001 : End physical optimization;  11.980149s wall, 13.546875s user + 0.437500s system = 13.984375s CPU (116.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5844 LUT to BLE ...
SYN-4008 : Packed 5844 LUT and 2866 SEQ to BLE.
SYN-4003 : Packing 9754 remaining SEQ's ...
SYN-4005 : Packed 3373 SEQ with LUT/SLICE
SYN-4006 : 70 single LUT's are left
SYN-4006 : 6381 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 12225/13768 primitive instances ...
PHY-3001 : End packing;  2.617403s wall, 2.609375s user + 0.015625s system = 2.625000s CPU (100.3%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8154 instances
RUN-1001 : 4023 mslices, 4023 lslices, 61 pads, 42 brams, 0 dsps
RUN-1001 : There are total 19437 nets
RUN-1001 : 13329 nets have 2 pins
RUN-1001 : 4674 nets have [3 - 5] pins
RUN-1001 : 849 nets have [6 - 10] pins
RUN-1001 : 426 nets have [11 - 20] pins
RUN-1001 : 150 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8152 instances, 8046 slices, 262 macros(1315 instances: 864 mslices 451 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 537971, Over = 367.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 8026/19437.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 680632, over cnt = 1739(4%), over = 2745, worst = 9
PHY-1002 : len = 687440, over cnt = 1161(3%), over = 1600, worst = 7
PHY-1002 : len = 704064, over cnt = 292(0%), over = 357, worst = 6
PHY-1002 : len = 708648, over cnt = 81(0%), over = 99, worst = 5
PHY-1002 : len = 710992, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.215776s wall, 1.875000s user + 0.062500s system = 1.937500s CPU (159.4%)

PHY-1001 : Congestion index: top1 = 52.80, top5 = 46.68, top10 = 43.28, top15 = 40.95.
PHY-3001 : End congestion estimation;  1.502757s wall, 2.156250s user + 0.062500s system = 2.218750s CPU (147.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68512, tnet num: 19435, tinst num: 8152, tnode num: 93327, tedge num: 112773.
TMR-2508 : Levelizing timing graph completed, there are 29 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.651242s wall, 1.640625s user + 0.015625s system = 1.656250s CPU (100.3%)

RUN-1004 : used memory is 604 MB, reserved memory is 607 MB, peak memory is 735 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19435 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.486446s wall, 2.468750s user + 0.031250s system = 2.500000s CPU (100.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.99374e-05
PHY-3002 : Step(188): len = 535522, overlap = 344.25
PHY-3002 : Step(189): len = 529070, overlap = 363.25
PHY-3002 : Step(190): len = 525789, overlap = 369.25
PHY-3002 : Step(191): len = 525169, overlap = 384.5
PHY-3002 : Step(192): len = 526078, overlap = 388
PHY-3002 : Step(193): len = 524509, overlap = 386
PHY-3002 : Step(194): len = 523622, overlap = 388.5
PHY-3002 : Step(195): len = 522265, overlap = 388.5
PHY-3002 : Step(196): len = 520883, overlap = 389.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.98749e-05
PHY-3002 : Step(197): len = 524905, overlap = 377.5
PHY-3002 : Step(198): len = 529439, overlap = 370.75
PHY-3002 : Step(199): len = 529398, overlap = 365
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00019975
PHY-3002 : Step(200): len = 537983, overlap = 350.25
PHY-3002 : Step(201): len = 548006, overlap = 325.5
PHY-3002 : Step(202): len = 546007, overlap = 322.25
PHY-3002 : Step(203): len = 544179, overlap = 319.75
PHY-3002 : Step(204): len = 544080, overlap = 313
PHY-3001 : Before Legalized: Len = 544080
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.619771s wall, 0.546875s user + 0.875000s system = 1.421875s CPU (229.4%)

PHY-3001 : After Legalized: Len = 650378, Over = 0
PHY-3001 : Trial Legalized: Len = 650378
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 565/19437.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 746720, over cnt = 2472(7%), over = 4020, worst = 7
PHY-1002 : len = 760872, over cnt = 1562(4%), over = 2221, worst = 7
PHY-1002 : len = 778200, over cnt = 697(1%), over = 949, worst = 6
PHY-1002 : len = 788160, over cnt = 281(0%), over = 365, worst = 4
PHY-1002 : len = 794600, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.769978s wall, 2.984375s user + 0.046875s system = 3.031250s CPU (171.3%)

PHY-1001 : Congestion index: top1 = 51.55, top5 = 46.36, top10 = 43.53, top15 = 41.70.
PHY-3001 : End congestion estimation;  2.096202s wall, 3.312500s user + 0.046875s system = 3.359375s CPU (160.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19435 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.022621s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (99.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000192354
PHY-3002 : Step(205): len = 606343, overlap = 74.5
PHY-3002 : Step(206): len = 588312, overlap = 128.25
PHY-3002 : Step(207): len = 577231, overlap = 173.75
PHY-3002 : Step(208): len = 571007, overlap = 210.75
PHY-3002 : Step(209): len = 567768, overlap = 231.75
PHY-3002 : Step(210): len = 565819, overlap = 245.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000384708
PHY-3002 : Step(211): len = 570740, overlap = 243
PHY-3002 : Step(212): len = 575619, overlap = 237.25
PHY-3002 : Step(213): len = 575860, overlap = 239.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000727952
PHY-3002 : Step(214): len = 579380, overlap = 236.75
PHY-3002 : Step(215): len = 586053, overlap = 228.5
PHY-3001 : Before Legalized: Len = 586053
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.030423s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (102.7%)

PHY-3001 : After Legalized: Len = 624214, Over = 0
PHY-3001 : Legalized: Len = 624214, Over = 0
PHY-3001 : Spreading special nets. 50 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.071938s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (108.6%)

PHY-3001 : 83 instances has been re-located, deltaX = 23, deltaY = 48, maxDist = 2.
PHY-3001 : Final: Len = 625636, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68512, tnet num: 19435, tinst num: 8152, tnode num: 93327, tedge num: 112773.
TMR-2508 : Levelizing timing graph completed, there are 29 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.877271s wall, 1.875000s user + 0.000000s system = 1.875000s CPU (99.9%)

RUN-1004 : used memory is 601 MB, reserved memory is 591 MB, peak memory is 735 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3816/19437.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 735424, over cnt = 2344(6%), over = 3721, worst = 7
PHY-1002 : len = 748168, over cnt = 1323(3%), over = 1830, worst = 6
PHY-1002 : len = 758208, over cnt = 772(2%), over = 1077, worst = 6
PHY-1002 : len = 767248, over cnt = 355(1%), over = 494, worst = 6
PHY-1002 : len = 772840, over cnt = 123(0%), over = 168, worst = 6
PHY-1001 : End global iterations;  1.543562s wall, 2.718750s user + 0.062500s system = 2.781250s CPU (180.2%)

PHY-1001 : Congestion index: top1 = 49.57, top5 = 44.86, top10 = 42.34, top15 = 40.62.
PHY-1001 : End incremental global routing;  1.821895s wall, 3.000000s user + 0.062500s system = 3.062500s CPU (168.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19435 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.868532s wall, 0.843750s user + 0.015625s system = 0.859375s CPU (98.9%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 61 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8087 has valid locations, 11 needs to be replaced
PHY-3001 : design contains 8162 instances, 8056 slices, 262 macros(1315 instances: 864 mslices 451 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 630016
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17712/19446.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 779336, over cnt = 150(0%), over = 208, worst = 6
PHY-1002 : len = 779920, over cnt = 64(0%), over = 76, worst = 6
PHY-1002 : len = 780784, over cnt = 20(0%), over = 23, worst = 3
PHY-1002 : len = 781056, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 781384, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.687148s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (100.1%)

PHY-1001 : Congestion index: top1 = 49.78, top5 = 45.12, top10 = 42.58, top15 = 40.84.
PHY-3001 : End congestion estimation;  0.958274s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (99.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68601, tnet num: 19444, tinst num: 8162, tnode num: 93426, tedge num: 112871.
TMR-2508 : Levelizing timing graph completed, there are 29 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.888100s wall, 1.890625s user + 0.000000s system = 1.890625s CPU (100.1%)

RUN-1004 : used memory is 631 MB, reserved memory is 613 MB, peak memory is 735 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19444 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.751246s wall, 2.734375s user + 0.015625s system = 2.750000s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(216): len = 629599, overlap = 0.5
PHY-3002 : Step(217): len = 629376, overlap = 0.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17711/19446.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 780088, over cnt = 30(0%), over = 45, worst = 7
PHY-1002 : len = 780160, over cnt = 23(0%), over = 31, worst = 5
PHY-1002 : len = 780320, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 780384, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.516327s wall, 0.609375s user + 0.031250s system = 0.640625s CPU (124.1%)

PHY-1001 : Congestion index: top1 = 49.94, top5 = 45.05, top10 = 42.48, top15 = 40.75.
PHY-3001 : End congestion estimation;  0.786329s wall, 0.875000s user + 0.031250s system = 0.906250s CPU (115.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19444 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.820354s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (99.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00120049
PHY-3002 : Step(218): len = 629240, overlap = 1.25
PHY-3002 : Step(219): len = 629188, overlap = 1.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005865s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 629358, Over = 0
PHY-3001 : End spreading;  0.085565s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (109.6%)

PHY-3001 : Final: Len = 629358, Over = 0
PHY-3001 : End incremental placement;  5.920901s wall, 6.328125s user + 0.109375s system = 6.437500s CPU (108.7%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  9.046634s wall, 10.609375s user + 0.187500s system = 10.796875s CPU (119.3%)

OPT-1001 : Current memory(MB): used = 720, reserve = 710, peak = 735.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17710/19446.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 780840, over cnt = 25(0%), over = 47, worst = 6
PHY-1002 : len = 781000, over cnt = 20(0%), over = 29, worst = 4
PHY-1002 : len = 781200, over cnt = 7(0%), over = 9, worst = 3
PHY-1002 : len = 781320, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 781408, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.677659s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (101.5%)

PHY-1001 : Congestion index: top1 = 49.83, top5 = 45.04, top10 = 42.49, top15 = 40.76.
OPT-1001 : End congestion update;  0.948960s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (102.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19444 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.693385s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (99.2%)

OPT-0007 : Start: WNS 4352 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.646944s wall, 1.671875s user + 0.000000s system = 1.671875s CPU (101.5%)

OPT-1001 : Current memory(MB): used = 720, reserve = 710, peak = 735.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19444 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.689979s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (99.6%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17722/19446.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 781408, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.112902s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (96.9%)

PHY-1001 : Congestion index: top1 = 49.83, top5 = 45.04, top10 = 42.49, top15 = 40.76.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19444 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.695840s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (101.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4352 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 49.413793
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4352ps with logic level 4 
RUN-1001 :       #2 path slack 4359ps with logic level 4 
OPT-1001 : End physical optimization;  14.577085s wall, 16.109375s user + 0.234375s system = 16.343750s CPU (112.1%)

RUN-1003 : finish command "place" in  67.675547s wall, 97.234375s user + 7.015625s system = 104.250000s CPU (154.0%)

RUN-1004 : used memory is 570 MB, reserved memory is 550 MB, peak memory is 735 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.473086s wall, 2.593750s user + 0.000000s system = 2.593750s CPU (176.1%)

RUN-1004 : used memory is 571 MB, reserved memory is 551 MB, peak memory is 735 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.5_SP3/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8164 instances
RUN-1001 : 4033 mslices, 4023 lslices, 61 pads, 42 brams, 0 dsps
RUN-1001 : There are total 19446 nets
RUN-1001 : 13326 nets have 2 pins
RUN-1001 : 4675 nets have [3 - 5] pins
RUN-1001 : 853 nets have [6 - 10] pins
RUN-1001 : 433 nets have [11 - 20] pins
RUN-1001 : 150 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68601, tnet num: 19444, tinst num: 8162, tnode num: 93426, tedge num: 112871.
TMR-2508 : Levelizing timing graph completed, there are 29 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.650926s wall, 1.656250s user + 0.000000s system = 1.656250s CPU (100.3%)

RUN-1004 : used memory is 598 MB, reserved memory is 599 MB, peak memory is 735 MB
PHY-1001 : 4033 mslices, 4023 lslices, 61 pads, 42 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19444 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 718016, over cnt = 2418(6%), over = 4018, worst = 7
PHY-1002 : len = 733376, over cnt = 1509(4%), over = 2160, worst = 7
PHY-1002 : len = 752864, over cnt = 542(1%), over = 756, worst = 6
PHY-1002 : len = 763408, over cnt = 55(0%), over = 73, worst = 6
PHY-1002 : len = 764544, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.667889s wall, 2.734375s user + 0.046875s system = 2.781250s CPU (166.8%)

PHY-1001 : Congestion index: top1 = 48.84, top5 = 44.36, top10 = 41.86, top15 = 40.19.
PHY-1001 : End global routing;  1.965752s wall, 3.031250s user + 0.062500s system = 3.093750s CPU (157.4%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 703, reserve = 701, peak = 735.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net DATA/DIV_Dtemp/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 973, reserve = 967, peak = 973.
PHY-1001 : End build detailed router design. 4.201094s wall, 4.156250s user + 0.046875s system = 4.203125s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 197488, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.749141s wall, 0.734375s user + 0.015625s system = 0.750000s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1010, reserve = 1005, peak = 1010.
PHY-1001 : End phase 1; 0.756351s wall, 0.734375s user + 0.015625s system = 0.750000s CPU (99.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 94% nets.
PHY-1022 : len = 1.86269e+06, over cnt = 1552(0%), over = 1560, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1028, reserve = 1023, peak = 1028.
PHY-1001 : End initial routed; 19.658502s wall, 52.546875s user + 0.312500s system = 52.859375s CPU (268.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18330(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.801   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.220   |  -16.690  |   9   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.165191s wall, 3.156250s user + 0.000000s system = 3.156250s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 1040, reserve = 1035, peak = 1040.
PHY-1001 : End phase 2; 22.823835s wall, 55.703125s user + 0.312500s system = 56.015625s CPU (245.4%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.86269e+06, over cnt = 1552(0%), over = 1560, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.226013s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (103.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.84463e+06, over cnt = 509(0%), over = 509, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.916078s wall, 1.453125s user + 0.015625s system = 1.468750s CPU (160.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.84434e+06, over cnt = 92(0%), over = 92, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.390723s wall, 0.531250s user + 0.015625s system = 0.546875s CPU (140.0%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.84525e+06, over cnt = 10(0%), over = 10, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.238130s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (111.5%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.84522e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.172509s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (117.7%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.84524e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.157092s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (99.5%)

PHY-1001 : ===== DR Iter 6 =====
PHY-1022 : len = 1.84524e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 6; 0.201293s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (100.9%)

PHY-1001 : ===== DR Iter 7 =====
PHY-1022 : len = 1.84524e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 7; 0.296204s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (95.0%)

PHY-1001 : ===== DR Iter 8 =====
PHY-1022 : len = 1.84525e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 8; 0.147680s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (95.2%)

PHY-1001 : ==== DR Iter 9 ====
PHY-1022 : len = 1.84525e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 9; 0.139259s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (101.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18330(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.801   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.220   |  -16.690  |   9   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.190139s wall, 3.203125s user + 0.000000s system = 3.203125s CPU (100.4%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 417 feed throughs used by 338 nets
PHY-1001 : End commit to database; 2.165083s wall, 2.140625s user + 0.015625s system = 2.156250s CPU (99.6%)

PHY-1001 : Current memory(MB): used = 1134, reserve = 1132, peak = 1134.
PHY-1001 : End phase 3; 8.735706s wall, 9.421875s user + 0.078125s system = 9.500000s CPU (108.7%)

PHY-1003 : Routed, final wirelength = 1.84525e+06
PHY-1001 : Current memory(MB): used = 1139, reserve = 1137, peak = 1139.
PHY-1001 : End export database. 0.058929s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (79.5%)

PHY-1001 : End detail routing;  36.963311s wall, 70.453125s user + 0.468750s system = 70.921875s CPU (191.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68601, tnet num: 19444, tinst num: 8162, tnode num: 93426, tedge num: 112871.
TMR-2508 : Levelizing timing graph completed, there are 29 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.645800s wall, 1.640625s user + 0.000000s system = 1.640625s CPU (99.7%)

RUN-1004 : used memory is 1064 MB, reserved memory is 1074 MB, peak memory is 1139 MB
RUN-1001 : fix hold violation on net config_inst_syn_2 endpin DATA/DIV_accY/al_9d7c420_syn_3.sr slack -2074ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net config_inst_syn_2 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_34.sr slack -1893ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net config_inst_syn_2 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_36.sr slack -1489ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net config_inst_syn_2 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_39.sr slack -1489ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net config_inst_syn_2 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_41.sr slack -1860ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net config_inst_syn_2 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_43.sr slack -1644ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net config_inst_syn_2 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_45.sr slack -2220ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net config_inst_syn_2 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_47.sr slack -1866ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net config_inst_syn_2 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_49.sr slack -2155ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68655, tnet num: 19471, tinst num: 8189, tnode num: 93480, tedge num: 112925.
TMR-2508 : Levelizing timing graph completed, there are 29 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.644488s wall, 1.656250s user + 0.000000s system = 1.656250s CPU (100.7%)

RUN-1004 : used memory is 1126 MB, reserved memory is 1124 MB, peak memory is 1139 MB
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  28.157309s wall, 28.593750s user + 0.406250s system = 29.000000s CPU (103.0%)

RUN-1003 : finish command "route" in  69.552716s wall, 104.531250s user + 0.937500s system = 105.468750s CPU (151.6%)

RUN-1004 : used memory is 1123 MB, reserved memory is 1122 MB, peak memory is 1139 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        67
  #input                   26
  #output                  39
  #inout                    2

Utilization Statistics
#lut                     8649   out of  19600   44.13%
#reg                    12719   out of  19600   64.89%
#le                     15003
  #lut only              2284   out of  15003   15.22%
  #reg only              6354   out of  15003   42.35%
  #lut&reg               6365   out of  15003   42.42%
#dsp                        0   out of     29    0.00%
#bram                      42   out of     64   65.62%
  #bram9k                  42
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       61   out of    188   32.45%
  #ireg                     9
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet              Type               DriverType         Driver                    Fanout
#1        DATA/DIV_Dtemp/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6964
#2        config_inst_syn_9     GCLK               config             config_inst.jtck          193
#3        clk_in_dup_1          GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         L4        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          NONE       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D14        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    GNSS_RMC       OUTPUT         L3        LVCMOS33           8            NONE           NONE       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         T6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT        D16        LVCMOS33           8            NONE           NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        R15        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15003  |7334    |1315    |12761   |42      |0       |
|  AnyFog_dataX                      |AnyFog          |208    |78      |22      |172     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |55      |22      |50      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |198    |93      |22      |157     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |78     |55      |22      |37      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |202    |111     |22      |168     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |83     |60      |22      |49      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |3521   |900     |34      |3421    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |718    |115     |5       |703     |0       |0       |
|    STADOP_com2                     |STADOP          |556    |55      |0       |549     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |41      |14      |42      |0       |0       |
|    head_com2                       |uniheading      |283    |78      |5       |263     |0       |0       |
|    rmc_com2                        |Gprmc           |159    |57      |0       |149     |0       |0       |
|    uart_com2                       |Agrica          |1451   |265     |10      |1426    |0       |0       |
|  DATA                              |Data_Processing |8666   |4397    |1057    |6976    |0       |0       |
|    DIV_Dtemp                       |Divider         |830    |337     |84      |707     |0       |0       |
|    DIV_Utemp                       |Divider         |613    |278     |84      |490     |0       |0       |
|    DIV_accX                        |Divider         |606    |315     |84      |473     |0       |0       |
|    DIV_accY                        |Divider         |739    |365     |132     |535     |0       |0       |
|    DIV_accZ                        |Divider         |721    |361     |132     |508     |0       |0       |
|    DIV_rateX                       |Divider         |608    |370     |132     |398     |0       |0       |
|    DIV_rateY                       |Divider         |593    |380     |132     |388     |0       |0       |
|    DIV_rateZ                       |Divider         |558    |358     |132     |331     |0       |0       |
|    genclk                          |genclk          |68     |55      |11      |49      |0       |0       |
|  FMC                               |FMC_Ctrl        |367    |331     |20      |335     |0       |0       |
|  IIC                               |I2C_master      |284    |258     |11      |264     |0       |0       |
|  IMU_CTRL                          |SCHA634         |888    |680     |61      |740     |0       |0       |
|    CtrlData                        |CtrlData        |461    |407     |47      |335     |0       |0       |
|      usms                          |Time_1ms        |32     |27      |5       |24      |0       |0       |
|    SPIM                            |SPI_SCHA634     |427    |273     |14      |405     |0       |0       |
|  POWER                             |POWER_EN        |45     |31      |11      |29      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |549    |399     |55      |436     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |549    |399     |55      |436     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |290    |202     |0       |273     |0       |0       |
|        reg_inst                    |register        |278    |190     |0       |261     |0       |0       |
|        tap_inst                    |tap             |12     |12      |0       |12      |0       |0       |
|      trigger_inst                  |trigger         |259    |197     |55      |163     |0       |0       |
|        bus_inst                    |bus_top         |41     |39      |0       |41      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |8      |8       |0       |8       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |4      |4       |0       |4       |0       |0       |
|          BUS_DETECTOR[5]$bus_nodes |bus_det         |10     |10      |0       |10      |0       |0       |
|          BUS_DETECTOR[6]$bus_nodes |bus_det         |15     |13      |0       |15      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |144    |107     |32      |86      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13291  
    #2          2       3845   
    #3          3        542   
    #4          4        288   
    #5        5-10       895   
    #6        11-50      525   
    #7       51-100      16    
    #8       101-500      5    
  Average     2.16             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  1.920963s wall, 3.296875s user + 0.015625s system = 3.312500s CPU (172.4%)

RUN-1004 : used memory is 1124 MB, reserved memory is 1123 MB, peak memory is 1178 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68655, tnet num: 19471, tinst num: 8189, tnode num: 93480, tedge num: 112925.
TMR-2508 : Levelizing timing graph completed, there are 29 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.656537s wall, 1.656250s user + 0.000000s system = 1.656250s CPU (100.0%)

RUN-1004 : used memory is 1126 MB, reserved memory is 1124 MB, peak memory is 1178 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19471 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 3. Number of clock nets = 3 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.329178s wall, 1.328125s user + 0.000000s system = 1.328125s CPU (99.9%)

RUN-1004 : used memory is 1128 MB, reserved memory is 1124 MB, peak memory is 1178 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8189
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19473, pip num: 153918
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 419
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3260 valid insts, and 423318 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011101100001111110010110
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  11.562525s wall, 118.421875s user + 0.093750s system = 118.515625s CPU (1025.0%)

RUN-1004 : used memory is 1229 MB, reserved memory is 1221 MB, peak memory is 1344 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250428_183358.log"

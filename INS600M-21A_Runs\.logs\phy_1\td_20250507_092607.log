============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Wed May  7 09:26:07 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(247)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(252)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(273)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(278)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(421)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(428)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(435)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(442)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(449)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(456)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(463)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(470)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(647)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(652)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(680)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(685)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(919)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(926)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(933)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(940)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(947)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(952)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(959)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(966)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(973)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(980)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(102)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 102 in ../../Src/INS600M-21A.v(106)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(516)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
RUN-1001 : Project manager successfully analyzed 21 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.070051s wall, 1.312500s user + 3.765625s system = 5.078125s CPU (100.2%)

RUN-1004 : used memory is 77 MB, reserved memory is 39 MB, peak memory is 89 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.687047s wall, 1.671875s user + 0.015625s system = 1.687500s CPU (100.0%)

RUN-1004 : used memory is 297 MB, reserved memory is 266 MB, peak memory is 300 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 7 view nodes, 47 trigger nets, 47 data nets.
KIT-1004 : Chipwatcher code = 0001111110010110
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=144) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=144) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=144)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=144)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22833/31 useful/useless nets, 19616/17 useful/useless insts
SYN-1016 : Merged 37 instances.
SYN-1032 : 22457/22 useful/useless nets, 20075/18 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 4 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 4 mux instances.
SYN-1015 : Optimize round 1, 443 better
SYN-1014 : Optimize round 2
SYN-1032 : 22085/60 useful/useless nets, 19703/64 useful/useless insts
SYN-1015 : Optimize round 2, 128 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.269832s wall, 2.187500s user + 0.078125s system = 2.265625s CPU (99.8%)

RUN-1004 : used memory is 325 MB, reserved memory is 293 MB, peak memory is 327 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 67 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22145/367 useful/useless nets, 19804/52 useful/useless insts
SYN-1016 : Merged 57 instances.
SYN-2571 : Optimize after map_dsp, round 1, 476 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 44 instances.
SYN-2501 : Optimize round 1, 90 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 18 macro adder
SYN-1019 : Optimized 17 mux instances.
SYN-1016 : Merged 12 instances.
SYN-1032 : 22616/5 useful/useless nets, 20275/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 83005, tnet num: 22616, tinst num: 20274, tnode num: 116623, tedge num: 129266.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.135022s wall, 1.109375s user + 0.015625s system = 1.125000s CPU (99.1%)

RUN-1004 : used memory is 466 MB, reserved memory is 435 MB, peak memory is 466 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22616 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 257 (3.41), #lev = 7 (1.78)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 257 (3.41), #lev = 7 (1.78)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 596 instances into 257 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 450 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 140 adder to BLE ...
SYN-4008 : Packed 140 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.348880s wall, 4.265625s user + 0.109375s system = 4.375000s CPU (100.6%)

RUN-1004 : used memory is 350 MB, reserved memory is 319 MB, peak memory is 575 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  6.916078s wall, 6.703125s user + 0.234375s system = 6.937500s CPU (100.3%)

RUN-1004 : used memory is 351 MB, reserved memory is 320 MB, peak memory is 575 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/rmc_com2/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/rmc_com2/GPRMC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/rmc_com2/GPRMC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/rmc_com2/GPRMC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/rmc_com2/GPRMC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/rmc_com2/GPRMC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/rmc_com2/GPRMC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/rmc_com2/GPRMC_data[0]
SYN-5055 WARNING: The kept net COM2/rx_err will be merged to another kept net COM2/rmc_com2/GPRMC_err
SYN-5055 WARNING: The kept net FMC/Error_state[0] will be merged to another kept net COM2/rmc_com2/GPRMC_sub_state_cnt0[3]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (306 clock/control pins, 0 other pins).
SYN-4027 : Net DATA/DIV_Dtemp/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net DATA/DIV_Dtemp/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19602 instances
RUN-0007 : 5599 luts, 12471 seqs, 933 mslices, 491 lslices, 61 pads, 42 brams, 0 dsps
RUN-1001 : There are total 21967 nets
RUN-1001 : 16481 nets have 2 pins
RUN-1001 : 4349 nets have [3 - 5] pins
RUN-1001 : 790 nets have [6 - 10] pins
RUN-1001 : 220 nets have [11 - 20] pins
RUN-1001 : 103 nets have [21 - 99] pins
RUN-1001 : 24 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4742     
RUN-1001 :   No   |  No   |  Yes  |     628     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    6531     
RUN-1001 :   Yes  |  No   |  Yes  |     470     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19600 instances, 5599 luts, 12471 seqs, 1424 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1131 pins
PHY-3001 : Huge net DATA/done_div with 1650 pins
PHY-0007 : Cell area utilization is 64%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 81536, tnet num: 21965, tinst num: 19600, tnode num: 115252, tedge num: 128077.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.140802s wall, 1.109375s user + 0.031250s system = 1.140625s CPU (100.0%)

RUN-1004 : used memory is 527 MB, reserved memory is 500 MB, peak memory is 575 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21965 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.936864s wall, 1.890625s user + 0.046875s system = 1.937500s CPU (100.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.41156e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19600.
PHY-3001 : Level 1 #clusters 2057.
PHY-3001 : End clustering;  0.139265s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (145.9%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 64%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 872053, overlap = 633.656
PHY-3002 : Step(2): len = 789292, overlap = 713.938
PHY-3002 : Step(3): len = 521775, overlap = 863.594
PHY-3002 : Step(4): len = 475433, overlap = 921.312
PHY-3002 : Step(5): len = 374100, overlap = 1059.97
PHY-3002 : Step(6): len = 314100, overlap = 1144.22
PHY-3002 : Step(7): len = 269029, overlap = 1173.97
PHY-3002 : Step(8): len = 240157, overlap = 1214.5
PHY-3002 : Step(9): len = 213568, overlap = 1260.72
PHY-3002 : Step(10): len = 196714, overlap = 1309
PHY-3002 : Step(11): len = 171614, overlap = 1385.59
PHY-3002 : Step(12): len = 162733, overlap = 1409.09
PHY-3002 : Step(13): len = 142100, overlap = 1440.94
PHY-3002 : Step(14): len = 133804, overlap = 1458.41
PHY-3002 : Step(15): len = 122172, overlap = 1474.12
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.1639e-06
PHY-3002 : Step(16): len = 130791, overlap = 1461.72
PHY-3002 : Step(17): len = 195998, overlap = 1319.19
PHY-3002 : Step(18): len = 200713, overlap = 1209.44
PHY-3002 : Step(19): len = 194113, overlap = 1159.09
PHY-3002 : Step(20): len = 188162, overlap = 1152.97
PHY-3002 : Step(21): len = 182121, overlap = 1147.09
PHY-3002 : Step(22): len = 176915, overlap = 1133.56
PHY-3002 : Step(23): len = 172460, overlap = 1132.78
PHY-3002 : Step(24): len = 170146, overlap = 1129.34
PHY-3002 : Step(25): len = 168251, overlap = 1106
PHY-3002 : Step(26): len = 166288, overlap = 1082.59
PHY-3002 : Step(27): len = 165246, overlap = 1095.72
PHY-3002 : Step(28): len = 164234, overlap = 1093.47
PHY-3002 : Step(29): len = 163699, overlap = 1110.31
PHY-3002 : Step(30): len = 164521, overlap = 1105.91
PHY-3002 : Step(31): len = 163920, overlap = 1085.81
PHY-3002 : Step(32): len = 163065, overlap = 1092.09
PHY-3002 : Step(33): len = 161646, overlap = 1075.41
PHY-3002 : Step(34): len = 162676, overlap = 1076.88
PHY-3002 : Step(35): len = 161485, overlap = 1075.5
PHY-3002 : Step(36): len = 161254, overlap = 1081.78
PHY-3002 : Step(37): len = 160418, overlap = 1073.59
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.3278e-06
PHY-3002 : Step(38): len = 167334, overlap = 1033.25
PHY-3002 : Step(39): len = 179427, overlap = 965.469
PHY-3002 : Step(40): len = 180834, overlap = 923.75
PHY-3002 : Step(41): len = 183598, overlap = 894.125
PHY-3002 : Step(42): len = 182615, overlap = 894.5
PHY-3002 : Step(43): len = 181141, overlap = 905.625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.65559e-06
PHY-3002 : Step(44): len = 192321, overlap = 844.375
PHY-3002 : Step(45): len = 206153, overlap = 751.344
PHY-3002 : Step(46): len = 211177, overlap = 716.031
PHY-3002 : Step(47): len = 214127, overlap = 706.531
PHY-3002 : Step(48): len = 213224, overlap = 697.094
PHY-3002 : Step(49): len = 212927, overlap = 682.812
PHY-3002 : Step(50): len = 211647, overlap = 667.969
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 9.31119e-06
PHY-3002 : Step(51): len = 224747, overlap = 654.062
PHY-3002 : Step(52): len = 240246, overlap = 586.531
PHY-3002 : Step(53): len = 244504, overlap = 522.75
PHY-3002 : Step(54): len = 245398, overlap = 540.781
PHY-3002 : Step(55): len = 243541, overlap = 532.625
PHY-3002 : Step(56): len = 241091, overlap = 545.719
PHY-3002 : Step(57): len = 238459, overlap = 565.688
PHY-3002 : Step(58): len = 236610, overlap = 565.281
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.86224e-05
PHY-3002 : Step(59): len = 250349, overlap = 503.781
PHY-3002 : Step(60): len = 259845, overlap = 478.344
PHY-3002 : Step(61): len = 261994, overlap = 465.906
PHY-3002 : Step(62): len = 263627, overlap = 450.781
PHY-3002 : Step(63): len = 262503, overlap = 455.281
PHY-3002 : Step(64): len = 261712, overlap = 443.281
PHY-3002 : Step(65): len = 260818, overlap = 437.25
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.72448e-05
PHY-3002 : Step(66): len = 269257, overlap = 421.188
PHY-3002 : Step(67): len = 281153, overlap = 381.969
PHY-3002 : Step(68): len = 284490, overlap = 372.781
PHY-3002 : Step(69): len = 284615, overlap = 364.406
PHY-3002 : Step(70): len = 282748, overlap = 347.875
PHY-3002 : Step(71): len = 280322, overlap = 348.281
PHY-3002 : Step(72): len = 278473, overlap = 334.188
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 7.44895e-05
PHY-3002 : Step(73): len = 285981, overlap = 330.594
PHY-3002 : Step(74): len = 293767, overlap = 312.719
PHY-3002 : Step(75): len = 296093, overlap = 292.156
PHY-3002 : Step(76): len = 297322, overlap = 279.5
PHY-3002 : Step(77): len = 295078, overlap = 288.5
PHY-3002 : Step(78): len = 294172, overlap = 290.719
PHY-3002 : Step(79): len = 292425, overlap = 281.781
PHY-3002 : Step(80): len = 293069, overlap = 282.469
PHY-3002 : Step(81): len = 292995, overlap = 284.594
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000148384
PHY-3002 : Step(82): len = 297133, overlap = 272.438
PHY-3002 : Step(83): len = 303786, overlap = 251.75
PHY-3002 : Step(84): len = 306240, overlap = 276.344
PHY-3002 : Step(85): len = 307731, overlap = 271.094
PHY-3002 : Step(86): len = 307886, overlap = 282.719
PHY-3002 : Step(87): len = 306917, overlap = 272.094
PHY-3002 : Step(88): len = 307049, overlap = 261.375
PHY-3002 : Step(89): len = 307110, overlap = 257.531
PHY-3002 : Step(90): len = 306570, overlap = 250.562
PHY-3002 : Step(91): len = 305780, overlap = 275.156
PHY-3002 : Step(92): len = 305034, overlap = 277.812
PHY-3002 : Step(93): len = 304971, overlap = 276.562
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000288164
PHY-3002 : Step(94): len = 307046, overlap = 261.906
PHY-3002 : Step(95): len = 310570, overlap = 256.438
PHY-3002 : Step(96): len = 311674, overlap = 242.094
PHY-3002 : Step(97): len = 312816, overlap = 237.375
PHY-3002 : Step(98): len = 312827, overlap = 242
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.009575s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (163.2%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21967.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 423432, over cnt = 1201(3%), over = 5492, worst = 39
PHY-1001 : End global iterations;  0.785508s wall, 1.046875s user + 0.046875s system = 1.093750s CPU (139.2%)

PHY-1001 : Congestion index: top1 = 73.97, top5 = 51.87, top10 = 42.54, top15 = 37.44.
PHY-3001 : End congestion estimation;  1.019507s wall, 1.281250s user + 0.046875s system = 1.328125s CPU (130.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21965 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.838893s wall, 0.812500s user + 0.015625s system = 0.828125s CPU (98.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.50926e-05
PHY-3002 : Step(99): len = 355976, overlap = 177.625
PHY-3002 : Step(100): len = 377045, overlap = 158.219
PHY-3002 : Step(101): len = 378575, overlap = 154.562
PHY-3002 : Step(102): len = 377874, overlap = 152.75
PHY-3002 : Step(103): len = 384492, overlap = 141.219
PHY-3002 : Step(104): len = 396980, overlap = 132.844
PHY-3002 : Step(105): len = 403362, overlap = 132.812
PHY-3002 : Step(106): len = 405258, overlap = 131.031
PHY-3002 : Step(107): len = 410717, overlap = 134.75
PHY-3002 : Step(108): len = 413673, overlap = 137.125
PHY-3002 : Step(109): len = 416427, overlap = 133.875
PHY-3002 : Step(110): len = 420177, overlap = 130.688
PHY-3002 : Step(111): len = 421719, overlap = 124.312
PHY-3002 : Step(112): len = 425070, overlap = 118.969
PHY-3002 : Step(113): len = 428526, overlap = 116.438
PHY-3002 : Step(114): len = 429468, overlap = 113.125
PHY-3002 : Step(115): len = 432640, overlap = 114.094
PHY-3002 : Step(116): len = 437191, overlap = 116.062
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000170185
PHY-3002 : Step(117): len = 436781, overlap = 109.781
PHY-3002 : Step(118): len = 438854, overlap = 108.25
PHY-3002 : Step(119): len = 439771, overlap = 106.156
PHY-3002 : Step(120): len = 443802, overlap = 107.719
PHY-3002 : Step(121): len = 448989, overlap = 107.094
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00034037
PHY-3002 : Step(122): len = 448589, overlap = 103.969
PHY-3002 : Step(123): len = 453588, overlap = 95
PHY-3002 : Step(124): len = 460632, overlap = 106.375
PHY-3002 : Step(125): len = 466482, overlap = 106.938
PHY-3002 : Step(126): len = 466532, overlap = 103.406
PHY-3002 : Step(127): len = 468084, overlap = 101.875
PHY-3002 : Step(128): len = 468472, overlap = 105.156
PHY-3002 : Step(129): len = 467427, overlap = 103.156
PHY-3002 : Step(130): len = 467957, overlap = 104.906
PHY-3002 : Step(131): len = 466128, overlap = 105.719
PHY-3002 : Step(132): len = 465863, overlap = 104.5
PHY-3002 : Step(133): len = 466071, overlap = 104
PHY-3002 : Step(134): len = 465684, overlap = 103.656
PHY-3002 : Step(135): len = 467518, overlap = 105.719
PHY-3002 : Step(136): len = 469823, overlap = 106.719
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000680741
PHY-3002 : Step(137): len = 469236, overlap = 100.312
PHY-3002 : Step(138): len = 471836, overlap = 96.8438
PHY-3002 : Step(139): len = 475525, overlap = 104.906
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00120399
PHY-3002 : Step(140): len = 475440, overlap = 104.656
PHY-3002 : Step(141): len = 480722, overlap = 104.812
PHY-3002 : Step(142): len = 490795, overlap = 115.531
PHY-3002 : Step(143): len = 498619, overlap = 123.312
PHY-3002 : Step(144): len = 505877, overlap = 130.719
PHY-3002 : Step(145): len = 512761, overlap = 136.5
PHY-3002 : Step(146): len = 513363, overlap = 138.031
PHY-3002 : Step(147): len = 512182, overlap = 136.625
PHY-3002 : Step(148): len = 509993, overlap = 132.25
PHY-3002 : Step(149): len = 508650, overlap = 130.5
PHY-3002 : Step(150): len = 507202, overlap = 130.312
PHY-3002 : Step(151): len = 504905, overlap = 128.625
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.0020344
PHY-3002 : Step(152): len = 504941, overlap = 129.688
PHY-3002 : Step(153): len = 506173, overlap = 132.062
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 35/21967.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 579088, over cnt = 2288(6%), over = 11063, worst = 37
PHY-1001 : End global iterations;  0.994368s wall, 1.687500s user + 0.062500s system = 1.750000s CPU (176.0%)

PHY-1001 : Congestion index: top1 = 89.03, top5 = 65.35, top10 = 55.04, top15 = 49.24.
PHY-3001 : End congestion estimation;  1.254246s wall, 1.953125s user + 0.062500s system = 2.015625s CPU (160.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21965 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.851869s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (100.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00010216
PHY-3002 : Step(154): len = 517430, overlap = 343.438
PHY-3002 : Step(155): len = 522053, overlap = 279.062
PHY-3002 : Step(156): len = 514671, overlap = 237.969
PHY-3002 : Step(157): len = 507475, overlap = 237.062
PHY-3002 : Step(158): len = 504651, overlap = 213.031
PHY-3002 : Step(159): len = 501064, overlap = 214.25
PHY-3002 : Step(160): len = 497389, overlap = 221
PHY-3002 : Step(161): len = 493903, overlap = 212.906
PHY-3002 : Step(162): len = 490218, overlap = 219.5
PHY-3002 : Step(163): len = 487447, overlap = 227.594
PHY-3002 : Step(164): len = 484162, overlap = 217.469
PHY-3002 : Step(165): len = 482878, overlap = 223.469
PHY-3002 : Step(166): len = 479755, overlap = 237.531
PHY-3002 : Step(167): len = 478154, overlap = 235.969
PHY-3002 : Step(168): len = 474747, overlap = 239.781
PHY-3002 : Step(169): len = 471041, overlap = 244.812
PHY-3002 : Step(170): len = 470013, overlap = 243.281
PHY-3002 : Step(171): len = 467440, overlap = 241.688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00020432
PHY-3002 : Step(172): len = 467711, overlap = 236.906
PHY-3002 : Step(173): len = 469638, overlap = 226.656
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000385856
PHY-3002 : Step(174): len = 474943, overlap = 218.156
PHY-3002 : Step(175): len = 481949, overlap = 209.969
PHY-3002 : Step(176): len = 481783, overlap = 202.625
PHY-3002 : Step(177): len = 484884, overlap = 201.25
PHY-3002 : Step(178): len = 488298, overlap = 196.625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000763057
PHY-3002 : Step(179): len = 488708, overlap = 185
PHY-3002 : Step(180): len = 491863, overlap = 180.562
PHY-3002 : Step(181): len = 499642, overlap = 166.125
PHY-3002 : Step(182): len = 507753, overlap = 155.906
PHY-3002 : Step(183): len = 508163, overlap = 154.844
PHY-3002 : Step(184): len = 506724, overlap = 153.656
PHY-3002 : Step(185): len = 505670, overlap = 155.188
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 81536, tnet num: 21965, tinst num: 19600, tnode num: 115252, tedge num: 128077.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.469623s wall, 1.468750s user + 0.000000s system = 1.468750s CPU (99.9%)

RUN-1004 : used memory is 566 MB, reserved memory is 542 MB, peak memory is 701 MB
OPT-1001 : Total overflow 518.22 peak overflow 3.75
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 317/21967.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 591872, over cnt = 2568(7%), over = 8688, worst = 24
PHY-1001 : End global iterations;  1.183484s wall, 1.843750s user + 0.015625s system = 1.859375s CPU (157.1%)

PHY-1001 : Congestion index: top1 = 60.86, top5 = 49.15, top10 = 44.37, top15 = 41.30.
PHY-1001 : End incremental global routing;  1.396933s wall, 2.046875s user + 0.031250s system = 2.078125s CPU (148.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21965 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.889852s wall, 0.843750s user + 0.046875s system = 0.890625s CPU (100.1%)

OPT-1001 : 23 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 61 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19513 has valid locations, 298 needs to be replaced
PHY-3001 : design contains 19875 instances, 5734 luts, 12611 seqs, 1424 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 526504
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17544/22242.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 611776, over cnt = 2641(7%), over = 8874, worst = 24
PHY-1001 : End global iterations;  0.208301s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (127.5%)

PHY-1001 : Congestion index: top1 = 61.36, top5 = 49.70, top10 = 45.01, top15 = 41.98.
PHY-3001 : End congestion estimation;  0.557698s wall, 0.609375s user + 0.000000s system = 0.609375s CPU (109.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 82497, tnet num: 22240, tinst num: 19875, tnode num: 116551, tedge num: 129449.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.407914s wall, 1.359375s user + 0.031250s system = 1.390625s CPU (98.8%)

RUN-1004 : used memory is 613 MB, reserved memory is 609 MB, peak memory is 704 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22240 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.330059s wall, 2.265625s user + 0.046875s system = 2.312500s CPU (99.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(186): len = 525818, overlap = 2.5
PHY-3002 : Step(187): len = 526594, overlap = 2.625
PHY-3002 : Step(188): len = 527328, overlap = 2.6875
PHY-3002 : Step(189): len = 527961, overlap = 2.6875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17572/22242.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 608944, over cnt = 2672(7%), over = 9016, worst = 25
PHY-1001 : End global iterations;  0.187399s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (141.7%)

PHY-1001 : Congestion index: top1 = 62.65, top5 = 50.16, top10 = 45.21, top15 = 42.16.
PHY-3001 : End congestion estimation;  0.410376s wall, 0.468750s user + 0.015625s system = 0.484375s CPU (118.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22240 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.901759s wall, 0.890625s user + 0.015625s system = 0.906250s CPU (100.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000756917
PHY-3002 : Step(190): len = 528269, overlap = 157.719
PHY-3002 : Step(191): len = 528796, overlap = 157.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00151383
PHY-3002 : Step(192): len = 529062, overlap = 157.75
PHY-3002 : Step(193): len = 529356, overlap = 157.375
PHY-3001 : Final: Len = 529356, Over = 157.375
PHY-3001 : End incremental placement;  5.231203s wall, 5.250000s user + 0.187500s system = 5.437500s CPU (103.9%)

OPT-1001 : Total overflow 523.44 peak overflow 3.75
OPT-1001 : End high-fanout net optimization;  8.004371s wall, 8.796875s user + 0.265625s system = 9.062500s CPU (113.2%)

OPT-1001 : Current memory(MB): used = 708, reserve = 689, peak = 725.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17585/22242.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 611840, over cnt = 2588(7%), over = 8332, worst = 24
PHY-1002 : len = 649640, over cnt = 1863(5%), over = 4773, worst = 23
PHY-1002 : len = 690408, over cnt = 816(2%), over = 1736, worst = 23
PHY-1002 : len = 711872, over cnt = 259(0%), over = 510, worst = 15
PHY-1002 : len = 721384, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.222610s wall, 1.812500s user + 0.015625s system = 1.828125s CPU (149.5%)

PHY-1001 : Congestion index: top1 = 51.81, top5 = 45.13, top10 = 42.01, top15 = 40.03.
OPT-1001 : End congestion update;  1.462540s wall, 2.062500s user + 0.015625s system = 2.078125s CPU (142.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22240 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.797510s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.9%)

OPT-0007 : Start: WNS 3869 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.266308s wall, 2.859375s user + 0.015625s system = 2.875000s CPU (126.9%)

OPT-1001 : Current memory(MB): used = 685, reserve = 672, peak = 725.
OPT-1001 : End physical optimization;  12.042665s wall, 13.437500s user + 0.312500s system = 13.750000s CPU (114.2%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5734 LUT to BLE ...
SYN-4008 : Packed 5734 LUT and 2860 SEQ to BLE.
SYN-4003 : Packing 9751 remaining SEQ's ...
SYN-4005 : Packed 3329 SEQ with LUT/SLICE
SYN-4006 : 56 single LUT's are left
SYN-4006 : 6422 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 12156/13808 primitive instances ...
PHY-3001 : End packing;  2.691812s wall, 2.703125s user + 0.000000s system = 2.703125s CPU (100.4%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8212 instances
RUN-1001 : 4052 mslices, 4052 lslices, 61 pads, 42 brams, 0 dsps
RUN-1001 : There are total 19427 nets
RUN-1001 : 13546 nets have 2 pins
RUN-1001 : 4496 nets have [3 - 5] pins
RUN-1001 : 860 nets have [6 - 10] pins
RUN-1001 : 365 nets have [11 - 20] pins
RUN-1001 : 150 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
PHY-3001 : design contains 8210 instances, 8104 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Cell area utilization is 86%
PHY-3001 : After packing: Len = 544406, Over = 393.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7923/19427.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 682272, over cnt = 1599(4%), over = 2600, worst = 9
PHY-1002 : len = 687584, over cnt = 1106(3%), over = 1588, worst = 7
PHY-1002 : len = 697480, over cnt = 569(1%), over = 771, worst = 5
PHY-1002 : len = 706416, over cnt = 163(0%), over = 220, worst = 5
PHY-1002 : len = 710288, over cnt = 8(0%), over = 13, worst = 4
PHY-1001 : End global iterations;  1.210129s wall, 1.812500s user + 0.031250s system = 1.843750s CPU (152.4%)

PHY-1001 : Congestion index: top1 = 53.97, top5 = 45.65, top10 = 41.81, top15 = 39.65.
PHY-3001 : End congestion estimation;  1.507777s wall, 2.093750s user + 0.031250s system = 2.125000s CPU (140.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68044, tnet num: 19425, tinst num: 8210, tnode num: 92812, tedge num: 112074.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.633632s wall, 1.609375s user + 0.031250s system = 1.640625s CPU (100.4%)

RUN-1004 : used memory is 605 MB, reserved memory is 598 MB, peak memory is 725 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19425 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.457963s wall, 2.437500s user + 0.031250s system = 2.468750s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.8327e-05
PHY-3002 : Step(194): len = 548371, overlap = 355.75
PHY-3002 : Step(195): len = 546948, overlap = 364.25
PHY-3002 : Step(196): len = 545909, overlap = 371.5
PHY-3002 : Step(197): len = 546420, overlap = 390.5
PHY-3002 : Step(198): len = 544877, overlap = 390.75
PHY-3002 : Step(199): len = 545024, overlap = 394.25
PHY-3002 : Step(200): len = 542038, overlap = 396.5
PHY-3002 : Step(201): len = 539973, overlap = 390
PHY-3002 : Step(202): len = 537191, overlap = 398
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.66539e-05
PHY-3002 : Step(203): len = 541209, overlap = 382.25
PHY-3002 : Step(204): len = 544901, overlap = 376
PHY-3002 : Step(205): len = 545202, overlap = 375.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000193308
PHY-3002 : Step(206): len = 555297, overlap = 361.5
PHY-3002 : Step(207): len = 565202, overlap = 336
PHY-3002 : Step(208): len = 561749, overlap = 338.75
PHY-3002 : Step(209): len = 559380, overlap = 330.75
PHY-3002 : Step(210): len = 559702, overlap = 327
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.594821s wall, 0.687500s user + 0.640625s system = 1.328125s CPU (223.3%)

PHY-3001 : Trial Legalized: Len = 665474
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 615/19427.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 757240, over cnt = 2457(6%), over = 4082, worst = 8
PHY-1002 : len = 773192, over cnt = 1476(4%), over = 2110, worst = 7
PHY-1002 : len = 791728, over cnt = 558(1%), over = 751, worst = 4
PHY-1002 : len = 802688, over cnt = 134(0%), over = 150, worst = 4
PHY-1002 : len = 805480, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.840057s wall, 3.000000s user + 0.031250s system = 3.031250s CPU (164.7%)

PHY-1001 : Congestion index: top1 = 52.13, top5 = 46.83, top10 = 44.08, top15 = 42.20.
PHY-3001 : End congestion estimation;  2.171518s wall, 3.328125s user + 0.031250s system = 3.359375s CPU (154.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19425 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.847175s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (99.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000174266
PHY-3002 : Step(211): len = 624512, overlap = 89
PHY-3002 : Step(212): len = 606321, overlap = 134.25
PHY-3002 : Step(213): len = 596202, overlap = 165
PHY-3002 : Step(214): len = 587549, overlap = 203.5
PHY-3002 : Step(215): len = 583532, overlap = 219.25
PHY-3002 : Step(216): len = 580941, overlap = 243.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000348532
PHY-3002 : Step(217): len = 584886, overlap = 238.75
PHY-3002 : Step(218): len = 589291, overlap = 232.75
PHY-3002 : Step(219): len = 590569, overlap = 238.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(220): len = 593305, overlap = 234.5
PHY-3002 : Step(221): len = 599008, overlap = 232.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.027443s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (113.9%)

PHY-3001 : Legalized: Len = 638910, Over = 0
PHY-3001 : Spreading special nets. 39 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.070125s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (89.1%)

PHY-3001 : 50 instances has been re-located, deltaX = 19, deltaY = 22, maxDist = 2.
PHY-3001 : Final: Len = 639512, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68044, tnet num: 19425, tinst num: 8210, tnode num: 92812, tedge num: 112074.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.887595s wall, 1.890625s user + 0.000000s system = 1.890625s CPU (100.2%)

RUN-1004 : used memory is 631 MB, reserved memory is 626 MB, peak memory is 725 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3915/19427.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 742032, over cnt = 2280(6%), over = 3499, worst = 7
PHY-1002 : len = 752616, over cnt = 1468(4%), over = 2016, worst = 7
PHY-1002 : len = 767928, over cnt = 644(1%), over = 849, worst = 5
PHY-1002 : len = 776248, over cnt = 260(0%), over = 328, worst = 5
PHY-1002 : len = 782008, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.590304s wall, 2.687500s user + 0.046875s system = 2.734375s CPU (171.9%)

PHY-1001 : Congestion index: top1 = 50.43, top5 = 44.89, top10 = 42.33, top15 = 40.65.
PHY-1001 : End incremental global routing;  1.883247s wall, 2.984375s user + 0.046875s system = 3.031250s CPU (161.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19425 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.851932s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (99.0%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 61 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8145 has valid locations, 9 needs to be replaced
PHY-3001 : design contains 8218 instances, 8112 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 642729
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17642/19434.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 786312, over cnt = 19(0%), over = 30, worst = 5
PHY-1002 : len = 786288, over cnt = 16(0%), over = 16, worst = 1
PHY-1002 : len = 786376, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 786424, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.507508s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (101.6%)

PHY-1001 : Congestion index: top1 = 50.45, top5 = 44.91, top10 = 42.43, top15 = 40.76.
PHY-3001 : End congestion estimation;  0.786717s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (101.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68099, tnet num: 19432, tinst num: 8218, tnode num: 92875, tedge num: 112136.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.830312s wall, 1.828125s user + 0.000000s system = 1.828125s CPU (99.9%)

RUN-1004 : used memory is 668 MB, reserved memory is 663 MB, peak memory is 725 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19432 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.652770s wall, 2.656250s user + 0.000000s system = 2.656250s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(222): len = 642512, overlap = 0.25
PHY-3002 : Step(223): len = 642407, overlap = 0.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17639/19434.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 785216, over cnt = 14(0%), over = 22, worst = 4
PHY-1002 : len = 785216, over cnt = 12(0%), over = 12, worst = 1
PHY-1002 : len = 785264, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 785312, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 785344, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.636949s wall, 0.656250s user + 0.015625s system = 0.671875s CPU (105.5%)

PHY-1001 : Congestion index: top1 = 50.52, top5 = 44.98, top10 = 42.42, top15 = 40.73.
PHY-3001 : End congestion estimation;  0.901862s wall, 0.921875s user + 0.015625s system = 0.937500s CPU (104.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19432 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.053904s wall, 1.046875s user + 0.000000s system = 1.046875s CPU (99.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000565312
PHY-3002 : Step(224): len = 642281, overlap = 1
PHY-3002 : Step(225): len = 642233, overlap = 1
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005618s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (278.1%)

PHY-3001 : Legalized: Len = 642295, Over = 0
PHY-3001 : End spreading;  0.059757s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.6%)

PHY-3001 : Final: Len = 642295, Over = 0
PHY-3001 : End incremental placement;  5.990106s wall, 5.968750s user + 0.093750s system = 6.062500s CPU (101.2%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  9.182569s wall, 10.375000s user + 0.140625s system = 10.515625s CPU (114.5%)

OPT-1001 : Current memory(MB): used = 715, reserve = 697, peak = 725.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17642/19434.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 785544, over cnt = 19(0%), over = 29, worst = 4
PHY-1002 : len = 785504, over cnt = 10(0%), over = 15, worst = 3
PHY-1002 : len = 785744, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 785784, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 785816, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.651807s wall, 0.640625s user + 0.000000s system = 0.640625s CPU (98.3%)

PHY-1001 : Congestion index: top1 = 50.52, top5 = 45.03, top10 = 42.45, top15 = 40.74.
OPT-1001 : End congestion update;  0.929828s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (99.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19432 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.688540s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (99.8%)

OPT-0007 : Start: WNS 3870 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.622604s wall, 1.625000s user + 0.000000s system = 1.625000s CPU (100.1%)

OPT-1001 : Current memory(MB): used = 715, reserve = 697, peak = 725.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19432 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.676778s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (99.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17650/19434.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 785816, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.111872s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (111.7%)

PHY-1001 : Congestion index: top1 = 50.52, top5 = 45.03, top10 = 42.45, top15 = 40.74.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19432 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.703093s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (100.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3870 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 50.103448
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3870ps with logic level 4 
OPT-1001 : End physical optimization;  14.698890s wall, 15.875000s user + 0.140625s system = 16.015625s CPU (109.0%)

RUN-1003 : finish command "place" in  77.876782s wall, 139.609375s user + 8.578125s system = 148.187500s CPU (190.3%)

RUN-1004 : used memory is 632 MB, reserved memory is 615 MB, peak memory is 725 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.553588s wall, 2.640625s user + 0.000000s system = 2.640625s CPU (170.0%)

RUN-1004 : used memory is 632 MB, reserved memory is 616 MB, peak memory is 725 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8220 instances
RUN-1001 : 4052 mslices, 4060 lslices, 61 pads, 42 brams, 0 dsps
RUN-1001 : There are total 19434 nets
RUN-1001 : 13545 nets have 2 pins
RUN-1001 : 4496 nets have [3 - 5] pins
RUN-1001 : 863 nets have [6 - 10] pins
RUN-1001 : 371 nets have [11 - 20] pins
RUN-1001 : 149 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68099, tnet num: 19432, tinst num: 8218, tnode num: 92875, tedge num: 112136.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.631527s wall, 1.640625s user + 0.000000s system = 1.640625s CPU (100.6%)

RUN-1004 : used memory is 616 MB, reserved memory is 601 MB, peak memory is 725 MB
PHY-1001 : 4052 mslices, 4060 lslices, 61 pads, 42 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19432 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 722864, over cnt = 2446(6%), over = 4024, worst = 8
PHY-1002 : len = 738344, over cnt = 1426(4%), over = 2094, worst = 8
PHY-1002 : len = 756120, over cnt = 611(1%), over = 867, worst = 6
PHY-1002 : len = 770488, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 770696, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.606134s wall, 2.812500s user + 0.078125s system = 2.890625s CPU (180.0%)

PHY-1001 : Congestion index: top1 = 49.05, top5 = 44.30, top10 = 41.68, top15 = 40.03.
PHY-1001 : End global routing;  1.927654s wall, 3.140625s user + 0.078125s system = 3.218750s CPU (167.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 699, reserve = 688, peak = 725.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net DATA/DIV_Dtemp/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 969, reserve = 959, peak = 969.
PHY-1001 : End build detailed router design. 4.463473s wall, 4.421875s user + 0.046875s system = 4.468750s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 189120, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.742980s wall, 0.718750s user + 0.015625s system = 0.734375s CPU (98.8%)

PHY-1001 : Current memory(MB): used = 1004, reserve = 995, peak = 1004.
PHY-1001 : End phase 1; 0.749891s wall, 0.734375s user + 0.015625s system = 0.750000s CPU (100.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 55% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.83203e+06, over cnt = 1320(0%), over = 1323, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1020, reserve = 1011, peak = 1020.
PHY-1001 : End initial routed; 18.626074s wall, 50.937500s user + 0.359375s system = 51.296875s CPU (275.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18229(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.603   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.131911s wall, 3.125000s user + 0.000000s system = 3.125000s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 1032, reserve = 1022, peak = 1032.
PHY-1001 : End phase 2; 21.758126s wall, 54.062500s user + 0.359375s system = 54.421875s CPU (250.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.83203e+06, over cnt = 1320(0%), over = 1323, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.219352s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (106.8%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.81846e+06, over cnt = 471(0%), over = 471, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.761209s wall, 1.406250s user + 0.000000s system = 1.406250s CPU (184.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.8192e+06, over cnt = 83(0%), over = 83, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.398752s wall, 0.500000s user + 0.000000s system = 0.500000s CPU (125.4%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.8206e+06, over cnt = 9(0%), over = 9, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.238726s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (117.8%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.82081e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.151275s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (103.3%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.82086e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.141873s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (99.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18229(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.265   |   0.000   |   0   
RUN-1001 :   Hold   |   0.156   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.205709s wall, 3.218750s user + 0.000000s system = 3.218750s CPU (100.4%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 318 feed throughs used by 270 nets
PHY-1001 : End commit to database; 2.203384s wall, 2.171875s user + 0.031250s system = 2.203125s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1122, reserve = 1116, peak = 1122.
PHY-1001 : End phase 3; 7.822655s wall, 8.578125s user + 0.031250s system = 8.609375s CPU (110.1%)

PHY-1003 : Routed, final wirelength = 1.82086e+06
PHY-1001 : Current memory(MB): used = 1127, reserve = 1120, peak = 1127.
PHY-1001 : End export database. 0.057210s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.2%)

PHY-1001 : End detail routing;  35.250743s wall, 68.250000s user + 0.453125s system = 68.703125s CPU (194.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68099, tnet num: 19432, tinst num: 8218, tnode num: 92875, tedge num: 112136.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.606205s wall, 1.609375s user + 0.000000s system = 1.609375s CPU (100.2%)

RUN-1004 : used memory is 1061 MB, reserved memory is 1059 MB, peak memory is 1127 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  42.752447s wall, 76.906250s user + 0.578125s system = 77.484375s CPU (181.2%)

RUN-1004 : used memory is 1062 MB, reserved memory is 1061 MB, peak memory is 1127 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        67
  #input                   26
  #output                  39
  #inout                    2

Utilization Statistics
#lut                     8725   out of  19600   44.52%
#reg                    12708   out of  19600   64.84%
#le                     15109
  #lut only              2401   out of  15109   15.89%
  #reg only              6384   out of  15109   42.25%
  #lut&reg               6324   out of  15109   41.86%
#dsp                        0   out of     29    0.00%
#bram                      42   out of     64   65.62%
  #bram9k                  42
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       61   out of    188   32.45%
  #ireg                     9
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet              Type               DriverType         Driver                    Fanout
#1        DATA/DIV_Dtemp/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6950
#2        config_inst_syn_9     GCLK               config             config_inst.jtck          176
#3        clk_in_dup_1          GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         L4        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          NONE       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D14        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    GNSS_RMC       OUTPUT         L3        LVCMOS33           8            NONE           NONE       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         T6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT        D16        LVCMOS33           8            NONE           NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15109  |7301    |1424    |12750   |42      |0       |
|  AnyFog_dataX                      |AnyFog          |205    |80      |22      |169     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |83     |51      |22      |47      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |204    |75      |22      |169     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |82     |56      |22      |47      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |204    |104     |22      |168     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |83     |53      |22      |47      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |3495   |948     |34      |3411    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |735    |92      |5       |723     |0       |0       |
|    STADOP_com2                     |STADOP          |554    |55      |0       |547     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |42      |14      |42      |0       |0       |
|    head_com2                       |uniheading      |263    |75      |5       |253     |0       |0       |
|    rmc_com2                        |Gprmc           |161    |76      |0       |151     |0       |0       |
|    uart_com2                       |Agrica          |1426   |317     |10      |1404    |0       |0       |
|  DATA                              |Data_Processing |8615   |4412    |1062    |6940    |0       |0       |
|    DIV_Dtemp                       |Divider         |823    |307     |84      |700     |0       |0       |
|    DIV_Utemp                       |Divider         |612    |310     |84      |486     |0       |0       |
|    DIV_accX                        |Divider         |624    |313     |84      |482     |0       |0       |
|    DIV_accY                        |Divider         |675    |317     |111     |506     |0       |0       |
|    DIV_accZ                        |Divider         |648    |403     |132     |445     |0       |0       |
|    DIV_rateX                       |Divider         |682    |436     |132     |476     |0       |0       |
|    DIV_rateY                       |Divider         |562    |343     |132     |359     |0       |0       |
|    DIV_rateZ                       |Divider         |561    |389     |132     |355     |0       |0       |
|    genclk                          |genclk          |90     |62      |20      |56      |0       |0       |
|  FMC                               |FMC_Ctrl        |413    |353     |43      |332     |0       |0       |
|  IIC                               |I2C_master      |293    |247     |11      |265     |0       |0       |
|  IMU_CTRL                          |SCHA634         |893    |643     |61      |732     |0       |0       |
|    CtrlData                        |CtrlData        |460    |409     |47      |338     |0       |0       |
|      usms                          |Time_1ms        |26     |21      |5       |18      |0       |0       |
|    SPIM                            |SPI_SCHA634     |433    |234     |14      |394     |0       |0       |
|  POWER                             |POWER_EN        |97     |47      |38      |34      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |669    |392     |109     |467     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |669    |392     |109     |467     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |300    |152     |0       |283     |0       |0       |
|        reg_inst                    |register        |298    |150     |0       |281     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |369    |240     |109     |184     |0       |0       |
|        bus_inst                    |bus_top         |148    |95      |52      |58      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |25     |15      |10      |9       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |3      |3       |0       |3       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |16     |10      |6       |7       |0       |0       |
|          BUS_DETECTOR[5]$bus_nodes |bus_det         |50     |32      |18      |18      |0       |0       |
|          BUS_DETECTOR[6]$bus_nodes |bus_det         |52     |33      |18      |19      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |135    |98      |29      |87      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13483  
    #2          2       3561   
    #3          3        674   
    #4          4        261   
    #5        5-10       907   
    #6        11-50      460   
    #7       51-100      16    
    #8       101-500      4    
    #9        >500        2    
  Average     2.14             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  1.816999s wall, 3.171875s user + 0.000000s system = 3.171875s CPU (174.6%)

RUN-1004 : used memory is 1062 MB, reserved memory is 1061 MB, peak memory is 1127 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68099, tnet num: 19432, tinst num: 8218, tnode num: 92875, tedge num: 112136.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.595577s wall, 1.593750s user + 0.000000s system = 1.593750s CPU (99.9%)

RUN-1004 : used memory is 1064 MB, reserved memory is 1063 MB, peak memory is 1127 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19432 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.294844s wall, 1.296875s user + 0.000000s system = 1.296875s CPU (100.2%)

RUN-1004 : used memory is 1070 MB, reserved memory is 1067 MB, peak memory is 1127 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 8f169df8acfcb835781910b1ac29267cd80e16d8a5e1b8cf9d032ec93c0a2865 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8218
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19434, pip num: 150849
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 318
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3256 valid insts, and 418895 bits set as '1'.
BIT-1004 : the usercode register value: 00000000110101000001111110010110
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  15.030982s wall, 107.578125s user + 0.296875s system = 107.875000s CPU (717.7%)

RUN-1004 : used memory is 1196 MB, reserved memory is 1182 MB, peak memory is 1311 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250507_092607.log"

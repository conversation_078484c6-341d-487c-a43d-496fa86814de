============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Jul 10 16:22:41 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(520)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.679064s wall, 1.546875s user + 4.109375s system = 5.656250s CPU (99.6%)

RUN-1004 : used memory is 79 MB, reserved memory is 40 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  2.012952s wall, 1.937500s user + 0.062500s system = 2.000000s CPU (99.4%)

RUN-1004 : used memory is 301 MB, reserved memory is 269 MB, peak memory is 304 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "read_sdc -ip Asys_fifo8x8 ../../al_ip/Asys_fifo8x8.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 95176475279360"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 95176475279360"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  9.6999999999999993 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 9.6999999999999993"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  9.6999999999999993 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 9.6999999999999993"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 4 view nodes, 26 trigger nets, 26 data nets.
KIT-1004 : Chipwatcher code = 0110100010110101
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=4,BUS_DIN_NUM=26,BUS_CTRL_NUM=68,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=90) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=90) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=4,BUS_DIN_NUM=26,BUS_CTRL_NUM=68,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=4,BUS_DIN_NUM=26,BUS_CTRL_NUM=68,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=4,BUS_DIN_NUM=26,BUS_CTRL_NUM=68,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=90)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=90)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=4,BUS_DIN_NUM=26,BUS_CTRL_NUM=68,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=4,BUS_DIN_NUM=26,BUS_CTRL_NUM=68,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22197/18 useful/useless nets, 19153/10 useful/useless insts
SYN-1016 : Merged 25 instances.
SYN-1032 : 21941/20 useful/useless nets, 19495/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 2 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 2 mux instances.
SYN-1015 : Optimize round 1, 307 better
SYN-1014 : Optimize round 2
SYN-1032 : 21710/30 useful/useless nets, 19264/32 useful/useless insts
SYN-1015 : Optimize round 2, 64 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.463277s wall, 2.406250s user + 0.062500s system = 2.468750s CPU (100.2%)

RUN-1004 : used memory is 326 MB, reserved memory is 292 MB, peak memory is 328 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 21734/157 useful/useless nets, 19311/31 useful/useless insts
SYN-1016 : Merged 21 instances.
SYN-2571 : Optimize after map_dsp, round 1, 209 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 24 instances.
SYN-2501 : Optimize round 1, 50 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 14 macro adder
SYN-1019 : Optimized 9 mux instances.
SYN-1016 : Merged 10 instances.
SYN-1032 : 22117/5 useful/useless nets, 19694/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80206, tnet num: 22117, tinst num: 19693, tnode num: 112836, tedge num: 125299.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.294697s wall, 1.281250s user + 0.015625s system = 1.296875s CPU (100.2%)

RUN-1004 : used memory is 463 MB, reserved memory is 431 MB, peak memory is 463 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22117 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 184 (3.58), #lev = 7 (1.95)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 184 (3.58), #lev = 7 (1.95)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 437 instances into 184 LUTs, name keeping = 74%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 318 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 108 adder to BLE ...
SYN-4008 : Packed 108 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.749552s wall, 4.687500s user + 0.078125s system = 4.765625s CPU (100.3%)

RUN-1004 : used memory is 349 MB, reserved memory is 312 MB, peak memory is 570 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.543017s wall, 7.390625s user + 0.156250s system = 7.546875s CPU (100.1%)

RUN-1004 : used memory is 349 MB, reserved memory is 313 MB, peak memory is 570 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/uart_com2/AGRIC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/uart_com2/AGRIC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/uart_com2/AGRIC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/uart_com2/AGRIC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/uart_com2/AGRIC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/uart_com2/AGRIC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/uart_com2/AGRIC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/uart_com2/AGRIC_data[0]
SYN-5055 WARNING: The kept net COM3/rmc_com3/GPRMC_end will be merged to another kept net COM3/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM3/rmc_com3/GPRMC_sta will be merged to another kept net COM3/GNRMC/GPRMC_sta
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (199 clock/control pins, 0 other pins).
SYN-4027 : Net COM3/GNRMC/u_fifo/wr_to_rd_cross_inst/secondary_clk_i is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net COM3/GNRMC/u_fifo/wr_to_rd_cross_inst/secondary_clk_i as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19009 instances
RUN-0007 : 5410 luts, 12066 seqs, 944 mslices, 498 lslices, 60 pads, 26 brams, 0 dsps
RUN-1001 : There are total 21456 nets
RUN-1001 : 16196 nets have 2 pins
RUN-1001 : 4099 nets have [3 - 5] pins
RUN-1001 : 821 nets have [6 - 10] pins
RUN-1001 : 216 nets have [11 - 20] pins
RUN-1001 : 105 nets have [21 - 99] pins
RUN-1001 : 19 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4769     
RUN-1001 :   No   |  No   |  Yes  |     705     
RUN-1001 :   No   |  Yes  |  No   |     85      
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     373     
RUN-1001 :   Yes  |  Yes  |  No   |     51      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 124
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19007 instances, 5410 luts, 12066 seqs, 1442 slices, 287 macros(1442 instances: 944 mslices 498 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 78753, tnet num: 21454, tinst num: 19007, tnode num: 111097, tedge num: 123765.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.306157s wall, 1.281250s user + 0.031250s system = 1.312500s CPU (100.5%)

RUN-1004 : used memory is 522 MB, reserved memory is 494 MB, peak memory is 570 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21454 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.245562s wall, 2.203125s user + 0.046875s system = 2.250000s CPU (100.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.62031e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19007.
PHY-3001 : Level 1 #clusters 2092.
PHY-3001 : End clustering;  0.171378s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (182.3%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 857318, overlap = 639.781
PHY-3002 : Step(2): len = 787218, overlap = 671.469
PHY-3002 : Step(3): len = 519699, overlap = 809.531
PHY-3002 : Step(4): len = 438908, overlap = 878.875
PHY-3002 : Step(5): len = 352179, overlap = 961.188
PHY-3002 : Step(6): len = 317426, overlap = 1000.03
PHY-3002 : Step(7): len = 258814, overlap = 1099.72
PHY-3002 : Step(8): len = 234282, overlap = 1138.41
PHY-3002 : Step(9): len = 211429, overlap = 1191.16
PHY-3002 : Step(10): len = 196264, overlap = 1207.62
PHY-3002 : Step(11): len = 178725, overlap = 1237.25
PHY-3002 : Step(12): len = 165400, overlap = 1261.75
PHY-3002 : Step(13): len = 151428, overlap = 1284.03
PHY-3002 : Step(14): len = 140771, overlap = 1301.69
PHY-3002 : Step(15): len = 130100, overlap = 1304.53
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.23465e-06
PHY-3002 : Step(16): len = 131378, overlap = 1282.22
PHY-3002 : Step(17): len = 173347, overlap = 1224.53
PHY-3002 : Step(18): len = 189300, overlap = 1101.06
PHY-3002 : Step(19): len = 194390, overlap = 990.656
PHY-3002 : Step(20): len = 190443, overlap = 975.812
PHY-3002 : Step(21): len = 184656, overlap = 976.75
PHY-3002 : Step(22): len = 179652, overlap = 974.531
PHY-3002 : Step(23): len = 176309, overlap = 950.406
PHY-3002 : Step(24): len = 172680, overlap = 950.875
PHY-3002 : Step(25): len = 169689, overlap = 950.406
PHY-3002 : Step(26): len = 167588, overlap = 956.719
PHY-3002 : Step(27): len = 166942, overlap = 946.062
PHY-3002 : Step(28): len = 165892, overlap = 957.531
PHY-3002 : Step(29): len = 164993, overlap = 952.031
PHY-3002 : Step(30): len = 164119, overlap = 970.688
PHY-3002 : Step(31): len = 164491, overlap = 992.594
PHY-3002 : Step(32): len = 163105, overlap = 987.062
PHY-3002 : Step(33): len = 162789, overlap = 982.844
PHY-3002 : Step(34): len = 161483, overlap = 989.781
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.4693e-06
PHY-3002 : Step(35): len = 170799, overlap = 963.969
PHY-3002 : Step(36): len = 185184, overlap = 884.312
PHY-3002 : Step(37): len = 189420, overlap = 840.219
PHY-3002 : Step(38): len = 191736, overlap = 825.219
PHY-3002 : Step(39): len = 191461, overlap = 816.156
PHY-3002 : Step(40): len = 191187, overlap = 818.531
PHY-3002 : Step(41): len = 188836, overlap = 829.688
PHY-3002 : Step(42): len = 188507, overlap = 830.938
PHY-3002 : Step(43): len = 187439, overlap = 825.938
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.93861e-06
PHY-3002 : Step(44): len = 197715, overlap = 778.906
PHY-3002 : Step(45): len = 212191, overlap = 693.969
PHY-3002 : Step(46): len = 216621, overlap = 624.031
PHY-3002 : Step(47): len = 218445, overlap = 613.875
PHY-3002 : Step(48): len = 218463, overlap = 626.5
PHY-3002 : Step(49): len = 219233, overlap = 617.625
PHY-3002 : Step(50): len = 217999, overlap = 613.812
PHY-3002 : Step(51): len = 216998, overlap = 610.844
PHY-3002 : Step(52): len = 215138, overlap = 618.75
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 9.87721e-06
PHY-3002 : Step(53): len = 226611, overlap = 574.875
PHY-3002 : Step(54): len = 240608, overlap = 493.906
PHY-3002 : Step(55): len = 244497, overlap = 476.25
PHY-3002 : Step(56): len = 247008, overlap = 465.094
PHY-3002 : Step(57): len = 246592, overlap = 464.406
PHY-3002 : Step(58): len = 244870, overlap = 468.562
PHY-3002 : Step(59): len = 243269, overlap = 485.188
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.97544e-05
PHY-3002 : Step(60): len = 254345, overlap = 444.844
PHY-3002 : Step(61): len = 267758, overlap = 432.906
PHY-3002 : Step(62): len = 272183, overlap = 400.625
PHY-3002 : Step(63): len = 274118, overlap = 385.906
PHY-3002 : Step(64): len = 272617, overlap = 358.562
PHY-3002 : Step(65): len = 270233, overlap = 354.656
PHY-3002 : Step(66): len = 268456, overlap = 336.062
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.95089e-05
PHY-3002 : Step(67): len = 277378, overlap = 346.406
PHY-3002 : Step(68): len = 287941, overlap = 318.156
PHY-3002 : Step(69): len = 292528, overlap = 292.906
PHY-3002 : Step(70): len = 294454, overlap = 281.906
PHY-3002 : Step(71): len = 293960, overlap = 267.438
PHY-3002 : Step(72): len = 292243, overlap = 272.688
PHY-3002 : Step(73): len = 290231, overlap = 276.125
PHY-3002 : Step(74): len = 290423, overlap = 259.344
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 7.90177e-05
PHY-3002 : Step(75): len = 297707, overlap = 261.062
PHY-3002 : Step(76): len = 304511, overlap = 249.906
PHY-3002 : Step(77): len = 306766, overlap = 253.812
PHY-3002 : Step(78): len = 308219, overlap = 251.906
PHY-3002 : Step(79): len = 306587, overlap = 258.594
PHY-3002 : Step(80): len = 305492, overlap = 259.406
PHY-3002 : Step(81): len = 303253, overlap = 269.25
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000151353
PHY-3002 : Step(82): len = 309353, overlap = 266.344
PHY-3002 : Step(83): len = 315319, overlap = 248.719
PHY-3002 : Step(84): len = 318826, overlap = 239.812
PHY-3002 : Step(85): len = 322674, overlap = 231.094
PHY-3002 : Step(86): len = 320936, overlap = 234.969
PHY-3002 : Step(87): len = 319648, overlap = 238.688
PHY-3002 : Step(88): len = 317051, overlap = 218.031
PHY-3002 : Step(89): len = 316714, overlap = 230.188
PHY-3002 : Step(90): len = 316081, overlap = 230.406
PHY-3002 : Step(91): len = 316339, overlap = 234.125
PHY-3002 : Step(92): len = 315272, overlap = 237.812
PHY-3002 : Step(93): len = 315454, overlap = 233.344
PHY-3002 : Step(94): len = 314806, overlap = 216.281
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000298664
PHY-3002 : Step(95): len = 318236, overlap = 208.531
PHY-3002 : Step(96): len = 322380, overlap = 201.688
PHY-3002 : Step(97): len = 322977, overlap = 195.406
PHY-3002 : Step(98): len = 323633, overlap = 173.969
PHY-3002 : Step(99): len = 323933, overlap = 163.375
PHY-3002 : Step(100): len = 324892, overlap = 181.344
PHY-3002 : Step(101): len = 325057, overlap = 182.781
PHY-3002 : Step(102): len = 323682, overlap = 193.906
PHY-3002 : Step(103): len = 323486, overlap = 205.562
PHY-3002 : Step(104): len = 323643, overlap = 215.875
PHY-3002 : Step(105): len = 323726, overlap = 221.656
PHY-3002 : Step(106): len = 323346, overlap = 213
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(107): len = 324443, overlap = 219.156
PHY-3002 : Step(108): len = 326056, overlap = 216.5
PHY-3002 : Step(109): len = 327002, overlap = 212
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.014416s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (108.4%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21456.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 423264, over cnt = 1134(3%), over = 5208, worst = 41
PHY-1001 : End global iterations;  0.840737s wall, 1.140625s user + 0.031250s system = 1.171875s CPU (139.4%)

PHY-1001 : Congestion index: top1 = 71.92, top5 = 51.04, top10 = 41.88, top15 = 36.54.
PHY-3001 : End congestion estimation;  1.091062s wall, 1.375000s user + 0.031250s system = 1.406250s CPU (128.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21454 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.977605s wall, 0.968750s user + 0.015625s system = 0.984375s CPU (100.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000120015
PHY-3002 : Step(110): len = 362782, overlap = 201.969
PHY-3002 : Step(111): len = 374546, overlap = 180.062
PHY-3002 : Step(112): len = 380224, overlap = 171.281
PHY-3002 : Step(113): len = 380778, overlap = 149.219
PHY-3002 : Step(114): len = 386478, overlap = 121.969
PHY-3002 : Step(115): len = 394652, overlap = 105.344
PHY-3002 : Step(116): len = 400138, overlap = 101.094
PHY-3002 : Step(117): len = 404383, overlap = 97.4062
PHY-3002 : Step(118): len = 409274, overlap = 103.875
PHY-3002 : Step(119): len = 413693, overlap = 107.594
PHY-3002 : Step(120): len = 415070, overlap = 100.031
PHY-3002 : Step(121): len = 416979, overlap = 101.25
PHY-3002 : Step(122): len = 419213, overlap = 109.125
PHY-3002 : Step(123): len = 422018, overlap = 114.062
PHY-3002 : Step(124): len = 423653, overlap = 112.219
PHY-3002 : Step(125): len = 426145, overlap = 113.906
PHY-3002 : Step(126): len = 428356, overlap = 117.469
PHY-3002 : Step(127): len = 431270, overlap = 120.969
PHY-3002 : Step(128): len = 432245, overlap = 120.688
PHY-3002 : Step(129): len = 434667, overlap = 117.375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000240029
PHY-3002 : Step(130): len = 433756, overlap = 115.969
PHY-3002 : Step(131): len = 435838, overlap = 109.938
PHY-3002 : Step(132): len = 438657, overlap = 107.219
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000480058
PHY-3002 : Step(133): len = 440152, overlap = 106.094
PHY-3002 : Step(134): len = 447171, overlap = 103.562
PHY-3002 : Step(135): len = 454245, overlap = 97.125
PHY-3002 : Step(136): len = 453218, overlap = 99.6875
PHY-3002 : Step(137): len = 454282, overlap = 102.312
PHY-3002 : Step(138): len = 454226, overlap = 103.875
PHY-3002 : Step(139): len = 453871, overlap = 101.938
PHY-3002 : Step(140): len = 455716, overlap = 95.6562
PHY-3002 : Step(141): len = 457727, overlap = 103.156
PHY-3002 : Step(142): len = 457357, overlap = 103.812
PHY-3002 : Step(143): len = 457756, overlap = 106.719
PHY-3002 : Step(144): len = 458084, overlap = 106.781
PHY-3002 : Step(145): len = 456615, overlap = 104.594
PHY-3002 : Step(146): len = 457948, overlap = 110.188
PHY-3002 : Step(147): len = 458419, overlap = 112.281
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000960117
PHY-3002 : Step(148): len = 457330, overlap = 108.219
PHY-3002 : Step(149): len = 459175, overlap = 105.594
PHY-3002 : Step(150): len = 461885, overlap = 104.906
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00175101
PHY-3002 : Step(151): len = 461918, overlap = 104.719
PHY-3002 : Step(152): len = 465616, overlap = 103.062
PHY-3002 : Step(153): len = 469860, overlap = 102.344
PHY-3002 : Step(154): len = 470698, overlap = 101.375
PHY-3002 : Step(155): len = 473487, overlap = 103.375
PHY-3002 : Step(156): len = 477628, overlap = 111.344
PHY-3002 : Step(157): len = 478517, overlap = 115.5
PHY-3002 : Step(158): len = 478534, overlap = 115.781
PHY-3002 : Step(159): len = 477726, overlap = 112.344
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 26/21456.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 537944, over cnt = 2103(5%), over = 10116, worst = 31
PHY-1001 : End global iterations;  1.102316s wall, 1.765625s user + 0.015625s system = 1.781250s CPU (161.6%)

PHY-1001 : Congestion index: top1 = 78.25, top5 = 58.08, top10 = 50.06, top15 = 45.27.
PHY-3001 : End congestion estimation;  1.406018s wall, 2.078125s user + 0.015625s system = 2.093750s CPU (148.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21454 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.026712s wall, 1.015625s user + 0.015625s system = 1.031250s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00010751
PHY-3002 : Step(160): len = 481336, overlap = 343.906
PHY-3002 : Step(161): len = 481533, overlap = 287.75
PHY-3002 : Step(162): len = 479646, overlap = 251.281
PHY-3002 : Step(163): len = 475447, overlap = 232.094
PHY-3002 : Step(164): len = 471898, overlap = 215.75
PHY-3002 : Step(165): len = 469885, overlap = 214.75
PHY-3002 : Step(166): len = 466351, overlap = 208.344
PHY-3002 : Step(167): len = 464141, overlap = 199.938
PHY-3002 : Step(168): len = 462937, overlap = 215.719
PHY-3002 : Step(169): len = 460762, overlap = 217.469
PHY-3002 : Step(170): len = 457990, overlap = 231.938
PHY-3002 : Step(171): len = 454467, overlap = 229.906
PHY-3002 : Step(172): len = 453155, overlap = 239.938
PHY-3002 : Step(173): len = 449838, overlap = 245.906
PHY-3002 : Step(174): len = 447451, overlap = 252.562
PHY-3002 : Step(175): len = 445642, overlap = 251.688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000215019
PHY-3002 : Step(176): len = 445478, overlap = 241.094
PHY-3002 : Step(177): len = 446897, overlap = 232.062
PHY-3002 : Step(178): len = 448200, overlap = 227.188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000430039
PHY-3002 : Step(179): len = 451059, overlap = 206.062
PHY-3002 : Step(180): len = 459972, overlap = 186.656
PHY-3002 : Step(181): len = 466946, overlap = 173.688
PHY-3002 : Step(182): len = 466878, overlap = 172.594
PHY-3002 : Step(183): len = 466731, overlap = 171.156
PHY-3002 : Step(184): len = 467120, overlap = 169.875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000860077
PHY-3002 : Step(185): len = 467974, overlap = 161.844
PHY-3002 : Step(186): len = 471618, overlap = 147.375
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 78753, tnet num: 21454, tinst num: 19007, tnode num: 111097, tedge num: 123765.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.647415s wall, 1.640625s user + 0.015625s system = 1.656250s CPU (100.5%)

RUN-1004 : used memory is 562 MB, reserved memory is 536 MB, peak memory is 693 MB
OPT-1001 : Total overflow 499.97 peak overflow 5.19
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 317/21456.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 548104, over cnt = 2392(6%), over = 8404, worst = 29
PHY-1001 : End global iterations;  1.380829s wall, 2.015625s user + 0.046875s system = 2.062500s CPU (149.4%)

PHY-1001 : Congestion index: top1 = 57.26, top5 = 48.07, top10 = 43.32, top15 = 40.23.
PHY-1001 : End incremental global routing;  1.648530s wall, 2.281250s user + 0.046875s system = 2.328125s CPU (141.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21454 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.087267s wall, 1.062500s user + 0.015625s system = 1.078125s CPU (99.2%)

OPT-1001 : 18 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 18926 has valid locations, 232 needs to be replaced
PHY-3001 : design contains 19221 instances, 5504 luts, 12186 seqs, 1442 slices, 287 macros(1442 instances: 944 mslices 498 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 486788
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17185/21670.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 560056, over cnt = 2402(6%), over = 8491, worst = 29
PHY-1001 : End global iterations;  0.203847s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (122.6%)

PHY-1001 : Congestion index: top1 = 57.24, top5 = 48.43, top10 = 43.61, top15 = 40.59.
PHY-3001 : End congestion estimation;  0.468514s wall, 0.500000s user + 0.015625s system = 0.515625s CPU (110.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 79448, tnet num: 21668, tinst num: 19221, tnode num: 112069, tedge num: 124727.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.625505s wall, 1.609375s user + 0.015625s system = 1.625000s CPU (100.0%)

RUN-1004 : used memory is 604 MB, reserved memory is 598 MB, peak memory is 695 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21668 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.748611s wall, 2.703125s user + 0.046875s system = 2.750000s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(187): len = 486813, overlap = 2.375
PHY-3002 : Step(188): len = 487937, overlap = 2.625
PHY-3002 : Step(189): len = 488427, overlap = 2.625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17220/21670.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 560520, over cnt = 2433(6%), over = 8554, worst = 29
PHY-1001 : End global iterations;  0.185158s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (135.0%)

PHY-1001 : Congestion index: top1 = 57.37, top5 = 48.47, top10 = 43.71, top15 = 40.71.
PHY-3001 : End congestion estimation;  0.464271s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (111.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21668 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.092235s wall, 1.093750s user + 0.000000s system = 1.093750s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000870301
PHY-3002 : Step(190): len = 488551, overlap = 150.094
PHY-3002 : Step(191): len = 489206, overlap = 149.969
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0017406
PHY-3002 : Step(192): len = 489495, overlap = 149.781
PHY-3002 : Step(193): len = 489944, overlap = 149.406
PHY-3001 : Final: Len = 489944, Over = 149.406
PHY-3001 : End incremental placement;  5.650577s wall, 6.187500s user + 0.281250s system = 6.468750s CPU (114.5%)

OPT-1001 : Total overflow 504.56 peak overflow 5.19
OPT-1001 : End high-fanout net optimization;  8.960903s wall, 10.250000s user + 0.359375s system = 10.609375s CPU (118.4%)

OPT-1001 : Current memory(MB): used = 697, reserve = 677, peak = 713.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17211/21670.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 561544, over cnt = 2370(6%), over = 8029, worst = 29
PHY-1002 : len = 604456, over cnt = 1584(4%), over = 3902, worst = 21
PHY-1002 : len = 637832, over cnt = 723(2%), over = 1500, worst = 15
PHY-1002 : len = 656424, over cnt = 180(0%), over = 284, worst = 9
PHY-1002 : len = 661824, over cnt = 3(0%), over = 3, worst = 1
PHY-1001 : End global iterations;  1.277432s wall, 2.062500s user + 0.031250s system = 2.093750s CPU (163.9%)

PHY-1001 : Congestion index: top1 = 50.04, top5 = 43.73, top10 = 40.72, top15 = 38.69.
OPT-1001 : End congestion update;  1.555058s wall, 2.328125s user + 0.031250s system = 2.359375s CPU (151.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 21668 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.917670s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (100.5%)

OPT-0007 : Start: WNS 3869 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.479315s wall, 3.250000s user + 0.031250s system = 3.281250s CPU (132.3%)

OPT-1001 : Current memory(MB): used = 698, reserve = 677, peak = 713.
OPT-1001 : End physical optimization;  13.423979s wall, 15.593750s user + 0.421875s system = 16.015625s CPU (119.3%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5504 LUT to BLE ...
SYN-4008 : Packed 5504 LUT and 2662 SEQ to BLE.
SYN-4003 : Packing 9524 remaining SEQ's ...
SYN-4005 : Packed 3284 SEQ with LUT/SLICE
SYN-4006 : 67 single LUT's are left
SYN-4006 : 6240 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11744/13523 primitive instances ...
PHY-3001 : End packing;  2.998340s wall, 2.984375s user + 0.015625s system = 3.000000s CPU (100.1%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 7964 instances
RUN-1001 : 3937 mslices, 3936 lslices, 60 pads, 26 brams, 0 dsps
RUN-1001 : There are total 19076 nets
RUN-1001 : 13486 nets have 2 pins
RUN-1001 : 4210 nets have [3 - 5] pins
RUN-1001 : 886 nets have [6 - 10] pins
RUN-1001 : 351 nets have [11 - 20] pins
RUN-1001 : 134 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 7962 instances, 7873 slices, 287 macros(1442 instances: 944 mslices 498 lslices)
PHY-3001 : Cell area utilization is 84%
PHY-3001 : After packing: Len = 505030, Over = 370
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7702/19076.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 628872, over cnt = 1471(4%), over = 2320, worst = 9
PHY-1002 : len = 635792, over cnt = 848(2%), over = 1104, worst = 9
PHY-1002 : len = 642280, over cnt = 451(1%), over = 562, worst = 5
PHY-1002 : len = 650480, over cnt = 84(0%), over = 92, worst = 2
PHY-1002 : len = 652920, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.568552s wall, 2.437500s user + 0.031250s system = 2.468750s CPU (157.4%)

PHY-1001 : Congestion index: top1 = 51.01, top5 = 43.60, top10 = 40.31, top15 = 38.23.
PHY-3001 : End congestion estimation;  1.983451s wall, 2.843750s user + 0.031250s system = 2.875000s CPU (144.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 65819, tnet num: 19074, tinst num: 7962, tnode num: 89567, tedge num: 108467.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.196569s wall, 2.187500s user + 0.015625s system = 2.203125s CPU (100.3%)

RUN-1004 : used memory is 597 MB, reserved memory is 590 MB, peak memory is 713 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19074 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.352293s wall, 3.328125s user + 0.031250s system = 3.359375s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.32579e-05
PHY-3002 : Step(194): len = 513048, overlap = 357.5
PHY-3002 : Step(195): len = 513279, overlap = 373.5
PHY-3002 : Step(196): len = 512890, overlap = 385
PHY-3002 : Step(197): len = 513551, overlap = 383
PHY-3002 : Step(198): len = 512630, overlap = 392.25
PHY-3002 : Step(199): len = 513277, overlap = 399.25
PHY-3002 : Step(200): len = 510874, overlap = 401.75
PHY-3002 : Step(201): len = 510482, overlap = 397
PHY-3002 : Step(202): len = 507848, overlap = 397.75
PHY-3002 : Step(203): len = 507146, overlap = 399
PHY-3002 : Step(204): len = 505778, overlap = 400.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000106516
PHY-3002 : Step(205): len = 509415, overlap = 395
PHY-3002 : Step(206): len = 513166, overlap = 385.25
PHY-3002 : Step(207): len = 514223, overlap = 380
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00020642
PHY-3002 : Step(208): len = 523519, overlap = 360.5
PHY-3002 : Step(209): len = 534563, overlap = 339
PHY-3002 : Step(210): len = 531781, overlap = 336.75
PHY-3002 : Step(211): len = 529631, overlap = 335.75
PHY-3002 : Step(212): len = 528712, overlap = 337.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.828985s wall, 1.000000s user + 0.968750s system = 1.968750s CPU (237.5%)

PHY-3001 : Trial Legalized: Len = 632531
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 83%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 592/19076.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 714312, over cnt = 2236(6%), over = 3667, worst = 7
PHY-1002 : len = 727384, over cnt = 1422(4%), over = 2038, worst = 7
PHY-1002 : len = 738944, over cnt = 848(2%), over = 1172, worst = 6
PHY-1002 : len = 753144, over cnt = 184(0%), over = 260, worst = 5
PHY-1002 : len = 757928, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.301152s wall, 3.609375s user + 0.062500s system = 3.671875s CPU (159.6%)

PHY-1001 : Congestion index: top1 = 48.64, top5 = 44.11, top10 = 41.67, top15 = 40.01.
PHY-3001 : End congestion estimation;  2.739641s wall, 4.031250s user + 0.078125s system = 4.109375s CPU (150.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19074 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.172037s wall, 1.171875s user + 0.000000s system = 1.171875s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000175524
PHY-3002 : Step(213): len = 591828, overlap = 79.5
PHY-3002 : Step(214): len = 573830, overlap = 116.5
PHY-3002 : Step(215): len = 562229, overlap = 154.5
PHY-3002 : Step(216): len = 554736, overlap = 199.25
PHY-3002 : Step(217): len = 550588, overlap = 221.75
PHY-3002 : Step(218): len = 548114, overlap = 241.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000351049
PHY-3002 : Step(219): len = 551485, overlap = 228
PHY-3002 : Step(220): len = 555723, overlap = 224.25
PHY-3002 : Step(221): len = 557725, overlap = 222.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(222): len = 560129, overlap = 221
PHY-3002 : Step(223): len = 565254, overlap = 215.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.067901s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (92.0%)

PHY-3001 : Legalized: Len = 602795, Over = 0
PHY-3001 : Spreading special nets. 47 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.103483s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (90.6%)

PHY-3001 : 69 instances has been re-located, deltaX = 18, deltaY = 48, maxDist = 2.
PHY-3001 : Final: Len = 603477, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 65819, tnet num: 19074, tinst num: 7962, tnode num: 89567, tedge num: 108467.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.486755s wall, 2.468750s user + 0.015625s system = 2.484375s CPU (99.9%)

RUN-1004 : used memory is 621 MB, reserved memory is 614 MB, peak memory is 713 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3620/19076.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 694720, over cnt = 2086(5%), over = 3315, worst = 8
PHY-1002 : len = 705792, over cnt = 1224(3%), over = 1731, worst = 8
PHY-1002 : len = 720880, over cnt = 409(1%), over = 539, worst = 5
PHY-1002 : len = 723760, over cnt = 278(0%), over = 367, worst = 4
PHY-1002 : len = 729664, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.006569s wall, 3.218750s user + 0.078125s system = 3.296875s CPU (164.3%)

PHY-1001 : Congestion index: top1 = 47.09, top5 = 42.52, top10 = 39.99, top15 = 38.45.
PHY-1001 : End incremental global routing;  2.372990s wall, 3.578125s user + 0.078125s system = 3.656250s CPU (154.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19074 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.076417s wall, 1.046875s user + 0.031250s system = 1.078125s CPU (100.2%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 7898 has valid locations, 14 needs to be replaced
PHY-3001 : design contains 7975 instances, 7886 slices, 287 macros(1442 instances: 944 mslices 498 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 606862
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17245/19101.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 735368, over cnt = 24(0%), over = 33, worst = 3
PHY-1002 : len = 735424, over cnt = 14(0%), over = 16, worst = 2
PHY-1002 : len = 735536, over cnt = 6(0%), over = 7, worst = 2
PHY-1002 : len = 735624, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 735640, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.935744s wall, 0.937500s user + 0.015625s system = 0.953125s CPU (101.9%)

PHY-1001 : Congestion index: top1 = 47.33, top5 = 42.65, top10 = 40.12, top15 = 38.57.
PHY-3001 : End congestion estimation;  1.311314s wall, 1.312500s user + 0.015625s system = 1.328125s CPU (101.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 65935, tnet num: 19099, tinst num: 7975, tnode num: 89709, tedge num: 108647.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.551824s wall, 2.546875s user + 0.000000s system = 2.546875s CPU (99.8%)

RUN-1004 : used memory is 661 MB, reserved memory is 650 MB, peak memory is 713 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19099 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.638364s wall, 3.640625s user + 0.000000s system = 3.640625s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(224): len = 606770, overlap = 0.5
PHY-3002 : Step(225): len = 606622, overlap = 0.75
PHY-3002 : Step(226): len = 606699, overlap = 1.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17242/19101.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 733144, over cnt = 23(0%), over = 28, worst = 2
PHY-1002 : len = 733128, over cnt = 13(0%), over = 13, worst = 1
PHY-1002 : len = 733216, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 733264, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 733280, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.790896s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (100.8%)

PHY-1001 : Congestion index: top1 = 47.63, top5 = 42.78, top10 = 40.20, top15 = 38.63.
PHY-3001 : End congestion estimation;  1.132977s wall, 1.140625s user + 0.000000s system = 1.140625s CPU (100.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19099 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.042923s wall, 1.046875s user + 0.000000s system = 1.046875s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000784534
PHY-3002 : Step(227): len = 606684, overlap = 2
PHY-3002 : Step(228): len = 606635, overlap = 2.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007907s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 606674, Over = 0
PHY-3001 : End spreading;  0.088631s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (105.8%)

PHY-3001 : Final: Len = 606674, Over = 0
PHY-3001 : End incremental placement;  7.872820s wall, 8.187500s user + 0.156250s system = 8.343750s CPU (106.0%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  11.883476s wall, 13.359375s user + 0.265625s system = 13.625000s CPU (114.7%)

OPT-1001 : Current memory(MB): used = 707, reserve = 687, peak = 713.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17242/19101.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 733144, over cnt = 15(0%), over = 27, worst = 5
PHY-1002 : len = 733072, over cnt = 12(0%), over = 14, worst = 2
PHY-1002 : len = 733160, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 733280, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 733296, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.830022s wall, 0.828125s user + 0.046875s system = 0.875000s CPU (105.4%)

PHY-1001 : Congestion index: top1 = 47.18, top5 = 42.65, top10 = 40.09, top15 = 38.53.
OPT-1001 : End congestion update;  1.209645s wall, 1.203125s user + 0.046875s system = 1.250000s CPU (103.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19099 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.970062s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (99.9%)

OPT-0007 : Start: WNS 3956 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  2.185805s wall, 2.171875s user + 0.046875s system = 2.218750s CPU (101.5%)

OPT-1001 : Current memory(MB): used = 707, reserve = 687, peak = 713.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19099 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.955498s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (99.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17258/19101.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 733296, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.149958s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (104.2%)

PHY-1001 : Congestion index: top1 = 47.18, top5 = 42.65, top10 = 40.09, top15 = 38.53.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19099 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.961021s wall, 0.937500s user + 0.015625s system = 0.953125s CPU (99.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3956 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 46.793103
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3956ps with logic level 4 
RUN-1001 :       #2 path slack 3963ps with logic level 4 
OPT-1001 : End physical optimization;  19.371830s wall, 20.937500s user + 0.359375s system = 21.296875s CPU (109.9%)

RUN-1003 : finish command "place" in  78.211199s wall, 153.500000s user + 8.437500s system = 161.937500s CPU (207.1%)

RUN-1004 : used memory is 624 MB, reserved memory is 608 MB, peak memory is 713 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  2.111150s wall, 3.578125s user + 0.000000s system = 3.578125s CPU (169.5%)

RUN-1004 : used memory is 625 MB, reserved memory is 608 MB, peak memory is 713 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 7977 instances
RUN-1001 : 3950 mslices, 3936 lslices, 60 pads, 26 brams, 0 dsps
RUN-1001 : There are total 19101 nets
RUN-1001 : 13500 nets have 2 pins
RUN-1001 : 4212 nets have [3 - 5] pins
RUN-1001 : 891 nets have [6 - 10] pins
RUN-1001 : 355 nets have [11 - 20] pins
RUN-1001 : 134 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 65935, tnet num: 19099, tinst num: 7975, tnode num: 89709, tedge num: 108647.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.179739s wall, 2.187500s user + 0.000000s system = 2.187500s CPU (100.4%)

RUN-1004 : used memory is 609 MB, reserved memory is 587 MB, peak memory is 713 MB
PHY-1001 : 3950 mslices, 3936 lslices, 60 pads, 26 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19099 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 678480, over cnt = 2216(6%), over = 3664, worst = 10
PHY-1002 : len = 690320, over cnt = 1465(4%), over = 2215, worst = 10
PHY-1002 : len = 705360, over cnt = 722(2%), over = 1054, worst = 5
PHY-1002 : len = 720528, over cnt = 56(0%), over = 88, worst = 5
PHY-1002 : len = 721696, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.054289s wall, 3.515625s user + 0.062500s system = 3.578125s CPU (174.2%)

PHY-1001 : Congestion index: top1 = 47.31, top5 = 42.63, top10 = 39.94, top15 = 38.24.
PHY-1001 : End global routing;  2.473547s wall, 3.937500s user + 0.062500s system = 4.000000s CPU (161.7%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 693, reserve = 680, peak = 713.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net COM3/GNRMC/u_fifo/wr_to_rd_cross_inst/secondary_clk_i will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 963, reserve = 949, peak = 963.
PHY-1001 : End build detailed router design. 6.373164s wall, 6.281250s user + 0.062500s system = 6.343750s CPU (99.5%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 189680, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.090088s wall, 1.093750s user + 0.000000s system = 1.093750s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 999, reserve = 986, peak = 999.
PHY-1001 : End phase 1; 1.097906s wall, 1.093750s user + 0.000000s system = 1.093750s CPU (99.6%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.67908e+06, over cnt = 1205(0%), over = 1209, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1015, reserve = 1003, peak = 1015.
PHY-1001 : End initial routed; 19.590662s wall, 49.953125s user + 0.593750s system = 50.546875s CPU (258.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17884(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.270   |   0.000   |   0   
RUN-1001 :   Hold   |   0.132   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 4.219798s wall, 4.218750s user + 0.000000s system = 4.218750s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1025, reserve = 1014, peak = 1025.
PHY-1001 : End phase 2; 23.810671s wall, 54.171875s user + 0.593750s system = 54.765625s CPU (230.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.67908e+06, over cnt = 1205(0%), over = 1209, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.292540s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (96.1%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.66756e+06, over cnt = 406(0%), over = 407, worst = 2, crit = 0
PHY-1001 : End DR Iter 1; 0.840427s wall, 1.453125s user + 0.015625s system = 1.468750s CPU (174.8%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.66843e+06, over cnt = 59(0%), over = 59, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.420212s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (163.6%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.66947e+06, over cnt = 12(0%), over = 12, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.303639s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (108.1%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.66986e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.225553s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (117.8%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.66985e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.183175s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (102.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17884(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.244   |   0.000   |   0   
RUN-1001 :   Hold   |   0.132   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 4.274035s wall, 4.281250s user + 0.000000s system = 4.281250s CPU (100.2%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 274 feed throughs used by 242 nets
PHY-1001 : End commit to database; 2.742228s wall, 2.734375s user + 0.015625s system = 2.750000s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 1112, reserve = 1104, peak = 1112.
PHY-1001 : End phase 3; 9.937016s wall, 10.843750s user + 0.031250s system = 10.875000s CPU (109.4%)

PHY-1003 : Routed, final wirelength = 1.66985e+06
PHY-1001 : Current memory(MB): used = 1116, reserve = 1108, peak = 1116.
PHY-1001 : End export database. 0.076816s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (101.7%)

PHY-1001 : End detail routing;  41.805559s wall, 72.984375s user + 0.687500s system = 73.671875s CPU (176.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 65935, tnet num: 19099, tinst num: 7975, tnode num: 89709, tedge num: 108647.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  2.127947s wall, 2.125000s user + 0.000000s system = 2.125000s CPU (99.9%)

RUN-1004 : used memory is 1050 MB, reserved memory is 1050 MB, peak memory is 1117 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  51.727644s wall, 84.328125s user + 0.796875s system = 85.125000s CPU (164.6%)

RUN-1004 : used memory is 1050 MB, reserved memory is 1051 MB, peak memory is 1117 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8539   out of  19600   43.57%
#reg                    12309   out of  19600   62.80%
#le                     14725
  #lut only              2416   out of  14725   16.41%
  #reg only              6186   out of  14725   42.01%
  #lut&reg               6123   out of  14725   41.58%
#dsp                        0   out of     29    0.00%
#bram                      26   out of     64   40.62%
  #bram9k                  26
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                                                 Type               DriverType         Driver                    Fanout
#1        COM3/GNRMC/u_fifo/wr_to_rd_cross_inst/secondary_clk_i    GCLK               pll                CLK100M/pll_inst.clkc0    6764
#2        config_inst_syn_9                                        GCLK               config             config_inst.jtck          117
#3        clk_in_dup_1                                             GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         M3        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         P6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------------+
|Instance                            |Module                                         |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A                                    |14725  |7097    |1442    |12353   |26      |0       |
|  AnyFog_dataX                      |AnyFog                                         |209    |95      |22      |171     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E                                 |89     |63      |22      |51      |0       |0       |
|  AnyFog_dataY                      |AnyFog                                         |213    |74      |22      |176     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E                                 |89     |56      |22      |52      |0       |0       |
|  AnyFog_dataZ                      |AnyFog                                         |197    |123     |22      |163     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E                                 |82     |59      |22      |48      |0       |0       |
|  CLK100M                           |global_clock                                   |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control                                   |2838   |616     |34      |2770    |0       |0       |
|    PPPNAV_com2                     |PPPNAV                                         |214    |66      |5       |203     |0       |0       |
|    STADOP_com2                     |STADOP                                         |562    |101     |0       |557     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800                                  |65     |47      |14      |42      |0       |0       |
|    head_com2                       |uniheading                                     |266    |77      |5       |255     |0       |0       |
|    uart_com2                       |Agrica                                         |1426   |311     |10      |1408    |0       |0       |
|  COM3                              |COM3_Control                                   |388    |184     |44      |320     |2       |0       |
|    GNRMC                           |GNRMC_Tx                                       |175    |84      |30      |136     |2       |0       |
|      u_fifo                        |Asys_fifo8x8                                   |120    |55      |25      |90      |1       |0       |
|        ram_inst                    |ram_infer_Asys_fifo8x8                         |0      |0       |0       |0       |1       |0       |
|        rd_to_wr_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo8x8 |37     |15      |0       |37      |0       |0       |
|        wr_to_rd_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo8x8 |29     |15      |0       |29      |0       |0       |
|    UART_RX_COM3                    |UART_RX460800                                  |57     |34      |14      |35      |0       |0       |
|    rmc_com3                        |Gprmc                                          |156    |66      |0       |149     |0       |0       |
|  DATA                              |Data_Processing                                |8679   |4345    |1056    |7012    |0       |0       |
|    DIV_Dtemp                       |Divider                                        |809    |339     |84      |676     |0       |0       |
|    DIV_Utemp                       |Divider                                        |599    |296     |84      |470     |0       |0       |
|    DIV_accX                        |Divider                                        |599    |307     |84      |474     |0       |0       |
|    DIV_accY                        |Divider                                        |650    |342     |105     |486     |0       |0       |
|    DIV_accZ                        |Divider                                        |644    |383     |132     |438     |0       |0       |
|    DIV_rateX                       |Divider                                        |655    |342     |132     |452     |0       |0       |
|    DIV_rateY                       |Divider                                        |620    |421     |132     |415     |0       |0       |
|    DIV_rateZ                       |Divider                                        |621    |348     |132     |417     |0       |0       |
|    genclk                          |genclk                                         |84     |49      |20      |51      |0       |0       |
|  FMC                               |FMC_Ctrl                                       |439    |388     |43      |342     |0       |0       |
|  IIC                               |I2C_master                                     |289    |253     |11      |265     |0       |0       |
|  IMU_CTRL                          |SCHA634                                        |862    |620     |61      |713     |0       |0       |
|    CtrlData                        |CtrlData                                       |463    |409     |47      |337     |0       |0       |
|      usms                          |Time_1ms                                       |27     |22      |5       |19      |0       |0       |
|    SPIM                            |SPI_SCHA634                                    |399    |211     |14      |376     |0       |0       |
|  POWER                             |POWER_EN                                       |98     |59      |38      |38      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER                                 |496    |336     |89      |322     |0       |0       |
|    wrapper_cwc_top                 |cwc_top                                        |496    |336     |89      |322     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int                                    |213    |156     |0       |196     |0       |0       |
|        reg_inst                    |register                                       |211    |154     |0       |194     |0       |0       |
|        tap_inst                    |tap                                            |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger                                        |283    |180     |89      |126     |0       |0       |
|        bus_inst                    |bus_top                                        |76     |47      |28      |27      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det                                        |24     |14      |10      |7       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det                                        |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det                                        |50     |31      |18      |18      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl                                       |124    |86      |29      |72      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13439  
    #2          2       3339   
    #3          3        625   
    #4          4        248   
    #5        5-10       945   
    #6        11-50      431   
    #7       51-100       4    
    #8       101-500      3    
    #9        >500        2    
  Average     2.10             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.543099s wall, 4.343750s user + 0.015625s system = 4.359375s CPU (171.4%)

RUN-1004 : used memory is 1050 MB, reserved memory is 1052 MB, peak memory is 1117 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 65935, tnet num: 19099, tinst num: 7975, tnode num: 89709, tedge num: 108647.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  2.141487s wall, 2.140625s user + 0.000000s system = 2.140625s CPU (100.0%)

RUN-1004 : used memory is 1052 MB, reserved memory is 1053 MB, peak memory is 1117 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19099 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.663543s wall, 1.656250s user + 0.015625s system = 1.671875s CPU (100.5%)

RUN-1004 : used memory is 1058 MB, reserved memory is 1058 MB, peak memory is 1117 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: dca6202e765a8ec1faf4c0cc68c277aa9616a59fee843dc08e195e3f86c57ae1 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 7975
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19101, pip num: 142896
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 274
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3251 valid insts, and 399653 bits set as '1'.
BIT-1004 : the usercode register value: 00000000111000010110100010110101
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  14.020888s wall, 138.375000s user + 0.234375s system = 138.609375s CPU (988.6%)

RUN-1004 : used memory is 1177 MB, reserved memory is 1162 MB, peak memory is 1292 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250710_162241.log"

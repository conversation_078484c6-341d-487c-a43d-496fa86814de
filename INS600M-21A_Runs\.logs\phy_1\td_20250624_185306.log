============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Tue Jun 24 18:53:07 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(516)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
RUN-1001 : Project manager successfully analyzed 22 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.756885s wall, 1.546875s user + 4.203125s system = 5.750000s CPU (99.9%)

RUN-1004 : used memory is 79 MB, reserved memory is 39 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  2.065213s wall, 1.984375s user + 0.078125s system = 2.062500s CPU (99.9%)

RUN-1004 : used memory is 300 MB, reserved memory is 268 MB, peak memory is 304 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 58 trigger nets, 58 data nets.
KIT-1004 : Chipwatcher code = 1110111010000001
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=158) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=158) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=158)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=158)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 23077/23 useful/useless nets, 19781/12 useful/useless insts
SYN-1016 : Merged 28 instances.
SYN-1032 : 22682/20 useful/useless nets, 20288/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 478 better
SYN-1014 : Optimize round 2
SYN-1032 : 22298/45 useful/useless nets, 19904/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.863653s wall, 2.812500s user + 0.046875s system = 2.859375s CPU (99.9%)

RUN-1004 : used memory is 330 MB, reserved memory is 296 MB, peak memory is 331 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22370/441 useful/useless nets, 20027/63 useful/useless insts
SYN-1016 : Merged 69 instances.
SYN-2571 : Optimize after map_dsp, round 1, 573 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 56 instances.
SYN-2501 : Optimize round 1, 114 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 22874/5 useful/useless nets, 20531/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 84361, tnet num: 22874, tinst num: 20530, tnode num: 118277, tedge num: 131440.
TMR-2508 : Levelizing timing graph completed, there are 85 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.317530s wall, 1.250000s user + 0.046875s system = 1.296875s CPU (98.4%)

RUN-1004 : used memory is 473 MB, reserved memory is 441 MB, peak memory is 473 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22874 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 282 (3.29), #lev = 7 (1.66)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 282 (3.29), #lev = 7 (1.66)
SYN-3001 : Logic optimization runtime opt =   0.04 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 614 instances into 282 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 487 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 160 adder to BLE ...
SYN-4008 : Packed 160 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  5.397950s wall, 5.328125s user + 0.062500s system = 5.390625s CPU (99.9%)

RUN-1004 : used memory is 352 MB, reserved memory is 340 MB, peak memory is 583 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  8.640016s wall, 8.484375s user + 0.140625s system = 8.625000s CPU (99.8%)

RUN-1004 : used memory is 353 MB, reserved memory is 340 MB, peak memory is 583 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[7] will be merged to another kept net COM3/rmc_com3/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[6] will be merged to another kept net COM3/rmc_com3/GPRMC_data[6]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[5] will be merged to another kept net COM3/rmc_com3/GPRMC_data[5]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[4] will be merged to another kept net COM3/rmc_com3/GPRMC_data[4]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[3] will be merged to another kept net COM3/rmc_com3/GPRMC_data[3]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[2] will be merged to another kept net COM3/rmc_com3/GPRMC_data[2]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[1] will be merged to another kept net COM3/rmc_com3/GPRMC_data[1]
SYN-5055 WARNING: The kept net FMC/Error_state[0] will be merged to another kept net COM3/rmc_com3/GPRMC_sub_state_cnt0[3]
SYN-5055 WARNING: The kept net COM3/rmc_com3/GPRMC_sub_state_cnt0[3] will be merged to another kept net COM3/rmc_com3/GPRMC_sub_state_cnt0[2]
SYN-5055 WARNING: The kept net COM3/rmc_com3/GPRMC_sub_state_cnt0[2] will be merged to another kept net COM3/rmc_com3/GPRMC_sub_state_cnt0[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (335 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19855 instances
RUN-0007 : 5746 luts, 12551 seqs, 951 mslices, 494 lslices, 56 pads, 52 brams, 0 dsps
RUN-1001 : There are total 22222 nets
RUN-1001 : 16607 nets have 2 pins
RUN-1001 : 4442 nets have [3 - 5] pins
RUN-1001 : 796 nets have [6 - 10] pins
RUN-1001 : 247 nets have [11 - 20] pins
RUN-1001 : 106 nets have [21 - 99] pins
RUN-1001 : 24 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4743     
RUN-1001 :   No   |  No   |  Yes  |     670     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    6531     
RUN-1001 :   Yes  |  No   |  Yes  |     507     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  116  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 124
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19853 instances, 5746 luts, 12551 seqs, 1445 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1131 pins
PHY-3001 : Huge net DATA/done_div with 1650 pins
PHY-0007 : Cell area utilization is 64%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82888, tnet num: 22220, tinst num: 19853, tnode num: 116939, tedge num: 130286.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.280631s wall, 1.234375s user + 0.046875s system = 1.281250s CPU (100.0%)

RUN-1004 : used memory is 534 MB, reserved memory is 506 MB, peak memory is 583 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22220 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.253341s wall, 2.187500s user + 0.062500s system = 2.250000s CPU (99.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.49077e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19853.
PHY-3001 : Level 1 #clusters 2138.
PHY-3001 : End clustering;  0.176725s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (176.8%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 64%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 887403, overlap = 698.5
PHY-3002 : Step(2): len = 799889, overlap = 722.938
PHY-3002 : Step(3): len = 518912, overlap = 929
PHY-3002 : Step(4): len = 466519, overlap = 980.875
PHY-3002 : Step(5): len = 370582, overlap = 1091.28
PHY-3002 : Step(6): len = 342563, overlap = 1136.44
PHY-3002 : Step(7): len = 280575, overlap = 1196.62
PHY-3002 : Step(8): len = 251806, overlap = 1252
PHY-3002 : Step(9): len = 222577, overlap = 1314.66
PHY-3002 : Step(10): len = 203970, overlap = 1368
PHY-3002 : Step(11): len = 184994, overlap = 1403.09
PHY-3002 : Step(12): len = 172284, overlap = 1445.59
PHY-3002 : Step(13): len = 154218, overlap = 1467.69
PHY-3002 : Step(14): len = 140934, overlap = 1491.53
PHY-3002 : Step(15): len = 133135, overlap = 1495.09
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.33665e-07
PHY-3002 : Step(16): len = 137006, overlap = 1489.66
PHY-3002 : Step(17): len = 173980, overlap = 1438.62
PHY-3002 : Step(18): len = 183312, overlap = 1348.84
PHY-3002 : Step(19): len = 189720, overlap = 1278.03
PHY-3002 : Step(20): len = 190506, overlap = 1236.88
PHY-3002 : Step(21): len = 187162, overlap = 1211.22
PHY-3002 : Step(22): len = 183193, overlap = 1196.62
PHY-3002 : Step(23): len = 178518, overlap = 1182.53
PHY-3002 : Step(24): len = 174255, overlap = 1189.75
PHY-3002 : Step(25): len = 170649, overlap = 1207.69
PHY-3002 : Step(26): len = 168812, overlap = 1217.41
PHY-3002 : Step(27): len = 168085, overlap = 1216.19
PHY-3002 : Step(28): len = 166501, overlap = 1202
PHY-3002 : Step(29): len = 165889, overlap = 1196.22
PHY-3002 : Step(30): len = 165197, overlap = 1188.81
PHY-3002 : Step(31): len = 165244, overlap = 1184.88
PHY-3002 : Step(32): len = 164820, overlap = 1170.88
PHY-3002 : Step(33): len = 164146, overlap = 1159.88
PHY-3002 : Step(34): len = 162488, overlap = 1149.28
PHY-3002 : Step(35): len = 161214, overlap = 1143.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 1.86733e-06
PHY-3002 : Step(36): len = 169913, overlap = 1099.97
PHY-3002 : Step(37): len = 183073, overlap = 1019.59
PHY-3002 : Step(38): len = 186353, overlap = 1005.91
PHY-3002 : Step(39): len = 189590, overlap = 989.812
PHY-3002 : Step(40): len = 190515, overlap = 972.906
PHY-3002 : Step(41): len = 190547, overlap = 985.812
PHY-3002 : Step(42): len = 188707, overlap = 960.656
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 3.73466e-06
PHY-3002 : Step(43): len = 200064, overlap = 919.156
PHY-3002 : Step(44): len = 215749, overlap = 818.625
PHY-3002 : Step(45): len = 220198, overlap = 784.781
PHY-3002 : Step(46): len = 222265, overlap = 797.969
PHY-3002 : Step(47): len = 221485, overlap = 815.375
PHY-3002 : Step(48): len = 221518, overlap = 810.125
PHY-3002 : Step(49): len = 220732, overlap = 791.062
PHY-3002 : Step(50): len = 220316, overlap = 800.594
PHY-3002 : Step(51): len = 219186, overlap = 800.375
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 7.46932e-06
PHY-3002 : Step(52): len = 230256, overlap = 773.719
PHY-3002 : Step(53): len = 245367, overlap = 715.812
PHY-3002 : Step(54): len = 250523, overlap = 669.438
PHY-3002 : Step(55): len = 253007, overlap = 654.312
PHY-3002 : Step(56): len = 251946, overlap = 651.531
PHY-3002 : Step(57): len = 250991, overlap = 651.156
PHY-3002 : Step(58): len = 248897, overlap = 649.281
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.49386e-05
PHY-3002 : Step(59): len = 260197, overlap = 648.438
PHY-3002 : Step(60): len = 272753, overlap = 556.875
PHY-3002 : Step(61): len = 278022, overlap = 510.031
PHY-3002 : Step(62): len = 281657, overlap = 487.75
PHY-3002 : Step(63): len = 280056, overlap = 477.719
PHY-3002 : Step(64): len = 278185, overlap = 473.062
PHY-3002 : Step(65): len = 275662, overlap = 463.094
PHY-3002 : Step(66): len = 274760, overlap = 468.25
PHY-3002 : Step(67): len = 274033, overlap = 459.625
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 2.98773e-05
PHY-3002 : Step(68): len = 283139, overlap = 449.156
PHY-3002 : Step(69): len = 292956, overlap = 419.281
PHY-3002 : Step(70): len = 296252, overlap = 411.469
PHY-3002 : Step(71): len = 297049, overlap = 399.156
PHY-3002 : Step(72): len = 295368, overlap = 391.812
PHY-3002 : Step(73): len = 294531, overlap = 377.062
PHY-3002 : Step(74): len = 293757, overlap = 347.281
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 5.97545e-05
PHY-3002 : Step(75): len = 300685, overlap = 328.781
PHY-3002 : Step(76): len = 311285, overlap = 344.5
PHY-3002 : Step(77): len = 315216, overlap = 345.125
PHY-3002 : Step(78): len = 315032, overlap = 339.406
PHY-3002 : Step(79): len = 312887, overlap = 342.969
PHY-3002 : Step(80): len = 311260, overlap = 333.562
PHY-3002 : Step(81): len = 309749, overlap = 335.062
PHY-3002 : Step(82): len = 309672, overlap = 331.625
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000117925
PHY-3002 : Step(83): len = 313412, overlap = 330.875
PHY-3002 : Step(84): len = 318645, overlap = 333.562
PHY-3002 : Step(85): len = 321581, overlap = 330.906
PHY-3002 : Step(86): len = 322648, overlap = 308.875
PHY-3002 : Step(87): len = 321697, overlap = 300.688
PHY-3002 : Step(88): len = 320928, overlap = 300.188
PHY-3002 : Step(89): len = 319460, overlap = 305.938
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000220883
PHY-3002 : Step(90): len = 321442, overlap = 318.844
PHY-3002 : Step(91): len = 325193, overlap = 324.375
PHY-3002 : Step(92): len = 326819, overlap = 326.812
PHY-3002 : Step(93): len = 328079, overlap = 321.375
PHY-3002 : Step(94): len = 328105, overlap = 318.062
PHY-3002 : Step(95): len = 328852, overlap = 326.719
PHY-3002 : Step(96): len = 329149, overlap = 318.562
PHY-3002 : Step(97): len = 329771, overlap = 305.031
PHY-3002 : Step(98): len = 331399, overlap = 283.125
PHY-3002 : Step(99): len = 331530, overlap = 278.719
PHY-3002 : Step(100): len = 329831, overlap = 281.781
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(101): len = 330857, overlap = 281.625
PHY-3002 : Step(102): len = 332243, overlap = 280.438
PHY-3002 : Step(103): len = 332603, overlap = 278.844
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.015010s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (104.1%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/22222.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 449080, over cnt = 1284(3%), over = 6039, worst = 39
PHY-1001 : End global iterations;  1.005631s wall, 1.234375s user + 0.031250s system = 1.265625s CPU (125.9%)

PHY-1001 : Congestion index: top1 = 78.08, top5 = 54.76, top10 = 44.50, top15 = 38.68.
PHY-3001 : End congestion estimation;  1.267881s wall, 1.437500s user + 0.078125s system = 1.515625s CPU (119.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22220 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.042660s wall, 1.046875s user + 0.000000s system = 1.046875s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.31542e-05
PHY-3002 : Step(104): len = 374863, overlap = 202.406
PHY-3002 : Step(105): len = 391221, overlap = 171.406
PHY-3002 : Step(106): len = 392373, overlap = 165.875
PHY-3002 : Step(107): len = 394458, overlap = 150.75
PHY-3002 : Step(108): len = 399278, overlap = 131.531
PHY-3002 : Step(109): len = 407756, overlap = 116.469
PHY-3002 : Step(110): len = 410563, overlap = 112.5
PHY-3002 : Step(111): len = 414736, overlap = 108.938
PHY-3002 : Step(112): len = 420005, overlap = 106
PHY-3002 : Step(113): len = 422164, overlap = 105.156
PHY-3002 : Step(114): len = 425425, overlap = 101.5
PHY-3002 : Step(115): len = 427652, overlap = 100.656
PHY-3002 : Step(116): len = 431398, overlap = 98.0625
PHY-3002 : Step(117): len = 434925, overlap = 95.3125
PHY-3002 : Step(118): len = 438332, overlap = 101.406
PHY-3002 : Step(119): len = 441856, overlap = 100.688
PHY-3002 : Step(120): len = 445555, overlap = 103.812
PHY-3002 : Step(121): len = 448912, overlap = 103.938
PHY-3002 : Step(122): len = 449940, overlap = 101.094
PHY-3002 : Step(123): len = 451920, overlap = 101
PHY-3002 : Step(124): len = 452639, overlap = 102.469
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000186308
PHY-3002 : Step(125): len = 452100, overlap = 99.625
PHY-3002 : Step(126): len = 453449, overlap = 95.875
PHY-3002 : Step(127): len = 456803, overlap = 97.0938
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000372617
PHY-3002 : Step(128): len = 460934, overlap = 95.125
PHY-3002 : Step(129): len = 467826, overlap = 96.0938
PHY-3002 : Step(130): len = 472316, overlap = 96.2812
PHY-3002 : Step(131): len = 475403, overlap = 90.875
PHY-3002 : Step(132): len = 478789, overlap = 84.2188
PHY-3002 : Step(133): len = 479201, overlap = 84.6562
PHY-3002 : Step(134): len = 477270, overlap = 86.7188
PHY-3002 : Step(135): len = 476877, overlap = 89.0938
PHY-3002 : Step(136): len = 476000, overlap = 90.5
PHY-3002 : Step(137): len = 473821, overlap = 91.375
PHY-3002 : Step(138): len = 472722, overlap = 93.2812
PHY-3002 : Step(139): len = 472064, overlap = 98.0312
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000745234
PHY-3002 : Step(140): len = 472368, overlap = 95.5
PHY-3002 : Step(141): len = 475757, overlap = 92.625
PHY-3002 : Step(142): len = 478761, overlap = 92.375
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00138099
PHY-3002 : Step(143): len = 479306, overlap = 96.75
PHY-3002 : Step(144): len = 487139, overlap = 101.062
PHY-3002 : Step(145): len = 494272, overlap = 101.031
PHY-3002 : Step(146): len = 498809, overlap = 106.594
PHY-3002 : Step(147): len = 501122, overlap = 109.531
PHY-3002 : Step(148): len = 502745, overlap = 103.531
PHY-3002 : Step(149): len = 502364, overlap = 108.094
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 39/22222.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 570448, over cnt = 2282(6%), over = 11595, worst = 62
PHY-1001 : End global iterations;  1.109221s wall, 1.765625s user + 0.015625s system = 1.781250s CPU (160.6%)

PHY-1001 : Congestion index: top1 = 89.46, top5 = 65.68, top10 = 55.12, top15 = 49.08.
PHY-3001 : End congestion estimation;  1.416996s wall, 2.062500s user + 0.031250s system = 2.093750s CPU (147.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22220 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.077592s wall, 1.062500s user + 0.015625s system = 1.078125s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.70116e-05
PHY-3002 : Step(150): len = 510504, overlap = 388
PHY-3002 : Step(151): len = 511931, overlap = 342.969
PHY-3002 : Step(152): len = 501328, overlap = 318.156
PHY-3002 : Step(153): len = 492553, overlap = 310.531
PHY-3002 : Step(154): len = 487703, overlap = 277.906
PHY-3002 : Step(155): len = 482287, overlap = 273.375
PHY-3002 : Step(156): len = 478675, overlap = 254.781
PHY-3002 : Step(157): len = 476965, overlap = 246.125
PHY-3002 : Step(158): len = 472058, overlap = 247.469
PHY-3002 : Step(159): len = 468648, overlap = 249.375
PHY-3002 : Step(160): len = 466814, overlap = 247.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000174023
PHY-3002 : Step(161): len = 467465, overlap = 237.469
PHY-3002 : Step(162): len = 468685, overlap = 225.156
PHY-3002 : Step(163): len = 469370, overlap = 217.781
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000348046
PHY-3002 : Step(164): len = 472083, overlap = 204.188
PHY-3002 : Step(165): len = 481012, overlap = 188.594
PHY-3002 : Step(166): len = 485156, overlap = 183.312
PHY-3002 : Step(167): len = 488134, overlap = 179.625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000696093
PHY-3002 : Step(168): len = 487692, overlap = 184.531
PHY-3002 : Step(169): len = 489857, overlap = 181.312
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82888, tnet num: 22220, tinst num: 19853, tnode num: 116939, tedge num: 130286.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.607412s wall, 1.578125s user + 0.031250s system = 1.609375s CPU (100.1%)

RUN-1004 : used memory is 574 MB, reserved memory is 549 MB, peak memory is 711 MB
OPT-1001 : Total overflow 540.41 peak overflow 4.59
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 674/22222.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 575872, over cnt = 2651(7%), over = 9238, worst = 33
PHY-1001 : End global iterations;  1.268318s wall, 1.984375s user + 0.031250s system = 2.015625s CPU (158.9%)

PHY-1001 : Congestion index: top1 = 57.50, top5 = 48.12, top10 = 43.21, top15 = 40.16.
PHY-1001 : End incremental global routing;  1.539379s wall, 2.218750s user + 0.046875s system = 2.265625s CPU (147.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22220 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.112571s wall, 1.093750s user + 0.015625s system = 1.109375s CPU (99.7%)

OPT-1001 : 16 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 56 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19778 has valid locations, 277 needs to be replaced
PHY-3001 : design contains 20114 instances, 5860 luts, 12698 seqs, 1445 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 508421
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17566/22483.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 594064, over cnt = 2683(7%), over = 9318, worst = 33
PHY-1001 : End global iterations;  0.212422s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (125.0%)

PHY-1001 : Congestion index: top1 = 57.63, top5 = 48.43, top10 = 43.52, top15 = 40.50.
PHY-3001 : End congestion estimation;  0.492384s wall, 0.562500s user + 0.000000s system = 0.562500s CPU (114.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83775, tnet num: 22481, tinst num: 20114, tnode num: 118180, tedge num: 131538.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.668916s wall, 1.593750s user + 0.062500s system = 1.656250s CPU (99.2%)

RUN-1004 : used memory is 622 MB, reserved memory is 620 MB, peak memory is 713 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22481 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.828705s wall, 2.734375s user + 0.093750s system = 2.828125s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(170): len = 508135, overlap = 1.75
PHY-3002 : Step(171): len = 509153, overlap = 1.75
PHY-3002 : Step(172): len = 509561, overlap = 1.75
PHY-3002 : Step(173): len = 510151, overlap = 1.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17617/22483.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 592512, over cnt = 2715(7%), over = 9504, worst = 33
PHY-1001 : End global iterations;  0.210101s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (104.1%)

PHY-1001 : Congestion index: top1 = 57.76, top5 = 48.69, top10 = 43.75, top15 = 40.78.
PHY-3001 : End congestion estimation;  0.490017s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (105.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22481 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.139077s wall, 1.140625s user + 0.000000s system = 1.140625s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000793557
PHY-3002 : Step(174): len = 510461, overlap = 185.188
PHY-3002 : Step(175): len = 510962, overlap = 185.094
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00158711
PHY-3002 : Step(176): len = 511361, overlap = 184.188
PHY-3002 : Step(177): len = 511707, overlap = 184.125
PHY-3001 : Final: Len = 511707, Over = 184.125
PHY-3001 : End incremental placement;  5.888500s wall, 5.968750s user + 0.218750s system = 6.187500s CPU (105.1%)

OPT-1001 : Total overflow 545.56 peak overflow 4.59
OPT-1001 : End high-fanout net optimization;  9.125715s wall, 10.031250s user + 0.281250s system = 10.312500s CPU (113.0%)

OPT-1001 : Current memory(MB): used = 717, reserve = 697, peak = 734.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17609/22483.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 595224, over cnt = 2663(7%), over = 8891, worst = 33
PHY-1002 : len = 635816, over cnt = 1989(5%), over = 5032, worst = 22
PHY-1002 : len = 677024, over cnt = 707(2%), over = 1664, worst = 19
PHY-1002 : len = 702400, over cnt = 179(0%), over = 350, worst = 9
PHY-1002 : len = 708768, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.446564s wall, 2.109375s user + 0.000000s system = 2.109375s CPU (145.8%)

PHY-1001 : Congestion index: top1 = 50.04, top5 = 44.63, top10 = 41.46, top15 = 39.43.
OPT-1001 : End congestion update;  1.726788s wall, 2.421875s user + 0.000000s system = 2.421875s CPU (140.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22481 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.130997s wall, 1.125000s user + 0.000000s system = 1.125000s CPU (99.5%)

OPT-0007 : Start: WNS 4103 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.865055s wall, 3.562500s user + 0.000000s system = 3.562500s CPU (124.3%)

OPT-1001 : Current memory(MB): used = 717, reserve = 696, peak = 734.
OPT-1001 : End physical optimization;  13.950772s wall, 15.671875s user + 0.312500s system = 15.984375s CPU (114.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5860 LUT to BLE ...
SYN-4008 : Packed 5860 LUT and 2889 SEQ to BLE.
SYN-4003 : Packing 9809 remaining SEQ's ...
SYN-4005 : Packed 3321 SEQ with LUT/SLICE
SYN-4006 : 157 single LUT's are left
SYN-4006 : 6488 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 12348/14026 primitive instances ...
PHY-3001 : End packing;  3.240544s wall, 3.234375s user + 0.000000s system = 3.234375s CPU (99.8%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8293 instances
RUN-1001 : 4090 mslices, 4090 lslices, 56 pads, 52 brams, 0 dsps
RUN-1001 : There are total 19641 nets
RUN-1001 : 13677 nets have 2 pins
RUN-1001 : 4548 nets have [3 - 5] pins
RUN-1001 : 874 nets have [6 - 10] pins
RUN-1001 : 394 nets have [11 - 20] pins
RUN-1001 : 138 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
PHY-3001 : design contains 8291 instances, 8180 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Cell area utilization is 87%
PHY-3001 : After packing: Len = 528655, Over = 368.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 8192/19641.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 670624, over cnt = 1543(4%), over = 2427, worst = 8
PHY-1002 : len = 676816, over cnt = 986(2%), over = 1315, worst = 6
PHY-1002 : len = 686528, over cnt = 453(1%), over = 568, worst = 5
PHY-1002 : len = 695320, over cnt = 45(0%), over = 51, worst = 3
PHY-1002 : len = 696912, over cnt = 2(0%), over = 2, worst = 1
PHY-1001 : End global iterations;  1.333655s wall, 2.312500s user + 0.062500s system = 2.375000s CPU (178.1%)

PHY-1001 : Congestion index: top1 = 51.31, top5 = 44.66, top10 = 41.29, top15 = 39.08.
PHY-3001 : End congestion estimation;  1.703570s wall, 2.671875s user + 0.062500s system = 2.734375s CPU (160.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69075, tnet num: 19639, tinst num: 8291, tnode num: 94067, tedge num: 113719.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.953961s wall, 1.937500s user + 0.000000s system = 1.937500s CPU (99.2%)

RUN-1004 : used memory is 607 MB, reserved memory is 591 MB, peak memory is 734 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19639 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.979312s wall, 2.906250s user + 0.046875s system = 2.953125s CPU (99.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.95331e-05
PHY-3002 : Step(178): len = 530121, overlap = 354
PHY-3002 : Step(179): len = 528889, overlap = 369.5
PHY-3002 : Step(180): len = 528010, overlap = 384.75
PHY-3002 : Step(181): len = 528795, overlap = 386.75
PHY-3002 : Step(182): len = 526682, overlap = 397
PHY-3002 : Step(183): len = 524426, overlap = 401
PHY-3002 : Step(184): len = 523075, overlap = 397
PHY-3002 : Step(185): len = 520988, overlap = 400.25
PHY-3002 : Step(186): len = 519324, overlap = 404.5
PHY-3002 : Step(187): len = 518154, overlap = 410.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.90662e-05
PHY-3002 : Step(188): len = 521628, overlap = 404.75
PHY-3002 : Step(189): len = 525487, overlap = 394.75
PHY-3002 : Step(190): len = 525291, overlap = 393
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000198132
PHY-3002 : Step(191): len = 534815, overlap = 377.5
PHY-3002 : Step(192): len = 545152, overlap = 358.5
PHY-3002 : Step(193): len = 542514, overlap = 351.5
PHY-3002 : Step(194): len = 540552, overlap = 346.25
PHY-3002 : Step(195): len = 540868, overlap = 338.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.731941s wall, 0.640625s user + 0.843750s system = 1.484375s CPU (202.8%)

PHY-3001 : Trial Legalized: Len = 645637
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 499/19641.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 740808, over cnt = 2447(6%), over = 3974, worst = 7
PHY-1002 : len = 757264, over cnt = 1360(3%), over = 1878, worst = 6
PHY-1002 : len = 771232, over cnt = 546(1%), over = 758, worst = 5
PHY-1002 : len = 779416, over cnt = 206(0%), over = 301, worst = 5
PHY-1002 : len = 785056, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.114151s wall, 3.500000s user + 0.015625s system = 3.515625s CPU (166.3%)

PHY-1001 : Congestion index: top1 = 49.18, top5 = 44.69, top10 = 42.28, top15 = 40.64.
PHY-3001 : End congestion estimation;  2.528361s wall, 3.906250s user + 0.015625s system = 3.921875s CPU (155.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19639 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.976707s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (99.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000197563
PHY-3002 : Step(196): len = 605697, overlap = 78.25
PHY-3002 : Step(197): len = 589384, overlap = 123
PHY-3002 : Step(198): len = 578382, overlap = 163.75
PHY-3002 : Step(199): len = 572231, overlap = 201.25
PHY-3002 : Step(200): len = 568768, overlap = 219.25
PHY-3002 : Step(201): len = 566360, overlap = 240.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000395126
PHY-3002 : Step(202): len = 571568, overlap = 239.5
PHY-3002 : Step(203): len = 576183, overlap = 233.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000790251
PHY-3002 : Step(204): len = 577293, overlap = 235.25
PHY-3002 : Step(205): len = 584118, overlap = 232.25
PHY-3002 : Step(206): len = 586005, overlap = 231
PHY-3002 : Step(207): len = 586324, overlap = 228.75
PHY-3002 : Step(208): len = 587049, overlap = 231.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.037332s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (83.7%)

PHY-3001 : Legalized: Len = 625521, Over = 0
PHY-3001 : Spreading special nets. 34 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.090887s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (103.2%)

PHY-3001 : 51 instances has been re-located, deltaX = 22, deltaY = 26, maxDist = 2.
PHY-3001 : Final: Len = 626009, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69075, tnet num: 19639, tinst num: 8291, tnode num: 94067, tedge num: 113719.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.091384s wall, 2.031250s user + 0.046875s system = 2.078125s CPU (99.4%)

RUN-1004 : used memory is 621 MB, reserved memory is 623 MB, peak memory is 734 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3992/19641.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 731792, over cnt = 2339(6%), over = 3636, worst = 8
PHY-1002 : len = 744096, over cnt = 1384(3%), over = 1927, worst = 7
PHY-1002 : len = 758232, over cnt = 622(1%), over = 830, worst = 6
PHY-1002 : len = 765440, over cnt = 321(0%), over = 421, worst = 6
PHY-1002 : len = 772880, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.840001s wall, 3.078125s user + 0.046875s system = 3.125000s CPU (169.8%)

PHY-1001 : Congestion index: top1 = 48.21, top5 = 43.24, top10 = 40.61, top15 = 39.02.
PHY-1001 : End incremental global routing;  2.181665s wall, 3.421875s user + 0.046875s system = 3.468750s CPU (159.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19639 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.009757s wall, 0.984375s user + 0.015625s system = 1.000000s CPU (99.0%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 56 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8230 has valid locations, 22 needs to be replaced
PHY-3001 : design contains 8311 instances, 8200 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 631161
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17793/19659.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 779848, over cnt = 37(0%), over = 48, worst = 5
PHY-1002 : len = 779688, over cnt = 25(0%), over = 25, worst = 1
PHY-1002 : len = 779768, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 779984, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 780032, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.785350s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (109.4%)

PHY-1001 : Congestion index: top1 = 48.77, top5 = 43.45, top10 = 40.78, top15 = 39.18.
PHY-3001 : End congestion estimation;  1.122837s wall, 1.171875s user + 0.000000s system = 1.171875s CPU (104.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69213, tnet num: 19657, tinst num: 8311, tnode num: 94234, tedge num: 113884.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.141104s wall, 2.140625s user + 0.000000s system = 2.140625s CPU (100.0%)

RUN-1004 : used memory is 651 MB, reserved memory is 639 MB, peak memory is 734 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19657 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.216495s wall, 3.187500s user + 0.031250s system = 3.218750s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(209): len = 630715, overlap = 1
PHY-3002 : Step(210): len = 630612, overlap = 0.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17789/19659.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 778544, over cnt = 34(0%), over = 47, worst = 5
PHY-1002 : len = 778624, over cnt = 19(0%), over = 22, worst = 2
PHY-1002 : len = 778744, over cnt = 6(0%), over = 7, worst = 2
PHY-1002 : len = 778848, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 778880, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.783067s wall, 0.796875s user + 0.031250s system = 0.828125s CPU (105.8%)

PHY-1001 : Congestion index: top1 = 48.47, top5 = 43.49, top10 = 40.79, top15 = 39.17.
PHY-3001 : End congestion estimation;  1.116779s wall, 1.140625s user + 0.031250s system = 1.171875s CPU (104.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19657 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.010997s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (98.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000994167
PHY-3002 : Step(211): len = 630510, overlap = 2.5
PHY-3002 : Step(212): len = 630523, overlap = 2.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006883s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 630525, Over = 0
PHY-3001 : End spreading;  0.075432s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (103.6%)

PHY-3001 : Final: Len = 630525, Over = 0
PHY-3001 : End incremental placement;  7.095934s wall, 7.515625s user + 0.203125s system = 7.718750s CPU (108.8%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.840080s wall, 12.656250s user + 0.265625s system = 12.921875s CPU (119.2%)

OPT-1001 : Current memory(MB): used = 726, reserve = 712, peak = 734.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17789/19659.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 778136, over cnt = 30(0%), over = 37, worst = 2
PHY-1002 : len = 778080, over cnt = 26(0%), over = 29, worst = 2
PHY-1002 : len = 778248, over cnt = 15(0%), over = 16, worst = 2
PHY-1002 : len = 778472, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 778488, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.776818s wall, 0.843750s user + 0.031250s system = 0.875000s CPU (112.6%)

PHY-1001 : Congestion index: top1 = 48.38, top5 = 43.33, top10 = 40.74, top15 = 39.12.
OPT-1001 : End congestion update;  1.110133s wall, 1.171875s user + 0.031250s system = 1.203125s CPU (108.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19657 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.851669s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (99.1%)

OPT-0007 : Start: WNS 4420 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.967191s wall, 2.031250s user + 0.031250s system = 2.062500s CPU (104.8%)

OPT-1001 : Current memory(MB): used = 726, reserve = 712, peak = 734.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19657 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.841150s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (100.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17813/19659.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 778488, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.132116s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (94.6%)

PHY-1001 : Congestion index: top1 = 48.38, top5 = 43.33, top10 = 40.74, top15 = 39.12.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19657 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.849846s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (101.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4420 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 47.931034
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4420ps with logic level 4 
OPT-1001 : End physical optimization;  17.332425s wall, 19.203125s user + 0.343750s system = 19.546875s CPU (112.8%)

RUN-1003 : finish command "place" in  75.214141s wall, 145.031250s user + 8.765625s system = 153.796875s CPU (204.5%)

RUN-1004 : used memory is 643 MB, reserved memory is 619 MB, peak memory is 734 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.700348s wall, 2.906250s user + 0.015625s system = 2.921875s CPU (171.8%)

RUN-1004 : used memory is 643 MB, reserved memory is 620 MB, peak memory is 734 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8313 instances
RUN-1001 : 4101 mslices, 4099 lslices, 56 pads, 52 brams, 0 dsps
RUN-1001 : There are total 19659 nets
RUN-1001 : 13675 nets have 2 pins
RUN-1001 : 4552 nets have [3 - 5] pins
RUN-1001 : 881 nets have [6 - 10] pins
RUN-1001 : 404 nets have [11 - 20] pins
RUN-1001 : 137 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69213, tnet num: 19657, tinst num: 8311, tnode num: 94234, tedge num: 113884.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.906921s wall, 1.890625s user + 0.015625s system = 1.906250s CPU (100.0%)

RUN-1004 : used memory is 624 MB, reserved memory is 600 MB, peak memory is 734 MB
PHY-1001 : 4101 mslices, 4099 lslices, 56 pads, 52 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19657 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 715408, over cnt = 2420(6%), over = 4026, worst = 8
PHY-1002 : len = 731408, over cnt = 1477(4%), over = 2208, worst = 6
PHY-1002 : len = 752096, over cnt = 487(1%), over = 673, worst = 6
PHY-1002 : len = 763464, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 763672, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.006421s wall, 3.250000s user + 0.062500s system = 3.312500s CPU (165.1%)

PHY-1001 : Congestion index: top1 = 47.37, top5 = 42.84, top10 = 40.42, top15 = 38.88.
PHY-1001 : End global routing;  2.394350s wall, 3.640625s user + 0.078125s system = 3.718750s CPU (155.3%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 706, reserve = 692, peak = 734.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 976, reserve = 962, peak = 976.
PHY-1001 : End build detailed router design. 5.115005s wall, 5.015625s user + 0.109375s system = 5.125000s CPU (100.2%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 193736, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.956987s wall, 0.937500s user + 0.015625s system = 0.953125s CPU (99.6%)

PHY-1001 : Current memory(MB): used = 1013, reserve = 1000, peak = 1013.
PHY-1001 : End phase 1; 0.964789s wall, 0.937500s user + 0.015625s system = 0.953125s CPU (98.8%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 55% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.83876e+06, over cnt = 1381(0%), over = 1384, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1029, reserve = 1016, peak = 1029.
PHY-1001 : End initial routed; 26.550383s wall, 59.140625s user + 0.406250s system = 59.546875s CPU (224.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18439(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.292   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 27.391734s wall, 3.828125s user + 0.015625s system = 3.843750s CPU (14.0%)

PHY-1001 : Current memory(MB): used = 1041, reserve = 1028, peak = 1041.
PHY-1001 : End phase 2; 53.942283s wall, 62.968750s user + 0.421875s system = 63.390625s CPU (117.5%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.83876e+06, over cnt = 1381(0%), over = 1384, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.258452s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (96.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.82557e+06, over cnt = 481(0%), over = 482, worst = 2, crit = 0
PHY-1001 : End DR Iter 1; 4.854315s wall, 5.312500s user + 0.062500s system = 5.375000s CPU (110.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.82586e+06, over cnt = 73(0%), over = 73, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 1.120660s wall, 1.843750s user + 0.031250s system = 1.875000s CPU (167.3%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.82651e+06, over cnt = 11(0%), over = 11, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.337142s wall, 0.406250s user + 0.015625s system = 0.421875s CPU (125.1%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.82666e+06, over cnt = 5(0%), over = 5, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.222740s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (112.2%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.82683e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.235546s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (92.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18439(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.292   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.660648s wall, 3.671875s user + 0.000000s system = 3.671875s CPU (100.3%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 368 feed throughs used by 316 nets
PHY-1001 : End commit to database; 2.437126s wall, 2.390625s user + 0.031250s system = 2.421875s CPU (99.4%)

PHY-1001 : Current memory(MB): used = 1130, reserve = 1120, peak = 1130.
PHY-1001 : End phase 3; 14.598809s wall, 15.515625s user + 0.156250s system = 15.671875s CPU (107.4%)

PHY-1003 : Routed, final wirelength = 1.82683e+06
PHY-1001 : Current memory(MB): used = 1134, reserve = 1125, peak = 1134.
PHY-1001 : End export database. 0.055510s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (84.4%)

PHY-1001 : End detail routing;  75.112185s wall, 84.921875s user + 0.703125s system = 85.625000s CPU (114.0%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69213, tnet num: 19657, tinst num: 8311, tnode num: 94234, tedge num: 113884.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.757322s wall, 1.765625s user + 0.000000s system = 1.765625s CPU (100.5%)

RUN-1004 : used memory is 1063 MB, reserved memory is 1059 MB, peak memory is 1134 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  83.856556s wall, 94.890625s user + 0.796875s system = 95.687500s CPU (114.1%)

RUN-1004 : used memory is 1062 MB, reserved memory is 1059 MB, peak memory is 1134 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8901   out of  19600   45.41%
#reg                    12794   out of  19600   65.28%
#le                     15365
  #lut only              2571   out of  15365   16.73%
  #reg only              6464   out of  15365   42.07%
  #lut&reg               6330   out of  15365   41.20%
#dsp                        0   out of     29    0.00%
#bram                      52   out of     64   81.25%
  #bram9k                  52
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       56   out of    188   29.79%
  #ireg                    10
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6994
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          187
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT        E11        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT        A13        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT        F10        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         E8        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         E7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT        A10        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C6        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         D6        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         C7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT        E12        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         F1        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         A7        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H13        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J11        LVCMOS33          N/A          PULLUP          IREG       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT        E13        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         R7        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         M7        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         P7        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         T8        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         P8        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         N8        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M9        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         T9        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         R9        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         T6        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         P9        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         N9        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT        P11        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         T5        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         B2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        L10        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        E15        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        R14        LVCMOS33           8            N/A            OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT         R2        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT        N12        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        B10        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         J6        LVCMOS33           8            N/A            NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        R15        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E6        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15365  |7456    |1445    |12837   |52      |0       |
|  AnyFog_dataX                      |AnyFog          |209    |83      |22      |172     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |87     |64      |22      |50      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |215    |93      |22      |176     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |91     |58      |22      |52      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |206    |97      |22      |170     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |85     |61      |22      |49      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |3333   |889     |34      |3255    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |735    |80      |5       |722     |0       |0       |
|    STADOP_com2                     |STADOP          |555    |135     |0       |547     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |51      |14      |43      |0       |0       |
|    head_com2                       |uniheading      |271    |75      |5       |260     |0       |0       |
|    uart_com2                       |Agrica          |1417   |258     |10      |1393    |0       |0       |
|  COM3                              |COM3_Control    |220    |114     |14      |188     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |68     |44      |14      |41      |0       |0       |
|    rmc_com3                        |Gprmc           |152    |70      |0       |147     |0       |0       |
|  DATA                              |Data_Processing |8613   |4329    |1059    |6915    |0       |0       |
|    DIV_Dtemp                       |Divider         |779    |370     |84      |644     |0       |0       |
|    DIV_Utemp                       |Divider         |640    |312     |84      |509     |0       |0       |
|    DIV_accX                        |Divider         |650    |351     |84      |527     |0       |0       |
|    DIV_accY                        |Divider         |619    |326     |108     |454     |0       |0       |
|    DIV_accZ                        |Divider         |720    |367     |132     |494     |0       |0       |
|    DIV_rateX                       |Divider         |628    |365     |132     |423     |0       |0       |
|    DIV_rateY                       |Divider         |595    |333     |132     |389     |0       |0       |
|    DIV_rateZ                       |Divider         |541    |334     |132     |334     |0       |0       |
|    genclk                          |genclk          |89     |58      |20      |54      |0       |0       |
|  FMC                               |FMC_Ctrl        |501    |451     |43      |363     |0       |0       |
|  IIC                               |I2C_master      |303    |253     |11      |269     |0       |0       |
|  IMU_CTRL                          |SCHA634         |903    |629     |61      |730     |0       |0       |
|    CtrlData                        |CtrlData        |460    |408     |47      |323     |0       |0       |
|      usms                          |Time_1ms        |29     |24      |5       |21      |0       |0       |
|    SPIM                            |SPI_SCHA634     |443    |221     |14      |407     |0       |0       |
|  POWER                             |POWER_EN        |103    |44      |38      |42      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |755    |472     |119     |511     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |755    |472     |119     |511     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |333    |187     |0       |316     |0       |0       |
|        reg_inst                    |register        |330    |184     |0       |313     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |422    |285     |119     |195     |0       |0       |
|        bus_inst                    |bus_top         |177    |114     |62      |65      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |28     |18      |10      |12      |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |52     |34      |18      |20      |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |95     |60      |34      |31      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |150    |111     |29      |100     |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13618  
    #2          2       3568   
    #3          3        705   
    #4          4        279   
    #5        5-10       965   
    #6        11-50      436   
    #7       51-100      21    
    #8       101-500      4    
    #9        >500        2    
  Average     2.16             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.117326s wall, 3.734375s user + 0.000000s system = 3.734375s CPU (176.4%)

RUN-1004 : used memory is 1063 MB, reserved memory is 1059 MB, peak memory is 1134 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69213, tnet num: 19657, tinst num: 8311, tnode num: 94234, tedge num: 113884.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.861589s wall, 1.843750s user + 0.000000s system = 1.843750s CPU (99.0%)

RUN-1004 : used memory is 1065 MB, reserved memory is 1060 MB, peak memory is 1134 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19657 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.361686s wall, 1.359375s user + 0.000000s system = 1.359375s CPU (99.8%)

RUN-1004 : used memory is 1070 MB, reserved memory is 1065 MB, peak memory is 1134 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 28b752c1c0e7d7ba92ac5beed3c46754b7b56ac64969e4b0149cfdbc1d295011 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8311
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19659, pip num: 152267
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 368
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3223 valid insts, and 424461 bits set as '1'.
BIT-1004 : the usercode register value: 00000000000000001110111010000001
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  13.730823s wall, 135.468750s user + 0.218750s system = 135.687500s CPU (988.2%)

RUN-1004 : used memory is 1202 MB, reserved memory is 1188 MB, peak memory is 1317 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250624_185306.log"

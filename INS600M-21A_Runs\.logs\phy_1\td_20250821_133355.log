============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Aug 21 13:33:55 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-5007 WARNING: data object 'write_en' is already declared in ../../Src/FMC/FMC_Ctrl.v(114)
HDL-1007 : previous declaration of 'write_en' is from here in ../../Src/FMC/FMC_Ctrl.v(113)
HDL-5007 WARNING: second declaration of 'write_en' is ignored in ../../Src/FMC/FMC_Ctrl.v(114)
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.172525s wall, 1.359375s user + 3.812500s system = 5.171875s CPU (100.0%)

RUN-1004 : used memory is 79 MB, reserved memory is 41 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.788925s wall, 1.734375s user + 0.062500s system = 1.796875s CPU (100.4%)

RUN-1004 : used memory is 302 MB, reserved memory is 270 MB, peak memory is 305 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 42 trigger nets, 42 data nets.
KIT-1004 : Chipwatcher code = 0000101011000101
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=126) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=126) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=126)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=126)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22790/23 useful/useless nets, 19538/12 useful/useless insts
SYN-1016 : Merged 29 instances.
SYN-1032 : 22458/20 useful/useless nets, 19964/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 398 better
SYN-1014 : Optimize round 2
SYN-1032 : 22138/45 useful/useless nets, 19644/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.384225s wall, 2.343750s user + 0.046875s system = 2.390625s CPU (100.3%)

RUN-1004 : used memory is 328 MB, reserved memory is 295 MB, peak memory is 330 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22186/299 useful/useless nets, 19729/47 useful/useless insts
SYN-1016 : Merged 45 instances.
SYN-2571 : Optimize after map_dsp, round 1, 391 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 40 instances.
SYN-2501 : Optimize round 1, 82 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 22634/5 useful/useless nets, 20177/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82619, tnet num: 22634, tinst num: 20176, tnode num: 115701, tedge num: 129185.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.098172s wall, 1.031250s user + 0.062500s system = 1.093750s CPU (99.6%)

RUN-1004 : used memory is 469 MB, reserved memory is 436 MB, peak memory is 469 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22634 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 234 (3.43), #lev = 7 (1.79)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 234 (3.43), #lev = 7 (1.79)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 534 instances into 234 LUTs, name keeping = 75%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 407 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 128 adder to BLE ...
SYN-4008 : Packed 128 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.000654s wall, 3.937500s user + 0.078125s system = 4.015625s CPU (100.4%)

RUN-1004 : used memory is 364 MB, reserved memory is 350 MB, peak memory is 578 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  6.693011s wall, 6.531250s user + 0.171875s system = 6.703125s CPU (100.2%)

RUN-1004 : used memory is 364 MB, reserved memory is 350 MB, peak memory is 578 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (271 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19417 instances
RUN-0007 : 5610 luts, 12201 seqs, 983 mslices, 519 lslices, 59 pads, 40 brams, 0 dsps
RUN-1001 : There are total 21882 nets
RUN-1001 : 16425 nets have 2 pins
RUN-1001 : 4262 nets have [3 - 5] pins
RUN-1001 : 823 nets have [6 - 10] pins
RUN-1001 : 244 nets have [11 - 20] pins
RUN-1001 : 106 nets have [21 - 99] pins
RUN-1001 : 22 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4785     
RUN-1001 :   No   |  No   |  Yes  |     704     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     443     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19415 instances, 5610 luts, 12201 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80996, tnet num: 21880, tinst num: 19415, tnode num: 113869, tedge num: 127507.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.108832s wall, 1.078125s user + 0.031250s system = 1.109375s CPU (100.0%)

RUN-1004 : used memory is 529 MB, reserved memory is 501 MB, peak memory is 578 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21880 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.893787s wall, 1.843750s user + 0.046875s system = 1.890625s CPU (99.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.60964e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19415.
PHY-3001 : Level 1 #clusters 2161.
PHY-3001 : End clustering;  0.127698s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (171.3%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 868520, overlap = 638.031
PHY-3002 : Step(2): len = 810750, overlap = 690.281
PHY-3002 : Step(3): len = 523377, overlap = 895.156
PHY-3002 : Step(4): len = 470261, overlap = 950.5
PHY-3002 : Step(5): len = 370005, overlap = 1053.66
PHY-3002 : Step(6): len = 335725, overlap = 1098.16
PHY-3002 : Step(7): len = 286879, overlap = 1173.81
PHY-3002 : Step(8): len = 256767, overlap = 1228.06
PHY-3002 : Step(9): len = 223617, overlap = 1260.09
PHY-3002 : Step(10): len = 206504, overlap = 1299.69
PHY-3002 : Step(11): len = 184124, overlap = 1355.09
PHY-3002 : Step(12): len = 166702, overlap = 1382.34
PHY-3002 : Step(13): len = 155637, overlap = 1396.5
PHY-3002 : Step(14): len = 145263, overlap = 1420.94
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.33219e-06
PHY-3002 : Step(15): len = 150466, overlap = 1376.47
PHY-3002 : Step(16): len = 205717, overlap = 1242.78
PHY-3002 : Step(17): len = 213757, overlap = 1174.31
PHY-3002 : Step(18): len = 212815, overlap = 1116.66
PHY-3002 : Step(19): len = 207328, overlap = 1088.09
PHY-3002 : Step(20): len = 199321, overlap = 1071.44
PHY-3002 : Step(21): len = 192551, overlap = 1041.12
PHY-3002 : Step(22): len = 186482, overlap = 1021.06
PHY-3002 : Step(23): len = 182594, overlap = 1006.75
PHY-3002 : Step(24): len = 179399, overlap = 1024.47
PHY-3002 : Step(25): len = 177890, overlap = 1039.19
PHY-3002 : Step(26): len = 174721, overlap = 1056.09
PHY-3002 : Step(27): len = 174377, overlap = 1041.34
PHY-3002 : Step(28): len = 173106, overlap = 1041.38
PHY-3002 : Step(29): len = 172808, overlap = 1024.41
PHY-3002 : Step(30): len = 172156, overlap = 1012.28
PHY-3002 : Step(31): len = 172308, overlap = 1011
PHY-3002 : Step(32): len = 170606, overlap = 1011.75
PHY-3002 : Step(33): len = 170378, overlap = 996
PHY-3002 : Step(34): len = 169592, overlap = 981.469
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.66439e-06
PHY-3002 : Step(35): len = 175440, overlap = 930.125
PHY-3002 : Step(36): len = 190425, overlap = 861.188
PHY-3002 : Step(37): len = 194620, overlap = 832.812
PHY-3002 : Step(38): len = 196789, overlap = 828.188
PHY-3002 : Step(39): len = 197010, overlap = 833.844
PHY-3002 : Step(40): len = 195452, overlap = 829.25
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 5.32878e-06
PHY-3002 : Step(41): len = 208458, overlap = 798.906
PHY-3002 : Step(42): len = 223157, overlap = 757
PHY-3002 : Step(43): len = 227265, overlap = 748.188
PHY-3002 : Step(44): len = 229648, overlap = 721.719
PHY-3002 : Step(45): len = 227709, overlap = 717.094
PHY-3002 : Step(46): len = 224990, overlap = 713.75
PHY-3002 : Step(47): len = 222907, overlap = 708.25
PHY-3002 : Step(48): len = 222481, overlap = 710.25
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 1.06576e-05
PHY-3002 : Step(49): len = 234533, overlap = 656.562
PHY-3002 : Step(50): len = 248507, overlap = 600.594
PHY-3002 : Step(51): len = 251719, overlap = 534.219
PHY-3002 : Step(52): len = 254436, overlap = 539.75
PHY-3002 : Step(53): len = 253294, overlap = 526.688
PHY-3002 : Step(54): len = 252606, overlap = 497.688
PHY-3002 : Step(55): len = 250914, overlap = 472.125
PHY-3002 : Step(56): len = 251532, overlap = 487.312
PHY-3002 : Step(57): len = 248692, overlap = 497.062
PHY-3002 : Step(58): len = 247590, overlap = 507.5
PHY-3002 : Step(59): len = 245972, overlap = 510.094
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 2.13151e-05
PHY-3002 : Step(60): len = 258744, overlap = 506.656
PHY-3002 : Step(61): len = 270181, overlap = 497.188
PHY-3002 : Step(62): len = 273333, overlap = 453.406
PHY-3002 : Step(63): len = 273451, overlap = 440.656
PHY-3002 : Step(64): len = 271545, overlap = 420.125
PHY-3002 : Step(65): len = 270062, overlap = 422.344
PHY-3002 : Step(66): len = 268195, overlap = 412.688
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 4.26302e-05
PHY-3002 : Step(67): len = 279330, overlap = 428.312
PHY-3002 : Step(68): len = 288823, overlap = 417.469
PHY-3002 : Step(69): len = 291006, overlap = 395.75
PHY-3002 : Step(70): len = 292142, overlap = 374.469
PHY-3002 : Step(71): len = 291458, overlap = 380.188
PHY-3002 : Step(72): len = 289486, overlap = 371.688
PHY-3002 : Step(73): len = 288242, overlap = 357.062
PHY-3002 : Step(74): len = 287752, overlap = 338.031
PHY-3002 : Step(75): len = 286234, overlap = 333.969
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 8.52605e-05
PHY-3002 : Step(76): len = 292382, overlap = 324.75
PHY-3002 : Step(77): len = 300482, overlap = 304.375
PHY-3002 : Step(78): len = 302672, overlap = 275.344
PHY-3002 : Step(79): len = 303034, overlap = 262.594
PHY-3002 : Step(80): len = 302195, overlap = 268.156
PHY-3002 : Step(81): len = 301184, overlap = 275.25
PHY-3002 : Step(82): len = 299461, overlap = 280.75
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000167398
PHY-3002 : Step(83): len = 303790, overlap = 290.344
PHY-3002 : Step(84): len = 309841, overlap = 298.656
PHY-3002 : Step(85): len = 311883, overlap = 296.594
PHY-3002 : Step(86): len = 312587, overlap = 277.062
PHY-3002 : Step(87): len = 312538, overlap = 268.75
PHY-3002 : Step(88): len = 312163, overlap = 275.844
PHY-3002 : Step(89): len = 310855, overlap = 268.031
PHY-3002 : Step(90): len = 311774, overlap = 245.594
PHY-3002 : Step(91): len = 311480, overlap = 256.219
PHY-3002 : Step(92): len = 311624, overlap = 256.312
PHY-3002 : Step(93): len = 310806, overlap = 260.688
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008048s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21882.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 415856, over cnt = 1198(3%), over = 5378, worst = 35
PHY-1001 : End global iterations;  0.772218s wall, 1.000000s user + 0.015625s system = 1.015625s CPU (131.5%)

PHY-1001 : Congestion index: top1 = 73.69, top5 = 52.16, top10 = 42.42, top15 = 36.85.
PHY-3001 : End congestion estimation;  1.000357s wall, 1.234375s user + 0.015625s system = 1.250000s CPU (125.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21880 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.818969s wall, 0.796875s user + 0.015625s system = 0.812500s CPU (99.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.00303e-05
PHY-3002 : Step(94): len = 356290, overlap = 195.281
PHY-3002 : Step(95): len = 369912, overlap = 183.344
PHY-3002 : Step(96): len = 370116, overlap = 171.562
PHY-3002 : Step(97): len = 370915, overlap = 157.469
PHY-3002 : Step(98): len = 376486, overlap = 148.875
PHY-3002 : Step(99): len = 382184, overlap = 150.375
PHY-3002 : Step(100): len = 384604, overlap = 147.5
PHY-3002 : Step(101): len = 390814, overlap = 143
PHY-3002 : Step(102): len = 395782, overlap = 134.844
PHY-3002 : Step(103): len = 396946, overlap = 127.469
PHY-3002 : Step(104): len = 399552, overlap = 120.219
PHY-3002 : Step(105): len = 404048, overlap = 127.688
PHY-3002 : Step(106): len = 407103, overlap = 129.562
PHY-3002 : Step(107): len = 409170, overlap = 138.125
PHY-3002 : Step(108): len = 412748, overlap = 148.25
PHY-3002 : Step(109): len = 415200, overlap = 146.781
PHY-3002 : Step(110): len = 418648, overlap = 150.594
PHY-3002 : Step(111): len = 421154, overlap = 152.094
PHY-3002 : Step(112): len = 423208, overlap = 149.469
PHY-3002 : Step(113): len = 426608, overlap = 144.094
PHY-3002 : Step(114): len = 428067, overlap = 145.188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000180061
PHY-3002 : Step(115): len = 428437, overlap = 141.281
PHY-3002 : Step(116): len = 430205, overlap = 135.5
PHY-3002 : Step(117): len = 432606, overlap = 129.875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000360121
PHY-3002 : Step(118): len = 436748, overlap = 126.688
PHY-3002 : Step(119): len = 442511, overlap = 109.438
PHY-3002 : Step(120): len = 445611, overlap = 104.969
PHY-3002 : Step(121): len = 446561, overlap = 104.969
PHY-3002 : Step(122): len = 448336, overlap = 98.7188
PHY-3002 : Step(123): len = 447632, overlap = 95.375
PHY-3002 : Step(124): len = 446944, overlap = 93.0625
PHY-3002 : Step(125): len = 447291, overlap = 97.6562
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000720242
PHY-3002 : Step(126): len = 447107, overlap = 91.3125
PHY-3002 : Step(127): len = 449475, overlap = 90.2188
PHY-3002 : Step(128): len = 456781, overlap = 92.3125
PHY-3002 : Step(129): len = 462740, overlap = 96.625
PHY-3002 : Step(130): len = 463050, overlap = 102.438
PHY-3002 : Step(131): len = 464930, overlap = 103.906
PHY-3002 : Step(132): len = 467061, overlap = 109.562
PHY-3002 : Step(133): len = 466502, overlap = 100.938
PHY-3002 : Step(134): len = 465014, overlap = 106.406
PHY-3002 : Step(135): len = 464281, overlap = 102.75
PHY-3002 : Step(136): len = 464172, overlap = 110.375
PHY-3002 : Step(137): len = 463255, overlap = 112.719
PHY-3002 : Step(138): len = 462353, overlap = 114.031
PHY-3002 : Step(139): len = 462533, overlap = 117.5
PHY-3002 : Step(140): len = 461915, overlap = 119.5
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(141): len = 462376, overlap = 113.469
PHY-3002 : Step(142): len = 464794, overlap = 109.531
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 39/21882.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 529720, over cnt = 2145(6%), over = 10025, worst = 42
PHY-1001 : End global iterations;  0.995021s wall, 1.609375s user + 0.031250s system = 1.640625s CPU (164.9%)

PHY-1001 : Congestion index: top1 = 81.94, top5 = 61.84, top10 = 51.56, top15 = 45.84.
PHY-3001 : End congestion estimation;  1.240456s wall, 1.859375s user + 0.031250s system = 1.890625s CPU (152.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21880 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.879677s wall, 0.843750s user + 0.031250s system = 0.875000s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.17604e-05
PHY-3002 : Step(143): len = 472924, overlap = 355.625
PHY-3002 : Step(144): len = 477864, overlap = 287.219
PHY-3002 : Step(145): len = 471615, overlap = 277.125
PHY-3002 : Step(146): len = 468940, overlap = 240.375
PHY-3002 : Step(147): len = 465647, overlap = 223.75
PHY-3002 : Step(148): len = 462768, overlap = 213.156
PHY-3002 : Step(149): len = 460372, overlap = 211.531
PHY-3002 : Step(150): len = 459961, overlap = 206.25
PHY-3002 : Step(151): len = 457355, overlap = 201.031
PHY-3002 : Step(152): len = 455179, overlap = 200.094
PHY-3002 : Step(153): len = 454693, overlap = 194.469
PHY-3002 : Step(154): len = 450868, overlap = 191.844
PHY-3002 : Step(155): len = 450175, overlap = 193.406
PHY-3002 : Step(156): len = 446837, overlap = 201.25
PHY-3002 : Step(157): len = 444662, overlap = 202.656
PHY-3002 : Step(158): len = 442032, overlap = 204.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000183521
PHY-3002 : Step(159): len = 441381, overlap = 196.781
PHY-3002 : Step(160): len = 442593, overlap = 191.594
PHY-3002 : Step(161): len = 444458, overlap = 188.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000367041
PHY-3002 : Step(162): len = 447399, overlap = 181.312
PHY-3002 : Step(163): len = 453426, overlap = 173.25
PHY-3002 : Step(164): len = 458157, overlap = 167.031
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000734083
PHY-3002 : Step(165): len = 458761, overlap = 162.344
PHY-3002 : Step(166): len = 464214, overlap = 148.125
PHY-3002 : Step(167): len = 470730, overlap = 139.062
PHY-3002 : Step(168): len = 473155, overlap = 128.25
PHY-3002 : Step(169): len = 473254, overlap = 134.125
PHY-3002 : Step(170): len = 474774, overlap = 131.812
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00139988
PHY-3002 : Step(171): len = 476033, overlap = 127.812
PHY-3002 : Step(172): len = 478780, overlap = 121.469
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80996, tnet num: 21880, tinst num: 19415, tnode num: 113869, tedge num: 127507.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.432951s wall, 1.390625s user + 0.031250s system = 1.421875s CPU (99.2%)

RUN-1004 : used memory is 570 MB, reserved memory is 544 MB, peak memory is 702 MB
OPT-1001 : Total overflow 473.41 peak overflow 5.09
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 336/21882.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 555816, over cnt = 2529(7%), over = 8795, worst = 28
PHY-1001 : End global iterations;  1.174120s wall, 1.859375s user + 0.015625s system = 1.875000s CPU (159.7%)

PHY-1001 : Congestion index: top1 = 57.72, top5 = 47.36, top10 = 42.79, top15 = 39.89.
PHY-1001 : End incremental global routing;  1.397140s wall, 2.078125s user + 0.015625s system = 2.093750s CPU (149.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21880 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.139611s wall, 1.109375s user + 0.031250s system = 1.140625s CPU (100.1%)

OPT-1001 : 20 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19333 has valid locations, 246 needs to be replaced
PHY-3001 : design contains 19641 instances, 5708 luts, 12329 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 494940
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17396/22108.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 569456, over cnt = 2563(7%), over = 8880, worst = 28
PHY-1001 : End global iterations;  0.187330s wall, 0.312500s user + 0.031250s system = 0.343750s CPU (183.5%)

PHY-1001 : Congestion index: top1 = 58.36, top5 = 47.84, top10 = 43.22, top15 = 40.37.
PHY-3001 : End congestion estimation;  0.435240s wall, 0.546875s user + 0.031250s system = 0.578125s CPU (132.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81743, tnet num: 22106, tinst num: 19641, tnode num: 114921, tedge num: 128549.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.566838s wall, 1.546875s user + 0.031250s system = 1.578125s CPU (100.7%)

RUN-1004 : used memory is 613 MB, reserved memory is 610 MB, peak memory is 705 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22106 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.596643s wall, 2.578125s user + 0.031250s system = 2.609375s CPU (100.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(173): len = 495352, overlap = 0.5
PHY-3002 : Step(174): len = 496827, overlap = 0.5
PHY-3002 : Step(175): len = 498084, overlap = 0.5
PHY-3002 : Step(176): len = 499306, overlap = 0.5625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17414/22108.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 569400, over cnt = 2562(7%), over = 8940, worst = 28
PHY-1001 : End global iterations;  0.174284s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (134.5%)

PHY-1001 : Congestion index: top1 = 58.49, top5 = 47.95, top10 = 43.33, top15 = 40.42.
PHY-3001 : End congestion estimation;  0.403773s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (112.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22106 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.936213s wall, 0.921875s user + 0.015625s system = 0.937500s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000706456
PHY-3002 : Step(177): len = 499126, overlap = 124.969
PHY-3002 : Step(178): len = 499091, overlap = 124.844
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00141291
PHY-3002 : Step(179): len = 499215, overlap = 124.531
PHY-3002 : Step(180): len = 499508, overlap = 124.219
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00282583
PHY-3002 : Step(181): len = 499420, overlap = 124.469
PHY-3002 : Step(182): len = 499478, overlap = 124.406
PHY-3001 : Final: Len = 499478, Over = 124.406
PHY-3001 : End incremental placement;  5.382379s wall, 5.625000s user + 0.234375s system = 5.859375s CPU (108.9%)

OPT-1001 : Total overflow 479.22 peak overflow 5.09
OPT-1001 : End high-fanout net optimization;  8.399205s wall, 9.359375s user + 0.296875s system = 9.656250s CPU (115.0%)

OPT-1001 : Current memory(MB): used = 708, reserve = 688, peak = 725.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17412/22108.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 571176, over cnt = 2521(7%), over = 8368, worst = 28
PHY-1002 : len = 610736, over cnt = 1858(5%), over = 4703, worst = 21
PHY-1002 : len = 635592, over cnt = 1152(3%), over = 2792, worst = 21
PHY-1002 : len = 659408, over cnt = 556(1%), over = 1308, worst = 21
PHY-1002 : len = 679416, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.172322s wall, 1.671875s user + 0.000000s system = 1.671875s CPU (142.6%)

PHY-1001 : Congestion index: top1 = 49.55, top5 = 43.48, top10 = 40.45, top15 = 38.42.
OPT-1001 : End congestion update;  1.410173s wall, 1.906250s user + 0.000000s system = 1.906250s CPU (135.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22106 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.779886s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (100.2%)

OPT-0007 : Start: WNS 4315 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.195109s wall, 2.687500s user + 0.000000s system = 2.687500s CPU (122.4%)

OPT-1001 : Current memory(MB): used = 705, reserve = 685, peak = 725.
OPT-1001 : End physical optimization;  12.323312s wall, 13.906250s user + 0.328125s system = 14.234375s CPU (115.5%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5708 LUT to BLE ...
SYN-4008 : Packed 5708 LUT and 2731 SEQ to BLE.
SYN-4003 : Packing 9598 remaining SEQ's ...
SYN-4005 : Packed 3403 SEQ with LUT/SLICE
SYN-4006 : 95 single LUT's are left
SYN-4006 : 6195 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11903/13773 primitive instances ...
PHY-3001 : End packing;  2.581282s wall, 2.578125s user + 0.000000s system = 2.578125s CPU (99.9%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8141 instances
RUN-1001 : 4019 mslices, 4018 lslices, 59 pads, 40 brams, 0 dsps
RUN-1001 : There are total 19429 nets
RUN-1001 : 13615 nets have 2 pins
RUN-1001 : 4391 nets have [3 - 5] pins
RUN-1001 : 894 nets have [6 - 10] pins
RUN-1001 : 384 nets have [11 - 20] pins
RUN-1001 : 136 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8139 instances, 8037 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 512639, Over = 362
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7843/19429.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 641456, over cnt = 1547(4%), over = 2442, worst = 8
PHY-1002 : len = 648936, over cnt = 994(2%), over = 1312, worst = 5
PHY-1002 : len = 658960, over cnt = 419(1%), over = 534, worst = 5
PHY-1002 : len = 665624, over cnt = 127(0%), over = 160, worst = 5
PHY-1002 : len = 668200, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.153267s wall, 1.765625s user + 0.046875s system = 1.812500s CPU (157.2%)

PHY-1001 : Congestion index: top1 = 50.88, top5 = 43.54, top10 = 39.99, top15 = 37.80.
PHY-3001 : End congestion estimation;  1.488729s wall, 2.093750s user + 0.046875s system = 2.140625s CPU (143.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67889, tnet num: 19427, tinst num: 8139, tnode num: 92067, tedge num: 111993.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.946915s wall, 1.937500s user + 0.015625s system = 1.953125s CPU (100.3%)

RUN-1004 : used memory is 601 MB, reserved memory is 582 MB, peak memory is 725 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19427 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.833519s wall, 2.781250s user + 0.062500s system = 2.843750s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.13798e-05
PHY-3002 : Step(183): len = 515483, overlap = 343.5
PHY-3002 : Step(184): len = 514896, overlap = 348.75
PHY-3002 : Step(185): len = 514896, overlap = 364
PHY-3002 : Step(186): len = 515254, overlap = 374
PHY-3002 : Step(187): len = 514267, overlap = 383
PHY-3002 : Step(188): len = 512189, overlap = 390.5
PHY-3002 : Step(189): len = 509288, overlap = 398.25
PHY-3002 : Step(190): len = 506788, overlap = 399.75
PHY-3002 : Step(191): len = 504627, overlap = 401.25
PHY-3002 : Step(192): len = 502998, overlap = 408
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00010276
PHY-3002 : Step(193): len = 506434, overlap = 395.25
PHY-3002 : Step(194): len = 511009, overlap = 382.75
PHY-3002 : Step(195): len = 512664, overlap = 371.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000205519
PHY-3002 : Step(196): len = 518927, overlap = 362.25
PHY-3002 : Step(197): len = 528409, overlap = 347
PHY-3002 : Step(198): len = 528218, overlap = 344
PHY-3002 : Step(199): len = 527159, overlap = 340
PHY-3002 : Step(200): len = 527017, overlap = 343.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.598468s wall, 0.640625s user + 0.859375s system = 1.500000s CPU (250.6%)

PHY-3001 : Trial Legalized: Len = 632137
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 564/19429.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 719176, over cnt = 2381(6%), over = 3908, worst = 7
PHY-1002 : len = 736464, over cnt = 1432(4%), over = 1885, worst = 6
PHY-1002 : len = 746736, over cnt = 835(2%), over = 1112, worst = 5
PHY-1002 : len = 759048, over cnt = 292(0%), over = 395, worst = 5
PHY-1002 : len = 765800, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.103192s wall, 3.390625s user + 0.062500s system = 3.453125s CPU (164.2%)

PHY-1001 : Congestion index: top1 = 50.00, top5 = 44.83, top10 = 42.15, top15 = 40.28.
PHY-3001 : End congestion estimation;  2.432317s wall, 3.734375s user + 0.062500s system = 3.796875s CPU (156.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19427 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.805516s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (98.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000182055
PHY-3002 : Step(201): len = 590586, overlap = 75.75
PHY-3002 : Step(202): len = 573132, overlap = 121.75
PHY-3002 : Step(203): len = 560793, overlap = 173
PHY-3002 : Step(204): len = 553193, overlap = 214
PHY-3002 : Step(205): len = 548910, overlap = 231.5
PHY-3002 : Step(206): len = 546857, overlap = 252.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00036411
PHY-3002 : Step(207): len = 550338, overlap = 247.25
PHY-3002 : Step(208): len = 554311, overlap = 245.25
PHY-3002 : Step(209): len = 555104, overlap = 248.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(210): len = 558001, overlap = 242.25
PHY-3002 : Step(211): len = 564218, overlap = 232
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.045900s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (102.1%)

PHY-3001 : Legalized: Len = 605405, Over = 0
PHY-3001 : Spreading special nets. 42 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.155402s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (100.5%)

PHY-3001 : 61 instances has been re-located, deltaX = 20, deltaY = 38, maxDist = 2.
PHY-3001 : Final: Len = 606705, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67889, tnet num: 19427, tinst num: 8139, tnode num: 92067, tedge num: 111993.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.106288s wall, 2.109375s user + 0.000000s system = 2.109375s CPU (100.1%)

RUN-1004 : used memory is 609 MB, reserved memory is 596 MB, peak memory is 725 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 4360/19429.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 705488, over cnt = 2202(6%), over = 3385, worst = 7
PHY-1002 : len = 716072, over cnt = 1278(3%), over = 1738, worst = 6
PHY-1002 : len = 728672, over cnt = 565(1%), over = 769, worst = 6
PHY-1002 : len = 736456, over cnt = 199(0%), over = 264, worst = 4
PHY-1002 : len = 741728, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.577535s wall, 2.562500s user + 0.000000s system = 2.562500s CPU (162.4%)

PHY-1001 : Congestion index: top1 = 47.78, top5 = 43.12, top10 = 40.53, top15 = 38.96.
PHY-1001 : End incremental global routing;  1.855226s wall, 2.843750s user + 0.000000s system = 2.843750s CPU (153.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19427 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.836029s wall, 0.812500s user + 0.015625s system = 0.828125s CPU (99.1%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8076 has valid locations, 6 needs to be replaced
PHY-3001 : design contains 8144 instances, 8042 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 607942
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17558/19433.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 742656, over cnt = 13(0%), over = 13, worst = 1
PHY-1002 : len = 742656, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 742728, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 742752, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 742816, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.876191s wall, 0.859375s user + 0.031250s system = 0.890625s CPU (101.6%)

PHY-1001 : Congestion index: top1 = 47.74, top5 = 43.09, top10 = 40.51, top15 = 38.94.
PHY-3001 : End congestion estimation;  1.260239s wall, 1.234375s user + 0.031250s system = 1.265625s CPU (100.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67918, tnet num: 19431, tinst num: 8144, tnode num: 92101, tedge num: 112026.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.889260s wall, 1.859375s user + 0.031250s system = 1.890625s CPU (100.1%)

RUN-1004 : used memory is 645 MB, reserved memory is 626 MB, peak memory is 725 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19431 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.909329s wall, 2.875000s user + 0.031250s system = 2.906250s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(212): len = 607819, overlap = 0.25
PHY-3002 : Step(213): len = 607773, overlap = 0
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17555/19433.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 742424, over cnt = 13(0%), over = 14, worst = 2
PHY-1002 : len = 742352, over cnt = 14(0%), over = 14, worst = 1
PHY-1002 : len = 742424, over cnt = 10(0%), over = 10, worst = 1
PHY-1002 : len = 742576, over cnt = 2(0%), over = 2, worst = 1
PHY-1001 : End global iterations;  0.475850s wall, 0.500000s user + 0.015625s system = 0.515625s CPU (108.4%)

PHY-1001 : Congestion index: top1 = 47.74, top5 = 43.11, top10 = 40.56, top15 = 38.98.
PHY-3001 : End congestion estimation;  0.761776s wall, 0.796875s user + 0.015625s system = 0.812500s CPU (106.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19431 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.824200s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (100.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000545624
PHY-3002 : Step(214): len = 607695, overlap = 0.75
PHY-3002 : Step(215): len = 607681, overlap = 0.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005329s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 607685, Over = 0
PHY-3001 : End spreading;  0.059521s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.0%)

PHY-3001 : Final: Len = 607685, Over = 0
PHY-3001 : End incremental placement;  6.331923s wall, 6.390625s user + 0.125000s system = 6.515625s CPU (102.9%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  9.494261s wall, 10.703125s user + 0.140625s system = 10.843750s CPU (114.2%)

OPT-1001 : Current memory(MB): used = 717, reserve = 702, peak = 725.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17555/19433.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 742600, over cnt = 10(0%), over = 11, worst = 2
PHY-1002 : len = 742576, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 742632, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.382320s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (102.2%)

PHY-1001 : Congestion index: top1 = 47.76, top5 = 43.10, top10 = 40.53, top15 = 38.94.
OPT-1001 : End congestion update;  0.674331s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (102.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19431 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.788707s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (99.1%)

OPT-0007 : Start: WNS 4365 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.467002s wall, 1.484375s user + 0.000000s system = 1.484375s CPU (101.2%)

OPT-1001 : Current memory(MB): used = 717, reserve = 702, peak = 725.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19431 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.840506s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (98.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17563/19433.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 742632, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.151454s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (92.8%)

PHY-1001 : Congestion index: top1 = 47.76, top5 = 43.10, top10 = 40.53, top15 = 38.94.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19431 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.853902s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (98.8%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4365 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 47.310345
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4365ps with logic level 3 
RUN-1001 :       #2 path slack 4415ps with logic level 3 
RUN-1001 :       #3 path slack 4430ps with logic level 3 
OPT-1001 : End physical optimization;  15.506207s wall, 16.718750s user + 0.140625s system = 16.859375s CPU (108.7%)

RUN-1003 : finish command "place" in  75.933083s wall, 130.953125s user + 7.890625s system = 138.843750s CPU (182.9%)

RUN-1004 : used memory is 636 MB, reserved memory is 616 MB, peak memory is 725 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.602993s wall, 2.703125s user + 0.031250s system = 2.734375s CPU (170.6%)

RUN-1004 : used memory is 637 MB, reserved memory is 617 MB, peak memory is 725 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8146 instances
RUN-1001 : 4024 mslices, 4018 lslices, 59 pads, 40 brams, 0 dsps
RUN-1001 : There are total 19433 nets
RUN-1001 : 13615 nets have 2 pins
RUN-1001 : 4389 nets have [3 - 5] pins
RUN-1001 : 896 nets have [6 - 10] pins
RUN-1001 : 389 nets have [11 - 20] pins
RUN-1001 : 135 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67918, tnet num: 19431, tinst num: 8144, tnode num: 92101, tedge num: 112026.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.139904s wall, 2.140625s user + 0.000000s system = 2.140625s CPU (100.0%)

RUN-1004 : used memory is 642 MB, reserved memory is 639 MB, peak memory is 725 MB
PHY-1001 : 4024 mslices, 4018 lslices, 59 pads, 40 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19431 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 683008, over cnt = 2332(6%), over = 3842, worst = 7
PHY-1002 : len = 700328, over cnt = 1285(3%), over = 1755, worst = 5
PHY-1002 : len = 714744, over cnt = 511(1%), over = 696, worst = 5
PHY-1002 : len = 726784, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 726912, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.898235s wall, 3.296875s user + 0.046875s system = 3.343750s CPU (176.2%)

PHY-1001 : Congestion index: top1 = 47.52, top5 = 42.93, top10 = 40.24, top15 = 38.58.
PHY-1001 : End global routing;  2.295555s wall, 3.703125s user + 0.046875s system = 3.750000s CPU (163.4%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 705, reserve = 695, peak = 725.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 974, reserve = 961, peak = 974.
PHY-1001 : End build detailed router design. 4.468100s wall, 4.421875s user + 0.046875s system = 4.468750s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 191496, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.848660s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (99.4%)

PHY-1001 : Current memory(MB): used = 1010, reserve = 997, peak = 1010.
PHY-1001 : End phase 1; 0.858206s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (100.1%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.70924e+06, over cnt = 1355(0%), over = 1362, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1023, reserve = 1009, peak = 1023.
PHY-1001 : End initial routed; 16.147260s wall, 49.328125s user + 0.328125s system = 49.656250s CPU (307.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18161(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.283   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.159056s wall, 3.156250s user + 0.000000s system = 3.156250s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 1033, reserve = 1019, peak = 1033.
PHY-1001 : End phase 2; 19.306453s wall, 52.484375s user + 0.328125s system = 52.812500s CPU (273.5%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.70924e+06, over cnt = 1355(0%), over = 1362, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.222075s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (105.5%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.69279e+06, over cnt = 483(0%), over = 483, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.983034s wall, 1.468750s user + 0.000000s system = 1.468750s CPU (149.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.69298e+06, over cnt = 83(0%), over = 83, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.471185s wall, 0.640625s user + 0.000000s system = 0.640625s CPU (136.0%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.69327e+06, over cnt = 19(0%), over = 19, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.229429s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (115.8%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.6935e+06, over cnt = 8(0%), over = 8, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.157801s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (118.8%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.69363e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.145749s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (96.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18161(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.283   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.172025s wall, 3.171875s user + 0.000000s system = 3.171875s CPU (100.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 365 feed throughs used by 302 nets
PHY-1001 : End commit to database; 2.032448s wall, 2.015625s user + 0.015625s system = 2.031250s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 1119, reserve = 1108, peak = 1119.
PHY-1001 : End phase 3; 7.867322s wall, 8.562500s user + 0.015625s system = 8.578125s CPU (109.0%)

PHY-1003 : Routed, final wirelength = 1.69363e+06
PHY-1001 : Current memory(MB): used = 1123, reserve = 1113, peak = 1123.
PHY-1001 : End export database. 0.054609s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (114.4%)

PHY-1001 : End detail routing;  32.932808s wall, 66.734375s user + 0.406250s system = 67.140625s CPU (203.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67918, tnet num: 19431, tinst num: 8144, tnode num: 92101, tedge num: 112026.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.559989s wall, 1.562500s user + 0.000000s system = 1.562500s CPU (100.2%)

RUN-1004 : used memory is 1058 MB, reserved memory is 1050 MB, peak memory is 1123 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  41.559231s wall, 76.781250s user + 0.453125s system = 77.234375s CPU (185.8%)

RUN-1004 : used memory is 1057 MB, reserved memory is 1050 MB, peak memory is 1123 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8861   out of  19600   45.21%
#reg                    12423   out of  19600   63.38%
#le                     15009
  #lut only              2586   out of  15009   17.23%
  #reg only              6148   out of  15009   40.96%
  #lut&reg               6275   out of  15009   41.81%
#dsp                        0   out of     29    0.00%
#bram                      40   out of     64   62.50%
  #bram9k                  38
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       59   out of    188   31.38%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6827
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          156
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         F2        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         D1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         B3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         C1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         A3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         A2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         B2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         B1        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F15        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDZ        INPUT        K15        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         P2        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         N4        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         M5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         N6        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         N5        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         P5        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M3        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         M4        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         H2        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         M1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         L1        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         N1        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         N3        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         M2        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT         T5        LVCMOS33           8            NONE           OREG       
    Fog_ENY        OUTPUT         T4        LVCMOS33           8            NONE           OREG       
    Fog_ENZ        OUTPUT         T7        LVCMOS33           8            NONE           OREG       
    Fog_INT        OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         T6        LVCMOS33           8            N/A            OREG       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E1        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        G11        LVCMOS25           8            N/A            NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15009  |7359    |1502    |12467   |40      |0       |
|  AnyFog_dataX                      |AnyFog          |209    |92      |22      |167     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |90     |68      |22      |48      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |208    |90      |22      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |60      |22      |51      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |207    |95      |22      |172     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |56      |22      |51      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2938   |700     |39      |2842    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |62     |35      |5       |53      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |217    |87      |5       |204     |0       |0       |
|    STADOP_com2                     |STADOP          |545    |87      |0       |538     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |64     |44      |14      |41      |0       |0       |
|    head_com2                       |uniheading      |276    |63      |5       |259     |0       |0       |
|    rmc_com2                        |Gprmc           |37     |36      |0       |30      |0       |0       |
|    uart_com2                       |Agrica          |1426   |315     |10      |1406    |0       |0       |
|  COM3                              |COM3_Control    |273    |127     |19      |237     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |59     |48      |5       |51      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |61     |38      |14      |38      |0       |0       |
|    rmc_com3                        |Gprmc           |153    |41      |0       |148     |0       |0       |
|  DATA                              |Data_Processing |8831   |4514    |1122    |7032    |0       |0       |
|    DIV_Dtemp                       |Divider         |767    |266     |84      |646     |0       |0       |
|    DIV_Utemp                       |Divider         |643    |288     |84      |518     |0       |0       |
|    DIV_accX                        |Divider         |626    |337     |84      |495     |0       |0       |
|    DIV_accY                        |Divider         |633    |365     |102     |478     |0       |0       |
|    DIV_accZ                        |Divider         |706    |414     |132     |499     |0       |0       |
|    DIV_rateX                       |Divider         |629    |351     |132     |416     |0       |0       |
|    DIV_rateY                       |Divider         |567    |375     |132     |362     |0       |0       |
|    DIV_rateZ                       |Divider         |567    |342     |132     |362     |0       |0       |
|    genclk                          |genclk          |268    |174     |89      |107     |0       |0       |
|  FMC                               |FMC_Ctrl        |444    |393     |43      |346     |0       |0       |
|  IIC                               |I2C_master      |284    |225     |11      |261     |0       |0       |
|  IMU_CTRL                          |SCHA634         |895    |669     |61      |724     |0       |0       |
|    CtrlData                        |CtrlData        |453    |399     |47      |327     |0       |0       |
|      usms                          |Time_1ms        |28     |22      |5       |20      |0       |0       |
|    SPIM                            |SPI_SCHA634     |442    |270     |14      |397     |0       |0       |
|  POWER                             |POWER_EN        |94     |49      |38      |35      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |616    |403     |103     |424     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |616    |403     |103     |424     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |275    |166     |0       |261     |0       |0       |
|        reg_inst                    |register        |273    |166     |0       |259     |0       |0       |
|        tap_inst                    |tap             |2      |0       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |341    |237     |103     |163     |0       |0       |
|        bus_inst                    |bus_top         |134    |88      |46      |53      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |26     |16      |10      |9       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |3      |3       |0       |3       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |53     |35      |18      |21      |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |52     |34      |18      |20      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |133    |103     |29      |83      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13555  
    #2          2       3397   
    #3          3        668   
    #4          4        324   
    #5        5-10       972   
    #6        11-50      440   
    #7       51-100       8    
    #8       101-500      3    
    #9        >500        2    
  Average     2.14             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  1.853628s wall, 3.250000s user + 0.000000s system = 3.250000s CPU (175.3%)

RUN-1004 : used memory is 1057 MB, reserved memory is 1051 MB, peak memory is 1123 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67918, tnet num: 19431, tinst num: 8144, tnode num: 92101, tedge num: 112026.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.600968s wall, 1.609375s user + 0.000000s system = 1.609375s CPU (100.5%)

RUN-1004 : used memory is 1059 MB, reserved memory is 1052 MB, peak memory is 1123 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19431 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.245761s wall, 1.218750s user + 0.015625s system = 1.234375s CPU (99.1%)

RUN-1004 : used memory is 1064 MB, reserved memory is 1057 MB, peak memory is 1123 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 19285304e968e394d67a8f065444a1f66c49f77adeb419c41e6b7764d8228547 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8144
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19433, pip num: 147286
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 365
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3224 valid insts, and 412961 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011111000000101011000101
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  10.794284s wall, 104.062500s user + 0.218750s system = 104.281250s CPU (966.1%)

RUN-1004 : used memory is 1191 MB, reserved memory is 1176 MB, peak memory is 1306 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250821_133355.log"

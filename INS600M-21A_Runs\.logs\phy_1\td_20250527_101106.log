============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Tue May 27 10:11:06 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(247)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(252)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(273)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(278)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(421)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(428)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(435)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(442)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(449)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(456)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(463)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(470)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(647)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(652)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(680)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(685)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(919)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(926)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(933)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(940)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(947)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(952)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(959)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(966)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(973)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(980)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(514)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
RUN-1001 : Project manager successfully analyzed 21 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.527312s wall, 1.562500s user + 3.968750s system = 5.531250s CPU (100.1%)

RUN-1004 : used memory is 79 MB, reserved memory is 40 MB, peak memory is 90 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.728658s wall, 1.671875s user + 0.062500s system = 1.734375s CPU (100.3%)

RUN-1004 : used memory is 299 MB, reserved memory is 267 MB, peak memory is 302 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 58 trigger nets, 58 data nets.
KIT-1004 : Chipwatcher code = 1110111010000001
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=158) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=158) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=158)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=158)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22947/23 useful/useless nets, 19663/12 useful/useless insts
SYN-1016 : Merged 29 instances.
SYN-1032 : 22551/20 useful/useless nets, 20169/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 478 better
SYN-1014 : Optimize round 2
SYN-1032 : 22167/45 useful/useless nets, 19785/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.677989s wall, 2.609375s user + 0.062500s system = 2.671875s CPU (99.8%)

RUN-1004 : used memory is 328 MB, reserved memory is 294 MB, peak memory is 330 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22239/441 useful/useless nets, 19908/63 useful/useless insts
SYN-1016 : Merged 69 instances.
SYN-2571 : Optimize after map_dsp, round 1, 573 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 56 instances.
SYN-2501 : Optimize round 1, 114 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 22743/5 useful/useless nets, 20412/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83800, tnet num: 22743, tinst num: 20411, tnode num: 117656, tedge num: 130540.
TMR-2508 : Levelizing timing graph completed, there are 85 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.218839s wall, 1.203125s user + 0.015625s system = 1.218750s CPU (100.0%)

RUN-1004 : used memory is 471 MB, reserved memory is 439 MB, peak memory is 471 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22743 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 282 (3.29), #lev = 7 (1.66)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 282 (3.29), #lev = 7 (1.66)
SYN-3001 : Logic optimization runtime opt =   0.04 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 614 instances into 282 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 487 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 160 adder to BLE ...
SYN-4008 : Packed 160 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  5.015951s wall, 4.953125s user + 0.078125s system = 5.031250s CPU (100.3%)

RUN-1004 : used memory is 355 MB, reserved memory is 321 MB, peak memory is 581 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  8.060747s wall, 7.859375s user + 0.203125s system = 8.062500s CPU (100.0%)

RUN-1004 : used memory is 355 MB, reserved memory is 322 MB, peak memory is 581 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/rmc_com2/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/rmc_com2/GPRMC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/rmc_com2/GPRMC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/rmc_com2/GPRMC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/rmc_com2/GPRMC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/rmc_com2/GPRMC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/rmc_com2/GPRMC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/rmc_com2/GPRMC_data[0]
SYN-5055 WARNING: The kept net COM2/rx_err will be merged to another kept net COM2/rmc_com2/GPRMC_err
SYN-5055 WARNING: The kept net FMC/Error_state[0] will be merged to another kept net COM2/rmc_com2/GPRMC_sub_state_cnt0[3]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (335 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19736 instances
RUN-0007 : 5655 luts, 12534 seqs, 943 mslices, 491 lslices, 56 pads, 52 brams, 0 dsps
RUN-1001 : There are total 22091 nets
RUN-1001 : 16503 nets have 2 pins
RUN-1001 : 4430 nets have [3 - 5] pins
RUN-1001 : 779 nets have [6 - 10] pins
RUN-1001 : 255 nets have [11 - 20] pins
RUN-1001 : 100 nets have [21 - 99] pins
RUN-1001 : 24 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4743     
RUN-1001 :   No   |  No   |  Yes  |     636     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    6556     
RUN-1001 :   Yes  |  No   |  Yes  |     499     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  118  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 126
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19734 instances, 5655 luts, 12534 seqs, 1434 slices, 280 macros(1434 instances: 943 mslices 491 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1131 pins
PHY-3001 : Huge net DATA/done_div with 1644 pins
PHY-0007 : Cell area utilization is 64%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82327, tnet num: 22089, tinst num: 19734, tnode num: 116318, tedge num: 129386.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.223934s wall, 1.234375s user + 0.000000s system = 1.234375s CPU (100.9%)

RUN-1004 : used memory is 533 MB, reserved memory is 505 MB, peak memory is 581 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22089 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.179216s wall, 2.125000s user + 0.062500s system = 2.187500s CPU (100.4%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.48956e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19734.
PHY-3001 : Level 1 #clusters 2137.
PHY-3001 : End clustering;  0.159661s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (137.0%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 64%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 916508, overlap = 654.031
PHY-3002 : Step(2): len = 845677, overlap = 724.906
PHY-3002 : Step(3): len = 523271, overlap = 896.938
PHY-3002 : Step(4): len = 456542, overlap = 972.938
PHY-3002 : Step(5): len = 369275, overlap = 1058
PHY-3002 : Step(6): len = 331207, overlap = 1133.22
PHY-3002 : Step(7): len = 278472, overlap = 1203.78
PHY-3002 : Step(8): len = 251542, overlap = 1256.53
PHY-3002 : Step(9): len = 224077, overlap = 1302.78
PHY-3002 : Step(10): len = 212647, overlap = 1341.09
PHY-3002 : Step(11): len = 185173, overlap = 1389.81
PHY-3002 : Step(12): len = 167207, overlap = 1423.62
PHY-3002 : Step(13): len = 151637, overlap = 1462.88
PHY-3002 : Step(14): len = 141979, overlap = 1504.03
PHY-3002 : Step(15): len = 129067, overlap = 1520.47
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.01841e-06
PHY-3002 : Step(16): len = 134992, overlap = 1517.53
PHY-3002 : Step(17): len = 184328, overlap = 1436.16
PHY-3002 : Step(18): len = 202017, overlap = 1283.69
PHY-3002 : Step(19): len = 200640, overlap = 1249.5
PHY-3002 : Step(20): len = 195872, overlap = 1206.78
PHY-3002 : Step(21): len = 189424, overlap = 1193.38
PHY-3002 : Step(22): len = 184323, overlap = 1197.5
PHY-3002 : Step(23): len = 179947, overlap = 1194.22
PHY-3002 : Step(24): len = 176597, overlap = 1170.38
PHY-3002 : Step(25): len = 172275, overlap = 1176.47
PHY-3002 : Step(26): len = 170105, overlap = 1177.12
PHY-3002 : Step(27): len = 166795, overlap = 1167.28
PHY-3002 : Step(28): len = 166889, overlap = 1158.38
PHY-3002 : Step(29): len = 167224, overlap = 1157.91
PHY-3002 : Step(30): len = 166843, overlap = 1159.72
PHY-3002 : Step(31): len = 165718, overlap = 1167.59
PHY-3002 : Step(32): len = 164678, overlap = 1180.97
PHY-3002 : Step(33): len = 162759, overlap = 1182.47
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.03682e-06
PHY-3002 : Step(34): len = 170126, overlap = 1156.94
PHY-3002 : Step(35): len = 185640, overlap = 1074.78
PHY-3002 : Step(36): len = 189857, overlap = 1034.16
PHY-3002 : Step(37): len = 190991, overlap = 1033.91
PHY-3002 : Step(38): len = 191446, overlap = 1019.16
PHY-3002 : Step(39): len = 191726, overlap = 1024.81
PHY-3002 : Step(40): len = 190251, overlap = 1028.22
PHY-3002 : Step(41): len = 189629, overlap = 1036.38
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.07364e-06
PHY-3002 : Step(42): len = 198805, overlap = 968.906
PHY-3002 : Step(43): len = 216192, overlap = 871.875
PHY-3002 : Step(44): len = 222830, overlap = 797.156
PHY-3002 : Step(45): len = 225878, overlap = 763
PHY-3002 : Step(46): len = 226788, overlap = 737.625
PHY-3002 : Step(47): len = 224425, overlap = 705.219
PHY-3002 : Step(48): len = 223354, overlap = 702.25
PHY-3002 : Step(49): len = 221920, overlap = 723.406
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 8.14728e-06
PHY-3002 : Step(50): len = 232787, overlap = 683.656
PHY-3002 : Step(51): len = 247823, overlap = 627.5
PHY-3002 : Step(52): len = 252500, overlap = 576.438
PHY-3002 : Step(53): len = 253453, overlap = 532.312
PHY-3002 : Step(54): len = 252521, overlap = 537.031
PHY-3002 : Step(55): len = 250906, overlap = 544.469
PHY-3002 : Step(56): len = 248738, overlap = 541.469
PHY-3002 : Step(57): len = 248231, overlap = 545.188
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.62946e-05
PHY-3002 : Step(58): len = 258023, overlap = 500.688
PHY-3002 : Step(59): len = 269946, overlap = 469.125
PHY-3002 : Step(60): len = 275120, overlap = 456.781
PHY-3002 : Step(61): len = 277864, overlap = 431.406
PHY-3002 : Step(62): len = 276650, overlap = 432.312
PHY-3002 : Step(63): len = 274158, overlap = 419.312
PHY-3002 : Step(64): len = 272546, overlap = 416.219
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.25891e-05
PHY-3002 : Step(65): len = 283870, overlap = 417.438
PHY-3002 : Step(66): len = 292953, overlap = 399.812
PHY-3002 : Step(67): len = 294739, overlap = 395.094
PHY-3002 : Step(68): len = 294869, overlap = 396.844
PHY-3002 : Step(69): len = 293090, overlap = 403
PHY-3002 : Step(70): len = 292307, overlap = 389.938
PHY-3002 : Step(71): len = 290219, overlap = 376.75
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 6.51783e-05
PHY-3002 : Step(72): len = 298885, overlap = 395.031
PHY-3002 : Step(73): len = 306616, overlap = 369.906
PHY-3002 : Step(74): len = 309184, overlap = 371.25
PHY-3002 : Step(75): len = 310211, overlap = 361.719
PHY-3002 : Step(76): len = 309534, overlap = 337.656
PHY-3002 : Step(77): len = 308889, overlap = 331
PHY-3002 : Step(78): len = 307322, overlap = 316.344
PHY-3002 : Step(79): len = 308390, overlap = 328.344
PHY-3002 : Step(80): len = 307368, overlap = 326.688
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000130357
PHY-3002 : Step(81): len = 312858, overlap = 312.062
PHY-3002 : Step(82): len = 317411, overlap = 312.312
PHY-3002 : Step(83): len = 318791, overlap = 305.031
PHY-3002 : Step(84): len = 319807, overlap = 300.781
PHY-3002 : Step(85): len = 319432, overlap = 293.844
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000241774
PHY-3002 : Step(86): len = 321503, overlap = 303.938
PHY-3002 : Step(87): len = 324977, overlap = 297.594
PHY-3002 : Step(88): len = 326415, overlap = 288
PHY-3002 : Step(89): len = 327956, overlap = 285.781
PHY-3002 : Step(90): len = 327689, overlap = 282.219
PHY-3002 : Step(91): len = 327571, overlap = 269.75
PHY-3002 : Step(92): len = 327070, overlap = 260.781
PHY-3002 : Step(93): len = 326718, overlap = 257.969
PHY-3002 : Step(94): len = 326830, overlap = 265.344
PHY-3002 : Step(95): len = 327071, overlap = 268.969
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(96): len = 328055, overlap = 265.125
PHY-3002 : Step(97): len = 329946, overlap = 261.594
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.011655s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/22091.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 440680, over cnt = 1257(3%), over = 5730, worst = 45
PHY-1001 : End global iterations;  0.874002s wall, 1.343750s user + 0.078125s system = 1.421875s CPU (162.7%)

PHY-1001 : Congestion index: top1 = 73.04, top5 = 53.32, top10 = 43.82, top15 = 38.34.
PHY-3001 : End congestion estimation;  1.136110s wall, 1.578125s user + 0.093750s system = 1.671875s CPU (147.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22089 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.018207s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (101.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000112967
PHY-3002 : Step(98): len = 373780, overlap = 175.844
PHY-3002 : Step(99): len = 383981, overlap = 168.781
PHY-3002 : Step(100): len = 386942, overlap = 185.281
PHY-3002 : Step(101): len = 387006, overlap = 180
PHY-3002 : Step(102): len = 389520, overlap = 173.25
PHY-3002 : Step(103): len = 395082, overlap = 167.031
PHY-3002 : Step(104): len = 397592, overlap = 162.625
PHY-3002 : Step(105): len = 399912, overlap = 165.25
PHY-3002 : Step(106): len = 399865, overlap = 157.469
PHY-3002 : Step(107): len = 401248, overlap = 153.375
PHY-3002 : Step(108): len = 400659, overlap = 149.312
PHY-3002 : Step(109): len = 400700, overlap = 137.781
PHY-3002 : Step(110): len = 401578, overlap = 133.125
PHY-3002 : Step(111): len = 401102, overlap = 122.375
PHY-3002 : Step(112): len = 401476, overlap = 126.438
PHY-3002 : Step(113): len = 401253, overlap = 126.625
PHY-3002 : Step(114): len = 402049, overlap = 124.938
PHY-3002 : Step(115): len = 402097, overlap = 122.094
PHY-3002 : Step(116): len = 401425, overlap = 120.875
PHY-3002 : Step(117): len = 401784, overlap = 118.438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(118): len = 401730, overlap = 115.469
PHY-3002 : Step(119): len = 402273, overlap = 114.438
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 117/22091.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 479160, over cnt = 2111(5%), over = 9514, worst = 38
PHY-1001 : End global iterations;  1.128423s wall, 1.828125s user + 0.000000s system = 1.828125s CPU (162.0%)

PHY-1001 : Congestion index: top1 = 76.83, top5 = 55.79, top10 = 47.21, top15 = 42.33.
PHY-3001 : End congestion estimation;  1.413115s wall, 2.093750s user + 0.015625s system = 2.109375s CPU (149.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22089 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.031625s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.15347e-05
PHY-3002 : Step(120): len = 409488, overlap = 387.875
PHY-3002 : Step(121): len = 419812, overlap = 343.438
PHY-3002 : Step(122): len = 413904, overlap = 331.875
PHY-3002 : Step(123): len = 410693, overlap = 309.25
PHY-3002 : Step(124): len = 411725, overlap = 303.688
PHY-3002 : Step(125): len = 412208, overlap = 285.844
PHY-3002 : Step(126): len = 411760, overlap = 269.812
PHY-3002 : Step(127): len = 410940, overlap = 258.125
PHY-3002 : Step(128): len = 411235, overlap = 257.969
PHY-3002 : Step(129): len = 411646, overlap = 253.906
PHY-3002 : Step(130): len = 410476, overlap = 249.812
PHY-3002 : Step(131): len = 409604, overlap = 248.812
PHY-3002 : Step(132): len = 409875, overlap = 247.438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000163069
PHY-3002 : Step(133): len = 409034, overlap = 236.062
PHY-3002 : Step(134): len = 410840, overlap = 225.906
PHY-3002 : Step(135): len = 412463, overlap = 219.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000326139
PHY-3002 : Step(136): len = 414030, overlap = 217.219
PHY-3002 : Step(137): len = 421027, overlap = 192.938
PHY-3002 : Step(138): len = 426313, overlap = 181.656
PHY-3002 : Step(139): len = 425649, overlap = 185.438
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000652278
PHY-3002 : Step(140): len = 426847, overlap = 176.438
PHY-3002 : Step(141): len = 430259, overlap = 167.656
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82327, tnet num: 22089, tinst num: 19734, tnode num: 116318, tedge num: 129386.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.533125s wall, 1.484375s user + 0.046875s system = 1.531250s CPU (99.9%)

RUN-1004 : used memory is 572 MB, reserved memory is 546 MB, peak memory is 708 MB
OPT-1001 : Total overflow 548.28 peak overflow 4.59
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 764/22091.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 516272, over cnt = 2480(7%), over = 8946, worst = 24
PHY-1001 : End global iterations;  1.249844s wall, 1.937500s user + 0.046875s system = 1.984375s CPU (158.8%)

PHY-1001 : Congestion index: top1 = 56.29, top5 = 47.15, top10 = 42.40, top15 = 39.40.
PHY-1001 : End incremental global routing;  1.603453s wall, 2.296875s user + 0.046875s system = 2.343750s CPU (146.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22089 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.076338s wall, 1.031250s user + 0.031250s system = 1.062500s CPU (98.7%)

OPT-1001 : 20 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 56 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19655 has valid locations, 273 needs to be replaced
PHY-3001 : design contains 19987 instances, 5762 luts, 12680 seqs, 1434 slices, 280 macros(1434 instances: 943 mslices 491 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 448305
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16663/22344.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 532512, over cnt = 2535(7%), over = 9098, worst = 24
PHY-1001 : End global iterations;  0.199065s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (86.3%)

PHY-1001 : Congestion index: top1 = 56.59, top5 = 47.33, top10 = 42.70, top15 = 39.72.
PHY-3001 : End congestion estimation;  0.499743s wall, 0.484375s user + 0.000000s system = 0.484375s CPU (96.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83194, tnet num: 22342, tinst num: 19987, tnode num: 117545, tedge num: 130614.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.551247s wall, 1.515625s user + 0.031250s system = 1.546875s CPU (99.7%)

RUN-1004 : used memory is 616 MB, reserved memory is 605 MB, peak memory is 709 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22342 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.756309s wall, 2.703125s user + 0.046875s system = 2.750000s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(142): len = 447844, overlap = 4.28125
PHY-3002 : Step(143): len = 448928, overlap = 4.28125
PHY-3002 : Step(144): len = 449728, overlap = 4.21875
PHY-3002 : Step(145): len = 450765, overlap = 4.28125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 16689/22344.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 529688, over cnt = 2560(7%), over = 9205, worst = 24
PHY-1001 : End global iterations;  0.214086s wall, 0.312500s user + 0.031250s system = 0.343750s CPU (160.6%)

PHY-1001 : Congestion index: top1 = 56.79, top5 = 47.51, top10 = 42.83, top15 = 39.90.
PHY-3001 : End congestion estimation;  0.471746s wall, 0.578125s user + 0.031250s system = 0.609375s CPU (129.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22342 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.243824s wall, 1.218750s user + 0.015625s system = 1.234375s CPU (99.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000770315
PHY-3002 : Step(146): len = 450952, overlap = 170.125
PHY-3002 : Step(147): len = 451259, overlap = 170.156
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00154019
PHY-3002 : Step(148): len = 451577, overlap = 170.25
PHY-3002 : Step(149): len = 452142, overlap = 170.438
PHY-3001 : Final: Len = 452142, Over = 170.438
PHY-3001 : End incremental placement;  5.852779s wall, 6.062500s user + 0.250000s system = 6.312500s CPU (107.9%)

OPT-1001 : Total overflow 553.62 peak overflow 4.59
OPT-1001 : End high-fanout net optimization;  9.128919s wall, 10.171875s user + 0.328125s system = 10.500000s CPU (115.0%)

OPT-1001 : Current memory(MB): used = 713, reserve = 692, peak = 730.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16700/22344.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 533040, over cnt = 2519(7%), over = 8552, worst = 24
PHY-1002 : len = 574656, over cnt = 1786(5%), over = 4764, worst = 22
PHY-1002 : len = 601376, over cnt = 1013(2%), over = 2730, worst = 22
PHY-1002 : len = 634840, over cnt = 298(0%), over = 852, worst = 15
PHY-1002 : len = 649672, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.603775s wall, 2.234375s user + 0.015625s system = 2.250000s CPU (140.3%)

PHY-1001 : Congestion index: top1 = 48.77, top5 = 43.87, top10 = 40.75, top15 = 38.64.
OPT-1001 : End congestion update;  1.868277s wall, 2.500000s user + 0.015625s system = 2.515625s CPU (134.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22342 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.930388s wall, 0.906250s user + 0.015625s system = 0.921875s CPU (99.1%)

OPT-0007 : Start: WNS 4467 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.804847s wall, 3.421875s user + 0.031250s system = 3.453125s CPU (123.1%)

OPT-1001 : Current memory(MB): used = 687, reserve = 669, peak = 730.
OPT-1001 : End physical optimization;  13.801990s wall, 15.546875s user + 0.437500s system = 15.984375s CPU (115.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5762 LUT to BLE ...
SYN-4008 : Packed 5762 LUT and 2876 SEQ to BLE.
SYN-4003 : Packing 9804 remaining SEQ's ...
SYN-4005 : Packed 3210 SEQ with LUT/SLICE
SYN-4006 : 187 single LUT's are left
SYN-4006 : 6594 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 12356/14023 primitive instances ...
PHY-3001 : End packing;  2.821581s wall, 2.812500s user + 0.000000s system = 2.812500s CPU (99.7%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8261 instances
RUN-1001 : 4074 mslices, 4074 lslices, 56 pads, 52 brams, 0 dsps
RUN-1001 : There are total 19514 nets
RUN-1001 : 13578 nets have 2 pins
RUN-1001 : 4546 nets have [3 - 5] pins
RUN-1001 : 842 nets have [6 - 10] pins
RUN-1001 : 398 nets have [11 - 20] pins
RUN-1001 : 140 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
PHY-3001 : design contains 8259 instances, 8148 slices, 280 macros(1434 instances: 943 mslices 491 lslices)
PHY-3001 : Cell area utilization is 86%
PHY-3001 : After packing: Len = 474237, Over = 402.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7734/19514.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 614776, over cnt = 1660(4%), over = 2619, worst = 9
PHY-1002 : len = 620704, over cnt = 1090(3%), over = 1493, worst = 9
PHY-1002 : len = 632192, over cnt = 450(1%), over = 603, worst = 9
PHY-1002 : len = 636712, over cnt = 235(0%), over = 333, worst = 4
PHY-1002 : len = 643784, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.344448s wall, 2.125000s user + 0.078125s system = 2.203125s CPU (163.9%)

PHY-1001 : Congestion index: top1 = 50.15, top5 = 43.84, top10 = 40.47, top15 = 38.32.
PHY-3001 : End congestion estimation;  1.688512s wall, 2.453125s user + 0.093750s system = 2.546875s CPU (150.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68613, tnet num: 19512, tinst num: 8259, tnode num: 93541, tedge num: 113031.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.715922s wall, 1.718750s user + 0.000000s system = 1.718750s CPU (100.2%)

RUN-1004 : used memory is 606 MB, reserved memory is 597 MB, peak memory is 730 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19512 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.768371s wall, 2.750000s user + 0.015625s system = 2.765625s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.68648e-05
PHY-3002 : Step(150): len = 476839, overlap = 390.25
PHY-3002 : Step(151): len = 476914, overlap = 401.25
PHY-3002 : Step(152): len = 478508, overlap = 392
PHY-3002 : Step(153): len = 481227, overlap = 400.25
PHY-3002 : Step(154): len = 480742, overlap = 406
PHY-3002 : Step(155): len = 478360, overlap = 404
PHY-3002 : Step(156): len = 478511, overlap = 406.25
PHY-3002 : Step(157): len = 476921, overlap = 409
PHY-3002 : Step(158): len = 476474, overlap = 413.5
PHY-3002 : Step(159): len = 475307, overlap = 419.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.37295e-05
PHY-3002 : Step(160): len = 479968, overlap = 403.5
PHY-3002 : Step(161): len = 483962, overlap = 396.25
PHY-3002 : Step(162): len = 484754, overlap = 388.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000186414
PHY-3002 : Step(163): len = 496565, overlap = 382.25
PHY-3002 : Step(164): len = 507797, overlap = 364
PHY-3002 : Step(165): len = 504523, overlap = 366.25
PHY-3002 : Step(166): len = 501858, overlap = 367.25
PHY-3002 : Step(167): len = 500944, overlap = 374.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.682912s wall, 0.828125s user + 0.843750s system = 1.671875s CPU (244.8%)

PHY-3001 : Trial Legalized: Len = 622806
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 567/19514.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 714872, over cnt = 2541(7%), over = 4125, worst = 8
PHY-1002 : len = 729848, over cnt = 1505(4%), over = 2167, worst = 8
PHY-1002 : len = 746624, over cnt = 611(1%), over = 890, worst = 6
PHY-1002 : len = 756736, over cnt = 232(0%), over = 340, worst = 5
PHY-1002 : len = 763224, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.954582s wall, 3.531250s user + 0.046875s system = 3.578125s CPU (183.1%)

PHY-1001 : Congestion index: top1 = 50.80, top5 = 45.88, top10 = 43.15, top15 = 41.46.
PHY-3001 : End congestion estimation;  2.334700s wall, 3.906250s user + 0.046875s system = 3.953125s CPU (169.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19512 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.973607s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000191156
PHY-3002 : Step(168): len = 579903, overlap = 84
PHY-3002 : Step(169): len = 561398, overlap = 131
PHY-3002 : Step(170): len = 549015, overlap = 173.75
PHY-3002 : Step(171): len = 540810, overlap = 217
PHY-3002 : Step(172): len = 536118, overlap = 251.5
PHY-3002 : Step(173): len = 533168, overlap = 274
PHY-3002 : Step(174): len = 530981, overlap = 285
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000382312
PHY-3002 : Step(175): len = 535419, overlap = 276.75
PHY-3002 : Step(176): len = 541795, overlap = 268.25
PHY-3002 : Step(177): len = 542636, overlap = 266
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000751025
PHY-3002 : Step(178): len = 545505, overlap = 262
PHY-3002 : Step(179): len = 553627, overlap = 257
PHY-3002 : Step(180): len = 556368, overlap = 251.75
PHY-3002 : Step(181): len = 558472, overlap = 257
PHY-3002 : Step(182): len = 560940, overlap = 256.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.034987s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (89.3%)

PHY-3001 : Legalized: Len = 603490, Over = 0
PHY-3001 : Spreading special nets. 58 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.094138s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (99.6%)

PHY-3001 : 83 instances has been re-located, deltaX = 23, deltaY = 50, maxDist = 2.
PHY-3001 : Final: Len = 604278, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68613, tnet num: 19512, tinst num: 8259, tnode num: 93541, tedge num: 113031.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.978009s wall, 1.984375s user + 0.000000s system = 1.984375s CPU (100.3%)

RUN-1004 : used memory is 610 MB, reserved memory is 601 MB, peak memory is 730 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3214/19514.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 705776, over cnt = 2335(6%), over = 3631, worst = 6
PHY-1002 : len = 720504, over cnt = 1236(3%), over = 1593, worst = 6
PHY-1002 : len = 735128, over cnt = 348(0%), over = 448, worst = 5
PHY-1002 : len = 738416, over cnt = 194(0%), over = 262, worst = 5
PHY-1002 : len = 743600, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.939329s wall, 3.281250s user + 0.015625s system = 3.296875s CPU (170.0%)

PHY-1001 : Congestion index: top1 = 47.56, top5 = 43.26, top10 = 40.76, top15 = 39.18.
PHY-1001 : End incremental global routing;  2.269273s wall, 3.609375s user + 0.015625s system = 3.625000s CPU (159.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19512 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.000870s wall, 0.953125s user + 0.046875s system = 1.000000s CPU (99.9%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 56 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8198 has valid locations, 15 needs to be replaced
PHY-3001 : design contains 8272 instances, 8161 slices, 280 macros(1434 instances: 943 mslices 491 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 607668
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17470/19525.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 746912, over cnt = 35(0%), over = 41, worst = 3
PHY-1002 : len = 746896, over cnt = 23(0%), over = 24, worst = 2
PHY-1002 : len = 747056, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 747176, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 747184, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.724833s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (107.8%)

PHY-1001 : Congestion index: top1 = 47.78, top5 = 43.40, top10 = 40.87, top15 = 39.26.
PHY-3001 : End congestion estimation;  1.056035s wall, 1.078125s user + 0.015625s system = 1.093750s CPU (103.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68699, tnet num: 19523, tinst num: 8272, tnode num: 93645, tedge num: 113133.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.993726s wall, 2.000000s user + 0.000000s system = 2.000000s CPU (100.3%)

RUN-1004 : used memory is 670 MB, reserved memory is 660 MB, peak memory is 730 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19523 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.009973s wall, 3.000000s user + 0.015625s system = 3.015625s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(183): len = 607295, overlap = 1
PHY-3002 : Step(184): len = 607183, overlap = 0.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17461/19525.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 745880, over cnt = 26(0%), over = 28, worst = 2
PHY-1002 : len = 745592, over cnt = 23(0%), over = 24, worst = 2
PHY-1002 : len = 745688, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 745752, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 745848, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.788120s wall, 0.812500s user + 0.015625s system = 0.828125s CPU (105.1%)

PHY-1001 : Congestion index: top1 = 47.72, top5 = 43.50, top10 = 40.96, top15 = 39.31.
PHY-3001 : End congestion estimation;  1.117492s wall, 1.140625s user + 0.015625s system = 1.156250s CPU (103.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19523 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.064805s wall, 1.062500s user + 0.000000s system = 1.062500s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000828776
PHY-3002 : Step(185): len = 607122, overlap = 2
PHY-3002 : Step(186): len = 607027, overlap = 1.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.012631s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (123.7%)

PHY-3001 : Legalized: Len = 607086, Over = 0
PHY-3001 : End spreading;  0.076565s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (102.0%)

PHY-3001 : Final: Len = 607086, Over = 0
PHY-3001 : End incremental placement;  7.017347s wall, 7.000000s user + 0.203125s system = 7.203125s CPU (102.6%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.823307s wall, 12.265625s user + 0.281250s system = 12.546875s CPU (115.9%)

OPT-1001 : Current memory(MB): used = 724, reserve = 706, peak = 730.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17461/19525.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 745856, over cnt = 22(0%), over = 22, worst = 1
PHY-1002 : len = 745784, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 745808, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 745864, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 745944, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.819666s wall, 0.890625s user + 0.031250s system = 0.921875s CPU (112.5%)

PHY-1001 : Congestion index: top1 = 47.67, top5 = 43.38, top10 = 40.84, top15 = 39.24.
OPT-1001 : End congestion update;  1.146239s wall, 1.203125s user + 0.031250s system = 1.234375s CPU (107.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19523 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.835600s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (101.0%)

OPT-0007 : Start: WNS 4916 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.987542s wall, 2.046875s user + 0.031250s system = 2.078125s CPU (104.6%)

OPT-1001 : Current memory(MB): used = 724, reserve = 706, peak = 730.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19523 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.850093s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (101.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17483/19525.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 745944, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.134532s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (92.9%)

PHY-1001 : Congestion index: top1 = 47.67, top5 = 43.38, top10 = 40.84, top15 = 39.24.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19523 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.827630s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (100.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4916 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 47.241379
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4916ps with logic level 6 
RUN-1001 :       #2 path slack 4916ps with logic level 6 
RUN-1001 :       #3 path slack 4916ps with logic level 6 
RUN-1001 :       #4 path slack 4916ps with logic level 6 
RUN-1001 :       #5 path slack 4923ps with logic level 8 
RUN-1001 :       #6 path slack 4923ps with logic level 8 
RUN-1001 :       #7 path slack 4959ps with logic level 6 
RUN-1001 :       #8 path slack 4959ps with logic level 6 
RUN-1001 :       #9 path slack 4959ps with logic level 6 
RUN-1001 :       #10 path slack 4959ps with logic level 6 
OPT-1001 : End physical optimization;  17.199139s wall, 18.703125s user + 0.312500s system = 19.015625s CPU (110.6%)

RUN-1003 : finish command "place" in  68.006145s wall, 118.046875s user + 7.390625s system = 125.437500s CPU (184.5%)

RUN-1004 : used memory is 640 MB, reserved memory is 617 MB, peak memory is 730 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.849865s wall, 3.125000s user + 0.031250s system = 3.156250s CPU (170.6%)

RUN-1004 : used memory is 640 MB, reserved memory is 618 MB, peak memory is 730 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8274 instances
RUN-1001 : 4079 mslices, 4082 lslices, 56 pads, 52 brams, 0 dsps
RUN-1001 : There are total 19525 nets
RUN-1001 : 13575 nets have 2 pins
RUN-1001 : 4545 nets have [3 - 5] pins
RUN-1001 : 852 nets have [6 - 10] pins
RUN-1001 : 403 nets have [11 - 20] pins
RUN-1001 : 140 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68699, tnet num: 19523, tinst num: 8272, tnode num: 93645, tedge num: 113133.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.873544s wall, 1.859375s user + 0.000000s system = 1.859375s CPU (99.2%)

RUN-1004 : used memory is 622 MB, reserved memory is 600 MB, peak memory is 730 MB
PHY-1001 : 4079 mslices, 4082 lslices, 56 pads, 52 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19523 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 687960, over cnt = 2404(6%), over = 3977, worst = 7
PHY-1002 : len = 701728, over cnt = 1561(4%), over = 2302, worst = 6
PHY-1002 : len = 721088, over cnt = 726(2%), over = 1027, worst = 6
PHY-1002 : len = 738040, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 738360, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.988284s wall, 3.296875s user + 0.078125s system = 3.375000s CPU (169.7%)

PHY-1001 : Congestion index: top1 = 48.99, top5 = 43.78, top10 = 41.00, top15 = 39.26.
PHY-1001 : End global routing;  2.363690s wall, 3.687500s user + 0.078125s system = 3.765625s CPU (159.3%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 702, reserve = 689, peak = 730.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 972, reserve = 960, peak = 972.
PHY-1001 : End build detailed router design. 4.667355s wall, 4.656250s user + 0.015625s system = 4.671875s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 197120, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.971150s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 1009, reserve = 998, peak = 1009.
PHY-1001 : End phase 1; 0.981070s wall, 0.984375s user + 0.000000s system = 0.984375s CPU (100.3%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.81786e+06, over cnt = 1421(0%), over = 1428, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1023, reserve = 1012, peak = 1023.
PHY-1001 : End initial routed; 27.378742s wall, 57.781250s user + 0.390625s system = 58.171875s CPU (212.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18313(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.234   |   0.000   |   0   
RUN-1001 :   Hold   |   0.112   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.757585s wall, 3.750000s user + 0.000000s system = 3.750000s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 1034, reserve = 1023, peak = 1034.
PHY-1001 : End phase 2; 31.136495s wall, 61.531250s user + 0.390625s system = 61.921875s CPU (198.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.81786e+06, over cnt = 1421(0%), over = 1428, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.259032s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (102.5%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.80406e+06, over cnt = 557(0%), over = 558, worst = 2, crit = 0
PHY-1001 : End DR Iter 1; 0.818874s wall, 1.531250s user + 0.031250s system = 1.562500s CPU (190.8%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.80342e+06, over cnt = 113(0%), over = 113, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.532394s wall, 0.796875s user + 0.015625s system = 0.812500s CPU (152.6%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.80484e+06, over cnt = 18(0%), over = 18, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.344100s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (118.1%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.80525e+06, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.306710s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (96.8%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.80528e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.334124s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (107.6%)

PHY-1001 : ===== DR Iter 6 =====
PHY-1022 : len = 1.80528e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 6; 0.697773s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (96.3%)

PHY-1001 : ===== DR Iter 7 =====
PHY-1022 : len = 1.80528e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 7; 0.868993s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (98.9%)

PHY-1001 : ===== DR Iter 8 =====
PHY-1022 : len = 1.80529e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 8; 0.203469s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (99.8%)

PHY-1001 : ==== DR Iter 9 ====
PHY-1022 : len = 1.80528e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 9; 0.209028s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (127.1%)

PHY-1001 : ==== DR Iter 10 ====
PHY-1022 : len = 1.80522e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 10; 0.205453s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (98.9%)

PHY-1001 : ==== DR Iter 11 ====
PHY-1022 : len = 1.80522e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 11; 0.337332s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (97.3%)

PHY-1001 : ==== DR Iter 12 ====
PHY-1022 : len = 1.80522e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 12; 0.473745s wall, 0.484375s user + 0.000000s system = 0.484375s CPU (102.2%)

PHY-1001 : ===== DR Iter 13 =====
PHY-1022 : len = 1.80522e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 13; 0.180923s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (95.0%)

PHY-1001 : ==== DR Iter 14 ====
PHY-1022 : len = 1.80526e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 14; 0.168738s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (101.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18313(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.186   |   0.000   |   0   
RUN-1001 :   Hold   |   0.112   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 4.185182s wall, 4.171875s user + 0.000000s system = 4.171875s CPU (99.7%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 351 feed throughs used by 294 nets
PHY-1001 : End commit to database; 2.478755s wall, 2.468750s user + 0.000000s system = 2.468750s CPU (99.6%)

PHY-1001 : Current memory(MB): used = 1124, reserve = 1116, peak = 1124.
PHY-1001 : End phase 3; 13.153062s wall, 14.203125s user + 0.062500s system = 14.265625s CPU (108.5%)

PHY-1003 : Routed, final wirelength = 1.80526e+06
PHY-1001 : Current memory(MB): used = 1129, reserve = 1120, peak = 1129.
PHY-1001 : End export database. 0.064327s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.2%)

PHY-1001 : End detail routing;  50.446297s wall, 81.890625s user + 0.468750s system = 82.359375s CPU (163.3%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68699, tnet num: 19523, tinst num: 8272, tnode num: 93645, tedge num: 113133.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.941958s wall, 1.937500s user + 0.000000s system = 1.937500s CPU (99.8%)

RUN-1004 : used memory is 1065 MB, reserved memory is 1061 MB, peak memory is 1129 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  59.497252s wall, 92.218750s user + 0.578125s system = 92.796875s CPU (156.0%)

RUN-1004 : used memory is 1065 MB, reserved memory is 1062 MB, peak memory is 1129 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8793   out of  19600   44.86%
#reg                    12777   out of  19600   65.19%
#le                     15345
  #lut only              2568   out of  15345   16.74%
  #reg only              6552   out of  15345   42.70%
  #lut&reg               6225   out of  15345   40.57%
#dsp                        0   out of     29    0.00%
#bram                      52   out of     64   81.25%
  #bram9k                  52
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       56   out of    188   29.79%
  #ireg                     9
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6953
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          195
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT        E11        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT        A13        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT        F10        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         E8        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         E7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT        A10        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C6        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         D6        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         C7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT        E12        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         A9        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         A7        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H13        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J11        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT        E13        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         R7        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         M7        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         P7        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         T8        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         P8        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         N8        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M9        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         T9        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         R9        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         T6        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         P9        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         N9        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT        P11        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         T5        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         B2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        L10        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        E15        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        R14        LVCMOS33           8            N/A            OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT         R1        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT        M12        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        K12        LVCMOS33           8            N/A            NONE       
    TXD_RMC        OUTPUT         C3        LVCMOS33           8            N/A            NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        R15        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E6        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15345  |7359    |1434    |12819   |52      |0       |
|  AnyFog_dataX                      |AnyFog          |221    |77      |22      |181     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |84     |50      |22      |48      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |220    |102     |22      |182     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |82     |57      |22      |47      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |219    |107     |22      |181     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |81     |55      |22      |47      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |3492   |826     |34      |3403    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |729    |60      |5       |719     |0       |0       |
|    STADOP_com2                     |STADOP          |557    |45      |0       |549     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |84     |66      |14      |51      |0       |0       |
|    head_com2                       |uniheading      |258    |83      |5       |241     |0       |0       |
|    rmc_com2                        |Gprmc           |149    |56      |0       |144     |0       |0       |
|    uart_com2                       |Agrica          |1426   |227     |10      |1410    |0       |0       |
|  DATA                              |Data_Processing |8603   |4300    |1062    |6918    |0       |0       |
|    DIV_Dtemp                       |Divider         |762    |341     |84      |640     |0       |0       |
|    DIV_Utemp                       |Divider         |634    |299     |84      |509     |0       |0       |
|    DIV_accX                        |Divider         |629    |357     |84      |504     |0       |0       |
|    DIV_accY                        |Divider         |685    |327     |111     |512     |0       |0       |
|    DIV_accZ                        |Divider         |649    |350     |132     |446     |0       |0       |
|    DIV_rateX                       |Divider         |663    |389     |132     |454     |0       |0       |
|    DIV_rateY                       |Divider         |580    |363     |132     |376     |0       |0       |
|    DIV_rateZ                       |Divider         |600    |338     |132     |389     |0       |0       |
|    genclk                          |genclk          |74     |40      |20      |41      |0       |0       |
|  FMC                               |FMC_Ctrl        |499    |447     |43      |363     |0       |0       |
|  IIC                               |I2C_master      |299    |251     |11      |266     |0       |0       |
|  IMU_CTRL                          |SCHA634         |958    |697     |61      |744     |0       |0       |
|    CtrlData                        |CtrlData        |496    |439     |47      |332     |0       |0       |
|      usms                          |Time_1ms        |29     |24      |5       |19      |0       |0       |
|    SPIM                            |SPI_SCHA634     |462    |258     |14      |412     |0       |0       |
|  POWER                             |POWER_EN        |98     |55      |38      |37      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |726    |495     |119     |492     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |726    |495     |119     |492     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |318    |218     |0       |301     |0       |0       |
|        reg_inst                    |register        |315    |215     |0       |298     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |408    |277     |119     |191     |0       |0       |
|        bus_inst                    |bus_top         |188    |126     |62      |66      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |25     |15      |10      |9       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |12     |12      |0       |2       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |52     |34      |18      |20      |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |99     |65      |34      |35      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |145    |114     |29      |96      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13518  
    #2          2       3560   
    #3          3        689   
    #4          4        296   
    #5        5-10       930   
    #6        11-50      439   
    #7       51-100      26    
    #8       101-500      4    
    #9        >500        2    
  Average     2.16             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.045402s wall, 3.468750s user + 0.046875s system = 3.515625s CPU (171.9%)

RUN-1004 : used memory is 1065 MB, reserved memory is 1064 MB, peak memory is 1129 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68699, tnet num: 19523, tinst num: 8272, tnode num: 93645, tedge num: 113133.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.758478s wall, 1.750000s user + 0.000000s system = 1.750000s CPU (99.5%)

RUN-1004 : used memory is 1067 MB, reserved memory is 1065 MB, peak memory is 1129 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19523 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.472490s wall, 1.437500s user + 0.015625s system = 1.453125s CPU (98.7%)

RUN-1004 : used memory is 1072 MB, reserved memory is 1070 MB, peak memory is 1129 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 28b752c1c0e7d7ba92ac5beed3c46754b7b56ac64969e4b0149cfdbc1d295011 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8272
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19525, pip num: 150734
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 351
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3212 valid insts, and 419499 bits set as '1'.
BIT-1004 : the usercode register value: 00000000000000001110111010000001
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  13.141739s wall, 128.078125s user + 0.250000s system = 128.328125s CPU (976.5%)

RUN-1004 : used memory is 1201 MB, reserved memory is 1187 MB, peak memory is 1316 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250527_101106.log"

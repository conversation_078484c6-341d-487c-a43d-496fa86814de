============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Aug 21 11:27:56 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.840791s wall, 1.593750s user + 4.234375s system = 5.828125s CPU (99.8%)

RUN-1004 : used memory is 79 MB, reserved memory is 40 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  2.012661s wall, 1.890625s user + 0.125000s system = 2.015625s CPU (100.1%)

RUN-1004 : used memory is 302 MB, reserved memory is 270 MB, peak memory is 305 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 8 view nodes, 34 trigger nets, 34 data nets.
KIT-1004 : Chipwatcher code = 0000011010100101
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_DIN_NUM=34,BUS_CTRL_NUM=100,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb011,32'sb011,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01110,32'sb010001,32'sb010010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110000,32'sb0111010,32'sb01000000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=122) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=122) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_DIN_NUM=34,BUS_CTRL_NUM=100,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb011,32'sb011,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01110,32'sb010001,32'sb010010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110000,32'sb0111010,32'sb01000000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_DIN_NUM=34,BUS_CTRL_NUM=100,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb011,32'sb011,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01110,32'sb010001,32'sb010010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110000,32'sb0111010,32'sb01000000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb011) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_DIN_NUM=34,BUS_CTRL_NUM=100,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb011,32'sb011,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01110,32'sb010001,32'sb010010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110000,32'sb0111010,32'sb01000000})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=122)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=122)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_DIN_NUM=34,BUS_CTRL_NUM=100,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb011,32'sb011,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01110,32'sb010001,32'sb010010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110000,32'sb0111010,32'sb01000000})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_DIN_NUM=34,BUS_CTRL_NUM=100,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb011,32'sb011,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01110,32'sb010001,32'sb010010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110000,32'sb0111010,32'sb01000000})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb011)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22901/34 useful/useless nets, 19673/20 useful/useless insts
SYN-1016 : Merged 40 instances.
SYN-1032 : 22566/24 useful/useless nets, 20072/20 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 4 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 4 mux instances.
SYN-1015 : Optimize round 1, 387 better
SYN-1014 : Optimize round 2
SYN-1032 : 22239/60 useful/useless nets, 19745/64 useful/useless insts
SYN-1015 : Optimize round 2, 128 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.585888s wall, 2.500000s user + 0.093750s system = 2.593750s CPU (100.3%)

RUN-1004 : used memory is 328 MB, reserved memory is 295 MB, peak memory is 330 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22275/228 useful/useless nets, 19811/39 useful/useless insts
SYN-1016 : Merged 33 instances.
SYN-2571 : Optimize after map_dsp, round 1, 300 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 30 instances.
SYN-2501 : Optimize round 1, 62 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 18 macro adder
SYN-1019 : Optimized 17 mux instances.
SYN-1016 : Merged 12 instances.
SYN-1032 : 22706/5 useful/useless nets, 20242/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82664, tnet num: 22706, tinst num: 20241, tnode num: 115675, tedge num: 129165.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.296096s wall, 1.281250s user + 0.015625s system = 1.296875s CPU (100.1%)

RUN-1004 : used memory is 468 MB, reserved memory is 436 MB, peak memory is 468 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22706 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 221 (3.54), #lev = 7 (1.93)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 221 (3.54), #lev = 7 (1.93)
SYN-3001 : Logic optimization runtime opt =   0.04 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 551 instances into 221 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 394 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 112 adder to BLE ...
SYN-4008 : Packed 112 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.945351s wall, 4.859375s user + 0.093750s system = 4.953125s CPU (100.2%)

RUN-1004 : used memory is 352 MB, reserved memory is 318 MB, peak memory is 577 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.889043s wall, 7.703125s user + 0.203125s system = 7.906250s CPU (100.2%)

RUN-1004 : used memory is 352 MB, reserved memory is 319 MB, peak memory is 577 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (261 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19462 instances
RUN-0007 : 5677 luts, 12192 seqs, 977 mslices, 519 lslices, 59 pads, 33 brams, 0 dsps
RUN-1001 : There are total 21934 nets
RUN-1001 : 16481 nets have 2 pins
RUN-1001 : 4253 nets have [3 - 5] pins
RUN-1001 : 830 nets have [6 - 10] pins
RUN-1001 : 241 nets have [11 - 20] pins
RUN-1001 : 107 nets have [21 - 99] pins
RUN-1001 : 22 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4789     
RUN-1001 :   No   |  No   |  Yes  |     701     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     433     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19460 instances, 5677 luts, 12192 seqs, 1496 slices, 293 macros(1496 instances: 977 mslices 519 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80978, tnet num: 21932, tinst num: 19460, tnode num: 113767, tedge num: 127388.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.370438s wall, 1.328125s user + 0.046875s system = 1.375000s CPU (100.3%)

RUN-1004 : used memory is 527 MB, reserved memory is 499 MB, peak memory is 577 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21932 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.335086s wall, 2.281250s user + 0.062500s system = 2.343750s CPU (100.4%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.61365e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19460.
PHY-3001 : Level 1 #clusters 2169.
PHY-3001 : End clustering;  0.161815s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (135.2%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 910350, overlap = 598.312
PHY-3002 : Step(2): len = 830317, overlap = 641.156
PHY-3002 : Step(3): len = 526480, overlap = 852.094
PHY-3002 : Step(4): len = 456320, overlap = 931.781
PHY-3002 : Step(5): len = 372849, overlap = 1030.12
PHY-3002 : Step(6): len = 320300, overlap = 1103.59
PHY-3002 : Step(7): len = 268136, overlap = 1168.38
PHY-3002 : Step(8): len = 236994, overlap = 1234.47
PHY-3002 : Step(9): len = 207856, overlap = 1292.66
PHY-3002 : Step(10): len = 187195, overlap = 1328.34
PHY-3002 : Step(11): len = 171699, overlap = 1364.22
PHY-3002 : Step(12): len = 153759, overlap = 1380.88
PHY-3002 : Step(13): len = 143894, overlap = 1411.31
PHY-3002 : Step(14): len = 131742, overlap = 1441.34
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.12072e-06
PHY-3002 : Step(15): len = 135323, overlap = 1430.19
PHY-3002 : Step(16): len = 181091, overlap = 1339.38
PHY-3002 : Step(17): len = 192306, overlap = 1185
PHY-3002 : Step(18): len = 196210, overlap = 1098.28
PHY-3002 : Step(19): len = 191647, overlap = 1073.12
PHY-3002 : Step(20): len = 186924, overlap = 1052.44
PHY-3002 : Step(21): len = 184049, overlap = 1042.62
PHY-3002 : Step(22): len = 179187, overlap = 1038.59
PHY-3002 : Step(23): len = 176644, overlap = 1029.16
PHY-3002 : Step(24): len = 173575, overlap = 1029.88
PHY-3002 : Step(25): len = 171476, overlap = 1011.91
PHY-3002 : Step(26): len = 169293, overlap = 998.875
PHY-3002 : Step(27): len = 167848, overlap = 1010.09
PHY-3002 : Step(28): len = 166401, overlap = 1004.41
PHY-3002 : Step(29): len = 164961, overlap = 999.625
PHY-3002 : Step(30): len = 164404, overlap = 1001.59
PHY-3002 : Step(31): len = 163137, overlap = 1005.72
PHY-3002 : Step(32): len = 162118, overlap = 1013.12
PHY-3002 : Step(33): len = 161883, overlap = 999.844
PHY-3002 : Step(34): len = 160711, overlap = 994.844
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.24145e-06
PHY-3002 : Step(35): len = 167057, overlap = 965.031
PHY-3002 : Step(36): len = 182884, overlap = 912.781
PHY-3002 : Step(37): len = 186213, overlap = 867.344
PHY-3002 : Step(38): len = 187731, overlap = 865.5
PHY-3002 : Step(39): len = 187409, overlap = 843.906
PHY-3002 : Step(40): len = 186893, overlap = 841.312
PHY-3002 : Step(41): len = 184983, overlap = 858
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.4829e-06
PHY-3002 : Step(42): len = 195290, overlap = 804.281
PHY-3002 : Step(43): len = 210672, overlap = 717.562
PHY-3002 : Step(44): len = 214703, overlap = 700.75
PHY-3002 : Step(45): len = 215720, overlap = 684.906
PHY-3002 : Step(46): len = 214586, overlap = 687.219
PHY-3002 : Step(47): len = 213620, overlap = 676.406
PHY-3002 : Step(48): len = 212207, overlap = 697.562
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 8.9658e-06
PHY-3002 : Step(49): len = 223232, overlap = 661.031
PHY-3002 : Step(50): len = 235042, overlap = 632.594
PHY-3002 : Step(51): len = 240152, overlap = 586.594
PHY-3002 : Step(52): len = 243268, overlap = 583.594
PHY-3002 : Step(53): len = 242867, overlap = 580.875
PHY-3002 : Step(54): len = 241691, overlap = 584.156
PHY-3002 : Step(55): len = 240409, overlap = 565.781
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.79316e-05
PHY-3002 : Step(56): len = 252972, overlap = 532.312
PHY-3002 : Step(57): len = 266291, overlap = 486.594
PHY-3002 : Step(58): len = 269707, overlap = 467.969
PHY-3002 : Step(59): len = 271058, overlap = 444.188
PHY-3002 : Step(60): len = 268808, overlap = 442.594
PHY-3002 : Step(61): len = 266054, overlap = 451.75
PHY-3002 : Step(62): len = 264449, overlap = 451.875
PHY-3002 : Step(63): len = 264074, overlap = 442.219
PHY-3002 : Step(64): len = 263747, overlap = 411.844
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.58632e-05
PHY-3002 : Step(65): len = 273733, overlap = 396.875
PHY-3002 : Step(66): len = 284261, overlap = 394.844
PHY-3002 : Step(67): len = 286602, overlap = 380.406
PHY-3002 : Step(68): len = 286770, overlap = 375.312
PHY-3002 : Step(69): len = 285998, overlap = 368
PHY-3002 : Step(70): len = 284222, overlap = 357.469
PHY-3002 : Step(71): len = 282656, overlap = 363.094
PHY-3002 : Step(72): len = 282092, overlap = 381.688
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 7.17264e-05
PHY-3002 : Step(73): len = 289347, overlap = 362.281
PHY-3002 : Step(74): len = 296797, overlap = 355.688
PHY-3002 : Step(75): len = 298784, overlap = 363.344
PHY-3002 : Step(76): len = 300165, overlap = 364.312
PHY-3002 : Step(77): len = 298745, overlap = 362.406
PHY-3002 : Step(78): len = 298496, overlap = 360.5
PHY-3002 : Step(79): len = 295726, overlap = 368.156
PHY-3002 : Step(80): len = 296113, overlap = 370.281
PHY-3002 : Step(81): len = 296177, overlap = 367.5
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000143453
PHY-3002 : Step(82): len = 301646, overlap = 344.188
PHY-3002 : Step(83): len = 307681, overlap = 340.906
PHY-3002 : Step(84): len = 308822, overlap = 332.375
PHY-3002 : Step(85): len = 310501, overlap = 324.438
PHY-3002 : Step(86): len = 309722, overlap = 334.062
PHY-3002 : Step(87): len = 309460, overlap = 351.312
PHY-3002 : Step(88): len = 308668, overlap = 334
PHY-3002 : Step(89): len = 308894, overlap = 335.906
PHY-3002 : Step(90): len = 308603, overlap = 311.656
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000277259
PHY-3002 : Step(91): len = 310871, overlap = 306.469
PHY-3002 : Step(92): len = 314768, overlap = 296.594
PHY-3002 : Step(93): len = 315883, overlap = 292.656
PHY-3002 : Step(94): len = 316775, overlap = 279.344
PHY-3002 : Step(95): len = 316409, overlap = 270.312
PHY-3002 : Step(96): len = 316520, overlap = 268.438
PHY-3002 : Step(97): len = 315333, overlap = 271.656
PHY-3002 : Step(98): len = 315189, overlap = 281.531
PHY-3002 : Step(99): len = 315188, overlap = 271.438
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000498799
PHY-3002 : Step(100): len = 316689, overlap = 268.594
PHY-3002 : Step(101): len = 322566, overlap = 252.344
PHY-3002 : Step(102): len = 324853, overlap = 242.031
PHY-3002 : Step(103): len = 325790, overlap = 239.156
PHY-3002 : Step(104): len = 325557, overlap = 245.062
PHY-3002 : Step(105): len = 325410, overlap = 253.875
PHY-3002 : Step(106): len = 326086, overlap = 253.562
PHY-3002 : Step(107): len = 326096, overlap = 241.469
PHY-3002 : Step(108): len = 326874, overlap = 241.062
PHY-3002 : Step(109): len = 326118, overlap = 248.312
PHY-3002 : Step(110): len = 326110, overlap = 250.5
PHY-3002 : Step(111): len = 325252, overlap = 255.656
PHY-3002 : Step(112): len = 325183, overlap = 251.75
PHY-3002 : Step(113): len = 324837, overlap = 256.344
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.016877s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (92.6%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21934.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 423104, over cnt = 1160(3%), over = 5170, worst = 45
PHY-1001 : End global iterations;  0.867773s wall, 1.093750s user + 0.015625s system = 1.109375s CPU (127.8%)

PHY-1001 : Congestion index: top1 = 76.31, top5 = 53.98, top10 = 43.10, top15 = 37.19.
PHY-3001 : End congestion estimation;  1.112854s wall, 1.328125s user + 0.015625s system = 1.343750s CPU (120.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21932 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.013214s wall, 1.000000s user + 0.015625s system = 1.015625s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000118541
PHY-3002 : Step(114): len = 369619, overlap = 221.594
PHY-3002 : Step(115): len = 379460, overlap = 201.344
PHY-3002 : Step(116): len = 378215, overlap = 192.656
PHY-3002 : Step(117): len = 377856, overlap = 182.594
PHY-3002 : Step(118): len = 383122, overlap = 158.531
PHY-3002 : Step(119): len = 386572, overlap = 142.094
PHY-3002 : Step(120): len = 388211, overlap = 136.594
PHY-3002 : Step(121): len = 391895, overlap = 137.406
PHY-3002 : Step(122): len = 393234, overlap = 130.438
PHY-3002 : Step(123): len = 395063, overlap = 129.906
PHY-3002 : Step(124): len = 397367, overlap = 125.906
PHY-3002 : Step(125): len = 399066, overlap = 125.875
PHY-3002 : Step(126): len = 402327, overlap = 122.719
PHY-3002 : Step(127): len = 403031, overlap = 126.844
PHY-3002 : Step(128): len = 403634, overlap = 130.125
PHY-3002 : Step(129): len = 405672, overlap = 133.656
PHY-3002 : Step(130): len = 407834, overlap = 131.906
PHY-3002 : Step(131): len = 410497, overlap = 132.125
PHY-3002 : Step(132): len = 410557, overlap = 133.25
PHY-3002 : Step(133): len = 410636, overlap = 135.844
PHY-3002 : Step(134): len = 411524, overlap = 135.469
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000237081
PHY-3002 : Step(135): len = 411743, overlap = 128.969
PHY-3002 : Step(136): len = 413006, overlap = 126.938
PHY-3002 : Step(137): len = 415422, overlap = 123.969
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000468515
PHY-3002 : Step(138): len = 421960, overlap = 117.062
PHY-3002 : Step(139): len = 428011, overlap = 111.25
PHY-3002 : Step(140): len = 436717, overlap = 104.438
PHY-3002 : Step(141): len = 439175, overlap = 99.5625
PHY-3002 : Step(142): len = 443097, overlap = 91.1562
PHY-3002 : Step(143): len = 445670, overlap = 91.0625
PHY-3002 : Step(144): len = 445977, overlap = 93.25
PHY-3002 : Step(145): len = 447511, overlap = 95.0312
PHY-3002 : Step(146): len = 446967, overlap = 96.5312
PHY-3002 : Step(147): len = 445501, overlap = 95.8438
PHY-3002 : Step(148): len = 444527, overlap = 99.8438
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00093703
PHY-3002 : Step(149): len = 445815, overlap = 96.6562
PHY-3002 : Step(150): len = 448272, overlap = 97
PHY-3002 : Step(151): len = 450683, overlap = 95.3438
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00178683
PHY-3002 : Step(152): len = 452522, overlap = 93.1562
PHY-3002 : Step(153): len = 459385, overlap = 93
PHY-3002 : Step(154): len = 469818, overlap = 90.9062
PHY-3002 : Step(155): len = 471223, overlap = 91.9688
PHY-3002 : Step(156): len = 471680, overlap = 95.4688
PHY-3002 : Step(157): len = 473094, overlap = 93.9688
PHY-3002 : Step(158): len = 473110, overlap = 91.9062
PHY-3002 : Step(159): len = 472380, overlap = 95.875
PHY-3002 : Step(160): len = 472087, overlap = 94.1875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 100/21934.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 537264, over cnt = 2212(6%), over = 9780, worst = 42
PHY-1001 : End global iterations;  1.149064s wall, 1.875000s user + 0.000000s system = 1.875000s CPU (163.2%)

PHY-1001 : Congestion index: top1 = 79.91, top5 = 58.59, top10 = 50.05, top15 = 45.20.
PHY-3001 : End congestion estimation;  1.587592s wall, 2.328125s user + 0.000000s system = 2.328125s CPU (146.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21932 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.052773s wall, 1.046875s user + 0.000000s system = 1.046875s CPU (99.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000118015
PHY-3002 : Step(161): len = 476914, overlap = 341
PHY-3002 : Step(162): len = 476871, overlap = 295.25
PHY-3002 : Step(163): len = 468188, overlap = 281.094
PHY-3002 : Step(164): len = 461841, overlap = 273.031
PHY-3002 : Step(165): len = 456984, overlap = 252.531
PHY-3002 : Step(166): len = 453049, overlap = 236.75
PHY-3002 : Step(167): len = 450268, overlap = 221.375
PHY-3002 : Step(168): len = 447629, overlap = 214.781
PHY-3002 : Step(169): len = 446802, overlap = 203.562
PHY-3002 : Step(170): len = 444311, overlap = 202.156
PHY-3002 : Step(171): len = 441586, overlap = 200.875
PHY-3002 : Step(172): len = 441018, overlap = 204.156
PHY-3002 : Step(173): len = 439890, overlap = 209.625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000236031
PHY-3002 : Step(174): len = 439259, overlap = 196.031
PHY-3002 : Step(175): len = 440792, overlap = 182.594
PHY-3002 : Step(176): len = 441418, overlap = 168.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000472061
PHY-3002 : Step(177): len = 443215, overlap = 164.875
PHY-3002 : Step(178): len = 449114, overlap = 164.469
PHY-3002 : Step(179): len = 452448, overlap = 149.594
PHY-3002 : Step(180): len = 452250, overlap = 140.875
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80978, tnet num: 21932, tinst num: 19460, tnode num: 113767, tedge num: 127388.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.692266s wall, 1.671875s user + 0.046875s system = 1.718750s CPU (101.6%)

RUN-1004 : used memory is 567 MB, reserved memory is 541 MB, peak memory is 700 MB
OPT-1001 : Total overflow 530.94 peak overflow 4.50
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 427/21934.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 530504, over cnt = 2407(6%), over = 8577, worst = 20
PHY-1001 : End global iterations;  1.275328s wall, 1.937500s user + 0.015625s system = 1.953125s CPU (153.1%)

PHY-1001 : Congestion index: top1 = 58.23, top5 = 47.83, top10 = 42.65, top15 = 39.44.
PHY-1001 : End incremental global routing;  1.552650s wall, 2.218750s user + 0.015625s system = 2.234375s CPU (143.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21932 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.271214s wall, 1.250000s user + 0.031250s system = 1.281250s CPU (100.8%)

OPT-1001 : 14 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19384 has valid locations, 210 needs to be replaced
PHY-3001 : design contains 19656 instances, 5754 luts, 12311 seqs, 1496 slices, 293 macros(1496 instances: 977 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 465874
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17102/22130.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 540544, over cnt = 2444(6%), over = 8606, worst = 20
PHY-1001 : End global iterations;  0.197298s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (134.6%)

PHY-1001 : Congestion index: top1 = 58.90, top5 = 48.10, top10 = 42.92, top15 = 39.74.
PHY-3001 : End congestion estimation;  0.476623s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (114.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81605, tnet num: 22128, tinst num: 19656, tnode num: 114671, tedge num: 128250.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.684428s wall, 1.640625s user + 0.031250s system = 1.671875s CPU (99.3%)

RUN-1004 : used memory is 613 MB, reserved memory is 605 MB, peak memory is 703 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22128 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.815526s wall, 2.750000s user + 0.062500s system = 2.812500s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(181): len = 466280, overlap = 1.6875
PHY-3002 : Step(182): len = 467659, overlap = 1.75
PHY-3002 : Step(183): len = 469044, overlap = 1.875
PHY-3002 : Step(184): len = 470399, overlap = 1.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17127/22130.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 541928, over cnt = 2442(6%), over = 8661, worst = 20
PHY-1001 : End global iterations;  0.207125s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (166.0%)

PHY-1001 : Congestion index: top1 = 59.42, top5 = 48.34, top10 = 43.15, top15 = 39.93.
PHY-3001 : End congestion estimation;  0.490062s wall, 0.625000s user + 0.000000s system = 0.625000s CPU (127.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22128 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.100869s wall, 1.093750s user + 0.000000s system = 1.093750s CPU (99.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000807015
PHY-3002 : Step(185): len = 469656, overlap = 143.5
PHY-3002 : Step(186): len = 469635, overlap = 142.75
PHY-3002 : Step(187): len = 469765, overlap = 142.719
PHY-3001 : Final: Len = 469765, Over = 142.719
PHY-3001 : End incremental placement;  5.759671s wall, 5.906250s user + 0.187500s system = 6.093750s CPU (105.8%)

OPT-1001 : Total overflow 534.72 peak overflow 4.50
OPT-1001 : End high-fanout net optimization;  9.156571s wall, 10.125000s user + 0.234375s system = 10.359375s CPU (113.1%)

OPT-1001 : Current memory(MB): used = 706, reserve = 686, peak = 723.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17128/22130.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 543000, over cnt = 2399(6%), over = 8174, worst = 20
PHY-1002 : len = 584008, over cnt = 1665(4%), over = 4278, worst = 20
PHY-1002 : len = 617864, over cnt = 772(2%), over = 1830, worst = 17
PHY-1002 : len = 640176, over cnt = 203(0%), over = 466, worst = 16
PHY-1002 : len = 649152, over cnt = 7(0%), over = 26, worst = 9
PHY-1001 : End global iterations;  1.346037s wall, 2.062500s user + 0.015625s system = 2.078125s CPU (154.4%)

PHY-1001 : Congestion index: top1 = 51.70, top5 = 44.31, top10 = 40.81, top15 = 38.61.
OPT-1001 : End congestion update;  1.622010s wall, 2.343750s user + 0.015625s system = 2.359375s CPU (145.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22128 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.118316s wall, 1.093750s user + 0.000000s system = 1.093750s CPU (97.8%)

OPT-0007 : Start: WNS 4169 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.746730s wall, 3.453125s user + 0.015625s system = 3.468750s CPU (126.3%)

OPT-1001 : Current memory(MB): used = 683, reserve = 664, peak = 723.
OPT-1001 : End physical optimization;  13.950850s wall, 15.765625s user + 0.296875s system = 16.062500s CPU (115.1%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5754 LUT to BLE ...
SYN-4008 : Packed 5754 LUT and 2735 SEQ to BLE.
SYN-4003 : Packing 9576 remaining SEQ's ...
SYN-4005 : Packed 3380 SEQ with LUT/SLICE
SYN-4006 : 171 single LUT's are left
SYN-4006 : 6196 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11950/13807 primitive instances ...
PHY-3001 : End packing;  3.181567s wall, 3.187500s user + 0.000000s system = 3.187500s CPU (100.2%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8127 instances
RUN-1001 : 4015 mslices, 4015 lslices, 59 pads, 33 brams, 0 dsps
RUN-1001 : There are total 19446 nets
RUN-1001 : 13706 nets have 2 pins
RUN-1001 : 4329 nets have [3 - 5] pins
RUN-1001 : 899 nets have [6 - 10] pins
RUN-1001 : 364 nets have [11 - 20] pins
RUN-1001 : 139 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8125 instances, 8030 slices, 293 macros(1496 instances: 977 mslices 519 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 489825, Over = 375.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7781/19446.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 618664, over cnt = 1561(4%), over = 2524, worst = 8
PHY-1002 : len = 624848, over cnt = 1024(2%), over = 1493, worst = 7
PHY-1002 : len = 640256, over cnt = 274(0%), over = 354, worst = 6
PHY-1002 : len = 645832, over cnt = 47(0%), over = 52, worst = 3
PHY-1002 : len = 647032, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.408756s wall, 2.187500s user + 0.000000s system = 2.187500s CPU (155.3%)

PHY-1001 : Congestion index: top1 = 51.42, top5 = 44.52, top10 = 40.55, top15 = 38.09.
PHY-3001 : End congestion estimation;  1.778157s wall, 2.546875s user + 0.000000s system = 2.546875s CPU (143.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67729, tnet num: 19444, tinst num: 8125, tnode num: 91818, tedge num: 111576.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.897642s wall, 1.890625s user + 0.015625s system = 1.906250s CPU (100.5%)

RUN-1004 : used memory is 602 MB, reserved memory is 591 MB, peak memory is 723 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19444 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.027719s wall, 3.015625s user + 0.015625s system = 3.031250s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.61911e-05
PHY-3002 : Step(188): len = 496399, overlap = 367.5
PHY-3002 : Step(189): len = 497286, overlap = 365
PHY-3002 : Step(190): len = 497828, overlap = 375.75
PHY-3002 : Step(191): len = 498110, overlap = 385.5
PHY-3002 : Step(192): len = 495482, overlap = 396.25
PHY-3002 : Step(193): len = 494162, overlap = 412.5
PHY-3002 : Step(194): len = 491294, overlap = 413
PHY-3002 : Step(195): len = 489325, overlap = 406.5
PHY-3002 : Step(196): len = 487258, overlap = 409
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.23822e-05
PHY-3002 : Step(197): len = 491458, overlap = 395.5
PHY-3002 : Step(198): len = 495702, overlap = 374.5
PHY-3002 : Step(199): len = 495704, overlap = 374
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000184764
PHY-3002 : Step(200): len = 506395, overlap = 364.25
PHY-3002 : Step(201): len = 515484, overlap = 348.25
PHY-3002 : Step(202): len = 512856, overlap = 345
PHY-3002 : Step(203): len = 510358, overlap = 337.75
PHY-3002 : Step(204): len = 509909, overlap = 338.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.744449s wall, 0.703125s user + 0.812500s system = 1.515625s CPU (203.6%)

PHY-3001 : Trial Legalized: Len = 619846
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 588/19446.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 705280, over cnt = 2408(6%), over = 3930, worst = 7
PHY-1002 : len = 718880, over cnt = 1514(4%), over = 2218, worst = 6
PHY-1002 : len = 733232, over cnt = 774(2%), over = 1135, worst = 6
PHY-1002 : len = 745000, over cnt = 340(0%), over = 463, worst = 5
PHY-1002 : len = 752864, over cnt = 5(0%), over = 8, worst = 3
PHY-1001 : End global iterations;  1.921911s wall, 3.375000s user + 0.046875s system = 3.421875s CPU (178.0%)

PHY-1001 : Congestion index: top1 = 49.74, top5 = 45.15, top10 = 42.43, top15 = 40.69.
PHY-3001 : End congestion estimation;  2.329236s wall, 3.765625s user + 0.062500s system = 3.828125s CPU (164.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19444 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.065880s wall, 1.062500s user + 0.000000s system = 1.062500s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000167987
PHY-3002 : Step(205): len = 578730, overlap = 82.5
PHY-3002 : Step(206): len = 559959, overlap = 122.25
PHY-3002 : Step(207): len = 548521, overlap = 163.25
PHY-3002 : Step(208): len = 541710, overlap = 194
PHY-3002 : Step(209): len = 537182, overlap = 216.25
PHY-3002 : Step(210): len = 534737, overlap = 236.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000335973
PHY-3002 : Step(211): len = 539028, overlap = 227.5
PHY-3002 : Step(212): len = 543990, overlap = 222.5
PHY-3002 : Step(213): len = 545525, overlap = 228.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(214): len = 548023, overlap = 230
PHY-3002 : Step(215): len = 553290, overlap = 223.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.038332s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (122.3%)

PHY-3001 : Legalized: Len = 594198, Over = 0
PHY-3001 : Spreading special nets. 42 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.088345s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (88.4%)

PHY-3001 : 59 instances has been re-located, deltaX = 13, deltaY = 37, maxDist = 2.
PHY-3001 : Final: Len = 595100, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67729, tnet num: 19444, tinst num: 8125, tnode num: 91818, tedge num: 111576.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.185513s wall, 2.171875s user + 0.015625s system = 2.187500s CPU (100.1%)

RUN-1004 : used memory is 613 MB, reserved memory is 613 MB, peak memory is 723 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3916/19446.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 691136, over cnt = 2160(6%), over = 3371, worst = 7
PHY-1002 : len = 700504, over cnt = 1346(3%), over = 1926, worst = 7
PHY-1002 : len = 720520, over cnt = 288(0%), over = 395, worst = 5
PHY-1002 : len = 722904, over cnt = 175(0%), over = 240, worst = 5
PHY-1002 : len = 727168, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.909210s wall, 3.156250s user + 0.000000s system = 3.156250s CPU (165.3%)

PHY-1001 : Congestion index: top1 = 48.49, top5 = 43.44, top10 = 40.61, top15 = 38.86.
PHY-1001 : End incremental global routing;  2.250719s wall, 3.500000s user + 0.000000s system = 3.500000s CPU (155.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19444 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.028277s wall, 1.000000s user + 0.031250s system = 1.031250s CPU (100.3%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8062 has valid locations, 6 needs to be replaced
PHY-3001 : design contains 8130 instances, 8035 slices, 293 macros(1496 instances: 977 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 597468
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17523/19455.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 730344, over cnt = 9(0%), over = 13, worst = 3
PHY-1002 : len = 730384, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 730400, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 730432, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.577682s wall, 0.593750s user + 0.015625s system = 0.609375s CPU (105.5%)

PHY-1001 : Congestion index: top1 = 48.60, top5 = 43.61, top10 = 40.74, top15 = 38.97.
PHY-3001 : End congestion estimation;  0.912124s wall, 0.921875s user + 0.015625s system = 0.937500s CPU (102.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67773, tnet num: 19453, tinst num: 8130, tnode num: 91872, tedge num: 111644.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.131371s wall, 2.140625s user + 0.000000s system = 2.140625s CPU (100.4%)

RUN-1004 : used memory is 644 MB, reserved memory is 635 MB, peak memory is 723 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19453 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.192047s wall, 3.171875s user + 0.031250s system = 3.203125s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(216): len = 597131, overlap = 0.75
PHY-3002 : Step(217): len = 596889, overlap = 1
PHY-3002 : Step(218): len = 596610, overlap = 1.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17518/19455.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 728192, over cnt = 7(0%), over = 8, worst = 2
PHY-1002 : len = 728208, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 728232, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.421436s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (100.1%)

PHY-1001 : Congestion index: top1 = 48.47, top5 = 43.47, top10 = 40.65, top15 = 38.90.
PHY-3001 : End congestion estimation;  0.754855s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (99.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19453 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.030644s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000655124
PHY-3002 : Step(219): len = 596516, overlap = 0.5
PHY-3002 : Step(220): len = 596460, overlap = 0.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006982s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 596488, Over = 0
PHY-3001 : End spreading;  0.074646s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (104.7%)

PHY-3001 : Final: Len = 596488, Over = 0
PHY-3001 : End incremental placement;  6.533977s wall, 6.828125s user + 0.125000s system = 6.953125s CPU (106.4%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.339080s wall, 12.015625s user + 0.171875s system = 12.187500s CPU (117.9%)

OPT-1001 : Current memory(MB): used = 716, reserve = 701, peak = 723.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17518/19455.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 728144, over cnt = 10(0%), over = 14, worst = 4
PHY-1002 : len = 728192, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 728208, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.432911s wall, 0.421875s user + 0.015625s system = 0.437500s CPU (101.1%)

PHY-1001 : Congestion index: top1 = 48.51, top5 = 43.44, top10 = 40.61, top15 = 38.88.
OPT-1001 : End congestion update;  0.760784s wall, 0.734375s user + 0.015625s system = 0.750000s CPU (98.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19453 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.861759s wall, 0.875000s user + 0.000000s system = 0.875000s CPU (101.5%)

OPT-0007 : Start: WNS 4215 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.627975s wall, 1.609375s user + 0.015625s system = 1.625000s CPU (99.8%)

OPT-1001 : Current memory(MB): used = 716, reserve = 701, peak = 723.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19453 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.843146s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (100.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17528/19455.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 728208, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.132245s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (94.5%)

PHY-1001 : Congestion index: top1 = 48.51, top5 = 43.44, top10 = 40.61, top15 = 38.88.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19453 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.893223s wall, 0.890625s user + 0.000000s system = 0.890625s CPU (99.7%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4215 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 48.034483
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4215ps with logic level 3 
RUN-1001 :       #2 path slack 4259ps with logic level 4 
RUN-1001 :       #3 path slack 4265ps with logic level 3 
RUN-1001 :       #4 path slack 4302ps with logic level 4 
RUN-1001 :       #5 path slack 4303ps with logic level 3 
OPT-1001 : End physical optimization;  16.640183s wall, 18.265625s user + 0.218750s system = 18.484375s CPU (111.1%)

RUN-1003 : finish command "place" in  74.665292s wall, 133.468750s user + 7.859375s system = 141.328125s CPU (189.3%)

RUN-1004 : used memory is 632 MB, reserved memory is 626 MB, peak memory is 723 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.866881s wall, 3.125000s user + 0.031250s system = 3.156250s CPU (169.1%)

RUN-1004 : used memory is 633 MB, reserved memory is 626 MB, peak memory is 723 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8132 instances
RUN-1001 : 4015 mslices, 4020 lslices, 59 pads, 33 brams, 0 dsps
RUN-1001 : There are total 19455 nets
RUN-1001 : 13708 nets have 2 pins
RUN-1001 : 4328 nets have [3 - 5] pins
RUN-1001 : 904 nets have [6 - 10] pins
RUN-1001 : 368 nets have [11 - 20] pins
RUN-1001 : 138 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67773, tnet num: 19453, tinst num: 8130, tnode num: 91872, tedge num: 111644.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.959748s wall, 1.953125s user + 0.000000s system = 1.953125s CPU (99.7%)

RUN-1004 : used memory is 615 MB, reserved memory is 599 MB, peak memory is 723 MB
PHY-1001 : 4015 mslices, 4020 lslices, 59 pads, 33 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19453 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 671288, over cnt = 2242(6%), over = 3724, worst = 8
PHY-1002 : len = 682872, over cnt = 1601(4%), over = 2372, worst = 6
PHY-1002 : len = 700896, over cnt = 708(2%), over = 1006, worst = 5
PHY-1002 : len = 716352, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 716528, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.935320s wall, 3.140625s user + 0.015625s system = 3.156250s CPU (163.1%)

PHY-1001 : Congestion index: top1 = 48.02, top5 = 42.93, top10 = 40.19, top15 = 38.48.
PHY-1001 : End global routing;  2.299730s wall, 3.500000s user + 0.031250s system = 3.531250s CPU (153.6%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 698, reserve = 689, peak = 723.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 969, reserve = 957, peak = 969.
PHY-1001 : End build detailed router design. 5.536391s wall, 5.453125s user + 0.078125s system = 5.531250s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 192032, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.004235s wall, 0.984375s user + 0.015625s system = 1.000000s CPU (99.6%)

PHY-1001 : Current memory(MB): used = 1004, reserve = 993, peak = 1004.
PHY-1001 : End phase 1; 1.012406s wall, 0.984375s user + 0.015625s system = 1.000000s CPU (98.8%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.69399e+06, over cnt = 1283(0%), over = 1286, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1020, reserve = 1008, peak = 1020.
PHY-1001 : End initial routed; 17.293852s wall, 46.265625s user + 0.343750s system = 46.609375s CPU (269.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18191(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.528   |   0.000   |   0   
RUN-1001 :   Hold   |   0.131   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.804085s wall, 3.812500s user + 0.000000s system = 3.812500s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 1027, reserve = 1014, peak = 1027.
PHY-1001 : End phase 2; 21.098111s wall, 50.078125s user + 0.343750s system = 50.421875s CPU (239.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.69399e+06, over cnt = 1283(0%), over = 1286, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.253843s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (104.6%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.68308e+06, over cnt = 408(0%), over = 408, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.914763s wall, 1.656250s user + 0.000000s system = 1.656250s CPU (181.1%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.68414e+06, over cnt = 92(0%), over = 92, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.375053s wall, 0.484375s user + 0.015625s system = 0.500000s CPU (133.3%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.68434e+06, over cnt = 19(0%), over = 19, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.342827s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (109.4%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.68451e+06, over cnt = 4(0%), over = 4, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.199665s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (93.9%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.68458e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.196406s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (95.5%)

PHY-1001 : ===== DR Iter 6 =====
PHY-1022 : len = 1.68463e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 6; 0.279467s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (100.6%)

PHY-1001 : ===== DR Iter 7 =====
PHY-1022 : len = 1.68463e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 7; 0.383390s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (101.9%)

PHY-1001 : ===== DR Iter 8 =====
PHY-1022 : len = 1.68463e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 8; 0.203815s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (107.3%)

PHY-1001 : ==== DR Iter 9 ====
PHY-1022 : len = 1.68465e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 9; 0.182014s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (103.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18191(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.569   |   0.000   |   0   
RUN-1001 :   Hold   |   0.122   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.782599s wall, 3.750000s user + 0.000000s system = 3.750000s CPU (99.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 287 feed throughs used by 254 nets
PHY-1001 : End commit to database; 2.450159s wall, 2.437500s user + 0.015625s system = 2.453125s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1116, reserve = 1107, peak = 1116.
PHY-1001 : End phase 3; 10.170170s wall, 11.000000s user + 0.046875s system = 11.046875s CPU (108.6%)

PHY-1003 : Routed, final wirelength = 1.68465e+06
PHY-1001 : Current memory(MB): used = 1120, reserve = 1111, peak = 1120.
PHY-1001 : End export database. 0.067261s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (116.2%)

PHY-1001 : End detail routing;  38.354526s wall, 68.046875s user + 0.484375s system = 68.531250s CPU (178.7%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67773, tnet num: 19453, tinst num: 8130, tnode num: 91872, tedge num: 111644.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  2.055071s wall, 2.046875s user + 0.000000s system = 2.046875s CPU (99.6%)

RUN-1004 : used memory is 1056 MB, reserved memory is 1054 MB, peak memory is 1120 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  47.471597s wall, 78.312500s user + 0.562500s system = 78.875000s CPU (166.2%)

RUN-1004 : used memory is 1055 MB, reserved memory is 1054 MB, peak memory is 1120 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8886   out of  19600   45.34%
#reg                    12410   out of  19600   63.32%
#le                     15043
  #lut only              2633   out of  15043   17.50%
  #reg only              6157   out of  15043   40.93%
  #lut&reg               6253   out of  15043   41.57%
#dsp                        0   out of     29    0.00%
#bram                      33   out of     64   51.56%
  #bram9k                  31
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       59   out of    188   31.38%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6813
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          152
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         F2        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         D1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         B3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         C1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         A3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         A2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         B2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         B1        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F15        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDZ        INPUT        K15        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         P2        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         N4        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         M5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         N6        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         N5        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         P5        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M3        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         M4        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         H2        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         M1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         L1        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         N1        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         N3        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         M2        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT         T5        LVCMOS33           8            NONE           OREG       
    Fog_ENY        OUTPUT         T4        LVCMOS33           8            NONE           OREG       
    Fog_ENZ        OUTPUT         T7        LVCMOS33           8            NONE           OREG       
    Fog_INT        OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         T6        LVCMOS33           8            N/A            OREG       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E1        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        G11        LVCMOS25           8            N/A            NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15043  |7390    |1496    |12454   |33      |0       |
|  AnyFog_dataX                      |AnyFog          |218    |83      |22      |175     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |95     |65      |22      |52      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |208    |72      |22      |174     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |53      |22      |52      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |224    |92      |22      |178     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |100    |68      |22      |54      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2920   |671     |39      |2827    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |63     |39      |5       |55      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |214    |70      |5       |203     |0       |0       |
|    STADOP_com2                     |STADOP          |551    |130     |0       |547     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |63     |44      |14      |39      |0       |0       |
|    head_com2                       |uniheading      |266    |58      |5       |256     |0       |0       |
|    rmc_com2                        |Gprmc           |36     |36      |0       |28      |0       |0       |
|    uart_com2                       |Agrica          |1425   |270     |10      |1397    |0       |0       |
|  COM3                              |COM3_Control    |287    |168     |19      |240     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |61     |42      |5       |52      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |61     |39      |14      |38      |0       |0       |
|    rmc_com3                        |Gprmc           |165    |87      |0       |150     |0       |0       |
|  DATA                              |Data_Processing |8796   |4473    |1122    |7013    |0       |0       |
|    DIV_Dtemp                       |Divider         |779    |301     |84      |655     |0       |0       |
|    DIV_Utemp                       |Divider         |643    |291     |84      |519     |0       |0       |
|    DIV_accX                        |Divider         |556    |312     |84      |430     |0       |0       |
|    DIV_accY                        |Divider         |650    |364     |102     |495     |0       |0       |
|    DIV_accZ                        |Divider         |634    |376     |132     |427     |0       |0       |
|    DIV_rateX                       |Divider         |614    |387     |132     |408     |0       |0       |
|    DIV_rateY                       |Divider         |625    |330     |132     |419     |0       |0       |
|    DIV_rateZ                       |Divider         |627    |388     |132     |421     |0       |0       |
|    genclk                          |genclk          |271    |174     |89      |106     |0       |0       |
|  FMC                               |FMC_Ctrl        |495    |441     |43      |362     |0       |0       |
|  IIC                               |I2C_master      |279    |244     |11      |254     |0       |0       |
|  IMU_CTRL                          |SCHA634         |916    |690     |61      |727     |0       |0       |
|    CtrlData                        |CtrlData        |484    |430     |47      |337     |0       |0       |
|      usms                          |Time_1ms        |28     |23      |5       |19      |0       |0       |
|    SPIM                            |SPI_SCHA634     |432    |260     |14      |390     |0       |0       |
|  POWER                             |POWER_EN        |98     |51      |38      |38      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |590    |403     |97      |410     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |590    |403     |97      |410     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |259    |176     |0       |242     |0       |0       |
|        reg_inst                    |register        |257    |174     |0       |240     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |331    |227     |97      |168     |0       |0       |
|        bus_inst                    |bus_top         |113    |73      |40      |47      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |25     |15      |10      |8       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |16     |10      |6       |7       |0       |0       |
|          BUS_DETECTOR[5]$bus_nodes |bus_det         |15     |9       |6       |7       |0       |0       |
|          BUS_DETECTOR[7]$bus_nodes |bus_det         |52     |34      |18      |20      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |133    |98      |29      |84      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13648  
    #2          2       3380   
    #3          3        651   
    #4          4        297   
    #5        5-10       964   
    #6        11-50      432   
    #7       51-100      14    
    #8       101-500      3    
    #9        >500        2    
  Average     2.13             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.171149s wall, 3.734375s user + 0.015625s system = 3.750000s CPU (172.7%)

RUN-1004 : used memory is 1056 MB, reserved memory is 1055 MB, peak memory is 1120 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67773, tnet num: 19453, tinst num: 8130, tnode num: 91872, tedge num: 111644.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.835514s wall, 1.812500s user + 0.000000s system = 1.812500s CPU (98.7%)

RUN-1004 : used memory is 1058 MB, reserved memory is 1056 MB, peak memory is 1120 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19453 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.485091s wall, 1.468750s user + 0.000000s system = 1.468750s CPU (98.9%)

RUN-1004 : used memory is 1062 MB, reserved memory is 1060 MB, peak memory is 1120 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 5914bebc17a1c31f442cd7128357dba2c23fce68140dc4f57a917e3b4f582a0e -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8130
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19455, pip num: 145768
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 287
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3243 valid insts, and 409998 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011100100000011010100101
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.367543s wall, 124.218750s user + 0.171875s system = 124.390625s CPU (1005.8%)

RUN-1004 : used memory is 1191 MB, reserved memory is 1177 MB, peak memory is 1305 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250821_112755.log"

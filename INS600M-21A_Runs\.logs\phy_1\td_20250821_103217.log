============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Aug 21 10:32:17 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.777029s wall, 1.687500s user + 4.046875s system = 5.734375s CPU (99.3%)

RUN-1004 : used memory is 79 MB, reserved memory is 40 MB, peak memory is 90 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.870764s wall, 1.812500s user + 0.046875s system = 1.859375s CPU (99.4%)

RUN-1004 : used memory is 302 MB, reserved memory is 271 MB, peak memory is 305 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 7 view nodes, 31 trigger nets, 31 data nets.
KIT-1004 : Chipwatcher code = 0101000000101111
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=7,BUS_DIN_NUM=31,BUS_CTRL_NUM=90,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010,32'sb010,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01100,32'sb01110,32'sb01111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0101000,32'sb0110000,32'sb0110110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=112) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=112) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=7,BUS_DIN_NUM=31,BUS_CTRL_NUM=90,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010,32'sb010,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01100,32'sb01110,32'sb01111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0101000,32'sb0110000,32'sb0110110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=7,BUS_DIN_NUM=31,BUS_CTRL_NUM=90,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010,32'sb010,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01100,32'sb01110,32'sb01111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0101000,32'sb0110000,32'sb0110110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=7,BUS_DIN_NUM=31,BUS_CTRL_NUM=90,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010,32'sb010,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01100,32'sb01110,32'sb01111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0101000,32'sb0110000,32'sb0110110})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=112)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=112)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=7,BUS_DIN_NUM=31,BUS_CTRL_NUM=90,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010,32'sb010,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01100,32'sb01110,32'sb01111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0101000,32'sb0110000,32'sb0110110})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=7,BUS_DIN_NUM=31,BUS_CTRL_NUM=90,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010,32'sb010,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01100,32'sb01110,32'sb01111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0101000,32'sb0110000,32'sb0110110})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22731/31 useful/useless nets, 19545/17 useful/useless insts
SYN-1016 : Merged 37 instances.
SYN-1032 : 22419/22 useful/useless nets, 19924/18 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 4 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 4 mux instances.
SYN-1015 : Optimize round 1, 363 better
SYN-1014 : Optimize round 2
SYN-1032 : 22111/60 useful/useless nets, 19616/64 useful/useless insts
SYN-1015 : Optimize round 2, 128 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.619284s wall, 2.625000s user + 0.000000s system = 2.625000s CPU (100.2%)

RUN-1004 : used memory is 328 MB, reserved memory is 295 MB, peak memory is 330 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22147/225 useful/useless nets, 19679/36 useful/useless insts
SYN-1016 : Merged 33 instances.
SYN-2571 : Optimize after map_dsp, round 1, 294 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 30 instances.
SYN-2501 : Optimize round 1, 60 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 18 macro adder
SYN-1019 : Optimized 17 mux instances.
SYN-1016 : Merged 20 instances.
SYN-1032 : 22552/5 useful/useless nets, 20084/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81997, tnet num: 22552, tinst num: 20083, tnode num: 114916, tedge num: 128173.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.307561s wall, 1.265625s user + 0.031250s system = 1.296875s CPU (99.2%)

RUN-1004 : used memory is 468 MB, reserved memory is 436 MB, peak memory is 468 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22552 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 211 (3.63), #lev = 7 (2.01)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 211 (3.63), #lev = 7 (2.01)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 522 instances into 211 LUTs, name keeping = 69%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 370 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 108 adder to BLE ...
SYN-4008 : Packed 108 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.888447s wall, 4.750000s user + 0.140625s system = 4.890625s CPU (100.0%)

RUN-1004 : used memory is 352 MB, reserved memory is 317 MB, peak memory is 576 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.866978s wall, 7.703125s user + 0.171875s system = 7.875000s CPU (100.1%)

RUN-1004 : used memory is 353 MB, reserved memory is 317 MB, peak memory is 576 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (242 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19335 instances
RUN-0007 : 5590 luts, 12163 seqs, 973 mslices, 515 lslices, 59 pads, 30 brams, 0 dsps
RUN-1001 : There are total 21811 nets
RUN-1001 : 16390 nets have 2 pins
RUN-1001 : 4232 nets have [3 - 5] pins
RUN-1001 : 823 nets have [6 - 10] pins
RUN-1001 : 240 nets have [11 - 20] pins
RUN-1001 : 104 nets have [21 - 99] pins
RUN-1001 : 22 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4784     
RUN-1001 :   No   |  No   |  Yes  |     696     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     414     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19333 instances, 5590 luts, 12163 seqs, 1488 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80398, tnet num: 21809, tinst num: 19333, tnode num: 113071, tedge num: 126467.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.291154s wall, 1.218750s user + 0.062500s system = 1.281250s CPU (99.2%)

RUN-1004 : used memory is 527 MB, reserved memory is 499 MB, peak memory is 576 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21809 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.248968s wall, 2.140625s user + 0.093750s system = 2.234375s CPU (99.4%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.65051e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19333.
PHY-3001 : Level 1 #clusters 2198.
PHY-3001 : End clustering;  0.160322s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (136.4%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 887671, overlap = 627.562
PHY-3002 : Step(2): len = 793343, overlap = 692.406
PHY-3002 : Step(3): len = 514746, overlap = 885.125
PHY-3002 : Step(4): len = 455103, overlap = 919.094
PHY-3002 : Step(5): len = 347324, overlap = 1048.22
PHY-3002 : Step(6): len = 315631, overlap = 1092.44
PHY-3002 : Step(7): len = 262804, overlap = 1205.59
PHY-3002 : Step(8): len = 234749, overlap = 1251.16
PHY-3002 : Step(9): len = 207157, overlap = 1318.31
PHY-3002 : Step(10): len = 193644, overlap = 1349.53
PHY-3002 : Step(11): len = 172512, overlap = 1376.38
PHY-3002 : Step(12): len = 160958, overlap = 1402.25
PHY-3002 : Step(13): len = 144088, overlap = 1423.62
PHY-3002 : Step(14): len = 136318, overlap = 1447.88
PHY-3002 : Step(15): len = 125162, overlap = 1460.88
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.42587e-07
PHY-3002 : Step(16): len = 127023, overlap = 1442.78
PHY-3002 : Step(17): len = 150759, overlap = 1368.66
PHY-3002 : Step(18): len = 158083, overlap = 1344.28
PHY-3002 : Step(19): len = 159363, overlap = 1290.53
PHY-3002 : Step(20): len = 159654, overlap = 1271.34
PHY-3002 : Step(21): len = 155363, overlap = 1257.03
PHY-3002 : Step(22): len = 153406, overlap = 1235.53
PHY-3002 : Step(23): len = 149678, overlap = 1226.41
PHY-3002 : Step(24): len = 148404, overlap = 1214.97
PHY-3002 : Step(25): len = 145402, overlap = 1203.56
PHY-3002 : Step(26): len = 145768, overlap = 1194.94
PHY-3002 : Step(27): len = 144497, overlap = 1179.84
PHY-3002 : Step(28): len = 143911, overlap = 1185.19
PHY-3002 : Step(29): len = 143627, overlap = 1176.03
PHY-3002 : Step(30): len = 143762, overlap = 1162.16
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 1.48517e-06
PHY-3002 : Step(31): len = 147628, overlap = 1138.94
PHY-3002 : Step(32): len = 163061, overlap = 1082.72
PHY-3002 : Step(33): len = 168414, overlap = 1015.09
PHY-3002 : Step(34): len = 170594, overlap = 979.188
PHY-3002 : Step(35): len = 170310, overlap = 965.406
PHY-3002 : Step(36): len = 169839, overlap = 948.875
PHY-3002 : Step(37): len = 167544, overlap = 969.688
PHY-3002 : Step(38): len = 167389, overlap = 964.562
PHY-3002 : Step(39): len = 165650, overlap = 953.156
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 2.97035e-06
PHY-3002 : Step(40): len = 175579, overlap = 922.5
PHY-3002 : Step(41): len = 191926, overlap = 850.688
PHY-3002 : Step(42): len = 196608, overlap = 809.406
PHY-3002 : Step(43): len = 197556, overlap = 802.125
PHY-3002 : Step(44): len = 197339, overlap = 794.438
PHY-3002 : Step(45): len = 196494, overlap = 787.812
PHY-3002 : Step(46): len = 194324, overlap = 795.812
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 5.9407e-06
PHY-3002 : Step(47): len = 204169, overlap = 792.938
PHY-3002 : Step(48): len = 217472, overlap = 734.625
PHY-3002 : Step(49): len = 221470, overlap = 692.531
PHY-3002 : Step(50): len = 222744, overlap = 693.438
PHY-3002 : Step(51): len = 222363, overlap = 688.531
PHY-3002 : Step(52): len = 220641, overlap = 670.219
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.18814e-05
PHY-3002 : Step(53): len = 233950, overlap = 630.219
PHY-3002 : Step(54): len = 244446, overlap = 543.094
PHY-3002 : Step(55): len = 248374, overlap = 529
PHY-3002 : Step(56): len = 251414, overlap = 514.594
PHY-3002 : Step(57): len = 250653, overlap = 481.469
PHY-3002 : Step(58): len = 250026, overlap = 465.531
PHY-3002 : Step(59): len = 248185, overlap = 468.469
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 2.37628e-05
PHY-3002 : Step(60): len = 260441, overlap = 481.5
PHY-3002 : Step(61): len = 273449, overlap = 423.781
PHY-3002 : Step(62): len = 276888, overlap = 417.344
PHY-3002 : Step(63): len = 277458, overlap = 389.219
PHY-3002 : Step(64): len = 275991, overlap = 375.031
PHY-3002 : Step(65): len = 274093, overlap = 379.25
PHY-3002 : Step(66): len = 272881, overlap = 384.438
PHY-3002 : Step(67): len = 271949, overlap = 374.594
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 4.75256e-05
PHY-3002 : Step(68): len = 281356, overlap = 373.719
PHY-3002 : Step(69): len = 290765, overlap = 359.125
PHY-3002 : Step(70): len = 293456, overlap = 322.188
PHY-3002 : Step(71): len = 294370, overlap = 322.75
PHY-3002 : Step(72): len = 293712, overlap = 312.625
PHY-3002 : Step(73): len = 292995, overlap = 308.625
PHY-3002 : Step(74): len = 291565, overlap = 298.469
PHY-3002 : Step(75): len = 291814, overlap = 300.875
PHY-3002 : Step(76): len = 290624, overlap = 292.719
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 9.50512e-05
PHY-3002 : Step(77): len = 297215, overlap = 266.375
PHY-3002 : Step(78): len = 304427, overlap = 263.75
PHY-3002 : Step(79): len = 306541, overlap = 255.906
PHY-3002 : Step(80): len = 306602, overlap = 251.156
PHY-3002 : Step(81): len = 304866, overlap = 247.531
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000190102
PHY-3002 : Step(82): len = 308583, overlap = 235.562
PHY-3002 : Step(83): len = 313218, overlap = 228
PHY-3002 : Step(84): len = 315200, overlap = 239.25
PHY-3002 : Step(85): len = 316839, overlap = 233.781
PHY-3002 : Step(86): len = 317134, overlap = 232.625
PHY-3002 : Step(87): len = 317311, overlap = 241.188
PHY-3002 : Step(88): len = 315842, overlap = 242.688
PHY-3002 : Step(89): len = 315731, overlap = 235.25
PHY-3002 : Step(90): len = 314208, overlap = 228.5
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000380205
PHY-3002 : Step(91): len = 315968, overlap = 226.594
PHY-3002 : Step(92): len = 318651, overlap = 216.125
PHY-3002 : Step(93): len = 318972, overlap = 206.875
PHY-3002 : Step(94): len = 320300, overlap = 206.062
PHY-3002 : Step(95): len = 320568, overlap = 214.344
PHY-3002 : Step(96): len = 321123, overlap = 227.531
PHY-3002 : Step(97): len = 320641, overlap = 221.625
PHY-3002 : Step(98): len = 321415, overlap = 223.062
PHY-3002 : Step(99): len = 321482, overlap = 221.156
PHY-3002 : Step(100): len = 322005, overlap = 221.812
PHY-3002 : Step(101): len = 321045, overlap = 225.344
PHY-3002 : Step(102): len = 321066, overlap = 219.344
PHY-3002 : Step(103): len = 320977, overlap = 223.438
PHY-3002 : Step(104): len = 321177, overlap = 221.875
PHY-3002 : Step(105): len = 320500, overlap = 212.5
PHY-3001 : :::11::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(106): len = 321821, overlap = 207.469
PHY-3002 : Step(107): len = 325097, overlap = 201.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.016619s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (94.0%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21811.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 426016, over cnt = 1205(3%), over = 5315, worst = 26
PHY-1001 : End global iterations;  0.872986s wall, 1.156250s user + 0.000000s system = 1.156250s CPU (132.4%)

PHY-1001 : Congestion index: top1 = 70.69, top5 = 50.63, top10 = 41.41, top15 = 36.35.
PHY-3001 : End congestion estimation;  1.135669s wall, 1.390625s user + 0.015625s system = 1.406250s CPU (123.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21809 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.031230s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000119697
PHY-3002 : Step(108): len = 369719, overlap = 174.906
PHY-3002 : Step(109): len = 376112, overlap = 167.75
PHY-3002 : Step(110): len = 375525, overlap = 152.375
PHY-3002 : Step(111): len = 375375, overlap = 140.375
PHY-3002 : Step(112): len = 382633, overlap = 130.938
PHY-3002 : Step(113): len = 388187, overlap = 110.969
PHY-3002 : Step(114): len = 392773, overlap = 102.594
PHY-3002 : Step(115): len = 397856, overlap = 100.469
PHY-3002 : Step(116): len = 402763, overlap = 99.0312
PHY-3002 : Step(117): len = 407661, overlap = 99.2812
PHY-3002 : Step(118): len = 410612, overlap = 97.0312
PHY-3002 : Step(119): len = 414184, overlap = 98.6875
PHY-3002 : Step(120): len = 417408, overlap = 94.0938
PHY-3002 : Step(121): len = 419670, overlap = 96.7812
PHY-3002 : Step(122): len = 420825, overlap = 101.344
PHY-3002 : Step(123): len = 423995, overlap = 114.031
PHY-3002 : Step(124): len = 427520, overlap = 120.062
PHY-3002 : Step(125): len = 428787, overlap = 126.688
PHY-3002 : Step(126): len = 430379, overlap = 132.719
PHY-3002 : Step(127): len = 431403, overlap = 135.969
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000239395
PHY-3002 : Step(128): len = 431813, overlap = 129.875
PHY-3002 : Step(129): len = 433402, overlap = 126.812
PHY-3002 : Step(130): len = 434888, overlap = 117.938
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(131): len = 442157, overlap = 107.938
PHY-3002 : Step(132): len = 448165, overlap = 97.9375
PHY-3002 : Step(133): len = 449924, overlap = 90.375
PHY-3002 : Step(134): len = 451088, overlap = 90.75
PHY-3002 : Step(135): len = 453070, overlap = 93.875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 96/21811.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 515144, over cnt = 2217(6%), over = 9855, worst = 33
PHY-1001 : End global iterations;  1.070809s wall, 1.859375s user + 0.000000s system = 1.859375s CPU (173.6%)

PHY-1001 : Congestion index: top1 = 76.85, top5 = 57.99, top10 = 49.39, top15 = 44.40.
PHY-3001 : End congestion estimation;  1.350943s wall, 2.140625s user + 0.000000s system = 2.140625s CPU (158.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21809 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.171635s wall, 1.156250s user + 0.015625s system = 1.171875s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00010471
PHY-3002 : Step(136): len = 455157, overlap = 372.125
PHY-3002 : Step(137): len = 459610, overlap = 314.812
PHY-3002 : Step(138): len = 456235, overlap = 289.375
PHY-3002 : Step(139): len = 452649, overlap = 272.938
PHY-3002 : Step(140): len = 448630, overlap = 247.062
PHY-3002 : Step(141): len = 447668, overlap = 237.719
PHY-3002 : Step(142): len = 445978, overlap = 234.156
PHY-3002 : Step(143): len = 442770, overlap = 226.594
PHY-3002 : Step(144): len = 441900, overlap = 224.438
PHY-3002 : Step(145): len = 440087, overlap = 221.062
PHY-3002 : Step(146): len = 437151, overlap = 227.969
PHY-3002 : Step(147): len = 436835, overlap = 222.781
PHY-3002 : Step(148): len = 434317, overlap = 233.781
PHY-3002 : Step(149): len = 432194, overlap = 235.938
PHY-3002 : Step(150): len = 432103, overlap = 233.125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000209421
PHY-3002 : Step(151): len = 432839, overlap = 228.5
PHY-3002 : Step(152): len = 435455, overlap = 216.625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000408405
PHY-3002 : Step(153): len = 442488, overlap = 198.406
PHY-3002 : Step(154): len = 449026, overlap = 175.625
PHY-3002 : Step(155): len = 450271, overlap = 170.469
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00081681
PHY-3002 : Step(156): len = 451446, overlap = 168.156
PHY-3002 : Step(157): len = 456763, overlap = 158.938
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80398, tnet num: 21809, tinst num: 19333, tnode num: 113071, tedge num: 126467.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.604959s wall, 1.578125s user + 0.031250s system = 1.609375s CPU (100.3%)

RUN-1004 : used memory is 565 MB, reserved memory is 539 MB, peak memory is 699 MB
OPT-1001 : Total overflow 519.19 peak overflow 4.16
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 583/21811.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 531856, over cnt = 2455(6%), over = 8503, worst = 22
PHY-1001 : End global iterations;  1.136682s wall, 1.937500s user + 0.031250s system = 1.968750s CPU (173.2%)

PHY-1001 : Congestion index: top1 = 55.65, top5 = 46.39, top10 = 42.09, top15 = 39.06.
PHY-1001 : End incremental global routing;  1.410982s wall, 2.203125s user + 0.031250s system = 2.234375s CPU (158.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21809 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.064087s wall, 1.031250s user + 0.031250s system = 1.062500s CPU (99.9%)

OPT-1001 : 15 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19256 has valid locations, 210 needs to be replaced
PHY-3001 : design contains 19528 instances, 5669 luts, 12279 seqs, 1488 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 470969
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17043/22006.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 541776, over cnt = 2465(7%), over = 8519, worst = 22
PHY-1001 : End global iterations;  0.198811s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (141.5%)

PHY-1001 : Congestion index: top1 = 55.71, top5 = 46.51, top10 = 42.19, top15 = 39.23.
PHY-3001 : End congestion estimation;  0.458360s wall, 0.515625s user + 0.015625s system = 0.531250s CPU (115.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81022, tnet num: 22004, tinst num: 19528, tnode num: 113966, tedge num: 127325.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.556952s wall, 1.562500s user + 0.000000s system = 1.562500s CPU (100.4%)

RUN-1004 : used memory is 607 MB, reserved memory is 599 MB, peak memory is 700 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22004 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.634980s wall, 2.625000s user + 0.015625s system = 2.640625s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(158): len = 471089, overlap = 5.6875
PHY-3002 : Step(159): len = 472370, overlap = 5.8125
PHY-3002 : Step(160): len = 473364, overlap = 6.3125
PHY-3002 : Step(161): len = 474714, overlap = 5.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17067/22006.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 542416, over cnt = 2476(7%), over = 8603, worst = 22
PHY-1001 : End global iterations;  0.195583s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (111.8%)

PHY-1001 : Congestion index: top1 = 56.08, top5 = 46.68, top10 = 42.37, top15 = 39.41.
PHY-3001 : End congestion estimation;  0.456262s wall, 0.484375s user + 0.000000s system = 0.484375s CPU (106.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22004 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.104003s wall, 1.093750s user + 0.015625s system = 1.109375s CPU (100.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000600017
PHY-3002 : Step(162): len = 474469, overlap = 162
PHY-3002 : Step(163): len = 474558, overlap = 160.875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00117109
PHY-3002 : Step(164): len = 474858, overlap = 160.75
PHY-3002 : Step(165): len = 475099, overlap = 160.562
PHY-3001 : Final: Len = 475099, Over = 160.562
PHY-3001 : End incremental placement;  5.609976s wall, 6.125000s user + 0.140625s system = 6.265625s CPU (111.7%)

OPT-1001 : Total overflow 524.16 peak overflow 4.16
OPT-1001 : End high-fanout net optimization;  8.648397s wall, 10.093750s user + 0.203125s system = 10.296875s CPU (119.1%)

OPT-1001 : Current memory(MB): used = 702, reserve = 681, peak = 718.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17078/22006.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 544936, over cnt = 2454(6%), over = 8149, worst = 22
PHY-1002 : len = 579288, over cnt = 1777(5%), over = 4851, worst = 22
PHY-1002 : len = 610016, over cnt = 1023(2%), over = 2622, worst = 21
PHY-1002 : len = 628240, over cnt = 562(1%), over = 1455, worst = 19
PHY-1002 : len = 650520, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.410073s wall, 1.859375s user + 0.000000s system = 1.859375s CPU (131.9%)

PHY-1001 : Congestion index: top1 = 48.10, top5 = 42.59, top10 = 39.85, top15 = 37.93.
OPT-1001 : End congestion update;  1.719494s wall, 2.171875s user + 0.000000s system = 2.171875s CPU (126.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22004 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.916677s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (100.6%)

OPT-0007 : Start: WNS 4565 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.642423s wall, 3.093750s user + 0.000000s system = 3.093750s CPU (117.1%)

OPT-1001 : Current memory(MB): used = 697, reserve = 677, peak = 718.
OPT-1001 : End physical optimization;  13.233119s wall, 15.265625s user + 0.250000s system = 15.515625s CPU (117.2%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5669 LUT to BLE ...
SYN-4008 : Packed 5669 LUT and 2731 SEQ to BLE.
SYN-4003 : Packing 9548 remaining SEQ's ...
SYN-4005 : Packed 3330 SEQ with LUT/SLICE
SYN-4006 : 134 single LUT's are left
SYN-4006 : 6218 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11887/13733 primitive instances ...
PHY-3001 : End packing;  2.938896s wall, 2.953125s user + 0.000000s system = 2.953125s CPU (100.5%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8085 instances
RUN-1001 : 3996 mslices, 3995 lslices, 59 pads, 30 brams, 0 dsps
RUN-1001 : There are total 19328 nets
RUN-1001 : 13577 nets have 2 pins
RUN-1001 : 4366 nets have [3 - 5] pins
RUN-1001 : 883 nets have [6 - 10] pins
RUN-1001 : 356 nets have [11 - 20] pins
RUN-1001 : 137 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8083 instances, 7991 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 494286, Over = 385
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7760/19328.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 617320, over cnt = 1585(4%), over = 2480, worst = 9
PHY-1002 : len = 622872, over cnt = 1043(2%), over = 1470, worst = 9
PHY-1002 : len = 634568, over cnt = 445(1%), over = 576, worst = 6
PHY-1002 : len = 640552, over cnt = 184(0%), over = 230, worst = 4
PHY-1002 : len = 643776, over cnt = 3(0%), over = 5, worst = 2
PHY-1001 : End global iterations;  1.338476s wall, 1.984375s user + 0.015625s system = 2.000000s CPU (149.4%)

PHY-1001 : Congestion index: top1 = 48.34, top5 = 42.87, top10 = 39.72, top15 = 37.47.
PHY-3001 : End congestion estimation;  1.675831s wall, 2.328125s user + 0.031250s system = 2.359375s CPU (140.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67169, tnet num: 19326, tinst num: 8083, tnode num: 91121, tedge num: 110789.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.780501s wall, 1.765625s user + 0.015625s system = 1.781250s CPU (100.0%)

RUN-1004 : used memory is 598 MB, reserved memory is 583 MB, peak memory is 718 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19326 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.775939s wall, 2.750000s user + 0.031250s system = 2.781250s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.15671e-05
PHY-3002 : Step(166): len = 498833, overlap = 374
PHY-3002 : Step(167): len = 496182, overlap = 385.75
PHY-3002 : Step(168): len = 497303, overlap = 393.25
PHY-3002 : Step(169): len = 497722, overlap = 398.5
PHY-3002 : Step(170): len = 496584, overlap = 396.75
PHY-3002 : Step(171): len = 495176, overlap = 393
PHY-3002 : Step(172): len = 493379, overlap = 403
PHY-3002 : Step(173): len = 490299, overlap = 396
PHY-3002 : Step(174): len = 488148, overlap = 398.25
PHY-3002 : Step(175): len = 485619, overlap = 404.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000103134
PHY-3002 : Step(176): len = 490599, overlap = 394.75
PHY-3002 : Step(177): len = 494907, overlap = 378.75
PHY-3002 : Step(178): len = 495302, overlap = 377
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000206268
PHY-3002 : Step(179): len = 501787, overlap = 362.5
PHY-3002 : Step(180): len = 513087, overlap = 349.5
PHY-3002 : Step(181): len = 513168, overlap = 341.25
PHY-3002 : Step(182): len = 512035, overlap = 336
PHY-3002 : Step(183): len = 511839, overlap = 338.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(184): len = 516277, overlap = 329.75
PHY-3002 : Step(185): len = 519552, overlap = 323.5
PHY-3002 : Step(186): len = 523252, overlap = 315.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.854957s wall, 0.921875s user + 1.125000s system = 2.046875s CPU (239.4%)

PHY-3001 : Trial Legalized: Len = 618474
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 562/19328.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 703096, over cnt = 2340(6%), over = 3777, worst = 8
PHY-1002 : len = 716256, over cnt = 1509(4%), over = 2144, worst = 8
PHY-1002 : len = 732032, over cnt = 700(1%), over = 963, worst = 6
PHY-1002 : len = 740992, over cnt = 260(0%), over = 362, worst = 6
PHY-1002 : len = 746984, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.028180s wall, 3.406250s user + 0.046875s system = 3.453125s CPU (170.3%)

PHY-1001 : Congestion index: top1 = 49.27, top5 = 43.85, top10 = 41.27, top15 = 39.51.
PHY-3001 : End congestion estimation;  2.414272s wall, 3.781250s user + 0.046875s system = 3.828125s CPU (158.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19326 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.953858s wall, 0.937500s user + 0.015625s system = 0.953125s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000193147
PHY-3002 : Step(187): len = 576915, overlap = 70.5
PHY-3002 : Step(188): len = 558236, overlap = 117.25
PHY-3002 : Step(189): len = 545808, overlap = 171.75
PHY-3002 : Step(190): len = 539108, overlap = 199.25
PHY-3002 : Step(191): len = 535772, overlap = 216
PHY-3002 : Step(192): len = 534175, overlap = 231.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000386295
PHY-3002 : Step(193): len = 538877, overlap = 222.25
PHY-3002 : Step(194): len = 543070, overlap = 220.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00077259
PHY-3002 : Step(195): len = 543539, overlap = 218.25
PHY-3002 : Step(196): len = 549621, overlap = 214.25
PHY-3002 : Step(197): len = 552857, overlap = 216.5
PHY-3002 : Step(198): len = 553826, overlap = 221.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.037105s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (84.2%)

PHY-3001 : Legalized: Len = 592646, Over = 0
PHY-3001 : Spreading special nets. 28 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.084847s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (92.1%)

PHY-3001 : 43 instances has been re-located, deltaX = 14, deltaY = 29, maxDist = 2.
PHY-3001 : Final: Len = 593166, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67169, tnet num: 19326, tinst num: 8083, tnode num: 91121, tedge num: 110789.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.059028s wall, 2.015625s user + 0.031250s system = 2.046875s CPU (99.4%)

RUN-1004 : used memory is 597 MB, reserved memory is 578 MB, peak memory is 718 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 4295/19328.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 689440, over cnt = 2117(6%), over = 3246, worst = 6
PHY-1002 : len = 699688, over cnt = 1284(3%), over = 1694, worst = 6
PHY-1002 : len = 716416, over cnt = 334(0%), over = 422, worst = 4
PHY-1002 : len = 722976, over cnt = 42(0%), over = 49, worst = 3
PHY-1002 : len = 724072, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.755734s wall, 2.984375s user + 0.031250s system = 3.015625s CPU (171.8%)

PHY-1001 : Congestion index: top1 = 48.38, top5 = 42.61, top10 = 40.03, top15 = 38.31.
PHY-1001 : End incremental global routing;  2.082148s wall, 3.296875s user + 0.031250s system = 3.328125s CPU (159.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19326 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.976526s wall, 0.968750s user + 0.015625s system = 0.984375s CPU (100.8%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8019 has valid locations, 14 needs to be replaced
PHY-3001 : design contains 8095 instances, 8003 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 596888
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17346/19338.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 728232, over cnt = 31(0%), over = 43, worst = 5
PHY-1002 : len = 728264, over cnt = 23(0%), over = 23, worst = 1
PHY-1002 : len = 728392, over cnt = 12(0%), over = 12, worst = 1
PHY-1002 : len = 728496, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 728480, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.761532s wall, 0.781250s user + 0.015625s system = 0.796875s CPU (104.6%)

PHY-1001 : Congestion index: top1 = 48.53, top5 = 42.84, top10 = 40.19, top15 = 38.50.
PHY-3001 : End congestion estimation;  1.084342s wall, 1.093750s user + 0.015625s system = 1.109375s CPU (102.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67255, tnet num: 19336, tinst num: 8095, tnode num: 91222, tedge num: 110891.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.989626s wall, 1.953125s user + 0.031250s system = 1.984375s CPU (99.7%)

RUN-1004 : used memory is 627 MB, reserved memory is 606 MB, peak memory is 718 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19336 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.979697s wall, 2.906250s user + 0.062500s system = 2.968750s CPU (99.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(199): len = 596445, overlap = 0
PHY-3002 : Step(200): len = 596125, overlap = 0.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17340/19338.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 727200, over cnt = 24(0%), over = 35, worst = 5
PHY-1002 : len = 727136, over cnt = 15(0%), over = 15, worst = 1
PHY-1002 : len = 727184, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 727336, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 727336, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.700084s wall, 0.750000s user + 0.062500s system = 0.812500s CPU (116.1%)

PHY-1001 : Congestion index: top1 = 48.28, top5 = 42.69, top10 = 40.10, top15 = 38.42.
PHY-3001 : End congestion estimation;  1.031225s wall, 1.078125s user + 0.062500s system = 1.140625s CPU (110.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19336 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.958270s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00251015
PHY-3002 : Step(201): len = 595916, overlap = 1.25
PHY-3002 : Step(202): len = 595907, overlap = 1.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007604s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 596008, Over = 0
PHY-3001 : End spreading;  0.071910s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (108.6%)

PHY-3001 : Final: Len = 596008, Over = 0
PHY-3001 : End incremental placement;  6.684985s wall, 6.671875s user + 0.218750s system = 6.890625s CPU (103.1%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.261533s wall, 11.437500s user + 0.265625s system = 11.703125s CPU (114.0%)

OPT-1001 : Current memory(MB): used = 711, reserve = 697, peak = 718.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17341/19338.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 727568, over cnt = 25(0%), over = 32, worst = 3
PHY-1002 : len = 727600, over cnt = 20(0%), over = 23, worst = 3
PHY-1002 : len = 727688, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 727768, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.541451s wall, 0.609375s user + 0.015625s system = 0.625000s CPU (115.4%)

PHY-1001 : Congestion index: top1 = 48.00, top5 = 42.60, top10 = 40.05, top15 = 38.38.
OPT-1001 : End congestion update;  0.856394s wall, 0.921875s user + 0.015625s system = 0.937500s CPU (109.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19336 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.801592s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.4%)

OPT-0007 : Start: WNS 4405 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.664526s wall, 1.718750s user + 0.015625s system = 1.734375s CPU (104.2%)

OPT-1001 : Current memory(MB): used = 710, reserve = 696, peak = 718.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19336 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.792629s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (100.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17358/19338.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 727768, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.122437s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (102.1%)

PHY-1001 : Congestion index: top1 = 48.00, top5 = 42.60, top10 = 40.05, top15 = 38.38.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19336 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.794259s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (100.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4405 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 47.724138
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4405ps with logic level 5 
RUN-1001 :       #2 path slack 4422ps with logic level 5 
RUN-1001 :       #3 path slack 4465ps with logic level 3 
RUN-1001 :       #4 path slack 4471ps with logic level 5 
RUN-1001 :       #5 path slack 4480ps with logic level 3 
OPT-1001 : End physical optimization;  16.284690s wall, 17.593750s user + 0.359375s system = 17.953125s CPU (110.2%)

RUN-1003 : finish command "place" in  68.335783s wall, 125.812500s user + 7.937500s system = 133.750000s CPU (195.7%)

RUN-1004 : used memory is 596 MB, reserved memory is 591 MB, peak memory is 718 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.683662s wall, 2.921875s user + 0.000000s system = 2.921875s CPU (173.5%)

RUN-1004 : used memory is 596 MB, reserved memory is 592 MB, peak memory is 718 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8097 instances
RUN-1001 : 4008 mslices, 3995 lslices, 59 pads, 30 brams, 0 dsps
RUN-1001 : There are total 19338 nets
RUN-1001 : 13576 nets have 2 pins
RUN-1001 : 4364 nets have [3 - 5] pins
RUN-1001 : 890 nets have [6 - 10] pins
RUN-1001 : 362 nets have [11 - 20] pins
RUN-1001 : 137 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67255, tnet num: 19336, tinst num: 8095, tnode num: 91222, tedge num: 110891.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.723836s wall, 1.734375s user + 0.000000s system = 1.734375s CPU (100.6%)

RUN-1004 : used memory is 594 MB, reserved memory is 592 MB, peak memory is 718 MB
PHY-1001 : 4008 mslices, 3995 lslices, 59 pads, 30 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19336 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 669936, over cnt = 2238(6%), over = 3665, worst = 7
PHY-1002 : len = 684272, over cnt = 1384(3%), over = 1975, worst = 6
PHY-1002 : len = 705248, over cnt = 306(0%), over = 443, worst = 6
PHY-1002 : len = 712872, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 713144, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.945741s wall, 3.187500s user + 0.078125s system = 3.265625s CPU (167.8%)

PHY-1001 : Congestion index: top1 = 48.17, top5 = 42.34, top10 = 39.57, top15 = 37.84.
PHY-1001 : End global routing;  2.304715s wall, 3.546875s user + 0.078125s system = 3.625000s CPU (157.3%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 697, reserve = 683, peak = 718.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 965, reserve = 954, peak = 965.
PHY-1001 : End build detailed router design. 4.999028s wall, 4.968750s user + 0.015625s system = 4.984375s CPU (99.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 190392, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.965022s wall, 0.953125s user + 0.015625s system = 0.968750s CPU (100.4%)

PHY-1001 : Current memory(MB): used = 1002, reserve = 991, peak = 1002.
PHY-1001 : End phase 1; 0.972717s wall, 0.968750s user + 0.015625s system = 0.984375s CPU (101.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.68988e+06, over cnt = 1331(0%), over = 1338, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1019, reserve = 1008, peak = 1019.
PHY-1001 : End initial routed; 16.733760s wall, 45.718750s user + 0.390625s system = 46.109375s CPU (275.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18078(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.503   |   0.000   |   0   
RUN-1001 :   Hold   |   0.137   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.552214s wall, 3.546875s user + 0.000000s system = 3.546875s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 1033, reserve = 1023, peak = 1033.
PHY-1001 : End phase 2; 20.286138s wall, 49.265625s user + 0.390625s system = 49.656250s CPU (244.8%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.68988e+06, over cnt = 1331(0%), over = 1338, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.254519s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (98.2%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.67852e+06, over cnt = 464(0%), over = 464, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.738559s wall, 1.203125s user + 0.031250s system = 1.234375s CPU (167.1%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.67906e+06, over cnt = 102(0%), over = 102, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.405249s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (134.9%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.68006e+06, over cnt = 5(0%), over = 5, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.280193s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (106.0%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.68012e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.174574s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (98.5%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.68017e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.160725s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (97.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18078(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.503   |   0.000   |   0   
RUN-1001 :   Hold   |   0.137   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.544536s wall, 3.515625s user + 0.015625s system = 3.531250s CPU (99.6%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 348 feed throughs used by 295 nets
PHY-1001 : End commit to database; 2.284351s wall, 2.281250s user + 0.000000s system = 2.281250s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 1111, reserve = 1102, peak = 1111.
PHY-1001 : End phase 3; 8.336060s wall, 8.968750s user + 0.046875s system = 9.015625s CPU (108.2%)

PHY-1003 : Routed, final wirelength = 1.68017e+06
PHY-1001 : Current memory(MB): used = 1115, reserve = 1107, peak = 1115.
PHY-1001 : End export database. 0.062320s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.3%)

PHY-1001 : End detail routing;  35.114736s wall, 64.671875s user + 0.468750s system = 65.140625s CPU (185.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67255, tnet num: 19336, tinst num: 8095, tnode num: 91222, tedge num: 110891.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.801286s wall, 1.781250s user + 0.000000s system = 1.781250s CPU (98.9%)

RUN-1004 : used memory is 1002 MB, reserved memory is 1007 MB, peak memory is 1115 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  43.751365s wall, 74.453125s user + 0.640625s system = 75.093750s CPU (171.6%)

RUN-1004 : used memory is 1032 MB, reserved memory is 1034 MB, peak memory is 1115 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8806   out of  19600   44.93%
#reg                    12376   out of  19600   63.14%
#le                     14980
  #lut only              2604   out of  14980   17.38%
  #reg only              6174   out of  14980   41.21%
  #lut&reg               6202   out of  14980   41.40%
#dsp                        0   out of     29    0.00%
#bram                      30   out of     64   46.88%
  #bram9k                  28
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       59   out of    188   31.38%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6789
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          139
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         F2        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         D1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         B3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         C1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         A3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         A2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         B2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         B1        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F15        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDZ        INPUT        K15        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         P2        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         N4        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         M5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         N6        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         N5        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         P5        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M3        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         M4        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         H2        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         M1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         L1        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         N1        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         N3        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         M2        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT         T5        LVCMOS33           8            NONE           OREG       
    Fog_ENY        OUTPUT         T4        LVCMOS33           8            NONE           OREG       
    Fog_ENZ        OUTPUT         T7        LVCMOS33           8            NONE           OREG       
    Fog_INT        OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         T6        LVCMOS33           8            N/A            OREG       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E1        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        G11        LVCMOS25           8            N/A            NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |14980  |7318    |1488    |12420   |30      |0       |
|  AnyFog_dataX                      |AnyFog          |215    |105     |22      |169     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |95     |69      |22      |49      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |207    |79      |22      |172     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |60      |22      |51      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |209    |96      |22      |175     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |87     |62      |22      |53      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2937   |669     |39      |2854    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |63     |37      |5       |54      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |218    |72      |5       |208     |0       |0       |
|    STADOP_com2                     |STADOP          |554    |135     |0       |547     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |43      |14      |43      |0       |0       |
|    head_com2                       |uniheading      |263    |42      |5       |254     |0       |0       |
|    rmc_com2                        |Gprmc           |40     |40      |0       |31      |0       |0       |
|    uart_com2                       |Agrica          |1429   |290     |10      |1412    |0       |0       |
|  COM3                              |COM3_Control    |276    |147     |19      |236     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |61     |37      |5       |52      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |58     |40      |14      |35      |0       |0       |
|    rmc_com3                        |Gprmc           |157    |70      |0       |149     |0       |0       |
|  DATA                              |Data_Processing |8841   |4520    |1122    |7049    |0       |0       |
|    DIV_Dtemp                       |Divider         |820    |348     |84      |692     |0       |0       |
|    DIV_Utemp                       |Divider         |678    |313     |84      |537     |0       |0       |
|    DIV_accX                        |Divider         |533    |265     |84      |409     |0       |0       |
|    DIV_accY                        |Divider         |601    |298     |102     |447     |0       |0       |
|    DIV_accZ                        |Divider         |657    |413     |132     |453     |0       |0       |
|    DIV_rateX                       |Divider         |706    |395     |132     |500     |0       |0       |
|    DIV_rateY                       |Divider         |567    |380     |132     |361     |0       |0       |
|    DIV_rateZ                       |Divider         |608    |356     |132     |404     |0       |0       |
|    genclk                          |genclk          |263    |163     |89      |103     |0       |0       |
|  FMC                               |FMC_Ctrl        |424    |372     |43      |334     |0       |0       |
|  IIC                               |I2C_master      |303    |247     |11      |272     |0       |0       |
|  IMU_CTRL                          |SCHA634         |924    |721     |61      |699     |0       |0       |
|    CtrlData                        |CtrlData        |508    |461     |47      |326     |0       |0       |
|      usms                          |Time_1ms        |28     |23      |5       |20      |0       |0       |
|    SPIM                            |SPI_SCHA634     |416    |260     |14      |373     |0       |0       |
|  POWER                             |POWER_EN        |98     |51      |38      |39      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |538    |311     |89      |369     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |538    |311     |89      |369     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |240    |117     |0       |227     |0       |0       |
|        reg_inst                    |register        |237    |114     |0       |224     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |298    |194     |89      |142     |0       |0       |
|        bus_inst                    |bus_top         |90     |62      |28      |41      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |30     |20      |10      |14      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |3      |3       |0       |3       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[5]$bus_nodes |bus_det         |3      |3       |0       |3       |0       |0       |
|          BUS_DETECTOR[6]$bus_nodes |bus_det         |50     |32      |18      |17      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |122    |84      |29      |70      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13516  
    #2          2       3421   
    #3          3        640   
    #4          4        303   
    #5        5-10       943   
    #6        11-50      438   
    #7       51-100       8    
    #8       101-500      3    
    #9        >500        2    
  Average     2.12             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.085603s wall, 3.531250s user + 0.046875s system = 3.578125s CPU (171.6%)

RUN-1004 : used memory is 1033 MB, reserved memory is 1034 MB, peak memory is 1115 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67255, tnet num: 19336, tinst num: 8095, tnode num: 91222, tedge num: 110891.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.731283s wall, 1.718750s user + 0.000000s system = 1.718750s CPU (99.3%)

RUN-1004 : used memory is 1035 MB, reserved memory is 1035 MB, peak memory is 1115 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19336 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.469047s wall, 1.453125s user + 0.015625s system = 1.468750s CPU (100.0%)

RUN-1004 : used memory is 1044 MB, reserved memory is 1042 MB, peak memory is 1115 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 8c0bffcc340113c1c38b8aafe0d48c06df796401b0914866d2af7291cff32e3b -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8095
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19338, pip num: 145472
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 348
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3230 valid insts, and 408536 bits set as '1'.
BIT-1004 : the usercode register value: 00000000010111100101000000101111
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.206514s wall, 124.015625s user + 0.156250s system = 124.171875s CPU (1017.3%)

RUN-1004 : used memory is 1186 MB, reserved memory is 1172 MB, peak memory is 1301 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250821_103217.log"

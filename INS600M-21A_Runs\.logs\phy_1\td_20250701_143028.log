============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Tue Jul  1 14:30:28 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(519)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
RUN-1001 : Project manager successfully analyzed 22 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  6.129653s wall, 1.687500s user + 4.421875s system = 6.109375s CPU (99.7%)

RUN-1004 : used memory is 80 MB, reserved memory is 40 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  2.148842s wall, 1.984375s user + 0.109375s system = 2.093750s CPU (97.4%)

RUN-1004 : used memory is 304 MB, reserved memory is 272 MB, peak memory is 307 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 7 view nodes, 41 trigger nets, 41 data nets.
KIT-1004 : Chipwatcher code = 1100010101110111
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=7,BUS_DIN_NUM=41,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb011,32'sb0100,32'sb01000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010,32'sb011101,32'sb0100001},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100,32'sb01001110,32'sb01011010}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=132) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=132) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=7,BUS_DIN_NUM=41,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb011,32'sb0100,32'sb01000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010,32'sb011101,32'sb0100001},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100,32'sb01001110,32'sb01011010}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=7,BUS_DIN_NUM=41,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb011,32'sb0100,32'sb01000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010,32'sb011101,32'sb0100001},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100,32'sb01001110,32'sb01011010}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb011) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=7,BUS_DIN_NUM=41,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb011,32'sb0100,32'sb01000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010,32'sb011101,32'sb0100001},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100,32'sb01001110,32'sb01011010})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=132)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=132)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=7,BUS_DIN_NUM=41,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb011,32'sb0100,32'sb01000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010,32'sb011101,32'sb0100001},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100,32'sb01001110,32'sb01011010})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=7,BUS_DIN_NUM=41,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb011,32'sb0100,32'sb01000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010,32'sb011101,32'sb0100001},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100,32'sb01001110,32'sb01011010})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb011)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 23214/33 useful/useless nets, 20039/16 useful/useless insts
SYN-1016 : Merged 38 instances.
SYN-1032 : 22861/20 useful/useless nets, 20467/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 5 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 5 mux instances.
SYN-1015 : Optimize round 1, 415 better
SYN-1014 : Optimize round 2
SYN-1032 : 22495/75 useful/useless nets, 20101/80 useful/useless insts
SYN-1015 : Optimize round 2, 160 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.839393s wall, 2.828125s user + 0.015625s system = 2.843750s CPU (100.2%)

RUN-1004 : used memory is 332 MB, reserved memory is 297 MB, peak memory is 334 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22543/298 useful/useless nets, 20185/46 useful/useless insts
SYN-1016 : Merged 45 instances.
SYN-2571 : Optimize after map_dsp, round 1, 389 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 39 instances.
SYN-2501 : Optimize round 1, 80 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 20 macro adder
SYN-1019 : Optimized 21 mux instances.
SYN-1016 : Merged 13 instances.
SYN-1032 : 23004/5 useful/useless nets, 20646/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 84417, tnet num: 23004, tinst num: 20645, tnode num: 118854, tedge num: 131432.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.410838s wall, 1.375000s user + 0.031250s system = 1.406250s CPU (99.7%)

RUN-1004 : used memory is 475 MB, reserved memory is 442 MB, peak memory is 475 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 23004 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 243 (3.45), #lev = 7 (1.84)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 243 (3.45), #lev = 7 (1.84)
SYN-3001 : Logic optimization runtime opt =   0.04 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 563 instances into 243 LUTs, name keeping = 72%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 420 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 134 adder to BLE ...
SYN-4008 : Packed 134 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  5.316582s wall, 5.171875s user + 0.125000s system = 5.296875s CPU (99.6%)

RUN-1004 : used memory is 355 MB, reserved memory is 332 MB, peak memory is 588 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  8.558025s wall, 8.359375s user + 0.187500s system = 8.546875s CPU (99.9%)

RUN-1004 : used memory is 355 MB, reserved memory is 332 MB, peak memory is 588 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/uart_com2/AGRIC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/uart_com2/AGRIC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/uart_com2/AGRIC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/uart_com2/AGRIC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/uart_com2/AGRIC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/uart_com2/AGRIC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/uart_com2/AGRIC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/uart_com2/AGRIC_data[0]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[7] will be merged to another kept net COM3/rmc_com3/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[6] will be merged to another kept net COM3/rmc_com3/GPRMC_data[6]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (283 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19980 instances
RUN-0007 : 5706 luts, 12739 seqs, 939 mslices, 494 lslices, 60 pads, 37 brams, 0 dsps
RUN-1001 : There are total 22362 nets
RUN-1001 : 16772 nets have 2 pins
RUN-1001 : 4449 nets have [3 - 5] pins
RUN-1001 : 791 nets have [6 - 10] pins
RUN-1001 : 224 nets have [11 - 20] pins
RUN-1001 : 102 nets have [21 - 99] pins
RUN-1001 : 24 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4742     
RUN-1001 :   No   |  No   |  Yes  |     655     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    6787     
RUN-1001 :   Yes  |  No   |  Yes  |     455     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  117  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 125
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19978 instances, 5706 luts, 12739 seqs, 1433 slices, 287 macros(1433 instances: 939 mslices 494 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1195 pins
PHY-3001 : Huge net DATA/done_div with 1714 pins
PHY-0007 : Cell area utilization is 65%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82982, tnet num: 22360, tinst num: 19978, tnode num: 117455, tedge num: 130235.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.324607s wall, 1.296875s user + 0.015625s system = 1.312500s CPU (99.1%)

RUN-1004 : used memory is 536 MB, reserved memory is 509 MB, peak memory is 588 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22360 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.340396s wall, 2.296875s user + 0.031250s system = 2.328125s CPU (99.5%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.45049e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19978.
PHY-3001 : Level 1 #clusters 2115.
PHY-3001 : End clustering;  0.178387s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (113.9%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 65%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 868623, overlap = 653.938
PHY-3002 : Step(2): len = 795287, overlap = 714.25
PHY-3002 : Step(3): len = 518603, overlap = 885.562
PHY-3002 : Step(4): len = 451762, overlap = 957.594
PHY-3002 : Step(5): len = 360336, overlap = 1082.16
PHY-3002 : Step(6): len = 324976, overlap = 1139.62
PHY-3002 : Step(7): len = 271568, overlap = 1199.84
PHY-3002 : Step(8): len = 243861, overlap = 1267.56
PHY-3002 : Step(9): len = 215035, overlap = 1336.03
PHY-3002 : Step(10): len = 196066, overlap = 1375.16
PHY-3002 : Step(11): len = 176382, overlap = 1411.03
PHY-3002 : Step(12): len = 157742, overlap = 1433.94
PHY-3002 : Step(13): len = 143652, overlap = 1461.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.10634e-06
PHY-3002 : Step(14): len = 149313, overlap = 1429.44
PHY-3002 : Step(15): len = 190929, overlap = 1345.72
PHY-3002 : Step(16): len = 197549, overlap = 1214.66
PHY-3002 : Step(17): len = 199689, overlap = 1167.25
PHY-3002 : Step(18): len = 195443, overlap = 1131.97
PHY-3002 : Step(19): len = 192417, overlap = 1097.28
PHY-3002 : Step(20): len = 187604, overlap = 1080.94
PHY-3002 : Step(21): len = 184880, overlap = 1077.22
PHY-3002 : Step(22): len = 181093, overlap = 1099.38
PHY-3002 : Step(23): len = 178497, overlap = 1083.81
PHY-3002 : Step(24): len = 176366, overlap = 1089.16
PHY-3002 : Step(25): len = 174817, overlap = 1087.75
PHY-3002 : Step(26): len = 173649, overlap = 1082.09
PHY-3002 : Step(27): len = 173276, overlap = 1082.75
PHY-3002 : Step(28): len = 172915, overlap = 1089.31
PHY-3002 : Step(29): len = 172165, overlap = 1090.38
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.21268e-06
PHY-3002 : Step(30): len = 181162, overlap = 1053.78
PHY-3002 : Step(31): len = 197157, overlap = 991.75
PHY-3002 : Step(32): len = 201011, overlap = 961.219
PHY-3002 : Step(33): len = 203247, overlap = 936.188
PHY-3002 : Step(34): len = 203415, overlap = 899.562
PHY-3002 : Step(35): len = 203668, overlap = 893.438
PHY-3002 : Step(36): len = 202113, overlap = 892.812
PHY-3002 : Step(37): len = 201138, overlap = 881.844
PHY-3002 : Step(38): len = 199900, overlap = 864.688
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.42535e-06
PHY-3002 : Step(39): len = 209601, overlap = 825.906
PHY-3002 : Step(40): len = 223724, overlap = 767.719
PHY-3002 : Step(41): len = 228907, overlap = 727.375
PHY-3002 : Step(42): len = 231417, overlap = 722.312
PHY-3002 : Step(43): len = 231457, overlap = 732.125
PHY-3002 : Step(44): len = 231055, overlap = 722.25
PHY-3002 : Step(45): len = 229935, overlap = 726.188
PHY-3002 : Step(46): len = 229735, overlap = 757.344
PHY-3002 : Step(47): len = 227939, overlap = 773.031
PHY-3002 : Step(48): len = 227263, overlap = 773.688
PHY-3002 : Step(49): len = 226763, overlap = 792.656
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 8.85071e-06
PHY-3002 : Step(50): len = 236654, overlap = 742.656
PHY-3002 : Step(51): len = 250529, overlap = 638.5
PHY-3002 : Step(52): len = 253527, overlap = 602.938
PHY-3002 : Step(53): len = 255425, overlap = 612.75
PHY-3002 : Step(54): len = 254079, overlap = 617.125
PHY-3002 : Step(55): len = 253641, overlap = 586.938
PHY-3002 : Step(56): len = 252523, overlap = 567.094
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.77014e-05
PHY-3002 : Step(57): len = 264759, overlap = 530.75
PHY-3002 : Step(58): len = 276453, overlap = 507.719
PHY-3002 : Step(59): len = 278679, overlap = 503
PHY-3002 : Step(60): len = 279526, overlap = 486.781
PHY-3002 : Step(61): len = 278070, overlap = 482.531
PHY-3002 : Step(62): len = 277632, overlap = 490.781
PHY-3002 : Step(63): len = 277113, overlap = 481.969
PHY-3002 : Step(64): len = 276176, overlap = 478.031
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.54028e-05
PHY-3002 : Step(65): len = 284840, overlap = 463.938
PHY-3002 : Step(66): len = 293393, overlap = 441.594
PHY-3002 : Step(67): len = 295852, overlap = 445.094
PHY-3002 : Step(68): len = 297377, overlap = 434.562
PHY-3002 : Step(69): len = 296503, overlap = 437.938
PHY-3002 : Step(70): len = 296182, overlap = 431.594
PHY-3002 : Step(71): len = 294528, overlap = 418.188
PHY-3002 : Step(72): len = 294200, overlap = 410.531
PHY-3002 : Step(73): len = 293367, overlap = 424
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 7.08056e-05
PHY-3002 : Step(74): len = 299979, overlap = 409.594
PHY-3002 : Step(75): len = 307802, overlap = 398.344
PHY-3002 : Step(76): len = 311479, overlap = 373.844
PHY-3002 : Step(77): len = 313151, overlap = 355.719
PHY-3002 : Step(78): len = 311783, overlap = 357.969
PHY-3002 : Step(79): len = 310930, overlap = 350.969
PHY-3002 : Step(80): len = 309396, overlap = 363.812
PHY-3002 : Step(81): len = 309509, overlap = 370.156
PHY-3002 : Step(82): len = 309679, overlap = 360.125
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000141611
PHY-3002 : Step(83): len = 314469, overlap = 338.219
PHY-3002 : Step(84): len = 321861, overlap = 322
PHY-3002 : Step(85): len = 323744, overlap = 333.75
PHY-3002 : Step(86): len = 325843, overlap = 331.938
PHY-3002 : Step(87): len = 325592, overlap = 320.469
PHY-3002 : Step(88): len = 324032, overlap = 321.469
PHY-3002 : Step(89): len = 323445, overlap = 312.375
PHY-3002 : Step(90): len = 322564, overlap = 304.406
PHY-3002 : Step(91): len = 324657, overlap = 297.625
PHY-3002 : Step(92): len = 324977, overlap = 298.906
PHY-3002 : Step(93): len = 325487, overlap = 295.5
PHY-3002 : Step(94): len = 323712, overlap = 283.344
PHY-3002 : Step(95): len = 323246, overlap = 279.844
PHY-3002 : Step(96): len = 323082, overlap = 288.719
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000277333
PHY-3002 : Step(97): len = 325742, overlap = 285.312
PHY-3002 : Step(98): len = 329570, overlap = 279.438
PHY-3002 : Step(99): len = 331294, overlap = 277
PHY-3002 : Step(100): len = 332869, overlap = 269.594
PHY-3002 : Step(101): len = 332941, overlap = 259.125
PHY-3002 : Step(102): len = 333118, overlap = 268
PHY-3002 : Step(103): len = 331834, overlap = 275.344
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.012392s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/22362.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 441040, over cnt = 1278(3%), over = 5807, worst = 37
PHY-1001 : End global iterations;  0.987463s wall, 1.328125s user + 0.046875s system = 1.375000s CPU (139.2%)

PHY-1001 : Congestion index: top1 = 75.65, top5 = 54.86, top10 = 44.60, top15 = 38.82.
PHY-3001 : End congestion estimation;  1.238071s wall, 1.578125s user + 0.046875s system = 1.625000s CPU (131.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22360 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.229462s wall, 1.234375s user + 0.000000s system = 1.234375s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000104674
PHY-3002 : Step(104): len = 371476, overlap = 213.219
PHY-3002 : Step(105): len = 387622, overlap = 186.094
PHY-3002 : Step(106): len = 386026, overlap = 186.062
PHY-3002 : Step(107): len = 384600, overlap = 178.438
PHY-3002 : Step(108): len = 390809, overlap = 165.094
PHY-3002 : Step(109): len = 393984, overlap = 161.625
PHY-3002 : Step(110): len = 394139, overlap = 160
PHY-3002 : Step(111): len = 395403, overlap = 156.625
PHY-3002 : Step(112): len = 394156, overlap = 154.531
PHY-3002 : Step(113): len = 393916, overlap = 154.469
PHY-3002 : Step(114): len = 395572, overlap = 159.344
PHY-3002 : Step(115): len = 395022, overlap = 158.406
PHY-3002 : Step(116): len = 396367, overlap = 156.375
PHY-3002 : Step(117): len = 398788, overlap = 154.906
PHY-3002 : Step(118): len = 399386, overlap = 154.156
PHY-3002 : Step(119): len = 400811, overlap = 155.875
PHY-3002 : Step(120): len = 401330, overlap = 157.406
PHY-3002 : Step(121): len = 401876, overlap = 156.031
PHY-3002 : Step(122): len = 402610, overlap = 154.438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000209348
PHY-3002 : Step(123): len = 402163, overlap = 153.219
PHY-3002 : Step(124): len = 403051, overlap = 152.281
PHY-3002 : Step(125): len = 404092, overlap = 145.406
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000416999
PHY-3002 : Step(126): len = 410088, overlap = 123.656
PHY-3002 : Step(127): len = 419406, overlap = 105.188
PHY-3002 : Step(128): len = 425508, overlap = 101.031
PHY-3002 : Step(129): len = 424009, overlap = 93.0938
PHY-3002 : Step(130): len = 424694, overlap = 88.7812
PHY-3002 : Step(131): len = 424553, overlap = 88.9688
PHY-3002 : Step(132): len = 423979, overlap = 87.5312
PHY-3002 : Step(133): len = 424655, overlap = 90.875
PHY-3002 : Step(134): len = 428398, overlap = 86.0625
PHY-3002 : Step(135): len = 428045, overlap = 85.2812
PHY-3002 : Step(136): len = 427301, overlap = 83.375
PHY-3002 : Step(137): len = 427499, overlap = 89.625
PHY-3002 : Step(138): len = 427010, overlap = 98.9062
PHY-3002 : Step(139): len = 427823, overlap = 98.5312
PHY-3002 : Step(140): len = 429131, overlap = 99.375
PHY-3002 : Step(141): len = 428972, overlap = 96.9062
PHY-3002 : Step(142): len = 429634, overlap = 96.75
PHY-3002 : Step(143): len = 428556, overlap = 89.7812
PHY-3002 : Step(144): len = 426740, overlap = 92.5938
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000833998
PHY-3002 : Step(145): len = 428591, overlap = 91.9062
PHY-3002 : Step(146): len = 430918, overlap = 88.5312
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00146639
PHY-3002 : Step(147): len = 431635, overlap = 87.625
PHY-3002 : Step(148): len = 436364, overlap = 85.1562
PHY-3002 : Step(149): len = 443239, overlap = 81.2188
PHY-3002 : Step(150): len = 447073, overlap = 77.4062
PHY-3002 : Step(151): len = 450572, overlap = 77.4375
PHY-3002 : Step(152): len = 450158, overlap = 78.875
PHY-3002 : Step(153): len = 448885, overlap = 78.5
PHY-3002 : Step(154): len = 447158, overlap = 77.5312
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 32/22362.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 517176, over cnt = 2198(6%), over = 10352, worst = 43
PHY-1001 : End global iterations;  1.101148s wall, 1.906250s user + 0.015625s system = 1.921875s CPU (174.5%)

PHY-1001 : Congestion index: top1 = 75.45, top5 = 58.77, top10 = 50.56, top15 = 45.34.
PHY-3001 : End congestion estimation;  1.444258s wall, 2.250000s user + 0.015625s system = 2.265625s CPU (156.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22360 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.286150s wall, 1.281250s user + 0.015625s system = 1.296875s CPU (100.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000104023
PHY-3002 : Step(155): len = 455636, overlap = 338.688
PHY-3002 : Step(156): len = 462829, overlap = 285.781
PHY-3002 : Step(157): len = 455258, overlap = 271.688
PHY-3002 : Step(158): len = 450992, overlap = 260.469
PHY-3002 : Step(159): len = 447483, overlap = 247.375
PHY-3002 : Step(160): len = 445142, overlap = 229.969
PHY-3002 : Step(161): len = 443211, overlap = 230.969
PHY-3002 : Step(162): len = 443066, overlap = 226.594
PHY-3002 : Step(163): len = 439430, overlap = 228.781
PHY-3002 : Step(164): len = 436819, overlap = 233.281
PHY-3002 : Step(165): len = 436618, overlap = 231.344
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000208046
PHY-3002 : Step(166): len = 436391, overlap = 226.812
PHY-3002 : Step(167): len = 437284, overlap = 223.406
PHY-3002 : Step(168): len = 437849, overlap = 211.562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000416092
PHY-3002 : Step(169): len = 440383, overlap = 207.438
PHY-3002 : Step(170): len = 448104, overlap = 190.781
PHY-3002 : Step(171): len = 451775, overlap = 173.594
PHY-3002 : Step(172): len = 454017, overlap = 164.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000832185
PHY-3002 : Step(173): len = 453984, overlap = 158.188
PHY-3002 : Step(174): len = 456784, overlap = 148.938
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82982, tnet num: 22360, tinst num: 19978, tnode num: 117455, tedge num: 130235.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.881377s wall, 1.859375s user + 0.015625s system = 1.875000s CPU (99.7%)

RUN-1004 : used memory is 575 MB, reserved memory is 549 MB, peak memory is 713 MB
OPT-1001 : Total overflow 543.97 peak overflow 3.94
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 738/22362.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 542624, over cnt = 2526(7%), over = 9081, worst = 30
PHY-1001 : End global iterations;  1.464017s wall, 2.296875s user + 0.125000s system = 2.421875s CPU (165.4%)

PHY-1001 : Congestion index: top1 = 58.10, top5 = 48.58, top10 = 43.66, top15 = 40.44.
PHY-1001 : End incremental global routing;  1.752856s wall, 2.578125s user + 0.125000s system = 2.703125s CPU (154.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22360 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.309580s wall, 1.281250s user + 0.031250s system = 1.312500s CPU (100.2%)

OPT-1001 : 26 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19889 has valid locations, 302 needs to be replaced
PHY-3001 : design contains 20254 instances, 5837 luts, 12884 seqs, 1433 slices, 287 macros(1433 instances: 939 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 477117
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 72%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17554/22638.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 559976, over cnt = 2558(7%), over = 9226, worst = 30
PHY-1001 : End global iterations;  0.229074s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (122.8%)

PHY-1001 : Congestion index: top1 = 58.21, top5 = 48.99, top10 = 43.95, top15 = 40.76.
PHY-3001 : End congestion estimation;  0.512249s wall, 0.562500s user + 0.015625s system = 0.578125s CPU (112.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83939, tnet num: 22636, tinst num: 20254, tnode num: 118777, tedge num: 131597.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.711641s wall, 1.703125s user + 0.015625s system = 1.718750s CPU (100.4%)

RUN-1004 : used memory is 622 MB, reserved memory is 615 MB, peak memory is 717 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22636 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.926841s wall, 2.906250s user + 0.031250s system = 2.937500s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(175): len = 476393, overlap = 1.84375
PHY-3002 : Step(176): len = 477092, overlap = 1.90625
PHY-3002 : Step(177): len = 478502, overlap = 1.90625
PHY-3002 : Step(178): len = 479504, overlap = 1.96875
PHY-3002 : Step(179): len = 480554, overlap = 2.03125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 72%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17581/22638.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 557720, over cnt = 2605(7%), over = 9342, worst = 30
PHY-1001 : End global iterations;  0.261374s wall, 0.296875s user + 0.031250s system = 0.328125s CPU (125.5%)

PHY-1001 : Congestion index: top1 = 58.23, top5 = 49.02, top10 = 44.02, top15 = 40.92.
PHY-3001 : End congestion estimation;  0.538749s wall, 0.562500s user + 0.031250s system = 0.593750s CPU (110.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22636 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.315251s wall, 1.296875s user + 0.015625s system = 1.312500s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000730531
PHY-3002 : Step(180): len = 480599, overlap = 151.406
PHY-3002 : Step(181): len = 481240, overlap = 150.719
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00146106
PHY-3002 : Step(182): len = 481259, overlap = 150.688
PHY-3002 : Step(183): len = 481748, overlap = 150.406
PHY-3001 : Final: Len = 481748, Over = 150.406
PHY-3001 : End incremental placement;  6.493264s wall, 6.515625s user + 0.343750s system = 6.859375s CPU (105.6%)

OPT-1001 : Total overflow 550.25 peak overflow 3.94
OPT-1001 : End high-fanout net optimization;  10.297367s wall, 11.296875s user + 0.500000s system = 11.796875s CPU (114.6%)

OPT-1001 : Current memory(MB): used = 721, reserve = 701, peak = 738.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17597/22638.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 561296, over cnt = 2533(7%), over = 8711, worst = 28
PHY-1002 : len = 597760, over cnt = 1754(4%), over = 5143, worst = 28
PHY-1002 : len = 634056, over cnt = 961(2%), over = 2643, worst = 28
PHY-1002 : len = 661072, over cnt = 341(0%), over = 865, worst = 16
PHY-1002 : len = 676104, over cnt = 10(0%), over = 11, worst = 2
PHY-1001 : End global iterations;  1.494747s wall, 2.125000s user + 0.000000s system = 2.125000s CPU (142.2%)

PHY-1001 : Congestion index: top1 = 50.26, top5 = 44.37, top10 = 41.22, top15 = 39.08.
OPT-1001 : End congestion update;  1.785931s wall, 2.421875s user + 0.000000s system = 2.421875s CPU (135.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22636 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.970782s wall, 0.953125s user + 0.015625s system = 0.968750s CPU (99.8%)

OPT-0007 : Start: WNS 4517 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.762966s wall, 3.390625s user + 0.015625s system = 3.406250s CPU (123.3%)

OPT-1001 : Current memory(MB): used = 697, reserve = 679, peak = 738.
OPT-1001 : End physical optimization;  15.300506s wall, 17.109375s user + 0.531250s system = 17.640625s CPU (115.3%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5837 LUT to BLE ...
SYN-4008 : Packed 5837 LUT and 2888 SEQ to BLE.
SYN-4003 : Packing 9996 remaining SEQ's ...
SYN-4005 : Packed 3371 SEQ with LUT/SLICE
SYN-4006 : 94 single LUT's are left
SYN-4006 : 6625 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 12462/14133 primitive instances ...
PHY-3001 : End packing;  3.345535s wall, 3.343750s user + 0.000000s system = 3.343750s CPU (99.9%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8337 instances
RUN-1001 : 4118 mslices, 4117 lslices, 60 pads, 37 brams, 0 dsps
RUN-1001 : There are total 19803 nets
RUN-1001 : 13810 nets have 2 pins
RUN-1001 : 4593 nets have [3 - 5] pins
RUN-1001 : 865 nets have [6 - 10] pins
RUN-1001 : 388 nets have [11 - 20] pins
RUN-1001 : 137 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
PHY-3001 : design contains 8335 instances, 8235 slices, 287 macros(1433 instances: 939 mslices 494 lslices)
PHY-3001 : Cell area utilization is 87%
PHY-3001 : After packing: Len = 499404, Over = 388
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 8077/19803.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 639416, over cnt = 1642(4%), over = 2600, worst = 10
PHY-1002 : len = 645152, over cnt = 996(2%), over = 1385, worst = 7
PHY-1002 : len = 652224, over cnt = 627(1%), over = 865, worst = 7
PHY-1002 : len = 660080, over cnt = 277(0%), over = 365, worst = 5
PHY-1002 : len = 666736, over cnt = 6(0%), over = 6, worst = 1
PHY-1001 : End global iterations;  1.342521s wall, 2.140625s user + 0.015625s system = 2.156250s CPU (160.6%)

PHY-1001 : Congestion index: top1 = 50.17, top5 = 44.51, top10 = 41.25, top15 = 39.00.
PHY-3001 : End congestion estimation;  1.711368s wall, 2.515625s user + 0.015625s system = 2.531250s CPU (147.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69135, tnet num: 19801, tinst num: 8335, tnode num: 94404, tedge num: 113744.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.976657s wall, 1.937500s user + 0.031250s system = 1.968750s CPU (99.6%)

RUN-1004 : used memory is 617 MB, reserved memory is 606 MB, peak memory is 738 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19801 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.160967s wall, 3.078125s user + 0.078125s system = 3.156250s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.59799e-05
PHY-3002 : Step(184): len = 503359, overlap = 371.25
PHY-3002 : Step(185): len = 500524, overlap = 387
PHY-3002 : Step(186): len = 498535, overlap = 404.5
PHY-3002 : Step(187): len = 499432, overlap = 421.75
PHY-3002 : Step(188): len = 498548, overlap = 431.5
PHY-3002 : Step(189): len = 497392, overlap = 429.5
PHY-3002 : Step(190): len = 494878, overlap = 429.75
PHY-3002 : Step(191): len = 493173, overlap = 427.75
PHY-3002 : Step(192): len = 489876, overlap = 429.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.19599e-05
PHY-3002 : Step(193): len = 494101, overlap = 416.5
PHY-3002 : Step(194): len = 496442, overlap = 414.75
PHY-3002 : Step(195): len = 495758, overlap = 413.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00018392
PHY-3002 : Step(196): len = 504073, overlap = 398.5
PHY-3002 : Step(197): len = 511088, overlap = 383.75
PHY-3002 : Step(198): len = 509210, overlap = 386.25
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00036784
PHY-3002 : Step(199): len = 513882, overlap = 381.5
PHY-3002 : Step(200): len = 523503, overlap = 366
PHY-3002 : Step(201): len = 524675, overlap = 361.5
PHY-3002 : Step(202): len = 522845, overlap = 363.75
PHY-3002 : Step(203): len = 521413, overlap = 364
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.818549s wall, 0.859375s user + 0.953125s system = 1.812500s CPU (221.4%)

PHY-3001 : Trial Legalized: Len = 628298
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 544/19803.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 719048, over cnt = 2389(6%), over = 3973, worst = 8
PHY-1002 : len = 735216, over cnt = 1435(4%), over = 2004, worst = 8
PHY-1002 : len = 749088, over cnt = 705(2%), over = 951, worst = 6
PHY-1002 : len = 760584, over cnt = 192(0%), over = 252, worst = 4
PHY-1002 : len = 764776, over cnt = 29(0%), over = 41, worst = 3
PHY-1001 : End global iterations;  2.248089s wall, 3.578125s user + 0.015625s system = 3.593750s CPU (159.9%)

PHY-1001 : Congestion index: top1 = 50.91, top5 = 45.41, top10 = 42.48, top15 = 40.72.
PHY-3001 : End congestion estimation;  2.649111s wall, 3.968750s user + 0.031250s system = 4.000000s CPU (151.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19801 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.160238s wall, 1.140625s user + 0.000000s system = 1.140625s CPU (98.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000186791
PHY-3002 : Step(204): len = 582889, overlap = 87.5
PHY-3002 : Step(205): len = 563244, overlap = 128.5
PHY-3002 : Step(206): len = 551030, overlap = 178
PHY-3002 : Step(207): len = 544302, overlap = 208.75
PHY-3002 : Step(208): len = 539175, overlap = 239.75
PHY-3002 : Step(209): len = 537586, overlap = 264
PHY-3002 : Step(210): len = 535743, overlap = 268
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000373583
PHY-3002 : Step(211): len = 539683, overlap = 266.25
PHY-3002 : Step(212): len = 543680, overlap = 262.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000747166
PHY-3002 : Step(213): len = 546015, overlap = 254.5
PHY-3002 : Step(214): len = 553213, overlap = 250
PHY-3002 : Step(215): len = 554189, overlap = 249.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.035347s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (132.6%)

PHY-3001 : Legalized: Len = 600800, Over = 0
PHY-3001 : Spreading special nets. 45 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.094077s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (99.7%)

PHY-3001 : 64 instances has been re-located, deltaX = 24, deltaY = 49, maxDist = 3.
PHY-3001 : Final: Len = 601998, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69135, tnet num: 19801, tinst num: 8335, tnode num: 94404, tedge num: 113744.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.186147s wall, 2.171875s user + 0.000000s system = 2.171875s CPU (99.3%)

RUN-1004 : used memory is 639 MB, reserved memory is 614 MB, peak memory is 738 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3912/19803.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 703192, over cnt = 2357(6%), over = 3675, worst = 9
PHY-1002 : len = 716160, over cnt = 1375(3%), over = 1869, worst = 8
PHY-1002 : len = 731416, over cnt = 575(1%), over = 734, worst = 6
PHY-1002 : len = 737216, over cnt = 300(0%), over = 377, worst = 6
PHY-1002 : len = 744176, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.018191s wall, 3.250000s user + 0.000000s system = 3.250000s CPU (161.0%)

PHY-1001 : Congestion index: top1 = 50.17, top5 = 44.44, top10 = 41.36, top15 = 39.56.
PHY-1001 : End incremental global routing;  2.365458s wall, 3.593750s user + 0.000000s system = 3.593750s CPU (151.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19801 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.024962s wall, 1.015625s user + 0.015625s system = 1.031250s CPU (100.6%)

OPT-1001 : 4 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8269 has valid locations, 32 needs to be replaced
PHY-3001 : design contains 8364 instances, 8264 slices, 287 macros(1433 instances: 939 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 609662
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17866/19839.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 755928, over cnt = 71(0%), over = 104, worst = 6
PHY-1002 : len = 756072, over cnt = 37(0%), over = 43, worst = 5
PHY-1002 : len = 756344, over cnt = 16(0%), over = 16, worst = 1
PHY-1002 : len = 756544, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 756640, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.809731s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (104.2%)

PHY-1001 : Congestion index: top1 = 50.30, top5 = 44.95, top10 = 41.85, top15 = 39.95.
PHY-3001 : End congestion estimation;  1.142736s wall, 1.171875s user + 0.000000s system = 1.171875s CPU (102.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69449, tnet num: 19837, tinst num: 8364, tnode num: 94824, tedge num: 114140.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.189084s wall, 2.203125s user + 0.000000s system = 2.203125s CPU (100.6%)

RUN-1004 : used memory is 682 MB, reserved memory is 664 MB, peak memory is 738 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19837 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.236664s wall, 3.250000s user + 0.000000s system = 3.250000s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(216): len = 609662, overlap = 0.5
PHY-3002 : Step(217): len = 609662, overlap = 0.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17895/19839.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 756640, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.130490s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (95.8%)

PHY-1001 : Congestion index: top1 = 50.30, top5 = 44.95, top10 = 41.85, top15 = 39.95.
PHY-3001 : End congestion estimation;  0.464399s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (97.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19837 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.006249s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (100.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00196262
PHY-3002 : Step(218): len = 608477, overlap = 3.25
PHY-3002 : Step(219): len = 608055, overlap = 2.75
PHY-3002 : Step(220): len = 607982, overlap = 3
PHY-3002 : Step(221): len = 607836, overlap = 1.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007341s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (212.9%)

PHY-3001 : Legalized: Len = 607974, Over = 0
PHY-3001 : Spreading special nets. 1 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.076399s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (81.8%)

PHY-3001 : 2 instances has been re-located, deltaX = 2, deltaY = 0, maxDist = 1.
PHY-3001 : Final: Len = 608012, Over = 0
PHY-3001 : End incremental placement;  6.611345s wall, 6.562500s user + 0.140625s system = 6.703125s CPU (101.4%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.558812s wall, 11.890625s user + 0.156250s system = 12.046875s CPU (114.1%)

OPT-1001 : Current memory(MB): used = 733, reserve = 714, peak = 738.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17849/19839.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 752480, over cnt = 110(0%), over = 159, worst = 3
PHY-1002 : len = 752264, over cnt = 110(0%), over = 137, worst = 3
PHY-1002 : len = 752912, over cnt = 56(0%), over = 65, worst = 3
PHY-1002 : len = 753928, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 754008, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.857156s wall, 0.890625s user + 0.015625s system = 0.906250s CPU (105.7%)

PHY-1001 : Congestion index: top1 = 51.44, top5 = 45.23, top10 = 42.03, top15 = 40.09.
OPT-1001 : End congestion update;  1.193700s wall, 1.218750s user + 0.015625s system = 1.234375s CPU (103.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19837 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.861489s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (99.8%)

OPT-0007 : Start: WNS 4793 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  2.061148s wall, 2.093750s user + 0.015625s system = 2.109375s CPU (102.3%)

OPT-1001 : Current memory(MB): used = 733, reserve = 715, peak = 738.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19837 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.843770s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (98.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17894/19839.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 754008, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.132231s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (106.3%)

PHY-1001 : Congestion index: top1 = 51.44, top5 = 45.23, top10 = 42.03, top15 = 40.09.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19837 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.851089s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (99.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4793 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 51.068966
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4793ps with logic level 1 
RUN-1001 :       #2 path slack 4885ps with logic level 8 
RUN-1001 :       #3 path slack 4885ps with logic level 8 
OPT-1001 : End physical optimization;  17.247979s wall, 18.718750s user + 0.203125s system = 18.921875s CPU (109.7%)

RUN-1003 : finish command "place" in  80.290949s wall, 162.625000s user + 9.562500s system = 172.187500s CPU (214.5%)

RUN-1004 : used memory is 648 MB, reserved memory is 627 MB, peak memory is 738 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.810293s wall, 3.109375s user + 0.000000s system = 3.109375s CPU (171.8%)

RUN-1004 : used memory is 648 MB, reserved memory is 628 MB, peak memory is 738 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8366 instances
RUN-1001 : 4118 mslices, 4146 lslices, 60 pads, 37 brams, 0 dsps
RUN-1001 : There are total 19839 nets
RUN-1001 : 13820 nets have 2 pins
RUN-1001 : 4595 nets have [3 - 5] pins
RUN-1001 : 872 nets have [6 - 10] pins
RUN-1001 : 402 nets have [11 - 20] pins
RUN-1001 : 140 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69449, tnet num: 19837, tinst num: 8364, tnode num: 94824, tedge num: 114140.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.901030s wall, 1.890625s user + 0.000000s system = 1.890625s CPU (99.5%)

RUN-1004 : used memory is 630 MB, reserved memory is 612 MB, peak memory is 738 MB
PHY-1001 : 4118 mslices, 4146 lslices, 60 pads, 37 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19837 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 690568, over cnt = 2437(6%), over = 4116, worst = 9
PHY-1002 : len = 708032, over cnt = 1518(4%), over = 2145, worst = 8
PHY-1002 : len = 723544, over cnt = 752(2%), over = 1053, worst = 7
PHY-1002 : len = 740160, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 740360, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.047003s wall, 3.500000s user + 0.046875s system = 3.546875s CPU (173.3%)

PHY-1001 : Congestion index: top1 = 50.50, top5 = 44.62, top10 = 41.60, top15 = 39.69.
PHY-1001 : End global routing;  2.432626s wall, 3.890625s user + 0.046875s system = 3.937500s CPU (161.9%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 712, reserve = 701, peak = 738.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 982, reserve = 970, peak = 982.
PHY-1001 : End build detailed router design. 4.847998s wall, 4.812500s user + 0.031250s system = 4.843750s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 196664, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.014192s wall, 1.000000s user + 0.015625s system = 1.015625s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1019, reserve = 1008, peak = 1019.
PHY-1001 : End phase 1; 1.022193s wall, 1.000000s user + 0.015625s system = 1.015625s CPU (99.4%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 55% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.79698e+06, over cnt = 1452(0%), over = 1458, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1036, reserve = 1025, peak = 1036.
PHY-1001 : End initial routed; 20.972658s wall, 51.375000s user + 0.406250s system = 51.781250s CPU (246.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18631(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.456   |   0.000   |   0   
RUN-1001 :   Hold   |   0.156   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.750919s wall, 3.750000s user + 0.000000s system = 3.750000s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1047, reserve = 1036, peak = 1047.
PHY-1001 : End phase 2; 24.723741s wall, 55.125000s user + 0.406250s system = 55.531250s CPU (224.6%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.79698e+06, over cnt = 1452(0%), over = 1458, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.268786s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (98.8%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.78263e+06, over cnt = 504(0%), over = 505, worst = 2, crit = 0
PHY-1001 : End DR Iter 1; 0.915712s wall, 1.609375s user + 0.015625s system = 1.625000s CPU (177.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.78365e+06, over cnt = 113(0%), over = 113, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.466901s wall, 0.609375s user + 0.015625s system = 0.625000s CPU (133.9%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.78469e+06, over cnt = 27(0%), over = 27, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.366081s wall, 0.453125s user + 0.015625s system = 0.468750s CPU (128.0%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.78521e+06, over cnt = 11(0%), over = 11, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.273075s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (103.0%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.78536e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.205946s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (121.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18631(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.300   |   0.000   |   0   
RUN-1001 :   Hold   |   0.195   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 4.032358s wall, 4.031250s user + 0.000000s system = 4.031250s CPU (100.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 355 feed throughs used by 309 nets
PHY-1001 : End commit to database; 2.421526s wall, 2.421875s user + 0.000000s system = 2.421875s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1137, reserve = 1128, peak = 1137.
PHY-1001 : End phase 3; 9.536148s wall, 10.468750s user + 0.062500s system = 10.531250s CPU (110.4%)

PHY-1003 : Routed, final wirelength = 1.78536e+06
PHY-1001 : Current memory(MB): used = 1141, reserve = 1133, peak = 1141.
PHY-1001 : End export database. 0.065650s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (95.2%)

PHY-1001 : End detail routing;  40.659979s wall, 71.921875s user + 0.531250s system = 72.453125s CPU (178.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69449, tnet num: 19837, tinst num: 8364, tnode num: 94824, tedge num: 114140.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.977424s wall, 1.984375s user + 0.000000s system = 1.984375s CPU (100.4%)

RUN-1004 : used memory is 1074 MB, reserved memory is 1069 MB, peak memory is 1141 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  50.058103s wall, 82.750000s user + 0.593750s system = 83.343750s CPU (166.5%)

RUN-1004 : used memory is 1074 MB, reserved memory is 1072 MB, peak memory is 1141 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8867   out of  19600   45.24%
#reg                    13011   out of  19600   66.38%
#le                     15468
  #lut only              2457   out of  15468   15.88%
  #reg only              6601   out of  15468   42.68%
  #lut&reg               6410   out of  15468   41.44%
#dsp                        0   out of     29    0.00%
#bram                      37   out of     64   57.81%
  #bram9k                  37
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                    10
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    7105
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          168
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         M3        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         P6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15468  |7434    |1433    |13054   |37      |0       |
|  AnyFog_dataX                      |AnyFog          |226    |104     |22      |179     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |105    |76      |22      |58      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |208    |90      |22      |174     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |60      |22      |52      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |200    |90      |22      |165     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |55      |22      |51      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |3513   |900     |34      |3432    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |731    |68      |5       |720     |0       |0       |
|    STADOP_com2                     |STADOP          |547    |129     |0       |541     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |115    |79      |14      |85      |0       |0       |
|    head_com2                       |uniheading      |269    |100     |5       |252     |0       |0       |
|    uart_com2                       |Agrica          |1560   |233     |10      |1543    |0       |0       |
|  COM3                              |COM3_Control    |211    |132     |14      |178     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |60     |38      |14      |38      |0       |0       |
|    rmc_com3                        |Gprmc           |151    |94      |0       |140     |0       |0       |
|  DATA                              |Data_Processing |8734   |4358    |1059    |7049    |0       |0       |
|    DIV_Dtemp                       |Divider         |830    |347     |84      |690     |0       |0       |
|    DIV_Utemp                       |Divider         |646    |264     |84      |521     |0       |0       |
|    DIV_accX                        |Divider         |553    |295     |84      |426     |0       |0       |
|    DIV_accY                        |Divider         |642    |307     |108     |476     |0       |0       |
|    DIV_accZ                        |Divider         |710    |384     |132     |503     |0       |0       |
|    DIV_rateX                       |Divider         |662    |371     |132     |456     |0       |0       |
|    DIV_rateY                       |Divider         |605    |376     |132     |399     |0       |0       |
|    DIV_rateZ                       |Divider         |522    |355     |132     |315     |0       |0       |
|    genclk                          |genclk          |88     |55      |20      |55      |0       |0       |
|  FMC                               |FMC_Ctrl        |454    |402     |43      |351     |0       |0       |
|  IIC                               |I2C_master      |291    |244     |11      |264     |0       |0       |
|  IMU_CTRL                          |SCHA634         |898    |665     |61      |739     |0       |0       |
|    CtrlData                        |CtrlData        |451    |398     |47      |332     |0       |0       |
|      usms                          |Time_1ms        |29     |24      |5       |21      |0       |0       |
|    SPIM                            |SPI_SCHA634     |447    |267     |14      |407     |0       |0       |
|  POWER                             |POWER_EN        |95     |51      |38      |36      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |616    |396     |107     |422     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |616    |396     |107     |422     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |273    |184     |0       |259     |0       |0       |
|        reg_inst                    |register        |271    |182     |0       |257     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |343    |212     |107     |163     |0       |0       |
|        bus_inst                    |bus_top         |137    |87      |50      |53      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |28     |18      |10      |12      |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |51     |33      |18      |19      |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |14     |8       |6       |5       |0       |0       |
|          BUS_DETECTOR[5]$bus_nodes |bus_det         |16     |10      |6       |7       |0       |0       |
|          BUS_DETECTOR[6]$bus_nodes |bus_det         |28     |18      |10      |10      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |128    |86      |29      |78      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13759  
    #2          2       3684   
    #3          3        664   
    #4          4        247   
    #5        5-10       922   
    #6        11-50      484   
    #7       51-100       8    
    #8       101-500      4    
    #9        >500        2    
  Average     2.14             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.212603s wall, 3.781250s user + 0.015625s system = 3.796875s CPU (171.6%)

RUN-1004 : used memory is 1075 MB, reserved memory is 1072 MB, peak memory is 1141 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69449, tnet num: 19837, tinst num: 8364, tnode num: 94824, tedge num: 114140.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.916740s wall, 1.921875s user + 0.000000s system = 1.921875s CPU (100.3%)

RUN-1004 : used memory is 1077 MB, reserved memory is 1073 MB, peak memory is 1141 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19837 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.550100s wall, 1.546875s user + 0.000000s system = 1.546875s CPU (99.8%)

RUN-1004 : used memory is 1082 MB, reserved memory is 1077 MB, peak memory is 1141 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 8be28411f7730a971c8e05bdf06eab6934c4a899986b333e3d1a535f60eb5e63 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8364
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19839, pip num: 151582
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 355
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3265 valid insts, and 422345 bits set as '1'.
BIT-1004 : the usercode register value: 00000000110110001100010101110111
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  13.271228s wall, 127.140625s user + 0.187500s system = 127.328125s CPU (959.4%)

RUN-1004 : used memory is 1211 MB, reserved memory is 1196 MB, peak memory is 1325 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250701_143028.log"

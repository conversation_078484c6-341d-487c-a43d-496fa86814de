============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Fri Aug 15 10:10:27 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.163923s wall, 1.531250s user + 3.640625s system = 5.171875s CPU (100.2%)

RUN-1004 : used memory is 78 MB, reserved memory is 40 MB, peak memory is 89 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.731280s wall, 1.625000s user + 0.109375s system = 1.734375s CPU (100.2%)

RUN-1004 : used memory is 299 MB, reserved memory is 268 MB, peak memory is 302 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 2 view nodes, 24 trigger nets, 24 data nets.
KIT-1004 : Chipwatcher code = 1010010110001101
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=78) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=78) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=78)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=78)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22223/12 useful/useless nets, 19238/4 useful/useless insts
SYN-1016 : Merged 17 instances.
SYN-1032 : 21999/16 useful/useless nets, 19562/12 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 2 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 2 mux instances.
SYN-1015 : Optimize round 1, 279 better
SYN-1014 : Optimize round 2
SYN-1032 : 21790/30 useful/useless nets, 19353/32 useful/useless insts
SYN-1015 : Optimize round 2, 64 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.089133s wall, 2.062500s user + 0.015625s system = 2.078125s CPU (99.5%)

RUN-1004 : used memory is 323 MB, reserved memory is 291 MB, peak memory is 326 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 21814/155 useful/useless nets, 19398/29 useful/useless insts
SYN-1016 : Merged 21 instances.
SYN-2571 : Optimize after map_dsp, round 1, 205 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 24 instances.
SYN-2501 : Optimize round 1, 50 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 14 macro adder
SYN-1019 : Optimized 9 mux instances.
SYN-1016 : Merged 10 instances.
SYN-1032 : 22185/5 useful/useless nets, 19769/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80437, tnet num: 22185, tinst num: 19768, tnode num: 113045, tedge num: 125680.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.094085s wall, 1.062500s user + 0.031250s system = 1.093750s CPU (100.0%)

RUN-1004 : used memory is 461 MB, reserved memory is 430 MB, peak memory is 461 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22185 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 173 (3.57), #lev = 7 (1.79)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 173 (3.57), #lev = 7 (1.79)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 387 instances into 173 LUTs, name keeping = 77%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 290 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 108 adder to BLE ...
SYN-4008 : Packed 108 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  3.837957s wall, 3.765625s user + 0.078125s system = 3.843750s CPU (100.2%)

RUN-1004 : used memory is 343 MB, reserved memory is 321 MB, peak memory is 567 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  6.215308s wall, 6.093750s user + 0.125000s system = 6.218750s CPU (100.1%)

RUN-1004 : used memory is 343 MB, reserved memory is 321 MB, peak memory is 567 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (177 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19077 instances
RUN-0007 : 5518 luts, 12041 seqs, 937 mslices, 494 lslices, 58 pads, 24 brams, 0 dsps
RUN-1001 : There are total 21521 nets
RUN-1001 : 16216 nets have 2 pins
RUN-1001 : 4138 nets have [3 - 5] pins
RUN-1001 : 818 nets have [6 - 10] pins
RUN-1001 : 225 nets have [11 - 20] pins
RUN-1001 : 106 nets have [21 - 99] pins
RUN-1001 : 18 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4792     
RUN-1001 :   No   |  No   |  Yes  |     631     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     349     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19075 instances, 5518 luts, 12041 seqs, 1431 slices, 283 macros(1431 instances: 937 mslices 494 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 61%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 79000, tnet num: 21519, tinst num: 19075, tnode num: 111202, tedge num: 124084.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.122784s wall, 1.109375s user + 0.015625s system = 1.125000s CPU (100.2%)

RUN-1004 : used memory is 518 MB, reserved memory is 492 MB, peak memory is 567 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21519 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.886511s wall, 1.859375s user + 0.031250s system = 1.890625s CPU (100.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.55332e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19075.
PHY-3001 : Level 1 #clusters 2157.
PHY-3001 : End clustering;  0.129909s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (132.3%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 61%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 835526, overlap = 598.938
PHY-3002 : Step(2): len = 762731, overlap = 649
PHY-3002 : Step(3): len = 495232, overlap = 816.031
PHY-3002 : Step(4): len = 435981, overlap = 875
PHY-3002 : Step(5): len = 341464, overlap = 999.25
PHY-3002 : Step(6): len = 308045, overlap = 1071.66
PHY-3002 : Step(7): len = 259045, overlap = 1137.09
PHY-3002 : Step(8): len = 234727, overlap = 1176.88
PHY-3002 : Step(9): len = 203214, overlap = 1215.72
PHY-3002 : Step(10): len = 186161, overlap = 1246.47
PHY-3002 : Step(11): len = 165149, overlap = 1270.44
PHY-3002 : Step(12): len = 156514, overlap = 1287.06
PHY-3002 : Step(13): len = 142321, overlap = 1311.62
PHY-3002 : Step(14): len = 135503, overlap = 1335.31
PHY-3002 : Step(15): len = 128128, overlap = 1354.09
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.14515e-06
PHY-3002 : Step(16): len = 131978, overlap = 1358.69
PHY-3002 : Step(17): len = 172370, overlap = 1286.97
PHY-3002 : Step(18): len = 182171, overlap = 1181.44
PHY-3002 : Step(19): len = 186131, overlap = 1134.41
PHY-3002 : Step(20): len = 180359, overlap = 1100.41
PHY-3002 : Step(21): len = 175122, overlap = 1078.66
PHY-3002 : Step(22): len = 171890, overlap = 1066.53
PHY-3002 : Step(23): len = 168694, overlap = 1064.5
PHY-3002 : Step(24): len = 167724, overlap = 1073.91
PHY-3002 : Step(25): len = 165357, overlap = 1084.81
PHY-3002 : Step(26): len = 163416, overlap = 1084.97
PHY-3002 : Step(27): len = 162053, overlap = 1096.19
PHY-3002 : Step(28): len = 160078, overlap = 1081.53
PHY-3002 : Step(29): len = 158388, overlap = 1082.41
PHY-3002 : Step(30): len = 157496, overlap = 1083.78
PHY-3002 : Step(31): len = 156476, overlap = 1093.62
PHY-3002 : Step(32): len = 156046, overlap = 1079.38
PHY-3002 : Step(33): len = 155153, overlap = 1093.22
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.2903e-06
PHY-3002 : Step(34): len = 160997, overlap = 1051.31
PHY-3002 : Step(35): len = 175005, overlap = 996.469
PHY-3002 : Step(36): len = 178982, overlap = 971
PHY-3002 : Step(37): len = 181076, overlap = 955.062
PHY-3002 : Step(38): len = 181507, overlap = 951.438
PHY-3002 : Step(39): len = 183363, overlap = 936.656
PHY-3002 : Step(40): len = 181663, overlap = 941.688
PHY-3002 : Step(41): len = 181318, overlap = 942.219
PHY-3002 : Step(42): len = 179566, overlap = 937.688
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.58061e-06
PHY-3002 : Step(43): len = 190327, overlap = 894.656
PHY-3002 : Step(44): len = 208530, overlap = 803.844
PHY-3002 : Step(45): len = 213423, overlap = 767.344
PHY-3002 : Step(46): len = 215839, overlap = 765.469
PHY-3002 : Step(47): len = 216654, overlap = 739.688
PHY-3002 : Step(48): len = 214837, overlap = 740.156
PHY-3002 : Step(49): len = 213278, overlap = 744.375
PHY-3002 : Step(50): len = 212065, overlap = 735.875
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 9.16122e-06
PHY-3002 : Step(51): len = 224989, overlap = 689.75
PHY-3002 : Step(52): len = 240522, overlap = 588.906
PHY-3002 : Step(53): len = 244479, overlap = 529.219
PHY-3002 : Step(54): len = 247563, overlap = 528.812
PHY-3002 : Step(55): len = 246716, overlap = 509
PHY-3002 : Step(56): len = 245447, overlap = 506.344
PHY-3002 : Step(57): len = 243317, overlap = 508.844
PHY-3002 : Step(58): len = 242734, overlap = 530.438
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.83224e-05
PHY-3002 : Step(59): len = 256977, overlap = 481.656
PHY-3002 : Step(60): len = 269506, overlap = 446.406
PHY-3002 : Step(61): len = 271349, overlap = 404.781
PHY-3002 : Step(62): len = 273613, overlap = 374.406
PHY-3002 : Step(63): len = 273048, overlap = 365.281
PHY-3002 : Step(64): len = 272473, overlap = 366.281
PHY-3002 : Step(65): len = 271267, overlap = 365.719
PHY-3002 : Step(66): len = 269874, overlap = 369.031
PHY-3002 : Step(67): len = 269258, overlap = 364.406
PHY-3002 : Step(68): len = 269078, overlap = 368.406
PHY-3002 : Step(69): len = 268137, overlap = 397.375
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.66449e-05
PHY-3002 : Step(70): len = 279344, overlap = 367.094
PHY-3002 : Step(71): len = 290613, overlap = 340.812
PHY-3002 : Step(72): len = 292284, overlap = 338.312
PHY-3002 : Step(73): len = 293350, overlap = 338.719
PHY-3002 : Step(74): len = 292497, overlap = 343.5
PHY-3002 : Step(75): len = 290837, overlap = 341.875
PHY-3002 : Step(76): len = 288251, overlap = 340.062
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 7.32897e-05
PHY-3002 : Step(77): len = 297878, overlap = 307.969
PHY-3002 : Step(78): len = 306062, overlap = 301.656
PHY-3002 : Step(79): len = 308143, overlap = 297.719
PHY-3002 : Step(80): len = 308840, overlap = 291.656
PHY-3002 : Step(81): len = 308355, overlap = 292.031
PHY-3002 : Step(82): len = 307735, overlap = 296.031
PHY-3002 : Step(83): len = 305363, overlap = 288.531
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000146579
PHY-3002 : Step(84): len = 311443, overlap = 269.625
PHY-3002 : Step(85): len = 319089, overlap = 241.75
PHY-3002 : Step(86): len = 322458, overlap = 223.094
PHY-3002 : Step(87): len = 322583, overlap = 232.656
PHY-3002 : Step(88): len = 322681, overlap = 250.25
PHY-3002 : Step(89): len = 321862, overlap = 239.656
PHY-3002 : Step(90): len = 320386, overlap = 237.375
PHY-3002 : Step(91): len = 320647, overlap = 238.312
PHY-3002 : Step(92): len = 320201, overlap = 237.281
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000293159
PHY-3002 : Step(93): len = 323508, overlap = 232.406
PHY-3002 : Step(94): len = 328469, overlap = 223.719
PHY-3002 : Step(95): len = 329575, overlap = 220.406
PHY-3002 : Step(96): len = 330067, overlap = 210.781
PHY-3002 : Step(97): len = 329877, overlap = 203.844
PHY-3002 : Step(98): len = 329716, overlap = 198.75
PHY-3002 : Step(99): len = 328883, overlap = 185.375
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000486242
PHY-3002 : Step(100): len = 330024, overlap = 180.625
PHY-3002 : Step(101): len = 332689, overlap = 165.469
PHY-3002 : Step(102): len = 333677, overlap = 154.438
PHY-3002 : Step(103): len = 334174, overlap = 155.969
PHY-3002 : Step(104): len = 334149, overlap = 165.5
PHY-3002 : Step(105): len = 334338, overlap = 182.125
PHY-3002 : Step(106): len = 333623, overlap = 184.125
PHY-3002 : Step(107): len = 333890, overlap = 196.312
PHY-3002 : Step(108): len = 334545, overlap = 204.938
PHY-3002 : Step(109): len = 334831, overlap = 200.281
PHY-3002 : Step(110): len = 334272, overlap = 198.125
PHY-3001 : :::11::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(111): len = 335005, overlap = 195.5
PHY-3002 : Step(112): len = 336391, overlap = 196.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.011554s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (270.5%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21521.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 424448, over cnt = 1106(3%), over = 5197, worst = 36
PHY-1001 : End global iterations;  0.696183s wall, 0.859375s user + 0.031250s system = 0.890625s CPU (127.9%)

PHY-1001 : Congestion index: top1 = 74.50, top5 = 51.99, top10 = 41.98, top15 = 36.35.
PHY-3001 : End congestion estimation;  0.903580s wall, 1.078125s user + 0.031250s system = 1.109375s CPU (122.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21519 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.798108s wall, 0.765625s user + 0.031250s system = 0.796875s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000131627
PHY-3002 : Step(113): len = 376461, overlap = 185.125
PHY-3002 : Step(114): len = 390467, overlap = 170.188
PHY-3002 : Step(115): len = 388742, overlap = 156.719
PHY-3002 : Step(116): len = 386421, overlap = 145.094
PHY-3002 : Step(117): len = 391132, overlap = 132.031
PHY-3002 : Step(118): len = 396012, overlap = 126.312
PHY-3002 : Step(119): len = 399535, overlap = 114.312
PHY-3002 : Step(120): len = 404122, overlap = 108.344
PHY-3002 : Step(121): len = 404113, overlap = 105.125
PHY-3002 : Step(122): len = 403927, overlap = 102.812
PHY-3002 : Step(123): len = 405371, overlap = 103.938
PHY-3002 : Step(124): len = 405561, overlap = 97.5
PHY-3002 : Step(125): len = 405775, overlap = 95.125
PHY-3002 : Step(126): len = 407483, overlap = 94.9062
PHY-3002 : Step(127): len = 405872, overlap = 93.6562
PHY-3002 : Step(128): len = 405227, overlap = 92.875
PHY-3002 : Step(129): len = 405319, overlap = 95.7188
PHY-3002 : Step(130): len = 403638, overlap = 96.4062
PHY-3002 : Step(131): len = 403019, overlap = 94.8125
PHY-3002 : Step(132): len = 402997, overlap = 96.0938
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000263255
PHY-3002 : Step(133): len = 403994, overlap = 93.4062
PHY-3002 : Step(134): len = 406657, overlap = 91.125
PHY-3002 : Step(135): len = 409415, overlap = 89.1875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000526509
PHY-3002 : Step(136): len = 414594, overlap = 85.5938
PHY-3002 : Step(137): len = 422771, overlap = 88.7188
PHY-3002 : Step(138): len = 430346, overlap = 92.4688
PHY-3002 : Step(139): len = 429610, overlap = 90.0625
PHY-3002 : Step(140): len = 429325, overlap = 90.0938
PHY-3002 : Step(141): len = 429798, overlap = 95.4688
PHY-3002 : Step(142): len = 428495, overlap = 93.2188
PHY-3002 : Step(143): len = 428841, overlap = 93.1562
PHY-3002 : Step(144): len = 430677, overlap = 92.7188
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00105302
PHY-3002 : Step(145): len = 429793, overlap = 85.9688
PHY-3002 : Step(146): len = 433632, overlap = 87.4062
PHY-3002 : Step(147): len = 439672, overlap = 84.3125
PHY-3002 : Step(148): len = 446675, overlap = 80.5312
PHY-3002 : Step(149): len = 449860, overlap = 85.9688
PHY-3002 : Step(150): len = 448457, overlap = 80.75
PHY-3002 : Step(151): len = 445794, overlap = 83.0625
PHY-3002 : Step(152): len = 444082, overlap = 88.0312
PHY-3002 : Step(153): len = 443633, overlap = 83.625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 65/21521.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 503136, over cnt = 2046(5%), over = 9668, worst = 33
PHY-1001 : End global iterations;  0.906102s wall, 1.453125s user + 0.000000s system = 1.453125s CPU (160.4%)

PHY-1001 : Congestion index: top1 = 78.41, top5 = 58.26, top10 = 49.48, top15 = 44.23.
PHY-3001 : End congestion estimation;  1.135936s wall, 1.687500s user + 0.000000s system = 1.687500s CPU (148.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21519 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.819122s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (101.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.40079e-05
PHY-3002 : Step(154): len = 446819, overlap = 377.75
PHY-3002 : Step(155): len = 449223, overlap = 305.344
PHY-3002 : Step(156): len = 441027, overlap = 281.375
PHY-3002 : Step(157): len = 436454, overlap = 257.812
PHY-3002 : Step(158): len = 431133, overlap = 239.188
PHY-3002 : Step(159): len = 427472, overlap = 225.688
PHY-3002 : Step(160): len = 425137, overlap = 213.531
PHY-3002 : Step(161): len = 420876, overlap = 219.5
PHY-3002 : Step(162): len = 418935, overlap = 223.938
PHY-3002 : Step(163): len = 417570, overlap = 216.25
PHY-3002 : Step(164): len = 413713, overlap = 219.812
PHY-3002 : Step(165): len = 412076, overlap = 216.844
PHY-3002 : Step(166): len = 411481, overlap = 211.625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000188016
PHY-3002 : Step(167): len = 411628, overlap = 209.938
PHY-3002 : Step(168): len = 413577, overlap = 205.969
PHY-3002 : Step(169): len = 415815, overlap = 196.844
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000376031
PHY-3002 : Step(170): len = 417974, overlap = 187.438
PHY-3002 : Step(171): len = 423342, overlap = 170.281
PHY-3002 : Step(172): len = 429069, overlap = 157.125
PHY-3002 : Step(173): len = 430789, overlap = 149.062
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000752063
PHY-3002 : Step(174): len = 430898, overlap = 146.062
PHY-3002 : Step(175): len = 433179, overlap = 134.625
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 79000, tnet num: 21519, tinst num: 19075, tnode num: 111202, tedge num: 124084.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.365059s wall, 1.328125s user + 0.031250s system = 1.359375s CPU (99.6%)

RUN-1004 : used memory is 559 MB, reserved memory is 534 MB, peak memory is 689 MB
OPT-1001 : Total overflow 488.50 peak overflow 3.59
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 612/21521.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 505872, over cnt = 2219(6%), over = 7915, worst = 24
PHY-1001 : End global iterations;  1.100537s wall, 1.656250s user + 0.031250s system = 1.687500s CPU (153.3%)

PHY-1001 : Congestion index: top1 = 54.05, top5 = 44.78, top10 = 40.00, top15 = 37.00.
PHY-1001 : End incremental global routing;  1.307645s wall, 1.859375s user + 0.046875s system = 1.906250s CPU (145.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21519 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.845705s wall, 0.812500s user + 0.031250s system = 0.843750s CPU (99.8%)

OPT-1001 : 17 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 58 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 18997 has valid locations, 222 needs to be replaced
PHY-3001 : design contains 19280 instances, 5609 luts, 12155 seqs, 1431 slices, 283 macros(1431 instances: 937 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 448046
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16613/21726.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 516832, over cnt = 2230(6%), over = 7946, worst = 24
PHY-1001 : End global iterations;  0.169087s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (138.6%)

PHY-1001 : Congestion index: top1 = 54.20, top5 = 44.98, top10 = 40.23, top15 = 37.26.
PHY-3001 : End congestion estimation;  0.371344s wall, 0.421875s user + 0.015625s system = 0.437500s CPU (117.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 79665, tnet num: 21724, tinst num: 19280, tnode num: 112127, tedge num: 125004.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.392332s wall, 1.375000s user + 0.015625s system = 1.390625s CPU (99.9%)

RUN-1004 : used memory is 600 MB, reserved memory is 596 MB, peak memory is 689 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21724 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.387930s wall, 2.343750s user + 0.031250s system = 2.375000s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(176): len = 447869, overlap = 0.25
PHY-3002 : Step(177): len = 448737, overlap = 0.25
PHY-3002 : Step(178): len = 449381, overlap = 0.4375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 16656/21726.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 515936, over cnt = 2251(6%), over = 8043, worst = 25
PHY-1001 : End global iterations;  0.150818s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (114.0%)

PHY-1001 : Congestion index: top1 = 55.04, top5 = 45.27, top10 = 40.44, top15 = 37.43.
PHY-3001 : End congestion estimation;  0.370198s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (101.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21724 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.876025s wall, 0.890625s user + 0.000000s system = 0.890625s CPU (101.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000741252
PHY-3002 : Step(179): len = 449382, overlap = 137.875
PHY-3002 : Step(180): len = 449823, overlap = 137.812
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0014825
PHY-3002 : Step(181): len = 450190, overlap = 137.031
PHY-3002 : Step(182): len = 450492, overlap = 136.969
PHY-3001 : Final: Len = 450492, Over = 136.969
PHY-3001 : End incremental placement;  4.767286s wall, 5.109375s user + 0.281250s system = 5.390625s CPU (113.1%)

OPT-1001 : Total overflow 492.09 peak overflow 3.59
OPT-1001 : End high-fanout net optimization;  7.378803s wall, 8.234375s user + 0.359375s system = 8.593750s CPU (116.5%)

OPT-1001 : Current memory(MB): used = 694, reserve = 674, peak = 709.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16638/21726.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 518176, over cnt = 2199(6%), over = 7567, worst = 24
PHY-1002 : len = 550640, over cnt = 1546(4%), over = 4299, worst = 23
PHY-1002 : len = 596552, over cnt = 516(1%), over = 1022, worst = 20
PHY-1002 : len = 603568, over cnt = 224(0%), over = 476, worst = 15
PHY-1002 : len = 611896, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.086094s wall, 1.687500s user + 0.109375s system = 1.796875s CPU (165.4%)

PHY-1001 : Congestion index: top1 = 46.59, top5 = 40.85, top10 = 37.97, top15 = 36.07.
OPT-1001 : End congestion update;  1.312632s wall, 1.890625s user + 0.109375s system = 2.000000s CPU (152.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 21724 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.880102s wall, 0.890625s user + 0.000000s system = 0.890625s CPU (101.2%)

OPT-0007 : Start: WNS 3261 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.198537s wall, 2.781250s user + 0.109375s system = 2.890625s CPU (131.5%)

OPT-1001 : Current memory(MB): used = 692, reserve = 672, peak = 709.
OPT-1001 : End physical optimization;  11.251083s wall, 12.781250s user + 0.546875s system = 13.328125s CPU (118.5%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5609 LUT to BLE ...
SYN-4008 : Packed 5609 LUT and 2681 SEQ to BLE.
SYN-4003 : Packing 9474 remaining SEQ's ...
SYN-4005 : Packed 3275 SEQ with LUT/SLICE
SYN-4006 : 175 single LUT's are left
SYN-4006 : 6199 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11808/13590 primitive instances ...
PHY-3001 : End packing;  2.598804s wall, 2.593750s user + 0.000000s system = 2.593750s CPU (99.8%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 7953 instances
RUN-1001 : 3933 mslices, 3933 lslices, 58 pads, 24 brams, 0 dsps
RUN-1001 : There are total 19099 nets
RUN-1001 : 13458 nets have 2 pins
RUN-1001 : 4259 nets have [3 - 5] pins
RUN-1001 : 878 nets have [6 - 10] pins
RUN-1001 : 376 nets have [11 - 20] pins
RUN-1001 : 119 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 7951 instances, 7866 slices, 283 macros(1431 instances: 937 mslices 494 lslices)
PHY-3001 : Cell area utilization is 84%
PHY-3001 : After packing: Len = 469288, Over = 362.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7641/19099.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 585464, over cnt = 1399(3%), over = 2268, worst = 9
PHY-1002 : len = 590592, over cnt = 922(2%), over = 1310, worst = 8
PHY-1002 : len = 601752, over cnt = 325(0%), over = 413, worst = 6
PHY-1002 : len = 606080, over cnt = 118(0%), over = 164, worst = 5
PHY-1002 : len = 609096, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.239222s wall, 1.984375s user + 0.015625s system = 2.000000s CPU (161.4%)

PHY-1001 : Congestion index: top1 = 48.86, top5 = 41.53, top10 = 38.33, top15 = 36.12.
PHY-3001 : End congestion estimation;  1.535047s wall, 2.281250s user + 0.015625s system = 2.296875s CPU (149.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66038, tnet num: 19097, tinst num: 7951, tnode num: 89625, tedge num: 108757.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.601471s wall, 1.578125s user + 0.015625s system = 1.593750s CPU (99.5%)

RUN-1004 : used memory is 587 MB, reserved memory is 572 MB, peak memory is 709 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19097 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.678781s wall, 2.656250s user + 0.015625s system = 2.671875s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.24594e-05
PHY-3002 : Step(183): len = 473927, overlap = 347.75
PHY-3002 : Step(184): len = 473811, overlap = 357.5
PHY-3002 : Step(185): len = 473069, overlap = 370.5
PHY-3002 : Step(186): len = 473541, overlap = 373.5
PHY-3002 : Step(187): len = 474272, overlap = 381.75
PHY-3002 : Step(188): len = 474223, overlap = 386
PHY-3002 : Step(189): len = 473459, overlap = 398.75
PHY-3002 : Step(190): len = 472242, overlap = 403
PHY-3002 : Step(191): len = 469951, overlap = 400.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000104919
PHY-3002 : Step(192): len = 472999, overlap = 385.75
PHY-3002 : Step(193): len = 476196, overlap = 373
PHY-3002 : Step(194): len = 476396, overlap = 366
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(195): len = 484947, overlap = 343
PHY-3002 : Step(196): len = 491740, overlap = 327
PHY-3002 : Step(197): len = 489380, overlap = 324.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.638324s wall, 0.781250s user + 0.765625s system = 1.546875s CPU (242.3%)

PHY-3001 : Trial Legalized: Len = 604860
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 83%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 634/19099.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 681488, over cnt = 2251(6%), over = 3746, worst = 8
PHY-1002 : len = 694768, over cnt = 1402(3%), over = 2031, worst = 8
PHY-1002 : len = 710256, over cnt = 636(1%), over = 867, worst = 6
PHY-1002 : len = 720704, over cnt = 174(0%), over = 214, worst = 3
PHY-1002 : len = 725376, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.648289s wall, 2.734375s user + 0.000000s system = 2.734375s CPU (165.9%)

PHY-1001 : Congestion index: top1 = 48.45, top5 = 44.15, top10 = 41.61, top15 = 39.83.
PHY-3001 : End congestion estimation;  1.965209s wall, 3.062500s user + 0.000000s system = 3.062500s CPU (155.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19097 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.804863s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (100.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000180366
PHY-3002 : Step(198): len = 559525, overlap = 83.75
PHY-3002 : Step(199): len = 539221, overlap = 120.25
PHY-3002 : Step(200): len = 526559, overlap = 158.25
PHY-3002 : Step(201): len = 517966, overlap = 198.75
PHY-3002 : Step(202): len = 514186, overlap = 226
PHY-3002 : Step(203): len = 511908, overlap = 228.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000360731
PHY-3002 : Step(204): len = 516438, overlap = 220.5
PHY-3002 : Step(205): len = 521158, overlap = 220.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000721462
PHY-3002 : Step(206): len = 522333, overlap = 220
PHY-3002 : Step(207): len = 530741, overlap = 218
PHY-3002 : Step(208): len = 534845, overlap = 216.75
PHY-3002 : Step(209): len = 536316, overlap = 214.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.026265s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (59.5%)

PHY-3001 : Legalized: Len = 578516, Over = 0
PHY-3001 : Spreading special nets. 25 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.070359s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (111.0%)

PHY-3001 : 41 instances has been re-located, deltaX = 18, deltaY = 23, maxDist = 2.
PHY-3001 : Final: Len = 579118, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66038, tnet num: 19097, tinst num: 7951, tnode num: 89625, tedge num: 108757.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.937387s wall, 1.937500s user + 0.000000s system = 1.937500s CPU (100.0%)

RUN-1004 : used memory is 615 MB, reserved memory is 610 MB, peak memory is 709 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3465/19099.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 665184, over cnt = 2037(5%), over = 3196, worst = 6
PHY-1002 : len = 674952, over cnt = 1257(3%), over = 1774, worst = 6
PHY-1002 : len = 692624, over cnt = 306(0%), over = 399, worst = 4
PHY-1002 : len = 697776, over cnt = 52(0%), over = 65, worst = 4
PHY-1002 : len = 699040, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.710348s wall, 2.718750s user + 0.015625s system = 2.734375s CPU (159.9%)

PHY-1001 : Congestion index: top1 = 46.19, top5 = 41.65, top10 = 39.23, top15 = 37.62.
PHY-1001 : End incremental global routing;  2.005733s wall, 3.015625s user + 0.015625s system = 3.031250s CPU (151.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19097 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.859658s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (100.0%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 58 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 7889 has valid locations, 11 needs to be replaced
PHY-3001 : design contains 7961 instances, 7876 slices, 283 macros(1431 instances: 937 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 580797
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17134/19108.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 700672, over cnt = 16(0%), over = 16, worst = 1
PHY-1002 : len = 700600, over cnt = 10(0%), over = 10, worst = 1
PHY-1002 : len = 700712, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.370130s wall, 0.437500s user + 0.015625s system = 0.453125s CPU (122.4%)

PHY-1001 : Congestion index: top1 = 46.49, top5 = 41.78, top10 = 39.35, top15 = 37.72.
PHY-3001 : End congestion estimation;  0.692826s wall, 0.765625s user + 0.015625s system = 0.781250s CPU (112.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66097, tnet num: 19106, tinst num: 7961, tnode num: 89694, tedge num: 108825.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.930911s wall, 1.921875s user + 0.000000s system = 1.921875s CPU (99.5%)

RUN-1004 : used memory is 655 MB, reserved memory is 645 MB, peak memory is 709 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19106 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.972251s wall, 2.968750s user + 0.000000s system = 2.968750s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(210): len = 580712, overlap = 0
PHY-3002 : Step(211): len = 580751, overlap = 0.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17131/19108.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 700080, over cnt = 14(0%), over = 15, worst = 2
PHY-1002 : len = 700072, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 700096, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 700112, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.944628s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (97.6%)

PHY-1001 : Congestion index: top1 = 46.21, top5 = 41.69, top10 = 39.32, top15 = 37.69.
PHY-3001 : End congestion estimation;  1.406604s wall, 1.359375s user + 0.000000s system = 1.359375s CPU (96.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19106 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.293353s wall, 1.265625s user + 0.015625s system = 1.281250s CPU (99.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000631825
PHY-3002 : Step(212): len = 580682, overlap = 1
PHY-3002 : Step(213): len = 580682, overlap = 0.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006134s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 580691, Over = 0
PHY-3001 : End spreading;  0.093628s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (100.1%)

PHY-3001 : Final: Len = 580691, Over = 0
PHY-3001 : End incremental placement;  7.015958s wall, 7.203125s user + 0.062500s system = 7.265625s CPU (103.6%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.451410s wall, 11.750000s user + 0.078125s system = 11.828125s CPU (113.2%)

OPT-1001 : Current memory(MB): used = 706, reserve = 689, peak = 711.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17133/19108.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 700176, over cnt = 6(0%), over = 7, worst = 2
PHY-1002 : len = 700128, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 700160, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 700200, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 700200, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.768439s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (101.7%)

PHY-1001 : Congestion index: top1 = 46.19, top5 = 41.69, top10 = 39.31, top15 = 37.70.
OPT-1001 : End congestion update;  1.124980s wall, 1.140625s user + 0.000000s system = 1.140625s CPU (101.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19106 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.736318s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (99.7%)

OPT-0007 : Start: WNS 3400 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.867217s wall, 1.875000s user + 0.000000s system = 1.875000s CPU (100.4%)

OPT-1001 : Current memory(MB): used = 707, reserve = 689, peak = 711.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19106 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.726232s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (101.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17144/19108.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 700200, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.102625s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (91.4%)

PHY-1001 : Congestion index: top1 = 46.19, top5 = 41.69, top10 = 39.31, top15 = 37.70.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19106 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.692257s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (99.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3400 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 45.862069
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3400ps with logic level 8 
RUN-1001 :       #2 path slack 3425ps with logic level 8 
RUN-1001 :       #3 path slack 3431ps with logic level 5 
OPT-1001 : End physical optimization;  16.270328s wall, 17.750000s user + 0.078125s system = 17.828125s CPU (109.6%)

RUN-1003 : finish command "place" in  68.531243s wall, 133.656250s user + 7.640625s system = 141.296875s CPU (206.2%)

RUN-1004 : used memory is 623 MB, reserved memory is 601 MB, peak memory is 711 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.612837s wall, 2.734375s user + 0.000000s system = 2.734375s CPU (169.5%)

RUN-1004 : used memory is 623 MB, reserved memory is 602 MB, peak memory is 711 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 7963 instances
RUN-1001 : 3943 mslices, 3933 lslices, 58 pads, 24 brams, 0 dsps
RUN-1001 : There are total 19108 nets
RUN-1001 : 13458 nets have 2 pins
RUN-1001 : 4259 nets have [3 - 5] pins
RUN-1001 : 884 nets have [6 - 10] pins
RUN-1001 : 378 nets have [11 - 20] pins
RUN-1001 : 120 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66097, tnet num: 19106, tinst num: 7961, tnode num: 89694, tedge num: 108825.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.785344s wall, 1.781250s user + 0.000000s system = 1.781250s CPU (99.8%)

RUN-1004 : used memory is 633 MB, reserved memory is 620 MB, peak memory is 711 MB
PHY-1001 : 3943 mslices, 3933 lslices, 58 pads, 24 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19106 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 647192, over cnt = 2187(6%), over = 3651, worst = 7
PHY-1002 : len = 657720, over cnt = 1465(4%), over = 2269, worst = 7
PHY-1002 : len = 681232, over cnt = 399(1%), over = 554, worst = 6
PHY-1002 : len = 689616, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.617824s wall, 2.921875s user + 0.046875s system = 2.968750s CPU (183.5%)

PHY-1001 : Congestion index: top1 = 45.99, top5 = 41.34, top10 = 39.06, top15 = 37.40.
PHY-1001 : End global routing;  1.984602s wall, 3.281250s user + 0.046875s system = 3.328125s CPU (167.7%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 694, reserve = 679, peak = 711.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 962, reserve = 950, peak = 962.
PHY-1001 : End build detailed router design. 4.711999s wall, 4.671875s user + 0.015625s system = 4.687500s CPU (99.5%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 188152, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.896785s wall, 0.890625s user + 0.015625s system = 0.906250s CPU (101.1%)

PHY-1001 : Current memory(MB): used = 998, reserve = 987, peak = 998.
PHY-1001 : End phase 1; 0.904454s wall, 0.890625s user + 0.015625s system = 0.906250s CPU (100.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 55% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.55569e+06, over cnt = 1223(0%), over = 1229, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1010, reserve = 997, peak = 1010.
PHY-1001 : End initial routed; 13.084999s wall, 41.562500s user + 0.296875s system = 41.859375s CPU (319.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17900(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.379   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.247456s wall, 3.234375s user + 0.000000s system = 3.234375s CPU (99.6%)

PHY-1001 : Current memory(MB): used = 1022, reserve = 1010, peak = 1022.
PHY-1001 : End phase 2; 16.332675s wall, 44.796875s user + 0.296875s system = 45.093750s CPU (276.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.55569e+06, over cnt = 1223(0%), over = 1229, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.224831s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (97.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.54475e+06, over cnt = 390(0%), over = 390, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.909958s wall, 1.562500s user + 0.000000s system = 1.562500s CPU (171.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.54494e+06, over cnt = 77(0%), over = 77, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.410535s wall, 0.625000s user + 0.015625s system = 0.640625s CPU (156.0%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.54568e+06, over cnt = 10(0%), over = 10, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.260482s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (108.0%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.54594e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 4; 0.164350s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (95.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17900(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.379   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.361946s wall, 3.359375s user + 0.000000s system = 3.359375s CPU (99.9%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 293 feed throughs used by 254 nets
PHY-1001 : End commit to database; 2.009026s wall, 2.000000s user + 0.015625s system = 2.015625s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 1106, reserve = 1096, peak = 1106.
PHY-1001 : End phase 3; 7.817180s wall, 8.718750s user + 0.031250s system = 8.750000s CPU (111.9%)

PHY-1003 : Routed, final wirelength = 1.54594e+06
PHY-1001 : Current memory(MB): used = 1110, reserve = 1101, peak = 1110.
PHY-1001 : End export database. 0.062427s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.1%)

PHY-1001 : End detail routing;  30.307215s wall, 59.609375s user + 0.359375s system = 59.968750s CPU (197.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66097, tnet num: 19106, tinst num: 7961, tnode num: 89694, tedge num: 108825.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.644451s wall, 1.640625s user + 0.000000s system = 1.640625s CPU (99.8%)

RUN-1004 : used memory is 1042 MB, reserved memory is 1040 MB, peak memory is 1110 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  38.166406s wall, 68.750000s user + 0.437500s system = 69.187500s CPU (181.3%)

RUN-1004 : used memory is 1041 MB, reserved memory is 1040 MB, peak memory is 1110 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8630   out of  19600   44.03%
#reg                    12253   out of  19600   62.52%
#le                     14786
  #lut only              2533   out of  14786   17.13%
  #reg only              6156   out of  14786   41.63%
  #lut&reg               6097   out of  14786   41.23%
#dsp                        0   out of     29    0.00%
#bram                      24   out of     64   37.50%
  #bram9k                  22
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       58   out of    188   30.85%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6737
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          106
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         F2        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         D1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         B3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         C1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         A3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         A2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         B2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         B1        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F15        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDZ        INPUT        K15        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_PPS        INPUT         T9        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        M14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        M13        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         P2        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT         P4        LVCMOS33          N/A           N/A            IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         N4        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         M5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         N6        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         N5        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         P5        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M3        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         M4        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         H2        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         M1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         L1        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         N1        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         N3        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         M2        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         T4        LVCMOS33           8            NONE           OREG       
    Fog_ENZ        OUTPUT         T7        LVCMOS33           8            NONE           OREG       
    Fog_INT        OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT         T8        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT         C3        LVCMOS33           8            N/A            OREG       
    I2C_SCL        OUTPUT         P9        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         F5        LVCMOS33           8            N/A            OREG       
    TXD_PPS        OUTPUT        L12        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         R9        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        C16        LVCMOS33           8            N/A            OREG       
     cs_uno        OUTPUT         G6        LVCMOS33           8            N/A            OREG       
      mosi         OUTPUT         B8        LVCMOS33           8            N/A            OREG       
      sclk         OUTPUT         J3        LVCMOS33           8            N/A            OREG       
    I2C_SDA         INOUT         N8        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT         K2        LVCMOS25           8            N/A            NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |14786  |7199    |1431    |12297   |24      |0       |
|  AnyFog_dataX                      |AnyFog          |207    |91      |22      |171     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |60      |22      |50      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |210    |91      |22      |175     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |87     |58      |22      |52      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |207    |123     |22      |172     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |63      |22      |51      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2934   |526     |39      |2849    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |61     |37      |5       |53      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |214    |96      |5       |203     |0       |0       |
|    STADOP_com2                     |STADOP          |552    |56      |0       |544     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |68     |45      |14      |46      |0       |0       |
|    head_com2                       |uniheading      |267    |51      |5       |254     |0       |0       |
|    rmc_com2                        |Gprmc           |34     |34      |0       |30      |0       |0       |
|    uart_com2                       |Agrica          |1427   |196     |10      |1408    |0       |0       |
|  COM3                              |COM3_Control    |272    |159     |19      |236     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |59     |36      |5       |51      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |62     |40      |14      |39      |0       |0       |
|    rmc_com3                        |Gprmc           |151    |83      |0       |146     |0       |0       |
|  DATA                              |Data_Processing |8634   |4478    |1065    |6942    |0       |0       |
|    DIV_Dtemp                       |Divider         |794    |382     |84      |672     |0       |0       |
|    DIV_Utemp                       |Divider         |607    |300     |84      |480     |0       |0       |
|    DIV_accX                        |Divider         |538    |330     |84      |413     |0       |0       |
|    DIV_accY                        |Divider         |640    |335     |114     |460     |0       |0       |
|    DIV_accZ                        |Divider         |705    |364     |132     |504     |0       |0       |
|    DIV_rateX                       |Divider         |642    |347     |132     |437     |0       |0       |
|    DIV_rateY                       |Divider         |609    |378     |132     |403     |0       |0       |
|    DIV_rateZ                       |Divider         |577    |356     |132     |371     |0       |0       |
|    genclk                          |genclk          |89     |57      |20      |56      |0       |0       |
|  FMC                               |FMC_Ctrl        |526    |477     |43      |355     |0       |0       |
|  IIC                               |I2C_master      |300    |273     |11      |256     |0       |0       |
|  IMU_CTRL                          |SCHA634         |925    |655     |61      |753     |0       |0       |
|    CtrlData                        |CtrlData        |472    |420     |47      |337     |0       |0       |
|      usms                          |Time_1ms        |30     |25      |5       |21      |0       |0       |
|    SPIM                            |SPI_SCHA634     |453    |235     |14      |416     |0       |0       |
|  POWER                             |POWER_EN        |97     |50      |38      |38      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |463    |276     |89      |295     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |463    |276     |89      |295     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |178    |95      |0       |165     |0       |0       |
|        reg_inst                    |register        |175    |92      |0       |162     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |285    |181     |89      |130     |0       |0       |
|        bus_inst                    |bus_top         |78     |50      |28      |29      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |26     |16      |10      |9       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |52     |34      |18      |20      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |127    |95      |29      |78      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13399  
    #2          2       3365   
    #3          3        629   
    #4          4        265   
    #5        5-10       940   
    #6        11-50      433   
    #7       51-100       9    
    #8       101-500      3    
    #9        >500        2    
  Average     2.10             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.195016s wall, 3.984375s user + 0.000000s system = 3.984375s CPU (181.5%)

RUN-1004 : used memory is 1042 MB, reserved memory is 1041 MB, peak memory is 1110 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66097, tnet num: 19106, tinst num: 7961, tnode num: 89694, tedge num: 108825.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.829058s wall, 1.828125s user + 0.000000s system = 1.828125s CPU (99.9%)

RUN-1004 : used memory is 1044 MB, reserved memory is 1043 MB, peak memory is 1110 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19106 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.339591s wall, 1.343750s user + 0.000000s system = 1.343750s CPU (100.3%)

RUN-1004 : used memory is 1048 MB, reserved memory is 1046 MB, peak memory is 1110 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 057bf4ae9dc0d18f7126c20ce7aec8c31cdd0d41110c6b742e6e5620711a0ef6 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 7961
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19108, pip num: 139780
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 293
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3231 valid insts, and 394557 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101000001010010110001101
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  10.136952s wall, 97.640625s user + 0.171875s system = 97.812500s CPU (964.9%)

RUN-1004 : used memory is 1169 MB, reserved memory is 1154 MB, peak memory is 1283 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250815_101027.log"

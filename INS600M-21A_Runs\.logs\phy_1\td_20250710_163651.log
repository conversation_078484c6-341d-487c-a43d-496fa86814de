============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Jul 10 16:36:51 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(520)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.271224s wall, 1.406250s user + 3.859375s system = 5.265625s CPU (99.9%)

RUN-1004 : used memory is 80 MB, reserved memory is 42 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.816160s wall, 1.781250s user + 0.046875s system = 1.828125s CPU (100.7%)

RUN-1004 : used memory is 302 MB, reserved memory is 270 MB, peak memory is 305 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "read_sdc -ip Asys_fifo8x8 ../../al_ip/Asys_fifo8x8.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 95176475279360"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 95176475279360"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  9.6999999999999993 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 9.6999999999999993"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  9.6999999999999993 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 9.6999999999999993"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 14 view nodes, 53 trigger nets, 53 data nets.
KIT-1004 : Chipwatcher code = 1010011101000100
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=14,BUS_DIN_NUM=53,BUS_CTRL_NUM=162,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb011,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011101,32'sb011110,32'sb011111,32'sb0100000,32'sb0100001,32'sb0100010,32'sb0101010,32'sb0110010,32'sb0110011,32'sb0110100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01001010,32'sb01010000,32'sb01010110,32'sb01011100,32'sb01100010,32'sb01101000,32'sb01111100,32'sb010010000,32'sb010010110,32'sb010011100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=184) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=184) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=14,BUS_DIN_NUM=53,BUS_CTRL_NUM=162,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb011,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011101,32'sb011110,32'sb011111,32'sb0100000,32'sb0100001,32'sb0100010,32'sb0101010,32'sb0110010,32'sb0110011,32'sb0110100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01001010,32'sb01010000,32'sb01010110,32'sb01011100,32'sb01100010,32'sb01101000,32'sb01111100,32'sb010010000,32'sb010010110,32'sb010011100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=14,BUS_DIN_NUM=53,BUS_CTRL_NUM=162,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb011,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011101,32'sb011110,32'sb011111,32'sb0100000,32'sb0100001,32'sb0100010,32'sb0101010,32'sb0110010,32'sb0110011,32'sb0110100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01001010,32'sb01010000,32'sb01010110,32'sb01011100,32'sb01100010,32'sb01101000,32'sb01111100,32'sb010010000,32'sb010010110,32'sb010011100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb011) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=14,BUS_DIN_NUM=53,BUS_CTRL_NUM=162,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb011,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011101,32'sb011110,32'sb011111,32'sb0100000,32'sb0100001,32'sb0100010,32'sb0101010,32'sb0110010,32'sb0110011,32'sb0110100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01001010,32'sb01010000,32'sb01010110,32'sb01011100,32'sb01100010,32'sb01101000,32'sb01111100,32'sb010010000,32'sb010010110,32'sb010011100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=184)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=184)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=14,BUS_DIN_NUM=53,BUS_CTRL_NUM=162,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb011,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011101,32'sb011110,32'sb011111,32'sb0100000,32'sb0100001,32'sb0100010,32'sb0101010,32'sb0110010,32'sb0110011,32'sb0110100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01001010,32'sb01010000,32'sb01010110,32'sb01011100,32'sb01100010,32'sb01101000,32'sb01111100,32'sb010010000,32'sb010010110,32'sb010011100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=14,BUS_DIN_NUM=53,BUS_CTRL_NUM=162,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb011,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011101,32'sb011110,32'sb011111,32'sb0100000,32'sb0100001,32'sb0100010,32'sb0101010,32'sb0110010,32'sb0110011,32'sb0110100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01001010,32'sb01010000,32'sb01010110,32'sb01011100,32'sb01100010,32'sb01101000,32'sb01111100,32'sb010010000,32'sb010010110,32'sb010011100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb011)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 23102/56 useful/useless nets, 19657/36 useful/useless insts
SYN-1016 : Merged 65 instances.
SYN-1032 : 22618/32 useful/useless nets, 20174/28 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 6 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 6 mux instances.
SYN-1015 : Optimize round 1, 540 better
SYN-1014 : Optimize round 2
SYN-1032 : 22137/90 useful/useless nets, 19693/96 useful/useless insts
SYN-1015 : Optimize round 2, 192 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.451147s wall, 2.421875s user + 0.031250s system = 2.453125s CPU (100.1%)

RUN-1004 : used memory is 331 MB, reserved memory is 298 MB, peak memory is 333 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22197/373 useful/useless nets, 19800/58 useful/useless insts
SYN-1016 : Merged 57 instances.
SYN-2571 : Optimize after map_dsp, round 1, 488 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 46 instances.
SYN-2501 : Optimize round 1, 93 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 22 macro adder
SYN-1019 : Optimized 25 mux instances.
SYN-1016 : Merged 18 instances.
SYN-1032 : 22716/5 useful/useless nets, 20319/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83135, tnet num: 22716, tinst num: 20318, tnode num: 116541, tedge num: 129660.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.137833s wall, 1.093750s user + 0.031250s system = 1.125000s CPU (98.9%)

RUN-1004 : used memory is 473 MB, reserved memory is 441 MB, peak memory is 473 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22716 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 291 (3.50), #lev = 6 (1.86)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 291 (3.50), #lev = 6 (1.86)
SYN-3001 : Logic optimization runtime opt =   0.04 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 763 instances into 291 LUTs, name keeping = 72%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 543 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 142 adder to BLE ...
SYN-4008 : Packed 142 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.368403s wall, 4.250000s user + 0.109375s system = 4.359375s CPU (99.8%)

RUN-1004 : used memory is 367 MB, reserved memory is 351 MB, peak memory is 581 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.139084s wall, 6.968750s user + 0.171875s system = 7.140625s CPU (100.0%)

RUN-1004 : used memory is 368 MB, reserved memory is 351 MB, peak memory is 581 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/uart_com2/AGRIC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/uart_com2/AGRIC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/uart_com2/AGRIC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/uart_com2/AGRIC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/uart_com2/AGRIC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/uart_com2/AGRIC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/uart_com2/AGRIC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/uart_com2/AGRIC_data[0]
SYN-5055 WARNING: The kept net COM3/rmc_com3/GPRMC_end will be merged to another kept net COM3/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM3/rmc_com3/GPRMC_sta will be merged to another kept net COM3/GNRMC/GPRMC_sta
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (381 clock/control pins, 0 other pins).
SYN-4027 : Net COM3/GNRMC/u_fifo/wr_to_rd_cross_inst/secondary_clk_i is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net COM3/GNRMC/u_fifo/wr_to_rd_cross_inst/secondary_clk_i as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19387 instances
RUN-0007 : 5517 luts, 12291 seqs, 962 mslices, 502 lslices, 60 pads, 50 brams, 0 dsps
RUN-1001 : There are total 21808 nets
RUN-1001 : 16317 nets have 2 pins
RUN-1001 : 4307 nets have [3 - 5] pins
RUN-1001 : 815 nets have [6 - 10] pins
RUN-1001 : 245 nets have [11 - 20] pins
RUN-1001 : 102 nets have [21 - 99] pins
RUN-1001 : 22 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4769     
RUN-1001 :   No   |  No   |  Yes  |     748     
RUN-1001 :   No   |  Yes  |  No   |     85      
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     555     
RUN-1001 :   Yes  |  Yes  |  No   |     51      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 124
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19385 instances, 5517 luts, 12291 seqs, 1464 slices, 293 macros(1464 instances: 962 mslices 502 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 63%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81062, tnet num: 21806, tinst num: 19385, tnode num: 114407, tedge num: 127622.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.139154s wall, 1.109375s user + 0.031250s system = 1.140625s CPU (100.1%)

RUN-1004 : used memory is 531 MB, reserved memory is 503 MB, peak memory is 581 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21806 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.942704s wall, 1.906250s user + 0.031250s system = 1.937500s CPU (99.7%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.74013e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19385.
PHY-3001 : Level 1 #clusters 2164.
PHY-3001 : End clustering;  0.131473s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (166.4%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 63%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 854747, overlap = 719.531
PHY-3002 : Step(2): len = 786524, overlap = 750.938
PHY-3002 : Step(3): len = 514839, overlap = 925.406
PHY-3002 : Step(4): len = 442976, overlap = 994.281
PHY-3002 : Step(5): len = 357596, overlap = 1107.69
PHY-3002 : Step(6): len = 319833, overlap = 1148.09
PHY-3002 : Step(7): len = 267735, overlap = 1204.88
PHY-3002 : Step(8): len = 236479, overlap = 1237.09
PHY-3002 : Step(9): len = 206809, overlap = 1302.78
PHY-3002 : Step(10): len = 192508, overlap = 1335.38
PHY-3002 : Step(11): len = 176151, overlap = 1345.47
PHY-3002 : Step(12): len = 163099, overlap = 1366.69
PHY-3002 : Step(13): len = 152351, overlap = 1370.47
PHY-3002 : Step(14): len = 141114, overlap = 1405.28
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.03691e-06
PHY-3002 : Step(15): len = 142650, overlap = 1397
PHY-3002 : Step(16): len = 177114, overlap = 1329.94
PHY-3002 : Step(17): len = 193539, overlap = 1246.81
PHY-3002 : Step(18): len = 197697, overlap = 1178.81
PHY-3002 : Step(19): len = 195885, overlap = 1153.28
PHY-3002 : Step(20): len = 188356, overlap = 1135.28
PHY-3002 : Step(21): len = 183347, overlap = 1120.69
PHY-3002 : Step(22): len = 178584, overlap = 1113.75
PHY-3002 : Step(23): len = 175338, overlap = 1121.22
PHY-3002 : Step(24): len = 171145, overlap = 1145.09
PHY-3002 : Step(25): len = 168357, overlap = 1141.38
PHY-3002 : Step(26): len = 166041, overlap = 1133.06
PHY-3002 : Step(27): len = 164738, overlap = 1134.34
PHY-3002 : Step(28): len = 163439, overlap = 1136.41
PHY-3002 : Step(29): len = 161756, overlap = 1134.88
PHY-3002 : Step(30): len = 160282, overlap = 1143.44
PHY-3002 : Step(31): len = 158937, overlap = 1142.47
PHY-3002 : Step(32): len = 157269, overlap = 1142.88
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.07383e-06
PHY-3002 : Step(33): len = 163995, overlap = 1120.44
PHY-3002 : Step(34): len = 176635, overlap = 1069.09
PHY-3002 : Step(35): len = 180060, overlap = 1026.72
PHY-3002 : Step(36): len = 182292, overlap = 1010.5
PHY-3002 : Step(37): len = 182684, overlap = 1010.88
PHY-3002 : Step(38): len = 182634, overlap = 1013.25
PHY-3002 : Step(39): len = 181439, overlap = 1016.47
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.14766e-06
PHY-3002 : Step(40): len = 192255, overlap = 991.375
PHY-3002 : Step(41): len = 207460, overlap = 901.031
PHY-3002 : Step(42): len = 213210, overlap = 843.938
PHY-3002 : Step(43): len = 214871, overlap = 781.188
PHY-3002 : Step(44): len = 215192, overlap = 753.469
PHY-3002 : Step(45): len = 214021, overlap = 744.469
PHY-3002 : Step(46): len = 212759, overlap = 733.188
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 8.29531e-06
PHY-3002 : Step(47): len = 225139, overlap = 673.406
PHY-3002 : Step(48): len = 240624, overlap = 554.031
PHY-3002 : Step(49): len = 246964, overlap = 524.938
PHY-3002 : Step(50): len = 249111, overlap = 520.094
PHY-3002 : Step(51): len = 248355, overlap = 515.125
PHY-3002 : Step(52): len = 246255, overlap = 511
PHY-3002 : Step(53): len = 244668, overlap = 490.875
PHY-3002 : Step(54): len = 243210, overlap = 498.625
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.65906e-05
PHY-3002 : Step(55): len = 257472, overlap = 475.625
PHY-3002 : Step(56): len = 273104, overlap = 406.656
PHY-3002 : Step(57): len = 278888, overlap = 386.719
PHY-3002 : Step(58): len = 281227, overlap = 358.188
PHY-3002 : Step(59): len = 279640, overlap = 367.5
PHY-3002 : Step(60): len = 275676, overlap = 383.688
PHY-3002 : Step(61): len = 272391, overlap = 397.188
PHY-3002 : Step(62): len = 271470, overlap = 400.719
PHY-3002 : Step(63): len = 270723, overlap = 415.094
PHY-3002 : Step(64): len = 269510, overlap = 427
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.31812e-05
PHY-3002 : Step(65): len = 279814, overlap = 379.562
PHY-3002 : Step(66): len = 290178, overlap = 357.25
PHY-3002 : Step(67): len = 293379, overlap = 365.438
PHY-3002 : Step(68): len = 295739, overlap = 356.156
PHY-3002 : Step(69): len = 293699, overlap = 345.156
PHY-3002 : Step(70): len = 291929, overlap = 346.5
PHY-3002 : Step(71): len = 289619, overlap = 351.469
PHY-3002 : Step(72): len = 289273, overlap = 366.312
PHY-3002 : Step(73): len = 287461, overlap = 360
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 6.63625e-05
PHY-3002 : Step(74): len = 297301, overlap = 337.094
PHY-3002 : Step(75): len = 305685, overlap = 304.344
PHY-3002 : Step(76): len = 308114, overlap = 303.438
PHY-3002 : Step(77): len = 309979, overlap = 281.969
PHY-3002 : Step(78): len = 308707, overlap = 283.625
PHY-3002 : Step(79): len = 307633, overlap = 286.094
PHY-3002 : Step(80): len = 305152, overlap = 279.969
PHY-3002 : Step(81): len = 305772, overlap = 277.062
PHY-3002 : Step(82): len = 304533, overlap = 275.875
PHY-3002 : Step(83): len = 304994, overlap = 297.656
PHY-3002 : Step(84): len = 306150, overlap = 272.812
PHY-3002 : Step(85): len = 308083, overlap = 271.656
PHY-3002 : Step(86): len = 306329, overlap = 279.469
PHY-3002 : Step(87): len = 305401, overlap = 272.719
PHY-3002 : Step(88): len = 304312, overlap = 275.375
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000127803
PHY-3002 : Step(89): len = 310123, overlap = 261.031
PHY-3002 : Step(90): len = 314411, overlap = 260.125
PHY-3002 : Step(91): len = 315813, overlap = 258.719
PHY-3002 : Step(92): len = 317275, overlap = 264.188
PHY-3002 : Step(93): len = 316819, overlap = 249.969
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000243276
PHY-3002 : Step(94): len = 319975, overlap = 244.094
PHY-3002 : Step(95): len = 326759, overlap = 239.688
PHY-3002 : Step(96): len = 328975, overlap = 230.562
PHY-3002 : Step(97): len = 329386, overlap = 236.812
PHY-3002 : Step(98): len = 328547, overlap = 231.156
PHY-3002 : Step(99): len = 327617, overlap = 226.406
PHY-3002 : Step(100): len = 327699, overlap = 231.438
PHY-3002 : Step(101): len = 326970, overlap = 231.312
PHY-3002 : Step(102): len = 327668, overlap = 223.875
PHY-3002 : Step(103): len = 327846, overlap = 223.125
PHY-3002 : Step(104): len = 328202, overlap = 206.938
PHY-3002 : Step(105): len = 328247, overlap = 197.844
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.013867s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21808.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 436360, over cnt = 1179(3%), over = 5265, worst = 32
PHY-1001 : End global iterations;  0.810465s wall, 1.062500s user + 0.078125s system = 1.140625s CPU (140.7%)

PHY-1001 : Congestion index: top1 = 76.01, top5 = 53.43, top10 = 43.77, top15 = 37.89.
PHY-3001 : End congestion estimation;  1.023859s wall, 1.265625s user + 0.078125s system = 1.343750s CPU (131.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21806 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.883825s wall, 0.859375s user + 0.031250s system = 0.890625s CPU (100.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.04028e-05
PHY-3002 : Step(106): len = 368229, overlap = 154.094
PHY-3002 : Step(107): len = 381305, overlap = 142.438
PHY-3002 : Step(108): len = 385135, overlap = 135.094
PHY-3002 : Step(109): len = 386582, overlap = 127.719
PHY-3002 : Step(110): len = 390760, overlap = 124.25
PHY-3002 : Step(111): len = 393841, overlap = 121
PHY-3002 : Step(112): len = 401995, overlap = 122.406
PHY-3002 : Step(113): len = 406167, overlap = 125.844
PHY-3002 : Step(114): len = 407215, overlap = 124.219
PHY-3002 : Step(115): len = 410514, overlap = 128.062
PHY-3002 : Step(116): len = 412485, overlap = 132.531
PHY-3002 : Step(117): len = 415719, overlap = 135.625
PHY-3002 : Step(118): len = 417341, overlap = 142.125
PHY-3002 : Step(119): len = 418647, overlap = 140.438
PHY-3002 : Step(120): len = 422039, overlap = 144.688
PHY-3002 : Step(121): len = 423617, overlap = 151.438
PHY-3002 : Step(122): len = 424754, overlap = 153.438
PHY-3002 : Step(123): len = 427350, overlap = 156.562
PHY-3002 : Step(124): len = 428518, overlap = 160.75
PHY-3002 : Step(125): len = 429956, overlap = 165.125
PHY-3002 : Step(126): len = 430102, overlap = 165.688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000180806
PHY-3002 : Step(127): len = 430428, overlap = 168.062
PHY-3002 : Step(128): len = 431538, overlap = 169.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000296928
PHY-3002 : Step(129): len = 434394, overlap = 164.188
PHY-3002 : Step(130): len = 440681, overlap = 160.094
PHY-3002 : Step(131): len = 446118, overlap = 159.812
PHY-3002 : Step(132): len = 447829, overlap = 157.812
PHY-3002 : Step(133): len = 450722, overlap = 159.781
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000593856
PHY-3002 : Step(134): len = 450397, overlap = 154.938
PHY-3002 : Step(135): len = 454615, overlap = 152.312
PHY-3002 : Step(136): len = 461723, overlap = 140.875
PHY-3002 : Step(137): len = 467208, overlap = 139
PHY-3002 : Step(138): len = 466608, overlap = 136.031
PHY-3002 : Step(139): len = 468319, overlap = 131.562
PHY-3002 : Step(140): len = 472923, overlap = 127.562
PHY-3002 : Step(141): len = 476666, overlap = 123.812
PHY-3002 : Step(142): len = 476328, overlap = 126.75
PHY-3002 : Step(143): len = 475611, overlap = 117.312
PHY-3002 : Step(144): len = 479862, overlap = 120.062
PHY-3002 : Step(145): len = 482306, overlap = 114.281
PHY-3002 : Step(146): len = 479766, overlap = 116.375
PHY-3002 : Step(147): len = 478861, overlap = 115.375
PHY-3002 : Step(148): len = 479800, overlap = 107.344
PHY-3002 : Step(149): len = 480152, overlap = 109.219
PHY-3002 : Step(150): len = 478864, overlap = 112.625
PHY-3002 : Step(151): len = 477456, overlap = 116.406
PHY-3002 : Step(152): len = 476569, overlap = 110.594
PHY-3002 : Step(153): len = 475887, overlap = 108.188
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(154): len = 475632, overlap = 109.75
PHY-3002 : Step(155): len = 476809, overlap = 114.969
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 57/21808.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 549192, over cnt = 2181(6%), over = 9759, worst = 38
PHY-1001 : End global iterations;  1.069275s wall, 1.640625s user + 0.046875s system = 1.687500s CPU (157.8%)

PHY-1001 : Congestion index: top1 = 80.24, top5 = 58.72, top10 = 49.68, top15 = 44.56.
PHY-3001 : End congestion estimation;  1.320269s wall, 1.890625s user + 0.046875s system = 1.937500s CPU (146.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21806 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.873037s wall, 0.875000s user + 0.000000s system = 0.875000s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00011106
PHY-3002 : Step(156): len = 490396, overlap = 324.5
PHY-3002 : Step(157): len = 500006, overlap = 266.562
PHY-3002 : Step(158): len = 490390, overlap = 261.312
PHY-3002 : Step(159): len = 483128, overlap = 258.312
PHY-3002 : Step(160): len = 481346, overlap = 245.406
PHY-3002 : Step(161): len = 478843, overlap = 226.062
PHY-3002 : Step(162): len = 476479, overlap = 216.906
PHY-3002 : Step(163): len = 475741, overlap = 214.75
PHY-3002 : Step(164): len = 471747, overlap = 219.219
PHY-3002 : Step(165): len = 469413, overlap = 219.688
PHY-3002 : Step(166): len = 467248, overlap = 216.906
PHY-3002 : Step(167): len = 464179, overlap = 211.062
PHY-3002 : Step(168): len = 462957, overlap = 212.094
PHY-3002 : Step(169): len = 461314, overlap = 210.125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00022212
PHY-3002 : Step(170): len = 460362, overlap = 203.281
PHY-3002 : Step(171): len = 461912, overlap = 193
PHY-3002 : Step(172): len = 464150, overlap = 180.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00044424
PHY-3002 : Step(173): len = 465581, overlap = 175.719
PHY-3002 : Step(174): len = 470738, overlap = 164.156
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000743569
PHY-3002 : Step(175): len = 472491, overlap = 164.781
PHY-3002 : Step(176): len = 479090, overlap = 152.844
PHY-3002 : Step(177): len = 485272, overlap = 150.406
PHY-3002 : Step(178): len = 486761, overlap = 142.031
PHY-3002 : Step(179): len = 485470, overlap = 139.969
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00140537
PHY-3002 : Step(180): len = 486732, overlap = 134.531
PHY-3002 : Step(181): len = 491155, overlap = 133.344
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81062, tnet num: 21806, tinst num: 19385, tnode num: 114407, tedge num: 127622.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.402795s wall, 1.343750s user + 0.062500s system = 1.406250s CPU (100.2%)

RUN-1004 : used memory is 571 MB, reserved memory is 544 MB, peak memory is 703 MB
OPT-1001 : Total overflow 503.00 peak overflow 3.22
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 510/21808.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 579304, over cnt = 2555(7%), over = 8704, worst = 25
PHY-1001 : End global iterations;  1.283877s wall, 1.875000s user + 0.062500s system = 1.937500s CPU (150.9%)

PHY-1001 : Congestion index: top1 = 59.83, top5 = 48.77, top10 = 43.90, top15 = 40.82.
PHY-1001 : End incremental global routing;  1.501732s wall, 2.093750s user + 0.062500s system = 2.156250s CPU (143.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21806 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.928237s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (99.3%)

OPT-1001 : 15 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19307 has valid locations, 242 needs to be replaced
PHY-3001 : design contains 19612 instances, 5603 luts, 12432 seqs, 1464 slices, 293 macros(1464 instances: 962 mslices 502 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 505995
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17439/22035.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 589560, over cnt = 2568(7%), over = 8742, worst = 25
PHY-1001 : End global iterations;  0.177226s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (97.0%)

PHY-1001 : Congestion index: top1 = 59.91, top5 = 48.77, top10 = 43.98, top15 = 40.95.
PHY-3001 : End congestion estimation;  0.409741s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (99.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81804, tnet num: 22033, tinst num: 19612, tnode num: 115492, tedge num: 128652.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.410848s wall, 1.406250s user + 0.000000s system = 1.406250s CPU (99.7%)

RUN-1004 : used memory is 614 MB, reserved memory is 606 MB, peak memory is 705 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22033 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.337898s wall, 2.328125s user + 0.015625s system = 2.343750s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(182): len = 506324, overlap = 0.875
PHY-3002 : Step(183): len = 507541, overlap = 0.8125
PHY-3002 : Step(184): len = 508001, overlap = 1
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17474/22035.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 590408, over cnt = 2578(7%), over = 8795, worst = 25
PHY-1001 : End global iterations;  0.157677s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (148.6%)

PHY-1001 : Congestion index: top1 = 60.02, top5 = 49.09, top10 = 44.23, top15 = 41.22.
PHY-3001 : End congestion estimation;  0.430840s wall, 0.484375s user + 0.031250s system = 0.515625s CPU (119.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22033 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.894766s wall, 0.890625s user + 0.000000s system = 0.890625s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000844827
PHY-3002 : Step(185): len = 507937, overlap = 137.438
PHY-3002 : Step(186): len = 508227, overlap = 136.469
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00168965
PHY-3002 : Step(187): len = 508626, overlap = 137.094
PHY-3002 : Step(188): len = 508979, overlap = 136.781
PHY-3001 : Final: Len = 508979, Over = 136.781
PHY-3001 : End incremental placement;  4.883101s wall, 5.171875s user + 0.281250s system = 5.453125s CPU (111.7%)

OPT-1001 : Total overflow 507.47 peak overflow 3.22
OPT-1001 : End high-fanout net optimization;  7.766157s wall, 8.812500s user + 0.343750s system = 9.156250s CPU (117.9%)

OPT-1001 : Current memory(MB): used = 708, reserve = 688, peak = 725.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17480/22035.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 592760, over cnt = 2537(7%), over = 8299, worst = 25
PHY-1002 : len = 636192, over cnt = 1807(5%), over = 4242, worst = 16
PHY-1002 : len = 674104, over cnt = 676(1%), over = 1405, worst = 15
PHY-1002 : len = 693984, over cnt = 79(0%), over = 184, worst = 13
PHY-1002 : len = 697848, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.157373s wall, 1.812500s user + 0.015625s system = 1.828125s CPU (158.0%)

PHY-1001 : Congestion index: top1 = 50.73, top5 = 44.65, top10 = 41.40, top15 = 39.25.
OPT-1001 : End congestion update;  1.383272s wall, 2.031250s user + 0.015625s system = 2.046875s CPU (148.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22033 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.784951s wall, 0.781250s user + 0.015625s system = 0.796875s CPU (101.5%)

OPT-0007 : Start: WNS 3769 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.173836s wall, 2.812500s user + 0.031250s system = 2.843750s CPU (130.8%)

OPT-1001 : Current memory(MB): used = 683, reserve = 661, peak = 725.
OPT-1001 : End physical optimization;  11.626754s wall, 13.328125s user + 0.437500s system = 13.765625s CPU (118.4%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5603 LUT to BLE ...
SYN-4008 : Packed 5603 LUT and 2673 SEQ to BLE.
SYN-4003 : Packing 9759 remaining SEQ's ...
SYN-4005 : Packed 3402 SEQ with LUT/SLICE
SYN-4006 : 52 single LUT's are left
SYN-4006 : 6357 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11960/13785 primitive instances ...
PHY-3001 : End packing;  2.581928s wall, 2.578125s user + 0.000000s system = 2.578125s CPU (99.9%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8153 instances
RUN-1001 : 4019 mslices, 4019 lslices, 60 pads, 50 brams, 0 dsps
RUN-1001 : There are total 19431 nets
RUN-1001 : 13596 nets have 2 pins
RUN-1001 : 4441 nets have [3 - 5] pins
RUN-1001 : 876 nets have [6 - 10] pins
RUN-1001 : 382 nets have [11 - 20] pins
RUN-1001 : 125 nets have [21 - 99] pins
RUN-1001 : 11 nets have 100+ pins
PHY-3001 : design contains 8151 instances, 8038 slices, 293 macros(1464 instances: 962 mslices 502 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 526044, Over = 378.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7803/19431.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 660568, over cnt = 1598(4%), over = 2533, worst = 9
PHY-1002 : len = 666392, over cnt = 1085(3%), over = 1520, worst = 8
PHY-1002 : len = 677984, over cnt = 462(1%), over = 615, worst = 8
PHY-1002 : len = 686144, over cnt = 80(0%), over = 101, worst = 6
PHY-1002 : len = 688200, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.079571s wall, 1.640625s user + 0.031250s system = 1.671875s CPU (154.9%)

PHY-1001 : Congestion index: top1 = 52.03, top5 = 45.10, top10 = 41.42, top15 = 39.08.
PHY-3001 : End congestion estimation;  1.372753s wall, 1.921875s user + 0.031250s system = 1.953125s CPU (142.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67889, tnet num: 19429, tinst num: 8151, tnode num: 92423, tedge num: 111927.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.614236s wall, 1.578125s user + 0.031250s system = 1.609375s CPU (99.7%)

RUN-1004 : used memory is 607 MB, reserved memory is 596 MB, peak memory is 725 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19429 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.422334s wall, 2.375000s user + 0.062500s system = 2.437500s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.00637e-05
PHY-3002 : Step(189): len = 525841, overlap = 357
PHY-3002 : Step(190): len = 525080, overlap = 362.25
PHY-3002 : Step(191): len = 526066, overlap = 379.75
PHY-3002 : Step(192): len = 527231, overlap = 395.25
PHY-3002 : Step(193): len = 526018, overlap = 402
PHY-3002 : Step(194): len = 523728, overlap = 396.25
PHY-3002 : Step(195): len = 521811, overlap = 388.25
PHY-3002 : Step(196): len = 520677, overlap = 398
PHY-3002 : Step(197): len = 519355, overlap = 392
PHY-3002 : Step(198): len = 518364, overlap = 394.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000100127
PHY-3002 : Step(199): len = 522208, overlap = 386.25
PHY-3002 : Step(200): len = 526043, overlap = 371.25
PHY-3002 : Step(201): len = 525707, overlap = 367
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000200255
PHY-3002 : Step(202): len = 532194, overlap = 353.5
PHY-3002 : Step(203): len = 541782, overlap = 342.25
PHY-3002 : Step(204): len = 540652, overlap = 336.75
PHY-3002 : Step(205): len = 540247, overlap = 334.5
PHY-3002 : Step(206): len = 539828, overlap = 330.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.635508s wall, 0.734375s user + 0.734375s system = 1.468750s CPU (231.1%)

PHY-3001 : Trial Legalized: Len = 645450
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 607/19431.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 738192, over cnt = 2382(6%), over = 3795, worst = 8
PHY-1002 : len = 751056, over cnt = 1463(4%), over = 2072, worst = 6
PHY-1002 : len = 770136, over cnt = 516(1%), over = 654, worst = 6
PHY-1002 : len = 779704, over cnt = 83(0%), over = 106, worst = 4
PHY-1002 : len = 781728, over cnt = 5(0%), over = 5, worst = 1
PHY-1001 : End global iterations;  1.793439s wall, 3.171875s user + 0.046875s system = 3.218750s CPU (179.5%)

PHY-1001 : Congestion index: top1 = 51.75, top5 = 46.38, top10 = 43.26, top15 = 41.42.
PHY-3001 : End congestion estimation;  2.124801s wall, 3.500000s user + 0.046875s system = 3.546875s CPU (166.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19429 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.849750s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (99.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000174144
PHY-3002 : Step(207): len = 603132, overlap = 73
PHY-3002 : Step(208): len = 587844, overlap = 112.75
PHY-3002 : Step(209): len = 577779, overlap = 150.75
PHY-3002 : Step(210): len = 571422, overlap = 186.75
PHY-3002 : Step(211): len = 568158, overlap = 214.75
PHY-3002 : Step(212): len = 565753, overlap = 233.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000348288
PHY-3002 : Step(213): len = 568870, overlap = 226.5
PHY-3002 : Step(214): len = 573893, overlap = 220.5
PHY-3002 : Step(215): len = 577280, overlap = 225.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(216): len = 578917, overlap = 221.25
PHY-3002 : Step(217): len = 584288, overlap = 218
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.029868s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (104.6%)

PHY-3001 : Legalized: Len = 622338, Over = 0
PHY-3001 : Spreading special nets. 56 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.078256s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (99.8%)

PHY-3001 : 85 instances has been re-located, deltaX = 26, deltaY = 43, maxDist = 2.
PHY-3001 : Final: Len = 623968, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67889, tnet num: 19429, tinst num: 8151, tnode num: 92423, tedge num: 111927.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.890106s wall, 1.859375s user + 0.031250s system = 1.890625s CPU (100.0%)

RUN-1004 : used memory is 615 MB, reserved memory is 614 MB, peak memory is 725 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3972/19431.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 728384, over cnt = 2186(6%), over = 3326, worst = 6
PHY-1002 : len = 738168, over cnt = 1394(3%), over = 1895, worst = 5
PHY-1002 : len = 757200, over cnt = 333(0%), over = 435, worst = 5
PHY-1002 : len = 762616, over cnt = 103(0%), over = 119, worst = 3
PHY-1002 : len = 764776, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.602773s wall, 2.781250s user + 0.093750s system = 2.875000s CPU (179.4%)

PHY-1001 : Congestion index: top1 = 48.47, top5 = 43.75, top10 = 41.31, top15 = 39.58.
PHY-1001 : End incremental global routing;  1.894563s wall, 3.078125s user + 0.093750s system = 3.171875s CPU (167.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19429 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.867327s wall, 0.843750s user + 0.031250s system = 0.875000s CPU (100.9%)

OPT-1001 : 3 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8085 has valid locations, 20 needs to be replaced
PHY-3001 : design contains 8168 instances, 8055 slices, 293 macros(1464 instances: 962 mslices 502 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 627332
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17564/19453.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 768528, over cnt = 47(0%), over = 61, worst = 3
PHY-1002 : len = 768552, over cnt = 31(0%), over = 33, worst = 3
PHY-1002 : len = 768808, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 768920, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.561110s wall, 0.593750s user + 0.000000s system = 0.593750s CPU (105.8%)

PHY-1001 : Congestion index: top1 = 48.49, top5 = 43.87, top10 = 41.43, top15 = 39.70.
PHY-3001 : End congestion estimation;  0.854546s wall, 0.890625s user + 0.000000s system = 0.890625s CPU (104.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68028, tnet num: 19451, tinst num: 8168, tnode num: 92596, tedge num: 112103.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.834638s wall, 1.828125s user + 0.000000s system = 1.828125s CPU (99.6%)

RUN-1004 : used memory is 648 MB, reserved memory is 637 MB, peak memory is 725 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19451 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.763481s wall, 2.750000s user + 0.015625s system = 2.765625s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(218): len = 627000, overlap = 1.5
PHY-3002 : Step(219): len = 626768, overlap = 1.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17558/19453.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 768080, over cnt = 40(0%), over = 46, worst = 3
PHY-1002 : len = 768128, over cnt = 21(0%), over = 24, worst = 3
PHY-1002 : len = 768304, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 768432, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 768488, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.687214s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (104.6%)

PHY-1001 : Congestion index: top1 = 48.73, top5 = 44.04, top10 = 41.53, top15 = 39.79.
PHY-3001 : End congestion estimation;  0.975801s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (104.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19451 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.836884s wall, 0.812500s user + 0.015625s system = 0.828125s CPU (99.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000756298
PHY-3002 : Step(220): len = 626757, overlap = 3
PHY-3002 : Step(221): len = 626749, overlap = 2.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006608s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 626828, Over = 0
PHY-3001 : End spreading;  0.064285s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.2%)

PHY-3001 : Final: Len = 626828, Over = 0
PHY-3001 : End incremental placement;  6.001014s wall, 6.203125s user + 0.062500s system = 6.265625s CPU (104.4%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  9.222347s wall, 10.578125s user + 0.187500s system = 10.765625s CPU (116.7%)

OPT-1001 : Current memory(MB): used = 720, reserve = 706, peak = 726.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17559/19453.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 768256, over cnt = 30(0%), over = 37, worst = 4
PHY-1002 : len = 768272, over cnt = 23(0%), over = 26, worst = 3
PHY-1002 : len = 768464, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 768560, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 768592, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.670867s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (107.1%)

PHY-1001 : Congestion index: top1 = 48.73, top5 = 43.96, top10 = 41.49, top15 = 39.77.
OPT-1001 : End congestion update;  0.943879s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (105.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19451 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.815452s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (99.6%)

OPT-0007 : Start: WNS 3820 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.763410s wall, 1.812500s user + 0.000000s system = 1.812500s CPU (102.8%)

OPT-1001 : Current memory(MB): used = 720, reserve = 706, peak = 726.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19451 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.701342s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (100.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17582/19453.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 768592, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.114384s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (95.6%)

PHY-1001 : Congestion index: top1 = 48.73, top5 = 43.96, top10 = 41.49, top15 = 39.77.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19451 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.705796s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (101.8%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3820 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 48.241379
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3820ps with logic level 4 
RUN-1001 :       #2 path slack 3844ps with logic level 4 
RUN-1001 :       #3 path slack 3855ps with logic level 1 
OPT-1001 : End physical optimization;  14.924253s wall, 16.281250s user + 0.234375s system = 16.515625s CPU (110.7%)

RUN-1003 : finish command "place" in  72.611788s wall, 121.625000s user + 7.562500s system = 129.187500s CPU (177.9%)

RUN-1004 : used memory is 603 MB, reserved memory is 579 MB, peak memory is 726 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.537218s wall, 2.656250s user + 0.015625s system = 2.671875s CPU (173.8%)

RUN-1004 : used memory is 603 MB, reserved memory is 580 MB, peak memory is 726 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8170 instances
RUN-1001 : 4028 mslices, 4027 lslices, 60 pads, 50 brams, 0 dsps
RUN-1001 : There are total 19453 nets
RUN-1001 : 13602 nets have 2 pins
RUN-1001 : 4438 nets have [3 - 5] pins
RUN-1001 : 888 nets have [6 - 10] pins
RUN-1001 : 387 nets have [11 - 20] pins
RUN-1001 : 127 nets have [21 - 99] pins
RUN-1001 : 11 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68028, tnet num: 19451, tinst num: 8168, tnode num: 92596, tedge num: 112103.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.613905s wall, 1.609375s user + 0.000000s system = 1.609375s CPU (99.7%)

RUN-1004 : used memory is 588 MB, reserved memory is 563 MB, peak memory is 726 MB
PHY-1001 : 4028 mslices, 4027 lslices, 60 pads, 50 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19451 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 708904, over cnt = 2339(6%), over = 3809, worst = 8
PHY-1002 : len = 724568, over cnt = 1448(4%), over = 2050, worst = 7
PHY-1002 : len = 740320, over cnt = 630(1%), over = 846, worst = 7
PHY-1002 : len = 754320, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 754480, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.599126s wall, 2.687500s user + 0.000000s system = 2.687500s CPU (168.1%)

PHY-1001 : Congestion index: top1 = 47.93, top5 = 43.62, top10 = 41.05, top15 = 39.32.
PHY-1001 : End global routing;  1.911078s wall, 3.000000s user + 0.000000s system = 3.000000s CPU (157.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 702, reserve = 690, peak = 726.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net COM3/GNRMC/u_fifo/wr_to_rd_cross_inst/secondary_clk_i will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 971, reserve = 958, peak = 971.
PHY-1001 : End build detailed router design. 4.213829s wall, 4.187500s user + 0.015625s system = 4.203125s CPU (99.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 193584, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.765179s wall, 0.750000s user + 0.015625s system = 0.765625s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1007, reserve = 995, peak = 1007.
PHY-1001 : End phase 1; 0.772454s wall, 0.765625s user + 0.015625s system = 0.781250s CPU (101.1%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.80545e+06, over cnt = 1212(0%), over = 1213, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1022, reserve = 1010, peak = 1022.
PHY-1001 : End initial routed; 20.703965s wall, 50.125000s user + 0.468750s system = 50.593750s CPU (244.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18220(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.471   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.150393s wall, 3.156250s user + 0.000000s system = 3.156250s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 1026, reserve = 1013, peak = 1026.
PHY-1001 : End phase 2; 23.854488s wall, 53.281250s user + 0.468750s system = 53.750000s CPU (225.3%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.80545e+06, over cnt = 1212(0%), over = 1213, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.206951s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (98.2%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.79571e+06, over cnt = 401(0%), over = 401, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.560793s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (167.2%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.79572e+06, over cnt = 49(0%), over = 49, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.301079s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (145.3%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.79621e+06, over cnt = 11(0%), over = 11, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.185098s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (101.3%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.79646e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 4; 0.155496s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (100.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18220(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.240   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.222631s wall, 3.203125s user + 0.015625s system = 3.218750s CPU (99.9%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 300 feed throughs used by 269 nets
PHY-1001 : End commit to database; 2.094754s wall, 2.062500s user + 0.015625s system = 2.078125s CPU (99.2%)

PHY-1001 : Current memory(MB): used = 1129, reserve = 1120, peak = 1129.
PHY-1001 : End phase 3; 7.199829s wall, 7.671875s user + 0.031250s system = 7.703125s CPU (107.0%)

PHY-1003 : Routed, final wirelength = 1.79646e+06
PHY-1001 : Current memory(MB): used = 1133, reserve = 1125, peak = 1133.
PHY-1001 : End export database. 0.054527s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (86.0%)

PHY-1001 : End detail routing;  36.471485s wall, 66.359375s user + 0.531250s system = 66.890625s CPU (183.4%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68028, tnet num: 19451, tinst num: 8168, tnode num: 92596, tedge num: 112103.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.610230s wall, 1.609375s user + 0.000000s system = 1.609375s CPU (99.9%)

RUN-1004 : used memory is 1009 MB, reserved memory is 1020 MB, peak memory is 1133 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  44.003794s wall, 74.906250s user + 0.562500s system = 75.468750s CPU (171.5%)

RUN-1004 : used memory is 1043 MB, reserved memory is 1055 MB, peak memory is 1133 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8694   out of  19600   44.36%
#reg                    12551   out of  19600   64.04%
#le                     15001
  #lut only              2450   out of  15001   16.33%
  #reg only              6307   out of  15001   42.04%
  #lut&reg               6244   out of  15001   41.62%
#dsp                        0   out of     29    0.00%
#bram                      50   out of     64   78.12%
  #bram9k                  50
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                                                 Type               DriverType         Driver                    Fanout
#1        COM3/GNRMC/u_fifo/wr_to_rd_cross_inst/secondary_clk_i    GCLK               pll                CLK100M/pll_inst.clkc0    6844
#2        config_inst_syn_9                                        GCLK               config             config_inst.jtck          218
#3        clk_in_dup_1                                             GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         M3        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         P6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+------------------------------------------------------------------------------------------------------------------------------------------+
|Instance                             |Module                                         |le     |lut     |ripple  |seq     |bram    |dsp     |
+------------------------------------------------------------------------------------------------------------------------------------------+
|top                                  |INS600M_21A                                    |15001  |7230    |1464    |12595   |50      |0       |
|  AnyFog_dataX                       |AnyFog                                         |207    |84      |22      |173     |0       |0       |
|    UART_RX_COM3                     |UART_RX115200E                                 |86     |53      |22      |52      |0       |0       |
|  AnyFog_dataY                       |AnyFog                                         |208    |69      |22      |172     |0       |0       |
|    UART_RX_COM3                     |UART_RX115200E                                 |86     |53      |22      |50      |0       |0       |
|  AnyFog_dataZ                       |AnyFog                                         |207    |122     |22      |170     |0       |0       |
|    UART_RX_COM3                     |UART_RX115200E                                 |86     |60      |22      |49      |0       |0       |
|  CLK100M                            |global_clock                                   |0      |0       |0       |0       |0       |0       |
|  COM2                               |COM2_Control                                   |2847   |676     |34      |2774    |0       |0       |
|    PPPNAV_com2                      |PPPNAV                                         |225    |85      |5       |212     |0       |0       |
|    STADOP_com2                      |STADOP                                         |560    |121     |0       |554     |0       |0       |
|    UART_RX_COM3                     |UART_RX460800                                  |64     |46      |14      |41      |0       |0       |
|    head_com2                        |uniheading                                     |263    |112     |5       |251     |0       |0       |
|    uart_com2                        |Agrica                                         |1425   |293     |10      |1406    |0       |0       |
|  COM3                               |COM3_Control                                   |409    |202     |44      |332     |2       |0       |
|    GNRMC                            |GNRMC_Tx                                       |184    |92      |30      |143     |2       |0       |
|      u_fifo                         |Asys_fifo8x8                                   |120    |54      |25      |91      |1       |0       |
|        ram_inst                     |ram_infer_Asys_fifo8x8                         |0      |0       |0       |0       |1       |0       |
|        rd_to_wr_cross_inst          |fifo_cross_domain_addr_process_al_Asys_fifo8x8 |31     |13      |0       |31      |0       |0       |
|        wr_to_rd_cross_inst          |fifo_cross_domain_addr_process_al_Asys_fifo8x8 |33     |15      |0       |33      |0       |0       |
|    UART_RX_COM3                     |UART_RX460800                                  |61     |44      |14      |37      |0       |0       |
|    rmc_com3                         |Gprmc                                          |164    |66      |0       |152     |0       |0       |
|  DATA                               |Data_Processing                                |8666   |4301    |1056    |6990    |0       |0       |
|    DIV_Dtemp                        |Divider                                        |800    |313     |84      |675     |0       |0       |
|    DIV_Utemp                        |Divider                                        |617    |328     |84      |488     |0       |0       |
|    DIV_accX                         |Divider                                        |650    |319     |84      |530     |0       |0       |
|    DIV_accY                         |Divider                                        |672    |305     |105     |511     |0       |0       |
|    DIV_accZ                         |Divider                                        |651    |365     |132     |444     |0       |0       |
|    DIV_rateX                        |Divider                                        |604    |376     |132     |397     |0       |0       |
|    DIV_rateY                        |Divider                                        |604    |388     |132     |397     |0       |0       |
|    DIV_rateZ                        |Divider                                        |559    |367     |132     |353     |0       |0       |
|    genclk                           |genclk                                         |83     |55      |20      |50      |0       |0       |
|  FMC                                |FMC_Ctrl                                       |407    |353     |43      |328     |0       |0       |
|  IIC                                |I2C_master                                     |285    |219     |11      |266     |0       |0       |
|  IMU_CTRL                           |SCHA634                                        |872    |671     |61      |724     |0       |0       |
|    CtrlData                         |CtrlData                                       |466    |417     |47      |346     |0       |0       |
|      usms                           |Time_1ms                                       |28     |23      |5       |19      |0       |0       |
|    SPIM                             |SPI_SCHA634                                    |406    |254     |14      |378     |0       |0       |
|  POWER                              |POWER_EN                                       |98     |49      |38      |37      |0       |0       |
|  cw_top                             |CW_TOP_WRAPPER                                 |778    |484     |111     |568     |0       |0       |
|    wrapper_cwc_top                  |cwc_top                                        |778    |484     |111     |568     |0       |0       |
|      cfg_int_inst                   |cwc_cfg_int                                    |373    |208     |0       |356     |0       |0       |
|        reg_inst                     |register                                       |371    |206     |0       |354     |0       |0       |
|        tap_inst                     |tap                                            |2      |2       |0       |2       |0       |0       |
|      trigger_inst                   |trigger                                        |405    |276     |111     |212     |0       |0       |
|        bus_inst                     |bus_top                                        |177    |119     |54      |81      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes  |bus_det                                        |23     |13      |10      |7       |0       |0       |
|          BUS_DETECTOR[10]$bus_nodes |bus_det                                        |29     |15      |10      |12      |0       |0       |
|          BUS_DETECTOR[11]$bus_nodes |bus_det                                        |4      |4       |0       |4       |0       |0       |
|          BUS_DETECTOR[12]$bus_nodes |bus_det                                        |4      |4       |0       |4       |0       |0       |
|          BUS_DETECTOR[13]$bus_nodes |bus_det                                        |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes  |bus_det                                        |51     |33      |18      |18      |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes  |bus_det                                        |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes  |bus_det                                        |16     |10      |6       |8       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes  |bus_det                                        |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[5]$bus_nodes  |bus_det                                        |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[6]$bus_nodes  |bus_det                                        |3      |3       |0       |3       |0       |0       |
|          BUS_DETECTOR[7]$bus_nodes  |bus_det                                        |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[8]$bus_nodes  |bus_det                                        |14     |14      |0       |8       |0       |0       |
|          BUS_DETECTOR[9]$bus_nodes  |bus_det                                        |27     |17      |10      |11      |0       |0       |
|        emb_ctrl_inst                |emb_ctrl                                       |141    |100     |29      |92      |0       |0       |
+------------------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13541  
    #2          2       3476   
    #3          3        690   
    #4          4        272   
    #5        5-10       959   
    #6        11-50      426   
    #7       51-100      17    
    #8       101-500      5    
    #9        >500        2    
  Average     2.14             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  1.899730s wall, 3.281250s user + 0.015625s system = 3.296875s CPU (173.5%)

RUN-1004 : used memory is 1044 MB, reserved memory is 1056 MB, peak memory is 1133 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68028, tnet num: 19451, tinst num: 8168, tnode num: 92596, tedge num: 112103.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.592058s wall, 1.593750s user + 0.000000s system = 1.593750s CPU (100.1%)

RUN-1004 : used memory is 1046 MB, reserved memory is 1058 MB, peak memory is 1133 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19451 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.258196s wall, 1.265625s user + 0.000000s system = 1.265625s CPU (100.6%)

RUN-1004 : used memory is 1057 MB, reserved memory is 1065 MB, peak memory is 1133 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 1c20620e118e075ff35a1cd93778bfb6257150a0937144a4d9e00bcc01d776fb -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8168
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19453, pip num: 149255
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 300
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3248 valid insts, and 414792 bits set as '1'.
BIT-1004 : the usercode register value: 00000000100000011010011101000100
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  10.700067s wall, 105.562500s user + 0.156250s system = 105.718750s CPU (988.0%)

RUN-1004 : used memory is 1193 MB, reserved memory is 1178 MB, peak memory is 1308 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250710_163651.log"

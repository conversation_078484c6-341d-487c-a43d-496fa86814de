============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Tue Jul  1 10:33:40 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(519)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
RUN-1001 : Project manager successfully analyzed 22 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.155807s wall, 1.609375s user + 3.546875s system = 5.156250s CPU (100.0%)

RUN-1004 : used memory is 77 MB, reserved memory is 39 MB, peak memory is 89 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.798248s wall, 1.687500s user + 0.109375s system = 1.796875s CPU (99.9%)

RUN-1004 : used memory is 301 MB, reserved memory is 271 MB, peak memory is 304 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 58 trigger nets, 58 data nets.
KIT-1004 : Chipwatcher code = 1110111010000001
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=158) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=158) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=158)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=158)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 23380/23 useful/useless nets, 20084/12 useful/useless insts
SYN-1016 : Merged 28 instances.
SYN-1032 : 22985/20 useful/useless nets, 20591/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 478 better
SYN-1014 : Optimize round 2
SYN-1032 : 22601/45 useful/useless nets, 20207/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.548058s wall, 2.484375s user + 0.062500s system = 2.546875s CPU (100.0%)

RUN-1004 : used memory is 330 MB, reserved memory is 299 MB, peak memory is 332 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22673/441 useful/useless nets, 20330/63 useful/useless insts
SYN-1016 : Merged 69 instances.
SYN-2571 : Optimize after map_dsp, round 1, 573 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 56 instances.
SYN-2501 : Optimize round 1, 114 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 23177/5 useful/useless nets, 20834/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 85639, tnet num: 23177, tinst num: 20833, tnode num: 120323, tedge num: 133390.
TMR-2508 : Levelizing timing graph completed, there are 85 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.166568s wall, 1.140625s user + 0.031250s system = 1.171875s CPU (100.5%)

RUN-1004 : used memory is 470 MB, reserved memory is 446 MB, peak memory is 470 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 23177 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 282 (3.29), #lev = 7 (1.66)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 282 (3.29), #lev = 7 (1.66)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 614 instances into 282 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 487 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 160 adder to BLE ...
SYN-4008 : Packed 160 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.659059s wall, 4.515625s user + 0.125000s system = 4.640625s CPU (99.6%)

RUN-1004 : used memory is 353 MB, reserved memory is 327 MB, peak memory is 583 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.528755s wall, 7.281250s user + 0.234375s system = 7.515625s CPU (99.8%)

RUN-1004 : used memory is 353 MB, reserved memory is 328 MB, peak memory is 583 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[7] will be merged to another kept net COM3/rmc_com3/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[6] will be merged to another kept net COM3/rmc_com3/GPRMC_data[6]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[5] will be merged to another kept net COM3/rmc_com3/GPRMC_data[5]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[4] will be merged to another kept net COM3/rmc_com3/GPRMC_data[4]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[3] will be merged to another kept net COM3/rmc_com3/GPRMC_data[3]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[2] will be merged to another kept net COM3/rmc_com3/GPRMC_data[2]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[1] will be merged to another kept net COM3/rmc_com3/GPRMC_data[1]
SYN-5055 WARNING: The kept net FMC/Error_state[0] will be merged to another kept net COM3/rmc_com3/GPRMC_sub_state_cnt0[3]
SYN-5055 WARNING: The kept net COM3/rmc_com3/GPRMC_sub_state_cnt0[3] will be merged to another kept net COM3/rmc_com3/GPRMC_sub_state_cnt0[2]
SYN-5055 WARNING: The kept net COM3/rmc_com3/GPRMC_sub_state_cnt0[2] will be merged to another kept net COM3/rmc_com3/GPRMC_sub_state_cnt0[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (335 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 20158 instances
RUN-0007 : 5793 luts, 12807 seqs, 951 mslices, 494 lslices, 56 pads, 52 brams, 0 dsps
RUN-1001 : There are total 22525 nets
RUN-1001 : 16832 nets have 2 pins
RUN-1001 : 4511 nets have [3 - 5] pins
RUN-1001 : 804 nets have [6 - 10] pins
RUN-1001 : 250 nets have [11 - 20] pins
RUN-1001 : 104 nets have [21 - 99] pins
RUN-1001 : 24 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4743     
RUN-1001 :   No   |  No   |  Yes  |     670     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    6787     
RUN-1001 :   Yes  |  No   |  Yes  |     507     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  117  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 125
PHY-3001 : Initial placement ...
PHY-3001 : design contains 20156 instances, 5793 luts, 12807 seqs, 1445 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1195 pins
PHY-3001 : Huge net DATA/done_div with 1714 pins
PHY-0007 : Cell area utilization is 65%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 84166, tnet num: 22523, tinst num: 20156, tnode num: 118985, tedge num: 132236.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.158163s wall, 1.156250s user + 0.000000s system = 1.156250s CPU (99.8%)

RUN-1004 : used memory is 533 MB, reserved memory is 514 MB, peak memory is 583 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22523 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.999424s wall, 1.984375s user + 0.015625s system = 2.000000s CPU (100.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.51221e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 20156.
PHY-3001 : Level 1 #clusters 2186.
PHY-3001 : End clustering;  0.256242s wall, 0.390625s user + 0.015625s system = 0.406250s CPU (158.5%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 65%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 874950, overlap = 689.844
PHY-3002 : Step(2): len = 795555, overlap = 740.5
PHY-3002 : Step(3): len = 523091, overlap = 922.469
PHY-3002 : Step(4): len = 458879, overlap = 977.344
PHY-3002 : Step(5): len = 381319, overlap = 1076.97
PHY-3002 : Step(6): len = 339406, overlap = 1154.88
PHY-3002 : Step(7): len = 290224, overlap = 1215.59
PHY-3002 : Step(8): len = 262589, overlap = 1285.38
PHY-3002 : Step(9): len = 229958, overlap = 1313.66
PHY-3002 : Step(10): len = 206565, overlap = 1345.62
PHY-3002 : Step(11): len = 181818, overlap = 1395.47
PHY-3002 : Step(12): len = 166775, overlap = 1415
PHY-3002 : Step(13): len = 153789, overlap = 1463.41
PHY-3002 : Step(14): len = 142529, overlap = 1492.53
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.45087e-07
PHY-3002 : Step(15): len = 144532, overlap = 1478.72
PHY-3002 : Step(16): len = 173199, overlap = 1433.28
PHY-3002 : Step(17): len = 180264, overlap = 1347.56
PHY-3002 : Step(18): len = 186384, overlap = 1259.06
PHY-3002 : Step(19): len = 182847, overlap = 1209.84
PHY-3002 : Step(20): len = 179179, overlap = 1209.28
PHY-3002 : Step(21): len = 177851, overlap = 1204.75
PHY-3002 : Step(22): len = 173433, overlap = 1216.34
PHY-3002 : Step(23): len = 171229, overlap = 1222.91
PHY-3002 : Step(24): len = 167424, overlap = 1245.66
PHY-3002 : Step(25): len = 166000, overlap = 1238.94
PHY-3002 : Step(26): len = 164996, overlap = 1234.06
PHY-3002 : Step(27): len = 165087, overlap = 1224.09
PHY-3002 : Step(28): len = 162921, overlap = 1213.81
PHY-3002 : Step(29): len = 162037, overlap = 1221.44
PHY-3002 : Step(30): len = 161296, overlap = 1237.19
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 1.69017e-06
PHY-3002 : Step(31): len = 170221, overlap = 1221.28
PHY-3002 : Step(32): len = 183964, overlap = 1134.09
PHY-3002 : Step(33): len = 188014, overlap = 1094.69
PHY-3002 : Step(34): len = 190362, overlap = 1062.78
PHY-3002 : Step(35): len = 191534, overlap = 1052.72
PHY-3002 : Step(36): len = 191303, overlap = 1057.19
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 3.38035e-06
PHY-3002 : Step(37): len = 200778, overlap = 1039.5
PHY-3002 : Step(38): len = 215697, overlap = 973.719
PHY-3002 : Step(39): len = 219863, overlap = 925.5
PHY-3002 : Step(40): len = 222565, overlap = 899.281
PHY-3002 : Step(41): len = 222659, overlap = 884.5
PHY-3002 : Step(42): len = 222117, overlap = 870.156
PHY-3002 : Step(43): len = 221007, overlap = 877.281
PHY-3002 : Step(44): len = 219559, overlap = 865.375
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 6.7607e-06
PHY-3002 : Step(45): len = 228853, overlap = 830.938
PHY-3002 : Step(46): len = 246038, overlap = 697.125
PHY-3002 : Step(47): len = 250599, overlap = 658.125
PHY-3002 : Step(48): len = 252419, overlap = 637.344
PHY-3002 : Step(49): len = 252580, overlap = 644.156
PHY-3002 : Step(50): len = 251835, overlap = 644.469
PHY-3002 : Step(51): len = 249911, overlap = 643.781
PHY-3002 : Step(52): len = 247745, overlap = 662.031
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.35214e-05
PHY-3002 : Step(53): len = 259798, overlap = 639.281
PHY-3002 : Step(54): len = 274709, overlap = 553.125
PHY-3002 : Step(55): len = 280120, overlap = 523.875
PHY-3002 : Step(56): len = 282171, overlap = 516.156
PHY-3002 : Step(57): len = 282154, overlap = 515.406
PHY-3002 : Step(58): len = 280499, overlap = 515.594
PHY-3002 : Step(59): len = 278377, overlap = 529.094
PHY-3002 : Step(60): len = 277257, overlap = 525.844
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 2.70428e-05
PHY-3002 : Step(61): len = 286161, overlap = 508.562
PHY-3002 : Step(62): len = 297489, overlap = 473.188
PHY-3002 : Step(63): len = 300661, overlap = 453.5
PHY-3002 : Step(64): len = 301706, overlap = 447.125
PHY-3002 : Step(65): len = 300415, overlap = 463.312
PHY-3002 : Step(66): len = 299679, overlap = 461.906
PHY-3002 : Step(67): len = 297276, overlap = 467.125
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 5.40856e-05
PHY-3002 : Step(68): len = 306092, overlap = 432.031
PHY-3002 : Step(69): len = 315897, overlap = 414.812
PHY-3002 : Step(70): len = 317792, overlap = 408.219
PHY-3002 : Step(71): len = 318852, overlap = 381.125
PHY-3002 : Step(72): len = 318420, overlap = 379.656
PHY-3002 : Step(73): len = 317092, overlap = 373.75
PHY-3002 : Step(74): len = 316854, overlap = 374.312
PHY-3002 : Step(75): len = 316287, overlap = 376.438
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000103155
PHY-3002 : Step(76): len = 320040, overlap = 361.312
PHY-3002 : Step(77): len = 325850, overlap = 357.312
PHY-3002 : Step(78): len = 328612, overlap = 334.281
PHY-3002 : Step(79): len = 330638, overlap = 317.875
PHY-3002 : Step(80): len = 329958, overlap = 308
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000206311
PHY-3002 : Step(81): len = 332385, overlap = 310.688
PHY-3002 : Step(82): len = 337269, overlap = 298.406
PHY-3002 : Step(83): len = 339998, overlap = 296.188
PHY-3002 : Step(84): len = 342851, overlap = 285.562
PHY-3002 : Step(85): len = 342993, overlap = 272.312
PHY-3002 : Step(86): len = 343107, overlap = 271
PHY-3002 : Step(87): len = 342588, overlap = 283.812
PHY-3002 : Step(88): len = 342408, overlap = 271.125
PHY-3002 : Step(89): len = 342967, overlap = 278.594
PHY-3002 : Step(90): len = 342787, overlap = 282.719
PHY-3002 : Step(91): len = 342546, overlap = 284.656
PHY-3002 : Step(92): len = 341803, overlap = 277.344
PHY-3002 : Step(93): len = 342441, overlap = 290.719
PHY-3002 : Step(94): len = 341936, overlap = 290.719
PHY-3002 : Step(95): len = 341988, overlap = 299.062
PHY-3002 : Step(96): len = 340806, overlap = 291
PHY-3002 : Step(97): len = 340418, overlap = 286.562
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(98): len = 341408, overlap = 282.125
PHY-3002 : Step(99): len = 342994, overlap = 284.594
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.014112s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (110.7%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/22525.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 454224, over cnt = 1284(3%), over = 5944, worst = 49
PHY-1001 : End global iterations;  0.861546s wall, 1.203125s user + 0.015625s system = 1.218750s CPU (141.5%)

PHY-1001 : Congestion index: top1 = 75.37, top5 = 52.77, top10 = 43.49, top15 = 38.17.
PHY-3001 : End congestion estimation;  1.104388s wall, 1.437500s user + 0.031250s system = 1.468750s CPU (133.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22523 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.022012s wall, 1.000000s user + 0.031250s system = 1.031250s CPU (100.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.46098e-05
PHY-3002 : Step(100): len = 386176, overlap = 182.062
PHY-3002 : Step(101): len = 398883, overlap = 171.438
PHY-3002 : Step(102): len = 402928, overlap = 166.594
PHY-3002 : Step(103): len = 403986, overlap = 161.125
PHY-3002 : Step(104): len = 410778, overlap = 155.375
PHY-3002 : Step(105): len = 417741, overlap = 152.5
PHY-3002 : Step(106): len = 419910, overlap = 153.969
PHY-3002 : Step(107): len = 424627, overlap = 163
PHY-3002 : Step(108): len = 425617, overlap = 158.344
PHY-3002 : Step(109): len = 426385, overlap = 159.062
PHY-3002 : Step(110): len = 428854, overlap = 157.594
PHY-3002 : Step(111): len = 428005, overlap = 154.594
PHY-3002 : Step(112): len = 428378, overlap = 158.562
PHY-3002 : Step(113): len = 429280, overlap = 159.906
PHY-3002 : Step(114): len = 427574, overlap = 156.188
PHY-3002 : Step(115): len = 427761, overlap = 158.094
PHY-3002 : Step(116): len = 429337, overlap = 157.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00016922
PHY-3002 : Step(117): len = 429365, overlap = 154.031
PHY-3002 : Step(118): len = 430531, overlap = 150.156
PHY-3002 : Step(119): len = 431495, overlap = 149.312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000294404
PHY-3002 : Step(120): len = 438386, overlap = 134.875
PHY-3002 : Step(121): len = 444868, overlap = 128.906
PHY-3002 : Step(122): len = 446473, overlap = 127.781
PHY-3002 : Step(123): len = 451495, overlap = 126.75
PHY-3002 : Step(124): len = 454400, overlap = 123.625
PHY-3002 : Step(125): len = 455704, overlap = 116.656
PHY-3002 : Step(126): len = 457172, overlap = 110.281
PHY-3002 : Step(127): len = 458936, overlap = 115.062
PHY-3002 : Step(128): len = 457630, overlap = 118.188
PHY-3002 : Step(129): len = 457397, overlap = 116.344
PHY-3002 : Step(130): len = 455619, overlap = 118.719
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000588808
PHY-3002 : Step(131): len = 456844, overlap = 106.969
PHY-3002 : Step(132): len = 458929, overlap = 104.625
PHY-3002 : Step(133): len = 460847, overlap = 103.469
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.000986572
PHY-3002 : Step(134): len = 462725, overlap = 110.594
PHY-3002 : Step(135): len = 473764, overlap = 105.031
PHY-3002 : Step(136): len = 486475, overlap = 101
PHY-3002 : Step(137): len = 488239, overlap = 103.688
PHY-3002 : Step(138): len = 484902, overlap = 104.25
PHY-3002 : Step(139): len = 482878, overlap = 103.75
PHY-3002 : Step(140): len = 481730, overlap = 105.562
PHY-3002 : Step(141): len = 481463, overlap = 98.6875
PHY-3002 : Step(142): len = 483114, overlap = 100.875
PHY-3002 : Step(143): len = 485913, overlap = 105.219
PHY-3002 : Step(144): len = 485681, overlap = 102
PHY-3002 : Step(145): len = 485314, overlap = 103.938
PHY-3002 : Step(146): len = 484745, overlap = 101.531
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 41/22525.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 553840, over cnt = 2232(6%), over = 11213, worst = 50
PHY-1001 : End global iterations;  1.085364s wall, 1.703125s user + 0.000000s system = 1.703125s CPU (156.9%)

PHY-1001 : Congestion index: top1 = 90.15, top5 = 66.28, top10 = 55.00, top15 = 48.33.
PHY-3001 : End congestion estimation;  1.360866s wall, 1.968750s user + 0.000000s system = 1.968750s CPU (144.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22523 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.915288s wall, 0.906250s user + 0.015625s system = 0.921875s CPU (100.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.22878e-05
PHY-3002 : Step(147): len = 492290, overlap = 398.656
PHY-3002 : Step(148): len = 499416, overlap = 347.031
PHY-3002 : Step(149): len = 492968, overlap = 321.906
PHY-3002 : Step(150): len = 488623, overlap = 286.875
PHY-3002 : Step(151): len = 486707, overlap = 266.406
PHY-3002 : Step(152): len = 484005, overlap = 252.5
PHY-3002 : Step(153): len = 481035, overlap = 255.375
PHY-3002 : Step(154): len = 479270, overlap = 242.969
PHY-3002 : Step(155): len = 475499, overlap = 247.625
PHY-3002 : Step(156): len = 472257, overlap = 240.844
PHY-3002 : Step(157): len = 470701, overlap = 248.281
PHY-3002 : Step(158): len = 470961, overlap = 245.125
PHY-3002 : Step(159): len = 466506, overlap = 240.812
PHY-3002 : Step(160): len = 465215, overlap = 237.875
PHY-3002 : Step(161): len = 461691, overlap = 239.562
PHY-3002 : Step(162): len = 458360, overlap = 243.375
PHY-3002 : Step(163): len = 456762, overlap = 249.625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000164576
PHY-3002 : Step(164): len = 456342, overlap = 245.781
PHY-3002 : Step(165): len = 457368, overlap = 242.75
PHY-3002 : Step(166): len = 459456, overlap = 233.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000329151
PHY-3002 : Step(167): len = 461937, overlap = 231.688
PHY-3002 : Step(168): len = 469567, overlap = 221.844
PHY-3002 : Step(169): len = 473929, overlap = 219
PHY-3002 : Step(170): len = 472782, overlap = 216.562
PHY-3002 : Step(171): len = 472205, overlap = 215.125
PHY-3002 : Step(172): len = 472376, overlap = 212.844
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000638282
PHY-3002 : Step(173): len = 473047, overlap = 215.062
PHY-3002 : Step(174): len = 477766, overlap = 202.75
PHY-3002 : Step(175): len = 481324, overlap = 193
PHY-3002 : Step(176): len = 484718, overlap = 184.844
PHY-3002 : Step(177): len = 485543, overlap = 184.688
PHY-3002 : Step(178): len = 486073, overlap = 183.094
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.0011801
PHY-3002 : Step(179): len = 486964, overlap = 182.031
PHY-3002 : Step(180): len = 489309, overlap = 176.906
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 84166, tnet num: 22523, tinst num: 20156, tnode num: 118985, tedge num: 132236.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.539640s wall, 1.515625s user + 0.031250s system = 1.546875s CPU (100.5%)

RUN-1004 : used memory is 572 MB, reserved memory is 554 MB, peak memory is 712 MB
OPT-1001 : Total overflow 540.94 peak overflow 5.19
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 186/22525.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 575584, over cnt = 2608(7%), over = 9698, worst = 22
PHY-1001 : End global iterations;  1.293539s wall, 2.015625s user + 0.031250s system = 2.046875s CPU (158.2%)

PHY-1001 : Congestion index: top1 = 63.36, top5 = 51.20, top10 = 45.38, top15 = 41.92.
PHY-1001 : End incremental global routing;  1.526983s wall, 2.250000s user + 0.031250s system = 2.281250s CPU (149.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22523 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.940814s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (99.6%)

OPT-1001 : 19 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 56 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 20078 has valid locations, 278 needs to be replaced
PHY-3001 : design contains 20415 instances, 5900 luts, 12959 seqs, 1445 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 507417
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 72%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17828/22784.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 592120, over cnt = 2612(7%), over = 9706, worst = 22
PHY-1001 : End global iterations;  0.194740s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (112.3%)

PHY-1001 : Congestion index: top1 = 63.41, top5 = 51.31, top10 = 45.57, top15 = 42.16.
PHY-3001 : End congestion estimation;  0.422170s wall, 0.421875s user + 0.015625s system = 0.437500s CPU (103.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 85041, tnet num: 22782, tinst num: 20415, tnode num: 120228, tedge num: 133468.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.509966s wall, 1.468750s user + 0.062500s system = 1.531250s CPU (101.4%)

RUN-1004 : used memory is 620 MB, reserved memory is 621 MB, peak memory is 713 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22782 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.502060s wall, 2.437500s user + 0.078125s system = 2.515625s CPU (100.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(181): len = 507441, overlap = 3.4375
PHY-3002 : Step(182): len = 508703, overlap = 3.25
PHY-3002 : Step(183): len = 509375, overlap = 3.1875
PHY-3002 : Step(184): len = 510403, overlap = 3.1875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 72%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17857/22784.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 590416, over cnt = 2648(7%), over = 9865, worst = 22
PHY-1001 : End global iterations;  0.189037s wall, 0.296875s user + 0.031250s system = 0.328125s CPU (173.6%)

PHY-1001 : Congestion index: top1 = 64.12, top5 = 51.68, top10 = 45.80, top15 = 42.37.
PHY-3001 : End congestion estimation;  0.471828s wall, 0.562500s user + 0.031250s system = 0.593750s CPU (125.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22782 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.973950s wall, 0.937500s user + 0.031250s system = 0.968750s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000644241
PHY-3002 : Step(185): len = 510444, overlap = 180.781
PHY-3002 : Step(186): len = 511074, overlap = 179.656
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00128848
PHY-3002 : Step(187): len = 511376, overlap = 179.844
PHY-3002 : Step(188): len = 512030, overlap = 179.875
PHY-3001 : Final: Len = 512030, Over = 179.875
PHY-3001 : End incremental placement;  5.275480s wall, 5.328125s user + 0.265625s system = 5.593750s CPU (106.0%)

OPT-1001 : Total overflow 547.62 peak overflow 5.19
OPT-1001 : End high-fanout net optimization;  8.303564s wall, 9.218750s user + 0.328125s system = 9.546875s CPU (115.0%)

OPT-1001 : Current memory(MB): used = 717, reserve = 705, peak = 735.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17876/22784.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 592928, over cnt = 2616(7%), over = 9297, worst = 22
PHY-1002 : len = 646048, over cnt = 1848(5%), over = 4677, worst = 22
PHY-1002 : len = 682184, over cnt = 940(2%), over = 2229, worst = 17
PHY-1002 : len = 711072, over cnt = 328(0%), over = 677, worst = 16
PHY-1002 : len = 725064, over cnt = 2(0%), over = 2, worst = 1
PHY-1001 : End global iterations;  1.357577s wall, 1.906250s user + 0.000000s system = 1.906250s CPU (140.4%)

PHY-1001 : Congestion index: top1 = 53.36, top5 = 47.05, top10 = 43.55, top15 = 41.22.
OPT-1001 : End congestion update;  1.602947s wall, 2.140625s user + 0.000000s system = 2.140625s CPU (133.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22782 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.854939s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (100.5%)

OPT-0007 : Start: WNS 4433 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.463573s wall, 3.015625s user + 0.000000s system = 3.015625s CPU (122.4%)

OPT-1001 : Current memory(MB): used = 693, reserve = 688, peak = 735.
OPT-1001 : End physical optimization;  12.627233s wall, 14.156250s user + 0.421875s system = 14.578125s CPU (115.4%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5900 LUT to BLE ...
SYN-4008 : Packed 5900 LUT and 2889 SEQ to BLE.
SYN-4003 : Packing 10070 remaining SEQ's ...
SYN-4005 : Packed 3386 SEQ with LUT/SLICE
SYN-4006 : 140 single LUT's are left
SYN-4006 : 6684 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 12584/14262 primitive instances ...
PHY-3001 : End packing;  2.978627s wall, 2.968750s user + 0.000000s system = 2.968750s CPU (99.7%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8424 instances
RUN-1001 : 4156 mslices, 4155 lslices, 56 pads, 52 brams, 0 dsps
RUN-1001 : There are total 19944 nets
RUN-1001 : 13883 nets have 2 pins
RUN-1001 : 4642 nets have [3 - 5] pins
RUN-1001 : 852 nets have [6 - 10] pins
RUN-1001 : 411 nets have [11 - 20] pins
RUN-1001 : 146 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
PHY-3001 : design contains 8422 instances, 8311 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Cell area utilization is 88%
PHY-3001 : After packing: Len = 531950, Over = 413.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 88%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 8205/19944.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 681272, over cnt = 1675(4%), over = 2687, worst = 8
PHY-1002 : len = 687520, over cnt = 1113(3%), over = 1546, worst = 6
PHY-1002 : len = 702096, over cnt = 369(1%), over = 453, worst = 5
PHY-1002 : len = 707992, over cnt = 126(0%), over = 149, worst = 3
PHY-1002 : len = 711416, over cnt = 4(0%), over = 4, worst = 1
PHY-1001 : End global iterations;  1.283657s wall, 1.843750s user + 0.093750s system = 1.937500s CPU (150.9%)

PHY-1001 : Congestion index: top1 = 53.56, top5 = 46.29, top10 = 43.06, top15 = 40.70.
PHY-3001 : End congestion estimation;  1.606003s wall, 2.140625s user + 0.093750s system = 2.234375s CPU (139.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 70160, tnet num: 19942, tinst num: 8422, tnode num: 95687, tedge num: 115420.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.697886s wall, 1.703125s user + 0.000000s system = 1.703125s CPU (100.3%)

RUN-1004 : used memory is 609 MB, reserved memory is 607 MB, peak memory is 735 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19942 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.642822s wall, 2.640625s user + 0.000000s system = 2.640625s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.74299e-05
PHY-3002 : Step(189): len = 535554, overlap = 391
PHY-3002 : Step(190): len = 534445, overlap = 382.75
PHY-3002 : Step(191): len = 533465, overlap = 399.75
PHY-3002 : Step(192): len = 535112, overlap = 403.75
PHY-3002 : Step(193): len = 533909, overlap = 412.5
PHY-3002 : Step(194): len = 532232, overlap = 421.25
PHY-3002 : Step(195): len = 529379, overlap = 423.25
PHY-3002 : Step(196): len = 527477, overlap = 432.25
PHY-3002 : Step(197): len = 525696, overlap = 431.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.48597e-05
PHY-3002 : Step(198): len = 529942, overlap = 417.5
PHY-3002 : Step(199): len = 534678, overlap = 402.5
PHY-3002 : Step(200): len = 535092, overlap = 399
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000189719
PHY-3002 : Step(201): len = 541788, overlap = 385.25
PHY-3002 : Step(202): len = 552808, overlap = 377
PHY-3002 : Step(203): len = 553320, overlap = 372.25
PHY-3002 : Step(204): len = 551783, overlap = 372.25
PHY-3002 : Step(205): len = 551360, overlap = 363
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.515702s wall, 0.500000s user + 0.687500s system = 1.187500s CPU (230.3%)

PHY-3001 : Trial Legalized: Len = 671001
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 604/19944.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 767960, over cnt = 2688(7%), over = 4530, worst = 7
PHY-1002 : len = 785784, over cnt = 1681(4%), over = 2404, worst = 6
PHY-1002 : len = 806320, over cnt = 631(1%), over = 930, worst = 6
PHY-1002 : len = 824432, over cnt = 31(0%), over = 34, worst = 3
PHY-1002 : len = 825816, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.039297s wall, 3.296875s user + 0.000000s system = 3.296875s CPU (161.7%)

PHY-1001 : Congestion index: top1 = 52.65, top5 = 47.42, top10 = 44.83, top15 = 43.09.
PHY-3001 : End congestion estimation;  2.380308s wall, 3.625000s user + 0.000000s system = 3.625000s CPU (152.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19942 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.864865s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (99.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000201725
PHY-3002 : Step(206): len = 626010, overlap = 97
PHY-3002 : Step(207): len = 607320, overlap = 135
PHY-3002 : Step(208): len = 595231, overlap = 182.75
PHY-3002 : Step(209): len = 586935, overlap = 229
PHY-3002 : Step(210): len = 581888, overlap = 258
PHY-3002 : Step(211): len = 578916, overlap = 271.75
PHY-3002 : Step(212): len = 577401, overlap = 286
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00040345
PHY-3002 : Step(213): len = 582134, overlap = 276.75
PHY-3002 : Step(214): len = 586013, overlap = 271.25
PHY-3002 : Step(215): len = 584935, overlap = 271
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(216): len = 587447, overlap = 268.75
PHY-3002 : Step(217): len = 591669, overlap = 265
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.030296s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (51.6%)

PHY-3001 : Legalized: Len = 635257, Over = 0
PHY-3001 : Spreading special nets. 57 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.086519s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (108.4%)

PHY-3001 : 78 instances has been re-located, deltaX = 30, deltaY = 41, maxDist = 3.
PHY-3001 : Final: Len = 636551, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 70160, tnet num: 19942, tinst num: 8422, tnode num: 95687, tedge num: 115420.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.944803s wall, 1.906250s user + 0.031250s system = 1.937500s CPU (99.6%)

RUN-1004 : used memory is 624 MB, reserved memory is 633 MB, peak memory is 735 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3681/19944.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 744944, over cnt = 2456(6%), over = 3966, worst = 8
PHY-1002 : len = 759552, over cnt = 1481(4%), over = 2051, worst = 6
PHY-1002 : len = 777024, over cnt = 614(1%), over = 792, worst = 5
PHY-1002 : len = 785824, over cnt = 212(0%), over = 279, worst = 5
PHY-1002 : len = 791768, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.785131s wall, 2.843750s user + 0.031250s system = 2.875000s CPU (161.1%)

PHY-1001 : Congestion index: top1 = 49.44, top5 = 45.40, top10 = 42.97, top15 = 41.23.
PHY-1001 : End incremental global routing;  2.082584s wall, 3.140625s user + 0.031250s system = 3.171875s CPU (152.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19942 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.971925s wall, 0.953125s user + 0.015625s system = 0.968750s CPU (99.7%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 56 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8361 has valid locations, 15 needs to be replaced
PHY-3001 : design contains 8435 instances, 8324 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 638877
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 88%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17940/19955.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 793936, over cnt = 31(0%), over = 31, worst = 1
PHY-1002 : len = 793640, over cnt = 23(0%), over = 23, worst = 1
PHY-1002 : len = 793856, over cnt = 10(0%), over = 10, worst = 1
PHY-1002 : len = 793928, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 794112, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.694251s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (99.0%)

PHY-1001 : Congestion index: top1 = 49.44, top5 = 45.47, top10 = 43.06, top15 = 41.32.
PHY-3001 : End congestion estimation;  0.986675s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (101.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 70230, tnet num: 19953, tinst num: 8435, tnode num: 95776, tedge num: 115507.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.901274s wall, 1.859375s user + 0.000000s system = 1.859375s CPU (97.8%)

RUN-1004 : used memory is 652 MB, reserved memory is 647 MB, peak memory is 735 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19953 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.786422s wall, 2.734375s user + 0.015625s system = 2.750000s CPU (98.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(218): len = 638760, overlap = 1.5
PHY-3002 : Step(219): len = 638675, overlap = 1.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 88%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17935/19955.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 793496, over cnt = 18(0%), over = 21, worst = 2
PHY-1002 : len = 793464, over cnt = 14(0%), over = 16, worst = 2
PHY-1002 : len = 793592, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 793704, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 793704, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.699880s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (107.2%)

PHY-1001 : Congestion index: top1 = 49.55, top5 = 45.47, top10 = 43.01, top15 = 41.28.
PHY-3001 : End congestion estimation;  0.983273s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (104.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19953 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.859049s wall, 0.828125s user + 0.031250s system = 0.859375s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(220): len = 638745, overlap = 1.5
PHY-3002 : Step(221): len = 638697, overlap = 1.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005502s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 638807, Over = 0
PHY-3001 : Spreading special nets. 2 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.065459s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (95.5%)

PHY-3001 : 2 instances has been re-located, deltaX = 1, deltaY = 1, maxDist = 1.
PHY-3001 : Final: Len = 638807, Over = 0
PHY-3001 : End incremental placement;  6.246680s wall, 6.437500s user + 0.156250s system = 6.593750s CPU (105.6%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  9.758475s wall, 10.984375s user + 0.203125s system = 11.187500s CPU (114.6%)

OPT-1001 : Current memory(MB): used = 718, reserve = 707, peak = 735.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17935/19955.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 793904, over cnt = 13(0%), over = 14, worst = 2
PHY-1002 : len = 793888, over cnt = 12(0%), over = 12, worst = 1
PHY-1002 : len = 793968, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 794040, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 794088, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.682236s wall, 0.734375s user + 0.046875s system = 0.781250s CPU (114.5%)

PHY-1001 : Congestion index: top1 = 49.57, top5 = 45.48, top10 = 43.00, top15 = 41.27.
OPT-1001 : End congestion update;  0.969180s wall, 1.015625s user + 0.046875s system = 1.062500s CPU (109.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19953 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.742090s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (101.1%)

OPT-0007 : Start: WNS 4626 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.715595s wall, 1.765625s user + 0.046875s system = 1.812500s CPU (105.6%)

OPT-1001 : Current memory(MB): used = 718, reserve = 709, peak = 735.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19953 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.734695s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (100.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17953/19955.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 794088, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.122189s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (102.3%)

PHY-1001 : Congestion index: top1 = 49.57, top5 = 45.48, top10 = 43.00, top15 = 41.27.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19953 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.730510s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (100.5%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4626 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 49.172414
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4626ps with logic level 7 
RUN-1001 :       #2 path slack 4661ps with logic level 7 
RUN-1001 :       #3 path slack 4676ps with logic level 7 
RUN-1001 :       #4 path slack 4711ps with logic level 7 
RUN-1001 :       #5 path slack 4726ps with logic level 7 
RUN-1001 :       #6 path slack 4726ps with logic level 7 
OPT-1001 : End physical optimization;  15.565904s wall, 16.984375s user + 0.281250s system = 17.265625s CPU (110.9%)

RUN-1003 : finish command "place" in  79.031675s wall, 142.984375s user + 9.312500s system = 152.296875s CPU (192.7%)

RUN-1004 : used memory is 600 MB, reserved memory is 606 MB, peak memory is 735 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.597802s wall, 2.812500s user + 0.000000s system = 2.812500s CPU (176.0%)

RUN-1004 : used memory is 600 MB, reserved memory is 607 MB, peak memory is 735 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8437 instances
RUN-1001 : 4162 mslices, 4162 lslices, 56 pads, 52 brams, 0 dsps
RUN-1001 : There are total 19955 nets
RUN-1001 : 13882 nets have 2 pins
RUN-1001 : 4642 nets have [3 - 5] pins
RUN-1001 : 858 nets have [6 - 10] pins
RUN-1001 : 418 nets have [11 - 20] pins
RUN-1001 : 145 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 70230, tnet num: 19953, tinst num: 8435, tnode num: 95776, tedge num: 115507.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.657616s wall, 1.640625s user + 0.015625s system = 1.656250s CPU (99.9%)

RUN-1004 : used memory is 606 MB, reserved memory is 610 MB, peak memory is 735 MB
PHY-1001 : 4162 mslices, 4162 lslices, 56 pads, 52 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19953 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 722672, over cnt = 2579(7%), over = 4405, worst = 8
PHY-1002 : len = 741688, over cnt = 1553(4%), over = 2243, worst = 7
PHY-1002 : len = 759768, over cnt = 746(2%), over = 1009, worst = 5
PHY-1002 : len = 774960, over cnt = 110(0%), over = 125, worst = 3
PHY-1002 : len = 778048, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.749341s wall, 3.031250s user + 0.062500s system = 3.093750s CPU (176.9%)

PHY-1001 : Congestion index: top1 = 48.60, top5 = 44.97, top10 = 42.68, top15 = 40.96.
PHY-1001 : End global routing;  2.073215s wall, 3.343750s user + 0.078125s system = 3.421875s CPU (165.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 712, reserve = 712, peak = 735.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 980, reserve = 975, peak = 980.
PHY-1001 : End build detailed router design. 4.710173s wall, 4.640625s user + 0.078125s system = 4.718750s CPU (100.2%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 189904, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.823062s wall, 0.796875s user + 0.015625s system = 0.812500s CPU (98.7%)

PHY-1001 : Current memory(MB): used = 1017, reserve = 1013, peak = 1017.
PHY-1001 : End phase 1; 0.833425s wall, 0.812500s user + 0.015625s system = 0.828125s CPU (99.4%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 56% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.87454e+06, over cnt = 1530(0%), over = 1535, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1032, reserve = 1028, peak = 1032.
PHY-1001 : End initial routed; 22.344398s wall, 50.125000s user + 0.656250s system = 50.781250s CPU (227.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18735(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.616   |   0.000   |   0   
RUN-1001 :   Hold   |   0.104   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.398406s wall, 3.390625s user + 0.015625s system = 3.406250s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 1035, reserve = 1029, peak = 1035.
PHY-1001 : End phase 2; 25.743057s wall, 53.515625s user + 0.671875s system = 54.187500s CPU (210.5%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.87454e+06, over cnt = 1530(0%), over = 1535, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.242844s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (102.9%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.85838e+06, over cnt = 621(0%), over = 622, worst = 2, crit = 0
PHY-1001 : End DR Iter 1; 0.946191s wall, 1.687500s user + 0.015625s system = 1.703125s CPU (180.0%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.85789e+06, over cnt = 178(0%), over = 178, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.556433s wall, 0.843750s user + 0.015625s system = 0.859375s CPU (154.4%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.86013e+06, over cnt = 21(0%), over = 21, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.325013s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (105.8%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.86038e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.193949s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (96.7%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.86042e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.150852s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (93.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18735(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.404   |   0.000   |   0   
RUN-1001 :   Hold   |   0.104   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.362594s wall, 3.343750s user + 0.015625s system = 3.359375s CPU (99.9%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 388 feed throughs used by 325 nets
PHY-1001 : End commit to database; 2.222135s wall, 2.187500s user + 0.031250s system = 2.218750s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 1127, reserve = 1128, peak = 1127.
PHY-1001 : End phase 3; 8.521220s wall, 9.546875s user + 0.078125s system = 9.625000s CPU (113.0%)

PHY-1003 : Routed, final wirelength = 1.86042e+06
PHY-1001 : Current memory(MB): used = 1131, reserve = 1132, peak = 1131.
PHY-1001 : End export database. 0.155397s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (100.5%)

PHY-1001 : End detail routing;  40.355160s wall, 69.062500s user + 0.843750s system = 69.906250s CPU (173.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 70230, tnet num: 19953, tinst num: 8435, tnode num: 95776, tedge num: 115507.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.640929s wall, 1.640625s user + 0.000000s system = 1.640625s CPU (100.0%)

RUN-1004 : used memory is 1067 MB, reserved memory is 1081 MB, peak memory is 1131 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  48.202413s wall, 78.125000s user + 0.968750s system = 79.093750s CPU (164.1%)

RUN-1004 : used memory is 1068 MB, reserved memory is 1083 MB, peak memory is 1131 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8937   out of  19600   45.60%
#reg                    13055   out of  19600   66.61%
#le                     15591
  #lut only              2536   out of  15591   16.27%
  #reg only              6654   out of  15591   42.68%
  #lut&reg               6401   out of  15591   41.06%
#dsp                        0   out of     29    0.00%
#bram                      52   out of     64   81.25%
  #bram9k                  52
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       56   out of    188   29.79%
  #ireg                    10
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    7115
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          194
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT        E11        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT        A13        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT        F10        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         E8        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         E7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT        A10        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C6        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         D6        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         C7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT        E12        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         F1        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         A7        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H13        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J11        LVCMOS33          N/A          PULLUP          IREG       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT        E13        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         R7        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         M7        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         P7        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         T8        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         P8        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         N8        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M9        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         T9        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         R9        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         T6        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         P9        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         N9        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT        P11        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         T5        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         B2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        L10        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        E15        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        R14        LVCMOS33           8            N/A            OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT         R2        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT        N12        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        B10        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         J6        LVCMOS33           8            N/A            NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        R15        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E6        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15591  |7492    |1445    |13098   |52      |0       |
|  AnyFog_dataX                      |AnyFog          |201    |75      |22      |164     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |54      |22      |49      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |206    |113     |22      |172     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |84     |62      |22      |50      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |207    |83      |22      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |53      |22      |52      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |3471   |829     |34      |3394    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |735    |72      |5       |722     |0       |0       |
|    STADOP_com2                     |STADOP          |559    |105     |0       |551     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |47      |14      |42      |0       |0       |
|    head_com2                       |uniheading      |263    |91      |5       |249     |0       |0       |
|    uart_com2                       |Agrica          |1558   |223     |10      |1539    |0       |0       |
|  COM3                              |COM3_Control    |235    |110     |14      |191     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |60     |37      |14      |38      |0       |0       |
|    rmc_com3                        |Gprmc           |175    |73      |0       |153     |0       |0       |
|  DATA                              |Data_Processing |8733   |4348    |1059    |7061    |0       |0       |
|    DIV_Dtemp                       |Divider         |800    |341     |84      |676     |0       |0       |
|    DIV_Utemp                       |Divider         |594    |334     |84      |468     |0       |0       |
|    DIV_accX                        |Divider         |590    |309     |84      |468     |0       |0       |
|    DIV_accY                        |Divider         |671    |325     |108     |509     |0       |0       |
|    DIV_accZ                        |Divider         |681    |398     |132     |475     |0       |0       |
|    DIV_rateX                       |Divider         |719    |381     |132     |513     |0       |0       |
|    DIV_rateY                       |Divider         |534    |321     |132     |328     |0       |0       |
|    DIV_rateZ                       |Divider         |565    |370     |132     |360     |0       |0       |
|    genclk                          |genclk          |87     |54      |20      |53      |0       |0       |
|  FMC                               |FMC_Ctrl        |519    |465     |43      |359     |0       |0       |
|  IIC                               |I2C_master      |266    |238     |11      |244     |0       |0       |
|  IMU_CTRL                          |SCHA634         |914    |639     |61      |750     |0       |0       |
|    CtrlData                        |CtrlData        |450    |398     |47      |335     |0       |0       |
|      usms                          |Time_1ms        |30     |25      |5       |21      |0       |0       |
|    SPIM                            |SPI_SCHA634     |464    |241     |14      |415     |0       |0       |
|  POWER                             |POWER_EN        |92     |48      |38      |33      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |743    |544     |119     |510     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |743    |544     |119     |510     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |333    |269     |0       |316     |0       |0       |
|        reg_inst                    |register        |331    |267     |0       |314     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |410    |275     |119     |194     |0       |0       |
|        bus_inst                    |bus_top         |179    |117     |62      |66      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |27     |17      |10      |10      |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |51     |33      |18      |19      |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |101    |67      |34      |37      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |151    |116     |29      |96      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13825  
    #2          2       3648   
    #3          3        706   
    #4          4        288   
    #5        5-10       950   
    #6        11-50      452   
    #7       51-100      19    
    #8       101-500      4    
    #9        >500        2    
  Average     2.16             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  1.946891s wall, 3.406250s user + 0.000000s system = 3.406250s CPU (175.0%)

RUN-1004 : used memory is 1069 MB, reserved memory is 1084 MB, peak memory is 1131 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 70230, tnet num: 19953, tinst num: 8435, tnode num: 95776, tedge num: 115507.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.634935s wall, 1.625000s user + 0.000000s system = 1.625000s CPU (99.4%)

RUN-1004 : used memory is 1071 MB, reserved memory is 1085 MB, peak memory is 1131 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19953 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.304965s wall, 1.296875s user + 0.000000s system = 1.296875s CPU (99.4%)

RUN-1004 : used memory is 1076 MB, reserved memory is 1088 MB, peak memory is 1131 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 28b752c1c0e7d7ba92ac5beed3c46754b7b56ac64969e4b0149cfdbc1d295011 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8435
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19955, pip num: 154876
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 388
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3219 valid insts, and 430412 bits set as '1'.
BIT-1004 : the usercode register value: 00000000000000001110111010000001
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  10.784162s wall, 107.031250s user + 0.265625s system = 107.296875s CPU (994.9%)

RUN-1004 : used memory is 1204 MB, reserved memory is 1197 MB, peak memory is 1319 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250701_103340.log"

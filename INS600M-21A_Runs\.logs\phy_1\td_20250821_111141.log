============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Aug 21 11:11:41 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.983011s wall, 1.546875s user + 4.421875s system = 5.968750s CPU (99.8%)

RUN-1004 : used memory is 79 MB, reserved memory is 40 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.968715s wall, 1.906250s user + 0.031250s system = 1.937500s CPU (98.4%)

RUN-1004 : used memory is 302 MB, reserved memory is 271 MB, peak memory is 306 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 8 view nodes, 34 trigger nets, 34 data nets.
KIT-1004 : Chipwatcher code = 0000011010100101
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_DIN_NUM=34,BUS_CTRL_NUM=100,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb011,32'sb011,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01110,32'sb010001,32'sb010010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110000,32'sb0111010,32'sb01000000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=122) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=122) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_DIN_NUM=34,BUS_CTRL_NUM=100,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb011,32'sb011,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01110,32'sb010001,32'sb010010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110000,32'sb0111010,32'sb01000000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_DIN_NUM=34,BUS_CTRL_NUM=100,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb011,32'sb011,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01110,32'sb010001,32'sb010010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110000,32'sb0111010,32'sb01000000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb011) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_DIN_NUM=34,BUS_CTRL_NUM=100,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb011,32'sb011,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01110,32'sb010001,32'sb010010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110000,32'sb0111010,32'sb01000000})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=122)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=122)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_DIN_NUM=34,BUS_CTRL_NUM=100,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb011,32'sb011,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01110,32'sb010001,32'sb010010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110000,32'sb0111010,32'sb01000000})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_DIN_NUM=34,BUS_CTRL_NUM=100,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb011,32'sb011,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01110,32'sb010001,32'sb010010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110000,32'sb0111010,32'sb01000000})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb011)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22810/34 useful/useless nets, 19582/20 useful/useless insts
SYN-1016 : Merged 41 instances.
SYN-1032 : 22474/24 useful/useless nets, 19980/20 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 4 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 4 mux instances.
SYN-1015 : Optimize round 1, 387 better
SYN-1014 : Optimize round 2
SYN-1032 : 22147/60 useful/useless nets, 19653/64 useful/useless insts
SYN-1015 : Optimize round 2, 128 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.639099s wall, 2.562500s user + 0.093750s system = 2.656250s CPU (100.6%)

RUN-1004 : used memory is 330 MB, reserved memory is 295 MB, peak memory is 331 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22183/228 useful/useless nets, 19719/39 useful/useless insts
SYN-1016 : Merged 33 instances.
SYN-2571 : Optimize after map_dsp, round 1, 300 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 30 instances.
SYN-2501 : Optimize round 1, 62 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 18 macro adder
SYN-1019 : Optimized 17 mux instances.
SYN-1016 : Merged 12 instances.
SYN-1032 : 22614/5 useful/useless nets, 20150/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82241, tnet num: 22614, tinst num: 20149, tnode num: 115246, tedge num: 128503.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.340580s wall, 1.296875s user + 0.046875s system = 1.343750s CPU (100.2%)

RUN-1004 : used memory is 468 MB, reserved memory is 436 MB, peak memory is 468 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22614 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 221 (3.54), #lev = 7 (1.93)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 221 (3.54), #lev = 7 (1.93)
SYN-3001 : Logic optimization runtime opt =   0.04 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 551 instances into 221 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 394 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 112 adder to BLE ...
SYN-4008 : Packed 112 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.966697s wall, 4.890625s user + 0.078125s system = 4.968750s CPU (100.0%)

RUN-1004 : used memory is 352 MB, reserved memory is 318 MB, peak memory is 576 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.963168s wall, 7.750000s user + 0.234375s system = 7.984375s CPU (100.3%)

RUN-1004 : used memory is 353 MB, reserved memory is 319 MB, peak memory is 576 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (261 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19370 instances
RUN-0007 : 5588 luts, 12189 seqs, 977 mslices, 519 lslices, 59 pads, 33 brams, 0 dsps
RUN-1001 : There are total 21842 nets
RUN-1001 : 16402 nets have 2 pins
RUN-1001 : 4257 nets have [3 - 5] pins
RUN-1001 : 823 nets have [6 - 10] pins
RUN-1001 : 234 nets have [11 - 20] pins
RUN-1001 : 104 nets have [21 - 99] pins
RUN-1001 : 22 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4786     
RUN-1001 :   No   |  No   |  Yes  |     701     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     433     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19368 instances, 5588 luts, 12189 seqs, 1496 slices, 293 macros(1496 instances: 977 mslices 519 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80555, tnet num: 21840, tinst num: 19368, tnode num: 113338, tedge num: 126726.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.272598s wall, 1.250000s user + 0.031250s system = 1.281250s CPU (100.7%)

RUN-1004 : used memory is 526 MB, reserved memory is 498 MB, peak memory is 576 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21840 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.220770s wall, 2.140625s user + 0.078125s system = 2.218750s CPU (99.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.63323e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19368.
PHY-3001 : Level 1 #clusters 2225.
PHY-3001 : End clustering;  0.168155s wall, 0.265625s user + 0.031250s system = 0.296875s CPU (176.5%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 888685, overlap = 632.5
PHY-3002 : Step(2): len = 794092, overlap = 721.281
PHY-3002 : Step(3): len = 518287, overlap = 881.344
PHY-3002 : Step(4): len = 462793, overlap = 936
PHY-3002 : Step(5): len = 366424, overlap = 1018.22
PHY-3002 : Step(6): len = 324634, overlap = 1072
PHY-3002 : Step(7): len = 279578, overlap = 1132.03
PHY-3002 : Step(8): len = 249182, overlap = 1197.38
PHY-3002 : Step(9): len = 226005, overlap = 1276.75
PHY-3002 : Step(10): len = 198820, overlap = 1300.06
PHY-3002 : Step(11): len = 184850, overlap = 1346.12
PHY-3002 : Step(12): len = 163250, overlap = 1376.12
PHY-3002 : Step(13): len = 154804, overlap = 1387.69
PHY-3002 : Step(14): len = 141908, overlap = 1396.09
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.11832e-06
PHY-3002 : Step(15): len = 143361, overlap = 1383.19
PHY-3002 : Step(16): len = 178403, overlap = 1332.72
PHY-3002 : Step(17): len = 192409, overlap = 1227.84
PHY-3002 : Step(18): len = 195356, overlap = 1157.91
PHY-3002 : Step(19): len = 193200, overlap = 1129.62
PHY-3002 : Step(20): len = 186663, overlap = 1124.53
PHY-3002 : Step(21): len = 183209, overlap = 1112.97
PHY-3002 : Step(22): len = 175552, overlap = 1095.34
PHY-3002 : Step(23): len = 171874, overlap = 1078.84
PHY-3002 : Step(24): len = 167574, overlap = 1091
PHY-3002 : Step(25): len = 166994, overlap = 1108.94
PHY-3002 : Step(26): len = 165192, overlap = 1129.25
PHY-3002 : Step(27): len = 164984, overlap = 1106.03
PHY-3002 : Step(28): len = 164642, overlap = 1122.28
PHY-3002 : Step(29): len = 164516, overlap = 1116.59
PHY-3002 : Step(30): len = 163978, overlap = 1101.94
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.23664e-06
PHY-3002 : Step(31): len = 171676, overlap = 1064.28
PHY-3002 : Step(32): len = 187129, overlap = 1011.12
PHY-3002 : Step(33): len = 189706, overlap = 968.625
PHY-3002 : Step(34): len = 193406, overlap = 945.875
PHY-3002 : Step(35): len = 193357, overlap = 915.25
PHY-3002 : Step(36): len = 193130, overlap = 895.531
PHY-3002 : Step(37): len = 191712, overlap = 871.125
PHY-3002 : Step(38): len = 192434, overlap = 858.281
PHY-3002 : Step(39): len = 191351, overlap = 857.312
PHY-3002 : Step(40): len = 191366, overlap = 862.281
PHY-3002 : Step(41): len = 189259, overlap = 857.969
PHY-3002 : Step(42): len = 189392, overlap = 857.688
PHY-3002 : Step(43): len = 187360, overlap = 879.562
PHY-3002 : Step(44): len = 187509, overlap = 862.562
PHY-3002 : Step(45): len = 185750, overlap = 851.438
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.47329e-06
PHY-3002 : Step(46): len = 193631, overlap = 828.344
PHY-3002 : Step(47): len = 206061, overlap = 780.25
PHY-3002 : Step(48): len = 208810, overlap = 743.094
PHY-3002 : Step(49): len = 210742, overlap = 726.125
PHY-3002 : Step(50): len = 210637, overlap = 694.781
PHY-3002 : Step(51): len = 211448, overlap = 684.656
PHY-3002 : Step(52): len = 210353, overlap = 707.219
PHY-3002 : Step(53): len = 210477, overlap = 709.312
PHY-3002 : Step(54): len = 209422, overlap = 708.5
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 8.94658e-06
PHY-3002 : Step(55): len = 219705, overlap = 681.406
PHY-3002 : Step(56): len = 235062, overlap = 587.688
PHY-3002 : Step(57): len = 240421, overlap = 573.656
PHY-3002 : Step(58): len = 243133, overlap = 564.969
PHY-3002 : Step(59): len = 242274, overlap = 563.062
PHY-3002 : Step(60): len = 241344, overlap = 562.812
PHY-3002 : Step(61): len = 238776, overlap = 561.688
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.78932e-05
PHY-3002 : Step(62): len = 248929, overlap = 516.781
PHY-3002 : Step(63): len = 262662, overlap = 498.469
PHY-3002 : Step(64): len = 266512, overlap = 460.25
PHY-3002 : Step(65): len = 267557, overlap = 454.406
PHY-3002 : Step(66): len = 265997, overlap = 466.375
PHY-3002 : Step(67): len = 263584, overlap = 459.125
PHY-3002 : Step(68): len = 261617, overlap = 463.156
PHY-3002 : Step(69): len = 259792, overlap = 458.156
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.57863e-05
PHY-3002 : Step(70): len = 270663, overlap = 451.969
PHY-3002 : Step(71): len = 280458, overlap = 406.406
PHY-3002 : Step(72): len = 283437, overlap = 378.281
PHY-3002 : Step(73): len = 284743, overlap = 362.5
PHY-3002 : Step(74): len = 283603, overlap = 357.719
PHY-3002 : Step(75): len = 282115, overlap = 351.25
PHY-3002 : Step(76): len = 279364, overlap = 342.969
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 7.15726e-05
PHY-3002 : Step(77): len = 286237, overlap = 321.906
PHY-3002 : Step(78): len = 293775, overlap = 295.438
PHY-3002 : Step(79): len = 296732, overlap = 277.812
PHY-3002 : Step(80): len = 297745, overlap = 264.844
PHY-3002 : Step(81): len = 296536, overlap = 265
PHY-3002 : Step(82): len = 295416, overlap = 271.938
PHY-3002 : Step(83): len = 292318, overlap = 273.938
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000141307
PHY-3002 : Step(84): len = 296609, overlap = 263.938
PHY-3002 : Step(85): len = 302553, overlap = 242.031
PHY-3002 : Step(86): len = 304290, overlap = 229.594
PHY-3002 : Step(87): len = 305455, overlap = 235.188
PHY-3002 : Step(88): len = 306016, overlap = 225.969
PHY-3002 : Step(89): len = 306890, overlap = 234.406
PHY-3002 : Step(90): len = 304590, overlap = 238.031
PHY-3002 : Step(91): len = 304869, overlap = 227.188
PHY-3002 : Step(92): len = 303702, overlap = 238.281
PHY-3002 : Step(93): len = 303930, overlap = 244.281
PHY-3002 : Step(94): len = 302360, overlap = 242.781
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000270394
PHY-3002 : Step(95): len = 305101, overlap = 242.938
PHY-3002 : Step(96): len = 308694, overlap = 237.688
PHY-3002 : Step(97): len = 309916, overlap = 230.156
PHY-3002 : Step(98): len = 310947, overlap = 231.812
PHY-3002 : Step(99): len = 310660, overlap = 233.281
PHY-3002 : Step(100): len = 311153, overlap = 233.125
PHY-3002 : Step(101): len = 310092, overlap = 237.594
PHY-3002 : Step(102): len = 310439, overlap = 240.656
PHY-3002 : Step(103): len = 310141, overlap = 234.219
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(104): len = 311438, overlap = 233.062
PHY-3002 : Step(105): len = 314697, overlap = 225.719
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.019118s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (163.5%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21842.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 414744, over cnt = 1158(3%), over = 5482, worst = 38
PHY-1001 : End global iterations;  0.858276s wall, 1.125000s user + 0.031250s system = 1.156250s CPU (134.7%)

PHY-1001 : Congestion index: top1 = 73.12, top5 = 51.85, top10 = 42.53, top15 = 37.03.
PHY-3001 : End congestion estimation;  1.120003s wall, 1.390625s user + 0.031250s system = 1.421875s CPU (127.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21840 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.994897s wall, 0.953125s user + 0.031250s system = 0.984375s CPU (98.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000103993
PHY-3002 : Step(106): len = 353141, overlap = 170.594
PHY-3002 : Step(107): len = 363605, overlap = 155.375
PHY-3002 : Step(108): len = 369290, overlap = 129.344
PHY-3002 : Step(109): len = 373848, overlap = 122.531
PHY-3002 : Step(110): len = 378793, overlap = 110.281
PHY-3002 : Step(111): len = 385455, overlap = 110.312
PHY-3002 : Step(112): len = 392692, overlap = 99.2188
PHY-3002 : Step(113): len = 398663, overlap = 96.0625
PHY-3002 : Step(114): len = 401071, overlap = 102.375
PHY-3002 : Step(115): len = 404468, overlap = 106.219
PHY-3002 : Step(116): len = 405382, overlap = 108.5
PHY-3002 : Step(117): len = 407657, overlap = 109.562
PHY-3002 : Step(118): len = 408425, overlap = 113.938
PHY-3002 : Step(119): len = 409835, overlap = 114.375
PHY-3002 : Step(120): len = 412186, overlap = 113.844
PHY-3002 : Step(121): len = 412138, overlap = 116.969
PHY-3002 : Step(122): len = 413235, overlap = 118.469
PHY-3002 : Step(123): len = 416102, overlap = 118.719
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000207986
PHY-3002 : Step(124): len = 414947, overlap = 116.969
PHY-3002 : Step(125): len = 417153, overlap = 117.688
PHY-3002 : Step(126): len = 418960, overlap = 115.875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000415972
PHY-3002 : Step(127): len = 425142, overlap = 110.781
PHY-3002 : Step(128): len = 432281, overlap = 104.469
PHY-3002 : Step(129): len = 437704, overlap = 102.844
PHY-3002 : Step(130): len = 440570, overlap = 108.188
PHY-3002 : Step(131): len = 443254, overlap = 109.438
PHY-3002 : Step(132): len = 444068, overlap = 109.469
PHY-3002 : Step(133): len = 444275, overlap = 106.25
PHY-3002 : Step(134): len = 444532, overlap = 103.812
PHY-3002 : Step(135): len = 444225, overlap = 100
PHY-3002 : Step(136): len = 444075, overlap = 98.625
PHY-3002 : Step(137): len = 445077, overlap = 100.312
PHY-3002 : Step(138): len = 446905, overlap = 96.5312
PHY-3002 : Step(139): len = 447046, overlap = 95.0938
PHY-3002 : Step(140): len = 446831, overlap = 92.625
PHY-3002 : Step(141): len = 447284, overlap = 92.5625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000831944
PHY-3002 : Step(142): len = 446750, overlap = 86.375
PHY-3002 : Step(143): len = 449583, overlap = 86.7188
PHY-3002 : Step(144): len = 452776, overlap = 86.875
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00155873
PHY-3002 : Step(145): len = 452738, overlap = 85.125
PHY-3002 : Step(146): len = 457129, overlap = 87.1875
PHY-3002 : Step(147): len = 468666, overlap = 85.0312
PHY-3002 : Step(148): len = 474782, overlap = 80.4688
PHY-3002 : Step(149): len = 475162, overlap = 86.9062
PHY-3002 : Step(150): len = 474738, overlap = 88.4062
PHY-3002 : Step(151): len = 474791, overlap = 87.7812
PHY-3002 : Step(152): len = 474157, overlap = 90.1875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 57/21842.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 537560, over cnt = 2171(6%), over = 10465, worst = 37
PHY-1001 : End global iterations;  1.037371s wall, 1.750000s user + 0.000000s system = 1.750000s CPU (168.7%)

PHY-1001 : Congestion index: top1 = 78.73, top5 = 61.09, top10 = 52.00, top15 = 46.55.
PHY-3001 : End congestion estimation;  1.353036s wall, 2.078125s user + 0.000000s system = 2.078125s CPU (153.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21840 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.060242s wall, 1.046875s user + 0.015625s system = 1.062500s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000102382
PHY-3002 : Step(153): len = 479968, overlap = 379.156
PHY-3002 : Step(154): len = 481030, overlap = 304.062
PHY-3002 : Step(155): len = 471290, overlap = 286.656
PHY-3002 : Step(156): len = 465658, overlap = 262.531
PHY-3002 : Step(157): len = 459561, overlap = 243.094
PHY-3002 : Step(158): len = 455063, overlap = 224.625
PHY-3002 : Step(159): len = 451228, overlap = 220.375
PHY-3002 : Step(160): len = 448538, overlap = 213.875
PHY-3002 : Step(161): len = 445306, overlap = 206.469
PHY-3002 : Step(162): len = 442837, overlap = 197.531
PHY-3002 : Step(163): len = 441852, overlap = 197.719
PHY-3002 : Step(164): len = 438456, overlap = 201.844
PHY-3002 : Step(165): len = 435804, overlap = 207.906
PHY-3002 : Step(166): len = 434638, overlap = 209.406
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000204763
PHY-3002 : Step(167): len = 433633, overlap = 190.094
PHY-3002 : Step(168): len = 434581, overlap = 188.25
PHY-3002 : Step(169): len = 435292, overlap = 187.688
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000409526
PHY-3002 : Step(170): len = 439347, overlap = 170.281
PHY-3002 : Step(171): len = 446025, overlap = 157.844
PHY-3002 : Step(172): len = 450134, overlap = 146.562
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000819053
PHY-3002 : Step(173): len = 450459, overlap = 145.094
PHY-3002 : Step(174): len = 453460, overlap = 139.094
PHY-3002 : Step(175): len = 457740, overlap = 134.562
PHY-3002 : Step(176): len = 458576, overlap = 126.719
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80555, tnet num: 21840, tinst num: 19368, tnode num: 113338, tedge num: 126726.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.622713s wall, 1.593750s user + 0.031250s system = 1.625000s CPU (100.1%)

RUN-1004 : used memory is 566 MB, reserved memory is 541 MB, peak memory is 700 MB
OPT-1001 : Total overflow 486.50 peak overflow 4.41
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 398/21842.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 535912, over cnt = 2488(7%), over = 8594, worst = 21
PHY-1001 : End global iterations;  1.302159s wall, 2.140625s user + 0.015625s system = 2.156250s CPU (165.6%)

PHY-1001 : Congestion index: top1 = 54.94, top5 = 45.85, top10 = 41.43, top15 = 38.62.
PHY-1001 : End incremental global routing;  1.566567s wall, 2.406250s user + 0.015625s system = 2.421875s CPU (154.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21840 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.084086s wall, 1.078125s user + 0.000000s system = 1.078125s CPU (99.5%)

OPT-1001 : 15 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19291 has valid locations, 224 needs to be replaced
PHY-3001 : design contains 19577 instances, 5673 luts, 12313 seqs, 1496 slices, 293 macros(1496 instances: 977 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 472939
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17134/22051.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 547640, over cnt = 2499(7%), over = 8599, worst = 21
PHY-1001 : End global iterations;  0.200670s wall, 0.375000s user + 0.015625s system = 0.390625s CPU (194.7%)

PHY-1001 : Congestion index: top1 = 55.04, top5 = 45.91, top10 = 41.67, top15 = 38.92.
PHY-3001 : End congestion estimation;  0.464009s wall, 0.640625s user + 0.015625s system = 0.656250s CPU (141.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81226, tnet num: 22049, tinst num: 19577, tnode num: 114301, tedge num: 127650.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.591237s wall, 1.546875s user + 0.046875s system = 1.593750s CPU (100.2%)

RUN-1004 : used memory is 618 MB, reserved memory is 621 MB, peak memory is 700 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22049 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.722170s wall, 2.640625s user + 0.078125s system = 2.718750s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(177): len = 473272, overlap = 1.5
PHY-3002 : Step(178): len = 474529, overlap = 1.375
PHY-3002 : Step(179): len = 475479, overlap = 1.375
PHY-3002 : Step(180): len = 476116, overlap = 1.375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17167/22051.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 548560, over cnt = 2521(7%), over = 8694, worst = 21
PHY-1001 : End global iterations;  0.207589s wall, 0.265625s user + 0.031250s system = 0.296875s CPU (143.0%)

PHY-1001 : Congestion index: top1 = 55.71, top5 = 46.21, top10 = 41.98, top15 = 39.16.
PHY-3001 : End congestion estimation;  0.478648s wall, 0.546875s user + 0.031250s system = 0.578125s CPU (120.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22049 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.081651s wall, 1.078125s user + 0.000000s system = 1.078125s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000623102
PHY-3002 : Step(181): len = 476173, overlap = 129
PHY-3002 : Step(182): len = 476467, overlap = 128.781
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0012462
PHY-3002 : Step(183): len = 476533, overlap = 129.188
PHY-3002 : Step(184): len = 476856, overlap = 129.469
PHY-3001 : Final: Len = 476856, Over = 129.469
PHY-3001 : End incremental placement;  5.699784s wall, 6.375000s user + 0.265625s system = 6.640625s CPU (116.5%)

OPT-1001 : Total overflow 491.47 peak overflow 4.41
OPT-1001 : End high-fanout net optimization;  8.910102s wall, 10.562500s user + 0.281250s system = 10.843750s CPU (121.7%)

OPT-1001 : Current memory(MB): used = 714, reserve = 699, peak = 721.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17157/22051.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 550136, over cnt = 2475(7%), over = 8132, worst = 21
PHY-1002 : len = 585704, over cnt = 1772(5%), over = 4678, worst = 19
PHY-1002 : len = 622504, over cnt = 709(2%), over = 1837, worst = 19
PHY-1002 : len = 643896, over cnt = 256(0%), over = 625, worst = 12
PHY-1002 : len = 653608, over cnt = 6(0%), over = 9, worst = 4
PHY-1001 : End global iterations;  1.433239s wall, 2.125000s user + 0.046875s system = 2.171875s CPU (151.5%)

PHY-1001 : Congestion index: top1 = 48.02, top5 = 42.45, top10 = 39.53, top15 = 37.61.
OPT-1001 : End congestion update;  1.716012s wall, 2.390625s user + 0.046875s system = 2.437500s CPU (142.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22049 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.118162s wall, 1.125000s user + 0.000000s system = 1.125000s CPU (100.6%)

OPT-0007 : Start: WNS 4261 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.840577s wall, 3.515625s user + 0.046875s system = 3.562500s CPU (125.4%)

OPT-1001 : Current memory(MB): used = 714, reserve = 699, peak = 721.
OPT-1001 : End physical optimization;  13.714378s wall, 16.171875s user + 0.359375s system = 16.531250s CPU (120.5%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5673 LUT to BLE ...
SYN-4008 : Packed 5673 LUT and 2732 SEQ to BLE.
SYN-4003 : Packing 9581 remaining SEQ's ...
SYN-4005 : Packed 3348 SEQ with LUT/SLICE
SYN-4006 : 124 single LUT's are left
SYN-4006 : 6233 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11906/13763 primitive instances ...
PHY-3001 : End packing;  3.117850s wall, 3.109375s user + 0.000000s system = 3.109375s CPU (99.7%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8106 instances
RUN-1001 : 4004 mslices, 4005 lslices, 59 pads, 33 brams, 0 dsps
RUN-1001 : There are total 19371 nets
RUN-1001 : 13607 nets have 2 pins
RUN-1001 : 4365 nets have [3 - 5] pins
RUN-1001 : 885 nets have [6 - 10] pins
RUN-1001 : 371 nets have [11 - 20] pins
RUN-1001 : 134 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8104 instances, 8009 slices, 293 macros(1496 instances: 977 mslices 519 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 493460, Over = 352
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7737/19371.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 620784, over cnt = 1545(4%), over = 2518, worst = 8
PHY-1002 : len = 627944, over cnt = 938(2%), over = 1302, worst = 8
PHY-1002 : len = 636280, over cnt = 544(1%), over = 705, worst = 7
PHY-1002 : len = 643256, over cnt = 236(0%), over = 303, worst = 7
PHY-1002 : len = 647520, over cnt = 47(0%), over = 52, worst = 2
PHY-1001 : End global iterations;  1.262666s wall, 1.921875s user + 0.000000s system = 1.921875s CPU (152.2%)

PHY-1001 : Congestion index: top1 = 49.35, top5 = 42.90, top10 = 39.42, top15 = 37.31.
PHY-3001 : End congestion estimation;  1.610340s wall, 2.281250s user + 0.000000s system = 2.281250s CPU (141.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67318, tnet num: 19369, tinst num: 8104, tnode num: 91361, tedge num: 110981.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.776466s wall, 1.765625s user + 0.015625s system = 1.781250s CPU (100.3%)

RUN-1004 : used memory is 610 MB, reserved memory is 616 MB, peak memory is 721 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19369 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.766998s wall, 2.687500s user + 0.078125s system = 2.765625s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.73251e-05
PHY-3002 : Step(185): len = 497887, overlap = 347.25
PHY-3002 : Step(186): len = 498694, overlap = 353.5
PHY-3002 : Step(187): len = 499389, overlap = 364.25
PHY-3002 : Step(188): len = 498516, overlap = 383.75
PHY-3002 : Step(189): len = 496803, overlap = 388.75
PHY-3002 : Step(190): len = 496013, overlap = 391.5
PHY-3002 : Step(191): len = 494724, overlap = 391.75
PHY-3002 : Step(192): len = 493370, overlap = 395.75
PHY-3002 : Step(193): len = 491637, overlap = 396.25
PHY-3002 : Step(194): len = 490112, overlap = 396
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.46502e-05
PHY-3002 : Step(195): len = 494876, overlap = 380.5
PHY-3002 : Step(196): len = 499171, overlap = 368.25
PHY-3002 : Step(197): len = 499774, overlap = 360.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0001893
PHY-3002 : Step(198): len = 506554, overlap = 347.75
PHY-3002 : Step(199): len = 517833, overlap = 334
PHY-3002 : Step(200): len = 517910, overlap = 328.25
PHY-3002 : Step(201): len = 516713, overlap = 332
PHY-3002 : Step(202): len = 516315, overlap = 331
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.771718s wall, 0.812500s user + 0.890625s system = 1.703125s CPU (220.7%)

PHY-3001 : Trial Legalized: Len = 619647
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 527/19371.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 704488, over cnt = 2330(6%), over = 3825, worst = 9
PHY-1002 : len = 718312, over cnt = 1415(4%), over = 1974, worst = 7
PHY-1002 : len = 736528, over cnt = 464(1%), over = 596, worst = 6
PHY-1002 : len = 741480, over cnt = 242(0%), over = 299, worst = 5
PHY-1002 : len = 747760, over cnt = 1(0%), over = 1, worst = 1
PHY-1001 : End global iterations;  2.055954s wall, 3.484375s user + 0.078125s system = 3.562500s CPU (173.3%)

PHY-1001 : Congestion index: top1 = 51.10, top5 = 45.51, top10 = 42.64, top15 = 40.72.
PHY-3001 : End congestion estimation;  2.438785s wall, 3.875000s user + 0.078125s system = 3.953125s CPU (162.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19369 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.957953s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00017344
PHY-3002 : Step(203): len = 579199, overlap = 79.75
PHY-3002 : Step(204): len = 561675, overlap = 126
PHY-3002 : Step(205): len = 550709, overlap = 157.75
PHY-3002 : Step(206): len = 544094, overlap = 207.25
PHY-3002 : Step(207): len = 540893, overlap = 226.25
PHY-3002 : Step(208): len = 538361, overlap = 240.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00034688
PHY-3002 : Step(209): len = 542369, overlap = 237.5
PHY-3002 : Step(210): len = 546514, overlap = 230.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00069376
PHY-3002 : Step(211): len = 549398, overlap = 228.25
PHY-3002 : Step(212): len = 555912, overlap = 223.75
PHY-3002 : Step(213): len = 556691, overlap = 226.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.032237s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (96.9%)

PHY-3001 : Legalized: Len = 597019, Over = 0
PHY-3001 : Spreading special nets. 28 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.090270s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (103.9%)

PHY-3001 : 42 instances has been re-located, deltaX = 26, deltaY = 19, maxDist = 2.
PHY-3001 : Final: Len = 597599, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67318, tnet num: 19369, tinst num: 8104, tnode num: 91361, tedge num: 110981.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.145004s wall, 2.109375s user + 0.031250s system = 2.140625s CPU (99.8%)

RUN-1004 : used memory is 626 MB, reserved memory is 614 MB, peak memory is 721 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 4191/19371.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 691488, over cnt = 2109(5%), over = 3291, worst = 8
PHY-1002 : len = 703616, over cnt = 1149(3%), over = 1523, worst = 8
PHY-1002 : len = 715104, over cnt = 485(1%), over = 668, worst = 8
PHY-1002 : len = 724696, over cnt = 66(0%), over = 78, worst = 2
PHY-1002 : len = 726376, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.861981s wall, 3.015625s user + 0.062500s system = 3.078125s CPU (165.3%)

PHY-1001 : Congestion index: top1 = 49.33, top5 = 43.49, top10 = 40.54, top15 = 38.75.
PHY-1001 : End incremental global routing;  2.210865s wall, 3.343750s user + 0.062500s system = 3.406250s CPU (154.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19369 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.002604s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (99.7%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8040 has valid locations, 13 needs to be replaced
PHY-3001 : design contains 8115 instances, 8020 slices, 293 macros(1496 instances: 977 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 601235
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17413/19389.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 730352, over cnt = 33(0%), over = 45, worst = 5
PHY-1002 : len = 730456, over cnt = 13(0%), over = 16, worst = 4
PHY-1002 : len = 730512, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 730528, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 730560, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.731664s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (102.5%)

PHY-1001 : Congestion index: top1 = 49.66, top5 = 43.73, top10 = 40.70, top15 = 38.93.
PHY-3001 : End congestion estimation;  1.054314s wall, 1.062500s user + 0.000000s system = 1.062500s CPU (100.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67422, tnet num: 19387, tinst num: 8115, tnode num: 91490, tedge num: 111139.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.154198s wall, 2.156250s user + 0.000000s system = 2.156250s CPU (100.1%)

RUN-1004 : used memory is 666 MB, reserved memory is 656 MB, peak memory is 721 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19387 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.156850s wall, 3.140625s user + 0.015625s system = 3.156250s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(214): len = 601005, overlap = 0
PHY-3002 : Step(215): len = 600782, overlap = 0
PHY-3002 : Step(216): len = 600709, overlap = 0.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17407/19389.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 728808, over cnt = 29(0%), over = 34, worst = 3
PHY-1002 : len = 728848, over cnt = 12(0%), over = 12, worst = 1
PHY-1002 : len = 728928, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 728976, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.577512s wall, 0.593750s user + 0.000000s system = 0.593750s CPU (102.8%)

PHY-1001 : Congestion index: top1 = 49.22, top5 = 43.55, top10 = 40.61, top15 = 38.82.
PHY-3001 : End congestion estimation;  0.956016s wall, 0.984375s user + 0.000000s system = 0.984375s CPU (103.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19387 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.971414s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000889027
PHY-3002 : Step(217): len = 600687, overlap = 1.5
PHY-3002 : Step(218): len = 600674, overlap = 2
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006786s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (230.2%)

PHY-3001 : Legalized: Len = 600726, Over = 0
PHY-3001 : End spreading;  0.073298s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (85.3%)

PHY-3001 : Final: Len = 600726, Over = 0
PHY-3001 : End incremental placement;  6.816000s wall, 6.796875s user + 0.093750s system = 6.890625s CPU (101.1%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.561808s wall, 11.843750s user + 0.156250s system = 12.000000s CPU (113.6%)

OPT-1001 : Current memory(MB): used = 713, reserve = 694, peak = 721.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17408/19389.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 728888, over cnt = 19(0%), over = 27, worst = 5
PHY-1002 : len = 729008, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 729040, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 729136, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.560902s wall, 0.640625s user + 0.015625s system = 0.656250s CPU (117.0%)

PHY-1001 : Congestion index: top1 = 49.22, top5 = 43.51, top10 = 40.55, top15 = 38.78.
OPT-1001 : End congestion update;  0.882408s wall, 0.953125s user + 0.015625s system = 0.968750s CPU (109.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19387 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.830053s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (99.8%)

OPT-0007 : Start: WNS 4259 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.717388s wall, 1.796875s user + 0.015625s system = 1.812500s CPU (105.5%)

OPT-1001 : Current memory(MB): used = 714, reserve = 694, peak = 721.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19387 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.825501s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (98.4%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17425/19389.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 729136, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.127479s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (98.1%)

PHY-1001 : Congestion index: top1 = 49.22, top5 = 43.51, top10 = 40.55, top15 = 38.78.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19387 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.833905s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (101.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4259 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 48.793103
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4259ps with logic level 5 
RUN-1001 :       #2 path slack 4355ps with logic level 5 
OPT-1001 : End physical optimization;  16.820300s wall, 18.265625s user + 0.250000s system = 18.515625s CPU (110.1%)

RUN-1003 : finish command "place" in  76.026108s wall, 150.171875s user + 7.640625s system = 157.812500s CPU (207.6%)

RUN-1004 : used memory is 630 MB, reserved memory is 615 MB, peak memory is 721 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.729115s wall, 3.015625s user + 0.000000s system = 3.015625s CPU (174.4%)

RUN-1004 : used memory is 630 MB, reserved memory is 615 MB, peak memory is 721 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8117 instances
RUN-1001 : 4015 mslices, 4005 lslices, 59 pads, 33 brams, 0 dsps
RUN-1001 : There are total 19389 nets
RUN-1001 : 13611 nets have 2 pins
RUN-1001 : 4365 nets have [3 - 5] pins
RUN-1001 : 893 nets have [6 - 10] pins
RUN-1001 : 377 nets have [11 - 20] pins
RUN-1001 : 134 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67422, tnet num: 19387, tinst num: 8115, tnode num: 91490, tedge num: 111139.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.787991s wall, 1.781250s user + 0.000000s system = 1.781250s CPU (99.6%)

RUN-1004 : used memory is 615 MB, reserved memory is 602 MB, peak memory is 721 MB
PHY-1001 : 4015 mslices, 4005 lslices, 59 pads, 33 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19387 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 672392, over cnt = 2247(6%), over = 3708, worst = 9
PHY-1002 : len = 689024, over cnt = 1199(3%), over = 1685, worst = 9
PHY-1002 : len = 700848, over cnt = 595(1%), over = 822, worst = 6
PHY-1002 : len = 715432, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 715496, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.880529s wall, 3.109375s user + 0.015625s system = 3.125000s CPU (166.2%)

PHY-1001 : Congestion index: top1 = 48.38, top5 = 43.11, top10 = 40.27, top15 = 38.44.
PHY-1001 : End global routing;  2.242069s wall, 3.468750s user + 0.015625s system = 3.484375s CPU (155.4%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 700, reserve = 685, peak = 721.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 968, reserve = 953, peak = 968.
PHY-1001 : End build detailed router design. 4.927343s wall, 4.890625s user + 0.031250s system = 4.921875s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 191640, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.962211s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (100.7%)

PHY-1001 : Current memory(MB): used = 1003, reserve = 988, peak = 1003.
PHY-1001 : End phase 1; 0.970343s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (99.8%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.68758e+06, over cnt = 1243(0%), over = 1249, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1017, reserve = 1004, peak = 1017.
PHY-1001 : End initial routed; 17.514114s wall, 45.312500s user + 0.359375s system = 45.671875s CPU (260.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18125(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.352   |   0.000   |   0   
RUN-1001 :   Hold   |   0.112   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.580324s wall, 3.562500s user + 0.000000s system = 3.562500s CPU (99.5%)

PHY-1001 : Current memory(MB): used = 1026, reserve = 1013, peak = 1026.
PHY-1001 : End phase 2; 21.094602s wall, 48.875000s user + 0.359375s system = 49.234375s CPU (233.4%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.68758e+06, over cnt = 1243(0%), over = 1249, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.257751s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (97.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.67764e+06, over cnt = 378(0%), over = 379, worst = 2, crit = 0
PHY-1001 : End DR Iter 1; 0.666880s wall, 1.265625s user + 0.000000s system = 1.265625s CPU (189.8%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.67784e+06, over cnt = 94(0%), over = 94, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.400197s wall, 0.468750s user + 0.000000s system = 0.468750s CPU (117.1%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.67867e+06, over cnt = 22(0%), over = 22, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.233775s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (106.9%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.67908e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.230759s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (101.6%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.67913e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.167380s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (102.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18125(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.176   |   0.000   |   0   
RUN-1001 :   Hold   |   0.112   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.615283s wall, 3.625000s user + 0.000000s system = 3.625000s CPU (100.3%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 279 feed throughs used by 242 nets
PHY-1001 : End commit to database; 2.261556s wall, 2.250000s user + 0.015625s system = 2.265625s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 1117, reserve = 1107, peak = 1117.
PHY-1001 : End phase 3; 8.361771s wall, 9.046875s user + 0.015625s system = 9.062500s CPU (108.4%)

PHY-1003 : Routed, final wirelength = 1.67913e+06
PHY-1001 : Current memory(MB): used = 1121, reserve = 1111, peak = 1121.
PHY-1001 : End export database. 0.064158s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (97.4%)

PHY-1001 : End detail routing;  35.868147s wall, 64.281250s user + 0.421875s system = 64.703125s CPU (180.4%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67422, tnet num: 19387, tinst num: 8115, tnode num: 91490, tedge num: 111139.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.800512s wall, 1.796875s user + 0.000000s system = 1.796875s CPU (99.8%)

RUN-1004 : used memory is 1053 MB, reserved memory is 1051 MB, peak memory is 1121 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  44.491552s wall, 74.125000s user + 0.453125s system = 74.578125s CPU (167.6%)

RUN-1004 : used memory is 1054 MB, reserved memory is 1052 MB, peak memory is 1121 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8821   out of  19600   45.01%
#reg                    12418   out of  19600   63.36%
#le                     15000
  #lut only              2582   out of  15000   17.21%
  #reg only              6179   out of  15000   41.19%
  #lut&reg               6239   out of  15000   41.59%
#dsp                        0   out of     29    0.00%
#bram                      33   out of     64   51.56%
  #bram9k                  31
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       59   out of    188   31.38%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6805
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          146
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         F2        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         D1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         B3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         C1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         A3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         A2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         B2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         B1        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F15        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDZ        INPUT        K15        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         P2        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         N4        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         M5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         N6        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         N5        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         P5        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M3        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         M4        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         H2        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         M1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         L1        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         N1        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         N3        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         M2        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT         T5        LVCMOS33           8            NONE           OREG       
    Fog_ENY        OUTPUT         T4        LVCMOS33           8            NONE           OREG       
    Fog_ENZ        OUTPUT         T7        LVCMOS33           8            NONE           OREG       
    Fog_INT        OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         T6        LVCMOS33           8            N/A            OREG       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E1        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        G11        LVCMOS25           8            N/A            NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15000  |7325    |1496    |12462   |33      |0       |
|  AnyFog_dataX                      |AnyFog          |209    |77      |22      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |53      |22      |50      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |206    |73      |22      |168     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |83     |52      |22      |45      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |208    |88      |22      |170     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |55      |22      |48      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2908   |647     |39      |2832    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |63     |35      |5       |54      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |216    |67      |5       |206     |0       |0       |
|    STADOP_com2                     |STADOP          |536    |95      |0       |534     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |45      |14      |43      |0       |0       |
|    head_com2                       |uniheading      |265    |69      |5       |255     |0       |0       |
|    rmc_com2                        |Gprmc           |39     |39      |0       |33      |0       |0       |
|    uart_com2                       |Agrica          |1429   |293     |10      |1412    |0       |0       |
|  COM3                              |COM3_Control    |281    |145     |19      |239     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |61     |35      |5       |52      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |62     |46      |14      |39      |0       |0       |
|    rmc_com3                        |Gprmc           |158    |64      |0       |148     |0       |0       |
|  DATA                              |Data_Processing |8825   |4533    |1122    |7035    |0       |0       |
|    DIV_Dtemp                       |Divider         |852    |319     |84      |727     |0       |0       |
|    DIV_Utemp                       |Divider         |633    |283     |84      |505     |0       |0       |
|    DIV_accX                        |Divider         |567    |270     |84      |435     |0       |0       |
|    DIV_accY                        |Divider         |599    |355     |102     |442     |0       |0       |
|    DIV_accZ                        |Divider         |655    |402     |132     |449     |0       |0       |
|    DIV_rateX                       |Divider         |640    |351     |132     |437     |0       |0       |
|    DIV_rateY                       |Divider         |630    |355     |132     |426     |0       |0       |
|    DIV_rateZ                       |Divider         |572    |380     |132     |367     |0       |0       |
|    genclk                          |genclk          |276    |179     |89      |108     |0       |0       |
|  FMC                               |FMC_Ctrl        |455    |403     |43      |343     |0       |0       |
|  IIC                               |I2C_master      |297    |262     |11      |262     |0       |0       |
|  IMU_CTRL                          |SCHA634         |913    |724     |61      |737     |0       |0       |
|    CtrlData                        |CtrlData        |483    |432     |47      |340     |0       |0       |
|      usms                          |Time_1ms        |30     |25      |5       |22      |0       |0       |
|    SPIM                            |SPI_SCHA634     |430    |292     |14      |397     |0       |0       |
|  POWER                             |POWER_EN        |98     |51      |38      |39      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |584    |322     |97      |404     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |584    |322     |97      |404     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |263    |116     |0       |247     |0       |0       |
|        reg_inst                    |register        |262    |115     |0       |246     |0       |0       |
|        tap_inst                    |tap             |1      |1       |0       |1       |0       |0       |
|      trigger_inst                  |trigger         |321    |206     |97      |157     |0       |0       |
|        bus_inst                    |bus_top         |113    |72      |40      |47      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |27     |17      |10      |11      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |3      |2       |0       |3       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |14     |8       |6       |5       |0       |0       |
|          BUS_DETECTOR[5]$bus_nodes |bus_det         |14     |8       |6       |5       |0       |0       |
|          BUS_DETECTOR[6]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[7]$bus_nodes |bus_det         |52     |34      |18      |20      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |126    |91      |29      |75      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13551  
    #2          2       3404   
    #3          3        656   
    #4          4        305   
    #5        5-10       956   
    #6        11-50      442   
    #7       51-100       6    
    #8       101-500      3    
    #9        >500        2    
  Average     2.12             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.197354s wall, 3.765625s user + 0.015625s system = 3.781250s CPU (172.1%)

RUN-1004 : used memory is 1055 MB, reserved memory is 1053 MB, peak memory is 1121 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67422, tnet num: 19387, tinst num: 8115, tnode num: 91490, tedge num: 111139.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.823987s wall, 1.828125s user + 0.000000s system = 1.828125s CPU (100.2%)

RUN-1004 : used memory is 1057 MB, reserved memory is 1056 MB, peak memory is 1121 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19387 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.505977s wall, 1.484375s user + 0.015625s system = 1.500000s CPU (99.6%)

RUN-1004 : used memory is 1063 MB, reserved memory is 1060 MB, peak memory is 1121 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 5914bebc17a1c31f442cd7128357dba2c23fce68140dc4f57a917e3b4f582a0e -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8115
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19389, pip num: 145479
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 279
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3230 valid insts, and 408650 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011100100000011010100101
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.016217s wall, 119.734375s user + 0.140625s system = 119.875000s CPU (997.6%)

RUN-1004 : used memory is 1188 MB, reserved memory is 1173 MB, peak memory is 1303 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250821_111141.log"

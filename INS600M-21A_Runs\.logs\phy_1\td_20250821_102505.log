============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Aug 21 10:25:05 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.858102s wall, 1.640625s user + 4.203125s system = 5.843750s CPU (99.8%)

RUN-1004 : used memory is 79 MB, reserved memory is 40 MB, peak memory is 90 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.938880s wall, 1.828125s user + 0.078125s system = 1.906250s CPU (98.3%)

RUN-1004 : used memory is 302 MB, reserved memory is 271 MB, peak memory is 305 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 27 trigger nets, 27 data nets.
KIT-1004 : Chipwatcher code = 1011100110110011
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=27,BUS_CTRL_NUM=74,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=96) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=96) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=27,BUS_CTRL_NUM=74,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=27,BUS_CTRL_NUM=74,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=27,BUS_CTRL_NUM=74,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=96)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=96)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=27,BUS_CTRL_NUM=74,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=27,BUS_CTRL_NUM=74,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22555/21 useful/useless nets, 19437/13 useful/useless insts
SYN-1016 : Merged 29 instances.
SYN-1032 : 22283/22 useful/useless nets, 19788/18 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 2 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 2 mux instances.
SYN-1015 : Optimize round 1, 321 better
SYN-1014 : Optimize round 2
SYN-1032 : 22041/30 useful/useless nets, 19546/32 useful/useless insts
SYN-1015 : Optimize round 2, 64 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.514490s wall, 2.484375s user + 0.031250s system = 2.515625s CPU (100.0%)

RUN-1004 : used memory is 327 MB, reserved memory is 294 MB, peak memory is 329 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22077/221 useful/useless nets, 19605/32 useful/useless insts
SYN-1016 : Merged 33 instances.
SYN-2571 : Optimize after map_dsp, round 1, 286 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 24 instances.
SYN-2501 : Optimize round 1, 50 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 14 macro adder
SYN-1019 : Optimized 9 mux instances.
SYN-1016 : Merged 10 instances.
SYN-1032 : 22454/5 useful/useless nets, 19982/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81549, tnet num: 22454, tinst num: 19981, tnode num: 114340, tedge num: 127523.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.288798s wall, 1.171875s user + 0.109375s system = 1.281250s CPU (99.4%)

RUN-1004 : used memory is 466 MB, reserved memory is 434 MB, peak memory is 466 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22454 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 188 (3.61), #lev = 7 (1.95)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 188 (3.61), #lev = 7 (1.95)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 462 instances into 188 LUTs, name keeping = 75%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 332 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 108 adder to BLE ...
SYN-4008 : Packed 108 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.881960s wall, 4.687500s user + 0.156250s system = 4.843750s CPU (99.2%)

RUN-1004 : used memory is 347 MB, reserved memory is 327 MB, peak memory is 573 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.743192s wall, 7.468750s user + 0.234375s system = 7.703125s CPU (99.5%)

RUN-1004 : used memory is 348 MB, reserved memory is 327 MB, peak memory is 573 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (210 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19270 instances
RUN-0007 : 5567 luts, 12125 seqs, 973 mslices, 515 lslices, 59 pads, 26 brams, 0 dsps
RUN-1001 : There are total 21750 nets
RUN-1001 : 16374 nets have 2 pins
RUN-1001 : 4191 nets have [3 - 5] pins
RUN-1001 : 819 nets have [6 - 10] pins
RUN-1001 : 240 nets have [11 - 20] pins
RUN-1001 : 107 nets have [21 - 99] pins
RUN-1001 : 19 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4784     
RUN-1001 :   No   |  No   |  Yes  |     690     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     382     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19268 instances, 5567 luts, 12125 seqs, 1488 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80027, tnet num: 21748, tinst num: 19268, tnode num: 112534, tedge num: 125859.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.269726s wall, 1.187500s user + 0.078125s system = 1.265625s CPU (99.7%)

RUN-1004 : used memory is 525 MB, reserved memory is 496 MB, peak memory is 573 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21748 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.204430s wall, 2.109375s user + 0.093750s system = 2.203125s CPU (99.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.63241e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19268.
PHY-3001 : Level 1 #clusters 2173.
PHY-3001 : End clustering;  0.167764s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (121.1%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 876446, overlap = 612.375
PHY-3002 : Step(2): len = 788185, overlap = 658.344
PHY-3002 : Step(3): len = 511126, overlap = 858.781
PHY-3002 : Step(4): len = 457890, overlap = 917
PHY-3002 : Step(5): len = 350939, overlap = 1012.94
PHY-3002 : Step(6): len = 315316, overlap = 1069.81
PHY-3002 : Step(7): len = 264788, overlap = 1147.59
PHY-3002 : Step(8): len = 237128, overlap = 1198.88
PHY-3002 : Step(9): len = 208510, overlap = 1244.88
PHY-3002 : Step(10): len = 191487, overlap = 1289.47
PHY-3002 : Step(11): len = 176037, overlap = 1329.81
PHY-3002 : Step(12): len = 157542, overlap = 1357.97
PHY-3002 : Step(13): len = 147744, overlap = 1375.12
PHY-3002 : Step(14): len = 133697, overlap = 1397.59
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.67851e-07
PHY-3002 : Step(15): len = 136410, overlap = 1388.09
PHY-3002 : Step(16): len = 167286, overlap = 1337.88
PHY-3002 : Step(17): len = 174639, overlap = 1242.88
PHY-3002 : Step(18): len = 176972, overlap = 1184.88
PHY-3002 : Step(19): len = 176387, overlap = 1157.78
PHY-3002 : Step(20): len = 170480, overlap = 1137.66
PHY-3002 : Step(21): len = 167154, overlap = 1124.56
PHY-3002 : Step(22): len = 162853, overlap = 1116.59
PHY-3002 : Step(23): len = 159184, overlap = 1107.44
PHY-3002 : Step(24): len = 156109, overlap = 1111.47
PHY-3002 : Step(25): len = 155093, overlap = 1102.62
PHY-3002 : Step(26): len = 153172, overlap = 1084.75
PHY-3002 : Step(27): len = 153085, overlap = 1066.47
PHY-3002 : Step(28): len = 152666, overlap = 1074.56
PHY-3002 : Step(29): len = 152920, overlap = 1061.81
PHY-3002 : Step(30): len = 152490, overlap = 1052.56
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 1.9357e-06
PHY-3002 : Step(31): len = 158616, overlap = 1025.53
PHY-3002 : Step(32): len = 176933, overlap = 975.438
PHY-3002 : Step(33): len = 182637, overlap = 926.594
PHY-3002 : Step(34): len = 185352, overlap = 912.812
PHY-3002 : Step(35): len = 185154, overlap = 900.75
PHY-3002 : Step(36): len = 183999, overlap = 922.906
PHY-3002 : Step(37): len = 181048, overlap = 935
PHY-3002 : Step(38): len = 180263, overlap = 946.906
PHY-3002 : Step(39): len = 178280, overlap = 930.938
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 3.8714e-06
PHY-3002 : Step(40): len = 189424, overlap = 877.781
PHY-3002 : Step(41): len = 205410, overlap = 808.781
PHY-3002 : Step(42): len = 209478, overlap = 768.031
PHY-3002 : Step(43): len = 210488, overlap = 761.844
PHY-3002 : Step(44): len = 210250, overlap = 767.25
PHY-3002 : Step(45): len = 208816, overlap = 771.062
PHY-3002 : Step(46): len = 207237, overlap = 778.688
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 7.74281e-06
PHY-3002 : Step(47): len = 219108, overlap = 729.344
PHY-3002 : Step(48): len = 232383, overlap = 644.562
PHY-3002 : Step(49): len = 236254, overlap = 602.125
PHY-3002 : Step(50): len = 239121, overlap = 586.969
PHY-3002 : Step(51): len = 238736, overlap = 571.094
PHY-3002 : Step(52): len = 237700, overlap = 558.688
PHY-3002 : Step(53): len = 235606, overlap = 564.781
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.54856e-05
PHY-3002 : Step(54): len = 248811, overlap = 552.812
PHY-3002 : Step(55): len = 261744, overlap = 521.531
PHY-3002 : Step(56): len = 265273, overlap = 465
PHY-3002 : Step(57): len = 265730, overlap = 448.125
PHY-3002 : Step(58): len = 264077, overlap = 447.75
PHY-3002 : Step(59): len = 262914, overlap = 453
PHY-3002 : Step(60): len = 261245, overlap = 463.125
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.09712e-05
PHY-3002 : Step(61): len = 271341, overlap = 438.062
PHY-3002 : Step(62): len = 283678, overlap = 397.5
PHY-3002 : Step(63): len = 288708, overlap = 393.5
PHY-3002 : Step(64): len = 288866, overlap = 392.938
PHY-3002 : Step(65): len = 286546, overlap = 403.312
PHY-3002 : Step(66): len = 284294, overlap = 408.812
PHY-3002 : Step(67): len = 282363, overlap = 412.375
PHY-3002 : Step(68): len = 282068, overlap = 414.438
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 6.19425e-05
PHY-3002 : Step(69): len = 290930, overlap = 396.594
PHY-3002 : Step(70): len = 300035, overlap = 350.531
PHY-3002 : Step(71): len = 303458, overlap = 328.062
PHY-3002 : Step(72): len = 304337, overlap = 327.062
PHY-3002 : Step(73): len = 302324, overlap = 332.781
PHY-3002 : Step(74): len = 300508, overlap = 334.531
PHY-3002 : Step(75): len = 298901, overlap = 327.688
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000123885
PHY-3002 : Step(76): len = 305184, overlap = 310.406
PHY-3002 : Step(77): len = 310780, overlap = 297.969
PHY-3002 : Step(78): len = 313492, overlap = 294.875
PHY-3002 : Step(79): len = 315337, overlap = 291.75
PHY-3002 : Step(80): len = 314943, overlap = 295.656
PHY-3002 : Step(81): len = 314578, overlap = 288.438
PHY-3002 : Step(82): len = 312568, overlap = 289.469
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000233985
PHY-3002 : Step(83): len = 315536, overlap = 273.156
PHY-3002 : Step(84): len = 319944, overlap = 260.062
PHY-3002 : Step(85): len = 321944, overlap = 245.156
PHY-3002 : Step(86): len = 323263, overlap = 234.062
PHY-3002 : Step(87): len = 323142, overlap = 225.688
PHY-3002 : Step(88): len = 322812, overlap = 231.625
PHY-3002 : Step(89): len = 322879, overlap = 220.844
PHY-3002 : Step(90): len = 323198, overlap = 215.812
PHY-3002 : Step(91): len = 323480, overlap = 214.406
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000442452
PHY-3002 : Step(92): len = 324586, overlap = 215
PHY-3002 : Step(93): len = 328541, overlap = 201.812
PHY-3002 : Step(94): len = 330393, overlap = 199.875
PHY-3001 : :::11::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(95): len = 331180, overlap = 188.688
PHY-3002 : Step(96): len = 333451, overlap = 183.656
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.015792s wall, 0.000000s user + 0.031250s system = 0.031250s CPU (197.9%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21750.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 435248, over cnt = 1263(3%), over = 5377, worst = 35
PHY-1001 : End global iterations;  0.826106s wall, 1.312500s user + 0.015625s system = 1.328125s CPU (160.8%)

PHY-1001 : Congestion index: top1 = 70.45, top5 = 51.38, top10 = 42.23, top15 = 36.90.
PHY-3001 : End congestion estimation;  1.055084s wall, 1.531250s user + 0.031250s system = 1.562500s CPU (148.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21748 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.980589s wall, 0.937500s user + 0.046875s system = 0.984375s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000104213
PHY-3002 : Step(97): len = 373914, overlap = 158.406
PHY-3002 : Step(98): len = 379888, overlap = 139.844
PHY-3002 : Step(99): len = 381707, overlap = 139.156
PHY-3002 : Step(100): len = 384746, overlap = 129.719
PHY-3002 : Step(101): len = 393618, overlap = 127.375
PHY-3002 : Step(102): len = 398997, overlap = 114.719
PHY-3002 : Step(103): len = 407145, overlap = 105.25
PHY-3002 : Step(104): len = 411253, overlap = 104.531
PHY-3002 : Step(105): len = 414839, overlap = 106.656
PHY-3002 : Step(106): len = 420169, overlap = 99.5
PHY-3002 : Step(107): len = 421284, overlap = 97.5312
PHY-3002 : Step(108): len = 423731, overlap = 97.3125
PHY-3002 : Step(109): len = 427209, overlap = 96.5
PHY-3002 : Step(110): len = 429632, overlap = 99.9062
PHY-3002 : Step(111): len = 431298, overlap = 103.062
PHY-3002 : Step(112): len = 433951, overlap = 102.781
PHY-3002 : Step(113): len = 434637, overlap = 105.094
PHY-3002 : Step(114): len = 435736, overlap = 109.875
PHY-3002 : Step(115): len = 437751, overlap = 111.469
PHY-3002 : Step(116): len = 437749, overlap = 113.344
PHY-3002 : Step(117): len = 438116, overlap = 114.312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000208426
PHY-3002 : Step(118): len = 438789, overlap = 112.688
PHY-3002 : Step(119): len = 440814, overlap = 111.938
PHY-3002 : Step(120): len = 443035, overlap = 112.531
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(121): len = 446130, overlap = 109.219
PHY-3002 : Step(122): len = 455450, overlap = 101.938
PHY-3002 : Step(123): len = 459613, overlap = 106.125
PHY-3002 : Step(124): len = 459224, overlap = 104.844
PHY-3002 : Step(125): len = 459962, overlap = 107.031
PHY-3002 : Step(126): len = 460772, overlap = 105.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 32/21750.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 523632, over cnt = 2239(6%), over = 10091, worst = 39
PHY-1001 : End global iterations;  1.018718s wall, 1.796875s user + 0.062500s system = 1.859375s CPU (182.5%)

PHY-1001 : Congestion index: top1 = 73.08, top5 = 57.43, top10 = 49.60, top15 = 44.79.
PHY-3001 : End congestion estimation;  1.305424s wall, 2.078125s user + 0.062500s system = 2.140625s CPU (164.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21748 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.182412s wall, 1.171875s user + 0.015625s system = 1.187500s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.75752e-05
PHY-3002 : Step(127): len = 466160, overlap = 383
PHY-3002 : Step(128): len = 472446, overlap = 290.719
PHY-3002 : Step(129): len = 467834, overlap = 263.156
PHY-3002 : Step(130): len = 462492, overlap = 266
PHY-3002 : Step(131): len = 460177, overlap = 249.625
PHY-3002 : Step(132): len = 457694, overlap = 226.469
PHY-3002 : Step(133): len = 453836, overlap = 219.906
PHY-3002 : Step(134): len = 450659, overlap = 215.688
PHY-3002 : Step(135): len = 447591, overlap = 216.094
PHY-3002 : Step(136): len = 445680, overlap = 220.5
PHY-3002 : Step(137): len = 443583, overlap = 217.531
PHY-3002 : Step(138): len = 441050, overlap = 217.562
PHY-3002 : Step(139): len = 441011, overlap = 220.062
PHY-3002 : Step(140): len = 437872, overlap = 218.406
PHY-3002 : Step(141): len = 436379, overlap = 215.469
PHY-3002 : Step(142): len = 434411, overlap = 219.531
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00019515
PHY-3002 : Step(143): len = 434240, overlap = 208.031
PHY-3002 : Step(144): len = 435230, overlap = 200.656
PHY-3002 : Step(145): len = 436283, overlap = 190.281
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000390301
PHY-3002 : Step(146): len = 438836, overlap = 181.469
PHY-3002 : Step(147): len = 446615, overlap = 163.469
PHY-3002 : Step(148): len = 451359, overlap = 164
PHY-3002 : Step(149): len = 451343, overlap = 159.156
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000780601
PHY-3002 : Step(150): len = 451819, overlap = 155.375
PHY-3002 : Step(151): len = 456836, overlap = 150.5
PHY-3002 : Step(152): len = 467221, overlap = 135.344
PHY-3002 : Step(153): len = 470692, overlap = 139.625
PHY-3002 : Step(154): len = 470492, overlap = 136.688
PHY-3002 : Step(155): len = 469725, overlap = 138.969
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00155561
PHY-3002 : Step(156): len = 470556, overlap = 138.688
PHY-3002 : Step(157): len = 472475, overlap = 137.125
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80027, tnet num: 21748, tinst num: 19268, tnode num: 112534, tedge num: 125859.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.551621s wall, 1.515625s user + 0.046875s system = 1.562500s CPU (100.7%)

RUN-1004 : used memory is 565 MB, reserved memory is 540 MB, peak memory is 697 MB
OPT-1001 : Total overflow 491.81 peak overflow 4.31
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 390/21750.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 546240, over cnt = 2461(6%), over = 8351, worst = 21
PHY-1001 : End global iterations;  1.284275s wall, 1.937500s user + 0.031250s system = 1.968750s CPU (153.3%)

PHY-1001 : Congestion index: top1 = 55.67, top5 = 46.19, top10 = 41.56, top15 = 38.71.
PHY-1001 : End incremental global routing;  1.556515s wall, 2.218750s user + 0.031250s system = 2.250000s CPU (144.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21748 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.108000s wall, 1.093750s user + 0.031250s system = 1.125000s CPU (101.5%)

OPT-1001 : 15 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19191 has valid locations, 218 needs to be replaced
PHY-3001 : design contains 19471 instances, 5653 luts, 12242 seqs, 1488 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 487274
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17107/21953.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 558152, over cnt = 2496(7%), over = 8422, worst = 21
PHY-1001 : End global iterations;  0.194353s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (104.5%)

PHY-1001 : Congestion index: top1 = 56.08, top5 = 46.56, top10 = 41.96, top15 = 39.06.
PHY-3001 : End congestion estimation;  0.457452s wall, 0.468750s user + 0.000000s system = 0.468750s CPU (102.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80681, tnet num: 21951, tinst num: 19471, tnode num: 113459, tedge num: 126761.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.604838s wall, 1.562500s user + 0.046875s system = 1.609375s CPU (100.3%)

RUN-1004 : used memory is 608 MB, reserved memory is 601 MB, peak memory is 698 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21951 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.717289s wall, 2.671875s user + 0.046875s system = 2.718750s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(158): len = 487338, overlap = 0.75
PHY-3002 : Step(159): len = 488671, overlap = 0.9375
PHY-3002 : Step(160): len = 489515, overlap = 1.0625
PHY-3002 : Step(161): len = 490029, overlap = 1.0625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17121/21953.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 557944, over cnt = 2492(7%), over = 8454, worst = 21
PHY-1001 : End global iterations;  0.195593s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (135.8%)

PHY-1001 : Congestion index: top1 = 56.29, top5 = 46.63, top10 = 41.90, top15 = 39.14.
PHY-3001 : End congestion estimation;  0.455665s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (113.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21951 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.101529s wall, 1.046875s user + 0.046875s system = 1.093750s CPU (99.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000661469
PHY-3002 : Step(162): len = 489743, overlap = 139.844
PHY-3002 : Step(163): len = 489823, overlap = 139.781
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00132294
PHY-3002 : Step(164): len = 490246, overlap = 140.125
PHY-3002 : Step(165): len = 490477, overlap = 139.938
PHY-3001 : Final: Len = 490477, Over = 139.938
PHY-3001 : End incremental placement;  5.655665s wall, 5.890625s user + 0.328125s system = 6.218750s CPU (110.0%)

OPT-1001 : Total overflow 496.94 peak overflow 4.31
OPT-1001 : End high-fanout net optimization;  8.881878s wall, 9.812500s user + 0.390625s system = 10.203125s CPU (114.9%)

OPT-1001 : Current memory(MB): used = 702, reserve = 682, peak = 719.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17153/21953.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 560136, over cnt = 2452(6%), over = 8029, worst = 21
PHY-1002 : len = 599168, over cnt = 1769(5%), over = 4340, worst = 21
PHY-1002 : len = 632440, over cnt = 763(2%), over = 1822, worst = 21
PHY-1002 : len = 654824, over cnt = 139(0%), over = 317, worst = 14
PHY-1002 : len = 660744, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.271719s wall, 1.968750s user + 0.046875s system = 2.015625s CPU (158.5%)

PHY-1001 : Congestion index: top1 = 48.25, top5 = 42.57, top10 = 39.53, top15 = 37.59.
OPT-1001 : End congestion update;  1.542273s wall, 2.250000s user + 0.046875s system = 2.296875s CPU (148.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 21951 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.909710s wall, 0.906250s user + 0.000000s system = 0.906250s CPU (99.6%)

OPT-0007 : Start: WNS 4245 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.457761s wall, 3.171875s user + 0.046875s system = 3.218750s CPU (131.0%)

OPT-1001 : Current memory(MB): used = 679, reserve = 663, peak = 719.
OPT-1001 : End physical optimization;  13.223288s wall, 14.968750s user + 0.515625s system = 15.484375s CPU (117.1%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5653 LUT to BLE ...
SYN-4008 : Packed 5653 LUT and 2729 SEQ to BLE.
SYN-4003 : Packing 9513 remaining SEQ's ...
SYN-4005 : Packed 3317 SEQ with LUT/SLICE
SYN-4006 : 147 single LUT's are left
SYN-4006 : 6196 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11849/13691 primitive instances ...
PHY-3001 : End packing;  2.884952s wall, 2.875000s user + 0.000000s system = 2.875000s CPU (99.7%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8048 instances
RUN-1001 : 3979 mslices, 3979 lslices, 59 pads, 26 brams, 0 dsps
RUN-1001 : There are total 19277 nets
RUN-1001 : 13564 nets have 2 pins
RUN-1001 : 4317 nets have [3 - 5] pins
RUN-1001 : 885 nets have [6 - 10] pins
RUN-1001 : 370 nets have [11 - 20] pins
RUN-1001 : 132 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8046 instances, 7958 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 505410, Over = 364.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7817/19277.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 627944, over cnt = 1458(4%), over = 2284, worst = 7
PHY-1002 : len = 633392, over cnt = 924(2%), over = 1323, worst = 6
PHY-1002 : len = 643992, over cnt = 416(1%), over = 559, worst = 6
PHY-1002 : len = 649968, over cnt = 137(0%), over = 173, worst = 5
PHY-1002 : len = 653224, over cnt = 1(0%), over = 1, worst = 1
PHY-1001 : End global iterations;  1.240857s wall, 1.718750s user + 0.031250s system = 1.750000s CPU (141.0%)

PHY-1001 : Congestion index: top1 = 49.70, top5 = 42.85, top10 = 39.70, top15 = 37.65.
PHY-3001 : End congestion estimation;  1.577172s wall, 2.062500s user + 0.031250s system = 2.093750s CPU (132.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66857, tnet num: 19275, tinst num: 8046, tnode num: 90656, tedge num: 110284.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.754672s wall, 1.750000s user + 0.000000s system = 1.750000s CPU (99.7%)

RUN-1004 : used memory is 599 MB, reserved memory is 585 MB, peak memory is 719 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19275 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.725840s wall, 2.734375s user + 0.000000s system = 2.734375s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.94763e-05
PHY-3002 : Step(166): len = 510152, overlap = 348
PHY-3002 : Step(167): len = 506763, overlap = 358.75
PHY-3002 : Step(168): len = 505368, overlap = 368.5
PHY-3002 : Step(169): len = 505621, overlap = 375.5
PHY-3002 : Step(170): len = 503415, overlap = 381.25
PHY-3002 : Step(171): len = 502159, overlap = 379
PHY-3002 : Step(172): len = 498823, overlap = 383
PHY-3002 : Step(173): len = 496630, overlap = 383.75
PHY-3002 : Step(174): len = 494033, overlap = 388.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.89526e-05
PHY-3002 : Step(175): len = 497584, overlap = 377.75
PHY-3002 : Step(176): len = 501277, overlap = 370.25
PHY-3002 : Step(177): len = 501832, overlap = 365.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000197905
PHY-3002 : Step(178): len = 506968, overlap = 356.25
PHY-3002 : Step(179): len = 514660, overlap = 341.5
PHY-3002 : Step(180): len = 515794, overlap = 335
PHY-3002 : Step(181): len = 515106, overlap = 332
PHY-3002 : Step(182): len = 515028, overlap = 334.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.864257s wall, 0.937500s user + 1.046875s system = 1.984375s CPU (229.6%)

PHY-3001 : Trial Legalized: Len = 621841
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 561/19277.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 705768, over cnt = 2276(6%), over = 3734, worst = 7
PHY-1002 : len = 716632, over cnt = 1606(4%), over = 2403, worst = 7
PHY-1002 : len = 734736, over cnt = 667(1%), over = 992, worst = 7
PHY-1002 : len = 745864, over cnt = 195(0%), over = 304, worst = 5
PHY-1002 : len = 750944, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.920340s wall, 3.109375s user + 0.031250s system = 3.140625s CPU (163.5%)

PHY-1001 : Congestion index: top1 = 50.06, top5 = 45.00, top10 = 42.25, top15 = 40.49.
PHY-3001 : End congestion estimation;  2.293749s wall, 3.484375s user + 0.031250s system = 3.515625s CPU (153.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19275 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.951400s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000188659
PHY-3002 : Step(183): len = 581578, overlap = 77.5
PHY-3002 : Step(184): len = 562937, overlap = 116.25
PHY-3002 : Step(185): len = 551416, overlap = 155.5
PHY-3002 : Step(186): len = 544726, overlap = 183.5
PHY-3002 : Step(187): len = 540578, overlap = 202.5
PHY-3002 : Step(188): len = 538116, overlap = 210.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000377319
PHY-3002 : Step(189): len = 542384, overlap = 207.25
PHY-3002 : Step(190): len = 547804, overlap = 205.75
PHY-3002 : Step(191): len = 549296, overlap = 210
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000746401
PHY-3002 : Step(192): len = 551794, overlap = 205.5
PHY-3002 : Step(193): len = 558345, overlap = 208.5
PHY-3002 : Step(194): len = 562983, overlap = 213.5
PHY-3002 : Step(195): len = 566201, overlap = 213.75
PHY-3002 : Step(196): len = 568874, overlap = 210.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.035212s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (88.7%)

PHY-3001 : Legalized: Len = 606401, Over = 0
PHY-3001 : Spreading special nets. 33 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.085354s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (91.5%)

PHY-3001 : 51 instances has been re-located, deltaX = 21, deltaY = 22, maxDist = 2.
PHY-3001 : Final: Len = 606791, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66857, tnet num: 19275, tinst num: 8046, tnode num: 90656, tedge num: 110284.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.976263s wall, 1.953125s user + 0.031250s system = 1.984375s CPU (100.4%)

RUN-1004 : used memory is 616 MB, reserved memory is 613 MB, peak memory is 719 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3416/19277.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 697376, over cnt = 2216(6%), over = 3462, worst = 7
PHY-1002 : len = 710712, over cnt = 1197(3%), over = 1568, worst = 5
PHY-1002 : len = 725224, over cnt = 335(0%), over = 403, worst = 4
PHY-1002 : len = 728488, over cnt = 180(0%), over = 199, worst = 3
PHY-1002 : len = 732464, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.876971s wall, 3.000000s user + 0.046875s system = 3.046875s CPU (162.3%)

PHY-1001 : Congestion index: top1 = 49.25, top5 = 43.39, top10 = 40.39, top15 = 38.59.
PHY-1001 : End incremental global routing;  2.198622s wall, 3.312500s user + 0.046875s system = 3.359375s CPU (152.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19275 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.964416s wall, 0.953125s user + 0.015625s system = 0.968750s CPU (100.4%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 7982 has valid locations, 14 needs to be replaced
PHY-3001 : design contains 8058 instances, 7970 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 611173
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17407/19287.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 738688, over cnt = 27(0%), over = 43, worst = 7
PHY-1002 : len = 738800, over cnt = 17(0%), over = 21, worst = 4
PHY-1002 : len = 739024, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 739144, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.578371s wall, 0.625000s user + 0.000000s system = 0.625000s CPU (108.1%)

PHY-1001 : Congestion index: top1 = 49.46, top5 = 43.57, top10 = 40.55, top15 = 38.77.
PHY-3001 : End congestion estimation;  0.891694s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (103.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66975, tnet num: 19285, tinst num: 8058, tnode num: 90793, tedge num: 110425.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.968325s wall, 1.937500s user + 0.031250s system = 1.968750s CPU (100.0%)

RUN-1004 : used memory is 641 MB, reserved memory is 635 MB, peak memory is 719 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19285 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.958054s wall, 2.921875s user + 0.031250s system = 2.953125s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(197): len = 610736, overlap = 0.75
PHY-3002 : Step(198): len = 610291, overlap = 0.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17399/19287.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 737336, over cnt = 50(0%), over = 76, worst = 5
PHY-1002 : len = 737576, over cnt = 33(0%), over = 39, worst = 3
PHY-1002 : len = 737872, over cnt = 12(0%), over = 12, worst = 1
PHY-1002 : len = 737904, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 738064, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.714125s wall, 0.750000s user + 0.046875s system = 0.796875s CPU (111.6%)

PHY-1001 : Congestion index: top1 = 49.38, top5 = 43.67, top10 = 40.66, top15 = 38.87.
PHY-3001 : End congestion estimation;  1.033219s wall, 1.062500s user + 0.046875s system = 1.109375s CPU (107.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19285 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.943658s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (99.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000733455
PHY-3002 : Step(199): len = 610155, overlap = 1.25
PHY-3002 : Step(200): len = 610110, overlap = 1.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006370s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 610271, Over = 0
PHY-3001 : End spreading;  0.072045s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (108.4%)

PHY-3001 : Final: Len = 610271, Over = 0
PHY-3001 : End incremental placement;  6.434357s wall, 6.406250s user + 0.125000s system = 6.531250s CPU (101.5%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.109780s wall, 11.203125s user + 0.187500s system = 11.390625s CPU (112.7%)

OPT-1001 : Current memory(MB): used = 712, reserve = 697, peak = 719.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17400/19287.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 737352, over cnt = 38(0%), over = 46, worst = 3
PHY-1002 : len = 737248, over cnt = 27(0%), over = 27, worst = 1
PHY-1002 : len = 737608, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 737736, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 737736, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.716239s wall, 0.734375s user + 0.031250s system = 0.765625s CPU (106.9%)

PHY-1001 : Congestion index: top1 = 49.18, top5 = 43.43, top10 = 40.50, top15 = 38.75.
OPT-1001 : End congestion update;  1.030689s wall, 1.031250s user + 0.031250s system = 1.062500s CPU (103.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19285 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.799068s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (101.7%)

OPT-0007 : Start: WNS 4305 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.834738s wall, 1.843750s user + 0.031250s system = 1.875000s CPU (102.2%)

OPT-1001 : Current memory(MB): used = 712, reserve = 697, peak = 719.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19285 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.801010s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17418/19287.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 737736, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.128813s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (97.0%)

PHY-1001 : Congestion index: top1 = 49.18, top5 = 43.43, top10 = 40.50, top15 = 38.75.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19285 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.794919s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (98.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4305 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 48.758621
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4305ps with logic level 5 
RUN-1001 :       #2 path slack 4321ps with logic level 5 
OPT-1001 : End physical optimization;  16.234478s wall, 17.296875s user + 0.250000s system = 17.546875s CPU (108.1%)

RUN-1003 : finish command "place" in  69.212108s wall, 123.140625s user + 7.453125s system = 130.593750s CPU (188.7%)

RUN-1004 : used memory is 594 MB, reserved memory is 590 MB, peak memory is 719 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.708615s wall, 2.984375s user + 0.000000s system = 2.984375s CPU (174.7%)

RUN-1004 : used memory is 594 MB, reserved memory is 590 MB, peak memory is 719 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8060 instances
RUN-1001 : 3991 mslices, 3979 lslices, 59 pads, 26 brams, 0 dsps
RUN-1001 : There are total 19287 nets
RUN-1001 : 13559 nets have 2 pins
RUN-1001 : 4316 nets have [3 - 5] pins
RUN-1001 : 891 nets have [6 - 10] pins
RUN-1001 : 380 nets have [11 - 20] pins
RUN-1001 : 132 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66975, tnet num: 19285, tinst num: 8058, tnode num: 90793, tedge num: 110425.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.722826s wall, 1.718750s user + 0.000000s system = 1.718750s CPU (99.8%)

RUN-1004 : used memory is 593 MB, reserved memory is 587 MB, peak memory is 719 MB
PHY-1001 : 3991 mslices, 3979 lslices, 59 pads, 26 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19285 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 681888, over cnt = 2298(6%), over = 3792, worst = 7
PHY-1002 : len = 694032, over cnt = 1584(4%), over = 2378, worst = 6
PHY-1002 : len = 711880, over cnt = 693(1%), over = 1034, worst = 5
PHY-1002 : len = 727544, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 727752, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.857112s wall, 3.046875s user + 0.031250s system = 3.078125s CPU (165.7%)

PHY-1001 : Congestion index: top1 = 49.22, top5 = 43.44, top10 = 40.47, top15 = 38.62.
PHY-1001 : End global routing;  2.213644s wall, 3.406250s user + 0.031250s system = 3.437500s CPU (155.3%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 693, reserve = 681, peak = 719.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 963, reserve = 950, peak = 963.
PHY-1001 : End build detailed router design. 4.850590s wall, 4.812500s user + 0.031250s system = 4.843750s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 190120, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.962240s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (100.7%)

PHY-1001 : Current memory(MB): used = 999, reserve = 987, peak = 999.
PHY-1001 : End phase 1; 0.969998s wall, 0.984375s user + 0.000000s system = 0.984375s CPU (101.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 53% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.67657e+06, over cnt = 1296(0%), over = 1298, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1015, reserve = 1002, peak = 1015.
PHY-1001 : End initial routed; 15.448976s wall, 43.796875s user + 0.343750s system = 44.140625s CPU (285.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18027(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.517   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.501573s wall, 3.484375s user + 0.015625s system = 3.500000s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1030, reserve = 1018, peak = 1030.
PHY-1001 : End phase 2; 18.950709s wall, 47.281250s user + 0.359375s system = 47.640625s CPU (251.4%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.67657e+06, over cnt = 1296(0%), over = 1298, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.251597s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (99.4%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.66242e+06, over cnt = 463(0%), over = 463, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.803633s wall, 1.203125s user + 0.015625s system = 1.218750s CPU (151.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.6631e+06, over cnt = 86(0%), over = 86, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.416495s wall, 0.578125s user + 0.031250s system = 0.609375s CPU (146.3%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.66388e+06, over cnt = 18(0%), over = 18, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.238793s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (130.9%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.66434e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.216320s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (101.1%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.66434e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.223539s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (104.8%)

PHY-1001 : ===== DR Iter 6 =====
PHY-1022 : len = 1.66434e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 6; 0.317842s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (103.2%)

PHY-1001 : ===== DR Iter 7 =====
PHY-1022 : len = 1.66434e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 7; 0.487789s wall, 0.531250s user + 0.031250s system = 0.562500s CPU (115.3%)

PHY-1001 : ===== DR Iter 8 =====
PHY-1022 : len = 1.66438e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 8; 0.183076s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (102.4%)

PHY-1001 : ==== DR Iter 9 ====
PHY-1022 : len = 1.66435e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 9; 0.172189s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (99.8%)

PHY-1001 : ==== DR Iter 10 ====
PHY-1022 : len = 1.66435e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 10; 0.207590s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (105.4%)

PHY-1001 : ==== DR Iter 11 ====
PHY-1022 : len = 1.66435e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 11; 0.236679s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (99.0%)

PHY-1001 : ==== DR Iter 12 ====
PHY-1022 : len = 1.66435e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 12; 0.289859s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (97.0%)

PHY-1001 : ===== DR Iter 13 =====
PHY-1022 : len = 1.66436e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 13; 0.177960s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (96.6%)

PHY-1001 : ==== DR Iter 14 ====
PHY-1022 : len = 1.66436e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 14; 0.158969s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (137.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18027(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.517   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.540041s wall, 3.531250s user + 0.000000s system = 3.531250s CPU (99.8%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 293 feed throughs used by 263 nets
PHY-1001 : End commit to database; 2.216760s wall, 2.203125s user + 0.015625s system = 2.218750s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1119, reserve = 1110, peak = 1119.
PHY-1001 : End phase 3; 10.696188s wall, 11.343750s user + 0.140625s system = 11.484375s CPU (107.4%)

PHY-1003 : Routed, final wirelength = 1.66436e+06
PHY-1001 : Current memory(MB): used = 1123, reserve = 1114, peak = 1123.
PHY-1001 : End export database. 0.061449s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.7%)

PHY-1001 : End detail routing;  35.971934s wall, 64.937500s user + 0.531250s system = 65.468750s CPU (182.0%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66975, tnet num: 19285, tinst num: 8058, tnode num: 90793, tedge num: 110425.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.750433s wall, 1.750000s user + 0.000000s system = 1.750000s CPU (100.0%)

RUN-1004 : used memory is 999 MB, reserved memory is 996 MB, peak memory is 1123 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  44.414642s wall, 74.546875s user + 0.578125s system = 75.125000s CPU (169.1%)

RUN-1004 : used memory is 999 MB, reserved memory is 996 MB, peak memory is 1123 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8793   out of  19600   44.86%
#reg                    12339   out of  19600   62.95%
#le                     14951
  #lut only              2612   out of  14951   17.47%
  #reg only              6158   out of  14951   41.19%
  #lut&reg               6181   out of  14951   41.34%
#dsp                        0   out of     29    0.00%
#bram                      26   out of     64   40.62%
  #bram9k                  24
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       59   out of    188   31.38%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6766
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          120
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         F2        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         D1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         B3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         C1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         A3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         A2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         B2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         B1        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F15        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDZ        INPUT        K15        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         P2        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         N4        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         M5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         N6        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         N5        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         P5        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M3        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         M4        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         H2        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         M1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         L1        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         N1        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         N3        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         M2        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT         T5        LVCMOS33           8            NONE           OREG       
    Fog_ENY        OUTPUT         T4        LVCMOS33           8            NONE           OREG       
    Fog_ENZ        OUTPUT         T7        LVCMOS33           8            NONE           OREG       
    Fog_INT        OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         T6        LVCMOS33           8            N/A            OREG       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E1        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        G11        LVCMOS25           8            N/A            NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |14951  |7305    |1488    |12383   |26      |0       |
|  AnyFog_dataX                      |AnyFog          |206    |81      |22      |170     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |84     |53      |22      |48      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |204    |78      |22      |169     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |53      |22      |51      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |207    |102     |22      |172     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |88     |65      |22      |53      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2947   |715     |39      |2856    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |62     |37      |5       |53      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |218    |74      |5       |206     |0       |0       |
|    STADOP_com2                     |STADOP          |555    |184     |0       |546     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |66     |41      |14      |43      |0       |0       |
|    head_com2                       |uniheading      |268    |53      |5       |255     |0       |0       |
|    rmc_com2                        |Gprmc           |38     |37      |0       |30      |0       |0       |
|    uart_com2                       |Agrica          |1425   |269     |10      |1408    |0       |0       |
|  COM3                              |COM3_Control    |286    |129     |19      |239     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |61     |35      |5       |52      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |58     |40      |14      |35      |0       |0       |
|    rmc_com3                        |Gprmc           |167    |54      |0       |152     |0       |0       |
|  DATA                              |Data_Processing |8795   |4477    |1122    |7033    |0       |0       |
|    DIV_Dtemp                       |Divider         |806    |320     |84      |685     |0       |0       |
|    DIV_Utemp                       |Divider         |635    |289     |84      |498     |0       |0       |
|    DIV_accX                        |Divider         |567    |296     |84      |446     |0       |0       |
|    DIV_accY                        |Divider         |621    |310     |102     |469     |0       |0       |
|    DIV_accZ                        |Divider         |667    |381     |132     |464     |0       |0       |
|    DIV_rateX                       |Divider         |602    |382     |132     |399     |0       |0       |
|    DIV_rateY                       |Divider         |613    |372     |132     |410     |0       |0       |
|    DIV_rateZ                       |Divider         |603    |352     |132     |402     |0       |0       |
|    genclk                          |genclk          |271    |175     |89      |112     |0       |0       |
|  FMC                               |FMC_Ctrl        |431    |382     |43      |335     |0       |0       |
|  IIC                               |I2C_master      |296    |244     |11      |263     |0       |0       |
|  IMU_CTRL                          |SCHA634         |981    |754     |61      |735     |0       |0       |
|    CtrlData                        |CtrlData        |529    |481     |47      |338     |0       |0       |
|      usms                          |Time_1ms        |28     |23      |5       |20      |0       |0       |
|    SPIM                            |SPI_SCHA634     |452    |273     |14      |397     |0       |0       |
|  POWER                             |POWER_EN        |97     |56      |38      |37      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |497    |287     |89      |326     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |497    |287     |89      |326     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |207    |106     |0       |192     |0       |0       |
|        reg_inst                    |register        |204    |103     |0       |189     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |290    |181     |89      |134     |0       |0       |
|        bus_inst                    |bus_top         |82     |53      |28      |30      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |28     |18      |10      |9       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |51     |32      |18      |18      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |124    |88      |29      |75      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13499  
    #2          2       3393   
    #3          3        633   
    #4          4        290   
    #5        5-10       950   
    #6        11-50      445   
    #7       51-100       8    
    #8       101-500      3    
    #9        >500        2    
  Average     2.12             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.085500s wall, 3.562500s user + 0.000000s system = 3.562500s CPU (170.8%)

RUN-1004 : used memory is 999 MB, reserved memory is 996 MB, peak memory is 1123 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66975, tnet num: 19285, tinst num: 8058, tnode num: 90793, tedge num: 110425.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.749827s wall, 1.750000s user + 0.000000s system = 1.750000s CPU (100.0%)

RUN-1004 : used memory is 1001 MB, reserved memory is 997 MB, peak memory is 1123 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19285 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.469274s wall, 1.437500s user + 0.031250s system = 1.468750s CPU (100.0%)

RUN-1004 : used memory is 1039 MB, reserved memory is 1032 MB, peak memory is 1123 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 40c9047af6c60b8681e7bd8c3a1ad8946a1d4edd384cda8aef5d685a0a84c04c -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8058
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19287, pip num: 144564
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 293
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3227 valid insts, and 406098 bits set as '1'.
BIT-1004 : the usercode register value: 00000000010111011011100110110011
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  11.815362s wall, 119.203125s user + 0.078125s system = 119.281250s CPU (1009.5%)

RUN-1004 : used memory is 1185 MB, reserved memory is 1171 MB, peak memory is 1300 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250821_102505.log"

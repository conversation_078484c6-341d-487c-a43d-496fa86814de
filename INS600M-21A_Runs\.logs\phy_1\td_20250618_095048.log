============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Wed Jun 18 09:50:48 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(516)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
RUN-1001 : Project manager successfully analyzed 21 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  6.009617s wall, 1.671875s user + 4.296875s system = 5.968750s CPU (99.3%)

RUN-1004 : used memory is 77 MB, reserved memory is 39 MB, peak memory is 89 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  2.001155s wall, 1.875000s user + 0.125000s system = 2.000000s CPU (99.9%)

RUN-1004 : used memory is 288 MB, reserved memory is 257 MB, peak memory is 291 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 58 trigger nets, 58 data nets.
KIT-1004 : Chipwatcher code = 1110111010000001
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=158) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=158) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=158)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=158)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22066/23 useful/useless nets, 18789/12 useful/useless insts
SYN-1016 : Merged 29 instances.
SYN-1032 : 21670/20 useful/useless nets, 19295/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 478 better
SYN-1014 : Optimize round 2
SYN-1032 : 21286/45 useful/useless nets, 18911/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.736695s wall, 2.703125s user + 0.046875s system = 2.750000s CPU (100.5%)

RUN-1004 : used memory is 317 MB, reserved memory is 284 MB, peak memory is 318 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 21358/441 useful/useless nets, 19034/63 useful/useless insts
SYN-1016 : Merged 69 instances.
SYN-2571 : Optimize after map_dsp, round 1, 573 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 56 instances.
SYN-2501 : Optimize round 1, 114 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 21862/5 useful/useless nets, 19538/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80119, tnet num: 21862, tinst num: 19537, tnode num: 112082, tedge num: 125066.
TMR-2508 : Levelizing timing graph completed, there are 85 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.241868s wall, 1.203125s user + 0.031250s system = 1.234375s CPU (99.4%)

RUN-1004 : used memory is 453 MB, reserved memory is 422 MB, peak memory is 453 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21862 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 282 (3.29), #lev = 7 (1.66)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 282 (3.29), #lev = 7 (1.66)
SYN-3001 : Logic optimization runtime opt =   0.04 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 614 instances into 282 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 487 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 160 adder to BLE ...
SYN-4008 : Packed 160 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.991860s wall, 4.921875s user + 0.078125s system = 5.000000s CPU (100.2%)

RUN-1004 : used memory is 341 MB, reserved memory is 307 MB, peak memory is 559 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  8.103181s wall, 7.968750s user + 0.156250s system = 8.125000s CPU (100.3%)

RUN-1004 : used memory is 342 MB, reserved memory is 308 MB, peak memory is 559 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/uart_com2/AGRIC_data[7] will be merged to another kept net COM2/rmc_com2/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM2/uart_com2/AGRIC_data[6] will be merged to another kept net COM2/rmc_com2/GPRMC_data[6]
SYN-5055 WARNING: The kept net COM2/uart_com2/AGRIC_data[5] will be merged to another kept net COM2/rmc_com2/GPRMC_data[5]
SYN-5055 WARNING: The kept net COM2/uart_com2/AGRIC_data[4] will be merged to another kept net COM2/rmc_com2/GPRMC_data[4]
SYN-5055 WARNING: The kept net COM2/uart_com2/AGRIC_data[3] will be merged to another kept net COM2/rmc_com2/GPRMC_data[3]
SYN-5055 WARNING: The kept net COM2/uart_com2/AGRIC_data[2] will be merged to another kept net COM2/rmc_com2/GPRMC_data[2]
SYN-5055 WARNING: The kept net COM2/uart_com2/AGRIC_data[1] will be merged to another kept net COM2/rmc_com2/GPRMC_data[1]
SYN-5055 WARNING: The kept net COM2/uart_com2/AGRIC_data[0] will be merged to another kept net COM2/rmc_com2/GPRMC_data[0]
SYN-5055 WARNING: The kept net COM2/rx_err will be merged to another kept net COM2/rmc_com2/GPRMC_err
SYN-5055 WARNING: The kept net FMC/Error_state[0] will be merged to another kept net COM2/rmc_com2/GPRMC_sub_state_cnt0[3]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (335 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 18782 instances
RUN-0007 : 5396 luts, 11841 seqs, 943 mslices, 489 lslices, 56 pads, 52 brams, 0 dsps
RUN-1001 : There are total 21134 nets
RUN-1001 : 15828 nets have 2 pins
RUN-1001 : 4152 nets have [3 - 5] pins
RUN-1001 : 780 nets have [6 - 10] pins
RUN-1001 : 256 nets have [11 - 20] pins
RUN-1001 : 96 nets have [21 - 99] pins
RUN-1001 : 22 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4724     
RUN-1001 :   No   |  No   |  Yes  |     670     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    5840     
RUN-1001 :   Yes  |  No   |  Yes  |     507     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  111  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 119
PHY-3001 : Initial placement ...
PHY-3001 : design contains 18780 instances, 5396 luts, 11841 seqs, 1432 slices, 281 macros(1432 instances: 943 mslices 489 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1099 pins
PHY-3001 : Huge net DATA/done_div with 1612 pins
PHY-0007 : Cell area utilization is 60%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 78486, tnet num: 21132, tinst num: 18780, tnode num: 110424, tedge num: 123604.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.243993s wall, 1.250000s user + 0.000000s system = 1.250000s CPU (100.5%)

RUN-1004 : used memory is 512 MB, reserved memory is 484 MB, peak memory is 559 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21132 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.164158s wall, 2.093750s user + 0.062500s system = 2.156250s CPU (99.6%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.62212e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 18780.
PHY-3001 : Level 1 #clusters 2064.
PHY-3001 : End clustering;  0.155107s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (191.4%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 60%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 906391, overlap = 623.812
PHY-3002 : Step(2): len = 807750, overlap = 707.875
PHY-3002 : Step(3): len = 537464, overlap = 856.938
PHY-3002 : Step(4): len = 465839, overlap = 907.469
PHY-3002 : Step(5): len = 375168, overlap = 1020.72
PHY-3002 : Step(6): len = 327957, overlap = 1086.53
PHY-3002 : Step(7): len = 272955, overlap = 1145.62
PHY-3002 : Step(8): len = 242905, overlap = 1192.38
PHY-3002 : Step(9): len = 215726, overlap = 1238.75
PHY-3002 : Step(10): len = 197980, overlap = 1291.84
PHY-3002 : Step(11): len = 185101, overlap = 1349.78
PHY-3002 : Step(12): len = 172604, overlap = 1387.81
PHY-3002 : Step(13): len = 156906, overlap = 1418.75
PHY-3002 : Step(14): len = 144664, overlap = 1437.78
PHY-3002 : Step(15): len = 135787, overlap = 1461.38
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.9616e-07
PHY-3002 : Step(16): len = 137285, overlap = 1450.41
PHY-3002 : Step(17): len = 174255, overlap = 1392.12
PHY-3002 : Step(18): len = 184901, overlap = 1303.16
PHY-3002 : Step(19): len = 183395, overlap = 1261.34
PHY-3002 : Step(20): len = 182809, overlap = 1219.69
PHY-3002 : Step(21): len = 179533, overlap = 1173.81
PHY-3002 : Step(22): len = 177555, overlap = 1152.59
PHY-3002 : Step(23): len = 174734, overlap = 1144.69
PHY-3002 : Step(24): len = 171458, overlap = 1138.19
PHY-3002 : Step(25): len = 169056, overlap = 1153.31
PHY-3002 : Step(26): len = 167597, overlap = 1155.66
PHY-3002 : Step(27): len = 165845, overlap = 1135.84
PHY-3002 : Step(28): len = 165413, overlap = 1128.31
PHY-3002 : Step(29): len = 164315, overlap = 1118.28
PHY-3002 : Step(30): len = 163336, overlap = 1128.28
PHY-3002 : Step(31): len = 162510, overlap = 1118.16
PHY-3002 : Step(32): len = 162606, overlap = 1104.84
PHY-3002 : Step(33): len = 161946, overlap = 1121.53
PHY-3002 : Step(34): len = 161692, overlap = 1128
PHY-3002 : Step(35): len = 161142, overlap = 1119.41
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 1.79232e-06
PHY-3002 : Step(36): len = 167645, overlap = 1104
PHY-3002 : Step(37): len = 183528, overlap = 1059.22
PHY-3002 : Step(38): len = 186747, overlap = 1055.09
PHY-3002 : Step(39): len = 187590, overlap = 1026.88
PHY-3002 : Step(40): len = 186157, overlap = 1014.88
PHY-3002 : Step(41): len = 185749, overlap = 1003.78
PHY-3002 : Step(42): len = 185083, overlap = 1007.91
PHY-3002 : Step(43): len = 184500, overlap = 1014.16
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 3.58464e-06
PHY-3002 : Step(44): len = 193631, overlap = 990.719
PHY-3002 : Step(45): len = 207824, overlap = 861.312
PHY-3002 : Step(46): len = 213465, overlap = 791.688
PHY-3002 : Step(47): len = 217292, overlap = 764.531
PHY-3002 : Step(48): len = 217775, overlap = 748.281
PHY-3002 : Step(49): len = 216276, overlap = 751.25
PHY-3002 : Step(50): len = 215684, overlap = 748.969
PHY-3002 : Step(51): len = 214869, overlap = 733.562
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 7.16928e-06
PHY-3002 : Step(52): len = 227073, overlap = 684.469
PHY-3002 : Step(53): len = 244008, overlap = 637.344
PHY-3002 : Step(54): len = 247430, overlap = 607.531
PHY-3002 : Step(55): len = 248742, overlap = 594
PHY-3002 : Step(56): len = 246912, overlap = 596.312
PHY-3002 : Step(57): len = 246408, overlap = 605.812
PHY-3002 : Step(58): len = 243871, overlap = 619.938
PHY-3002 : Step(59): len = 243468, overlap = 615.188
PHY-3002 : Step(60): len = 243042, overlap = 628.625
PHY-3002 : Step(61): len = 242022, overlap = 629.75
PHY-3002 : Step(62): len = 241326, overlap = 639
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.43386e-05
PHY-3002 : Step(63): len = 253142, overlap = 587.719
PHY-3002 : Step(64): len = 267685, overlap = 545.156
PHY-3002 : Step(65): len = 271763, overlap = 520.406
PHY-3002 : Step(66): len = 273772, overlap = 510.031
PHY-3002 : Step(67): len = 273546, overlap = 500.5
PHY-3002 : Step(68): len = 272044, overlap = 513.844
PHY-3002 : Step(69): len = 269879, overlap = 512.125
PHY-3002 : Step(70): len = 268707, overlap = 485.844
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 2.86771e-05
PHY-3002 : Step(71): len = 278436, overlap = 467.75
PHY-3002 : Step(72): len = 289292, overlap = 461.75
PHY-3002 : Step(73): len = 292466, overlap = 422.844
PHY-3002 : Step(74): len = 293105, overlap = 421.5
PHY-3002 : Step(75): len = 290882, overlap = 413.875
PHY-3002 : Step(76): len = 289762, overlap = 408.531
PHY-3002 : Step(77): len = 288452, overlap = 393.188
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 5.73542e-05
PHY-3002 : Step(78): len = 296694, overlap = 375.469
PHY-3002 : Step(79): len = 305248, overlap = 359.094
PHY-3002 : Step(80): len = 309797, overlap = 355.438
PHY-3002 : Step(81): len = 310544, overlap = 343.969
PHY-3002 : Step(82): len = 308871, overlap = 346.375
PHY-3002 : Step(83): len = 306103, overlap = 346.219
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000114708
PHY-3002 : Step(84): len = 310840, overlap = 337.688
PHY-3002 : Step(85): len = 315129, overlap = 331.938
PHY-3002 : Step(86): len = 317986, overlap = 325.5
PHY-3002 : Step(87): len = 320708, overlap = 315.094
PHY-3002 : Step(88): len = 320207, overlap = 310.625
PHY-3002 : Step(89): len = 318403, overlap = 301.562
PHY-3002 : Step(90): len = 316641, overlap = 294.344
PHY-3002 : Step(91): len = 315471, overlap = 299.688
PHY-3002 : Step(92): len = 315485, overlap = 293.094
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000210027
PHY-3002 : Step(93): len = 318761, overlap = 279.625
PHY-3002 : Step(94): len = 321480, overlap = 271.812
PHY-3002 : Step(95): len = 322472, overlap = 280.125
PHY-3002 : Step(96): len = 324200, overlap = 271.688
PHY-3002 : Step(97): len = 324268, overlap = 273.531
PHY-3002 : Step(98): len = 325135, overlap = 259.25
PHY-3002 : Step(99): len = 325017, overlap = 262.5
PHY-3002 : Step(100): len = 324428, overlap = 272.062
PHY-3002 : Step(101): len = 324376, overlap = 275.75
PHY-3002 : Step(102): len = 323807, overlap = 280.719
PHY-3002 : Step(103): len = 323832, overlap = 290.469
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.009761s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (160.1%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 67%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21134.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 432304, over cnt = 1204(3%), over = 5630, worst = 54
PHY-1001 : End global iterations;  0.960739s wall, 1.234375s user + 0.046875s system = 1.281250s CPU (133.4%)

PHY-1001 : Congestion index: top1 = 77.74, top5 = 54.46, top10 = 43.90, top15 = 38.16.
PHY-3001 : End congestion estimation;  1.223501s wall, 1.484375s user + 0.046875s system = 1.531250s CPU (125.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21132 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.068142s wall, 1.046875s user + 0.031250s system = 1.078125s CPU (100.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.46871e-05
PHY-3002 : Step(104): len = 373415, overlap = 229
PHY-3002 : Step(105): len = 380042, overlap = 210
PHY-3002 : Step(106): len = 379965, overlap = 202.125
PHY-3002 : Step(107): len = 379714, overlap = 192.938
PHY-3002 : Step(108): len = 383334, overlap = 178.312
PHY-3002 : Step(109): len = 389519, overlap = 160.062
PHY-3002 : Step(110): len = 391451, overlap = 148.75
PHY-3002 : Step(111): len = 392236, overlap = 140.312
PHY-3002 : Step(112): len = 395382, overlap = 132.938
PHY-3002 : Step(113): len = 396867, overlap = 128.406
PHY-3002 : Step(114): len = 397054, overlap = 129.062
PHY-3002 : Step(115): len = 396945, overlap = 122.312
PHY-3002 : Step(116): len = 398752, overlap = 122.344
PHY-3002 : Step(117): len = 397281, overlap = 122.031
PHY-3002 : Step(118): len = 396220, overlap = 123.719
PHY-3002 : Step(119): len = 396871, overlap = 120.344
PHY-3002 : Step(120): len = 394997, overlap = 117.75
PHY-3002 : Step(121): len = 394814, overlap = 116.906
PHY-3002 : Step(122): len = 394699, overlap = 113.188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000189374
PHY-3002 : Step(123): len = 394990, overlap = 102.656
PHY-3002 : Step(124): len = 395614, overlap = 101.094
PHY-3002 : Step(125): len = 398107, overlap = 97.1562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000337972
PHY-3002 : Step(126): len = 400674, overlap = 90.4375
PHY-3002 : Step(127): len = 403449, overlap = 88.2812
PHY-3002 : Step(128): len = 407459, overlap = 85.3125
PHY-3002 : Step(129): len = 419095, overlap = 83.4688
PHY-3002 : Step(130): len = 425585, overlap = 81.9062
PHY-3002 : Step(131): len = 422835, overlap = 81.875
PHY-3002 : Step(132): len = 420913, overlap = 76.9062
PHY-3002 : Step(133): len = 420091, overlap = 73.9688
PHY-3002 : Step(134): len = 419390, overlap = 71.7188
PHY-3002 : Step(135): len = 421699, overlap = 66.5
PHY-3002 : Step(136): len = 423346, overlap = 65.2188
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(137): len = 422334, overlap = 66.9062
PHY-3002 : Step(138): len = 424460, overlap = 67.875
PHY-3002 : Step(139): len = 429077, overlap = 67.9688
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 67%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 74/21134.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 502256, over cnt = 2029(5%), over = 9359, worst = 41
PHY-1001 : End global iterations;  1.157018s wall, 1.734375s user + 0.078125s system = 1.812500s CPU (156.7%)

PHY-1001 : Congestion index: top1 = 77.63, top5 = 58.01, top10 = 48.99, top15 = 43.63.
PHY-3001 : End congestion estimation;  1.468103s wall, 2.046875s user + 0.078125s system = 2.125000s CPU (144.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21132 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.002982s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (101.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.26001e-05
PHY-3002 : Step(140): len = 430904, overlap = 366.844
PHY-3002 : Step(141): len = 439211, overlap = 304.719
PHY-3002 : Step(142): len = 432879, overlap = 293.438
PHY-3002 : Step(143): len = 429816, overlap = 282.656
PHY-3002 : Step(144): len = 431058, overlap = 252.75
PHY-3002 : Step(145): len = 430253, overlap = 238.5
PHY-3002 : Step(146): len = 428740, overlap = 225.188
PHY-3002 : Step(147): len = 429283, overlap = 212.188
PHY-3002 : Step(148): len = 427432, overlap = 200.094
PHY-3002 : Step(149): len = 425873, overlap = 201.406
PHY-3002 : Step(150): len = 426512, overlap = 194.094
PHY-3002 : Step(151): len = 423932, overlap = 192.656
PHY-3002 : Step(152): len = 421945, overlap = 188.969
PHY-3002 : Step(153): len = 422128, overlap = 193.312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0001852
PHY-3002 : Step(154): len = 421843, overlap = 181.875
PHY-3002 : Step(155): len = 423021, overlap = 177.531
PHY-3002 : Step(156): len = 424410, overlap = 172.781
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0003704
PHY-3002 : Step(157): len = 427121, overlap = 162.562
PHY-3002 : Step(158): len = 432116, overlap = 147.5
PHY-3002 : Step(159): len = 437124, overlap = 147.938
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000740801
PHY-3002 : Step(160): len = 437052, overlap = 141.125
PHY-3002 : Step(161): len = 440165, overlap = 133.156
PHY-3002 : Step(162): len = 444404, overlap = 128.375
PHY-3002 : Step(163): len = 446962, overlap = 124.438
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 78486, tnet num: 21132, tinst num: 18780, tnode num: 110424, tedge num: 123604.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.922958s wall, 1.875000s user + 0.046875s system = 1.921875s CPU (99.9%)

RUN-1004 : used memory is 550 MB, reserved memory is 526 MB, peak memory is 679 MB
OPT-1001 : Total overflow 474.34 peak overflow 3.53
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 470/21134.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 529944, over cnt = 2337(6%), over = 8172, worst = 24
PHY-1001 : End global iterations;  1.285637s wall, 1.796875s user + 0.046875s system = 1.843750s CPU (143.4%)

PHY-1001 : Congestion index: top1 = 56.77, top5 = 46.55, top10 = 41.90, top15 = 38.89.
PHY-1001 : End incremental global routing;  1.554590s wall, 2.078125s user + 0.046875s system = 2.125000s CPU (136.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21132 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.059489s wall, 1.062500s user + 0.000000s system = 1.062500s CPU (100.3%)

OPT-1001 : 15 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 56 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 18706 has valid locations, 242 needs to be replaced
PHY-3001 : design contains 19007 instances, 5483 luts, 11981 seqs, 1432 slices, 281 macros(1432 instances: 943 mslices 489 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 461432
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16189/21361.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 541936, over cnt = 2377(6%), over = 8260, worst = 24
PHY-1001 : End global iterations;  0.213548s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (117.1%)

PHY-1001 : Congestion index: top1 = 56.94, top5 = 46.65, top10 = 42.10, top15 = 39.11.
PHY-3001 : End congestion estimation;  0.473850s wall, 0.500000s user + 0.000000s system = 0.500000s CPU (105.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 79253, tnet num: 21359, tinst num: 19007, tnode num: 111531, tedge num: 124684.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.541088s wall, 1.515625s user + 0.031250s system = 1.546875s CPU (100.4%)

RUN-1004 : used memory is 593 MB, reserved memory is 586 MB, peak memory is 680 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21359 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.634650s wall, 2.578125s user + 0.062500s system = 2.640625s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(164): len = 461209, overlap = 3.25
PHY-3002 : Step(165): len = 462315, overlap = 3.25
PHY-3002 : Step(166): len = 462677, overlap = 3.375
PHY-3002 : Step(167): len = 463023, overlap = 3.5625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(168): len = 463289, overlap = 3.5
PHY-3002 : Step(169): len = 464170, overlap = 3.0625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(170): len = 464448, overlap = 3
PHY-3002 : Step(171): len = 465790, overlap = 2.9375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 16203/21361.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 542320, over cnt = 2393(6%), over = 8316, worst = 24
PHY-1001 : End global iterations;  0.203785s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (115.0%)

PHY-1001 : Congestion index: top1 = 57.26, top5 = 47.05, top10 = 42.43, top15 = 39.38.
PHY-3001 : End congestion estimation;  0.468394s wall, 0.484375s user + 0.015625s system = 0.500000s CPU (106.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21359 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.074503s wall, 1.062500s user + 0.015625s system = 1.078125s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00112175
PHY-3002 : Step(172): len = 465738, overlap = 126.344
PHY-3002 : Step(173): len = 465825, overlap = 126.344
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00219101
PHY-3002 : Step(174): len = 466144, overlap = 126.156
PHY-3002 : Step(175): len = 466364, overlap = 126.062
PHY-3001 : Final: Len = 466364, Over = 126.062
PHY-3001 : End incremental placement;  5.736611s wall, 6.046875s user + 0.328125s system = 6.375000s CPU (111.1%)

OPT-1001 : Total overflow 479.78 peak overflow 3.53
OPT-1001 : End high-fanout net optimization;  8.905683s wall, 9.890625s user + 0.375000s system = 10.265625s CPU (115.3%)

OPT-1001 : Current memory(MB): used = 683, reserve = 662, peak = 699.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16226/21361.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 544008, over cnt = 2345(6%), over = 7834, worst = 24
PHY-1002 : len = 582224, over cnt = 1695(4%), over = 4264, worst = 24
PHY-1002 : len = 615792, over cnt = 738(2%), over = 1807, worst = 24
PHY-1002 : len = 638320, over cnt = 235(0%), over = 534, worst = 15
PHY-1002 : len = 646816, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.362305s wall, 2.015625s user + 0.015625s system = 2.031250s CPU (149.1%)

PHY-1001 : Congestion index: top1 = 47.72, top5 = 42.30, top10 = 39.54, top15 = 37.62.
OPT-1001 : End congestion update;  1.625958s wall, 2.281250s user + 0.015625s system = 2.296875s CPU (141.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 21359 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.253600s wall, 1.250000s user + 0.015625s system = 1.265625s CPU (101.0%)

OPT-0007 : Start: WNS 4361 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.886275s wall, 3.531250s user + 0.031250s system = 3.562500s CPU (123.4%)

OPT-1001 : Current memory(MB): used = 659, reserve = 638, peak = 699.
OPT-1001 : End physical optimization;  14.042181s wall, 15.750000s user + 0.515625s system = 16.265625s CPU (115.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5483 LUT to BLE ...
SYN-4008 : Packed 5483 LUT and 2601 SEQ to BLE.
SYN-4003 : Packing 9380 remaining SEQ's ...
SYN-4005 : Packed 3238 SEQ with LUT/SLICE
SYN-4006 : 159 single LUT's are left
SYN-4006 : 6142 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11625/13362 primitive instances ...
PHY-3001 : End packing;  2.940021s wall, 2.937500s user + 0.000000s system = 2.937500s CPU (99.9%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 7967 instances
RUN-1001 : 3927 mslices, 3927 lslices, 56 pads, 52 brams, 0 dsps
RUN-1001 : There are total 18809 nets
RUN-1001 : 13195 nets have 2 pins
RUN-1001 : 4265 nets have [3 - 5] pins
RUN-1001 : 840 nets have [6 - 10] pins
RUN-1001 : 354 nets have [11 - 20] pins
RUN-1001 : 145 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
PHY-3001 : design contains 7965 instances, 7854 slices, 281 macros(1432 instances: 943 mslices 489 lslices)
PHY-3001 : Cell area utilization is 84%
PHY-3001 : After packing: Len = 486571, Over = 358.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7420/18809.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 622008, over cnt = 1494(4%), over = 2258, worst = 9
PHY-1002 : len = 626504, over cnt = 1009(2%), over = 1401, worst = 6
PHY-1002 : len = 636312, over cnt = 484(1%), over = 632, worst = 5
PHY-1002 : len = 641600, over cnt = 242(0%), over = 326, worst = 5
PHY-1002 : len = 646976, over cnt = 4(0%), over = 6, worst = 3
PHY-1001 : End global iterations;  1.202199s wall, 1.875000s user + 0.062500s system = 1.937500s CPU (161.2%)

PHY-1001 : Congestion index: top1 = 50.80, top5 = 43.41, top10 = 40.18, top15 = 37.98.
PHY-3001 : End congestion estimation;  1.542498s wall, 2.234375s user + 0.062500s system = 2.296875s CPU (148.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 65957, tnet num: 18807, tinst num: 7965, tnode num: 89616, tedge num: 108721.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.748784s wall, 1.718750s user + 0.015625s system = 1.734375s CPU (99.2%)

RUN-1004 : used memory is 583 MB, reserved memory is 568 MB, peak memory is 699 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 18807 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.698480s wall, 2.656250s user + 0.031250s system = 2.687500s CPU (99.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.27415e-05
PHY-3002 : Step(176): len = 490235, overlap = 344.25
PHY-3002 : Step(177): len = 491858, overlap = 352.25
PHY-3002 : Step(178): len = 492338, overlap = 358
PHY-3002 : Step(179): len = 494220, overlap = 362.75
PHY-3002 : Step(180): len = 494404, overlap = 368.25
PHY-3002 : Step(181): len = 494363, overlap = 378
PHY-3002 : Step(182): len = 493999, overlap = 381
PHY-3002 : Step(183): len = 492032, overlap = 379.75
PHY-3002 : Step(184): len = 490092, overlap = 386.5
PHY-3002 : Step(185): len = 488570, overlap = 387
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000105483
PHY-3002 : Step(186): len = 492281, overlap = 374
PHY-3002 : Step(187): len = 496524, overlap = 369.25
PHY-3002 : Step(188): len = 497341, overlap = 363.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000210966
PHY-3002 : Step(189): len = 503274, overlap = 353.25
PHY-3002 : Step(190): len = 511441, overlap = 333.5
PHY-3002 : Step(191): len = 512057, overlap = 325
PHY-3002 : Step(192): len = 512432, overlap = 318.25
PHY-3002 : Step(193): len = 513506, overlap = 321.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.834025s wall, 0.937500s user + 1.062500s system = 2.000000s CPU (239.8%)

PHY-3001 : Trial Legalized: Len = 621554
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 83%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 509/18809.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 707896, over cnt = 2253(6%), over = 3793, worst = 8
PHY-1002 : len = 722136, over cnt = 1380(3%), over = 1980, worst = 7
PHY-1002 : len = 739552, over cnt = 514(1%), over = 674, worst = 5
PHY-1002 : len = 743320, over cnt = 343(0%), over = 449, worst = 5
PHY-1002 : len = 749864, over cnt = 65(0%), over = 70, worst = 2
PHY-1001 : End global iterations;  2.137193s wall, 3.468750s user + 0.000000s system = 3.468750s CPU (162.3%)

PHY-1001 : Congestion index: top1 = 49.33, top5 = 44.62, top10 = 41.98, top15 = 40.20.
PHY-3001 : End congestion estimation;  2.521956s wall, 3.828125s user + 0.015625s system = 3.843750s CPU (152.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 18807 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.956353s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000163626
PHY-3002 : Step(194): len = 580180, overlap = 82.5
PHY-3002 : Step(195): len = 562048, overlap = 130.5
PHY-3002 : Step(196): len = 550845, overlap = 167.25
PHY-3002 : Step(197): len = 542420, overlap = 205.75
PHY-3002 : Step(198): len = 537974, overlap = 220.25
PHY-3002 : Step(199): len = 535119, overlap = 242.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000327251
PHY-3002 : Step(200): len = 538710, overlap = 235
PHY-3002 : Step(201): len = 543661, overlap = 236.5
PHY-3002 : Step(202): len = 546585, overlap = 233.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000654503
PHY-3002 : Step(203): len = 549598, overlap = 230.25
PHY-3002 : Step(204): len = 558388, overlap = 225.75
PHY-3002 : Step(205): len = 561796, overlap = 228
PHY-3002 : Step(206): len = 562624, overlap = 233
PHY-3002 : Step(207): len = 563106, overlap = 237.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.034195s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (137.1%)

PHY-3001 : Legalized: Len = 604489, Over = 0
PHY-3001 : Spreading special nets. 56 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.094834s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (98.9%)

PHY-3001 : 79 instances has been re-located, deltaX = 22, deltaY = 42, maxDist = 2.
PHY-3001 : Final: Len = 605615, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 65957, tnet num: 18807, tinst num: 7965, tnode num: 89616, tedge num: 108721.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.325056s wall, 2.296875s user + 0.031250s system = 2.328125s CPU (100.1%)

RUN-1004 : used memory is 596 MB, reserved memory is 593 MB, peak memory is 699 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3459/18809.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 701336, over cnt = 2166(6%), over = 3356, worst = 7
PHY-1002 : len = 712832, over cnt = 1286(3%), over = 1734, worst = 6
PHY-1002 : len = 727448, over cnt = 476(1%), over = 624, worst = 5
PHY-1002 : len = 735472, over cnt = 109(0%), over = 126, worst = 4
PHY-1002 : len = 737368, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.976272s wall, 3.359375s user + 0.031250s system = 3.390625s CPU (171.6%)

PHY-1001 : Congestion index: top1 = 47.37, top5 = 42.83, top10 = 40.37, top15 = 38.76.
PHY-1001 : End incremental global routing;  2.320202s wall, 3.687500s user + 0.031250s system = 3.718750s CPU (160.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 18807 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.993451s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (100.7%)

OPT-1001 : 3 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 56 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 7903 has valid locations, 17 needs to be replaced
PHY-3001 : design contains 7979 instances, 7868 slices, 281 macros(1432 instances: 943 mslices 489 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 610049
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16769/18829.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 743048, over cnt = 39(0%), over = 49, worst = 5
PHY-1002 : len = 743000, over cnt = 25(0%), over = 28, worst = 3
PHY-1002 : len = 743208, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 743224, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 743272, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.726350s wall, 0.734375s user + 0.046875s system = 0.781250s CPU (107.6%)

PHY-1001 : Congestion index: top1 = 47.33, top5 = 42.97, top10 = 40.49, top15 = 38.90.
PHY-3001 : End congestion estimation;  1.055566s wall, 1.046875s user + 0.062500s system = 1.109375s CPU (105.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66089, tnet num: 18827, tinst num: 7979, tnode num: 89778, tedge num: 108885.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.093912s wall, 2.046875s user + 0.046875s system = 2.093750s CPU (100.0%)

RUN-1004 : used memory is 627 MB, reserved memory is 612 MB, peak memory is 699 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 18827 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.093068s wall, 3.015625s user + 0.078125s system = 3.093750s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(208): len = 610049, overlap = 0
PHY-3002 : Step(209): len = 610049, overlap = 0
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 16783/18829.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 743272, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.119455s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (91.6%)

PHY-1001 : Congestion index: top1 = 47.33, top5 = 42.97, top10 = 40.49, top15 = 38.90.
PHY-3001 : End congestion estimation;  0.439521s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (99.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 18827 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.097136s wall, 1.093750s user + 0.000000s system = 1.093750s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.0007545
PHY-3002 : Step(210): len = 609593, overlap = 2.25
PHY-3002 : Step(211): len = 609554, overlap = 1.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006867s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (227.6%)

PHY-3001 : Legalized: Len = 609751, Over = 0
PHY-3001 : End spreading;  0.071972s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (108.5%)

PHY-3001 : Final: Len = 609751, Over = 0
PHY-3001 : End incremental placement;  6.328385s wall, 6.218750s user + 0.187500s system = 6.406250s CPU (101.2%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.190405s wall, 11.625000s user + 0.218750s system = 11.843750s CPU (116.2%)

OPT-1001 : Current memory(MB): used = 696, reserve = 680, peak = 702.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16767/18829.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 743368, over cnt = 19(0%), over = 26, worst = 4
PHY-1002 : len = 743376, over cnt = 12(0%), over = 14, worst = 3
PHY-1002 : len = 743472, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 743488, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 743488, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.719945s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (110.7%)

PHY-1001 : Congestion index: top1 = 47.33, top5 = 42.93, top10 = 40.48, top15 = 38.88.
OPT-1001 : End congestion update;  1.037568s wall, 1.093750s user + 0.000000s system = 1.093750s CPU (105.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 18827 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.810472s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (100.3%)

OPT-0007 : Start: WNS 4657 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.852829s wall, 1.906250s user + 0.000000s system = 1.906250s CPU (102.9%)

OPT-1001 : Current memory(MB): used = 694, reserve = 679, peak = 702.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 18827 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.808081s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (100.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16783/18829.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 743488, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.121888s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (102.6%)

PHY-1001 : Congestion index: top1 = 47.33, top5 = 42.93, top10 = 40.48, top15 = 38.88.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 18827 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.809214s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (98.5%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4657 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 47.000000
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4657ps with logic level 3 
RUN-1001 :       #2 path slack 4664ps with logic level 3 
RUN-1001 :       #3 path slack 4668ps with logic level 3 
RUN-1001 :       #4 path slack 4728ps with logic level 8 
RUN-1001 :       #5 path slack 4728ps with logic level 8 
OPT-1001 : End physical optimization;  16.702206s wall, 18.218750s user + 0.265625s system = 18.484375s CPU (110.7%)

RUN-1003 : finish command "place" in  70.939792s wall, 130.531250s user + 7.953125s system = 138.484375s CPU (195.2%)

RUN-1004 : used memory is 582 MB, reserved memory is 558 MB, peak memory is 702 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.641169s wall, 2.812500s user + 0.000000s system = 2.812500s CPU (171.4%)

RUN-1004 : used memory is 582 MB, reserved memory is 560 MB, peak memory is 702 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 7981 instances
RUN-1001 : 3941 mslices, 3927 lslices, 56 pads, 52 brams, 0 dsps
RUN-1001 : There are total 18829 nets
RUN-1001 : 13201 nets have 2 pins
RUN-1001 : 4263 nets have [3 - 5] pins
RUN-1001 : 846 nets have [6 - 10] pins
RUN-1001 : 363 nets have [11 - 20] pins
RUN-1001 : 146 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66089, tnet num: 18827, tinst num: 7979, tnode num: 89778, tedge num: 108885.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.888879s wall, 1.875000s user + 0.000000s system = 1.875000s CPU (99.3%)

RUN-1004 : used memory is 596 MB, reserved memory is 585 MB, peak memory is 702 MB
PHY-1001 : 3941 mslices, 3927 lslices, 56 pads, 52 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 18827 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 688120, over cnt = 2249(6%), over = 3736, worst = 8
PHY-1002 : len = 700840, over cnt = 1455(4%), over = 2157, worst = 8
PHY-1002 : len = 719688, over cnt = 529(1%), over = 772, worst = 7
PHY-1002 : len = 731864, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.847389s wall, 2.984375s user + 0.015625s system = 3.000000s CPU (162.4%)

PHY-1001 : Congestion index: top1 = 46.98, top5 = 42.68, top10 = 40.19, top15 = 38.57.
PHY-1001 : End global routing;  2.195321s wall, 3.312500s user + 0.015625s system = 3.328125s CPU (151.6%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 685, reserve = 674, peak = 702.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 954, reserve = 945, peak = 954.
PHY-1001 : End build detailed router design. 4.991316s wall, 4.968750s user + 0.015625s system = 4.984375s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 194720, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.033548s wall, 1.015625s user + 0.015625s system = 1.031250s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 990, reserve = 981, peak = 990.
PHY-1001 : End phase 1; 1.040748s wall, 1.031250s user + 0.015625s system = 1.046875s CPU (100.6%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.81541e+06, over cnt = 1213(0%), over = 1221, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1004, reserve = 992, peak = 1004.
PHY-1001 : End initial routed; 23.720161s wall, 53.546875s user + 0.546875s system = 54.093750s CPU (228.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17620(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.296   |   0.000   |   0   
RUN-1001 :   Hold   |   0.104   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.693089s wall, 3.656250s user + 0.015625s system = 3.671875s CPU (99.4%)

PHY-1001 : Current memory(MB): used = 1013, reserve = 1000, peak = 1013.
PHY-1001 : End phase 2; 27.413429s wall, 57.203125s user + 0.562500s system = 57.765625s CPU (210.7%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.81541e+06, over cnt = 1213(0%), over = 1221, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.249721s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (100.1%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.80426e+06, over cnt = 421(0%), over = 421, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.835848s wall, 1.421875s user + 0.000000s system = 1.421875s CPU (170.1%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.80343e+06, over cnt = 69(0%), over = 69, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.453121s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (148.3%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.80412e+06, over cnt = 8(0%), over = 8, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.232056s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (107.7%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.80434e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.215259s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (101.6%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.80437e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.169206s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (92.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17620(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.260   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.546582s wall, 3.546875s user + 0.000000s system = 3.546875s CPU (100.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 281 feed throughs used by 245 nets
PHY-1001 : End commit to database; 2.481022s wall, 2.468750s user + 0.000000s system = 2.468750s CPU (99.5%)

PHY-1001 : Current memory(MB): used = 1100, reserve = 1091, peak = 1100.
PHY-1001 : End phase 3; 8.756443s wall, 9.578125s user + 0.000000s system = 9.578125s CPU (109.4%)

PHY-1003 : Routed, final wirelength = 1.80437e+06
PHY-1001 : Current memory(MB): used = 1104, reserve = 1095, peak = 1104.
PHY-1001 : End export database. 0.062432s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.1%)

PHY-1001 : End detail routing;  42.743600s wall, 73.296875s user + 0.609375s system = 73.906250s CPU (172.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66089, tnet num: 18827, tinst num: 7979, tnode num: 89778, tedge num: 108885.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.884963s wall, 1.890625s user + 0.000000s system = 1.890625s CPU (100.3%)

RUN-1004 : used memory is 1039 MB, reserved memory is 1034 MB, peak memory is 1104 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  51.494316s wall, 83.156250s user + 0.640625s system = 83.796875s CPU (162.7%)

RUN-1004 : used memory is 1038 MB, reserved memory is 1034 MB, peak memory is 1104 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8494   out of  19600   43.34%
#reg                    12086   out of  19600   61.66%
#le                     14610
  #lut only              2524   out of  14610   17.28%
  #reg only              6116   out of  14610   41.86%
  #lut&reg               5970   out of  14610   40.86%
#dsp                        0   out of     29    0.00%
#bram                      52   out of     64   81.25%
  #bram9k                  52
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       56   out of    188   29.79%
  #ireg                    10
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6670
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          190
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT        E11        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT        A13        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT        F10        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         E8        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         E7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT        A10        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C6        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         D6        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         C7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT        E12        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         F1        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         A7        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H13        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J11        LVCMOS33          N/A          PULLUP          IREG       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT        E13        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         R7        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         M7        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         P7        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         T8        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         P8        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         N8        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M9        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         T9        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         R9        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         T6        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         P9        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         N9        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT        P11        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         T5        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         B2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        L10        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        E15        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        R14        LVCMOS33           8            N/A            OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT         R2        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT        N12        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        B10        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         J6        LVCMOS33           8            N/A            NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        R15        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E6        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |14610  |7062    |1432    |12129   |52      |0       |
|  AnyFog_dataX                      |AnyFog          |220    |87      |22      |182     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |80     |54      |22      |46      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |217    |101     |22      |179     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |56      |22      |52      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |219    |107     |22      |177     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |57      |22      |49      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2672   |513     |34      |2604    |0       |0       |
|    STADOP_com2                     |STADOP          |591    |106     |5       |578     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |45      |14      |42      |0       |0       |
|    head_com2                       |uniheading      |274    |65      |5       |257     |0       |0       |
|    rmc_com2                        |Gprmc           |33     |32      |0       |30      |0       |0       |
|    uart_com2                       |Agrica          |1407   |253     |10      |1395    |0       |0       |
|  COM3                              |COM3_Control    |212    |106     |14      |185     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |61     |40      |14      |39      |0       |0       |
|    rmc_com3                        |Gprmc           |151    |66      |0       |146     |0       |0       |
|  DATA                              |Data_Processing |8540   |4309    |1059    |6865    |0       |0       |
|    DIV_Dtemp                       |Divider         |801    |336     |84      |667     |0       |0       |
|    DIV_Utemp                       |Divider         |616    |286     |84      |493     |0       |0       |
|    DIV_accX                        |Divider         |595    |317     |84      |471     |0       |0       |
|    DIV_accY                        |Divider         |662    |311     |108     |493     |0       |0       |
|    DIV_accZ                        |Divider         |654    |381     |132     |451     |0       |0       |
|    DIV_rateX                       |Divider         |641    |376     |132     |435     |0       |0       |
|    DIV_rateY                       |Divider         |597    |348     |132     |393     |0       |0       |
|    DIV_rateZ                       |Divider         |631    |403     |132     |425     |0       |0       |
|    genclk                          |genclk          |97     |65      |20      |56      |0       |0       |
|  FMC                               |FMC_Ctrl        |476    |433     |30      |344     |0       |0       |
|  IIC                               |I2C_master      |293    |237     |11      |265     |0       |0       |
|  IMU_CTRL                          |SCHA634         |918    |642     |61      |726     |0       |0       |
|    CtrlData                        |CtrlData        |472    |420     |47      |326     |0       |0       |
|      usms                          |Time_1ms        |29     |23      |5       |20      |0       |0       |
|    SPIM                            |SPI_SCHA634     |446    |222     |14      |400     |0       |0       |
|  POWER                             |POWER_EN        |96     |49      |38      |37      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |741    |478     |119     |516     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |741    |478     |119     |516     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |337    |217     |0       |322     |0       |0       |
|        reg_inst                    |register        |336    |216     |0       |321     |0       |0       |
|        tap_inst                    |tap             |1      |1       |0       |1       |0       |0       |
|      trigger_inst                  |trigger         |404    |261     |119     |194     |0       |0       |
|        bus_inst                    |bus_top         |182    |120     |62      |69      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |28     |18      |10      |12      |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |53     |35      |18      |21      |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |100    |66      |34      |35      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |144    |91      |29      |95      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13144  
    #2          2       3279   
    #3          3        694   
    #4          4        290   
    #5        5-10       938   
    #6        11-50      398   
    #7       51-100      19    
    #8       101-500      4    
    #9        >500        2    
  Average     2.15             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.027266s wall, 3.531250s user + 0.000000s system = 3.531250s CPU (174.2%)

RUN-1004 : used memory is 1039 MB, reserved memory is 1035 MB, peak memory is 1104 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66089, tnet num: 18827, tinst num: 7979, tnode num: 89778, tedge num: 108885.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.771950s wall, 1.781250s user + 0.000000s system = 1.781250s CPU (100.5%)

RUN-1004 : used memory is 1040 MB, reserved memory is 1036 MB, peak memory is 1104 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 18827 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.456725s wall, 1.453125s user + 0.000000s system = 1.453125s CPU (99.8%)

RUN-1004 : used memory is 1045 MB, reserved memory is 1039 MB, peak memory is 1104 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 28b752c1c0e7d7ba92ac5beed3c46754b7b56ac64969e4b0149cfdbc1d295011 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 7979
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 18829, pip num: 146137
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 281
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3210 valid insts, and 405457 bits set as '1'.
BIT-1004 : the usercode register value: 00000000000000001110111010000001
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.583803s wall, 124.625000s user + 0.218750s system = 124.843750s CPU (992.1%)

RUN-1004 : used memory is 1164 MB, reserved memory is 1149 MB, peak memory is 1279 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250618_095048.log"

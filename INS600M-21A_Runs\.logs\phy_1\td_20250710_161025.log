============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Jul 10 16:10:25 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(520)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
RUN-1001 : Project manager successfully analyzed 22 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.609696s wall, 1.718750s user + 3.906250s system = 5.625000s CPU (100.3%)

RUN-1004 : used memory is 79 MB, reserved memory is 40 MB, peak memory is 90 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.839513s wall, 1.765625s user + 0.078125s system = 1.843750s CPU (100.2%)

RUN-1004 : used memory is 296 MB, reserved memory is 264 MB, peak memory is 299 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 4 view nodes, 26 trigger nets, 26 data nets.
KIT-1004 : Chipwatcher code = 0110100010110101
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=4,BUS_DIN_NUM=26,BUS_CTRL_NUM=68,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=90) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=90) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=4,BUS_DIN_NUM=26,BUS_CTRL_NUM=68,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=4,BUS_DIN_NUM=26,BUS_CTRL_NUM=68,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=4,BUS_DIN_NUM=26,BUS_CTRL_NUM=68,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=90)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=90)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=4,BUS_DIN_NUM=26,BUS_CTRL_NUM=68,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=4,BUS_DIN_NUM=26,BUS_CTRL_NUM=68,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 21775/18 useful/useless nets, 18784/10 useful/useless insts
SYN-1016 : Merged 25 instances.
SYN-1032 : 21519/20 useful/useless nets, 19126/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 2 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 2 mux instances.
SYN-1015 : Optimize round 1, 307 better
SYN-1014 : Optimize round 2
SYN-1032 : 21288/30 useful/useless nets, 18895/32 useful/useless insts
SYN-1015 : Optimize round 2, 64 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.403173s wall, 2.328125s user + 0.062500s system = 2.390625s CPU (99.5%)

RUN-1004 : used memory is 320 MB, reserved memory is 288 MB, peak memory is 322 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 21312/157 useful/useless nets, 18942/31 useful/useless insts
SYN-1016 : Merged 21 instances.
SYN-2571 : Optimize after map_dsp, round 1, 209 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 24 instances.
SYN-2501 : Optimize round 1, 50 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 14 macro adder
SYN-1019 : Optimized 9 mux instances.
SYN-1016 : Merged 10 instances.
SYN-1032 : 21695/5 useful/useless nets, 19325/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 78837, tnet num: 21695, tinst num: 19324, tnode num: 110800, tedge num: 123030.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.223532s wall, 1.203125s user + 0.015625s system = 1.218750s CPU (99.6%)

RUN-1004 : used memory is 455 MB, reserved memory is 423 MB, peak memory is 455 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21695 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 184 (3.58), #lev = 7 (1.95)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 184 (3.58), #lev = 7 (1.95)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 437 instances into 184 LUTs, name keeping = 74%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 318 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 108 adder to BLE ...
SYN-4008 : Packed 108 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.633364s wall, 4.546875s user + 0.109375s system = 4.656250s CPU (100.5%)

RUN-1004 : used memory is 340 MB, reserved memory is 318 MB, peak memory is 559 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.380420s wall, 7.187500s user + 0.218750s system = 7.406250s CPU (100.3%)

RUN-1004 : used memory is 340 MB, reserved memory is 318 MB, peak memory is 559 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/uart_com2/AGRIC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/uart_com2/AGRIC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/uart_com2/AGRIC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/uart_com2/AGRIC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/uart_com2/AGRIC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/uart_com2/AGRIC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/uart_com2/AGRIC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/uart_com2/AGRIC_data[0]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[7] will be merged to another kept net COM3/rmc_com3/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[6] will be merged to another kept net COM3/rmc_com3/GPRMC_data[6]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (199 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 18750 instances
RUN-0007 : 5310 luts, 11936 seqs, 925 mslices, 490 lslices, 60 pads, 24 brams, 0 dsps
RUN-1001 : There are total 21144 nets
RUN-1001 : 15987 nets have 2 pins
RUN-1001 : 4026 nets have [3 - 5] pins
RUN-1001 : 806 nets have [6 - 10] pins
RUN-1001 : 205 nets have [11 - 20] pins
RUN-1001 : 101 nets have [21 - 99] pins
RUN-1001 : 19 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4745     
RUN-1001 :   No   |  No   |  Yes  |     637     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     371     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  111  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 119
PHY-3001 : Initial placement ...
PHY-3001 : design contains 18748 instances, 5310 luts, 11936 seqs, 1415 slices, 281 macros(1415 instances: 925 mslices 490 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 61%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 77604, tnet num: 21142, tinst num: 18748, tnode num: 109501, tedge num: 121936.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.229147s wall, 1.203125s user + 0.015625s system = 1.218750s CPU (99.2%)

RUN-1004 : used memory is 513 MB, reserved memory is 485 MB, peak memory is 559 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21142 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.138592s wall, 2.078125s user + 0.062500s system = 2.140625s CPU (100.1%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.54684e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 18748.
PHY-3001 : Level 1 #clusters 2066.
PHY-3001 : End clustering;  0.273508s wall, 0.359375s user + 0.031250s system = 0.390625s CPU (142.8%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 61%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 838322, overlap = 603.938
PHY-3002 : Step(2): len = 757061, overlap = 636.125
PHY-3002 : Step(3): len = 480675, overlap = 799.625
PHY-3002 : Step(4): len = 417890, overlap = 878.688
PHY-3002 : Step(5): len = 325735, overlap = 980.688
PHY-3002 : Step(6): len = 290087, overlap = 1036.91
PHY-3002 : Step(7): len = 244392, overlap = 1106.34
PHY-3002 : Step(8): len = 221457, overlap = 1168
PHY-3002 : Step(9): len = 195358, overlap = 1221.72
PHY-3002 : Step(10): len = 180561, overlap = 1257.22
PHY-3002 : Step(11): len = 162374, overlap = 1289.16
PHY-3002 : Step(12): len = 155658, overlap = 1307.56
PHY-3002 : Step(13): len = 144934, overlap = 1354.38
PHY-3002 : Step(14): len = 138779, overlap = 1384.84
PHY-3002 : Step(15): len = 125291, overlap = 1419.62
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.26315e-06
PHY-3002 : Step(16): len = 129441, overlap = 1416.12
PHY-3002 : Step(17): len = 166472, overlap = 1332.59
PHY-3002 : Step(18): len = 182772, overlap = 1225.47
PHY-3002 : Step(19): len = 189524, overlap = 1127.03
PHY-3002 : Step(20): len = 187574, overlap = 1080.31
PHY-3002 : Step(21): len = 182712, overlap = 1065.03
PHY-3002 : Step(22): len = 178677, overlap = 1040.69
PHY-3002 : Step(23): len = 174777, overlap = 1011.25
PHY-3002 : Step(24): len = 172622, overlap = 1005.81
PHY-3002 : Step(25): len = 168457, overlap = 999.969
PHY-3002 : Step(26): len = 167316, overlap = 980.969
PHY-3002 : Step(27): len = 166008, overlap = 991.594
PHY-3002 : Step(28): len = 165193, overlap = 990.969
PHY-3002 : Step(29): len = 164340, overlap = 984.5
PHY-3002 : Step(30): len = 164709, overlap = 992.25
PHY-3002 : Step(31): len = 163884, overlap = 997.844
PHY-3002 : Step(32): len = 164323, overlap = 993.906
PHY-3002 : Step(33): len = 163693, overlap = 990.75
PHY-3002 : Step(34): len = 164303, overlap = 991.281
PHY-3002 : Step(35): len = 163881, overlap = 997.125
PHY-3002 : Step(36): len = 163485, overlap = 997.562
PHY-3002 : Step(37): len = 161998, overlap = 1007.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.5263e-06
PHY-3002 : Step(38): len = 167828, overlap = 1007.25
PHY-3002 : Step(39): len = 180162, overlap = 956.125
PHY-3002 : Step(40): len = 182385, overlap = 931.312
PHY-3002 : Step(41): len = 183990, overlap = 912.75
PHY-3002 : Step(42): len = 183858, overlap = 904.688
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 5.05261e-06
PHY-3002 : Step(43): len = 193427, overlap = 882.812
PHY-3002 : Step(44): len = 210730, overlap = 820.156
PHY-3002 : Step(45): len = 215031, overlap = 784.719
PHY-3002 : Step(46): len = 215384, overlap = 789.531
PHY-3002 : Step(47): len = 213789, overlap = 778.844
PHY-3002 : Step(48): len = 212473, overlap = 763.094
PHY-3002 : Step(49): len = 210762, overlap = 752.812
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 1.01052e-05
PHY-3002 : Step(50): len = 221792, overlap = 730.906
PHY-3002 : Step(51): len = 235222, overlap = 638.719
PHY-3002 : Step(52): len = 241355, overlap = 560.375
PHY-3002 : Step(53): len = 244544, overlap = 531.562
PHY-3002 : Step(54): len = 243516, overlap = 539.094
PHY-3002 : Step(55): len = 241555, overlap = 549.656
PHY-3002 : Step(56): len = 239576, overlap = 546.844
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 2.02104e-05
PHY-3002 : Step(57): len = 251994, overlap = 507.594
PHY-3002 : Step(58): len = 263483, overlap = 438.75
PHY-3002 : Step(59): len = 267152, overlap = 427.438
PHY-3002 : Step(60): len = 266865, overlap = 421.156
PHY-3002 : Step(61): len = 264938, overlap = 420.844
PHY-3002 : Step(62): len = 263710, overlap = 426.594
PHY-3002 : Step(63): len = 262973, overlap = 423.5
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 4.04208e-05
PHY-3002 : Step(64): len = 271414, overlap = 393.312
PHY-3002 : Step(65): len = 281873, overlap = 345.594
PHY-3002 : Step(66): len = 285423, overlap = 351.688
PHY-3002 : Step(67): len = 285718, overlap = 349.219
PHY-3002 : Step(68): len = 284378, overlap = 354.594
PHY-3002 : Step(69): len = 283290, overlap = 350.969
PHY-3002 : Step(70): len = 281762, overlap = 362.156
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 8.08417e-05
PHY-3002 : Step(71): len = 288687, overlap = 333.469
PHY-3002 : Step(72): len = 296530, overlap = 324.438
PHY-3002 : Step(73): len = 299142, overlap = 326.906
PHY-3002 : Step(74): len = 299793, overlap = 318.219
PHY-3002 : Step(75): len = 297463, overlap = 316.281
PHY-3002 : Step(76): len = 296718, overlap = 285.594
PHY-3002 : Step(77): len = 295143, overlap = 274.844
PHY-3002 : Step(78): len = 296101, overlap = 269.031
PHY-3002 : Step(79): len = 295718, overlap = 259.156
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000161683
PHY-3002 : Step(80): len = 300356, overlap = 250.5
PHY-3002 : Step(81): len = 308244, overlap = 238.188
PHY-3002 : Step(82): len = 309598, overlap = 210.75
PHY-3002 : Step(83): len = 310862, overlap = 196.969
PHY-3002 : Step(84): len = 309929, overlap = 187.281
PHY-3002 : Step(85): len = 310091, overlap = 179.312
PHY-3002 : Step(86): len = 309203, overlap = 181
PHY-3002 : Step(87): len = 310042, overlap = 182.188
PHY-3002 : Step(88): len = 309345, overlap = 204.062
PHY-3002 : Step(89): len = 310268, overlap = 200.625
PHY-3002 : Step(90): len = 308653, overlap = 200.844
PHY-3002 : Step(91): len = 309305, overlap = 191.406
PHY-3002 : Step(92): len = 308344, overlap = 193.75
PHY-3002 : Step(93): len = 308867, overlap = 198.781
PHY-3002 : Step(94): len = 308168, overlap = 200.844
PHY-3002 : Step(95): len = 308241, overlap = 201.188
PHY-3002 : Step(96): len = 307747, overlap = 193.156
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000313102
PHY-3002 : Step(97): len = 310041, overlap = 189.531
PHY-3002 : Step(98): len = 315267, overlap = 179.312
PHY-3002 : Step(99): len = 316561, overlap = 199.312
PHY-3002 : Step(100): len = 318008, overlap = 195.188
PHY-3002 : Step(101): len = 317491, overlap = 196.188
PHY-3002 : Step(102): len = 317796, overlap = 208.844
PHY-3002 : Step(103): len = 317177, overlap = 214.375
PHY-3002 : Step(104): len = 316963, overlap = 217.781
PHY-3002 : Step(105): len = 316254, overlap = 211.156
PHY-3002 : Step(106): len = 316660, overlap = 212.281
PHY-3002 : Step(107): len = 316255, overlap = 206.344
PHY-3002 : Step(108): len = 316537, overlap = 212.625
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.010818s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (144.4%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 67%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21144.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 415432, over cnt = 1116(3%), over = 5204, worst = 53
PHY-1001 : End global iterations;  0.849377s wall, 1.156250s user + 0.031250s system = 1.187500s CPU (139.8%)

PHY-1001 : Congestion index: top1 = 74.20, top5 = 54.16, top10 = 43.67, top15 = 37.44.
PHY-3001 : End congestion estimation;  1.106119s wall, 1.390625s user + 0.046875s system = 1.437500s CPU (130.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21142 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.970340s wall, 0.937500s user + 0.031250s system = 0.968750s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.95521e-05
PHY-3002 : Step(109): len = 355800, overlap = 200.406
PHY-3002 : Step(110): len = 374277, overlap = 170.688
PHY-3002 : Step(111): len = 368060, overlap = 177.219
PHY-3002 : Step(112): len = 363574, overlap = 172.312
PHY-3002 : Step(113): len = 367558, overlap = 148.656
PHY-3002 : Step(114): len = 371055, overlap = 149.719
PHY-3002 : Step(115): len = 372319, overlap = 141.375
PHY-3002 : Step(116): len = 374896, overlap = 132.156
PHY-3002 : Step(117): len = 379975, overlap = 118.875
PHY-3002 : Step(118): len = 382901, overlap = 108.312
PHY-3002 : Step(119): len = 384364, overlap = 100.406
PHY-3002 : Step(120): len = 386921, overlap = 100.469
PHY-3002 : Step(121): len = 390708, overlap = 104.562
PHY-3002 : Step(122): len = 392060, overlap = 107.156
PHY-3002 : Step(123): len = 393087, overlap = 104.562
PHY-3002 : Step(124): len = 394771, overlap = 105.281
PHY-3002 : Step(125): len = 395619, overlap = 101.781
PHY-3002 : Step(126): len = 396871, overlap = 101.812
PHY-3002 : Step(127): len = 397926, overlap = 97.5938
PHY-3002 : Step(128): len = 400656, overlap = 100.719
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000199104
PHY-3002 : Step(129): len = 399900, overlap = 96.2188
PHY-3002 : Step(130): len = 400770, overlap = 94.9062
PHY-3002 : Step(131): len = 402201, overlap = 91.875
PHY-3002 : Step(132): len = 405345, overlap = 91.5625
PHY-3002 : Step(133): len = 407188, overlap = 90.0625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000398209
PHY-3002 : Step(134): len = 406877, overlap = 86.5625
PHY-3002 : Step(135): len = 409328, overlap = 81.0312
PHY-3002 : Step(136): len = 414088, overlap = 75.9062
PHY-3002 : Step(137): len = 416567, overlap = 77.1562
PHY-3002 : Step(138): len = 418609, overlap = 73.5938
PHY-3002 : Step(139): len = 420824, overlap = 71.3438
PHY-3002 : Step(140): len = 418646, overlap = 71.9688
PHY-3002 : Step(141): len = 418362, overlap = 69.5625
PHY-3002 : Step(142): len = 418324, overlap = 67.9688
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 67%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 57/21144.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 480800, over cnt = 1973(5%), over = 9045, worst = 35
PHY-1001 : End global iterations;  1.106368s wall, 1.796875s user + 0.015625s system = 1.812500s CPU (163.8%)

PHY-1001 : Congestion index: top1 = 76.12, top5 = 57.13, top10 = 48.01, top15 = 42.73.
PHY-3001 : End congestion estimation;  1.418076s wall, 2.109375s user + 0.015625s system = 2.125000s CPU (149.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21142 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.022373s wall, 1.000000s user + 0.031250s system = 1.031250s CPU (100.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000103219
PHY-3002 : Step(143): len = 424142, overlap = 326.688
PHY-3002 : Step(144): len = 431898, overlap = 260.062
PHY-3002 : Step(145): len = 429758, overlap = 233.531
PHY-3002 : Step(146): len = 428359, overlap = 215.031
PHY-3002 : Step(147): len = 425897, overlap = 207.656
PHY-3002 : Step(148): len = 425026, overlap = 198.469
PHY-3002 : Step(149): len = 423070, overlap = 190.688
PHY-3002 : Step(150): len = 420521, overlap = 188.188
PHY-3002 : Step(151): len = 419625, overlap = 197.406
PHY-3002 : Step(152): len = 417931, overlap = 190.5
PHY-3002 : Step(153): len = 416037, overlap = 192.469
PHY-3002 : Step(154): len = 415359, overlap = 192.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000206439
PHY-3002 : Step(155): len = 415643, overlap = 184.844
PHY-3002 : Step(156): len = 417166, overlap = 179
PHY-3002 : Step(157): len = 418580, overlap = 165.312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000412877
PHY-3002 : Step(158): len = 420817, overlap = 168.781
PHY-3002 : Step(159): len = 426757, overlap = 160.656
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000732367
PHY-3002 : Step(160): len = 426852, overlap = 158.938
PHY-3002 : Step(161): len = 434784, overlap = 145.375
PHY-3002 : Step(162): len = 439563, overlap = 134.875
PHY-3002 : Step(163): len = 439739, overlap = 133.656
PHY-3002 : Step(164): len = 439864, overlap = 134.312
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00130179
PHY-3002 : Step(165): len = 440644, overlap = 132.5
PHY-3002 : Step(166): len = 443234, overlap = 128.594
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 77604, tnet num: 21142, tinst num: 18748, tnode num: 109501, tedge num: 121936.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.612531s wall, 1.578125s user + 0.046875s system = 1.625000s CPU (100.8%)

RUN-1004 : used memory is 551 MB, reserved memory is 525 MB, peak memory is 680 MB
OPT-1001 : Total overflow 489.53 peak overflow 5.28
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 679/21144.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 521040, over cnt = 2391(6%), over = 8400, worst = 30
PHY-1001 : End global iterations;  1.239214s wall, 1.968750s user + 0.031250s system = 2.000000s CPU (161.4%)

PHY-1001 : Congestion index: top1 = 58.02, top5 = 47.48, top10 = 42.38, top15 = 39.20.
PHY-1001 : End incremental global routing;  1.599074s wall, 2.343750s user + 0.031250s system = 2.375000s CPU (148.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21142 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.174312s wall, 1.156250s user + 0.015625s system = 1.171875s CPU (99.8%)

OPT-1001 : 14 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 18671 has valid locations, 216 needs to be replaced
PHY-3001 : design contains 18950 instances, 5397 luts, 12051 seqs, 1415 slices, 281 macros(1415 instances: 925 mslices 490 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 458109
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16567/21346.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 532032, over cnt = 2396(6%), over = 8403, worst = 30
PHY-1001 : End global iterations;  0.194816s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (104.3%)

PHY-1001 : Congestion index: top1 = 58.10, top5 = 47.53, top10 = 42.50, top15 = 39.39.
PHY-3001 : End congestion estimation;  0.461600s wall, 0.468750s user + 0.000000s system = 0.468750s CPU (101.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 78251, tnet num: 21344, tinst num: 18950, tnode num: 110413, tedge num: 122826.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.716603s wall, 1.640625s user + 0.046875s system = 1.687500s CPU (98.3%)

RUN-1004 : used memory is 594 MB, reserved memory is 583 MB, peak memory is 682 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21344 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.842410s wall, 2.734375s user + 0.093750s system = 2.828125s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(167): len = 458305, overlap = 2.0625
PHY-3002 : Step(168): len = 459553, overlap = 2
PHY-3002 : Step(169): len = 460269, overlap = 2.125
PHY-3002 : Step(170): len = 461352, overlap = 2.0625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 16586/21346.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 532576, over cnt = 2397(6%), over = 8508, worst = 30
PHY-1001 : End global iterations;  0.203263s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (123.0%)

PHY-1001 : Congestion index: top1 = 58.43, top5 = 47.82, top10 = 42.74, top15 = 39.59.
PHY-3001 : End congestion estimation;  0.534716s wall, 0.562500s user + 0.015625s system = 0.578125s CPU (108.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21344 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.064420s wall, 1.062500s user + 0.000000s system = 1.062500s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000585723
PHY-3002 : Step(171): len = 461348, overlap = 130.719
PHY-3002 : Step(172): len = 461470, overlap = 130.281
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00117145
PHY-3002 : Step(173): len = 461568, overlap = 131
PHY-3002 : Step(174): len = 461704, overlap = 131.125
PHY-3001 : Final: Len = 461704, Over = 131.125
PHY-3001 : End incremental placement;  5.850400s wall, 6.156250s user + 0.312500s system = 6.468750s CPU (110.6%)

OPT-1001 : Total overflow 495.38 peak overflow 5.28
OPT-1001 : End high-fanout net optimization;  9.151868s wall, 10.343750s user + 0.375000s system = 10.718750s CPU (117.1%)

OPT-1001 : Current memory(MB): used = 684, reserve = 662, peak = 700.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16609/21346.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 534488, over cnt = 2361(6%), over = 8059, worst = 30
PHY-1002 : len = 568248, over cnt = 1784(5%), over = 4969, worst = 21
PHY-1002 : len = 605448, over cnt = 789(2%), over = 2145, worst = 18
PHY-1002 : len = 629976, over cnt = 261(0%), over = 657, worst = 13
PHY-1002 : len = 638728, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.319277s wall, 1.828125s user + 0.000000s system = 1.828125s CPU (138.6%)

PHY-1001 : Congestion index: top1 = 49.91, top5 = 43.30, top10 = 39.97, top15 = 37.87.
OPT-1001 : End congestion update;  1.598792s wall, 2.093750s user + 0.015625s system = 2.109375s CPU (131.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 21344 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.933874s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (98.7%)

OPT-0007 : Start: WNS 4517 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.538871s wall, 3.031250s user + 0.015625s system = 3.046875s CPU (120.0%)

OPT-1001 : Current memory(MB): used = 680, reserve = 659, peak = 700.
OPT-1001 : End physical optimization;  13.672861s wall, 15.312500s user + 0.500000s system = 15.812500s CPU (115.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5397 LUT to BLE ...
SYN-4008 : Packed 5397 LUT and 2602 SEQ to BLE.
SYN-4003 : Packing 9449 remaining SEQ's ...
SYN-4005 : Packed 3181 SEQ with LUT/SLICE
SYN-4006 : 134 single LUT's are left
SYN-4006 : 6268 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11665/13305 primitive instances ...
PHY-3001 : End packing;  3.080826s wall, 3.093750s user + 0.015625s system = 3.109375s CPU (100.9%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 7874 instances
RUN-1001 : 3892 mslices, 3893 lslices, 60 pads, 24 brams, 0 dsps
RUN-1001 : There are total 18793 nets
RUN-1001 : 13303 nets have 2 pins
RUN-1001 : 4149 nets have [3 - 5] pins
RUN-1001 : 871 nets have [6 - 10] pins
RUN-1001 : 335 nets have [11 - 20] pins
RUN-1001 : 126 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 7872 instances, 7785 slices, 281 macros(1415 instances: 925 mslices 490 lslices)
PHY-3001 : Cell area utilization is 83%
PHY-3001 : After packing: Len = 480007, Over = 352
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 83%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7527/18793.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 605592, over cnt = 1430(4%), over = 2273, worst = 8
PHY-1002 : len = 611416, over cnt = 854(2%), over = 1162, worst = 6
PHY-1002 : len = 619216, over cnt = 435(1%), over = 571, worst = 5
PHY-1002 : len = 625216, over cnt = 151(0%), over = 196, worst = 5
PHY-1002 : len = 629048, over cnt = 1(0%), over = 1, worst = 1
PHY-1001 : End global iterations;  1.278268s wall, 1.984375s user + 0.000000s system = 1.984375s CPU (155.2%)

PHY-1001 : Congestion index: top1 = 49.59, top5 = 42.64, top10 = 39.38, top15 = 37.11.
PHY-3001 : End congestion estimation;  1.640448s wall, 2.343750s user + 0.000000s system = 2.343750s CPU (142.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 64828, tnet num: 18791, tinst num: 7872, tnode num: 88206, tedge num: 106779.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.948813s wall, 1.953125s user + 0.000000s system = 1.953125s CPU (100.2%)

RUN-1004 : used memory is 582 MB, reserved memory is 567 MB, peak memory is 700 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 18791 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.176561s wall, 3.140625s user + 0.031250s system = 3.171875s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.0925e-05
PHY-3002 : Step(175): len = 485655, overlap = 337.25
PHY-3002 : Step(176): len = 481996, overlap = 340
PHY-3002 : Step(177): len = 480469, overlap = 353.5
PHY-3002 : Step(178): len = 480291, overlap = 365.75
PHY-3002 : Step(179): len = 479310, overlap = 374.25
PHY-3002 : Step(180): len = 478788, overlap = 375.5
PHY-3002 : Step(181): len = 476491, overlap = 381
PHY-3002 : Step(182): len = 475053, overlap = 386.75
PHY-3002 : Step(183): len = 472726, overlap = 386
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00010185
PHY-3002 : Step(184): len = 476627, overlap = 374.75
PHY-3002 : Step(185): len = 481630, overlap = 357.25
PHY-3002 : Step(186): len = 483785, overlap = 353
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0002037
PHY-3002 : Step(187): len = 487942, overlap = 345.25
PHY-3002 : Step(188): len = 497488, overlap = 339.25
PHY-3002 : Step(189): len = 498529, overlap = 337.75
PHY-3002 : Step(190): len = 497204, overlap = 332
PHY-3002 : Step(191): len = 496054, overlap = 332.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.830570s wall, 0.812500s user + 0.875000s system = 1.687500s CPU (203.2%)

PHY-3001 : Trial Legalized: Len = 605549
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 82%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 591/18793.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 687808, over cnt = 2191(6%), over = 3539, worst = 7
PHY-1002 : len = 703416, over cnt = 1155(3%), over = 1532, worst = 7
PHY-1002 : len = 713328, over cnt = 615(1%), over = 785, worst = 7
PHY-1002 : len = 717112, over cnt = 434(1%), over = 563, worst = 5
PHY-1002 : len = 726808, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.827116s wall, 3.218750s user + 0.015625s system = 3.234375s CPU (177.0%)

PHY-1001 : Congestion index: top1 = 48.66, top5 = 44.29, top10 = 41.76, top15 = 39.83.
PHY-3001 : End congestion estimation;  2.212199s wall, 3.578125s user + 0.015625s system = 3.593750s CPU (162.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 18791 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.985741s wall, 0.984375s user + 0.000000s system = 0.984375s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000186741
PHY-3002 : Step(192): len = 563720, overlap = 78.25
PHY-3002 : Step(193): len = 544196, overlap = 123.75
PHY-3002 : Step(194): len = 531881, overlap = 169.5
PHY-3002 : Step(195): len = 525592, overlap = 199.5
PHY-3002 : Step(196): len = 522718, overlap = 228.5
PHY-3002 : Step(197): len = 520754, overlap = 245.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000373482
PHY-3002 : Step(198): len = 524777, overlap = 239
PHY-3002 : Step(199): len = 528698, overlap = 231.75
PHY-3002 : Step(200): len = 528058, overlap = 230.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(201): len = 530403, overlap = 230.25
PHY-3002 : Step(202): len = 538212, overlap = 228
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.036194s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (86.3%)

PHY-3001 : Legalized: Len = 574587, Over = 0
PHY-3001 : Spreading special nets. 32 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.107455s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (101.8%)

PHY-3001 : 47 instances has been re-located, deltaX = 17, deltaY = 22, maxDist = 2.
PHY-3001 : Final: Len = 575359, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 64828, tnet num: 18791, tinst num: 7872, tnode num: 88206, tedge num: 106779.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.078135s wall, 2.078125s user + 0.000000s system = 2.078125s CPU (100.0%)

RUN-1004 : used memory is 587 MB, reserved memory is 584 MB, peak memory is 700 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3861/18793.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 666504, over cnt = 1999(5%), over = 3062, worst = 9
PHY-1002 : len = 677096, over cnt = 1120(3%), over = 1483, worst = 6
PHY-1002 : len = 687864, over cnt = 521(1%), over = 686, worst = 5
PHY-1002 : len = 694680, over cnt = 189(0%), over = 246, worst = 5
PHY-1002 : len = 698392, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.620418s wall, 2.781250s user + 0.046875s system = 2.828125s CPU (174.5%)

PHY-1001 : Congestion index: top1 = 47.61, top5 = 42.72, top10 = 40.00, top15 = 38.14.
PHY-1001 : End incremental global routing;  1.946860s wall, 3.125000s user + 0.046875s system = 3.171875s CPU (162.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 18791 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.960986s wall, 0.937500s user + 0.015625s system = 0.953125s CPU (99.2%)

OPT-1001 : 3 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 7806 has valid locations, 20 needs to be replaced
PHY-3001 : design contains 7889 instances, 7802 slices, 281 macros(1415 instances: 925 mslices 490 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 577228
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 83%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16874/18811.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 700424, over cnt = 21(0%), over = 29, worst = 5
PHY-1002 : len = 700464, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 700512, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 700576, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.661080s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (101.6%)

PHY-1001 : Congestion index: top1 = 47.69, top5 = 42.79, top10 = 40.06, top15 = 38.19.
PHY-3001 : End congestion estimation;  0.990925s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (102.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 64964, tnet num: 18809, tinst num: 7889, tnode num: 88370, tedge num: 106963.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.988708s wall, 1.968750s user + 0.015625s system = 1.984375s CPU (99.8%)

RUN-1004 : used memory is 623 MB, reserved memory is 609 MB, peak memory is 700 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 18809 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.969056s wall, 2.953125s user + 0.015625s system = 2.968750s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(203): len = 577195, overlap = 0.5
PHY-3002 : Step(204): len = 576956, overlap = 0.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 83%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 16873/18811.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 700272, over cnt = 29(0%), over = 39, worst = 4
PHY-1002 : len = 700264, over cnt = 10(0%), over = 12, worst = 2
PHY-1002 : len = 700328, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 700408, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 700480, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.718835s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (106.5%)

PHY-1001 : Congestion index: top1 = 47.54, top5 = 42.69, top10 = 40.02, top15 = 38.18.
PHY-3001 : End congestion estimation;  1.027223s wall, 1.078125s user + 0.000000s system = 1.078125s CPU (105.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 18809 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.368600s wall, 1.375000s user + 0.000000s system = 1.375000s CPU (100.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000674086
PHY-3002 : Step(205): len = 576958, overlap = 2
PHY-3002 : Step(206): len = 576985, overlap = 2.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007232s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 577114, Over = 0
PHY-3001 : End spreading;  0.073187s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (106.7%)

PHY-3001 : Final: Len = 577114, Over = 0
PHY-3001 : End incremental placement;  7.007252s wall, 6.984375s user + 0.125000s system = 7.109375s CPU (101.5%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.429683s wall, 11.734375s user + 0.187500s system = 11.921875s CPU (114.3%)

OPT-1001 : Current memory(MB): used = 695, reserve = 681, peak = 700.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16873/18811.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 700480, over cnt = 18(0%), over = 23, worst = 3
PHY-1002 : len = 700536, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 700576, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 700608, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 700608, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.700772s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (104.8%)

PHY-1001 : Congestion index: top1 = 47.56, top5 = 42.72, top10 = 40.03, top15 = 38.19.
OPT-1001 : End congestion update;  1.021529s wall, 1.062500s user + 0.000000s system = 1.062500s CPU (104.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 18809 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.800710s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.5%)

OPT-0007 : Start: WNS 4826 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.827030s wall, 1.875000s user + 0.000000s system = 1.875000s CPU (102.6%)

OPT-1001 : Current memory(MB): used = 696, reserve = 681, peak = 700.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 18809 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.806211s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (98.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16891/18811.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 700608, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.119965s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (91.2%)

PHY-1001 : Congestion index: top1 = 47.56, top5 = 42.72, top10 = 40.03, top15 = 38.19.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 18809 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.864645s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (99.4%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4826 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 47.206897
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4826ps with logic level 5 
OPT-1001 : End physical optimization;  16.708579s wall, 18.046875s user + 0.187500s system = 18.234375s CPU (109.1%)

RUN-1003 : finish command "place" in  70.544278s wall, 122.234375s user + 7.296875s system = 129.531250s CPU (183.6%)

RUN-1004 : used memory is 615 MB, reserved memory is 593 MB, peak memory is 700 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.649666s wall, 2.859375s user + 0.015625s system = 2.875000s CPU (174.3%)

RUN-1004 : used memory is 615 MB, reserved memory is 593 MB, peak memory is 700 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 7891 instances
RUN-1001 : 3906 mslices, 3896 lslices, 60 pads, 24 brams, 0 dsps
RUN-1001 : There are total 18811 nets
RUN-1001 : 13303 nets have 2 pins
RUN-1001 : 4154 nets have [3 - 5] pins
RUN-1001 : 876 nets have [6 - 10] pins
RUN-1001 : 339 nets have [11 - 20] pins
RUN-1001 : 130 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 64964, tnet num: 18809, tinst num: 7889, tnode num: 88370, tedge num: 106963.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.770175s wall, 1.765625s user + 0.000000s system = 1.765625s CPU (99.7%)

RUN-1004 : used memory is 620 MB, reserved memory is 612 MB, peak memory is 700 MB
PHY-1001 : 3906 mslices, 3896 lslices, 60 pads, 24 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 18809 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 647488, over cnt = 2121(6%), over = 3466, worst = 7
PHY-1002 : len = 662288, over cnt = 1232(3%), over = 1703, worst = 6
PHY-1002 : len = 676560, over cnt = 462(1%), over = 612, worst = 5
PHY-1002 : len = 686304, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 686336, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.697426s wall, 2.859375s user + 0.015625s system = 2.875000s CPU (169.4%)

PHY-1001 : Congestion index: top1 = 47.74, top5 = 42.61, top10 = 39.69, top15 = 37.79.
PHY-1001 : End global routing;  2.057850s wall, 3.218750s user + 0.015625s system = 3.234375s CPU (157.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 682, reserve = 671, peak = 700.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 953, reserve = 938, peak = 953.
PHY-1001 : End build detailed router design. 4.873950s wall, 4.812500s user + 0.046875s system = 4.859375s CPU (99.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 187984, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.939675s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (101.4%)

PHY-1001 : Current memory(MB): used = 989, reserve = 974, peak = 989.
PHY-1001 : End phase 1; 0.948650s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (100.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.65862e+06, over cnt = 1182(0%), over = 1184, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 998, reserve = 983, peak = 998.
PHY-1001 : End initial routed; 17.112729s wall, 43.796875s user + 0.281250s system = 44.078125s CPU (257.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17615(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.761   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.452654s wall, 3.453125s user + 0.000000s system = 3.453125s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1007, reserve = 992, peak = 1007.
PHY-1001 : End phase 2; 20.565551s wall, 47.250000s user + 0.281250s system = 47.531250s CPU (231.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.65862e+06, over cnt = 1182(0%), over = 1184, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.247456s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (101.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.65019e+06, over cnt = 392(0%), over = 393, worst = 2, crit = 0
PHY-1001 : End DR Iter 1; 0.652780s wall, 1.125000s user + 0.000000s system = 1.125000s CPU (172.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.64977e+06, over cnt = 83(0%), over = 83, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.487472s wall, 0.609375s user + 0.000000s system = 0.609375s CPU (125.0%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.65041e+06, over cnt = 12(0%), over = 12, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.249696s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (112.6%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.65071e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 4; 0.201200s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (101.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17615(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.699   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.482407s wall, 3.468750s user + 0.015625s system = 3.484375s CPU (100.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 281 feed throughs used by 256 nets
PHY-1001 : End commit to database; 2.171236s wall, 2.125000s user + 0.046875s system = 2.171875s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1093, reserve = 1081, peak = 1093.
PHY-1001 : End phase 3; 7.999803s wall, 8.546875s user + 0.062500s system = 8.609375s CPU (107.6%)

PHY-1003 : Routed, final wirelength = 1.65071e+06
PHY-1001 : Current memory(MB): used = 1097, reserve = 1086, peak = 1097.
PHY-1001 : End export database. 0.061842s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.1%)

PHY-1001 : End detail routing;  34.897756s wall, 62.078125s user + 0.390625s system = 62.468750s CPU (179.0%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 64964, tnet num: 18809, tinst num: 7889, tnode num: 88370, tedge num: 106963.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.696673s wall, 1.687500s user + 0.000000s system = 1.687500s CPU (99.5%)

RUN-1004 : used memory is 1035 MB, reserved memory is 1027 MB, peak memory is 1098 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  43.096292s wall, 71.390625s user + 0.421875s system = 71.812500s CPU (166.6%)

RUN-1004 : used memory is 1035 MB, reserved memory is 1028 MB, peak memory is 1098 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8390   out of  19600   42.81%
#reg                    12152   out of  19600   62.00%
#le                     14622
  #lut only              2470   out of  14622   16.89%
  #reg only              6232   out of  14622   42.62%
  #lut&reg               5920   out of  14622   40.49%
#dsp                        0   out of     29    0.00%
#bram                      24   out of     64   37.50%
  #bram9k                  24
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                    10
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6681
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          114
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         M3        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         P6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |14622  |6975    |1415    |12195   |24      |0       |
|  AnyFog_dataX                      |AnyFog          |209    |89      |22      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |87     |61      |22      |51      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |206    |72      |22      |170     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |84     |51      |22      |48      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |206    |91      |22      |171     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |87     |61      |22      |52      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2861   |626     |34      |2765    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |214    |72      |5       |199     |0       |0       |
|    STADOP_com2                     |STADOP          |567    |90      |0       |551     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |64     |46      |14      |42      |0       |0       |
|    head_com2                       |uniheading      |277    |100     |5       |261     |0       |0       |
|    uart_com2                       |Agrica          |1431   |304     |10      |1404    |0       |0       |
|  COM3                              |COM3_Control    |215    |101     |14      |183     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |58     |36      |14      |36      |0       |0       |
|    rmc_com3                        |Gprmc           |157    |65      |0       |147     |0       |0       |
|  DATA                              |Data_Processing |8656   |4331    |1059    |6980    |0       |0       |
|    DIV_Dtemp                       |Divider         |773    |374     |84      |649     |0       |0       |
|    DIV_Utemp                       |Divider         |527    |318     |84      |402     |0       |0       |
|    DIV_accX                        |Divider         |636    |314     |84      |507     |0       |0       |
|    DIV_accY                        |Divider         |718    |328     |108     |555     |0       |0       |
|    DIV_accZ                        |Divider         |636    |379     |132     |432     |0       |0       |
|    DIV_rateX                       |Divider         |656    |397     |132     |451     |0       |0       |
|    DIV_rateY                       |Divider         |582    |351     |132     |379     |0       |0       |
|    DIV_rateZ                       |Divider         |621    |359     |132     |417     |0       |0       |
|    genclk                          |genclk          |81     |49      |20      |47      |0       |0       |
|  FMC                               |FMC_Ctrl        |435    |381     |43      |340     |0       |0       |
|  IIC                               |I2C_master      |308    |263     |11      |264     |0       |0       |
|  IMU_CTRL                          |SCHA634         |924    |690     |61      |730     |0       |0       |
|    CtrlData                        |CtrlData        |499    |444     |47      |341     |0       |0       |
|      usms                          |Time_1ms        |29     |24      |5       |21      |0       |0       |
|    SPIM                            |SPI_SCHA634     |425    |246     |14      |389     |0       |0       |
|  POWER                             |POWER_EN        |99     |50      |38      |39      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |486    |279     |89      |321     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |486    |279     |89      |321     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |196    |97      |0       |183     |0       |0       |
|        reg_inst                    |register        |194    |95      |0       |181     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |290    |182     |89      |138     |0       |0       |
|        bus_inst                    |bus_top         |81     |52      |28      |32      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |25     |14      |10      |8       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |54     |36      |18      |22      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |123    |88      |29      |74      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13242  
    #2          2       3294   
    #3          3        606   
    #4          4        254   
    #5        5-10       923   
    #6        11-50      418   
    #7       51-100       4    
    #8       101-500      3    
    #9        >500        2    
  Average     2.10             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.045446s wall, 3.484375s user + 0.015625s system = 3.500000s CPU (171.1%)

RUN-1004 : used memory is 1036 MB, reserved memory is 1029 MB, peak memory is 1098 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 64964, tnet num: 18809, tinst num: 7889, tnode num: 88370, tedge num: 106963.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.751759s wall, 1.750000s user + 0.000000s system = 1.750000s CPU (99.9%)

RUN-1004 : used memory is 1037 MB, reserved memory is 1031 MB, peak memory is 1098 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 18809 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.451486s wall, 1.453125s user + 0.000000s system = 1.453125s CPU (100.1%)

RUN-1004 : used memory is 1041 MB, reserved memory is 1034 MB, peak memory is 1098 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 89dcf97a3cd86c8370cef48ec94e5c7c51982d2733d868157b75ae7b794c4fe9 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 7889
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 18811, pip num: 140374
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 281
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3226 valid insts, and 393288 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111110110100010110101
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.178263s wall, 120.671875s user + 0.093750s system = 120.765625s CPU (991.6%)

RUN-1004 : used memory is 1164 MB, reserved memory is 1148 MB, peak memory is 1279 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250710_161025.log"

//////////////////////////////////////////////////////////////////////////////////
// Company: tianhailu
// Engineer: lidan
// 
// Create Date:    	13:14:20 08/27/2022 
// Design Name: 	INS600M_21A
// Module Name:    	INS600M_21A
// Project Name: 	INS600M_21A
// Target Devices: 
// Tool versions: 	TD5.6.1-64bit
// Description: 	8K21顶层模块
// Revision 1.01 - File Created
// Additional Comments: 
// /* synthesis keep */
//////////////////////////////////////////////////////////////////////////////////
module INS600M_21A
  (            			  
    //input                  	reset        ,    // 复位信号  
    input               clk_in		,   // 板载时钟为25MHz                                                
	//Version
	input 	[3:0]		Version  	,   //Version:当前版本为001
	//fmc相关接口          
    output 	[15:0]	   	FMC_data	,   // fmc总线数据
    input 	[7:0] 	   	FMC_addr	,   // fmc总线地址 
    input              	FMC_NWE 	,   // fmc读信号,低有效
    input              	FMC_NOE 	,   // fmc写信号,低有效
    input              	FMC_NCE  	,   // 选中fmc，低电平有效
    output             	ARM_INT 	,   // 给ARM的中断，为200Hz  
    output             	ARM_INTT	,   // 给ARM的中断，为200Hz                                             
	//FOGX
	input 				Fog_RXDX	,  //Fog_RXDX,光纤陀螺输入 
	output 				Fog_ENX		,  //第1只陀螺，电源使能 
	output 				Fog_INT		,  //Fog_INT,光纤陀螺同步信号                                        
	//FOGY			
	input 				Fog_RXDY	,  //Fog_RXDY,光纤陀螺输入  
	output 				Fog_ENY		,  //第2只陀螺，电源使能                                        
	//FOGZ			
	input 				Fog_RXDZ	,  //Fog_RXDZ,光纤陀螺输入  
	output 				Fog_ENZ		,  //第3只陀螺，电源使能                                                 
	//GNSS		
	output				GNSS_LED	,	//gnss控制灯，有gnss信号时为低，低电平有效
	output				TXD_RMC		,	//GNRMC直接转发到客户端
	output 				TXD_PPS		,   //PPS直接转发到客户端
	output 				GNSS_EN		,  
	input 				GNSS_PPS	,  //GNSS板卡,PPS输入
	input 				GNSS_RX2 	,  //GNSS板卡1路422接口RX                 			
    input 				GNSS_RX3 	,  //GNSS板卡1路422接口TX                                              
	//DACC三轴石英加速度计
	output 				DACC_EN		,  //三轴加速度计，电源使能
	output 				DACC_INT	,  //三轴加速度计，中断触发
	input 				DACC_RXD	,  //三轴加速度计，串口接收                                          
	//DACC三轴MEMS加速度计
	output 				DACC_MTXD	,  //三轴加速度计，串口发送
	input 				DACC_MRXD	,  //三轴加速度计，串口接收  
	//IMU SPI SCH634
    input         		miso, 	
    output 	 	  		spi_en,  //High level efficiency
    output  	  		cs_uno,	
    output  	  		cs_due,		
    output  	  		sclk,         		      
    output  	  		mosi,  	
	//Dronecan
	input 				Dronecan_Rx	,  //can总线接收 
	output 				Dronecan_Tx	,  //can总线发送
	output 				Dronecan_Stb,  //can总线唤醒状态，低电平有效：1’b1:正常模式；1’b0:待机模式；
	//MS4525D0空速管 I2C      			
    inout 				KSG_SDA   ,  //空速管数据总线SDA
	output				KSG_SCL	  ,  //空速管时钟总线SCL	
	//IMU PA_IMU_460        			
    input 				IMU_RXT   ,  
	output				IMU_INT	  ,  //同步信号,触发IMU输出数据 	
	//以下端口在INS912-3A中未使用
	//3-Axis Magnetic Sensor
	inout 				I2C_SDA		,  //磁力计数据总线SDA
	output 				I2C_SCL		   //磁力计时钟总线SCL                                                               
   ); 

//global_clock		  
wire 			rst_n;
wire 			reset;
wire 			locked;	
wire 			clk100mhz; 

// GNSS数据 (uavcan.equipment.gnss.Fix) - 完整数据集
wire [63:0] gnss_timestamp_usec;  		// GPS时间戳 (微秒)
wire [63:0] gnss_latitude_deg_1e8;		// 纬度 (*1e8度)
wire [63:0] gnss_longitude_deg_1e8;  	// 经度 (*1e8度)
wire [31:0] gnss_height_ellipsoid_mm;	// 椭球高度 (毫米)
wire [31:0] gnss_height_msl_mm;      	// 海平面高度 (毫米)
// NED速度分量 (北东下坐标系)
wire [31:0] gnss_velocity_north_m_s;  // 北向速度 (m/s; IEEE754)
wire [31:0] gnss_velocity_east_m_s;   // 东向速度 (m/s; IEEE754)
wire [31:0] gnss_velocity_down_m_s;   // 下向速度 (m/s; IEEE754)
// 速度准确度和状态
wire [15:0] gnss_velocity_accuracy_m_s; // 速度精度 (m/s * 100)
wire [31:0] gnss_ground_speed_m_s;      // 地面速度 (m/s; IEEE754)
wire [31:0] gnss_course_over_ground;    // 地面航向 (弧度; IEEE754)
// 位置精度信息
wire [15:0]	gnss_position_accuracy_mm; // 位置精度 (毫米)
wire [15:0]	gnss_hdop;                 // 水平精度因子 (*100)
wire [15:0]	gnss_vdop;                 // 垂直精度因子 (*100)
// 卫星和状态信息
wire [7:0]	gnss_sats_used;           // 使用的卫星数
wire [7:0]	gnss_sats_visible;        // 可见卫星数
wire [7:0]	gnss_fix_type;            // 定位类型 (0=无效; 2=2D; 3=3D; 4=RTK等)
wire [7:0]	gnss_status;              // GNSS系统状态
wire 		gnss_valid; 
// 罗盘数据 (uavcan.equipment.ahrs.MagneticFieldStrength)
wire [31:0] mag_field_ga_x;         // X轴磁场强度 (高斯)
wire [31:0] mag_field_ga_y;         // Y轴磁场强度 (高斯)
wire [31:0] mag_field_ga_z;         // Z轴磁场强度 (高斯)
wire 		mag_field_valid;
       
//global_clock	
global_clock CLK100M(
	.refclk(clk_in),
	.clk0_out(clk100mhz), 
	.reset(1'b0),   //High level reset     
	.extlock(locked)          
);       
 
//Synchronous signal assignment
assign rst_n		= locked; 
assign Dronecan_Stb	= locked; 

//Dronecan_protocol 
dronecan_protocol_parser Dronecan    
(
	.clk						(clk100mhz					),                    
	.rst_n						(rst_n 						),
	//CAN总线物理接口				
	.Dronecan_Rx				(Dronecan_Rx				),
	.Dronecan_Tx				(Dronecan_Tx				),
	//解析后的GNSS数据输出
	.gnss_timestamp_usec		(gnss_timestamp_usec		), //GPS时间戳 (微秒)
	.gnss_latitude_deg_1e8		(gnss_latitude_deg_1e8		), //纬度 (*1e8度)
	.gnss_longitude_deg_1e8		(gnss_longitude_deg_1e8		), //经度 (*1e8度)
	.gnss_height_ellipsoid_mm	(gnss_height_ellipsoid_mm	), //椭球高度 (毫米)
	.gnss_height_msl_mm			(gnss_height_msl_mm			), //海平面高度 (毫米)
	.gnss_velocity_north_m_s	(gnss_velocity_north_m_s	), //北向速度 (m/s, IEEE754)
	.gnss_velocity_east_m_s		(gnss_velocity_east_m_s		), //东向速度 (m/s, IEEE754)
	.gnss_velocity_down_m_s		(gnss_velocity_down_m_s		), //下向速度 (m/s, IEEE754)
	.gnss_velocity_accuracy_m_s	(gnss_velocity_accuracy_m_s	), //速度精度 (m/s * 100)
	.gnss_course_over_ground	(gnss_course_over_ground	), //地面速度 (m/s, IEEE754)
	.gnss_ground_speed_m_s		(gnss_ground_speed_m_s		), //地面航向 (弧度, IEEE754)
	.gnss_position_accuracy_mm	(gnss_position_accuracy_mm	), //位置精度 (毫米)
	.gnss_hdop					(gnss_hdop					), //水平精度因子 (*100)
	.gnss_vdop					(gnss_vdop					), //垂直精度因子 (*100)
	.gnss_sats_used				(gnss_sats_used				), //使用的卫星数
	.gnss_sats_visible			(gnss_sats_visible			), //可见卫星数
	.gnss_fix_type				(gnss_fix_type				), //定位类型 (0=无效, 2=2D, 3=3D, 4=RTK等)
	.gnss_status				(gnss_status				), //GNSS系统状态
	.gnss_valid					(gnss_valid					), //GNSS有效
	//解析后的GNSS数据输出
	.mag_field_ga_x				(mag_field_ga_x				), //X轴磁场强度 (高斯)
	.mag_field_ga_y				(mag_field_ga_y				), //Y轴磁场强度 (高斯)
	.mag_field_ga_z				(mag_field_ga_z				), //Z轴磁场强度 (高斯)
	.mag_field_valid			(mag_field_valid			)  //磁罗盘有效
);	 

endmodule

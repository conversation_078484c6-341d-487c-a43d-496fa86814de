============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Wed Aug 20 14:27:52 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.853551s wall, 1.421875s user + 4.406250s system = 5.828125s CPU (99.6%)

RUN-1004 : used memory is 80 MB, reserved memory is 40 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.875090s wall, 1.750000s user + 0.125000s system = 1.875000s CPU (100.0%)

RUN-1004 : used memory is 303 MB, reserved memory is 271 MB, peak memory is 306 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 4 view nodes, 48 trigger nets, 48 data nets.
KIT-1004 : Chipwatcher code = 0011000001101010
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=4,BUS_DIN_NUM=48,BUS_CTRL_NUM=112,BUS_WIDTH='{32'sb01000,32'sb01000,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb010000,32'sb0100000},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0101000,32'sb01001100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=134) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=134) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=4,BUS_DIN_NUM=48,BUS_CTRL_NUM=112,BUS_WIDTH='{32'sb01000,32'sb01000,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb010000,32'sb0100000},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0101000,32'sb01001100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=4,BUS_DIN_NUM=48,BUS_CTRL_NUM=112,BUS_WIDTH='{32'sb01000,32'sb01000,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb010000,32'sb0100000},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0101000,32'sb01001100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=4,BUS_DIN_NUM=48,BUS_CTRL_NUM=112,BUS_WIDTH='{32'sb01000,32'sb01000,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb010000,32'sb0100000},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0101000,32'sb01001100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=134)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=134)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=4,BUS_DIN_NUM=48,BUS_CTRL_NUM=112,BUS_WIDTH='{32'sb01000,32'sb01000,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb010000,32'sb0100000},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0101000,32'sb01001100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=4,BUS_DIN_NUM=48,BUS_CTRL_NUM=112,BUS_WIDTH='{32'sb01000,32'sb01000,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb010000,32'sb0100000},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0101000,32'sb01001100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22870/22 useful/useless nets, 19581/8 useful/useless insts
SYN-1016 : Merged 25 instances.
SYN-1032 : 22526/16 useful/useless nets, 20033/12 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 4 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 4 mux instances.
SYN-1015 : Optimize round 1, 421 better
SYN-1014 : Optimize round 2
SYN-1032 : 22171/60 useful/useless nets, 19678/64 useful/useless insts
SYN-1015 : Optimize round 2, 128 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.565485s wall, 2.468750s user + 0.093750s system = 2.562500s CPU (99.9%)

RUN-1004 : used memory is 329 MB, reserved memory is 295 MB, peak memory is 331 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22191/187 useful/useless nets, 19719/52 useful/useless insts
SYN-1016 : Merged 18 instances.
SYN-2571 : Optimize after map_dsp, round 1, 257 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 48 instances.
SYN-2501 : Optimize round 1, 98 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 18 macro adder
SYN-1019 : Optimized 17 mux instances.
SYN-1016 : Merged 12 instances.
SYN-1032 : 22704/4 useful/useless nets, 20232/3 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82453, tnet num: 22704, tinst num: 20231, tnode num: 115517, tedge num: 128739.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.264169s wall, 1.250000s user + 0.015625s system = 1.265625s CPU (100.1%)

RUN-1004 : used memory is 469 MB, reserved memory is 436 MB, peak memory is 469 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22704 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 251 (3.34), #lev = 7 (1.63)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 251 (3.34), #lev = 7 (1.63)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 541 instances into 251 LUTs, name keeping = 76%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 428 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 148 adder to BLE ...
SYN-4008 : Packed 148 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.766580s wall, 4.718750s user + 0.062500s system = 4.781250s CPU (100.3%)

RUN-1004 : used memory is 352 MB, reserved memory is 318 MB, peak memory is 576 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.681867s wall, 7.484375s user + 0.218750s system = 7.703125s CPU (100.3%)

RUN-1004 : used memory is 353 MB, reserved memory is 319 MB, peak memory is 576 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (289 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19472 instances
RUN-0007 : 5643 luts, 12229 seqs, 993 mslices, 519 lslices, 59 pads, 24 brams, 0 dsps
RUN-1001 : There are total 21952 nets
RUN-1001 : 16442 nets have 2 pins
RUN-1001 : 4309 nets have [3 - 5] pins
RUN-1001 : 839 nets have [6 - 10] pins
RUN-1001 : 237 nets have [11 - 20] pins
RUN-1001 : 104 nets have [21 - 99] pins
RUN-1001 : 21 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4792     
RUN-1001 :   No   |  No   |  Yes  |     707     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     461     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19470 instances, 5643 luts, 12229 seqs, 1512 slices, 293 macros(1512 instances: 993 mslices 519 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80844, tnet num: 21950, tinst num: 19470, tnode num: 113720, tedge num: 127110.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.309934s wall, 1.265625s user + 0.046875s system = 1.312500s CPU (100.2%)

RUN-1004 : used memory is 528 MB, reserved memory is 500 MB, peak memory is 576 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21950 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.313675s wall, 2.218750s user + 0.078125s system = 2.296875s CPU (99.3%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.65521e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19470.
PHY-3001 : Level 1 #clusters 2120.
PHY-3001 : End clustering;  0.181527s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (120.5%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 897884, overlap = 616.406
PHY-3002 : Step(2): len = 818730, overlap = 700.969
PHY-3002 : Step(3): len = 526158, overlap = 877.688
PHY-3002 : Step(4): len = 465545, overlap = 944.688
PHY-3002 : Step(5): len = 364221, overlap = 1038.97
PHY-3002 : Step(6): len = 323273, overlap = 1119.44
PHY-3002 : Step(7): len = 271816, overlap = 1192.94
PHY-3002 : Step(8): len = 245511, overlap = 1237.22
PHY-3002 : Step(9): len = 217854, overlap = 1263.62
PHY-3002 : Step(10): len = 198843, overlap = 1293.47
PHY-3002 : Step(11): len = 179521, overlap = 1351.03
PHY-3002 : Step(12): len = 165121, overlap = 1387.22
PHY-3002 : Step(13): len = 149899, overlap = 1403.28
PHY-3002 : Step(14): len = 141262, overlap = 1428.84
PHY-3002 : Step(15): len = 129177, overlap = 1457.53
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.19592e-06
PHY-3002 : Step(16): len = 136148, overlap = 1439.56
PHY-3002 : Step(17): len = 189435, overlap = 1308.41
PHY-3002 : Step(18): len = 201909, overlap = 1224.41
PHY-3002 : Step(19): len = 202175, overlap = 1141.06
PHY-3002 : Step(20): len = 196314, overlap = 1147.62
PHY-3002 : Step(21): len = 190281, overlap = 1136.25
PHY-3002 : Step(22): len = 184023, overlap = 1116.41
PHY-3002 : Step(23): len = 179878, overlap = 1114.28
PHY-3002 : Step(24): len = 176801, overlap = 1111.72
PHY-3002 : Step(25): len = 173010, overlap = 1101.84
PHY-3002 : Step(26): len = 170750, overlap = 1084.28
PHY-3002 : Step(27): len = 169140, overlap = 1076.47
PHY-3002 : Step(28): len = 168681, overlap = 1054.19
PHY-3002 : Step(29): len = 167320, overlap = 1033.91
PHY-3002 : Step(30): len = 166501, overlap = 1041.25
PHY-3002 : Step(31): len = 165414, overlap = 1047.28
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.39184e-06
PHY-3002 : Step(32): len = 175744, overlap = 1010.84
PHY-3002 : Step(33): len = 192379, overlap = 938.781
PHY-3002 : Step(34): len = 197749, overlap = 915.031
PHY-3002 : Step(35): len = 200436, overlap = 888
PHY-3002 : Step(36): len = 199316, overlap = 871.469
PHY-3002 : Step(37): len = 197933, overlap = 872.031
PHY-3002 : Step(38): len = 196133, overlap = 875.781
PHY-3002 : Step(39): len = 195514, overlap = 884.5
PHY-3002 : Step(40): len = 194825, overlap = 895.438
PHY-3002 : Step(41): len = 194683, overlap = 888.938
PHY-3002 : Step(42): len = 193090, overlap = 882.594
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.78368e-06
PHY-3002 : Step(43): len = 205354, overlap = 849.781
PHY-3002 : Step(44): len = 221730, overlap = 776.375
PHY-3002 : Step(45): len = 226747, overlap = 760.594
PHY-3002 : Step(46): len = 228670, overlap = 718.969
PHY-3002 : Step(47): len = 228308, overlap = 728.375
PHY-3002 : Step(48): len = 226967, overlap = 723.656
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 9.56735e-06
PHY-3002 : Step(49): len = 238921, overlap = 687.656
PHY-3002 : Step(50): len = 255704, overlap = 619.812
PHY-3002 : Step(51): len = 261540, overlap = 594.25
PHY-3002 : Step(52): len = 264037, overlap = 591.406
PHY-3002 : Step(53): len = 262588, overlap = 558.969
PHY-3002 : Step(54): len = 260671, overlap = 554.094
PHY-3002 : Step(55): len = 259529, overlap = 571.875
PHY-3002 : Step(56): len = 258408, overlap = 544.688
PHY-3002 : Step(57): len = 257517, overlap = 537.594
PHY-3002 : Step(58): len = 257414, overlap = 535.219
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.91347e-05
PHY-3002 : Step(59): len = 270090, overlap = 516.969
PHY-3002 : Step(60): len = 285365, overlap = 449.531
PHY-3002 : Step(61): len = 286815, overlap = 429.469
PHY-3002 : Step(62): len = 286094, overlap = 412.219
PHY-3002 : Step(63): len = 284086, overlap = 415.594
PHY-3002 : Step(64): len = 282452, overlap = 415.438
PHY-3002 : Step(65): len = 281630, overlap = 397.812
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.82694e-05
PHY-3002 : Step(66): len = 292354, overlap = 344.906
PHY-3002 : Step(67): len = 303029, overlap = 336.281
PHY-3002 : Step(68): len = 306231, overlap = 306.375
PHY-3002 : Step(69): len = 307341, overlap = 294.094
PHY-3002 : Step(70): len = 306102, overlap = 277.281
PHY-3002 : Step(71): len = 304771, overlap = 270.906
PHY-3002 : Step(72): len = 302054, overlap = 268.156
PHY-3002 : Step(73): len = 300695, overlap = 257.812
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 7.65388e-05
PHY-3002 : Step(74): len = 309184, overlap = 243.594
PHY-3002 : Step(75): len = 317546, overlap = 244.688
PHY-3002 : Step(76): len = 319972, overlap = 231.719
PHY-3002 : Step(77): len = 321855, overlap = 225.938
PHY-3002 : Step(78): len = 320511, overlap = 217.656
PHY-3002 : Step(79): len = 319793, overlap = 221.188
PHY-3002 : Step(80): len = 316639, overlap = 233.688
PHY-3002 : Step(81): len = 316771, overlap = 239.656
PHY-3002 : Step(82): len = 315789, overlap = 249.625
PHY-3002 : Step(83): len = 316512, overlap = 247.281
PHY-3002 : Step(84): len = 315109, overlap = 247.531
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000153078
PHY-3002 : Step(85): len = 321319, overlap = 229.25
PHY-3002 : Step(86): len = 328031, overlap = 215.969
PHY-3002 : Step(87): len = 327172, overlap = 209.938
PHY-3002 : Step(88): len = 327267, overlap = 228.344
PHY-3002 : Step(89): len = 327362, overlap = 220
PHY-3002 : Step(90): len = 328079, overlap = 218.969
PHY-3002 : Step(91): len = 327466, overlap = 227.656
PHY-3002 : Step(92): len = 328120, overlap = 230.344
PHY-3002 : Step(93): len = 328196, overlap = 216.531
PHY-3002 : Step(94): len = 329023, overlap = 205.531
PHY-3002 : Step(95): len = 327402, overlap = 210.406
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000291127
PHY-3002 : Step(96): len = 330917, overlap = 212.344
PHY-3002 : Step(97): len = 334989, overlap = 231.531
PHY-3002 : Step(98): len = 335523, overlap = 218.125
PHY-3002 : Step(99): len = 336281, overlap = 199.094
PHY-3002 : Step(100): len = 337190, overlap = 195.5
PHY-3002 : Step(101): len = 337968, overlap = 197.312
PHY-3002 : Step(102): len = 336841, overlap = 184.688
PHY-3002 : Step(103): len = 337039, overlap = 181.594
PHY-3002 : Step(104): len = 336706, overlap = 166.312
PHY-3002 : Step(105): len = 336806, overlap = 171.75
PHY-3002 : Step(106): len = 336291, overlap = 170.125
PHY-3002 : Step(107): len = 336496, overlap = 157
PHY-3002 : Step(108): len = 336626, overlap = 156.219
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000562488
PHY-3002 : Step(109): len = 338186, overlap = 146.094
PHY-3002 : Step(110): len = 342137, overlap = 139.844
PHY-3002 : Step(111): len = 343451, overlap = 144.5
PHY-3001 : :::11::: Try harder cell spreading with beta_ = 0.00104752
PHY-3002 : Step(112): len = 343841, overlap = 142.969
PHY-3002 : Step(113): len = 346743, overlap = 140.406
PHY-3002 : Step(114): len = 347509, overlap = 142.344
PHY-3002 : Step(115): len = 348601, overlap = 140.438
PHY-3002 : Step(116): len = 349314, overlap = 147.344
PHY-3002 : Step(117): len = 350189, overlap = 161.062
PHY-3002 : Step(118): len = 350574, overlap = 168.5
PHY-3002 : Step(119): len = 350801, overlap = 166.5
PHY-3002 : Step(120): len = 350954, overlap = 168.969
PHY-3002 : Step(121): len = 351155, overlap = 170.188
PHY-3002 : Step(122): len = 351061, overlap = 165.375
PHY-3002 : Step(123): len = 351050, overlap = 163.344
PHY-3002 : Step(124): len = 350826, overlap = 177.281
PHY-3002 : Step(125): len = 350843, overlap = 171.469
PHY-3002 : Step(126): len = 350817, overlap = 176.719
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.017467s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (89.5%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21952.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 450408, over cnt = 1211(3%), over = 5169, worst = 46
PHY-1001 : End global iterations;  0.938821s wall, 1.234375s user + 0.031250s system = 1.265625s CPU (134.8%)

PHY-1001 : Congestion index: top1 = 70.04, top5 = 50.75, top10 = 41.96, top15 = 36.60.
PHY-3001 : End congestion estimation;  1.216592s wall, 1.515625s user + 0.031250s system = 1.546875s CPU (127.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21950 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.021235s wall, 0.984375s user + 0.031250s system = 1.015625s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000101641
PHY-3002 : Step(127): len = 395177, overlap = 133.438
PHY-3002 : Step(128): len = 404836, overlap = 121.094
PHY-3002 : Step(129): len = 402932, overlap = 120.219
PHY-3002 : Step(130): len = 402481, overlap = 125.406
PHY-3002 : Step(131): len = 406837, overlap = 126.156
PHY-3002 : Step(132): len = 410540, overlap = 128.938
PHY-3002 : Step(133): len = 414975, overlap = 127.469
PHY-3002 : Step(134): len = 419309, overlap = 132.562
PHY-3002 : Step(135): len = 419523, overlap = 144.875
PHY-3002 : Step(136): len = 420330, overlap = 153.562
PHY-3002 : Step(137): len = 422429, overlap = 161.5
PHY-3002 : Step(138): len = 422849, overlap = 163.656
PHY-3002 : Step(139): len = 423979, overlap = 166.156
PHY-3002 : Step(140): len = 426156, overlap = 171.25
PHY-3002 : Step(141): len = 427147, overlap = 179.719
PHY-3002 : Step(142): len = 429207, overlap = 178.25
PHY-3002 : Step(143): len = 431512, overlap = 172.812
PHY-3002 : Step(144): len = 432437, overlap = 171.375
PHY-3002 : Step(145): len = 434327, overlap = 164.062
PHY-3002 : Step(146): len = 432781, overlap = 162.5
PHY-3002 : Step(147): len = 431299, overlap = 162.25
PHY-3002 : Step(148): len = 431525, overlap = 160.188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000203282
PHY-3002 : Step(149): len = 432015, overlap = 159.938
PHY-3002 : Step(150): len = 435063, overlap = 158.469
PHY-3002 : Step(151): len = 437594, overlap = 156.219
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(152): len = 441837, overlap = 150.656
PHY-3002 : Step(153): len = 451481, overlap = 154.062
PHY-3002 : Step(154): len = 456796, overlap = 151.188
PHY-3002 : Step(155): len = 456987, overlap = 152.156
PHY-3002 : Step(156): len = 458997, overlap = 145.688
PHY-3002 : Step(157): len = 461117, overlap = 143.188
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 81/21952.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 521472, over cnt = 2161(6%), over = 10109, worst = 40
PHY-1001 : End global iterations;  1.202511s wall, 1.937500s user + 0.062500s system = 2.000000s CPU (166.3%)

PHY-1001 : Congestion index: top1 = 78.08, top5 = 58.91, top10 = 50.28, top15 = 44.94.
PHY-3001 : End congestion estimation;  1.508676s wall, 2.250000s user + 0.062500s system = 2.312500s CPU (153.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21950 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.029554s wall, 1.000000s user + 0.031250s system = 1.031250s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.9822e-05
PHY-3002 : Step(158): len = 468496, overlap = 364.531
PHY-3002 : Step(159): len = 473988, overlap = 309.438
PHY-3002 : Step(160): len = 465582, overlap = 295.406
PHY-3002 : Step(161): len = 462722, overlap = 272.25
PHY-3002 : Step(162): len = 460692, overlap = 258.5
PHY-3002 : Step(163): len = 458497, overlap = 251.938
PHY-3002 : Step(164): len = 458259, overlap = 241.375
PHY-3002 : Step(165): len = 456100, overlap = 229.781
PHY-3002 : Step(166): len = 452863, overlap = 220.219
PHY-3002 : Step(167): len = 453038, overlap = 217.969
PHY-3002 : Step(168): len = 449530, overlap = 228.656
PHY-3002 : Step(169): len = 447298, overlap = 231.594
PHY-3002 : Step(170): len = 447338, overlap = 231.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000199644
PHY-3002 : Step(171): len = 446716, overlap = 222.281
PHY-3002 : Step(172): len = 447906, overlap = 219.312
PHY-3002 : Step(173): len = 449558, overlap = 214.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000399288
PHY-3002 : Step(174): len = 453667, overlap = 196.031
PHY-3002 : Step(175): len = 461244, overlap = 181.531
PHY-3002 : Step(176): len = 465818, overlap = 176.406
PHY-3002 : Step(177): len = 466180, overlap = 173.969
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000798576
PHY-3002 : Step(178): len = 467153, overlap = 166.344
PHY-3002 : Step(179): len = 470815, overlap = 167
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80844, tnet num: 21950, tinst num: 19470, tnode num: 113720, tedge num: 127110.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.904903s wall, 1.828125s user + 0.078125s system = 1.906250s CPU (100.1%)

RUN-1004 : used memory is 560 MB, reserved memory is 541 MB, peak memory is 692 MB
OPT-1001 : Total overflow 539.69 peak overflow 4.03
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 545/21952.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 544112, over cnt = 2410(6%), over = 8451, worst = 30
PHY-1001 : End global iterations;  1.404351s wall, 2.156250s user + 0.062500s system = 2.218750s CPU (158.0%)

PHY-1001 : Congestion index: top1 = 56.44, top5 = 47.62, top10 = 42.61, top15 = 39.47.
PHY-1001 : End incremental global routing;  1.691875s wall, 2.437500s user + 0.062500s system = 2.500000s CPU (147.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21950 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.121549s wall, 1.109375s user + 0.015625s system = 1.125000s CPU (100.3%)

OPT-1001 : 14 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19394 has valid locations, 195 needs to be replaced
PHY-3001 : design contains 19651 instances, 5732 luts, 12321 seqs, 1512 slices, 293 macros(1512 instances: 993 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 484983
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17253/22133.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 554768, over cnt = 2462(6%), over = 8541, worst = 30
PHY-1001 : End global iterations;  0.261902s wall, 0.343750s user + 0.046875s system = 0.390625s CPU (149.1%)

PHY-1001 : Congestion index: top1 = 56.83, top5 = 47.92, top10 = 42.85, top15 = 39.78.
PHY-3001 : End congestion estimation;  0.600441s wall, 0.687500s user + 0.062500s system = 0.750000s CPU (124.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81410, tnet num: 22131, tinst num: 19651, tnode num: 114481, tedge num: 127880.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.831787s wall, 1.812500s user + 0.015625s system = 1.828125s CPU (99.8%)

RUN-1004 : used memory is 605 MB, reserved memory is 604 MB, peak memory is 695 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22131 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.007063s wall, 2.968750s user + 0.031250s system = 3.000000s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(180): len = 484911, overlap = 2.875
PHY-3002 : Step(181): len = 485676, overlap = 2.875
PHY-3002 : Step(182): len = 486258, overlap = 2.9375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17287/22133.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 553072, over cnt = 2465(7%), over = 8629, worst = 30
PHY-1001 : End global iterations;  0.205784s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (159.5%)

PHY-1001 : Congestion index: top1 = 56.64, top5 = 48.07, top10 = 43.10, top15 = 39.98.
PHY-3001 : End congestion estimation;  0.485911s wall, 0.593750s user + 0.015625s system = 0.609375s CPU (125.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22131 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.502641s wall, 1.500000s user + 0.015625s system = 1.515625s CPU (100.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000860688
PHY-3002 : Step(183): len = 485924, overlap = 169.094
PHY-3002 : Step(184): len = 486141, overlap = 168.938
PHY-3001 : Final: Len = 486141, Over = 168.938
PHY-3001 : End incremental placement;  6.507257s wall, 6.703125s user + 0.218750s system = 6.921875s CPU (106.4%)

OPT-1001 : Total overflow 544.84 peak overflow 4.03
OPT-1001 : End high-fanout net optimization;  9.936823s wall, 11.046875s user + 0.296875s system = 11.343750s CPU (114.2%)

OPT-1001 : Current memory(MB): used = 697, reserve = 684, peak = 715.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17296/22133.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 554672, over cnt = 2422(6%), over = 8143, worst = 30
PHY-1002 : len = 596976, over cnt = 1709(4%), over = 4195, worst = 18
PHY-1002 : len = 637704, over cnt = 623(1%), over = 1312, worst = 16
PHY-1002 : len = 651512, over cnt = 240(0%), over = 442, worst = 10
PHY-1002 : len = 658128, over cnt = 27(0%), over = 46, worst = 5
PHY-1001 : End global iterations;  1.383338s wall, 2.015625s user + 0.046875s system = 2.062500s CPU (149.1%)

PHY-1001 : Congestion index: top1 = 48.97, top5 = 43.27, top10 = 40.05, top15 = 38.00.
OPT-1001 : End congestion update;  1.698152s wall, 2.343750s user + 0.046875s system = 2.390625s CPU (140.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22131 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.034416s wall, 1.015625s user + 0.015625s system = 1.031250s CPU (99.7%)

OPT-0007 : Start: WNS 4137 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.742564s wall, 3.359375s user + 0.062500s system = 3.421875s CPU (124.8%)

OPT-1001 : Current memory(MB): used = 673, reserve = 660, peak = 715.
OPT-1001 : End physical optimization;  14.965213s wall, 16.703125s user + 0.515625s system = 17.218750s CPU (115.1%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5732 LUT to BLE ...
SYN-4008 : Packed 5732 LUT and 2732 SEQ to BLE.
SYN-4003 : Packing 9589 remaining SEQ's ...
SYN-4005 : Packed 3406 SEQ with LUT/SLICE
SYN-4006 : 126 single LUT's are left
SYN-4006 : 6183 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11915/13779 primitive instances ...
PHY-3001 : End packing;  3.151009s wall, 3.140625s user + 0.015625s system = 3.156250s CPU (100.2%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8115 instances
RUN-1001 : 4014 mslices, 4013 lslices, 59 pads, 24 brams, 0 dsps
RUN-1001 : There are total 19452 nets
RUN-1001 : 13633 nets have 2 pins
RUN-1001 : 4399 nets have [3 - 5] pins
RUN-1001 : 905 nets have [6 - 10] pins
RUN-1001 : 381 nets have [11 - 20] pins
RUN-1001 : 125 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8113 instances, 8027 slices, 293 macros(1512 instances: 993 mslices 519 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 502705, Over = 378.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7777/19452.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 625176, over cnt = 1548(4%), over = 2493, worst = 9
PHY-1002 : len = 630840, over cnt = 1047(2%), over = 1497, worst = 7
PHY-1002 : len = 640816, over cnt = 564(1%), over = 771, worst = 7
PHY-1002 : len = 648352, over cnt = 217(0%), over = 289, worst = 6
PHY-1002 : len = 653952, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.512290s wall, 2.234375s user + 0.031250s system = 2.265625s CPU (149.8%)

PHY-1001 : Congestion index: top1 = 50.11, top5 = 43.72, top10 = 40.10, top15 = 37.84.
PHY-3001 : End congestion estimation;  1.914162s wall, 2.625000s user + 0.031250s system = 2.656250s CPU (138.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67492, tnet num: 19450, tinst num: 8113, tnode num: 91518, tedge num: 111206.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.902722s wall, 1.875000s user + 0.031250s system = 1.906250s CPU (100.2%)

RUN-1004 : used memory is 597 MB, reserved memory is 588 MB, peak memory is 715 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19450 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.063954s wall, 3.015625s user + 0.046875s system = 3.062500s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.13381e-05
PHY-3002 : Step(185): len = 507811, overlap = 352.25
PHY-3002 : Step(186): len = 506699, overlap = 372
PHY-3002 : Step(187): len = 506908, overlap = 383
PHY-3002 : Step(188): len = 509416, overlap = 391.75
PHY-3002 : Step(189): len = 509209, overlap = 397.5
PHY-3002 : Step(190): len = 509831, overlap = 403
PHY-3002 : Step(191): len = 506349, overlap = 405
PHY-3002 : Step(192): len = 504198, overlap = 402.75
PHY-3002 : Step(193): len = 501214, overlap = 412.5
PHY-3002 : Step(194): len = 499506, overlap = 409.25
PHY-3002 : Step(195): len = 498912, overlap = 406.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000102676
PHY-3002 : Step(196): len = 503459, overlap = 394.25
PHY-3002 : Step(197): len = 507610, overlap = 387
PHY-3002 : Step(198): len = 508985, overlap = 380.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000205352
PHY-3002 : Step(199): len = 519529, overlap = 370
PHY-3002 : Step(200): len = 530448, overlap = 342.75
PHY-3002 : Step(201): len = 527228, overlap = 344.75
PHY-3002 : Step(202): len = 524690, overlap = 350.75
PHY-3002 : Step(203): len = 524518, overlap = 346.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.778194s wall, 0.828125s user + 0.984375s system = 1.812500s CPU (232.9%)

PHY-3001 : Trial Legalized: Len = 638908
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 577/19452.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 721104, over cnt = 2381(6%), over = 3919, worst = 7
PHY-1002 : len = 736088, over cnt = 1508(4%), over = 2151, worst = 6
PHY-1002 : len = 754888, over cnt = 559(1%), over = 740, worst = 5
PHY-1002 : len = 762760, over cnt = 231(0%), over = 277, worst = 5
PHY-1002 : len = 768736, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.056160s wall, 3.437500s user + 0.015625s system = 3.453125s CPU (167.9%)

PHY-1001 : Congestion index: top1 = 50.99, top5 = 45.53, top10 = 42.80, top15 = 40.94.
PHY-3001 : End congestion estimation;  2.454412s wall, 3.828125s user + 0.015625s system = 3.843750s CPU (156.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19450 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.049190s wall, 1.031250s user + 0.015625s system = 1.046875s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000187895
PHY-3002 : Step(204): len = 596727, overlap = 92.75
PHY-3002 : Step(205): len = 576301, overlap = 142.5
PHY-3002 : Step(206): len = 564476, overlap = 182
PHY-3002 : Step(207): len = 557811, overlap = 211.5
PHY-3002 : Step(208): len = 553138, overlap = 248.75
PHY-3002 : Step(209): len = 550882, overlap = 260.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000375791
PHY-3002 : Step(210): len = 555959, overlap = 253
PHY-3002 : Step(211): len = 560592, overlap = 250
PHY-3002 : Step(212): len = 560125, overlap = 254.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(213): len = 563679, overlap = 253.25
PHY-3002 : Step(214): len = 570738, overlap = 245.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.070590s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (110.7%)

PHY-3001 : Legalized: Len = 614294, Over = 0
PHY-3001 : Spreading special nets. 33 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.090399s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (103.7%)

PHY-3001 : 52 instances has been re-located, deltaX = 14, deltaY = 31, maxDist = 2.
PHY-3001 : Final: Len = 615354, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67492, tnet num: 19450, tinst num: 8113, tnode num: 91518, tedge num: 111206.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.001085s wall, 2.000000s user + 0.015625s system = 2.015625s CPU (100.7%)

RUN-1004 : used memory is 600 MB, reserved memory is 602 MB, peak memory is 715 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3983/19452.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 706296, over cnt = 2228(6%), over = 3514, worst = 7
PHY-1002 : len = 718856, over cnt = 1252(3%), over = 1704, worst = 7
PHY-1002 : len = 733576, over cnt = 443(1%), over = 571, worst = 5
PHY-1002 : len = 740928, over cnt = 71(0%), over = 91, worst = 3
PHY-1002 : len = 742768, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.905188s wall, 3.093750s user + 0.046875s system = 3.140625s CPU (164.8%)

PHY-1001 : Congestion index: top1 = 49.38, top5 = 43.58, top10 = 40.71, top15 = 38.91.
PHY-1001 : End incremental global routing;  2.241637s wall, 3.437500s user + 0.046875s system = 3.484375s CPU (155.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19450 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.014034s wall, 0.984375s user + 0.015625s system = 1.000000s CPU (98.6%)

OPT-1001 : 3 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8048 has valid locations, 38 needs to be replaced
PHY-3001 : design contains 8148 instances, 8062 slices, 293 macros(1512 instances: 993 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 619042
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17495/19500.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 749072, over cnt = 48(0%), over = 69, worst = 4
PHY-1002 : len = 749160, over cnt = 34(0%), over = 41, worst = 4
PHY-1002 : len = 749280, over cnt = 17(0%), over = 21, worst = 3
PHY-1002 : len = 749480, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 749664, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.794456s wall, 0.828125s user + 0.046875s system = 0.875000s CPU (110.1%)

PHY-1001 : Congestion index: top1 = 50.06, top5 = 43.89, top10 = 40.98, top15 = 39.16.
PHY-3001 : End congestion estimation;  1.119212s wall, 1.125000s user + 0.062500s system = 1.187500s CPU (106.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67719, tnet num: 19498, tinst num: 8148, tnode num: 91839, tedge num: 111554.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.988401s wall, 1.984375s user + 0.015625s system = 2.000000s CPU (100.6%)

RUN-1004 : used memory is 629 MB, reserved memory is 618 MB, peak memory is 715 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19498 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.998503s wall, 2.968750s user + 0.031250s system = 3.000000s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(215): len = 618989, overlap = 2
PHY-3002 : Step(216): len = 619157, overlap = 2
PHY-3002 : Step(217): len = 618949, overlap = 1.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17486/19500.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 747184, over cnt = 47(0%), over = 65, worst = 4
PHY-1002 : len = 747376, over cnt = 27(0%), over = 27, worst = 1
PHY-1002 : len = 747464, over cnt = 12(0%), over = 12, worst = 1
PHY-1002 : len = 747576, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 747600, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.820427s wall, 0.890625s user + 0.078125s system = 0.968750s CPU (118.1%)

PHY-1001 : Congestion index: top1 = 49.76, top5 = 43.95, top10 = 41.01, top15 = 39.15.
PHY-3001 : End congestion estimation;  1.144900s wall, 1.218750s user + 0.078125s system = 1.296875s CPU (113.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19498 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.029169s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000368688
PHY-3002 : Step(218): len = 618983, overlap = 6
PHY-3002 : Step(219): len = 618908, overlap = 5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008160s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (191.5%)

PHY-3001 : Legalized: Len = 619027, Over = 0
PHY-3001 : End spreading;  0.084646s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (92.3%)

PHY-3001 : Final: Len = 619027, Over = 0
PHY-3001 : End incremental placement;  7.013202s wall, 7.046875s user + 0.234375s system = 7.281250s CPU (103.8%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.808820s wall, 12.187500s user + 0.296875s system = 12.484375s CPU (115.5%)

OPT-1001 : Current memory(MB): used = 708, reserve = 700, peak = 715.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17486/19500.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 747432, over cnt = 37(0%), over = 55, worst = 5
PHY-1002 : len = 747336, over cnt = 28(0%), over = 29, worst = 2
PHY-1002 : len = 747448, over cnt = 11(0%), over = 12, worst = 2
PHY-1002 : len = 747536, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 747600, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.870734s wall, 0.906250s user + 0.015625s system = 0.921875s CPU (105.9%)

PHY-1001 : Congestion index: top1 = 49.55, top5 = 43.77, top10 = 40.87, top15 = 39.10.
OPT-1001 : End congestion update;  1.209637s wall, 1.250000s user + 0.015625s system = 1.265625s CPU (104.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19498 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.825347s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (100.3%)

OPT-0007 : Start: WNS 4165 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  2.040035s wall, 2.078125s user + 0.015625s system = 2.093750s CPU (102.6%)

OPT-1001 : Current memory(MB): used = 708, reserve = 700, peak = 715.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19498 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.809672s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (100.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17530/19500.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 747600, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.125128s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (99.9%)

PHY-1001 : Congestion index: top1 = 49.55, top5 = 43.77, top10 = 40.87, top15 = 39.10.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19498 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.822760s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (98.8%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4165 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 49.000000
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4165ps with logic level 5 
OPT-1001 : End physical optimization;  17.209369s wall, 18.625000s user + 0.328125s system = 18.953125s CPU (110.1%)

RUN-1003 : finish command "place" in  75.380585s wall, 137.921875s user + 8.281250s system = 146.203125s CPU (194.0%)

RUN-1004 : used memory is 594 MB, reserved memory is 577 MB, peak memory is 715 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.737167s wall, 2.906250s user + 0.015625s system = 2.921875s CPU (168.2%)

RUN-1004 : used memory is 594 MB, reserved memory is 578 MB, peak memory is 715 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8150 instances
RUN-1001 : 4049 mslices, 4013 lslices, 59 pads, 24 brams, 0 dsps
RUN-1001 : There are total 19500 nets
RUN-1001 : 13643 nets have 2 pins
RUN-1001 : 4423 nets have [3 - 5] pins
RUN-1001 : 911 nets have [6 - 10] pins
RUN-1001 : 389 nets have [11 - 20] pins
RUN-1001 : 125 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67719, tnet num: 19498, tinst num: 8148, tnode num: 91839, tedge num: 111554.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.883700s wall, 1.890625s user + 0.000000s system = 1.890625s CPU (100.4%)

RUN-1004 : used memory is 606 MB, reserved memory is 604 MB, peak memory is 715 MB
PHY-1001 : 4049 mslices, 4013 lslices, 59 pads, 24 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19498 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 687480, over cnt = 2333(6%), over = 3873, worst = 8
PHY-1002 : len = 705024, over cnt = 1382(3%), over = 1917, worst = 8
PHY-1002 : len = 721680, over cnt = 541(1%), over = 718, worst = 5
PHY-1002 : len = 733512, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 733816, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.784856s wall, 3.140625s user + 0.046875s system = 3.187500s CPU (178.6%)

PHY-1001 : Congestion index: top1 = 49.44, top5 = 43.66, top10 = 40.66, top15 = 38.82.
PHY-1001 : End global routing;  2.162890s wall, 3.500000s user + 0.046875s system = 3.546875s CPU (164.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 698, reserve = 692, peak = 715.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 967, reserve = 959, peak = 967.
PHY-1001 : End build detailed router design. 4.587524s wall, 4.578125s user + 0.015625s system = 4.593750s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 190256, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.904590s wall, 0.906250s user + 0.000000s system = 0.906250s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 1003, reserve = 996, peak = 1003.
PHY-1001 : End phase 1; 0.911687s wall, 0.906250s user + 0.000000s system = 0.906250s CPU (99.4%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 55% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.65892e+06, over cnt = 1391(0%), over = 1394, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1020, reserve = 1013, peak = 1020.
PHY-1001 : End initial routed; 19.417034s wall, 49.453125s user + 0.234375s system = 49.687500s CPU (255.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18220(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.328   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.690235s wall, 3.687500s user + 0.000000s system = 3.687500s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 1031, reserve = 1024, peak = 1031.
PHY-1001 : End phase 2; 23.107436s wall, 53.140625s user + 0.234375s system = 53.375000s CPU (231.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.65892e+06, over cnt = 1391(0%), over = 1394, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.259005s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (102.6%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.64709e+06, over cnt = 429(0%), over = 429, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.885344s wall, 1.500000s user + 0.000000s system = 1.500000s CPU (169.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.64818e+06, over cnt = 75(0%), over = 75, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.349017s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (156.7%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.64901e+06, over cnt = 9(0%), over = 9, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.308431s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (106.4%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.64926e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 4; 0.233964s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (100.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18220(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.174   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.687496s wall, 3.671875s user + 0.015625s system = 3.687500s CPU (100.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 323 feed throughs used by 280 nets
PHY-1001 : End commit to database; 2.242829s wall, 2.234375s user + 0.015625s system = 2.250000s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 1120, reserve = 1116, peak = 1120.
PHY-1001 : End phase 3; 8.588677s wall, 9.390625s user + 0.046875s system = 9.437500s CPU (109.9%)

PHY-1003 : Routed, final wirelength = 1.64926e+06
PHY-1001 : Current memory(MB): used = 1124, reserve = 1121, peak = 1124.
PHY-1001 : End export database. 0.062534s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.9%)

PHY-1001 : End detail routing;  37.709485s wall, 68.515625s user + 0.312500s system = 68.828125s CPU (182.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67719, tnet num: 19498, tinst num: 8148, tnode num: 91839, tedge num: 111554.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.739852s wall, 1.734375s user + 0.000000s system = 1.734375s CPU (99.7%)

RUN-1004 : used memory is 1056 MB, reserved memory is 1065 MB, peak memory is 1124 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  46.260975s wall, 78.375000s user + 0.375000s system = 78.750000s CPU (170.2%)

RUN-1004 : used memory is 1055 MB, reserved memory is 1066 MB, peak memory is 1124 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8939   out of  19600   45.61%
#reg                    12452   out of  19600   63.53%
#le                     15070
  #lut only              2618   out of  15070   17.37%
  #reg only              6131   out of  15070   40.68%
  #lut&reg               6321   out of  15070   41.94%
#dsp                        0   out of     29    0.00%
#bram                      24   out of     64   37.50%
  #bram9k                  22
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       59   out of    188   31.38%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6810
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          159
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         F2        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         D1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         B3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         C1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         A3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         A2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         B2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         B1        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F15        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDZ        INPUT        K15        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         P2        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         N4        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         M5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         N6        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         N5        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         P5        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M3        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         M4        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         H2        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         M1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         L1        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         N1        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         N3        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         M2        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         T4        LVCMOS33           8            NONE           OREG       
    Fog_ENZ        OUTPUT         T7        LVCMOS33           8            NONE           OREG       
    Fog_INT        OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT         C3        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT        B15        LVCMOS33           8            N/A            OREG       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        R15        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         K2        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT         F6        LVCMOS25           8            N/A            NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15070  |7427    |1512    |12496   |24      |0       |
|  AnyFog_dataX                      |AnyFog          |207    |84      |22      |171     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |87     |57      |22      |51      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |208    |88      |22      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |87     |63      |22      |52      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |209    |106     |22      |174     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |87     |61      |22      |52      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2934   |693     |39      |2858    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |61     |34      |5       |53      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |211    |71      |5       |202     |0       |0       |
|    STADOP_com2                     |STADOP          |546    |54      |0       |544     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |64     |43      |14      |41      |0       |0       |
|    head_com2                       |uniheading      |274    |95      |5       |261     |0       |0       |
|    rmc_com2                        |Gprmc           |41     |41      |0       |33      |0       |0       |
|    uart_com2                       |Agrica          |1413   |323     |10      |1400    |0       |0       |
|  COM3                              |COM3_Control    |287    |146     |19      |239     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |62     |40      |5       |52      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |61     |39      |14      |37      |0       |0       |
|    rmc_com3                        |Gprmc           |164    |67      |0       |150     |0       |0       |
|  DATA                              |Data_Processing |8787   |4535    |1122    |6998    |0       |0       |
|    DIV_Dtemp                       |Divider         |781    |366     |84      |653     |0       |0       |
|    DIV_Utemp                       |Divider         |588    |322     |84      |457     |0       |0       |
|    DIV_accX                        |Divider         |569    |264     |84      |444     |0       |0       |
|    DIV_accY                        |Divider         |663    |299     |102     |511     |0       |0       |
|    DIV_accZ                        |Divider         |680    |391     |132     |478     |0       |0       |
|    DIV_rateX                       |Divider         |676    |416     |132     |469     |0       |0       |
|    DIV_rateY                       |Divider         |573    |349     |132     |369     |0       |0       |
|    DIV_rateZ                       |Divider         |571    |385     |132     |369     |0       |0       |
|    genclk                          |genclk          |288    |181     |89      |117     |0       |0       |
|  FMC                               |FMC_Ctrl        |481    |421     |43      |358     |0       |0       |
|  IIC                               |I2C_master      |272    |217     |11      |248     |0       |0       |
|  IMU_CTRL                          |SCHA634         |935    |687     |61      |751     |0       |0       |
|    CtrlData                        |CtrlData        |470    |415     |47      |337     |0       |0       |
|      usms                          |Time_1ms        |27     |21      |5       |19      |0       |0       |
|    SPIM                            |SPI_SCHA634     |465    |272     |14      |414     |0       |0       |
|  POWER                             |POWER_EN        |97     |51      |38      |37      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |643    |399     |113     |435     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |643    |399     |113     |435     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |281    |160     |0       |268     |0       |0       |
|        reg_inst                    |register        |279    |158     |0       |266     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |362    |239     |113     |167     |0       |0       |
|        bus_inst                    |bus_top         |155    |99      |56      |56      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |25     |15      |10      |8       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |28     |18      |10      |12      |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |51     |33      |18      |18      |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |51     |33      |18      |18      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |131    |102     |29      |82      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13583  
    #2          2       3441   
    #3          3        688   
    #4          4        294   
    #5        5-10       967   
    #6        11-50      447   
    #7       51-100      11    
    #8       101-500      3    
    #9        >500        2    
  Average     2.12             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.117115s wall, 3.609375s user + 0.031250s system = 3.640625s CPU (172.0%)

RUN-1004 : used memory is 1056 MB, reserved memory is 1066 MB, peak memory is 1124 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67719, tnet num: 19498, tinst num: 8148, tnode num: 91839, tedge num: 111554.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.765003s wall, 1.765625s user + 0.000000s system = 1.765625s CPU (100.0%)

RUN-1004 : used memory is 1058 MB, reserved memory is 1067 MB, peak memory is 1124 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19498 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.469339s wall, 1.468750s user + 0.000000s system = 1.468750s CPU (100.0%)

RUN-1004 : used memory is 1063 MB, reserved memory is 1071 MB, peak memory is 1124 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: a1322e0d67a901121a00c146f059d89851f0425aedc9ec6538272f84a697975d -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8148
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19500, pip num: 145269
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 323
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3231 valid insts, and 408845 bits set as '1'.
BIT-1004 : the usercode register value: 00000000000100000011000001101010
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.493113s wall, 127.015625s user + 0.156250s system = 127.171875s CPU (1017.9%)

RUN-1004 : used memory is 1186 MB, reserved memory is 1178 MB, peak memory is 1301 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250820_142751.log"

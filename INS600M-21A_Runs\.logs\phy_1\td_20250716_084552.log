============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Wed Jul 16 08:45:52 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.703043s wall, 1.640625s user + 4.046875s system = 5.687500s CPU (99.7%)

RUN-1004 : used memory is 78 MB, reserved memory is 41 MB, peak memory is 89 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.925446s wall, 1.828125s user + 0.093750s system = 1.921875s CPU (99.8%)

RUN-1004 : used memory is 299 MB, reserved memory is 268 MB, peak memory is 302 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 2 view nodes, 24 trigger nets, 24 data nets.
KIT-1004 : Chipwatcher code = 1010010110001101
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=78) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=78) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=78)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=78)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22204/12 useful/useless nets, 19219/4 useful/useless insts
SYN-1016 : Merged 17 instances.
SYN-1032 : 21980/16 useful/useless nets, 19543/12 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 2 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 2 mux instances.
SYN-1015 : Optimize round 1, 279 better
SYN-1014 : Optimize round 2
SYN-1032 : 21771/30 useful/useless nets, 19334/32 useful/useless insts
SYN-1015 : Optimize round 2, 64 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.345900s wall, 2.281250s user + 0.062500s system = 2.343750s CPU (99.9%)

RUN-1004 : used memory is 324 MB, reserved memory is 291 MB, peak memory is 326 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 21795/155 useful/useless nets, 19379/29 useful/useless insts
SYN-1016 : Merged 21 instances.
SYN-2571 : Optimize after map_dsp, round 1, 205 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 24 instances.
SYN-2501 : Optimize round 1, 50 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 14 macro adder
SYN-1019 : Optimized 9 mux instances.
SYN-1016 : Merged 10 instances.
SYN-1032 : 22166/5 useful/useless nets, 19750/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80402, tnet num: 22166, tinst num: 19749, tnode num: 113002, tedge num: 125646.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.296249s wall, 1.265625s user + 0.031250s system = 1.296875s CPU (100.0%)

RUN-1004 : used memory is 460 MB, reserved memory is 429 MB, peak memory is 460 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22166 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 173 (3.57), #lev = 7 (1.79)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 173 (3.57), #lev = 7 (1.79)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 387 instances into 173 LUTs, name keeping = 77%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 290 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 108 adder to BLE ...
SYN-4008 : Packed 108 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.748293s wall, 4.671875s user + 0.078125s system = 4.750000s CPU (100.0%)

RUN-1004 : used memory is 360 MB, reserved memory is 342 MB, peak memory is 567 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.432866s wall, 7.265625s user + 0.156250s system = 7.421875s CPU (99.9%)

RUN-1004 : used memory is 360 MB, reserved memory is 342 MB, peak memory is 567 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (177 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19058 instances
RUN-0007 : 5502 luts, 12036 seqs, 937 mslices, 494 lslices, 60 pads, 24 brams, 0 dsps
RUN-1001 : There are total 21502 nets
RUN-1001 : 16194 nets have 2 pins
RUN-1001 : 4140 nets have [3 - 5] pins
RUN-1001 : 816 nets have [6 - 10] pins
RUN-1001 : 224 nets have [11 - 20] pins
RUN-1001 : 110 nets have [21 - 99] pins
RUN-1001 : 18 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4787     
RUN-1001 :   No   |  No   |  Yes  |     631     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     349     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19056 instances, 5502 luts, 12036 seqs, 1431 slices, 283 macros(1431 instances: 937 mslices 494 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 61%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 78965, tnet num: 21500, tinst num: 19056, tnode num: 111159, tedge num: 124050.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.300331s wall, 1.265625s user + 0.031250s system = 1.296875s CPU (99.7%)

RUN-1004 : used memory is 519 MB, reserved memory is 491 MB, peak memory is 567 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21500 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.217229s wall, 2.187500s user + 0.031250s system = 2.218750s CPU (100.1%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.53311e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19056.
PHY-3001 : Level 1 #clusters 2163.
PHY-3001 : End clustering;  0.151904s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (144.0%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 61%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 844717, overlap = 608.344
PHY-3002 : Step(2): len = 759867, overlap = 652.188
PHY-3002 : Step(3): len = 489461, overlap = 846.688
PHY-3002 : Step(4): len = 427897, overlap = 919.812
PHY-3002 : Step(5): len = 340780, overlap = 988.312
PHY-3002 : Step(6): len = 303518, overlap = 1052.5
PHY-3002 : Step(7): len = 254332, overlap = 1114.09
PHY-3002 : Step(8): len = 230127, overlap = 1148.06
PHY-3002 : Step(9): len = 209612, overlap = 1186
PHY-3002 : Step(10): len = 190335, overlap = 1210.53
PHY-3002 : Step(11): len = 174991, overlap = 1257.88
PHY-3002 : Step(12): len = 159704, overlap = 1287.81
PHY-3002 : Step(13): len = 150269, overlap = 1312.31
PHY-3002 : Step(14): len = 137728, overlap = 1342.66
PHY-3002 : Step(15): len = 131235, overlap = 1362.56
PHY-3002 : Step(16): len = 121439, overlap = 1384.19
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.2951e-06
PHY-3002 : Step(17): len = 127951, overlap = 1351.28
PHY-3002 : Step(18): len = 174590, overlap = 1237.38
PHY-3002 : Step(19): len = 191901, overlap = 1171.16
PHY-3002 : Step(20): len = 195940, overlap = 1113.81
PHY-3002 : Step(21): len = 191067, overlap = 1094.34
PHY-3002 : Step(22): len = 186224, overlap = 1065.69
PHY-3002 : Step(23): len = 182041, overlap = 1038.31
PHY-3002 : Step(24): len = 176502, overlap = 1033.16
PHY-3002 : Step(25): len = 173879, overlap = 1026.38
PHY-3002 : Step(26): len = 168850, overlap = 1040.94
PHY-3002 : Step(27): len = 167171, overlap = 1053.97
PHY-3002 : Step(28): len = 165296, overlap = 1061.09
PHY-3002 : Step(29): len = 164087, overlap = 1062
PHY-3002 : Step(30): len = 162584, overlap = 1060.47
PHY-3002 : Step(31): len = 162291, overlap = 1057.75
PHY-3002 : Step(32): len = 161461, overlap = 1042.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.59019e-06
PHY-3002 : Step(33): len = 172002, overlap = 1028.59
PHY-3002 : Step(34): len = 191432, overlap = 905.688
PHY-3002 : Step(35): len = 197300, overlap = 809.5
PHY-3002 : Step(36): len = 199802, overlap = 770
PHY-3002 : Step(37): len = 198981, overlap = 748.344
PHY-3002 : Step(38): len = 198271, overlap = 735.438
PHY-3002 : Step(39): len = 196296, overlap = 743.406
PHY-3002 : Step(40): len = 195477, overlap = 777.344
PHY-3002 : Step(41): len = 194767, overlap = 773.188
PHY-3002 : Step(42): len = 193931, overlap = 786.156
PHY-3002 : Step(43): len = 193575, overlap = 786.438
PHY-3002 : Step(44): len = 192848, overlap = 802.25
PHY-3002 : Step(45): len = 192380, overlap = 792.469
PHY-3002 : Step(46): len = 192406, overlap = 790
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 5.18039e-06
PHY-3002 : Step(47): len = 201620, overlap = 729.188
PHY-3002 : Step(48): len = 216231, overlap = 675.125
PHY-3002 : Step(49): len = 218728, overlap = 620.844
PHY-3002 : Step(50): len = 220422, overlap = 620.344
PHY-3002 : Step(51): len = 219356, overlap = 619.719
PHY-3002 : Step(52): len = 217983, overlap = 610.156
PHY-3002 : Step(53): len = 216928, overlap = 602.125
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 1.03608e-05
PHY-3002 : Step(54): len = 225892, overlap = 590.25
PHY-3002 : Step(55): len = 239309, overlap = 546.562
PHY-3002 : Step(56): len = 244716, overlap = 513.969
PHY-3002 : Step(57): len = 248006, overlap = 493.875
PHY-3002 : Step(58): len = 247191, overlap = 479.219
PHY-3002 : Step(59): len = 246052, overlap = 489
PHY-3002 : Step(60): len = 243766, overlap = 494.5
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 2.07215e-05
PHY-3002 : Step(61): len = 255065, overlap = 454.625
PHY-3002 : Step(62): len = 269607, overlap = 379.062
PHY-3002 : Step(63): len = 274372, overlap = 353.156
PHY-3002 : Step(64): len = 274686, overlap = 346.188
PHY-3002 : Step(65): len = 273856, overlap = 331.656
PHY-3002 : Step(66): len = 271143, overlap = 345.562
PHY-3002 : Step(67): len = 268709, overlap = 345.875
PHY-3002 : Step(68): len = 267358, overlap = 354.719
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 4.14431e-05
PHY-3002 : Step(69): len = 277027, overlap = 341.5
PHY-3002 : Step(70): len = 287136, overlap = 334.312
PHY-3002 : Step(71): len = 289347, overlap = 293.031
PHY-3002 : Step(72): len = 290581, overlap = 272.656
PHY-3002 : Step(73): len = 289818, overlap = 257.125
PHY-3002 : Step(74): len = 288448, overlap = 254.406
PHY-3002 : Step(75): len = 286839, overlap = 267.281
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 8.28862e-05
PHY-3002 : Step(76): len = 293807, overlap = 276.094
PHY-3002 : Step(77): len = 301599, overlap = 269.469
PHY-3002 : Step(78): len = 303270, overlap = 271.406
PHY-3002 : Step(79): len = 303726, overlap = 263.375
PHY-3002 : Step(80): len = 301749, overlap = 259.156
PHY-3002 : Step(81): len = 300668, overlap = 268.75
PHY-3002 : Step(82): len = 299338, overlap = 281.188
PHY-3002 : Step(83): len = 300070, overlap = 276
PHY-3002 : Step(84): len = 299975, overlap = 268.781
PHY-3002 : Step(85): len = 300534, overlap = 258.469
PHY-3002 : Step(86): len = 299210, overlap = 254.562
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000163597
PHY-3002 : Step(87): len = 303300, overlap = 252.031
PHY-3002 : Step(88): len = 311258, overlap = 236.094
PHY-3002 : Step(89): len = 313084, overlap = 230.312
PHY-3002 : Step(90): len = 314884, overlap = 215.281
PHY-3002 : Step(91): len = 314709, overlap = 200.344
PHY-3002 : Step(92): len = 312917, overlap = 175.594
PHY-3002 : Step(93): len = 311569, overlap = 172
PHY-3002 : Step(94): len = 311727, overlap = 183.156
PHY-3002 : Step(95): len = 312641, overlap = 182.625
PHY-3002 : Step(96): len = 312942, overlap = 193.719
PHY-3002 : Step(97): len = 314338, overlap = 194.969
PHY-3002 : Step(98): len = 313315, overlap = 208
PHY-3002 : Step(99): len = 313325, overlap = 205.281
PHY-3002 : Step(100): len = 313226, overlap = 215.688
PHY-3002 : Step(101): len = 312522, overlap = 208.656
PHY-3002 : Step(102): len = 312218, overlap = 219.156
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000316221
PHY-3002 : Step(103): len = 314495, overlap = 216.375
PHY-3002 : Step(104): len = 318124, overlap = 215.031
PHY-3002 : Step(105): len = 318671, overlap = 206.719
PHY-3002 : Step(106): len = 318900, overlap = 198.406
PHY-3002 : Step(107): len = 318496, overlap = 211.438
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000539499
PHY-3002 : Step(108): len = 319272, overlap = 209.938
PHY-3002 : Step(109): len = 321887, overlap = 213.75
PHY-3002 : Step(110): len = 322242, overlap = 228.719
PHY-3002 : Step(111): len = 322995, overlap = 209.219
PHY-3002 : Step(112): len = 323229, overlap = 199.562
PHY-3002 : Step(113): len = 324336, overlap = 199.656
PHY-3002 : Step(114): len = 324529, overlap = 188.312
PHY-3002 : Step(115): len = 323822, overlap = 179.594
PHY-3002 : Step(116): len = 324165, overlap = 179.875
PHY-3002 : Step(117): len = 324305, overlap = 185.156
PHY-3002 : Step(118): len = 324340, overlap = 187.938
PHY-3002 : Step(119): len = 324355, overlap = 183.906
PHY-3001 : :::11::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(120): len = 324762, overlap = 179.375
PHY-3002 : Step(121): len = 325905, overlap = 172.344
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.014845s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (105.3%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21502.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 429112, over cnt = 1139(3%), over = 5216, worst = 39
PHY-1001 : End global iterations;  0.894019s wall, 1.281250s user + 0.093750s system = 1.375000s CPU (153.8%)

PHY-1001 : Congestion index: top1 = 72.72, top5 = 52.91, top10 = 42.76, top15 = 36.92.
PHY-3001 : End congestion estimation;  1.151768s wall, 1.531250s user + 0.093750s system = 1.625000s CPU (141.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21500 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.979488s wall, 0.984375s user + 0.000000s system = 0.984375s CPU (100.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000101701
PHY-3002 : Step(122): len = 365096, overlap = 191.031
PHY-3002 : Step(123): len = 381697, overlap = 174.406
PHY-3002 : Step(124): len = 385957, overlap = 161.969
PHY-3002 : Step(125): len = 384473, overlap = 146
PHY-3002 : Step(126): len = 390933, overlap = 133.219
PHY-3002 : Step(127): len = 398737, overlap = 127.281
PHY-3002 : Step(128): len = 402273, overlap = 116.969
PHY-3002 : Step(129): len = 404811, overlap = 115.656
PHY-3002 : Step(130): len = 407733, overlap = 123.875
PHY-3002 : Step(131): len = 409620, overlap = 122.594
PHY-3002 : Step(132): len = 409073, overlap = 124.969
PHY-3002 : Step(133): len = 410321, overlap = 127.906
PHY-3002 : Step(134): len = 409489, overlap = 132.875
PHY-3002 : Step(135): len = 410528, overlap = 139.188
PHY-3002 : Step(136): len = 410692, overlap = 140.156
PHY-3002 : Step(137): len = 408915, overlap = 139.781
PHY-3002 : Step(138): len = 409169, overlap = 137.438
PHY-3002 : Step(139): len = 407628, overlap = 138
PHY-3002 : Step(140): len = 407278, overlap = 136.281
PHY-3002 : Step(141): len = 407112, overlap = 135.688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000203401
PHY-3002 : Step(142): len = 407517, overlap = 136.594
PHY-3002 : Step(143): len = 409673, overlap = 135.062
PHY-3002 : Step(144): len = 412327, overlap = 133.031
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000406802
PHY-3002 : Step(145): len = 419808, overlap = 125.062
PHY-3002 : Step(146): len = 427055, overlap = 123.844
PHY-3002 : Step(147): len = 431352, overlap = 125.344
PHY-3002 : Step(148): len = 433045, overlap = 117.938
PHY-3002 : Step(149): len = 435688, overlap = 120.406
PHY-3002 : Step(150): len = 434673, overlap = 127.094
PHY-3002 : Step(151): len = 434368, overlap = 129.438
PHY-3002 : Step(152): len = 435336, overlap = 129.594
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 63/21502.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 499944, over cnt = 2026(5%), over = 9584, worst = 40
PHY-1001 : End global iterations;  1.075754s wall, 1.734375s user + 0.046875s system = 1.781250s CPU (165.6%)

PHY-1001 : Congestion index: top1 = 83.00, top5 = 59.19, top10 = 49.55, top15 = 44.19.
PHY-3001 : End congestion estimation;  1.381786s wall, 2.046875s user + 0.046875s system = 2.093750s CPU (151.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21500 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.190740s wall, 1.171875s user + 0.015625s system = 1.187500s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000101314
PHY-3002 : Step(153): len = 440821, overlap = 348.031
PHY-3002 : Step(154): len = 448786, overlap = 280.562
PHY-3002 : Step(155): len = 444365, overlap = 268.531
PHY-3002 : Step(156): len = 438398, overlap = 252.344
PHY-3002 : Step(157): len = 437407, overlap = 233.594
PHY-3002 : Step(158): len = 433887, overlap = 226.562
PHY-3002 : Step(159): len = 431655, overlap = 223.25
PHY-3002 : Step(160): len = 431428, overlap = 226.656
PHY-3002 : Step(161): len = 427923, overlap = 222.938
PHY-3002 : Step(162): len = 425505, overlap = 229.094
PHY-3002 : Step(163): len = 424927, overlap = 227.438
PHY-3002 : Step(164): len = 423389, overlap = 215.344
PHY-3002 : Step(165): len = 422081, overlap = 209.156
PHY-3002 : Step(166): len = 422365, overlap = 209.844
PHY-3002 : Step(167): len = 420088, overlap = 210.094
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000202627
PHY-3002 : Step(168): len = 420838, overlap = 213.219
PHY-3002 : Step(169): len = 422377, overlap = 201.531
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000363784
PHY-3002 : Step(170): len = 426042, overlap = 189.281
PHY-3002 : Step(171): len = 433186, overlap = 177.094
PHY-3002 : Step(172): len = 435135, overlap = 168.719
PHY-3002 : Step(173): len = 434970, overlap = 169.875
PHY-3002 : Step(174): len = 435495, overlap = 166.688
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000727569
PHY-3002 : Step(175): len = 436572, overlap = 161.469
PHY-3002 : Step(176): len = 440607, overlap = 149.094
PHY-3002 : Step(177): len = 443561, overlap = 143.562
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 78965, tnet num: 21500, tinst num: 19056, tnode num: 111159, tedge num: 124050.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.575713s wall, 1.546875s user + 0.031250s system = 1.578125s CPU (100.2%)

RUN-1004 : used memory is 560 MB, reserved memory is 535 MB, peak memory is 690 MB
OPT-1001 : Total overflow 502.34 peak overflow 4.25
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 511/21502.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 524368, over cnt = 2345(6%), over = 8115, worst = 36
PHY-1001 : End global iterations;  1.357417s wall, 2.000000s user + 0.046875s system = 2.046875s CPU (150.8%)

PHY-1001 : Congestion index: top1 = 55.95, top5 = 46.42, top10 = 41.82, top15 = 38.82.
PHY-1001 : End incremental global routing;  1.615982s wall, 2.265625s user + 0.046875s system = 2.312500s CPU (143.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21500 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.056143s wall, 1.031250s user + 0.031250s system = 1.062500s CPU (100.6%)

OPT-1001 : 17 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 18976 has valid locations, 217 needs to be replaced
PHY-3001 : design contains 19256 instances, 5590 luts, 12148 seqs, 1431 slices, 283 macros(1431 instances: 937 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 459157
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16837/21702.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 535368, over cnt = 2376(6%), over = 8157, worst = 37
PHY-1001 : End global iterations;  0.189230s wall, 0.281250s user + 0.031250s system = 0.312500s CPU (165.1%)

PHY-1001 : Congestion index: top1 = 56.08, top5 = 46.68, top10 = 42.10, top15 = 39.14.
PHY-3001 : End congestion estimation;  0.440052s wall, 0.515625s user + 0.031250s system = 0.546875s CPU (124.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 79617, tnet num: 21700, tinst num: 19256, tnode num: 112069, tedge num: 124954.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.594203s wall, 1.546875s user + 0.046875s system = 1.593750s CPU (100.0%)

RUN-1004 : used memory is 603 MB, reserved memory is 600 MB, peak memory is 693 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21700 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.658308s wall, 2.578125s user + 0.078125s system = 2.656250s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(178): len = 459551, overlap = 1.4375
PHY-3002 : Step(179): len = 460968, overlap = 1.5625
PHY-3002 : Step(180): len = 461930, overlap = 1.6875
PHY-3002 : Step(181): len = 463035, overlap = 2
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 16847/21702.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 534672, over cnt = 2375(6%), over = 8181, worst = 37
PHY-1001 : End global iterations;  0.190961s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (106.4%)

PHY-1001 : Congestion index: top1 = 56.53, top5 = 46.90, top10 = 42.17, top15 = 39.17.
PHY-3001 : End congestion estimation;  0.440826s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (102.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21700 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.043485s wall, 1.031250s user + 0.015625s system = 1.046875s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000586904
PHY-3002 : Step(182): len = 463066, overlap = 147.312
PHY-3002 : Step(183): len = 463144, overlap = 147.125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00117381
PHY-3002 : Step(184): len = 463368, overlap = 146.688
PHY-3002 : Step(185): len = 463751, overlap = 146.906
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00231444
PHY-3002 : Step(186): len = 463891, overlap = 146.875
PHY-3002 : Step(187): len = 464338, overlap = 146.906
PHY-3001 : Final: Len = 464338, Over = 146.906
PHY-3001 : End incremental placement;  5.557322s wall, 5.937500s user + 0.296875s system = 6.234375s CPU (112.2%)

OPT-1001 : Total overflow 507.78 peak overflow 4.25
OPT-1001 : End high-fanout net optimization;  8.771294s wall, 9.921875s user + 0.390625s system = 10.312500s CPU (117.6%)

OPT-1001 : Current memory(MB): used = 694, reserve = 675, peak = 711.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16857/21702.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 537352, over cnt = 2326(6%), over = 7666, worst = 36
PHY-1002 : len = 566768, over cnt = 1716(4%), over = 4863, worst = 21
PHY-1002 : len = 614336, over cnt = 540(1%), over = 1344, worst = 18
PHY-1002 : len = 632536, over cnt = 113(0%), over = 183, worst = 8
PHY-1002 : len = 635376, over cnt = 7(0%), over = 7, worst = 1
PHY-1001 : End global iterations;  1.205747s wall, 1.750000s user + 0.031250s system = 1.781250s CPU (147.7%)

PHY-1001 : Congestion index: top1 = 48.64, top5 = 42.57, top10 = 39.53, top15 = 37.44.
OPT-1001 : End congestion update;  1.460047s wall, 2.015625s user + 0.031250s system = 2.046875s CPU (140.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 21700 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.910807s wall, 0.906250s user + 0.000000s system = 0.906250s CPU (99.5%)

OPT-0007 : Start: WNS 4517 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.377389s wall, 2.921875s user + 0.031250s system = 2.953125s CPU (124.2%)

OPT-1001 : Current memory(MB): used = 691, reserve = 671, peak = 711.
OPT-1001 : End physical optimization;  13.052233s wall, 14.875000s user + 0.484375s system = 15.359375s CPU (117.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5590 LUT to BLE ...
SYN-4008 : Packed 5590 LUT and 2676 SEQ to BLE.
SYN-4003 : Packing 9472 remaining SEQ's ...
SYN-4005 : Packed 3291 SEQ with LUT/SLICE
SYN-4006 : 139 single LUT's are left
SYN-4006 : 6181 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11771/13555 primitive instances ...
PHY-3001 : End packing;  2.951494s wall, 2.953125s user + 0.000000s system = 2.953125s CPU (100.1%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 7910 instances
RUN-1001 : 3911 mslices, 3910 lslices, 60 pads, 24 brams, 0 dsps
RUN-1001 : There are total 19079 nets
RUN-1001 : 13417 nets have 2 pins
RUN-1001 : 4286 nets have [3 - 5] pins
RUN-1001 : 874 nets have [6 - 10] pins
RUN-1001 : 358 nets have [11 - 20] pins
RUN-1001 : 135 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 7908 instances, 7821 slices, 283 macros(1431 instances: 937 mslices 494 lslices)
PHY-3001 : Cell area utilization is 84%
PHY-3001 : After packing: Len = 482034, Over = 351.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7643/19079.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 603440, over cnt = 1487(4%), over = 2418, worst = 8
PHY-1002 : len = 609280, over cnt = 1011(2%), over = 1470, worst = 5
PHY-1002 : len = 620016, over cnt = 470(1%), over = 654, worst = 5
PHY-1002 : len = 627856, over cnt = 152(0%), over = 209, worst = 5
PHY-1002 : len = 632392, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.228473s wall, 2.187500s user + 0.078125s system = 2.265625s CPU (184.4%)

PHY-1001 : Congestion index: top1 = 49.35, top5 = 42.69, top10 = 39.42, top15 = 37.24.
PHY-3001 : End congestion estimation;  1.553284s wall, 2.500000s user + 0.078125s system = 2.578125s CPU (166.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 65958, tnet num: 19077, tinst num: 7908, tnode num: 89502, tedge num: 108670.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.816393s wall, 1.812500s user + 0.000000s system = 1.812500s CPU (99.8%)

RUN-1004 : used memory is 589 MB, reserved memory is 570 MB, peak memory is 711 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19077 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.735960s wall, 2.734375s user + 0.000000s system = 2.734375s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.87116e-05
PHY-3002 : Step(188): len = 488153, overlap = 331.5
PHY-3002 : Step(189): len = 487517, overlap = 357.25
PHY-3002 : Step(190): len = 486129, overlap = 372
PHY-3002 : Step(191): len = 487372, overlap = 378.5
PHY-3002 : Step(192): len = 486814, overlap = 377.75
PHY-3002 : Step(193): len = 486861, overlap = 388
PHY-3002 : Step(194): len = 485631, overlap = 385.75
PHY-3002 : Step(195): len = 483946, overlap = 380.75
PHY-3002 : Step(196): len = 482121, overlap = 385
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.74233e-05
PHY-3002 : Step(197): len = 486910, overlap = 372.25
PHY-3002 : Step(198): len = 491452, overlap = 361.5
PHY-3002 : Step(199): len = 491193, overlap = 358.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000193616
PHY-3002 : Step(200): len = 499866, overlap = 349.5
PHY-3002 : Step(201): len = 511308, overlap = 321.75
PHY-3002 : Step(202): len = 509034, overlap = 318
PHY-3002 : Step(203): len = 507063, overlap = 321.25
PHY-3002 : Step(204): len = 506754, overlap = 323
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.816125s wall, 0.890625s user + 0.859375s system = 1.750000s CPU (214.4%)

PHY-3001 : Trial Legalized: Len = 611017
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 83%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 594/19079.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 692504, over cnt = 2267(6%), over = 3759, worst = 9
PHY-1002 : len = 706504, over cnt = 1408(4%), over = 2030, worst = 7
PHY-1002 : len = 725040, over cnt = 485(1%), over = 683, worst = 5
PHY-1002 : len = 732224, over cnt = 173(0%), over = 239, worst = 5
PHY-1002 : len = 737032, over cnt = 18(0%), over = 22, worst = 2
PHY-1001 : End global iterations;  1.769057s wall, 3.250000s user + 0.031250s system = 3.281250s CPU (185.5%)

PHY-1001 : Congestion index: top1 = 48.81, top5 = 44.57, top10 = 41.94, top15 = 40.20.
PHY-3001 : End congestion estimation;  2.098416s wall, 3.578125s user + 0.031250s system = 3.609375s CPU (172.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19077 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.921351s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000164683
PHY-3002 : Step(205): len = 570461, overlap = 80.5
PHY-3002 : Step(206): len = 554844, overlap = 117.5
PHY-3002 : Step(207): len = 545015, overlap = 162.75
PHY-3002 : Step(208): len = 538003, overlap = 199.25
PHY-3002 : Step(209): len = 533977, overlap = 222.75
PHY-3002 : Step(210): len = 531610, overlap = 235.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000329366
PHY-3002 : Step(211): len = 536866, overlap = 232.75
PHY-3002 : Step(212): len = 541673, overlap = 235.25
PHY-3002 : Step(213): len = 540847, overlap = 236.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000658732
PHY-3002 : Step(214): len = 543874, overlap = 235.25
PHY-3002 : Step(215): len = 551590, overlap = 234.25
PHY-3002 : Step(216): len = 553442, overlap = 237
PHY-3002 : Step(217): len = 552369, overlap = 237.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.031846s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (98.1%)

PHY-3001 : Legalized: Len = 592211, Over = 0
PHY-3001 : Spreading special nets. 28 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.079831s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (97.9%)

PHY-3001 : 41 instances has been re-located, deltaX = 18, deltaY = 21, maxDist = 2.
PHY-3001 : Final: Len = 592805, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 65958, tnet num: 19077, tinst num: 7908, tnode num: 89502, tedge num: 108670.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.890591s wall, 1.890625s user + 0.000000s system = 1.890625s CPU (100.0%)

RUN-1004 : used memory is 595 MB, reserved memory is 589 MB, peak memory is 711 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3736/19079.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 686120, over cnt = 2033(5%), over = 3169, worst = 7
PHY-1002 : len = 695992, over cnt = 1279(3%), over = 1760, worst = 6
PHY-1002 : len = 706000, over cnt = 694(1%), over = 992, worst = 6
PHY-1002 : len = 718008, over cnt = 128(0%), over = 190, worst = 5
PHY-1002 : len = 721224, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.557614s wall, 2.593750s user + 0.015625s system = 2.609375s CPU (167.5%)

PHY-1001 : Congestion index: top1 = 47.11, top5 = 42.81, top10 = 40.36, top15 = 38.74.
PHY-1001 : End incremental global routing;  1.974016s wall, 3.000000s user + 0.015625s system = 3.015625s CPU (152.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19077 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.946180s wall, 0.937500s user + 0.015625s system = 0.953125s CPU (100.7%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 7844 has valid locations, 9 needs to be replaced
PHY-3001 : design contains 7916 instances, 7829 slices, 283 macros(1431 instances: 937 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 594093
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17133/19086.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 722488, over cnt = 12(0%), over = 15, worst = 2
PHY-1002 : len = 722432, over cnt = 6(0%), over = 7, worst = 2
PHY-1002 : len = 722512, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 722528, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 722576, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.694799s wall, 0.671875s user + 0.015625s system = 0.687500s CPU (98.9%)

PHY-1001 : Congestion index: top1 = 47.09, top5 = 42.82, top10 = 40.39, top15 = 38.77.
PHY-3001 : End congestion estimation;  1.003896s wall, 1.000000s user + 0.015625s system = 1.015625s CPU (101.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 65989, tnet num: 19084, tinst num: 7916, tnode num: 89541, tedge num: 108708.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.854273s wall, 1.843750s user + 0.000000s system = 1.843750s CPU (99.4%)

RUN-1004 : used memory is 630 MB, reserved memory is 610 MB, peak memory is 711 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19084 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.817732s wall, 2.796875s user + 0.015625s system = 2.812500s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(218): len = 594093, overlap = 0
PHY-3002 : Step(219): len = 594093, overlap = 0
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17141/19086.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 722576, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.119977s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (91.2%)

PHY-1001 : Congestion index: top1 = 47.09, top5 = 42.82, top10 = 40.39, top15 = 38.77.
PHY-3001 : End congestion estimation;  0.416041s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (101.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19084 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.921996s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000780391
PHY-3002 : Step(220): len = 594067, overlap = 0.75
PHY-3002 : Step(221): len = 594020, overlap = 0.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007044s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 594039, Over = 0
PHY-3001 : End spreading;  0.072141s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (86.6%)

PHY-3001 : Final: Len = 594039, Over = 0
PHY-3001 : End incremental placement;  5.749820s wall, 6.015625s user + 0.093750s system = 6.109375s CPU (106.3%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  9.160427s wall, 10.593750s user + 0.125000s system = 10.718750s CPU (117.0%)

OPT-1001 : Current memory(MB): used = 706, reserve = 691, peak = 711.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17132/19086.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 722264, over cnt = 14(0%), over = 15, worst = 2
PHY-1002 : len = 722184, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 722192, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 722248, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.534652s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (96.4%)

PHY-1001 : Congestion index: top1 = 47.24, top5 = 42.89, top10 = 40.42, top15 = 38.79.
OPT-1001 : End congestion update;  0.838799s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (98.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19084 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.778843s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (100.3%)

OPT-0007 : Start: WNS 4850 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.622696s wall, 1.609375s user + 0.000000s system = 1.609375s CPU (99.2%)

OPT-1001 : Current memory(MB): used = 706, reserve = 691, peak = 711.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19084 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.771922s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (99.2%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17141/19086.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 722248, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.122141s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (89.5%)

PHY-1001 : Congestion index: top1 = 47.24, top5 = 42.89, top10 = 40.42, top15 = 38.79.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19084 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.799249s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.7%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4850 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 46.862069
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4850ps with logic level 8 
RUN-1001 :       #2 path slack 4850ps with logic level 8 
RUN-1001 :       #3 path slack 4884ps with logic level 8 
RUN-1001 :       #4 path slack 4884ps with logic level 8 
RUN-1001 :       #5 path slack 4885ps with logic level 8 
RUN-1001 :       #6 path slack 4886ps with logic level 8 
RUN-1001 :       #7 path slack 4886ps with logic level 8 
RUN-1001 :       #8 path slack 4887ps with logic level 4 
RUN-1001 :       #9 path slack 4919ps with logic level 8 
RUN-1001 :       #10 path slack 4919ps with logic level 8 
OPT-1001 : End physical optimization;  14.932615s wall, 16.515625s user + 0.140625s system = 16.656250s CPU (111.5%)

RUN-1003 : finish command "place" in  65.373983s wall, 121.046875s user + 7.250000s system = 128.296875s CPU (196.3%)

RUN-1004 : used memory is 590 MB, reserved memory is 568 MB, peak memory is 711 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.657104s wall, 2.875000s user + 0.000000s system = 2.875000s CPU (173.5%)

RUN-1004 : used memory is 591 MB, reserved memory is 569 MB, peak memory is 711 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 7918 instances
RUN-1001 : 3919 mslices, 3910 lslices, 60 pads, 24 brams, 0 dsps
RUN-1001 : There are total 19086 nets
RUN-1001 : 13417 nets have 2 pins
RUN-1001 : 4287 nets have [3 - 5] pins
RUN-1001 : 877 nets have [6 - 10] pins
RUN-1001 : 361 nets have [11 - 20] pins
RUN-1001 : 135 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 65989, tnet num: 19084, tinst num: 7916, tnode num: 89541, tedge num: 108708.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.650664s wall, 1.640625s user + 0.000000s system = 1.640625s CPU (99.4%)

RUN-1004 : used memory is 576 MB, reserved memory is 553 MB, peak memory is 711 MB
PHY-1001 : 3919 mslices, 3910 lslices, 60 pads, 24 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19084 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 666616, over cnt = 2141(6%), over = 3613, worst = 7
PHY-1002 : len = 680744, over cnt = 1323(3%), over = 1898, worst = 6
PHY-1002 : len = 692928, over cnt = 661(1%), over = 972, worst = 6
PHY-1002 : len = 708552, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 708672, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.536235s wall, 2.968750s user + 0.031250s system = 3.000000s CPU (195.3%)

PHY-1001 : Congestion index: top1 = 46.77, top5 = 42.46, top10 = 39.97, top15 = 38.27.
PHY-1001 : End global routing;  1.871450s wall, 3.312500s user + 0.031250s system = 3.343750s CPU (178.7%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 684, reserve = 671, peak = 711.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 959, reserve = 950, peak = 959.
PHY-1001 : End build detailed router design. 4.358927s wall, 4.265625s user + 0.093750s system = 4.359375s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 185208, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.897234s wall, 0.890625s user + 0.000000s system = 0.890625s CPU (99.3%)

PHY-1001 : Current memory(MB): used = 995, reserve = 987, peak = 995.
PHY-1001 : End phase 1; 0.904703s wall, 0.906250s user + 0.000000s system = 0.906250s CPU (100.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 55% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.67062e+06, over cnt = 1106(0%), over = 1108, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1012, reserve = 1003, peak = 1012.
PHY-1001 : End initial routed; 16.256824s wall, 41.953125s user + 0.359375s system = 42.312500s CPU (260.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17876(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.167   |   0.000   |   0   
RUN-1001 :   Hold   |   0.171   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.270479s wall, 3.281250s user + 0.000000s system = 3.281250s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 1022, reserve = 1014, peak = 1022.
PHY-1001 : End phase 2; 19.527477s wall, 45.234375s user + 0.359375s system = 45.593750s CPU (233.5%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.67062e+06, over cnt = 1106(0%), over = 1108, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.239802s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (97.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.6629e+06, over cnt = 442(0%), over = 442, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.518624s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (159.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.6629e+06, over cnt = 112(0%), over = 112, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.321177s wall, 0.484375s user + 0.000000s system = 0.484375s CPU (150.8%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.66385e+06, over cnt = 10(0%), over = 10, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.231298s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (108.1%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.66406e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 4; 0.174629s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (107.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17876(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.167   |   0.000   |   0   
RUN-1001 :   Hold   |   0.171   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.298304s wall, 3.296875s user + 0.000000s system = 3.296875s CPU (100.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 282 feed throughs used by 253 nets
PHY-1001 : End commit to database; 2.025270s wall, 2.000000s user + 0.015625s system = 2.015625s CPU (99.5%)

PHY-1001 : Current memory(MB): used = 1110, reserve = 1105, peak = 1110.
PHY-1001 : End phase 3; 7.272288s wall, 7.781250s user + 0.015625s system = 7.796875s CPU (107.2%)

PHY-1003 : Routed, final wirelength = 1.66406e+06
PHY-1001 : Current memory(MB): used = 1114, reserve = 1109, peak = 1114.
PHY-1001 : End export database. 0.058994s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (105.9%)

PHY-1001 : End detail routing;  32.536809s wall, 58.640625s user + 0.484375s system = 59.125000s CPU (181.7%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 65989, tnet num: 19084, tinst num: 7916, tnode num: 89541, tedge num: 108708.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.600566s wall, 1.593750s user + 0.000000s system = 1.593750s CPU (99.6%)

RUN-1004 : used memory is 1047 MB, reserved memory is 1045 MB, peak memory is 1114 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  40.225166s wall, 67.750000s user + 0.531250s system = 68.281250s CPU (169.7%)

RUN-1004 : used memory is 1047 MB, reserved memory is 1045 MB, peak memory is 1114 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8597   out of  19600   43.86%
#reg                    12246   out of  19600   62.48%
#le                     14722
  #lut only              2476   out of  14722   16.82%
  #reg only              6125   out of  14722   41.60%
  #lut&reg               6121   out of  14722   41.58%
#dsp                        0   out of     29    0.00%
#bram                      24   out of     64   37.50%
  #bram9k                  22
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6716
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          103
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         M3        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         P6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |14722  |7166    |1431    |12290   |24      |0       |
|  AnyFog_dataX                      |AnyFog          |207    |79      |22      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |53      |22      |52      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |207    |82      |22      |171     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |53      |22      |50      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |212    |83      |22      |174     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |90     |63      |22      |52      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2915   |612     |39      |2831    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |64     |36      |5       |54      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |206    |55      |5       |198     |0       |0       |
|    STADOP_com2                     |STADOP          |533    |72      |0       |531     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |66     |45      |14      |43      |0       |0       |
|    head_com2                       |uniheading      |279    |71      |5       |266     |0       |0       |
|    rmc_com2                        |Gprmc           |39     |37      |0       |31      |0       |0       |
|    uart_com2                       |Agrica          |1419   |263     |10      |1399    |0       |0       |
|  COM3                              |COM3_Control    |273    |143     |19      |235     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |59     |33      |5       |51      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |60     |36      |14      |37      |0       |0       |
|    rmc_com3                        |Gprmc           |154    |74      |0       |147     |0       |0       |
|  DATA                              |Data_Processing |8692   |4536    |1065    |7006    |0       |0       |
|    DIV_Dtemp                       |Divider         |795    |355     |84      |664     |0       |0       |
|    DIV_Utemp                       |Divider         |581    |355     |84      |455     |0       |0       |
|    DIV_accX                        |Divider         |575    |342     |84      |452     |0       |0       |
|    DIV_accY                        |Divider         |695    |353     |114     |511     |0       |0       |
|    DIV_accZ                        |Divider         |661    |406     |132     |456     |0       |0       |
|    DIV_rateX                       |Divider         |658    |359     |132     |454     |0       |0       |
|    DIV_rateY                       |Divider         |575    |326     |132     |368     |0       |0       |
|    DIV_rateZ                       |Divider         |653    |373     |132     |452     |0       |0       |
|    genclk                          |genclk          |77     |48      |20      |43      |0       |0       |
|  FMC                               |FMC_Ctrl        |477    |420     |43      |358     |0       |0       |
|  IIC                               |I2C_master      |282    |209     |11      |245     |0       |0       |
|  IMU_CTRL                          |SCHA634         |895    |678     |61      |714     |0       |0       |
|    CtrlData                        |CtrlData        |465    |415     |47      |324     |0       |0       |
|      usms                          |Time_1ms        |27     |22      |5       |19      |0       |0       |
|    SPIM                            |SPI_SCHA634     |430    |263     |14      |390     |0       |0       |
|  POWER                             |POWER_EN        |96     |49      |38      |36      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |456    |275     |89      |293     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |456    |275     |89      |293     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |170    |99      |0       |160     |0       |0       |
|        reg_inst                    |register        |168    |97      |0       |158     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |286    |176     |89      |133     |0       |0       |
|        bus_inst                    |bus_top         |75     |47      |28      |25      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |24     |14      |10      |7       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |51     |33      |18      |18      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |126    |82      |29      |78      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13356  
    #2          2       3391   
    #3          3        633   
    #4          4        263   
    #5        5-10       935   
    #6        11-50      430   
    #7       51-100       8    
    #8       101-500      3    
    #9        >500        2    
  Average     2.10             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  1.929285s wall, 3.218750s user + 0.046875s system = 3.265625s CPU (169.3%)

RUN-1004 : used memory is 1047 MB, reserved memory is 1046 MB, peak memory is 1114 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 65989, tnet num: 19084, tinst num: 7916, tnode num: 89541, tedge num: 108708.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.607636s wall, 1.609375s user + 0.000000s system = 1.609375s CPU (100.1%)

RUN-1004 : used memory is 1049 MB, reserved memory is 1047 MB, peak memory is 1114 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19084 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.384120s wall, 1.359375s user + 0.015625s system = 1.375000s CPU (99.3%)

RUN-1004 : used memory is 1053 MB, reserved memory is 1051 MB, peak memory is 1114 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 057bf4ae9dc0d18f7126c20ce7aec8c31cdd0d41110c6b742e6e5620711a0ef6 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 7916
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19086, pip num: 142560
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 282
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3249 valid insts, and 399885 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101000001010010110001101
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.276456s wall, 124.296875s user + 0.187500s system = 124.484375s CPU (1014.0%)

RUN-1004 : used memory is 1172 MB, reserved memory is 1157 MB, peak memory is 1286 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250716_084552.log"

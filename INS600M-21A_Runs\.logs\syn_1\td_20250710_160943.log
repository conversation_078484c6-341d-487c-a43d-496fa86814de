============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Jul 10 16:09:44 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param rtl default_reg_initial 0"
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |     0      |       auto       |   *    
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(520)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
RUN-1001 : Project manager successfully analyzed 22 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.657250s wall, 1.437500s user + 4.218750s system = 5.656250s CPU (100.0%)

RUN-1004 : used memory is 78 MB, reserved memory is 40 MB, peak memory is 90 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "elaborate -top INS600M_21A"
RUN-1002 : start command "set_param rtl default_reg_initial 0"
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |     0      |       auto       |   *    
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param rtl default_reg_initial 0"
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |     0      |       auto       |   *    
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : port 'GPRMC_latch' remains unconnected for this instance in ../../Src/INS600M-21A.v(421)
HDL-1007 : port 'AGRIC_test' remains unconnected for this instance in ../../Src/INS600M-21A.v(435)
HDL-1007 : port 'GPRMC_test' remains unconnected for this instance in ../../Src/INS600M-21A.v(435)
HDL-1007 : port 'PPPdat_test' remains unconnected for this instance in ../../Src/INS600M-21A.v(435)
HDL-1007 : port 'STADOP_test' remains unconnected for this instance in ../../Src/INS600M-21A.v(435)
HDL-1007 : port 'HEADING_test' remains unconnected for this instance in ../../Src/INS600M-21A.v(435)
HDL-1007 : port 'GPRMC_POS' remains unconnected for this instance in ../../Src/INS600M-21A.v(435)
HDL-1007 : port 'GPRMC_LON' remains unconnected for this instance in ../../Src/INS600M-21A.v(435)
HDL-1007 : port 'GPRMC_LAT' remains unconnected for this instance in ../../Src/INS600M-21A.v(435)
HDL-1007 : port 'GPRMC_TRA' remains unconnected for this instance in ../../Src/INS600M-21A.v(435)
HDL-1007 : port 'FOG_INT' remains unconnected for this instance in ../../Src/INS600M-21A.v(492)
HDL-5007 WARNING: port 'Modle_state' is not connected on this instance in ../../Src/INS600M-21A.v(687)
HDL-1007 : port 'FMC_DACC_MdataX' remains unconnected for this instance in ../../Src/INS600M-21A.v(687)
HDL-1007 : port 'FMC_DACC_MdataY' remains unconnected for this instance in ../../Src/INS600M-21A.v(687)
HDL-1007 : port 'FMC_DACC_MdataZ' remains unconnected for this instance in ../../Src/INS600M-21A.v(687)
HDL-1007 : elaborate module INS600M_21A in ../../Src/INS600M-21A.v(16)
HDL-1007 : elaborate module global_clock in ../../al_ip/global_clock.v(22)
HDL-1007 : elaborate module EG_PHY_PLL(FIN="25.000",FBCLK_DIV=40,CLKC0_DIV=10,CLKC0_ENABLE="ENABLE",FEEDBK_MODE="NOCOMP",STDBY_ENABLE="DISABLE",CLKC0_CPHASE=9,GMC_GAIN=2,ICP_CURRENT=9,KVCO=2,LPF_CAPACITOR=1,LPF_RESISTOR=8,SYNC_ENABLE="DISABLE") in D:/softwawe/Anlogic/TD_5.6.2/arch/eagle_macro.v(930)
HDL-1007 : elaborate module POWER_EN in ../../Src/POWER/POWER_EN.v(16)
HDL-5007 WARNING: port 'tx_data' is not connected on this instance in ../../Src/IFOG/AnyFog.v(46)
HDL-1007 : port 'tx_rdy' remains unconnected for this instance in ../../Src/IFOG/AnyFog.v(46)
HDL-1007 : port 'txd' remains unconnected for this instance in ../../Src/IFOG/AnyFog.v(46)
HDL-1007 : elaborate module AnyFog in ../../Src/IFOG/AnyFog.v(17)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/UART_RX115200E_gate.v(272)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/UART_RX115200E_gate.v(298)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/UART_RX115200E_gate.v(441)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/UART_RX115200E_gate.v(672)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/UART_RX115200E_gate.v(705)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/UART_RX115200E_gate.v(1027)
HDL-1007 : elaborate module UART_RX115200E in ../../al_ip/UART_RX115200E_gate.v(5)
HDL-1007 : elaborate module AL_MAP_ADDER(ALUTYPE="ADD") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(72)
HDL-1007 : elaborate module AL_MAP_ADDER(ALUTYPE="ADD_CARRY") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(72)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b0100,EQN="(~C*B*~A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT2(INIT=4'b010,EQN="(~B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(48)
HDL-1007 : elaborate module AL_MAP_ADDER(ALUTYPE="A_LE_B_CARRY") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(72)
HDL-1007 : elaborate module AL_MAP_ADDER(ALUTYPE="A_LE_B") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(72)
HDL-1007 : elaborate module AL_DFF_X in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(236)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b0100111,EQN="~(C*~(B)*~(A)+C*B*~(A)+~(C)*B*A+C*B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b011111110,EQN="(~D*~(~C*~B*~A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b0111010100100000,EQN="(~E*(D*~((C*~B))*~(A)+D*(C*~B)*~(A)+~(D)*(C*~B)*A+D*(C*~B)*A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b10000010,EQN="(A*~(C@B))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b01,EQN="(~C*~B*~A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b0100000,EQN="(C*~B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b01000,EQN="(~D*~C*B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b011110010,EQN="(~D*~(~C*~(~B*A)))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b0100111011100100,EQN="(~E*(B*~((D@C))*~(A)+B*(D@C)*~(A)+~(B)*(D@C)*A+B*(D@C)*A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b10110000,EQN="(C*~(B*~A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b0100000000,EQN="(D*~C*~B*~A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b010110000,EQN="(~D*C*~(B*~A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b010000,EQN="(C*~B*~A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b01,EQN="(~D*~C*~B*~A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b01001100,EQN="(B*~(C*A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b0100000000000,EQN="(D*~C*B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT2(INIT=4'b1000,EQN="(B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(48)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b10011101110111010100000000000000,EQN="(~(A)*B*(D*C)*~(E)+~(A)*~(B)*~((D*C))*E+~(A)*B*~((D*C))*E+A*B*~((D*C))*E+~(A)*~(B)*(D*C)*E+A*B*(D*C)*E)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b01000000000000000000000000000,EQN="(E*D*~C*B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b010111010,EQN="(~D*~(~A*~(C*~B)))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT2(INIT=4'b01,EQN="(~B*~A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(48)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b010000000000000,EQN="(D*C*~B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b01000,EQN="(~C*B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b1000000000000000,EQN="(D*C*B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b010001011101010,EQN="(A*~(B)*~(C)*~(D)+A*B*~(C)*~(D)+A*~(B)*C*~(D)+~(A)*B*C*~(D)+A*B*C*~(D)+A*~(B)*~(C)*D+A*~(B)*C*D)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b010,EQN="(~E*~D*~C*~B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b0100000111101110000000000000000,EQN="(E*(~(A)*~(B)*~(C)*~(D)+A*~(B)*~(C)*~(D)+~(A)*B*~(C)*~(D)+~(A)*~(B)*C*~(D)+A*~(B)*C*~(D)+~(A)*B*C*~(D)+A*B*C*~(D)+A*~(B)*C*D))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b0110001,EQN="(~B*~(~C*A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b01000000000001000100010000,EQN="(~B*~A*~(~D*~(E)*~(C)+~D*E*~(C)+~(~D)*E*C+~D*E*C))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11111111011111111000000000000000,EQN="(E*~(D)*~((C*B*A))+E*D*~((C*B*A))+~(E)*D*(C*B*A)+E*D*(C*B*A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b11111110,EQN="~(~C*~B*~A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b010,EQN="(~D*~C*~B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b010000000,EQN="(~D*C*B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT2(INIT=4'b0100,EQN="(B*~A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(48)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b01000000,EQN="(C*B*~A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11111111111000000000000000000000,EQN="(E*~(~D*~(C*~(~B*~A))))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b1011000110111011,EQN="~(B*~((D*~C))*~(A)+B*(D*~C)*~(A)+~(B)*(D*~C)*A+B*(D*~C)*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b0111100000100,EQN="(~C*~(~D*~(B*~A)))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b011100000000,EQN="(D*~C*~(B*A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b01000001010000,EQN="(C*~A*~(D*B))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b01111000,EQN="(~D*(C@(B*A)))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b0110000000,EQN="(A*B*C*~(D)+~(A)*~(B)*~(C)*D)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b01100100011000100110011001100,EQN="(B*~(C*(E*~(D)*~(A)+E*D*~(A)+~(E)*D*A+E*D*A)))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b10111011101110111011000010111011,EQN="(~(~E*D*~C)*~(B*~A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b01101,EQN="(~C*~(~B*A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b0101100111011,EQN="(~(A)*~(B)*~(C)*~(D)+A*~(B)*~(C)*~(D)+A*B*~(C)*~(D)+~(A)*~(B)*C*~(D)+A*~(B)*C*~(D)+~(A)*~(B)*~(C)*D+A*~(B)*~(C)*D+A*B*~(C)*D)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b010001000000010001000000000000,EQN="(~B*~A*(E*~(D)*~(C)+E*D*~(C)+~(E)*D*C+E*D*C))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11110111000000001100010000000000,EQN="(D*~(~E*~((~C*A))*~(B)+~E*(~C*A)*~(B)+~(~E)*(~C*A)*B+~E*(~C*A)*B))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11010101110100001000101010000000,EQN="(A*B*C*~(D)*~(E)+A*~(B)*~(C)*D*~(E)+A*B*~(C)*D*~(E)+A*B*C*D*~(E)+~(A)*~(B)*C*~(D)*E+~(A)*B*C*~(D)*E+A*B*C*~(D)*E+~(A)*~(B)*~(C)*D*E+~(A)*B*~(C)*D*E+~(A)*~(B)*C*D*E+~(A)*B*C*D*E+A*B*C*D*E)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11110101110011001010000011001100,EQN="(B*~((E*~(C)*~(A)+E*C*~(A)+~(E)*C*A+E*C*A))*~(D)+B*(E*~(C)*~(A)+E*C*~(A)+~(E)*C*A+E*C*A)*~(D)+~(B)*(E*~(C)*~(A)+E*C*~(A)+~(E)*C*A+E*C*A)*D+B*(E*~(C)*~(A)+E*C*~(A)+~(E)*C*A+E*C*A)*D)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b10101011100010111101111111111111,EQN="(~(A)*~(B)*~(C)*~(D)*~(E)+A*~(B)*~(C)*~(D)*~(E)+~(A)*B*~(C)*~(D)*~(E)+A*B*~(C)*~(D)*~(E)+~(A)*~(B)*C*~(D)*~(E)+A*~(B)*C*~(D)*~(E)+~(A)*B*C*~(D)*~(E)+A*B*C*~(D)*~(E)+~(A)*~(B)*~(C)*D*~(E)+A*~(B)*~(C)*D*~(E)+~(A)*B*~(C)*D*~(E)+A*B*~(C)*D*~(E)+~(A)*~(B)*C*D*~(E)+~(A)*B*C*D*~(E)+A*B*C*D*~(E)+~(A)*~(B)*~(C)*~(D)*E+A*~(B)*~(C)*~(D)*E+A*B*~(C)*~(D)*E+A*B*C*~(D)*E+~(A)*~(B)*~(C)*D*E+A*~(B)*~(C)*D*E+A*B*~(C)*D*E+A*~(B)*C*D*E+A*B*C*D*E)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b0101001100000011,EQN="(~B*~((D*~A))*~(C)+~B*(D*~A)*~(C)+~(~B)*(D*~A)*C+~B*(D*~A)*C)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b10101000101011111111011111111111,EQN="(~(A)*~(B)*~(C)*~(D)*~(E)+A*~(B)*~(C)*~(D)*~(E)+~(A)*B*~(C)*~(D)*~(E)+A*B*~(C)*~(D)*~(E)+~(A)*~(B)*C*~(D)*~(E)+A*~(B)*C*~(D)*~(E)+~(A)*B*C*~(D)*~(E)+A*B*C*~(D)*~(E)+~(A)*~(B)*~(C)*D*~(E)+A*~(B)*~(C)*D*~(E)+~(A)*B*~(C)*D*~(E)+~(A)*~(B)*C*D*~(E)+A*~(B)*C*D*~(E)+~(A)*B*C*D*~(E)+A*B*C*D*~(E)+~(A)*~(B)*~(C)*~(D)*E+A*~(B)*~(C)*~(D)*E+~(A)*B*~(C)*~(D)*E+A*B*~(C)*~(D)*E+A*~(B)*C*~(D)*E+A*B*C*~(D)*E+A*B*~(C)*D*E+A*~(B)*C*D*E+A*B*C*D*E)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b011010100000101,EQN="(~A*~((D*~B))*~(C)+~A*(D*~B)*~(C)+~(~A)*(D*~B)*C+~A*(D*~B)*C)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b1001011001101001,EQN="~(D@C@B@A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b10010110011010010110100110010110,EQN="(E@D@C@B@A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11111111011111110000000001000000,EQN="~(~E*~(A)*~((~D*C*B))+~E*A*~((~D*C*B))+~(~E)*A*(~D*C*B)+~E*A*(~D*C*B))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b10001111000011111000100000000000,EQN="~(~(E*~C)*~(D*B*A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b010000000000,EQN="(D*~C*B*~A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b10000000,EQN="(C*B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b0100000000000000000000000000000,EQN="(E*D*C*~B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b010010001100,EQN="(B*~(C*~(D)*~(A)+C*D*~(A)+~(C)*D*A+C*D*A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b011110001111,EQN="~(C*~(D)*~((B*A))+C*D*~((B*A))+~(C)*D*(B*A)+C*D*(B*A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b0111101110000000011010101,EQN="(~D*~(A*~(E*~(C)*~(B)+E*C*~(B)+~(E)*C*B+E*C*B)))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11111111000100001111111101010000,EQN="~(~D*~(C*~A*~(E*B)))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b10101010000011001111111100111111,EQN="(~(A)*~(B)*~(C)*~(D)*~(E)+A*~(B)*~(C)*~(D)*~(E)+~(A)*B*~(C)*~(D)*~(E)+A*B*~(C)*~(D)*~(E)+~(A)*~(B)*C*~(D)*~(E)+A*~(B)*C*~(D)*~(E)+~(A)*~(B)*~(C)*D*~(E)+A*~(B)*~(C)*D*~(E)+~(A)*B*~(C)*D*~(E)+A*B*~(C)*D*~(E)+~(A)*~(B)*C*D*~(E)+A*~(B)*C*D*~(E)+~(A)*B*C*D*~(E)+A*B*C*D*~(E)+~(A)*B*~(C)*~(D)*E+A*B*~(C)*~(D)*E+A*~(B)*~(C)*D*E+A*B*~(C)*D*E+A*~(B)*C*D*E+A*B*C*D*E)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11111111010100110000000000000011,EQN="(~(A)*~(B)*~(C)*~(D)*~(E)+A*~(B)*~(C)*~(D)*~(E)+~(A)*~(B)*~(C)*~(D)*E+A*~(B)*~(C)*~(D)*E+~(A)*~(B)*C*~(D)*E+~(A)*B*C*~(D)*E+~(A)*~(B)*~(C)*D*E+A*~(B)*~(C)*D*E+~(A)*B*~(C)*D*E+A*B*~(C)*D*E+~(A)*~(B)*C*D*E+A*~(B)*C*D*E+~(A)*B*C*D*E+A*B*C*D*E)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/UART_RX115200E_gate.v(274)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/UART_RX115200E_gate.v(300)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/UART_RX115200E_gate.v(443)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/UART_RX115200E_gate.v(674)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/UART_RX115200E_gate.v(707)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/UART_RX115200E_gate.v(1029)
HDL-5007 WARNING: net 'Fog_TXO[6]' does not have a driver in ../../Src/IFOG/AnyFog.v(40)
HDL-5007 WARNING: net 'Fog_TXO_C[6]' does not have a driver in ../../Src/IFOG/AnyFog.v(42)
HDL-5007 WARNING: input port 'tx_data[7]' remains unconnected for this instance in ../../Src/IFOG/AnyFog.v(46)
HDL-1007 : elaborate module SCHA634(SCLK_FREQ=1000000) in ../../Src/SPI/SCHA634.v(18)
HDL-1007 : elaborate module SPI_SCHA634(SCLK_FREQ=1000000) in ../../Src/SPI/SPI_MASTER.v(17)
HDL-1007 : elaborate module CtrlData in ../../Src/SPI/CtrlData.v(17)
HDL-1007 : elaborate module Time_1ms in ../../Src/SPI/Time_1ms.v(17)
HDL-7007 CRITICAL-WARNING: 'top_state' should be on the sensitivity list in ../../Src/SPI/CtrlData.v(1952)
HDL-7007 CRITICAL-WARNING: 'cnt_time0' should be on the sensitivity list in ../../Src/SPI/CtrlData.v(1953)
HDL-7007 CRITICAL-WARNING: 'cnt_time1' should be on the sensitivity list in ../../Src/SPI/CtrlData.v(1954)
HDL-7007 CRITICAL-WARNING: 'cnt_time2' should be on the sensitivity list in ../../Src/SPI/CtrlData.v(1955)
HDL-7007 CRITICAL-WARNING: 'cnt_time3' should be on the sensitivity list in ../../Src/SPI/CtrlData.v(1956)
HDL-7007 CRITICAL-WARNING: 'cnt_time4' should be on the sensitivity list in ../../Src/SPI/CtrlData.v(1957)
HDL-7007 CRITICAL-WARNING: 'cnt_time5' should be on the sensitivity list in ../../Src/SPI/CtrlData.v(1958)
HDL-7007 CRITICAL-WARNING: 'cnt_time6' should be on the sensitivity list in ../../Src/SPI/CtrlData.v(1959)
HDL-7007 CRITICAL-WARNING: 'cnt_time7' should be on the sensitivity list in ../../Src/SPI/CtrlData.v(1960)
HDL-7007 CRITICAL-WARNING: 'cnt_time8' should be on the sensitivity list in ../../Src/SPI/CtrlData.v(1961)
HDL-7007 Similar messages will be suppressed.
HDL-5007 WARNING: port 'tx_data' is not connected on this instance in ../../Src/UART/COM3_Control.v(37)
HDL-1007 : port 'tx_rdy' remains unconnected for this instance in ../../Src/UART/COM3_Control.v(37)
HDL-1007 : port 'txd' remains unconnected for this instance in ../../Src/UART/COM3_Control.v(37)
HDL-1007 : elaborate module COM3_Control in ../../Src/UART/COM3_Control.v(16)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/UART_RX460800_gate.v(206)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/UART_RX460800_gate.v(461)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/UART_RX460800_gate.v(494)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/UART_RX460800_gate.v(838)
HDL-1007 : elaborate module UART_RX460800 in ../../al_ip/UART_RX460800_gate.v(5)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b010101,EQN="(~A*~(C*B))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b011001100110,EQN="(~(D*C)*(B@A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b0100001111000,EQN="((B*A)*~(C)*~(D)+~((B*A))*C*~(D)+(B*A)*~(C)*D)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b0111110000000,EQN="((B*A)*C*~(D)+~((B*A))*~(C)*D+(B*A)*~(C)*D)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b11011000,EQN="(C*~(B)*~(A)+C*B*~(A)+~(C)*B*A+C*B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b1111011100000000,EQN="(D*~(~C*B*A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b01010100010101010101000001010101,EQN="(~A*~(D*~C*~(E*B)))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b01010101000,EQN="(A*~(D@(~C*~B)))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b01000000000000000000000000000000,EQN="(E*D*C*B*~A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b010101000,EQN="(~D*A*~(~C*~B))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b011111000,EQN="(~D*~(~C*~(B*A)))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT2(INIT=4'b1101,EQN="~(~B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(48)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b1010101011101010,EQN="~(~A*~(~D*C*B))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b01000000000000000000000,EQN="(E*~D*C*~B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b0100000000000000000,EQN="(E*~D*~C*~B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b0100010001000000,EQN="(B*~A*~(~D*~C))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b01111111110000000,EQN="(~E*~(~D*~(C*B*A)))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b10111101,EQN="(~(A)*~(B)*~(C)+~(A)*B*~(C)+A*B*~(C)+~(A)*~(B)*C+A*~(B)*C+A*B*C)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b01000000000000,EQN="(D*C*~B*~A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b0111111000000001010101000000000,EQN="(D*~(~A*~((C*B))*~(E)+~A*(C*B)*~(E)+~(~A)*(C*B)*E+~A*(C*B)*E))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b0101010001111110010101001111111,EQN="(~(A)*~((C*B))*~(D)*~(E)+A*~((C*B))*~(D)*~(E)+~(A)*(C*B)*~(D)*~(E)+A*~((C*B))*D*~(E)+~(A)*~((C*B))*~(D)*E+A*~((C*B))*~(D)*E+A*~((C*B))*D*E)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b10000000000000000000000000000000,EQN="(E*D*C*B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b1000100000001000,EQN="(B*A*~(~D*C))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11111111111110000000000000000000,EQN="(E*~(~D*~C*~(B*A)))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b1100110011011100,EQN="~(~B*~(~D*C*~A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b01010110101010,EQN="(~E*(A*~((C*B))*~(D)+A*(C*B)*~(D)+~(A)*~((C*B))*D))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11111110111011101010101010101010,EQN="~(~A*~(E*~(~B*~(D*C))))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b01100101011000101110011111100,EQN="~(~B*~((E*~(D)*~(A)+E*D*~(A)+~(E)*D*A+E*D*A))*~(C)+~B*(E*~(D)*~(A)+E*D*~(A)+~(E)*D*A+E*D*A)*~(C)+~(~B)*(E*~(D)*~(A)+E*D*~(A)+~(E)*D*A+E*D*A)*C+~B*(E*~(D)*~(A)+E*D*~(A)+~(E)*D*A+E*D*A)*C)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11001100110011001111110011011101,EQN="~(~B*~(~E*(~A*~(C)*~(D)+~A*C*~(D)+~(~A)*C*D+~A*C*D)))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b1111000000100000,EQN="(C*~(~D*~(~B*A)))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b010011000011001101111111,EQN="(~E*~((~D*~(C*A)))*~(B)+~E*(~D*~(C*A))*~(B)+~(~E)*(~D*~(C*A))*B+~E*(~D*~(C*A))*B)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b01,EQN="(~E*~D*~C*~B*~A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b01011111011101110000101000100010,EQN="~(~E*~((B*~(C)*~(D)+B*C*~(D)+~(B)*C*D+B*C*D))*~(A)+~E*(B*~(C)*~(D)+B*C*~(D)+~(B)*C*D+B*C*D)*~(A)+~(~E)*(B*~(C)*~(D)+B*C*~(D)+~(B)*C*D+B*C*D)*A+~E*(B*~(C)*~(D)+B*C*~(D)+~(B)*C*D+B*C*D)*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b1111110101010100,EQN="(~(A)*~((~C*~B))*~(D)+~(A)*~((~C*~B))*D+A*~((~C*~B))*D+~(A)*(~C*~B)*D)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11111111111100110011101000111111,EQN="(~(A)*~(B)*~(C)*~(D)*~(E)+A*~(B)*~(C)*~(D)*~(E)+~(A)*B*~(C)*~(D)*~(E)+A*B*~(C)*~(D)*~(E)+~(A)*~(B)*C*~(D)*~(E)+A*~(B)*C*~(D)*~(E)+A*~(B)*~(C)*D*~(E)+A*B*~(C)*D*~(E)+~(A)*~(B)*C*D*~(E)+A*~(B)*C*D*~(E)+~(A)*~(B)*~(C)*~(D)*E+A*~(B)*~(C)*~(D)*E+~(A)*~(B)*C*~(D)*E+A*~(B)*C*~(D)*E+~(A)*B*C*~(D)*E+A*B*C*~(D)*E+~(A)*~(B)*~(C)*D*E+A*~(B)*~(C)*D*E+~(A)*B*~(C)*D*E+A*B*~(C)*D*E+~(A)*~(B)*C*D*E+A*~(B)*C*D*E+~(A)*B*C*D*E+A*B*C*D*E)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b1010101000110000,EQN="((C*~B)*~(A)*~(D)+(C*~B)*A*~(D)+~((C*~B))*A*D+(C*~B)*A*D)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b10101010111010101100000000000000,EQN="(~(A)*(C*B)*D*~(E)+A*(C*B)*D*~(E)+A*~((C*B))*~(D)*E+~(A)*(C*B)*~(D)*E+A*(C*B)*~(D)*E+A*~((C*B))*D*E+A*(C*B)*D*E)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b1010101100000011,EQN="~(~(~C*~B)*~(D*A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b11010000,EQN="(C*~(~B*A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b01110100,EQN="(~D*~(~C*~(A)*~(B)+~C*A*~(B)+~(~C)*A*B+~C*A*B))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b1011101011000000,EQN="(~(A)*B*C*~(D)+A*B*C*~(D)+A*~(B)*~(C)*D+A*B*~(C)*D+~(A)*~(B)*C*D+A*~(B)*C*D+A*B*C*D)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b0111110100101000,EQN="(D*~((C@B))*~(A)+D*(C@B)*~(A)+~(D)*(C@B)*A+D*(C@B)*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b10111010,EQN="~(~A*~(C*~B))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b01000000000,EQN="(D*~C*~B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11111111001000001101111100000000,EQN="(D*~(E)*~((C*~B*A))+D*E*~((C*~B*A))+~(D)*E*(C*~B*A)+D*E*(C*~B*A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b011010100111010001111110011,EQN="~(B*~((D*~(E)*~(A)+D*E*~(A)+~(D)*E*A+D*E*A))*~(C)+B*(D*~(E)*~(A)+D*E*~(A)+~(D)*E*A+D*E*A)*~(C)+~(B)*(D*~(E)*~(A)+D*E*~(A)+~(D)*E*A+D*E*A)*C+B*(D*~(E)*~(A)+D*E*~(A)+~(D)*E*A+D*E*A)*C)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11111111101000110000000010100011,EQN="((~B*~(A)*~(C)+~B*A*~(C)+~(~B)*A*C+~B*A*C)*~(E)*~(D)+(~B*~(A)*~(C)+~B*A*~(C)+~(~B)*A*C+~B*A*C)*E*~(D)+~((~B*~(A)*~(C)+~B*A*~(C)+~(~B)*A*C+~B*A*C))*E*D+(~B*~(A)*~(C)+~B*A*~(C)+~(~B)*A*C+~B*A*C)*E*D)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11111100101011000101110000001100,EQN="(B*~((D*~(E)*~(A)+D*E*~(A)+~(D)*E*A+D*E*A))*~(C)+B*(D*~(E)*~(A)+D*E*~(A)+~(D)*E*A+D*E*A)*~(C)+~(B)*(D*~(E)*~(A)+D*E*~(A)+~(D)*E*A+D*E*A)*C+B*(D*~(E)*~(A)+D*E*~(A)+~(D)*E*A+D*E*A)*C)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11111111110010100000000011001010,EQN="((A*~(B)*~(C)+A*B*~(C)+~(A)*B*C+A*B*C)*~(E)*~(D)+(A*~(B)*~(C)+A*B*~(C)+~(A)*B*C+A*B*C)*E*~(D)+~((A*~(B)*~(C)+A*B*~(C)+~(A)*B*C+A*B*C))*E*D+(A*~(B)*~(C)+A*B*~(C)+~(A)*B*C+A*B*C)*E*D)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b11111111110001010000000011000101,EQN="((~A*~(B)*~(C)+~A*B*~(C)+~(~A)*B*C+~A*B*C)*~(E)*~(D)+(~A*~(B)*~(C)+~A*B*~(C)+~(~A)*B*C+~A*B*C)*E*~(D)+~((~A*~(B)*~(C)+~A*B*~(C)+~(~A)*B*C+~A*B*C))*E*D+(~A*~(B)*~(C)+~A*B*~(C)+~(~A)*B*C+~A*B*C)*E*D)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b01000000000000000,EQN="(~E*D*C*B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/UART_RX460800_gate.v(208)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/UART_RX460800_gate.v(463)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/UART_RX460800_gate.v(496)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/UART_RX460800_gate.v(840)
HDL-1007 : elaborate module Gprmc in ../../Src/GNSS/Gprmc.v(17)
HDL-5007 WARNING: input port 'tx_data[7]' remains unconnected for this instance in ../../Src/UART/COM3_Control.v(37)
HDL-1007 : port 'tx_data' remains unconnected for this instance in ../../Src/UART/COM2_Control.v(88)
HDL-1007 : port 'tx_en' remains unconnected for this instance in ../../Src/UART/COM2_Control.v(88)
HDL-1007 : port 'tx_rdy' remains unconnected for this instance in ../../Src/UART/COM2_Control.v(88)
HDL-1007 : port 'txd' remains unconnected for this instance in ../../Src/UART/COM2_Control.v(88)
HDL-1007 : port 'AGRIC_BaseAlt' remains unconnected for this instance in ../../Src/UART/COM2_Control.v(97)
HDL-1007 : port 'HEADING_latch' remains unconnected for this instance in ../../Src/UART/COM2_Control.v(129)
HDL-1007 : port 'STADOP_latch' remains unconnected for this instance in ../../Src/UART/COM2_Control.v(156)
HDL-1007 : port 'PPPdat_valid' remains unconnected for this instance in ../../Src/UART/COM2_Control.v(174)
HDL-1007 : port 'PPPNAV_satanum' remains unconnected for this instance in ../../Src/UART/COM2_Control.v(174)
HDL-1007 : port 'PPPNAV_SVs' remains unconnected for this instance in ../../Src/UART/COM2_Control.v(174)
HDL-1007 : port 'PPPNAV_solnSVs' remains unconnected for this instance in ../../Src/UART/COM2_Control.v(174)
HDL-1007 : elaborate module COM2_Control in ../../Src/UART/COM2_Control.v(16)
HDL-1007 : elaborate module Agrica in ../../Src/GNSS/Agrica.v(18)
HDL-5007 WARNING: actual bit length 16 differs from formal bit length 8 for port 'hGPSData_satanum' in ../../Src/UART/COM2_Control.v(125)
HDL-1007 : elaborate module uniheading in ../../Src/GNSS/uniheading.v(17)
HDL-1007 : elaborate module STADOP in ../../Src/GNSS/STADOP.v(18)
HDL-1007 : elaborate module PPPNAV in ../../Src/GNSS/PPPNAV.v(17)
HDL-5007 WARNING: input port 'tx_data[7]' remains unconnected for this instance in ../../Src/UART/COM2_Control.v(88)
HDL-5007 WARNING: actual bit length 32 differs from formal bit length 8 for port 'AGRIC_SpeedType' in ../../Src/INS600M-21A.v(455)
HDL-1007 : elaborate module I2C_master in ../../Src/IIC/I2C_master.v(17)
HDL-1007 : elaborate module Data_Processing in ../../Src/FMC/Data_Processing.v(16)
HDL-1007 : elaborate module genclk in ../../Src/FMC/genclk.v(16)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(475)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(550)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(1131)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(1648)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(1731)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(2304)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(2821)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(3338)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(3421)
HDL-5007 WARNING: port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(3994)
HDL-5007 Similar messages will be suppressed.
HDL-1007 : elaborate module Divider in ../../al_ip/Divider_gate.v(5)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b10100010,EQN="(A*~(~C*B))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_ADDER(ALUTYPE="SUB_CARRY") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(72)
HDL-1007 : elaborate module AL_MAP_ADDER(ALUTYPE="SUB") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(72)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b10111000,EQN="(C*~(A)*~(B)+C*A*~(B)+~(C)*A*B+C*A*B)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b11001010,EQN="(A*~(B)*~(C)+A*B*~(C)+~(A)*B*C+A*B*C)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT4(INIT=16'b1011111100000000,EQN="(D*~(C*B*~A))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(24)
HDL-1007 : elaborate module AL_MAP_LUT5(INIT=32'b0100011111011110000000000000000,EQN="(E*~((C*~A)*~(D)*~(B)+(C*~A)*D*~(B)+~((C*~A))*D*B+(C*~A)*D*B))") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(13)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b010,EQN="(~C*~B*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b11100100,EQN="(B*~(C)*~(A)+B*C*~(A)+~(B)*C*A+B*C*A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b11100010,EQN="(A*~(C)*~(B)+A*C*~(B)+~(A)*C*B+A*C*B)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b10101100,EQN="(B*~(A)*~(C)+B*A*~(C)+~(B)*A*C+B*A*C)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-1007 : elaborate module AL_MAP_LUT1(INIT=2'b01,EQN="(~A)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(60)
HDL-1007 : elaborate module AL_MAP_LUT3(INIT=8'b10001011,EQN="(~C*~(A)*~(B)+~C*A*~(B)+~(~C)*A*B+~C*A*B)") in D:/softwawe/Anlogic/TD_5.6.2/arch/al_lmacro.v(36)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(477)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(552)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(1133)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(1650)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(1733)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(2306)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(2823)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(3340)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(3423)
HDL-5007 WARNING: input port 'b' is not connected on this instance in ../../al_ip/Divider_gate.v(3996)
HDL-5007 Similar messages will be suppressed.
HDL-1007 : elaborate module FMC_Ctrl(DATA_LEN=16'b01101100,Vers_Num=16'b0100000010) in ../../Src/FMC/FMC_Ctrl.v(17)
HDL-5007 WARNING: net 'DACC_Temp[15]' does not have a driver in ../../Src/INS600M-21A.v(139)
HDL-5007 WARNING: net 'gps_valid_rmc' does not have a driver in ../../Src/INS600M-21A.v(189)
HDL-5007 WARNING: net 'gps_valid_head' does not have a driver in ../../Src/INS600M-21A.v(190)
HDL-5007 WARNING: net 'FMC_DACC_MdataX[31]' does not have a driver in ../../Src/INS600M-21A.v(227)
HDL-5007 WARNING: net 'FMC_DACC_MdataY[31]' does not have a driver in ../../Src/INS600M-21A.v(228)
HDL-5007 WARNING: net 'FMC_DACC_MdataZ[31]' does not have a driver in ../../Src/INS600M-21A.v(229)
HDL-5007 WARNING: net 'DACC_valid' does not have a driver in ../../Src/INS600M-21A.v(520)
HDL-5007 WARNING: input port 'DACC_dataX[31]' remains unconnected for this instance in ../../Src/INS600M-21A.v(498)
HDL-5007 WARNING: input port 'Modle_state[7]' is not connected on this instance in ../../Src/INS600M-21A.v(693)
HDL-5007 WARNING: input port 'FMC_DACC_MdataX[31]' remains unconnected for this instance in ../../Src/INS600M-21A.v(693)
HDL-5001 WARNING: Contains anonymous inst(s) and/or net(s) that have not been renamed
HDL-1200 : Current top model is INS600M_21A
SYN-5064 CRITICAL-WARNING: Register "rxd_state[0]" in ../../Src/IFOG/AnyFog.v(37) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "scl_r0" in ../../Src/IIC/I2C_master.v(55) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "scl_r1" in ../../Src/IIC/I2C_master.v(55) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "scl_r2" in ../../Src/IIC/I2C_master.v(55) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "scl_r3" in ../../Src/IIC/I2C_master.v(55) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "scl_r4" in ../../Src/IIC/I2C_master.v(55) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "scl_r5" in ../../Src/IIC/I2C_master.v(55) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "sda_en0" in ../../Src/IIC/I2C_master.v(56) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "sda_en1" in ../../Src/IIC/I2C_master.v(56) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "sda_en2" in ../../Src/IIC/I2C_master.v(56) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-5105 WARNING: Improper initial value, net "lfsr_c[7]" initial value we do not reserve in ../../Src/SPI/SPI_MASTER.v(281) 
HDL-5105 WARNING: Improper initial value, net "lfsr_c[6]" initial value we do not reserve in ../../Src/SPI/SPI_MASTER.v(281) 
HDL-5105 WARNING: Improper initial value, net "lfsr_c[5]" initial value we do not reserve in ../../Src/SPI/SPI_MASTER.v(281) 
HDL-5105 WARNING: Improper initial value, net "lfsr_c[4]" initial value we do not reserve in ../../Src/SPI/SPI_MASTER.v(281) 
HDL-5105 WARNING: Improper initial value, net "lfsr_c[3]" initial value we do not reserve in ../../Src/SPI/SPI_MASTER.v(281) 
HDL-5105 WARNING: Improper initial value, net "lfsr_c[2]" initial value we do not reserve in ../../Src/SPI/SPI_MASTER.v(281) 
HDL-5105 WARNING: Improper initial value, net "lfsr_c[1]" initial value we do not reserve in ../../Src/SPI/SPI_MASTER.v(281) 
HDL-5105 WARNING: Improper initial value, net "lfsr_c[0]" initial value we do not reserve in ../../Src/SPI/SPI_MASTER.v(281) 
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "export_db INS600M-21A_elaborate.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "read_adc ../../Constraints/INS600M_21A.adc"
RUN-1002 : start command "set_pin_assignment  clk_in 		  LOCATION = C9;  IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  Version[0] 	  LOCATION = M5; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  Version[1] 	  LOCATION = N4; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  Version[2] 	  LOCATION = R2; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  Version[3] 	  LOCATION = P1; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  cs_uno 		  LOCATION = N16; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  cs_due 		  LOCATION = M16; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  sclk 	 		  LOCATION = K3;  IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  mosi 	 		  LOCATION = L16; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  miso 	 		  LOCATION = P15; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  spi_rst		  LOCATION = P16; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  ARM_INT 		  LOCATION = H5; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  FMC_NCE 		  LOCATION = B3; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  FMC_NOE 		  LOCATION = F4; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  FMC_NWE 		  LOCATION = C2; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  FMC_data[0] 	  LOCATION = A6; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  FMC_data[1] 	  LOCATION = C8; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  FMC_data[2] 	  LOCATION = E4; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  FMC_data[3] 	  LOCATION = D5; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  FMC_data[4] 	  LOCATION = K1; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  FMC_data[5] 	  LOCATION = J1; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  FMC_data[6] 	  LOCATION = D1; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  FMC_data[7] 	  LOCATION = G1; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  FMC_data[8] 	  LOCATION = C3; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  FMC_data[9] 	  LOCATION = C1; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  FMC_data[10] 	  LOCATION = A3; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  FMC_data[11] 	  LOCATION = B2; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  FMC_data[12] 	  LOCATION = B1; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  FMC_data[13] 	  LOCATION = A5; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  FMC_data[14] 	  LOCATION = B5; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  FMC_data[15] 	  LOCATION = B6; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  FMC_addr[0] 	  LOCATION = N5; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  FMC_addr[1] 	  LOCATION = F5;  IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  FMC_addr[2] 	  LOCATION = P5;  IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  FMC_addr[3] 	  LOCATION = E1;  IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  FMC_addr[4] 	  LOCATION = E2;  IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  FMC_addr[5] 	  LOCATION = R1; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  FMC_addr[6] 	  LOCATION = N1;  IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  FMC_addr[7] 	  LOCATION = F3;  IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  KSG_SCL 		  LOCATION = T14; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  KSG_SDA		  LOCATION = T15; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  GNSS_RX2 		  LOCATION = H14; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  GNSS_RX3 		  LOCATION = J12; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  GNSS_PPS 		  LOCATION = K14; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  GNSS_EN 		  LOCATION = A13; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  TXD_RMC 		  LOCATION = L3; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  TXD_PPS 		  LOCATION = L13; IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  GNSS_LED 		  LOCATION = C15; IOSTANDARD = LVCMOS33; "
RUN-1001 : Starting of IO setups legality check.
RUN-1001 : Starting of IO setups legality check.
RUN-1001 : Starting of IO vref setups legality check.
USR-6010 WARNING: ADC constraints: top model pin ARM_INTT has no constraint.
USR-6010 WARNING: ADC constraints: top model pin DACC_EN has no constraint.
USR-6010 WARNING: ADC constraints: top model pin DACC_INT has no constraint.
USR-6010 WARNING: ADC constraints: top model pin DACC_MRXD has no constraint.
USR-6010 WARNING: ADC constraints: top model pin DACC_MTXD has no constraint.
USR-6010 WARNING: ADC constraints: top model pin DACC_RXD has no constraint.
USR-6010 WARNING: ADC constraints: top model pin Fog_ENX has no constraint.
USR-6010 WARNING: ADC constraints: top model pin Fog_ENY has no constraint.
USR-6010 WARNING: ADC constraints: top model pin Fog_ENZ has no constraint.
USR-6010 WARNING: ADC constraints: top model pin Fog_INT has no constraint.
USR-6010 Similar messages will be suppressed.
RUN-1002 : start command "optimize_rtl"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "AnyFog"
SYN-1012 : SanityCheck: Model "UART_RX115200E"
SYN-1012 : SanityCheck: Model "AL_DFF_X"
SYN-1012 : SanityCheck: Model "global_clock"
SYN-1012 : SanityCheck: Model "COM2_Control"
SYN-1012 : SanityCheck: Model "PPPNAV"
SYN-1012 : SanityCheck: Model "STADOP"
SYN-1012 : SanityCheck: Model "UART_RX460800"
SYN-1012 : SanityCheck: Model "uniheading"
SYN-1012 : SanityCheck: Model "Agrica"
SYN-1012 : SanityCheck: Model "COM3_Control"
SYN-1012 : SanityCheck: Model "Gprmc"
SYN-1012 : SanityCheck: Model "Data_Processing"
SYN-1012 : SanityCheck: Model "Divider"
SYN-1012 : SanityCheck: Model "genclk"
SYN-1012 : SanityCheck: Model "FMC_Ctrl(DATA_LEN=16'b01101100,Vers_Num=16'b0100000010)"
SYN-1012 : SanityCheck: Model "I2C_master"
SYN-1012 : SanityCheck: Model "SCHA634(SCLK_FREQ=1000000)"
SYN-1012 : SanityCheck: Model "CtrlData"
SYN-1012 : SanityCheck: Model "Time_1ms"
SYN-1012 : SanityCheck: Model "SPI_SCHA634(SCLK_FREQ=1000000)"
SYN-1012 : SanityCheck: Model "POWER_EN"
SYN-1043 : Mark I2C_master as IO macro for instance iic_sda_i
SYN-1043 : Mark global_clock as IO macro for instance pll_inst
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 33265/12499 useful/useless nets, 13119/2152 useful/useless insts
SYN-1001 : Optimize 1 decoder instances
SYN-1001 : Bypass 1 mux instances
SYN-1016 : Merged 3239 instances.
SYN-1032 : 32195/2801 useful/useless nets, 11555/898 useful/useless insts
SYN-1016 : Merged 2018 instances.
SYN-5011 WARNING: Undriven pin: model "INS600M_21A" / inst "COM3/rmc_com3/mux25_syn_10" in ../../Src/GNSS/Gprmc.v(395) / pin "i1"
SYN-5011 WARNING: Undriven pin: model "INS600M_21A" / inst "COM3/rmc_com3/mux25_syn_12" in ../../Src/GNSS/Gprmc.v(395) / pin "i1"
SYN-5011 WARNING: Undriven pin: model "INS600M_21A" / inst "IMU_CTRL/SPIM/reg26_syn_8" in ../../Src/SPI/SPI_MASTER.v(222) / pin "d"
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "AnyFog_dataX/Fog_TXO[6]" in ../../Src/IFOG/AnyFog.v(40)
SYN-5014 WARNING: the net's pin: pin "i0[6]" in ../../Src/IFOG/AnyFog.v(185)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "AnyFog_dataX/Fog_TXO[5]" in ../../Src/IFOG/AnyFog.v(40)
SYN-5014 WARNING: the net's pin: pin "i0[5]" in ../../Src/IFOG/AnyFog.v(185)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "AnyFog_dataX/Fog_TXO[4]" in ../../Src/IFOG/AnyFog.v(40)
SYN-5014 WARNING: the net's pin: pin "i0[4]" in ../../Src/IFOG/AnyFog.v(185)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "AnyFog_dataX/Fog_TXO[3]" in ../../Src/IFOG/AnyFog.v(40)
SYN-5014 WARNING: the net's pin: pin "i0[3]" in ../../Src/IFOG/AnyFog.v(185)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "AnyFog_dataX/Fog_TXO[2]" in ../../Src/IFOG/AnyFog.v(40)
SYN-5014 WARNING: the net's pin: pin "i0[2]" in ../../Src/IFOG/AnyFog.v(185)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "AnyFog_dataX/Fog_TXO[1]" in ../../Src/IFOG/AnyFog.v(40)
SYN-5014 WARNING: the net's pin: pin "i0[1]" in ../../Src/IFOG/AnyFog.v(185)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "AnyFog_dataX/Fog_TXO[0]" in ../../Src/IFOG/AnyFog.v(40)
SYN-5014 WARNING: the net's pin: pin "i0[0]" in ../../Src/IFOG/AnyFog.v(185)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "AnyFog_dataX/Fog_TXO_C[6]" in ../../Src/IFOG/AnyFog.v(42)
SYN-5014 WARNING: the net's pin: pin "i1[6]" in ../../Src/IFOG/AnyFog.v(185)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "AnyFog_dataX/Fog_TXO_C[5]" in ../../Src/IFOG/AnyFog.v(42)
SYN-5014 WARNING: the net's pin: pin "i1[5]" in ../../Src/IFOG/AnyFog.v(185)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "AnyFog_dataX/Fog_TXO_C[4]" in ../../Src/IFOG/AnyFog.v(42)
SYN-5014 WARNING: the net's pin: pin "i1[4]" in ../../Src/IFOG/AnyFog.v(185)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "AnyFog_dataX/Fog_TXO_C[3]" in ../../Src/IFOG/AnyFog.v(42)
SYN-5014 WARNING: the net's pin: pin "i1[3]" in ../../Src/IFOG/AnyFog.v(185)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "AnyFog_dataX/Fog_TXO_C[2]" in ../../Src/IFOG/AnyFog.v(42)
SYN-5014 WARNING: the net's pin: pin "i1[2]" in ../../Src/IFOG/AnyFog.v(185)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "AnyFog_dataX/Fog_TXO_C[1]" in ../../Src/IFOG/AnyFog.v(42)
SYN-5014 WARNING: the net's pin: pin "i1[1]" in ../../Src/IFOG/AnyFog.v(185)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "AnyFog_dataX/Fog_TXO_C[0]" in ../../Src/IFOG/AnyFog.v(42)
SYN-5014 WARNING: the net's pin: pin "i1[0]" in ../../Src/IFOG/AnyFog.v(185)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "COM3/rmc_com3/GPRMC_sub_state_cnt0[3]_syn_3" in ../../Src/GNSS/Gprmc.v(54)
SYN-5014 WARNING: the net's pin: pin "i" in ../../Src/GNSS/Gprmc.v(54)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "COM3/rmc_com3/GPRMC_sub_state_cnt0[2]_syn_3" in ../../Src/GNSS/Gprmc.v(54)
SYN-5014 WARNING: the net's pin: pin "i" in ../../Src/GNSS/Gprmc.v(54)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "COM3/rmc_com3/GPRMC_sub_state_cnt0[1]_syn_3" in ../../Src/GNSS/Gprmc.v(54)
SYN-5014 WARNING: the net's pin: pin "i" in ../../Src/GNSS/Gprmc.v(54)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "COM3/rmc_com3/GPRMC_sub_state_cnt0[0]_syn_3" in ../../Src/GNSS/Gprmc.v(54)
SYN-5014 WARNING: the net's pin: pin "i" in ../../Src/GNSS/Gprmc.v(54)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "DATA/DACC_Temp[0]" in ../../Src/FMC/Data_Processing.v(42)
SYN-5014 WARNING: the net's pin: pin "i1" in ../../Src/FMC/Data_Processing.v(532)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "DATA/genclk/time_cnt1[19]_syn_3" in ../../Src/FMC/genclk.v(36)
SYN-5014 WARNING: the net's pin: pin "i" in ../../Src/FMC/genclk.v(36)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "DATA/genclk/time_cnt1[18]_syn_3" in ../../Src/FMC/genclk.v(36)
SYN-5014 WARNING: the net's pin: pin "i" in ../../Src/FMC/genclk.v(36)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "DATA/genclk/time_cnt1[17]_syn_3" in ../../Src/FMC/genclk.v(36)
SYN-5014 WARNING: the net's pin: pin "i" in ../../Src/FMC/genclk.v(36)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "DATA/genclk/time_cnt1[16]_syn_3" in ../../Src/FMC/genclk.v(36)
SYN-5014 WARNING: the net's pin: pin "i" in ../../Src/FMC/genclk.v(36)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "DATA/genclk/time_cnt1[15]_syn_3" in ../../Src/FMC/genclk.v(36)
SYN-5014 WARNING: the net's pin: pin "i" in ../../Src/FMC/genclk.v(36)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "DATA/genclk/time_cnt1[14]_syn_3" in ../../Src/FMC/genclk.v(36)
SYN-5014 WARNING: the net's pin: pin "i" in ../../Src/FMC/genclk.v(36)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "DATA/genclk/time_cnt1[13]_syn_3" in ../../Src/FMC/genclk.v(36)
SYN-5014 WARNING: the net's pin: pin "i" in ../../Src/FMC/genclk.v(36)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "DATA/genclk/time_cnt1[12]_syn_3" in ../../Src/FMC/genclk.v(36)
SYN-5014 WARNING: the net's pin: pin "i" in ../../Src/FMC/genclk.v(36)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "DATA/genclk/time_cnt1[11]_syn_3" in ../../Src/FMC/genclk.v(36)
SYN-5014 WARNING: the net's pin: pin "i" in ../../Src/FMC/genclk.v(36)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "DATA/genclk/time_cnt1[10]_syn_3" in ../../Src/FMC/genclk.v(36)
SYN-5014 WARNING: the net's pin: pin "i" in ../../Src/FMC/genclk.v(36)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "DATA/genclk/time_cnt1[9]_syn_3" in ../../Src/FMC/genclk.v(36)
SYN-5014 WARNING: the net's pin: pin "i" in ../../Src/FMC/genclk.v(36)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "DATA/genclk/time_cnt1[8]_syn_3" in ../../Src/FMC/genclk.v(36)
SYN-5014 WARNING: the net's pin: pin "i" in ../../Src/FMC/genclk.v(36)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "DATA/genclk/time_cnt1[7]_syn_3" in ../../Src/FMC/genclk.v(36)
SYN-5014 WARNING: the net's pin: pin "i" in ../../Src/FMC/genclk.v(36)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "DATA/genclk/time_cnt1[6]_syn_3" in ../../Src/FMC/genclk.v(36)
SYN-5014 WARNING: the net's pin: pin "i" in ../../Src/FMC/genclk.v(36)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "DATA/genclk/time_cnt1[5]_syn_3" in ../../Src/FMC/genclk.v(36)
SYN-5014 WARNING: the net's pin: pin "i" in ../../Src/FMC/genclk.v(36)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "DATA/genclk/time_cnt1[4]_syn_3" in ../../Src/FMC/genclk.v(36)
SYN-5014 WARNING: the net's pin: pin "i" in ../../Src/FMC/genclk.v(36)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "DATA/genclk/time_cnt1[3]_syn_3" in ../../Src/FMC/genclk.v(36)
SYN-5014 WARNING: the net's pin: pin "i" in ../../Src/FMC/genclk.v(36)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "DATA/genclk/time_cnt1[2]_syn_3" in ../../Src/FMC/genclk.v(36)
SYN-5014 WARNING: the net's pin: pin "i" in ../../Src/FMC/genclk.v(36)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "DATA/genclk/time_cnt1[1]_syn_3" in ../../Src/FMC/genclk.v(36)
SYN-5014 WARNING: the net's pin: pin "i" in ../../Src/FMC/genclk.v(36)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "DATA/genclk/time_cnt1[0]_syn_3" in ../../Src/FMC/genclk.v(36)
SYN-5014 WARNING: the net's pin: pin "i" in ../../Src/FMC/genclk.v(36)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[31]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i5" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[30]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i5" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[29]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i5" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[28]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i5" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[27]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i5" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[26]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i5" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[25]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i5" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[24]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i5" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[23]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i5" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[22]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i5" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[21]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i5" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[20]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i5" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[19]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i5" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[18]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i5" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[17]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i5" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[16]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i5" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[15]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i6" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[14]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i6" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[13]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i6" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[12]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i6" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[11]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i6" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[10]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i6" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[9]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i6" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[8]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i6" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[7]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i6" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[6]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i6" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[5]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i6" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[4]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i6" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[3]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i6" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[2]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i6" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[1]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i6" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataX[0]" in ../../Src/FMC/FMC_Ctrl.v(64)
SYN-5014 WARNING: the net's pin: pin "i6" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[31]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i3" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[30]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i3" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[29]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i3" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[28]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i3" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[27]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i3" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[26]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i3" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[25]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i3" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[24]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i3" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[23]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i3" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[22]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i3" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[21]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i3" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[20]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i3" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[19]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i3" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[18]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i3" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[17]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i3" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[16]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i3" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[15]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i4" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[14]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i4" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[13]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i4" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[12]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i4" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[11]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i4" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[10]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i4" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[9]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i4" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[8]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i4" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[7]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i4" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[6]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i4" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[5]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i4" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[4]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i4" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[3]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i4" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[2]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i4" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[1]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i4" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataY[0]" in ../../Src/FMC/FMC_Ctrl.v(65)
SYN-5014 WARNING: the net's pin: pin "i4" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[31]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i1" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[30]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i1" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[29]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i1" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[28]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i1" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[27]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i1" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[26]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i1" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[25]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i1" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[24]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i1" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[23]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i1" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[22]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i1" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[21]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i1" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[20]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i1" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[19]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i1" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[18]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i1" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[17]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i1" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[16]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i1" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[15]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i2" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[14]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i2" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[13]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i2" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[12]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i2" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[11]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i2" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[10]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i2" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[9]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i2" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[8]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i2" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[7]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i2" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[6]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i2" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[5]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i2" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[4]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i2" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[3]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i2" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[2]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i2" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[1]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i2" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5013 WARNING: Undriven net: model "INS600M_21A" / net "FMC/FMC_DACC_MdataZ[0]" in ../../Src/FMC/FMC_Ctrl.v(66)
SYN-5014 WARNING: the net's pin: pin "i2" in ../../Src/FMC/FMC_Ctrl.v(418)
SYN-5025 WARNING: Using 0 for all undriven pins and nets
SYN-1032 : 30596/103 useful/useless nets, 28276/616 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1017 : Remove 2 const input seq instances
SYN-1002 :     IMU_CTRL/CtrlData/cs_due_sub9_reg
SYN-1002 :     IMU_CTRL/CtrlData/reg25_syn_5
SYN-1019 : Optimized 8 mux instances.
SYN-1021 : Optimized 405 onehot mux instances.
SYN-1020 : Optimized 484 distributor mux.
SYN-1001 : Optimize 10 less-than instances
SYN-1019 : Optimized 730 mux instances.
SYN-1016 : Merged 930 instances.
SYN-1015 : Optimize round 1, 10179 better
SYN-1014 : Optimize round 2
SYN-1044 : Optimized 1 inv instances.
SYN-1032 : 23660/68 useful/useless nets, 21340/625 useful/useless insts
SYN-1017 : Remove 13 const input seq instances
SYN-1002 :     DATA/reg128_syn_10
SYN-1002 :     DATA/reg21_syn_10
SYN-1002 :     DATA/reg98_syn_3
SYN-1002 :     IMU_CTRL/CtrlData/reg0_syn_11
SYN-1002 :     IMU_CTRL/CtrlData/reg1_syn_10
SYN-1002 :     IMU_CTRL/CtrlData/reg2_syn_11
SYN-1002 :     IMU_CTRL/CtrlData/reg3_syn_12
SYN-1002 :     IMU_CTRL/CtrlData/reg4_syn_10
SYN-1002 :     IMU_CTRL/CtrlData/reg5_syn_11
SYN-1002 :     IMU_CTRL/CtrlData/reg7_syn_10
SYN-1002 :     IMU_CTRL/CtrlData/reg9_syn_10
SYN-1002 :     DATA/reg84_syn_10
SYN-1002 :     DATA/reg47_syn_10
SYN-1019 : Optimized 155 mux instances.
SYN-1020 : Optimized 4 distributor mux.
SYN-1015 : Optimize round 2, 949 better
SYN-1032 : 23619/1 useful/useless nets, 21299/11 useful/useless insts
SYN-3003 : Optimized 1 equivalent DFF(s)
SYN-3003 : Optimized 1 equivalent DFF(s)
SYN-3003 : Optimized 1 equivalent DFF(s)
SYN-3003 : Optimized 1 equivalent DFF(s)
SYN-3003 : Optimized 1 equivalent DFF(s)
SYN-3003 : Optimized 1 equivalent DFF(s)
SYN-3004 : Optimized 28 const0 DFF(s)
SYN-3004 : Optimized 2 const0 DFF(s)
SYN-1032 : 23522/97 useful/useless nets, 21230/33 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1019 : Optimized 1 mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1016 : Merged 2 instances.
SYN-1015 : Optimize round 1, 43 better
SYN-1014 : Optimize round 2
SYN-1017 : Remove 1 const input seq instances
SYN-1002 :     IMU_CTRL/CtrlData/reg10_syn_12
SYN-1015 : Optimize round 2, 23 better
SYN-1014 : Optimize round 3
SYN-1032 : 23499/0 useful/useless nets, 21207/11 useful/useless insts
SYN-1015 : Optimize round 3, 11 better
SYN-1014 : Optimize round 4
SYN-1015 : Optimize round 4, 0 better
RUN-1003 : finish command "optimize_rtl" in  20.506818s wall, 18.390625s user + 2.125000s system = 20.515625s CPU (100.0%)

RUN-1004 : used memory is 259 MB, reserved memory is 220 MB, peak memory is 268 MB
RUN-1002 : start command "report_area -file INS600M-21A_rtl.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Gate Statistics
#Basic gates            13108
  #and                    351
  #nand                     0
  #or                     148
  #nor                      0
  #xor                    197
  #xnor                     0
  #buf                      0
  #not                    233
  #bufif1                   1
  #MX21                   445
  #FADD                     0
  #DFF                  11733
  #LATCH                    0
#MACRO_ADD                107
#MACRO_EQ                 549
#MACRO_MUX               2899

LUT Statistics
#Total_luts              4356
  #lut4                  1986
  #lut5                    91
  #lut6                     0
  #lut5_mx41                0
  #lut4_alu1b            2279

Report Hierarchy Area:
+----------------------------------------------------------+
|Instance         |Module          |gates  |seq    |macros |
+----------------------------------------------------------+
|top              |INS600M_21A     |1375   |11733  |656    |
|  AnyFog_dataX   |AnyFog          |42     |165    |9      |
|    UART_RX_COM3 |UART_RX115200E  |0      |43     |0      |
|  AnyFog_dataY   |AnyFog          |42     |165    |9      |
|    UART_RX_COM3 |UART_RX115200E  |0      |43     |0      |
|  AnyFog_dataZ   |AnyFog          |42     |165    |9      |
|    UART_RX_COM3 |UART_RX115200E  |0      |43     |0      |
|  CLK100M        |global_clock    |0      |0      |0      |
|  COM2           |COM2_Control    |210    |2748   |295    |
|    PPPNAV_com2  |PPPNAV          |44     |195    |59     |
|    STADOP_com2  |STADOP          |31     |549    |49     |
|    UART_RX_COM3 |UART_RX460800   |0      |38     |0      |
|    head_com2    |uniheading      |35     |257    |60     |
|    uart_com2    |Agrica          |100    |1421   |127    |
|  COM3           |COM3_Control    |21     |180    |19     |
|    UART_RX_COM3 |UART_RX460800   |0      |34     |0      |
|    rmc_com3     |Gprmc           |21     |146    |19     |
|  DATA           |Data_Processing |9      |6927   |35     |
|    DIV_Dtemp    |Divider         |0      |595    |0      |
|    DIV_Utemp    |Divider         |0      |441    |0      |
|    DIV_accX     |Divider         |0      |441    |0      |
|    DIV_accY     |Divider         |0      |441    |0      |
|    DIV_accZ     |Divider         |0      |441    |0      |
|    DIV_rateX    |Divider         |0      |441    |0      |
|    DIV_rateY    |Divider         |0      |441    |0      |
|    DIV_rateZ    |Divider         |0      |441    |0      |
|    genclk       |genclk          |5      |57     |10     |
|  FMC            |FMC_Ctrl        |352    |324    |37     |
|  IIC            |I2C_master      |232    |266    |99     |
|  IMU_CTRL       |SCHA634         |392    |757    |133    |
|    CtrlData     |CtrlData        |224    |307    |108    |
|      usms       |Time_1ms        |4      |18     |2      |
|    SPIM         |SPI_SCHA634     |168    |450    |25     |
|  POWER          |POWER_EN        |0      |36     |11     |
+----------------------------------------------------------+

RUN-1002 : start command "export_db INS600M-21A_rtl.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "optimize_gate -maparea INS600M-21A_gate.area"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
SYN-1032 : 23539/19 useful/useless nets, 21246/21 useful/useless insts
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2571 : Optimize after map_dsp, round 1
SYN-2571 : Optimize after map_dsp, round 1, 0 better
SYN-1001 : Throwback 158 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 803 instances.
SYN-2501 : Optimize round 1, 2923 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 107 macro adder
SYN-3001 : Mapper mapped 7 instances into 1 LUTs, name keeping = 100%.
SYN-3001 : Mapper mapped 7 instances into 1 LUTs, name keeping = 100%.
SYN-3001 : Mapper mapped 7 instances into 1 LUTs, name keeping = 100%.
SYN-3001 : Mapper mapped 7 instances into 1 LUTs, name keeping = 100%.
SYN-3001 : Mapper mapped 7 instances into 1 LUTs, name keeping = 100%.
SYN-3001 : Mapper mapped 7 instances into 1 LUTs, name keeping = 100%.
SYN-3001 : Mapper mapped 7 instances into 1 LUTs, name keeping = 100%.
SYN-2501 : Inferred 7 ROM instances
SYN-1019 : Optimized 402 mux instances.
SYN-1016 : Merged 411 instances.
SYN-1032 : 27973/183 useful/useless nets, 25688/0 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 91674, tnet num: 27973, tinst num: 25687, tnode num: 121976, tedge num: 132817.
TMR-2508 : Levelizing timing graph completed, there are 73 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.240139s wall, 1.218750s user + 0.031250s system = 1.250000s CPU (100.8%)

RUN-1004 : used memory is 396 MB, reserved memory is 361 MB, peak memory is 396 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 27973 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 3088 (3.77), #lev = 7 (2.49)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 3035 (3.77), #lev = 6 (2.52)
SYN-3001 : Logic optimization runtime opt =   0.57 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 8395 instances into 3045 LUTs, name keeping = 49%.
SYN-3001 : Mapper removed 2 lut buffers
RUN-1002 : start command "report_area -file INS600M-21A_gate.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

LUT Statistics
#Total_luts              8374
  #lut4                  4612
  #lut5                   515
  #lut6                     0
  #lut5_mx41                0
  #lut4_alu1b            3247

Utilization Statistics
#lut                     8374   out of  19600   42.72%
#reg                    11714   out of  19600   59.77%
#le                         0
#dsp                        0   out of     29    0.00%
#bram                       0   out of     64    0.00%
  #bram9k                   0
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                    10
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%

Report Hierarchy Area:
+-------------------------------------------------------------------------------+
|Instance         |Module          |lut     |ripple  |seq     |bram    |dsp     |
+-------------------------------------------------------------------------------+
|top              |INS600M_21A     |5127    |3247    |11757   |0       |0       |
|  AnyFog_dataX   |AnyFog          |108     |41      |164     |0       |0       |
|    UART_RX_COM3 |UART_RX115200E  |68      |41      |42      |0       |0       |
|  AnyFog_dataY   |AnyFog          |106     |41      |164     |0       |0       |
|    UART_RX_COM3 |UART_RX115200E  |68      |41      |42      |0       |0       |
|  AnyFog_dataZ   |AnyFog          |108     |41      |164     |0       |0       |
|    UART_RX_COM3 |UART_RX115200E  |68      |41      |42      |0       |0       |
|  CLK100M        |global_clock    |0       |0       |0       |0       |0       |
|  COM2           |COM2_Control    |501     |78      |2747    |0       |0       |
|    PPPNAV_com2  |PPPNAV          |100     |17      |195     |0       |0       |
|    STADOP_com2  |STADOP          |81      |0       |549     |0       |0       |
|    UART_RX_COM3 |UART_RX460800   |65      |26      |37      |0       |0       |
|    head_com2    |uniheading      |92      |17      |257     |0       |0       |
|    uart_com2    |Agrica          |163     |18      |1421    |0       |0       |
|  COM3           |COM3_Control    |134     |26      |179     |0       |0       |
|    UART_RX_COM3 |UART_RX460800   |60      |26      |33      |0       |0       |
|    rmc_com3     |Gprmc           |74      |0       |146     |0       |0       |
|  DATA           |Data_Processing |2114    |2667    |6926    |0       |0       |
|    DIV_Dtemp    |Divider         |222     |263     |595     |0       |0       |
|    DIV_Utemp    |Divider         |218     |263     |441     |0       |0       |
|    DIV_accX     |Divider         |218     |263     |441     |0       |0       |
|    DIV_accY     |Divider         |218     |263     |441     |0       |0       |
|    DIV_accZ     |Divider         |218     |263     |441     |0       |0       |
|    DIV_rateX    |Divider         |218     |263     |441     |0       |0       |
|    DIV_rateY    |Divider         |218     |263     |441     |0       |0       |
|    DIV_rateZ    |Divider         |218     |263     |441     |0       |0       |
|    genclk       |genclk          |24      |56      |56      |0       |0       |
|  FMC            |FMC_Ctrl        |1177    |114     |321     |0       |0       |
|  IIC            |I2C_master      |292     |28      |265     |0       |0       |
|  IMU_CTRL       |SCHA634         |571     |127     |753     |0       |0       |
|    CtrlData     |CtrlData        |445     |94      |305     |0       |0       |
|      usms       |Time_1ms        |24      |17      |18      |0       |0       |
|    SPIM         |SPI_SCHA634     |126     |33      |448     |0       |0       |
|  POWER          |POWER_EN        |16      |84      |31      |0       |0       |
+-------------------------------------------------------------------------------+

SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 11714 DFF/LATCH to SEQ ...
SYN-4009 : Pack 102 carry chain into lslice
SYN-4007 : Packing 1619 adder to BLE ...
SYN-4008 : Packed 1619 adder and 96 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -maparea INS600M-21A_gate.area" in  10.285744s wall, 9.906250s user + 0.390625s system = 10.296875s CPU (100.1%)

RUN-1004 : used memory is 331 MB, reserved memory is 303 MB, peak memory is 526 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
RUN-1002 : start command "export_db INS600M-21A_gate.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1003 : finish command "export_db INS600M-21A_gate.db" in  1.407831s wall, 2.359375s user + 0.046875s system = 2.406250s CPU (170.9%)

RUN-1004 : used memory is 335 MB, reserved memory is 304 MB, peak memory is 526 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/syn_1/td_20250710_160943.log"

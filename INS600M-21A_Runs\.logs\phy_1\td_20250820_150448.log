============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Wed Aug 20 15:04:49 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.661916s wall, 1.671875s user + 3.984375s system = 5.656250s CPU (99.9%)

RUN-1004 : used memory is 80 MB, reserved memory is 40 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.861311s wall, 1.812500s user + 0.046875s system = 1.859375s CPU (99.9%)

RUN-1004 : used memory is 303 MB, reserved memory is 270 MB, peak memory is 306 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 6 view nodes, 43 trigger nets, 43 data nets.
KIT-1004 : Chipwatcher code = 0010111001101000
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb01001010}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=132) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=132) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb01001010}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb01001010}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb01001010})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=132)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=132)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb01001010})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb01001010})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22854/26 useful/useless nets, 19576/15 useful/useless insts
SYN-1016 : Merged 34 instances.
SYN-1032 : 22505/22 useful/useless nets, 20010/18 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 412 better
SYN-1014 : Optimize round 2
SYN-1032 : 22174/45 useful/useless nets, 19679/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.559802s wall, 2.468750s user + 0.078125s system = 2.546875s CPU (99.5%)

RUN-1004 : used memory is 328 MB, reserved memory is 295 MB, peak memory is 330 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22190/155 useful/useless nets, 19714/47 useful/useless insts
SYN-1016 : Merged 14 instances.
SYN-2571 : Optimize after map_dsp, round 1, 216 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 40 instances.
SYN-2501 : Optimize round 1, 82 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 22676/4 useful/useless nets, 20200/3 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82269, tnet num: 22676, tinst num: 20199, tnode num: 115290, tedge num: 128442.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.276226s wall, 1.265625s user + 0.015625s system = 1.281250s CPU (100.4%)

RUN-1004 : used memory is 469 MB, reserved memory is 436 MB, peak memory is 469 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22676 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 242 (3.42), #lev = 7 (1.80)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 242 (3.42), #lev = 7 (1.80)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 559 instances into 242 LUTs, name keeping = 72%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 421 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 128 adder to BLE ...
SYN-4008 : Packed 128 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.907826s wall, 4.843750s user + 0.093750s system = 4.937500s CPU (100.6%)

RUN-1004 : used memory is 352 MB, reserved memory is 318 MB, peak memory is 577 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.819980s wall, 7.625000s user + 0.203125s system = 7.828125s CPU (100.1%)

RUN-1004 : used memory is 353 MB, reserved memory is 318 MB, peak memory is 577 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (282 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19423 instances
RUN-0007 : 5617 luts, 12218 seqs, 983 mslices, 519 lslices, 59 pads, 22 brams, 0 dsps
RUN-1001 : There are total 21907 nets
RUN-1001 : 16426 nets have 2 pins
RUN-1001 : 4289 nets have [3 - 5] pins
RUN-1001 : 832 nets have [6 - 10] pins
RUN-1001 : 234 nets have [11 - 20] pins
RUN-1001 : 105 nets have [21 - 99] pins
RUN-1001 : 21 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4788     
RUN-1001 :   No   |  No   |  Yes  |     707     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     454     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19421 instances, 5617 luts, 12218 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80599, tnet num: 21905, tinst num: 19421, tnode num: 113425, tedge num: 126718.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.254378s wall, 1.265625s user + 0.000000s system = 1.265625s CPU (100.9%)

RUN-1004 : used memory is 528 MB, reserved memory is 498 MB, peak memory is 577 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.190621s wall, 2.156250s user + 0.046875s system = 2.203125s CPU (100.6%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.60807e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19421.
PHY-3001 : Level 1 #clusters 2150.
PHY-3001 : End clustering;  0.161915s wall, 0.312500s user + 0.046875s system = 0.359375s CPU (222.0%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 859943, overlap = 583.938
PHY-3002 : Step(2): len = 801648, overlap = 632.906
PHY-3002 : Step(3): len = 514595, overlap = 800.094
PHY-3002 : Step(4): len = 462388, overlap = 858.719
PHY-3002 : Step(5): len = 367715, overlap = 939.5
PHY-3002 : Step(6): len = 334237, overlap = 995.406
PHY-3002 : Step(7): len = 280367, overlap = 1095.5
PHY-3002 : Step(8): len = 256692, overlap = 1165.38
PHY-3002 : Step(9): len = 221698, overlap = 1241.34
PHY-3002 : Step(10): len = 208783, overlap = 1288.59
PHY-3002 : Step(11): len = 189253, overlap = 1324.72
PHY-3002 : Step(12): len = 178554, overlap = 1349.31
PHY-3002 : Step(13): len = 162138, overlap = 1386.94
PHY-3002 : Step(14): len = 151546, overlap = 1405.06
PHY-3002 : Step(15): len = 141694, overlap = 1426.88
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.10553e-06
PHY-3002 : Step(16): len = 144407, overlap = 1404.56
PHY-3002 : Step(17): len = 182657, overlap = 1295.31
PHY-3002 : Step(18): len = 195689, overlap = 1231.78
PHY-3002 : Step(19): len = 201088, overlap = 1183.97
PHY-3002 : Step(20): len = 195888, overlap = 1148.38
PHY-3002 : Step(21): len = 190703, overlap = 1126.16
PHY-3002 : Step(22): len = 186140, overlap = 1131.56
PHY-3002 : Step(23): len = 181096, overlap = 1128.88
PHY-3002 : Step(24): len = 177761, overlap = 1109.94
PHY-3002 : Step(25): len = 175141, overlap = 1128.28
PHY-3002 : Step(26): len = 173174, overlap = 1136.41
PHY-3002 : Step(27): len = 171514, overlap = 1151.06
PHY-3002 : Step(28): len = 171496, overlap = 1145.91
PHY-3002 : Step(29): len = 170659, overlap = 1142.78
PHY-3002 : Step(30): len = 170486, overlap = 1131.59
PHY-3002 : Step(31): len = 168971, overlap = 1133.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.21107e-06
PHY-3002 : Step(32): len = 176607, overlap = 1096.28
PHY-3002 : Step(33): len = 195062, overlap = 1008.78
PHY-3002 : Step(34): len = 200829, overlap = 994.5
PHY-3002 : Step(35): len = 202738, overlap = 951.406
PHY-3002 : Step(36): len = 202734, overlap = 938.5
PHY-3002 : Step(37): len = 202642, overlap = 945.844
PHY-3002 : Step(38): len = 201572, overlap = 968.906
PHY-3002 : Step(39): len = 201230, overlap = 966.094
PHY-3002 : Step(40): len = 198804, overlap = 981.562
PHY-3002 : Step(41): len = 198679, overlap = 983.906
PHY-3002 : Step(42): len = 197271, overlap = 993.969
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.42214e-06
PHY-3002 : Step(43): len = 205924, overlap = 953.438
PHY-3002 : Step(44): len = 221516, overlap = 886.688
PHY-3002 : Step(45): len = 226888, overlap = 821.281
PHY-3002 : Step(46): len = 228796, overlap = 774.75
PHY-3002 : Step(47): len = 229516, overlap = 740.781
PHY-3002 : Step(48): len = 229309, overlap = 728.969
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 8.84427e-06
PHY-3002 : Step(49): len = 241621, overlap = 699.969
PHY-3002 : Step(50): len = 259637, overlap = 600.125
PHY-3002 : Step(51): len = 263997, overlap = 575.562
PHY-3002 : Step(52): len = 265264, overlap = 572.562
PHY-3002 : Step(53): len = 264432, overlap = 567.906
PHY-3002 : Step(54): len = 263686, overlap = 561.031
PHY-3002 : Step(55): len = 262117, overlap = 558.812
PHY-3002 : Step(56): len = 261688, overlap = 547.906
PHY-3002 : Step(57): len = 261136, overlap = 545.031
PHY-3002 : Step(58): len = 259782, overlap = 533.469
PHY-3002 : Step(59): len = 258470, overlap = 541.812
PHY-3002 : Step(60): len = 257613, overlap = 537.594
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.76885e-05
PHY-3002 : Step(61): len = 267560, overlap = 510.219
PHY-3002 : Step(62): len = 279826, overlap = 432.906
PHY-3002 : Step(63): len = 281622, overlap = 428.75
PHY-3002 : Step(64): len = 283683, overlap = 420.031
PHY-3002 : Step(65): len = 282903, overlap = 417.188
PHY-3002 : Step(66): len = 281457, overlap = 413.688
PHY-3002 : Step(67): len = 280616, overlap = 407.438
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.53771e-05
PHY-3002 : Step(68): len = 290087, overlap = 394.031
PHY-3002 : Step(69): len = 300356, overlap = 376.656
PHY-3002 : Step(70): len = 303724, overlap = 335.969
PHY-3002 : Step(71): len = 305074, overlap = 312.094
PHY-3002 : Step(72): len = 303694, overlap = 331.938
PHY-3002 : Step(73): len = 301955, overlap = 332.312
PHY-3002 : Step(74): len = 299400, overlap = 339.312
PHY-3002 : Step(75): len = 298127, overlap = 311.406
PHY-3002 : Step(76): len = 296898, overlap = 301.125
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 7.07542e-05
PHY-3002 : Step(77): len = 304656, overlap = 296.094
PHY-3002 : Step(78): len = 311914, overlap = 280.219
PHY-3002 : Step(79): len = 312526, overlap = 280.531
PHY-3002 : Step(80): len = 313122, overlap = 274.688
PHY-3002 : Step(81): len = 312311, overlap = 247.812
PHY-3002 : Step(82): len = 312492, overlap = 232.688
PHY-3002 : Step(83): len = 310908, overlap = 229.594
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000141508
PHY-3002 : Step(84): len = 315521, overlap = 235.312
PHY-3002 : Step(85): len = 320531, overlap = 216.5
PHY-3002 : Step(86): len = 321484, overlap = 206.312
PHY-3002 : Step(87): len = 322455, overlap = 199.188
PHY-3002 : Step(88): len = 321133, overlap = 201.531
PHY-3002 : Step(89): len = 321105, overlap = 202.812
PHY-3002 : Step(90): len = 319646, overlap = 210.625
PHY-3002 : Step(91): len = 320395, overlap = 204.938
PHY-3002 : Step(92): len = 319701, overlap = 198.062
PHY-3002 : Step(93): len = 320126, overlap = 186.531
PHY-3002 : Step(94): len = 319129, overlap = 188.75
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000258665
PHY-3002 : Step(95): len = 321646, overlap = 185.75
PHY-3002 : Step(96): len = 325348, overlap = 181.625
PHY-3002 : Step(97): len = 325782, overlap = 184.469
PHY-3002 : Step(98): len = 326613, overlap = 189.625
PHY-3002 : Step(99): len = 327096, overlap = 187.844
PHY-3002 : Step(100): len = 328270, overlap = 182.219
PHY-3002 : Step(101): len = 326905, overlap = 191.031
PHY-3002 : Step(102): len = 326579, overlap = 186.062
PHY-3002 : Step(103): len = 326675, overlap = 174.156
PHY-3002 : Step(104): len = 327505, overlap = 172.75
PHY-3002 : Step(105): len = 326226, overlap = 167.625
PHY-3002 : Step(106): len = 326268, overlap = 167.406
PHY-3002 : Step(107): len = 326582, overlap = 160.031
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000513339
PHY-3002 : Step(108): len = 329533, overlap = 165.25
PHY-3002 : Step(109): len = 335330, overlap = 145.25
PHY-3002 : Step(110): len = 337409, overlap = 136.5
PHY-3002 : Step(111): len = 339304, overlap = 132.969
PHY-3002 : Step(112): len = 338768, overlap = 145.219
PHY-3002 : Step(113): len = 338316, overlap = 141.688
PHY-3002 : Step(114): len = 338160, overlap = 145.156
PHY-3002 : Step(115): len = 337423, overlap = 139.75
PHY-3002 : Step(116): len = 336935, overlap = 131.281
PHY-3002 : Step(117): len = 337321, overlap = 130.562
PHY-3002 : Step(118): len = 338269, overlap = 150.656
PHY-3002 : Step(119): len = 338308, overlap = 147.781
PHY-3002 : Step(120): len = 338013, overlap = 153.625
PHY-3001 : :::11::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(121): len = 338394, overlap = 153.844
PHY-3002 : Step(122): len = 338999, overlap = 155
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.015099s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (207.0%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21907.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 442064, over cnt = 1152(3%), over = 5368, worst = 38
PHY-1001 : End global iterations;  0.907784s wall, 1.171875s user + 0.031250s system = 1.203125s CPU (132.5%)

PHY-1001 : Congestion index: top1 = 74.35, top5 = 52.58, top10 = 43.00, top15 = 37.32.
PHY-3001 : End congestion estimation;  1.152735s wall, 1.390625s user + 0.046875s system = 1.437500s CPU (124.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.980523s wall, 0.984375s user + 0.000000s system = 0.984375s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000100681
PHY-3002 : Step(123): len = 378429, overlap = 132.25
PHY-3002 : Step(124): len = 386354, overlap = 120.031
PHY-3002 : Step(125): len = 388057, overlap = 118.125
PHY-3002 : Step(126): len = 386569, overlap = 109.969
PHY-3002 : Step(127): len = 388678, overlap = 106.219
PHY-3002 : Step(128): len = 393795, overlap = 106.594
PHY-3002 : Step(129): len = 397763, overlap = 109.188
PHY-3002 : Step(130): len = 398586, overlap = 109.25
PHY-3002 : Step(131): len = 399881, overlap = 111.281
PHY-3002 : Step(132): len = 402293, overlap = 109.219
PHY-3002 : Step(133): len = 403880, overlap = 107.594
PHY-3002 : Step(134): len = 404679, overlap = 100.875
PHY-3002 : Step(135): len = 405175, overlap = 93.5625
PHY-3002 : Step(136): len = 405765, overlap = 88.6562
PHY-3002 : Step(137): len = 407448, overlap = 90.5625
PHY-3002 : Step(138): len = 405591, overlap = 92.0625
PHY-3002 : Step(139): len = 404285, overlap = 93.375
PHY-3002 : Step(140): len = 404502, overlap = 92.9688
PHY-3002 : Step(141): len = 405158, overlap = 98.3438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000201362
PHY-3002 : Step(142): len = 404297, overlap = 101.312
PHY-3002 : Step(143): len = 406397, overlap = 103.312
PHY-3002 : Step(144): len = 409721, overlap = 103.344
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000402724
PHY-3002 : Step(145): len = 415153, overlap = 96.375
PHY-3002 : Step(146): len = 422550, overlap = 91.625
PHY-3002 : Step(147): len = 428221, overlap = 92.875
PHY-3002 : Step(148): len = 432004, overlap = 93.9062
PHY-3002 : Step(149): len = 435876, overlap = 88.7812
PHY-3002 : Step(150): len = 438513, overlap = 84.9375
PHY-3002 : Step(151): len = 438133, overlap = 84.4062
PHY-3002 : Step(152): len = 437802, overlap = 87.9062
PHY-3002 : Step(153): len = 437393, overlap = 90.9062
PHY-3002 : Step(154): len = 436111, overlap = 86.3125
PHY-3002 : Step(155): len = 435201, overlap = 87.7812
PHY-3002 : Step(156): len = 435133, overlap = 87.5625
PHY-3002 : Step(157): len = 435150, overlap = 83.25
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000805447
PHY-3002 : Step(158): len = 436981, overlap = 82.1562
PHY-3002 : Step(159): len = 438685, overlap = 77.75
PHY-3002 : Step(160): len = 440119, overlap = 79
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(161): len = 441493, overlap = 71.625
PHY-3002 : Step(162): len = 447153, overlap = 78.875
PHY-3002 : Step(163): len = 453027, overlap = 69.3438
PHY-3002 : Step(164): len = 454609, overlap = 72.25
PHY-3002 : Step(165): len = 454959, overlap = 67
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 57/21907.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 516144, over cnt = 2119(6%), over = 10092, worst = 51
PHY-1001 : End global iterations;  1.018434s wall, 1.781250s user + 0.093750s system = 1.875000s CPU (184.1%)

PHY-1001 : Congestion index: top1 = 81.75, top5 = 61.01, top10 = 51.85, top15 = 46.20.
PHY-3001 : End congestion estimation;  1.315331s wall, 2.062500s user + 0.093750s system = 2.156250s CPU (163.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.024418s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (100.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000106975
PHY-3002 : Step(166): len = 459939, overlap = 385.281
PHY-3002 : Step(167): len = 461748, overlap = 331.125
PHY-3002 : Step(168): len = 455880, overlap = 301.438
PHY-3002 : Step(169): len = 451520, overlap = 276.438
PHY-3002 : Step(170): len = 446436, overlap = 256.594
PHY-3002 : Step(171): len = 443545, overlap = 241.406
PHY-3002 : Step(172): len = 439760, overlap = 239.875
PHY-3002 : Step(173): len = 437138, overlap = 245.5
PHY-3002 : Step(174): len = 434326, overlap = 234.906
PHY-3002 : Step(175): len = 432402, overlap = 235.344
PHY-3002 : Step(176): len = 431425, overlap = 241.031
PHY-3002 : Step(177): len = 428884, overlap = 237.062
PHY-3002 : Step(178): len = 426534, overlap = 239.188
PHY-3002 : Step(179): len = 425491, overlap = 235.781
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00021395
PHY-3002 : Step(180): len = 425497, overlap = 227.375
PHY-3002 : Step(181): len = 426852, overlap = 216.5
PHY-3002 : Step(182): len = 428351, overlap = 211.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000427899
PHY-3002 : Step(183): len = 430275, overlap = 207.812
PHY-3002 : Step(184): len = 435635, overlap = 190.844
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000774008
PHY-3002 : Step(185): len = 436422, overlap = 179.969
PHY-3002 : Step(186): len = 444061, overlap = 169.688
PHY-3002 : Step(187): len = 451518, overlap = 162.5
PHY-3002 : Step(188): len = 452374, overlap = 162.656
PHY-3002 : Step(189): len = 453058, overlap = 150.312
PHY-3002 : Step(190): len = 453150, overlap = 150.969
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00145796
PHY-3002 : Step(191): len = 453743, overlap = 150.781
PHY-3002 : Step(192): len = 456170, overlap = 143.594
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80599, tnet num: 21905, tinst num: 19421, tnode num: 113425, tedge num: 126718.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.546294s wall, 1.500000s user + 0.046875s system = 1.546875s CPU (100.0%)

RUN-1004 : used memory is 568 MB, reserved memory is 541 MB, peak memory is 700 MB
OPT-1001 : Total overflow 528.75 peak overflow 4.81
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 474/21907.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 535552, over cnt = 2378(6%), over = 8356, worst = 22
PHY-1001 : End global iterations;  1.245463s wall, 1.968750s user + 0.031250s system = 2.000000s CPU (160.6%)

PHY-1001 : Congestion index: top1 = 54.83, top5 = 46.31, top10 = 41.86, top15 = 38.95.
PHY-1001 : End incremental global routing;  1.512193s wall, 2.234375s user + 0.031250s system = 2.265625s CPU (149.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.065434s wall, 1.046875s user + 0.015625s system = 1.062500s CPU (99.7%)

OPT-1001 : 16 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19343 has valid locations, 214 needs to be replaced
PHY-3001 : design contains 19619 instances, 5698 luts, 12335 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 470600
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17226/22105.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 544912, over cnt = 2378(6%), over = 8339, worst = 23
PHY-1001 : End global iterations;  0.194103s wall, 0.312500s user + 0.031250s system = 0.343750s CPU (177.1%)

PHY-1001 : Congestion index: top1 = 55.17, top5 = 46.66, top10 = 42.17, top15 = 39.25.
PHY-3001 : End congestion estimation;  0.588098s wall, 0.671875s user + 0.046875s system = 0.718750s CPU (122.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81234, tnet num: 22103, tinst num: 19619, tnode num: 114325, tedge num: 127592.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.566894s wall, 1.578125s user + 0.000000s system = 1.578125s CPU (100.7%)

RUN-1004 : used memory is 610 MB, reserved memory is 594 MB, peak memory is 703 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22103 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.668281s wall, 2.656250s user + 0.015625s system = 2.671875s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(193): len = 470633, overlap = 1.125
PHY-3002 : Step(194): len = 472010, overlap = 0.8125
PHY-3002 : Step(195): len = 472512, overlap = 0.9375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17263/22105.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 544432, over cnt = 2391(6%), over = 8470, worst = 23
PHY-1001 : End global iterations;  0.196998s wall, 0.281250s user + 0.046875s system = 0.328125s CPU (166.6%)

PHY-1001 : Congestion index: top1 = 55.28, top5 = 46.87, top10 = 42.28, top15 = 39.36.
PHY-3001 : End congestion estimation;  0.455746s wall, 0.546875s user + 0.046875s system = 0.593750s CPU (130.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22103 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.061393s wall, 1.031250s user + 0.015625s system = 1.046875s CPU (98.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000726144
PHY-3002 : Step(196): len = 472497, overlap = 144.781
PHY-3002 : Step(197): len = 472785, overlap = 145.094
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00145229
PHY-3002 : Step(198): len = 473057, overlap = 144.625
PHY-3002 : Step(199): len = 473683, overlap = 144.531
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00290458
PHY-3002 : Step(200): len = 473725, overlap = 144.625
PHY-3002 : Step(201): len = 473890, overlap = 144.688
PHY-3001 : Final: Len = 473890, Over = 144.688
PHY-3001 : End incremental placement;  5.779506s wall, 5.984375s user + 0.359375s system = 6.343750s CPU (109.8%)

OPT-1001 : Total overflow 532.91 peak overflow 4.81
OPT-1001 : End high-fanout net optimization;  8.914659s wall, 10.031250s user + 0.406250s system = 10.437500s CPU (117.1%)

OPT-1001 : Current memory(MB): used = 704, reserve = 683, peak = 720.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17245/22105.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 547224, over cnt = 2356(6%), over = 7908, worst = 22
PHY-1002 : len = 579112, over cnt = 1772(5%), over = 5017, worst = 22
PHY-1002 : len = 612184, over cnt = 926(2%), over = 2494, worst = 22
PHY-1002 : len = 630400, over cnt = 516(1%), over = 1364, worst = 22
PHY-1002 : len = 655792, over cnt = 18(0%), over = 54, worst = 7
PHY-1001 : End global iterations;  1.267671s wall, 1.765625s user + 0.031250s system = 1.796875s CPU (141.7%)

PHY-1001 : Congestion index: top1 = 48.88, top5 = 43.04, top10 = 39.86, top15 = 37.93.
OPT-1001 : End congestion update;  1.534883s wall, 2.031250s user + 0.031250s system = 2.062500s CPU (134.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22103 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.952637s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (100.1%)

OPT-0007 : Start: WNS 4137 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.495942s wall, 2.984375s user + 0.031250s system = 3.015625s CPU (120.8%)

OPT-1001 : Current memory(MB): used = 701, reserve = 680, peak = 720.
OPT-1001 : End physical optimization;  13.294544s wall, 15.000000s user + 0.515625s system = 15.515625s CPU (116.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5698 LUT to BLE ...
SYN-4008 : Packed 5698 LUT and 2732 SEQ to BLE.
SYN-4003 : Packing 9603 remaining SEQ's ...
SYN-4005 : Packed 3357 SEQ with LUT/SLICE
SYN-4006 : 137 single LUT's are left
SYN-4006 : 6246 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11944/13796 primitive instances ...
PHY-3001 : End packing;  2.847158s wall, 2.843750s user + 0.000000s system = 2.843750s CPU (99.9%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8105 instances
RUN-1001 : 4009 mslices, 4010 lslices, 59 pads, 22 brams, 0 dsps
RUN-1001 : There are total 19426 nets
RUN-1001 : 13611 nets have 2 pins
RUN-1001 : 4416 nets have [3 - 5] pins
RUN-1001 : 890 nets have [6 - 10] pins
RUN-1001 : 371 nets have [11 - 20] pins
RUN-1001 : 129 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8103 instances, 8019 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 491765, Over = 375.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 8005/19426.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 622832, over cnt = 1521(4%), over = 2466, worst = 7
PHY-1002 : len = 629192, over cnt = 1030(2%), over = 1438, worst = 7
PHY-1002 : len = 639680, over cnt = 438(1%), over = 586, worst = 7
PHY-1002 : len = 645960, over cnt = 133(0%), over = 186, worst = 7
PHY-1002 : len = 649392, over cnt = 4(0%), over = 4, worst = 1
PHY-1001 : End global iterations;  1.264116s wall, 2.031250s user + 0.031250s system = 2.062500s CPU (163.2%)

PHY-1001 : Congestion index: top1 = 49.89, top5 = 43.09, top10 = 39.54, top15 = 37.40.
PHY-3001 : End congestion estimation;  1.604249s wall, 2.359375s user + 0.031250s system = 2.390625s CPU (149.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67304, tnet num: 19424, tinst num: 8103, tnode num: 91328, tedge num: 110935.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.738800s wall, 1.750000s user + 0.000000s system = 1.750000s CPU (100.6%)

RUN-1004 : used memory is 599 MB, reserved memory is 584 MB, peak memory is 720 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19424 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.724077s wall, 2.703125s user + 0.031250s system = 2.734375s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.16579e-05
PHY-3002 : Step(202): len = 499026, overlap = 358
PHY-3002 : Step(203): len = 498119, overlap = 362.5
PHY-3002 : Step(204): len = 497900, overlap = 375.5
PHY-3002 : Step(205): len = 499560, overlap = 393.5
PHY-3002 : Step(206): len = 497854, overlap = 413.25
PHY-3002 : Step(207): len = 496263, overlap = 406.75
PHY-3002 : Step(208): len = 494853, overlap = 400.25
PHY-3002 : Step(209): len = 492591, overlap = 401.75
PHY-3002 : Step(210): len = 490881, overlap = 412.75
PHY-3002 : Step(211): len = 489237, overlap = 416.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000103316
PHY-3002 : Step(212): len = 493805, overlap = 405.75
PHY-3002 : Step(213): len = 498740, overlap = 382
PHY-3002 : Step(214): len = 499286, overlap = 380.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(215): len = 506582, overlap = 369.25
PHY-3002 : Step(216): len = 513079, overlap = 356
PHY-3002 : Step(217): len = 512139, overlap = 353.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.777281s wall, 0.796875s user + 0.890625s system = 1.687500s CPU (217.1%)

PHY-3001 : Trial Legalized: Len = 618327
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 493/19426.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 702336, over cnt = 2318(6%), over = 3732, worst = 9
PHY-1002 : len = 718576, over cnt = 1287(3%), over = 1699, worst = 6
PHY-1002 : len = 727328, over cnt = 815(2%), over = 1042, worst = 6
PHY-1002 : len = 739136, over cnt = 275(0%), over = 347, worst = 5
PHY-1002 : len = 745272, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.950965s wall, 3.421875s user + 0.062500s system = 3.484375s CPU (178.6%)

PHY-1001 : Congestion index: top1 = 49.89, top5 = 44.72, top10 = 41.91, top15 = 40.06.
PHY-3001 : End congestion estimation;  2.345372s wall, 3.812500s user + 0.062500s system = 3.875000s CPU (165.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19424 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.953750s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000184456
PHY-3002 : Step(218): len = 577209, overlap = 75.5
PHY-3002 : Step(219): len = 558792, overlap = 121.75
PHY-3002 : Step(220): len = 547213, overlap = 170.75
PHY-3002 : Step(221): len = 540345, overlap = 204
PHY-3002 : Step(222): len = 535628, overlap = 228.25
PHY-3002 : Step(223): len = 533473, overlap = 241.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000368912
PHY-3002 : Step(224): len = 537540, overlap = 237.5
PHY-3002 : Step(225): len = 541589, overlap = 239.5
PHY-3002 : Step(226): len = 541621, overlap = 238.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(227): len = 543985, overlap = 237
PHY-3002 : Step(228): len = 549039, overlap = 234
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.033680s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (92.8%)

PHY-3001 : Legalized: Len = 590985, Over = 0
PHY-3001 : Spreading special nets. 36 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.087498s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (107.1%)

PHY-3001 : 52 instances has been re-located, deltaX = 26, deltaY = 32, maxDist = 2.
PHY-3001 : Final: Len = 592211, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67304, tnet num: 19424, tinst num: 8103, tnode num: 91328, tedge num: 110935.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.971690s wall, 1.906250s user + 0.062500s system = 1.968750s CPU (99.9%)

RUN-1004 : used memory is 611 MB, reserved memory is 615 MB, peak memory is 720 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3883/19426.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 685856, over cnt = 2173(6%), over = 3400, worst = 6
PHY-1002 : len = 698072, over cnt = 1270(3%), over = 1711, worst = 6
PHY-1002 : len = 712648, over cnt = 492(1%), over = 663, worst = 5
PHY-1002 : len = 721552, over cnt = 104(0%), over = 127, worst = 3
PHY-1002 : len = 723992, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.019040s wall, 3.171875s user + 0.046875s system = 3.218750s CPU (159.4%)

PHY-1001 : Congestion index: top1 = 47.50, top5 = 42.51, top10 = 40.12, top15 = 38.56.
PHY-1001 : End incremental global routing;  2.362667s wall, 3.500000s user + 0.046875s system = 3.546875s CPU (150.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19424 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.058663s wall, 1.031250s user + 0.031250s system = 1.062500s CPU (100.4%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8040 has valid locations, 5 needs to be replaced
PHY-3001 : design contains 8107 instances, 8023 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 592731
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17423/19429.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 724432, over cnt = 10(0%), over = 10, worst = 1
PHY-1002 : len = 724488, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 724536, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 724552, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.516571s wall, 0.562500s user + 0.015625s system = 0.578125s CPU (111.9%)

PHY-1001 : Congestion index: top1 = 47.50, top5 = 42.51, top10 = 40.13, top15 = 38.58.
PHY-3001 : End congestion estimation;  0.833953s wall, 0.859375s user + 0.031250s system = 0.890625s CPU (106.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67331, tnet num: 19427, tinst num: 8107, tnode num: 91362, tedge num: 110971.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.993986s wall, 1.968750s user + 0.031250s system = 2.000000s CPU (100.3%)

RUN-1004 : used memory is 642 MB, reserved memory is 629 MB, peak memory is 720 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19427 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.999972s wall, 2.968750s user + 0.031250s system = 3.000000s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(229): len = 592731, overlap = 0
PHY-3002 : Step(230): len = 592731, overlap = 0
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17427/19429.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 724552, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.121390s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (103.0%)

PHY-1001 : Congestion index: top1 = 47.50, top5 = 42.51, top10 = 40.13, top15 = 38.58.
PHY-3001 : End congestion estimation;  0.434955s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (100.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19427 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.955549s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.0397844
PHY-3002 : Step(231): len = 592901, overlap = 0.75
PHY-3002 : Step(232): len = 592906, overlap = 0.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006466s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 592910, Over = 0
PHY-3001 : End spreading;  0.074248s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (84.2%)

PHY-3001 : Final: Len = 592910, Over = 0
PHY-3001 : End incremental placement;  5.852237s wall, 5.796875s user + 0.125000s system = 5.921875s CPU (101.2%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  9.833114s wall, 10.890625s user + 0.203125s system = 11.093750s CPU (112.8%)

OPT-1001 : Current memory(MB): used = 718, reserve = 703, peak = 722.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17422/19429.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 724896, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 724936, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 724944, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.388889s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (100.4%)

PHY-1001 : Congestion index: top1 = 47.50, top5 = 42.51, top10 = 40.12, top15 = 38.57.
OPT-1001 : End congestion update;  0.701731s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (100.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19427 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.808300s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (100.5%)

OPT-0007 : Start: WNS 4166 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.515066s wall, 1.515625s user + 0.000000s system = 1.515625s CPU (100.0%)

OPT-1001 : Current memory(MB): used = 718, reserve = 703, peak = 722.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19427 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.821018s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (100.9%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17427/19429.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 724944, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.116861s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (93.6%)

PHY-1001 : Congestion index: top1 = 47.50, top5 = 42.51, top10 = 40.12, top15 = 38.57.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19427 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.809380s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (100.4%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4166 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 47.103448
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4166ps with logic level 8 
RUN-1001 :       #2 path slack 4191ps with logic level 8 
OPT-1001 : End physical optimization;  15.646966s wall, 16.812500s user + 0.281250s system = 17.093750s CPU (109.2%)

RUN-1003 : finish command "place" in  71.312160s wall, 137.031250s user + 8.718750s system = 145.750000s CPU (204.4%)

RUN-1004 : used memory is 634 MB, reserved memory is 621 MB, peak memory is 722 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.768114s wall, 2.968750s user + 0.015625s system = 2.984375s CPU (168.8%)

RUN-1004 : used memory is 634 MB, reserved memory is 622 MB, peak memory is 722 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8109 instances
RUN-1001 : 4009 mslices, 4014 lslices, 59 pads, 22 brams, 0 dsps
RUN-1001 : There are total 19429 nets
RUN-1001 : 13611 nets have 2 pins
RUN-1001 : 4415 nets have [3 - 5] pins
RUN-1001 : 891 nets have [6 - 10] pins
RUN-1001 : 373 nets have [11 - 20] pins
RUN-1001 : 130 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67331, tnet num: 19427, tinst num: 8107, tnode num: 91362, tedge num: 110971.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.704320s wall, 1.703125s user + 0.000000s system = 1.703125s CPU (99.9%)

RUN-1004 : used memory is 616 MB, reserved memory is 602 MB, peak memory is 722 MB
PHY-1001 : 4009 mslices, 4014 lslices, 59 pads, 22 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19427 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 664672, over cnt = 2297(6%), over = 3785, worst = 8
PHY-1002 : len = 680144, over cnt = 1411(4%), over = 1983, worst = 6
PHY-1002 : len = 698096, over cnt = 562(1%), over = 742, worst = 6
PHY-1002 : len = 710128, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 710304, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.802129s wall, 2.937500s user + 0.031250s system = 2.968750s CPU (164.7%)

PHY-1001 : Congestion index: top1 = 46.68, top5 = 42.32, top10 = 39.84, top15 = 38.29.
PHY-1001 : End global routing;  2.168230s wall, 3.296875s user + 0.031250s system = 3.328125s CPU (153.5%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 699, reserve = 688, peak = 722.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 968, reserve = 956, peak = 968.
PHY-1001 : End build detailed router design. 4.699202s wall, 4.687500s user + 0.015625s system = 4.703125s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 190504, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.940560s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 1004, reserve = 992, peak = 1004.
PHY-1001 : End phase 1; 0.948716s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (98.8%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.6539e+06, over cnt = 1260(0%), over = 1269, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1020, reserve = 1007, peak = 1020.
PHY-1001 : End initial routed; 14.186179s wall, 43.171875s user + 0.312500s system = 43.484375s CPU (306.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18157(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.051   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.595513s wall, 3.593750s user + 0.000000s system = 3.593750s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1034, reserve = 1021, peak = 1034.
PHY-1001 : End phase 2; 17.781853s wall, 46.765625s user + 0.312500s system = 47.078125s CPU (264.8%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.6539e+06, over cnt = 1260(0%), over = 1269, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.261477s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (95.6%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.64341e+06, over cnt = 405(0%), over = 406, worst = 2, crit = 0
PHY-1001 : End DR Iter 1; 0.634918s wall, 1.250000s user + 0.000000s system = 1.250000s CPU (196.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.64379e+06, over cnt = 87(0%), over = 87, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.409574s wall, 0.500000s user + 0.000000s system = 0.500000s CPU (122.1%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.64464e+06, over cnt = 17(0%), over = 17, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.256544s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (97.4%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.64491e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 4; 0.191252s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (98.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18157(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.081   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.676833s wall, 3.671875s user + 0.000000s system = 3.671875s CPU (99.9%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 303 feed throughs used by 268 nets
PHY-1001 : End commit to database; 2.223988s wall, 2.171875s user + 0.046875s system = 2.218750s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 1120, reserve = 1109, peak = 1120.
PHY-1001 : End phase 3; 8.177268s wall, 8.812500s user + 0.046875s system = 8.859375s CPU (108.3%)

PHY-1003 : Routed, final wirelength = 1.64491e+06
PHY-1001 : Current memory(MB): used = 1124, reserve = 1114, peak = 1124.
PHY-1001 : End export database. 0.062374s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.2%)

PHY-1001 : End detail routing;  32.128985s wall, 61.750000s user + 0.375000s system = 62.125000s CPU (193.4%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67331, tnet num: 19427, tinst num: 8107, tnode num: 91362, tedge num: 110971.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.793777s wall, 1.796875s user + 0.000000s system = 1.796875s CPU (100.2%)

RUN-1004 : used memory is 1054 MB, reserved memory is 1053 MB, peak memory is 1124 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  40.767121s wall, 71.484375s user + 0.437500s system = 71.921875s CPU (176.4%)

RUN-1004 : used memory is 1054 MB, reserved memory is 1053 MB, peak memory is 1124 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8847   out of  19600   45.14%
#reg                    12432   out of  19600   63.43%
#le                     15047
  #lut only              2615   out of  15047   17.38%
  #reg only              6200   out of  15047   41.20%
  #lut&reg               6232   out of  15047   41.42%
#dsp                        0   out of     29    0.00%
#bram                      22   out of     64   34.38%
  #bram9k                  20
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       59   out of    188   31.38%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6779
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          161
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         F2        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         D1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         B3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         C1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         A3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         A2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         B2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         B1        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F15        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDZ        INPUT        K15        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         P2        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         N4        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         M5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         N6        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         N5        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         P5        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M3        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         M4        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         H2        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         M1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         L1        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         N1        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         N3        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         M2        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT         T5        LVCMOS33           8            NONE           OREG       
    Fog_ENY        OUTPUT         T4        LVCMOS33           8            NONE           OREG       
    Fog_ENZ        OUTPUT         T7        LVCMOS33           8            NONE           OREG       
    Fog_INT        OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         T6        LVCMOS33           8            N/A            OREG       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        R15        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E1        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        G11        LVCMOS25           8            N/A            NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15047  |7345    |1502    |12476   |22      |0       |
|  AnyFog_dataX                      |AnyFog          |211    |76      |22      |175     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |88     |61      |22      |52      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |217    |87      |22      |172     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |98     |69      |22      |53      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |208    |89      |22      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |62      |22      |51      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2914   |640     |39      |2830    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |61     |37      |5       |52      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |213    |79      |5       |205     |0       |0       |
|    STADOP_com2                     |STADOP          |548    |112     |0       |544     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |66     |51      |14      |42      |0       |0       |
|    head_com2                       |uniheading      |256    |48      |5       |243     |0       |0       |
|    rmc_com2                        |Gprmc           |37     |37      |0       |29      |0       |0       |
|    uart_com2                       |Agrica          |1419   |240     |10      |1401    |0       |0       |
|  COM3                              |COM3_Control    |279    |128     |19      |239     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |61     |35      |5       |52      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |59     |43      |14      |36      |0       |0       |
|    rmc_com3                        |Gprmc           |159    |50      |0       |151     |0       |0       |
|  DATA                              |Data_Processing |8825   |4572    |1122    |7044    |0       |0       |
|    DIV_Dtemp                       |Divider         |755    |295     |84      |625     |0       |0       |
|    DIV_Utemp                       |Divider         |648    |272     |84      |523     |0       |0       |
|    DIV_accX                        |Divider         |598    |352     |84      |472     |0       |0       |
|    DIV_accY                        |Divider         |672    |365     |102     |511     |0       |0       |
|    DIV_accZ                        |Divider         |654    |360     |132     |450     |0       |0       |
|    DIV_rateX                       |Divider         |669    |424     |132     |464     |0       |0       |
|    DIV_rateY                       |Divider         |575    |360     |132     |370     |0       |0       |
|    DIV_rateZ                       |Divider         |584    |347     |132     |381     |0       |0       |
|    genclk                          |genclk          |266    |172     |89      |107     |0       |0       |
|  FMC                               |FMC_Ctrl        |485    |430     |43      |353     |0       |0       |
|  IIC                               |I2C_master      |272    |254     |11      |240     |0       |0       |
|  IMU_CTRL                          |SCHA634         |929    |648     |61      |749     |0       |0       |
|    CtrlData                        |CtrlData        |456    |402     |47      |332     |0       |0       |
|      usms                          |Time_1ms        |27     |22      |5       |19      |0       |0       |
|    SPIM                            |SPI_SCHA634     |473    |246     |14      |417     |0       |0       |
|  POWER                             |POWER_EN        |102    |56      |38      |43      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |596    |363     |103     |405     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |596    |363     |103     |405     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |268    |149     |0       |255     |0       |0       |
|        reg_inst                    |register        |266    |147     |0       |253     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |328    |214     |103     |150     |0       |0       |
|        bus_inst                    |bus_top         |127    |80      |46      |46      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |27     |17      |10      |10      |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |54     |35      |18      |22      |0       |0       |
|          BUS_DETECTOR[5]$bus_nodes |bus_det         |45     |27      |18      |13      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |119    |89      |29      |70      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13551  
    #2          2       3436   
    #3          3        683   
    #4          4        296   
    #5        5-10       955   
    #6        11-50      429   
    #7       51-100      10    
    #8       101-500      3    
    #9        >500        2    
  Average     2.11             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.227633s wall, 3.781250s user + 0.031250s system = 3.812500s CPU (171.1%)

RUN-1004 : used memory is 1054 MB, reserved memory is 1054 MB, peak memory is 1124 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67331, tnet num: 19427, tinst num: 8107, tnode num: 91362, tedge num: 110971.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.780938s wall, 1.781250s user + 0.000000s system = 1.781250s CPU (100.0%)

RUN-1004 : used memory is 1056 MB, reserved memory is 1057 MB, peak memory is 1124 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19427 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.437244s wall, 1.437500s user + 0.000000s system = 1.437500s CPU (100.0%)

RUN-1004 : used memory is 1062 MB, reserved memory is 1061 MB, peak memory is 1124 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 853bcefd6f8683164bbfb7926f14a230bf207ba9ebe1befe0692b850b36b2763 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8107
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19429, pip num: 144113
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 303
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3223 valid insts, and 405891 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101000110010111001101000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  11.508896s wall, 116.062500s user + 0.093750s system = 116.156250s CPU (1009.3%)

RUN-1004 : used memory is 1181 MB, reserved memory is 1165 MB, peak memory is 1295 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250820_150448.log"

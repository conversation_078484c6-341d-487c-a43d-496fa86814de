============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Aug 21 11:06:05 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.779671s wall, 1.734375s user + 4.031250s system = 5.765625s CPU (99.8%)

RUN-1004 : used memory is 79 MB, reserved memory is 41 MB, peak memory is 90 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.887024s wall, 1.859375s user + 0.031250s system = 1.890625s CPU (100.2%)

RUN-1004 : used memory is 302 MB, reserved memory is 270 MB, peak memory is 305 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 28 trigger nets, 28 data nets.
KIT-1004 : Chipwatcher code = 0010010110000101
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=28,BUS_CTRL_NUM=76,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01011,32'sb01100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100010,32'sb0101000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=98) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=98) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=28,BUS_CTRL_NUM=76,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01011,32'sb01100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100010,32'sb0101000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=28,BUS_CTRL_NUM=76,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01011,32'sb01100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100010,32'sb0101000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=28,BUS_CTRL_NUM=76,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01011,32'sb01100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100010,32'sb0101000})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=98)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=98)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=28,BUS_CTRL_NUM=76,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01011,32'sb01100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100010,32'sb0101000})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=28,BUS_CTRL_NUM=76,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01011,32'sb01100},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100010,32'sb0101000})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22572/23 useful/useless nets, 19446/12 useful/useless insts
SYN-1016 : Merged 29 instances.
SYN-1032 : 22296/20 useful/useless nets, 19802/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 328 better
SYN-1014 : Optimize round 2
SYN-1032 : 22032/45 useful/useless nets, 19538/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.476015s wall, 2.375000s user + 0.078125s system = 2.453125s CPU (99.1%)

RUN-1004 : used memory is 326 MB, reserved memory is 294 MB, peak memory is 329 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22068/222 useful/useless nets, 19598/33 useful/useless insts
SYN-1016 : Merged 33 instances.
SYN-2571 : Optimize after map_dsp, round 1, 288 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 27 instances.
SYN-2501 : Optimize round 1, 55 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 15 instances.
SYN-1032 : 22453/5 useful/useless nets, 19983/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81529, tnet num: 22453, tinst num: 19982, tnode num: 114344, tedge num: 127476.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.289776s wall, 1.265625s user + 0.031250s system = 1.296875s CPU (100.6%)

RUN-1004 : used memory is 466 MB, reserved memory is 434 MB, peak memory is 466 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22453 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 194 (3.61), #lev = 7 (1.97)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 194 (3.61), #lev = 7 (1.97)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 467 instances into 194 LUTs, name keeping = 72%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 337 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 108 adder to BLE ...
SYN-4008 : Packed 108 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.680537s wall, 4.593750s user + 0.109375s system = 4.703125s CPU (100.5%)

RUN-1004 : used memory is 351 MB, reserved memory is 316 MB, peak memory is 573 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.505195s wall, 7.265625s user + 0.234375s system = 7.500000s CPU (99.9%)

RUN-1004 : used memory is 351 MB, reserved memory is 316 MB, peak memory is 573 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (215 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19272 instances
RUN-0007 : 5561 luts, 12132 seqs, 973 mslices, 515 lslices, 59 pads, 27 brams, 0 dsps
RUN-1001 : There are total 21750 nets
RUN-1001 : 16363 nets have 2 pins
RUN-1001 : 4210 nets have [3 - 5] pins
RUN-1001 : 820 nets have [6 - 10] pins
RUN-1001 : 230 nets have [11 - 20] pins
RUN-1001 : 106 nets have [21 - 99] pins
RUN-1001 : 21 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4786     
RUN-1001 :   No   |  No   |  Yes  |     690     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     387     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19270 instances, 5561 luts, 12132 seqs, 1488 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80022, tnet num: 21748, tinst num: 19270, tnode num: 112558, tedge num: 125845.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.372647s wall, 1.328125s user + 0.046875s system = 1.375000s CPU (100.2%)

RUN-1004 : used memory is 523 MB, reserved memory is 496 MB, peak memory is 573 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21748 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.334216s wall, 2.250000s user + 0.078125s system = 2.328125s CPU (99.7%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.57094e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19270.
PHY-3001 : Level 1 #clusters 2225.
PHY-3001 : End clustering;  0.158882s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (137.7%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 884235, overlap = 594.844
PHY-3002 : Step(2): len = 796539, overlap = 664
PHY-3002 : Step(3): len = 520126, overlap = 857.656
PHY-3002 : Step(4): len = 464580, overlap = 938.938
PHY-3002 : Step(5): len = 367003, overlap = 1019.53
PHY-3002 : Step(6): len = 326605, overlap = 1079.19
PHY-3002 : Step(7): len = 277262, overlap = 1154.81
PHY-3002 : Step(8): len = 253937, overlap = 1200.88
PHY-3002 : Step(9): len = 228102, overlap = 1236.22
PHY-3002 : Step(10): len = 210274, overlap = 1271.25
PHY-3002 : Step(11): len = 186392, overlap = 1316.53
PHY-3002 : Step(12): len = 173234, overlap = 1357.47
PHY-3002 : Step(13): len = 156245, overlap = 1390.19
PHY-3002 : Step(14): len = 145794, overlap = 1417.19
PHY-3002 : Step(15): len = 133424, overlap = 1447.38
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.01632e-06
PHY-3002 : Step(16): len = 135201, overlap = 1424.03
PHY-3002 : Step(17): len = 169782, overlap = 1341.28
PHY-3002 : Step(18): len = 181332, overlap = 1279.75
PHY-3002 : Step(19): len = 185601, overlap = 1208.38
PHY-3002 : Step(20): len = 181484, overlap = 1158.53
PHY-3002 : Step(21): len = 176009, overlap = 1178.09
PHY-3002 : Step(22): len = 171882, overlap = 1156.22
PHY-3002 : Step(23): len = 168067, overlap = 1151.03
PHY-3002 : Step(24): len = 164627, overlap = 1154
PHY-3002 : Step(25): len = 161634, overlap = 1168.78
PHY-3002 : Step(26): len = 159946, overlap = 1166.47
PHY-3002 : Step(27): len = 158870, overlap = 1166.09
PHY-3002 : Step(28): len = 158399, overlap = 1171.94
PHY-3002 : Step(29): len = 158058, overlap = 1163.38
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.03265e-06
PHY-3002 : Step(30): len = 164156, overlap = 1131.41
PHY-3002 : Step(31): len = 180053, overlap = 1024.81
PHY-3002 : Step(32): len = 184510, overlap = 950.344
PHY-3002 : Step(33): len = 187988, overlap = 929
PHY-3002 : Step(34): len = 188015, overlap = 916.469
PHY-3002 : Step(35): len = 188451, overlap = 900.75
PHY-3002 : Step(36): len = 187105, overlap = 894.375
PHY-3002 : Step(37): len = 187100, overlap = 897
PHY-3002 : Step(38): len = 185515, overlap = 896.188
PHY-3002 : Step(39): len = 184797, overlap = 896.281
PHY-3002 : Step(40): len = 183672, overlap = 897.719
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.06529e-06
PHY-3002 : Step(41): len = 194522, overlap = 872.219
PHY-3002 : Step(42): len = 210471, overlap = 795.844
PHY-3002 : Step(43): len = 213753, overlap = 745.281
PHY-3002 : Step(44): len = 215808, overlap = 722.844
PHY-3002 : Step(45): len = 215319, overlap = 715.969
PHY-3002 : Step(46): len = 214388, overlap = 704.969
PHY-3002 : Step(47): len = 212640, overlap = 681.656
PHY-3002 : Step(48): len = 211197, overlap = 672.25
PHY-3002 : Step(49): len = 209821, overlap = 668.812
PHY-3002 : Step(50): len = 209105, overlap = 660.469
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 8.13059e-06
PHY-3002 : Step(51): len = 218866, overlap = 619.312
PHY-3002 : Step(52): len = 233657, overlap = 564.312
PHY-3002 : Step(53): len = 236193, overlap = 536.406
PHY-3002 : Step(54): len = 237938, overlap = 553.875
PHY-3002 : Step(55): len = 237705, overlap = 565.219
PHY-3002 : Step(56): len = 237030, overlap = 576.219
PHY-3002 : Step(57): len = 235287, overlap = 580.625
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.62612e-05
PHY-3002 : Step(58): len = 244874, overlap = 558.281
PHY-3002 : Step(59): len = 257502, overlap = 523.406
PHY-3002 : Step(60): len = 261484, overlap = 468.188
PHY-3002 : Step(61): len = 263853, overlap = 480.812
PHY-3002 : Step(62): len = 263089, overlap = 482.156
PHY-3002 : Step(63): len = 262001, overlap = 469.906
PHY-3002 : Step(64): len = 260304, overlap = 483.875
PHY-3002 : Step(65): len = 259982, overlap = 490.156
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.25223e-05
PHY-3002 : Step(66): len = 268278, overlap = 476.562
PHY-3002 : Step(67): len = 280173, overlap = 439.594
PHY-3002 : Step(68): len = 284067, overlap = 408.938
PHY-3002 : Step(69): len = 285251, overlap = 380.188
PHY-3002 : Step(70): len = 284429, overlap = 374.469
PHY-3002 : Step(71): len = 283456, overlap = 355.188
PHY-3002 : Step(72): len = 280514, overlap = 347.438
PHY-3002 : Step(73): len = 279499, overlap = 357.125
PHY-3002 : Step(74): len = 279586, overlap = 348.594
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 6.50447e-05
PHY-3002 : Step(75): len = 288552, overlap = 336.469
PHY-3002 : Step(76): len = 296682, overlap = 292.094
PHY-3002 : Step(77): len = 298241, overlap = 264.25
PHY-3002 : Step(78): len = 298680, overlap = 264.438
PHY-3002 : Step(79): len = 297691, overlap = 259.188
PHY-3002 : Step(80): len = 297434, overlap = 259.406
PHY-3002 : Step(81): len = 295964, overlap = 249.719
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000130089
PHY-3002 : Step(82): len = 302396, overlap = 238.969
PHY-3002 : Step(83): len = 308096, overlap = 225.312
PHY-3002 : Step(84): len = 309601, overlap = 215.5
PHY-3002 : Step(85): len = 310422, overlap = 205.844
PHY-3002 : Step(86): len = 309497, overlap = 210.75
PHY-3002 : Step(87): len = 309164, overlap = 219.25
PHY-3002 : Step(88): len = 307463, overlap = 216.844
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000237246
PHY-3002 : Step(89): len = 310736, overlap = 217.5
PHY-3002 : Step(90): len = 314620, overlap = 206.688
PHY-3002 : Step(91): len = 315848, overlap = 204.312
PHY-3002 : Step(92): len = 316837, overlap = 194.312
PHY-3002 : Step(93): len = 316740, overlap = 195
PHY-3002 : Step(94): len = 316625, overlap = 197.656
PHY-3002 : Step(95): len = 315276, overlap = 193
PHY-3002 : Step(96): len = 315298, overlap = 195.938
PHY-3002 : Step(97): len = 315054, overlap = 196.656
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.013322s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21750.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 415088, over cnt = 1155(3%), over = 5131, worst = 35
PHY-1001 : End global iterations;  0.841351s wall, 1.093750s user + 0.000000s system = 1.093750s CPU (130.0%)

PHY-1001 : Congestion index: top1 = 70.69, top5 = 49.97, top10 = 41.08, top15 = 35.87.
PHY-3001 : End congestion estimation;  1.089379s wall, 1.312500s user + 0.015625s system = 1.328125s CPU (121.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21748 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.981812s wall, 0.968750s user + 0.015625s system = 0.984375s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000101799
PHY-3002 : Step(98): len = 356900, overlap = 183.094
PHY-3002 : Step(99): len = 367549, overlap = 162.938
PHY-3002 : Step(100): len = 374643, overlap = 154.062
PHY-3002 : Step(101): len = 378700, overlap = 143.875
PHY-3002 : Step(102): len = 383871, overlap = 139.094
PHY-3002 : Step(103): len = 391523, overlap = 123.938
PHY-3002 : Step(104): len = 401649, overlap = 124.812
PHY-3002 : Step(105): len = 403014, overlap = 119.562
PHY-3002 : Step(106): len = 405675, overlap = 119.781
PHY-3002 : Step(107): len = 409468, overlap = 122.188
PHY-3002 : Step(108): len = 413780, overlap = 128.625
PHY-3002 : Step(109): len = 416888, overlap = 125.688
PHY-3002 : Step(110): len = 419955, overlap = 123.594
PHY-3002 : Step(111): len = 423514, overlap = 118.312
PHY-3002 : Step(112): len = 426279, overlap = 120.125
PHY-3002 : Step(113): len = 429574, overlap = 119.938
PHY-3002 : Step(114): len = 430835, overlap = 117.719
PHY-3002 : Step(115): len = 433679, overlap = 111.344
PHY-3002 : Step(116): len = 436780, overlap = 112.5
PHY-3002 : Step(117): len = 436144, overlap = 110.406
PHY-3002 : Step(118): len = 436921, overlap = 110.906
PHY-3002 : Step(119): len = 435700, overlap = 107.594
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000203598
PHY-3002 : Step(120): len = 436817, overlap = 101.688
PHY-3002 : Step(121): len = 439155, overlap = 98.75
PHY-3002 : Step(122): len = 441876, overlap = 98.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00039893
PHY-3002 : Step(123): len = 448919, overlap = 95.6875
PHY-3002 : Step(124): len = 459351, overlap = 94.0938
PHY-3002 : Step(125): len = 462227, overlap = 95.625
PHY-3002 : Step(126): len = 464107, overlap = 90.7812
PHY-3002 : Step(127): len = 465741, overlap = 92
PHY-3002 : Step(128): len = 464708, overlap = 88.2188
PHY-3002 : Step(129): len = 465257, overlap = 93.4062
PHY-3002 : Step(130): len = 468249, overlap = 87.5312
PHY-3002 : Step(131): len = 468273, overlap = 87.9688
PHY-3002 : Step(132): len = 467111, overlap = 85.5625
PHY-3002 : Step(133): len = 468662, overlap = 85.3125
PHY-3002 : Step(134): len = 467799, overlap = 87.3438
PHY-3002 : Step(135): len = 466234, overlap = 92.6875
PHY-3002 : Step(136): len = 466779, overlap = 98.1875
PHY-3002 : Step(137): len = 467522, overlap = 100.125
PHY-3002 : Step(138): len = 464775, overlap = 103.594
PHY-3002 : Step(139): len = 464251, overlap = 105.312
PHY-3002 : Step(140): len = 465114, overlap = 107.781
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000797861
PHY-3002 : Step(141): len = 464591, overlap = 110.031
PHY-3002 : Step(142): len = 467924, overlap = 111.281
PHY-3002 : Step(143): len = 472097, overlap = 110.531
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00154107
PHY-3002 : Step(144): len = 472288, overlap = 102.875
PHY-3002 : Step(145): len = 476633, overlap = 105.375
PHY-3002 : Step(146): len = 484041, overlap = 107.781
PHY-3002 : Step(147): len = 485643, overlap = 105.625
PHY-3002 : Step(148): len = 485196, overlap = 108.531
PHY-3002 : Step(149): len = 487017, overlap = 108.719
PHY-3002 : Step(150): len = 487437, overlap = 112.75
PHY-3002 : Step(151): len = 486101, overlap = 115.625
PHY-3002 : Step(152): len = 485917, overlap = 116.344
PHY-3002 : Step(153): len = 486107, overlap = 120.062
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 39/21750.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 549384, over cnt = 2256(6%), over = 10516, worst = 35
PHY-1001 : End global iterations;  1.061653s wall, 1.687500s user + 0.031250s system = 1.718750s CPU (161.9%)

PHY-1001 : Congestion index: top1 = 80.02, top5 = 62.24, top10 = 53.01, top15 = 47.31.
PHY-3001 : End congestion estimation;  1.361473s wall, 2.000000s user + 0.031250s system = 2.031250s CPU (149.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21748 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.015980s wall, 1.000000s user + 0.015625s system = 1.015625s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000105245
PHY-3002 : Step(154): len = 494507, overlap = 351.875
PHY-3002 : Step(155): len = 499601, overlap = 278.281
PHY-3002 : Step(156): len = 491895, overlap = 250.906
PHY-3002 : Step(157): len = 486047, overlap = 237.562
PHY-3002 : Step(158): len = 480669, overlap = 221.219
PHY-3002 : Step(159): len = 474668, overlap = 214.562
PHY-3002 : Step(160): len = 470802, overlap = 219.812
PHY-3002 : Step(161): len = 468201, overlap = 219.688
PHY-3002 : Step(162): len = 465063, overlap = 217.312
PHY-3002 : Step(163): len = 462855, overlap = 225.531
PHY-3002 : Step(164): len = 461018, overlap = 229.594
PHY-3002 : Step(165): len = 459124, overlap = 217.188
PHY-3002 : Step(166): len = 456994, overlap = 213.688
PHY-3002 : Step(167): len = 455602, overlap = 213.844
PHY-3002 : Step(168): len = 452603, overlap = 212.688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00021049
PHY-3002 : Step(169): len = 453352, overlap = 212.781
PHY-3002 : Step(170): len = 454400, overlap = 202.594
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000406513
PHY-3002 : Step(171): len = 455895, overlap = 194.156
PHY-3002 : Step(172): len = 462859, overlap = 177.375
PHY-3002 : Step(173): len = 468118, overlap = 173.781
PHY-3002 : Step(174): len = 468576, overlap = 165.188
PHY-3002 : Step(175): len = 469489, overlap = 162.719
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80022, tnet num: 21748, tinst num: 19270, tnode num: 112558, tedge num: 125845.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.614340s wall, 1.578125s user + 0.046875s system = 1.625000s CPU (100.7%)

RUN-1004 : used memory is 563 MB, reserved memory is 538 MB, peak memory is 696 MB
OPT-1001 : Total overflow 506.12 peak overflow 4.16
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 352/21750.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 544576, over cnt = 2512(7%), over = 9192, worst = 27
PHY-1001 : End global iterations;  1.260361s wall, 2.046875s user + 0.109375s system = 2.156250s CPU (171.1%)

PHY-1001 : Congestion index: top1 = 61.06, top5 = 49.18, top10 = 43.80, top15 = 40.50.
PHY-1001 : End incremental global routing;  1.521115s wall, 2.296875s user + 0.109375s system = 2.406250s CPU (158.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21748 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.065365s wall, 1.062500s user + 0.000000s system = 1.062500s CPU (99.7%)

OPT-1001 : 17 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19191 has valid locations, 231 needs to be replaced
PHY-3001 : design contains 19484 instances, 5652 luts, 12255 seqs, 1488 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 484359
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17194/21964.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 557680, over cnt = 2529(7%), over = 9241, worst = 27
PHY-1001 : End global iterations;  0.192614s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (121.7%)

PHY-1001 : Congestion index: top1 = 61.12, top5 = 49.30, top10 = 43.97, top15 = 40.76.
PHY-3001 : End congestion estimation;  0.468823s wall, 0.484375s user + 0.031250s system = 0.515625s CPU (110.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80710, tnet num: 21962, tinst num: 19484, tnode num: 113528, tedge num: 126793.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.600885s wall, 1.562500s user + 0.031250s system = 1.593750s CPU (99.6%)

RUN-1004 : used memory is 608 MB, reserved memory is 596 MB, peak memory is 699 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21962 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.705865s wall, 2.671875s user + 0.031250s system = 2.703125s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(176): len = 484448, overlap = 3.25
PHY-3002 : Step(177): len = 486012, overlap = 3.375
PHY-3002 : Step(178): len = 486338, overlap = 3.5
PHY-3002 : Step(179): len = 486919, overlap = 3.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17228/21964.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 557200, over cnt = 2548(7%), over = 9339, worst = 27
PHY-1001 : End global iterations;  0.185664s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (134.7%)

PHY-1001 : Congestion index: top1 = 61.34, top5 = 49.63, top10 = 44.24, top15 = 41.02.
PHY-3001 : End congestion estimation;  0.442115s wall, 0.500000s user + 0.000000s system = 0.500000s CPU (113.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21962 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.054403s wall, 1.046875s user + 0.031250s system = 1.078125s CPU (102.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00045603
PHY-3002 : Step(180): len = 486775, overlap = 165.969
PHY-3002 : Step(181): len = 486905, overlap = 165.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00091206
PHY-3002 : Step(182): len = 487076, overlap = 165.156
PHY-3002 : Step(183): len = 487206, overlap = 164.906
PHY-3001 : Final: Len = 487206, Over = 164.906
PHY-3001 : End incremental placement;  5.789696s wall, 5.750000s user + 0.296875s system = 6.046875s CPU (104.4%)

OPT-1001 : Total overflow 511.78 peak overflow 4.16
OPT-1001 : End high-fanout net optimization;  8.949335s wall, 9.843750s user + 0.406250s system = 10.250000s CPU (114.5%)

OPT-1001 : Current memory(MB): used = 701, reserve = 680, peak = 717.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17215/21964.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 558840, over cnt = 2493(7%), over = 8794, worst = 27
PHY-1002 : len = 603624, over cnt = 1793(5%), over = 4626, worst = 27
PHY-1002 : len = 653040, over cnt = 586(1%), over = 1108, worst = 13
PHY-1002 : len = 662360, over cnt = 233(0%), over = 444, worst = 11
PHY-1002 : len = 669072, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.334231s wall, 2.125000s user + 0.062500s system = 2.187500s CPU (164.0%)

PHY-1001 : Congestion index: top1 = 50.41, top5 = 44.22, top10 = 40.98, top15 = 38.92.
OPT-1001 : End congestion update;  1.597093s wall, 2.390625s user + 0.062500s system = 2.453125s CPU (153.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 21962 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.913833s wall, 0.906250s user + 0.000000s system = 0.906250s CPU (99.2%)

OPT-0007 : Start: WNS 3919 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.516968s wall, 3.312500s user + 0.062500s system = 3.375000s CPU (134.1%)

OPT-1001 : Current memory(MB): used = 697, reserve = 676, peak = 717.
OPT-1001 : End physical optimization;  13.409519s wall, 15.125000s user + 0.531250s system = 15.656250s CPU (116.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5652 LUT to BLE ...
SYN-4008 : Packed 5652 LUT and 2729 SEQ to BLE.
SYN-4003 : Packing 9526 remaining SEQ's ...
SYN-4005 : Packed 3263 SEQ with LUT/SLICE
SYN-4006 : 179 single LUT's are left
SYN-4006 : 6263 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11915/13758 primitive instances ...
PHY-3001 : End packing;  2.943290s wall, 2.921875s user + 0.000000s system = 2.921875s CPU (99.3%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8112 instances
RUN-1001 : 4010 mslices, 4011 lslices, 59 pads, 27 brams, 0 dsps
RUN-1001 : There are total 19287 nets
RUN-1001 : 13565 nets have 2 pins
RUN-1001 : 4317 nets have [3 - 5] pins
RUN-1001 : 898 nets have [6 - 10] pins
RUN-1001 : 364 nets have [11 - 20] pins
RUN-1001 : 134 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8110 instances, 8021 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 503590, Over = 389.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 8111/19287.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 637272, over cnt = 1548(4%), over = 2446, worst = 8
PHY-1002 : len = 642640, over cnt = 1054(2%), over = 1455, worst = 6
PHY-1002 : len = 654952, over cnt = 380(1%), over = 480, worst = 5
PHY-1002 : len = 660472, over cnt = 91(0%), over = 114, worst = 4
PHY-1002 : len = 662488, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.254809s wall, 1.937500s user + 0.015625s system = 1.953125s CPU (155.7%)

PHY-1001 : Congestion index: top1 = 51.19, top5 = 44.09, top10 = 40.70, top15 = 38.59.
PHY-3001 : End congestion estimation;  1.597495s wall, 2.296875s user + 0.015625s system = 2.312500s CPU (144.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66949, tnet num: 19285, tinst num: 8110, tnode num: 90876, tedge num: 110355.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.769190s wall, 1.765625s user + 0.000000s system = 1.765625s CPU (99.8%)

RUN-1004 : used memory is 604 MB, reserved memory is 596 MB, peak memory is 717 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19285 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.757671s wall, 2.734375s user + 0.015625s system = 2.750000s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.70711e-05
PHY-3002 : Step(184): len = 509521, overlap = 372
PHY-3002 : Step(185): len = 510037, overlap = 371.75
PHY-3002 : Step(186): len = 511482, overlap = 379.25
PHY-3002 : Step(187): len = 510368, overlap = 387
PHY-3002 : Step(188): len = 506613, overlap = 394
PHY-3002 : Step(189): len = 504493, overlap = 404
PHY-3002 : Step(190): len = 502168, overlap = 411
PHY-3002 : Step(191): len = 500422, overlap = 405.5
PHY-3002 : Step(192): len = 498390, overlap = 402
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.41421e-05
PHY-3002 : Step(193): len = 503620, overlap = 387.5
PHY-3002 : Step(194): len = 508360, overlap = 377
PHY-3002 : Step(195): len = 508658, overlap = 372.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000188284
PHY-3002 : Step(196): len = 517362, overlap = 349.75
PHY-3002 : Step(197): len = 528470, overlap = 334
PHY-3002 : Step(198): len = 526314, overlap = 332.25
PHY-3002 : Step(199): len = 524341, overlap = 333.25
PHY-3002 : Step(200): len = 524600, overlap = 338.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.741868s wall, 0.687500s user + 0.890625s system = 1.578125s CPU (212.7%)

PHY-3001 : Trial Legalized: Len = 621985
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 541/19287.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 706976, over cnt = 2392(6%), over = 3939, worst = 8
PHY-1002 : len = 720568, over cnt = 1582(4%), over = 2289, worst = 7
PHY-1002 : len = 740048, over cnt = 613(1%), over = 843, worst = 7
PHY-1002 : len = 747816, over cnt = 290(0%), over = 384, worst = 4
PHY-1002 : len = 755088, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.927329s wall, 3.062500s user + 0.031250s system = 3.093750s CPU (160.5%)

PHY-1001 : Congestion index: top1 = 48.36, top5 = 44.21, top10 = 41.76, top15 = 40.16.
PHY-3001 : End congestion estimation;  2.315034s wall, 3.453125s user + 0.031250s system = 3.484375s CPU (150.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19285 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.938317s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000177473
PHY-3002 : Step(201): len = 583338, overlap = 83.5
PHY-3002 : Step(202): len = 565914, overlap = 125
PHY-3002 : Step(203): len = 555740, overlap = 169.75
PHY-3002 : Step(204): len = 549725, overlap = 199
PHY-3002 : Step(205): len = 546112, overlap = 229.75
PHY-3002 : Step(206): len = 543964, overlap = 240
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000354946
PHY-3002 : Step(207): len = 547954, overlap = 235.25
PHY-3002 : Step(208): len = 552237, overlap = 227.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000709893
PHY-3002 : Step(209): len = 555305, overlap = 223
PHY-3002 : Step(210): len = 562172, overlap = 220.75
PHY-3002 : Step(211): len = 563152, overlap = 219.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.032944s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (94.9%)

PHY-3001 : Legalized: Len = 602328, Over = 0
PHY-3001 : Spreading special nets. 39 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.086747s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (108.1%)

PHY-3001 : 61 instances has been re-located, deltaX = 24, deltaY = 38, maxDist = 2.
PHY-3001 : Final: Len = 603518, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66949, tnet num: 19285, tinst num: 8110, tnode num: 90876, tedge num: 110355.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.035231s wall, 2.031250s user + 0.000000s system = 2.031250s CPU (99.8%)

RUN-1004 : used memory is 596 MB, reserved memory is 570 MB, peak memory is 717 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 4111/19287.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 701096, over cnt = 2204(6%), over = 3419, worst = 6
PHY-1002 : len = 712048, over cnt = 1367(3%), over = 1905, worst = 5
PHY-1002 : len = 725472, over cnt = 652(1%), over = 888, worst = 5
PHY-1002 : len = 732880, over cnt = 323(0%), over = 407, worst = 5
PHY-1002 : len = 740400, over cnt = 2(0%), over = 3, worst = 2
PHY-1001 : End global iterations;  1.765408s wall, 2.843750s user + 0.062500s system = 2.906250s CPU (164.6%)

PHY-1001 : Congestion index: top1 = 47.24, top5 = 42.73, top10 = 40.40, top15 = 38.78.
PHY-1001 : End incremental global routing;  2.096996s wall, 3.171875s user + 0.062500s system = 3.234375s CPU (154.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19285 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.995328s wall, 0.984375s user + 0.015625s system = 1.000000s CPU (100.5%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8046 has valid locations, 19 needs to be replaced
PHY-3001 : design contains 8127 instances, 8038 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 605199
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17369/19303.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 742264, over cnt = 23(0%), over = 27, worst = 2
PHY-1002 : len = 742288, over cnt = 12(0%), over = 12, worst = 1
PHY-1002 : len = 742384, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 742464, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 742480, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.701278s wall, 0.812500s user + 0.046875s system = 0.859375s CPU (122.5%)

PHY-1001 : Congestion index: top1 = 47.52, top5 = 42.83, top10 = 40.44, top15 = 38.82.
PHY-3001 : End congestion estimation;  1.019641s wall, 1.125000s user + 0.046875s system = 1.171875s CPU (114.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67065, tnet num: 19301, tinst num: 8127, tnode num: 91015, tedge num: 110499.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.030191s wall, 2.000000s user + 0.031250s system = 2.031250s CPU (100.1%)

RUN-1004 : used memory is 661 MB, reserved memory is 655 MB, peak memory is 717 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19301 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.046649s wall, 3.000000s user + 0.046875s system = 3.046875s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(212): len = 605303, overlap = 0
PHY-3002 : Step(213): len = 605228, overlap = 0.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17364/19303.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 741848, over cnt = 19(0%), over = 21, worst = 2
PHY-1002 : len = 741752, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 741848, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 741896, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 741944, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.708220s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (103.7%)

PHY-1001 : Congestion index: top1 = 47.52, top5 = 42.89, top10 = 40.51, top15 = 38.85.
PHY-3001 : End congestion estimation;  1.030933s wall, 1.062500s user + 0.000000s system = 1.062500s CPU (103.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19301 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.949406s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (98.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000444033
PHY-3002 : Step(214): len = 605247, overlap = 1
PHY-3002 : Step(215): len = 605327, overlap = 2.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006955s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (224.7%)

PHY-3001 : Legalized: Len = 605387, Over = 0
PHY-3001 : End spreading;  0.072251s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (108.1%)

PHY-3001 : Final: Len = 605387, Over = 0
PHY-3001 : End incremental placement;  6.696511s wall, 6.921875s user + 0.171875s system = 7.093750s CPU (105.9%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.305561s wall, 11.593750s user + 0.250000s system = 11.843750s CPU (114.9%)

OPT-1001 : Current memory(MB): used = 713, reserve = 697, peak = 718.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17369/19303.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 742136, over cnt = 20(0%), over = 24, worst = 3
PHY-1002 : len = 742112, over cnt = 12(0%), over = 12, worst = 1
PHY-1002 : len = 742224, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 742256, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 742272, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.728290s wall, 0.765625s user + 0.015625s system = 0.781250s CPU (107.3%)

PHY-1001 : Congestion index: top1 = 47.26, top5 = 42.80, top10 = 40.41, top15 = 38.80.
OPT-1001 : End congestion update;  1.047698s wall, 1.078125s user + 0.015625s system = 1.093750s CPU (104.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19301 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.803003s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (101.2%)

OPT-0007 : Start: WNS 4110 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.856383s wall, 1.890625s user + 0.015625s system = 1.906250s CPU (102.7%)

OPT-1001 : Current memory(MB): used = 713, reserve = 697, peak = 718.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19301 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.808082s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (100.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17386/19303.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 742272, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.122699s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (89.1%)

PHY-1001 : Congestion index: top1 = 47.26, top5 = 42.80, top10 = 40.41, top15 = 38.80.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19301 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.802264s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4110 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 46.827586
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4110ps with logic level 4 
RUN-1001 :       #2 path slack 4167ps with logic level 4 
OPT-1001 : End physical optimization;  16.510344s wall, 18.015625s user + 0.265625s system = 18.281250s CPU (110.7%)

RUN-1003 : finish command "place" in  75.155946s wall, 144.265625s user + 8.203125s system = 152.468750s CPU (202.9%)

RUN-1004 : used memory is 630 MB, reserved memory is 607 MB, peak memory is 718 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.728829s wall, 2.937500s user + 0.031250s system = 2.968750s CPU (171.7%)

RUN-1004 : used memory is 630 MB, reserved memory is 608 MB, peak memory is 718 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8129 instances
RUN-1001 : 4027 mslices, 4011 lslices, 59 pads, 27 brams, 0 dsps
RUN-1001 : There are total 19303 nets
RUN-1001 : 13568 nets have 2 pins
RUN-1001 : 4318 nets have [3 - 5] pins
RUN-1001 : 904 nets have [6 - 10] pins
RUN-1001 : 369 nets have [11 - 20] pins
RUN-1001 : 135 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67065, tnet num: 19301, tinst num: 8127, tnode num: 91015, tedge num: 110499.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.741892s wall, 1.750000s user + 0.000000s system = 1.750000s CPU (100.5%)

RUN-1004 : used memory is 637 MB, reserved memory is 628 MB, peak memory is 718 MB
PHY-1001 : 4027 mslices, 4011 lslices, 59 pads, 27 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19301 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 680952, over cnt = 2390(6%), over = 3831, worst = 7
PHY-1002 : len = 693768, over cnt = 1576(4%), over = 2270, worst = 6
PHY-1002 : len = 711952, over cnt = 683(1%), over = 928, worst = 5
PHY-1002 : len = 727112, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 727440, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.827188s wall, 3.046875s user + 0.109375s system = 3.156250s CPU (172.7%)

PHY-1001 : Congestion index: top1 = 46.72, top5 = 42.61, top10 = 40.11, top15 = 38.47.
PHY-1001 : End global routing;  2.183471s wall, 3.390625s user + 0.125000s system = 3.515625s CPU (161.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 697, reserve = 686, peak = 718.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 966, reserve = 952, peak = 966.
PHY-1001 : End build detailed router design. 4.609848s wall, 4.562500s user + 0.046875s system = 4.609375s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 192304, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.929964s wall, 0.890625s user + 0.031250s system = 0.921875s CPU (99.1%)

PHY-1001 : Current memory(MB): used = 1001, reserve = 988, peak = 1001.
PHY-1001 : End phase 1; 0.938936s wall, 0.906250s user + 0.031250s system = 0.937500s CPU (99.8%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 55% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.68302e+06, over cnt = 1195(0%), over = 1197, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1016, reserve = 1003, peak = 1016.
PHY-1001 : End initial routed; 16.162486s wall, 44.406250s user + 0.312500s system = 44.718750s CPU (276.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18043(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.266   |   0.000   |   0   
RUN-1001 :   Hold   |   0.195   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.515359s wall, 3.515625s user + 0.000000s system = 3.515625s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1029, reserve = 1017, peak = 1029.
PHY-1001 : End phase 2; 19.678016s wall, 47.921875s user + 0.312500s system = 48.234375s CPU (245.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.68302e+06, over cnt = 1195(0%), over = 1197, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.254711s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (98.2%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.66944e+06, over cnt = 332(0%), over = 333, worst = 2, crit = 0
PHY-1001 : End DR Iter 1; 0.738608s wall, 1.265625s user + 0.000000s system = 1.265625s CPU (171.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.67002e+06, over cnt = 77(0%), over = 77, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.372480s wall, 0.562500s user + 0.015625s system = 0.578125s CPU (155.2%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.67078e+06, over cnt = 14(0%), over = 14, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.267964s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (110.8%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.6712e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 4; 0.195676s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (95.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18043(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.396   |   0.000   |   0   
RUN-1001 :   Hold   |   0.195   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.563833s wall, 3.562500s user + 0.000000s system = 3.562500s CPU (100.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 286 feed throughs used by 247 nets
PHY-1001 : End commit to database; 2.212051s wall, 2.203125s user + 0.015625s system = 2.218750s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 1116, reserve = 1107, peak = 1116.
PHY-1001 : End phase 3; 8.111702s wall, 8.828125s user + 0.031250s system = 8.859375s CPU (109.2%)

PHY-1003 : Routed, final wirelength = 1.6712e+06
PHY-1001 : Current memory(MB): used = 1120, reserve = 1111, peak = 1120.
PHY-1001 : End export database. 0.061462s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.7%)

PHY-1001 : End detail routing;  33.838551s wall, 62.718750s user + 0.421875s system = 63.140625s CPU (186.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67065, tnet num: 19301, tinst num: 8127, tnode num: 91015, tedge num: 110499.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.806403s wall, 1.796875s user + 0.000000s system = 1.796875s CPU (99.5%)

RUN-1004 : used memory is 1050 MB, reserved memory is 1051 MB, peak memory is 1120 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  42.389572s wall, 72.484375s user + 0.546875s system = 73.031250s CPU (172.3%)

RUN-1004 : used memory is 1050 MB, reserved memory is 1053 MB, peak memory is 1120 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8796   out of  19600   44.88%
#reg                    12352   out of  19600   63.02%
#le                     15009
  #lut only              2657   out of  15009   17.70%
  #reg only              6213   out of  15009   41.40%
  #lut&reg               6139   out of  15009   40.90%
#dsp                        0   out of     29    0.00%
#bram                      27   out of     64   42.19%
  #bram9k                  25
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       59   out of    188   31.38%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6821
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          129
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         F2        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         D1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         B3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         C1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         A3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         A2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         B2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         B1        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F15        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDZ        INPUT        K15        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         P2        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         N4        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         M5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         N6        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         N5        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         P5        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M3        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         M4        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         H2        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         M1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         L1        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         N1        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         N3        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         M2        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT         T5        LVCMOS33           8            NONE           OREG       
    Fog_ENY        OUTPUT         T4        LVCMOS33           8            NONE           OREG       
    Fog_ENZ        OUTPUT         T7        LVCMOS33           8            NONE           OREG       
    Fog_INT        OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         T6        LVCMOS33           8            N/A            OREG       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E1        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        G11        LVCMOS25           8            N/A            NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15009  |7308    |1488    |12396   |27      |0       |
|  AnyFog_dataX                      |AnyFog          |207    |71      |22      |169     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |53      |22      |48      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |208    |77      |22      |174     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |55      |22      |52      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |207    |107     |22      |167     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |84     |61      |22      |44      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2910   |710     |39      |2835    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |63     |37      |5       |54      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |207    |57      |5       |197     |0       |0       |
|    STADOP_com2                     |STADOP          |532    |93      |0       |529     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |44      |14      |43      |0       |0       |
|    head_com2                       |uniheading      |270    |61      |5       |259     |0       |0       |
|    rmc_com2                        |Gprmc           |40     |38      |0       |35      |0       |0       |
|    uart_com2                       |Agrica          |1431   |371     |10      |1416    |0       |0       |
|  COM3                              |COM3_Control    |281    |132     |19      |241     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |63     |37      |5       |54      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |61     |41      |14      |37      |0       |0       |
|    rmc_com3                        |Gprmc           |157    |54      |0       |150     |0       |0       |
|  DATA                              |Data_Processing |8858   |4428    |1122    |7044    |0       |0       |
|    DIV_Dtemp                       |Divider         |842    |345     |84      |713     |0       |0       |
|    DIV_Utemp                       |Divider         |621    |278     |84      |498     |0       |0       |
|    DIV_accX                        |Divider         |593    |292     |84      |465     |0       |0       |
|    DIV_accY                        |Divider         |582    |344     |102     |420     |0       |0       |
|    DIV_accZ                        |Divider         |703    |396     |132     |501     |0       |0       |
|    DIV_rateX                       |Divider         |634    |351     |132     |426     |0       |0       |
|    DIV_rateY                       |Divider         |603    |337     |132     |400     |0       |0       |
|    DIV_rateZ                       |Divider         |572    |365     |132     |369     |0       |0       |
|    genclk                          |genclk          |264    |169     |89      |104     |0       |0       |
|  FMC                               |FMC_Ctrl        |488    |440     |43      |349     |0       |0       |
|  IIC                               |I2C_master      |328    |248     |11      |269     |0       |0       |
|  IMU_CTRL                          |SCHA634         |911    |694     |61      |722     |0       |0       |
|    CtrlData                        |CtrlData        |476    |422     |47      |330     |0       |0       |
|      usms                          |Time_1ms        |29     |24      |5       |21      |0       |0       |
|    SPIM                            |SPI_SCHA634     |435    |272     |14      |392     |0       |0       |
|  POWER                             |POWER_EN        |96     |49      |38      |37      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |504    |350     |89      |334     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |504    |350     |89      |334     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |211    |166     |0       |196     |0       |0       |
|        reg_inst                    |register        |208    |163     |0       |193     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |293    |184     |89      |138     |0       |0       |
|        bus_inst                    |bus_top         |83     |55      |28      |34      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |29     |19      |10      |12      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |3      |3       |0       |3       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |50     |32      |18      |18      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |124    |90      |29      |74      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13508  
    #2          2       3385   
    #3          3        633   
    #4          4        300   
    #5        5-10       972   
    #6        11-50      430   
    #7       51-100       6    
    #8       101-500      3    
    #9        >500        2    
  Average     2.12             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.231662s wall, 3.843750s user + 0.015625s system = 3.859375s CPU (172.9%)

RUN-1004 : used memory is 1051 MB, reserved memory is 1053 MB, peak memory is 1120 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67065, tnet num: 19301, tinst num: 8127, tnode num: 91015, tedge num: 110499.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.750536s wall, 1.750000s user + 0.000000s system = 1.750000s CPU (100.0%)

RUN-1004 : used memory is 1052 MB, reserved memory is 1055 MB, peak memory is 1120 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19301 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.483187s wall, 1.484375s user + 0.000000s system = 1.484375s CPU (100.1%)

RUN-1004 : used memory is 1057 MB, reserved memory is 1058 MB, peak memory is 1120 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: f93483d4eefe0fc495c71cfd33cdfdfd7fe3ec43aef27c1d437b0386330ceaf7 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8127
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19303, pip num: 144422
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 286
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3246 valid insts, and 405651 bits set as '1'.
BIT-1004 : the usercode register value: 00000000000010010010010110000101
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  11.971140s wall, 117.656250s user + 0.078125s system = 117.734375s CPU (983.5%)

RUN-1004 : used memory is 1176 MB, reserved memory is 1160 MB, peak memory is 1290 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250821_110605.log"

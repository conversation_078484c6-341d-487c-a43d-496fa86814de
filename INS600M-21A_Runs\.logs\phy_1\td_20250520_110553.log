============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Tue May 20 11:05:53 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(247)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(252)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(273)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(278)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(421)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(428)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(435)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(442)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(449)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(456)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(463)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(470)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(647)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(652)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(680)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(685)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(919)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(926)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(933)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(940)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(947)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(952)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(959)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(966)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(973)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(980)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(514)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
RUN-1001 : Project manager successfully analyzed 21 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  6.094085s wall, 1.578125s user + 4.515625s system = 6.093750s CPU (100.0%)

RUN-1004 : used memory is 78 MB, reserved memory is 39 MB, peak memory is 89 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.928897s wall, 1.921875s user + 0.015625s system = 1.937500s CPU (100.4%)

RUN-1004 : used memory is 298 MB, reserved memory is 267 MB, peak memory is 301 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 7 view nodes, 47 trigger nets, 47 data nets.
KIT-1004 : Chipwatcher code = 0001111110010110
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=144) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=144) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=144)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=144)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22832/31 useful/useless nets, 19615/17 useful/useless insts
SYN-1016 : Merged 37 instances.
SYN-1032 : 22456/22 useful/useless nets, 20074/18 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 4 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 4 mux instances.
SYN-1015 : Optimize round 1, 443 better
SYN-1014 : Optimize round 2
SYN-1032 : 22084/60 useful/useless nets, 19702/64 useful/useless insts
SYN-1015 : Optimize round 2, 128 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.805683s wall, 2.750000s user + 0.078125s system = 2.828125s CPU (100.8%)

RUN-1004 : used memory is 326 MB, reserved memory is 293 MB, peak memory is 327 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22144/367 useful/useless nets, 19803/52 useful/useless insts
SYN-1016 : Merged 57 instances.
SYN-2571 : Optimize after map_dsp, round 1, 476 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 44 instances.
SYN-2501 : Optimize round 1, 90 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 18 macro adder
SYN-1019 : Optimized 17 mux instances.
SYN-1016 : Merged 12 instances.
SYN-1032 : 22615/5 useful/useless nets, 20274/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83002, tnet num: 22615, tinst num: 20273, tnode num: 116620, tedge num: 129263.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.330282s wall, 1.312500s user + 0.015625s system = 1.328125s CPU (99.8%)

RUN-1004 : used memory is 467 MB, reserved memory is 435 MB, peak memory is 467 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22615 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 257 (3.41), #lev = 7 (1.78)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 257 (3.41), #lev = 7 (1.78)
SYN-3001 : Logic optimization runtime opt =   0.04 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 596 instances into 257 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 450 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 140 adder to BLE ...
SYN-4008 : Packed 140 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  5.152427s wall, 5.015625s user + 0.109375s system = 5.125000s CPU (99.5%)

RUN-1004 : used memory is 348 MB, reserved memory is 329 MB, peak memory is 576 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  8.337805s wall, 8.093750s user + 0.218750s system = 8.312500s CPU (99.7%)

RUN-1004 : used memory is 349 MB, reserved memory is 330 MB, peak memory is 576 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/rmc_com2/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/rmc_com2/GPRMC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/rmc_com2/GPRMC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/rmc_com2/GPRMC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/rmc_com2/GPRMC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/rmc_com2/GPRMC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/rmc_com2/GPRMC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/rmc_com2/GPRMC_data[0]
SYN-5055 WARNING: The kept net COM2/rx_err will be merged to another kept net COM2/rmc_com2/GPRMC_err
SYN-5055 WARNING: The kept net FMC/Error_state[0] will be merged to another kept net COM2/rmc_com2/GPRMC_sub_state_cnt0[3]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (306 clock/control pins, 0 other pins).
SYN-4027 : Net DATA/DIV_Dtemp/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net DATA/DIV_Dtemp/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19601 instances
RUN-0007 : 5599 luts, 12471 seqs, 933 mslices, 491 lslices, 60 pads, 42 brams, 0 dsps
RUN-1001 : There are total 21966 nets
RUN-1001 : 16481 nets have 2 pins
RUN-1001 : 4348 nets have [3 - 5] pins
RUN-1001 : 790 nets have [6 - 10] pins
RUN-1001 : 220 nets have [11 - 20] pins
RUN-1001 : 103 nets have [21 - 99] pins
RUN-1001 : 24 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4742     
RUN-1001 :   No   |  No   |  Yes  |     628     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    6531     
RUN-1001 :   Yes  |  No   |  Yes  |     470     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19599 instances, 5599 luts, 12471 seqs, 1424 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1131 pins
PHY-3001 : Huge net DATA/done_div with 1650 pins
PHY-0007 : Cell area utilization is 64%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81533, tnet num: 21964, tinst num: 19599, tnode num: 115249, tedge num: 128074.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.360936s wall, 1.343750s user + 0.015625s system = 1.359375s CPU (99.9%)

RUN-1004 : used memory is 527 MB, reserved memory is 499 MB, peak memory is 576 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21964 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.341997s wall, 2.312500s user + 0.015625s system = 2.328125s CPU (99.4%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.43126e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19599.
PHY-3001 : Level 1 #clusters 2055.
PHY-3001 : End clustering;  0.174404s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (197.1%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 64%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 853652, overlap = 639.906
PHY-3002 : Step(2): len = 765108, overlap = 704.344
PHY-3002 : Step(3): len = 496310, overlap = 909.625
PHY-3002 : Step(4): len = 437381, overlap = 986.031
PHY-3002 : Step(5): len = 355515, overlap = 1043.09
PHY-3002 : Step(6): len = 314182, overlap = 1112.41
PHY-3002 : Step(7): len = 255076, overlap = 1186.97
PHY-3002 : Step(8): len = 231597, overlap = 1236.09
PHY-3002 : Step(9): len = 202837, overlap = 1300.09
PHY-3002 : Step(10): len = 190410, overlap = 1329.28
PHY-3002 : Step(11): len = 169581, overlap = 1370.06
PHY-3002 : Step(12): len = 160016, overlap = 1429.81
PHY-3002 : Step(13): len = 145480, overlap = 1473.56
PHY-3002 : Step(14): len = 134883, overlap = 1533.09
PHY-3002 : Step(15): len = 124889, overlap = 1555.62
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.1315e-06
PHY-3002 : Step(16): len = 136417, overlap = 1486.56
PHY-3002 : Step(17): len = 192073, overlap = 1343.19
PHY-3002 : Step(18): len = 196295, overlap = 1250.09
PHY-3002 : Step(19): len = 194796, overlap = 1190.5
PHY-3002 : Step(20): len = 189254, overlap = 1160.16
PHY-3002 : Step(21): len = 186474, overlap = 1133.41
PHY-3002 : Step(22): len = 179968, overlap = 1112.56
PHY-3002 : Step(23): len = 174979, overlap = 1105.91
PHY-3002 : Step(24): len = 171940, overlap = 1102.34
PHY-3002 : Step(25): len = 170376, overlap = 1105.47
PHY-3002 : Step(26): len = 168187, overlap = 1085.41
PHY-3002 : Step(27): len = 166644, overlap = 1081.09
PHY-3002 : Step(28): len = 165669, overlap = 1077.59
PHY-3002 : Step(29): len = 165268, overlap = 1041.97
PHY-3002 : Step(30): len = 164028, overlap = 1029.09
PHY-3002 : Step(31): len = 163532, overlap = 1031.31
PHY-3002 : Step(32): len = 162557, overlap = 1039.78
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.26301e-06
PHY-3002 : Step(33): len = 170063, overlap = 1012.34
PHY-3002 : Step(34): len = 184456, overlap = 970.25
PHY-3002 : Step(35): len = 187954, overlap = 966.031
PHY-3002 : Step(36): len = 190611, overlap = 942.031
PHY-3002 : Step(37): len = 191268, overlap = 932.281
PHY-3002 : Step(38): len = 190670, overlap = 929.531
PHY-3002 : Step(39): len = 189715, overlap = 930.062
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.52601e-06
PHY-3002 : Step(40): len = 201148, overlap = 859.219
PHY-3002 : Step(41): len = 215195, overlap = 783.125
PHY-3002 : Step(42): len = 220401, overlap = 723.969
PHY-3002 : Step(43): len = 223132, overlap = 723.469
PHY-3002 : Step(44): len = 221282, overlap = 722.969
PHY-3002 : Step(45): len = 220457, overlap = 707.938
PHY-3002 : Step(46): len = 219153, overlap = 702.938
PHY-3002 : Step(47): len = 218175, overlap = 696.125
PHY-3002 : Step(48): len = 216357, overlap = 712.469
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 9.05203e-06
PHY-3002 : Step(49): len = 228670, overlap = 679.031
PHY-3002 : Step(50): len = 247232, overlap = 610.688
PHY-3002 : Step(51): len = 252652, overlap = 566.875
PHY-3002 : Step(52): len = 254014, overlap = 528.625
PHY-3002 : Step(53): len = 252162, overlap = 514.156
PHY-3002 : Step(54): len = 250840, overlap = 507.375
PHY-3002 : Step(55): len = 248678, overlap = 540.156
PHY-3002 : Step(56): len = 248185, overlap = 536.875
PHY-3002 : Step(57): len = 247842, overlap = 546.344
PHY-3002 : Step(58): len = 247557, overlap = 535.906
PHY-3002 : Step(59): len = 245450, overlap = 532
PHY-3002 : Step(60): len = 245014, overlap = 522.906
PHY-3002 : Step(61): len = 243948, overlap = 527.844
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.81041e-05
PHY-3002 : Step(62): len = 257931, overlap = 508.656
PHY-3002 : Step(63): len = 270964, overlap = 465.781
PHY-3002 : Step(64): len = 274512, overlap = 455.188
PHY-3002 : Step(65): len = 274928, overlap = 441.594
PHY-3002 : Step(66): len = 273137, overlap = 451.438
PHY-3002 : Step(67): len = 271592, overlap = 467.031
PHY-3002 : Step(68): len = 269373, overlap = 475.156
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.62081e-05
PHY-3002 : Step(69): len = 278893, overlap = 417.656
PHY-3002 : Step(70): len = 288176, overlap = 385.594
PHY-3002 : Step(71): len = 292161, overlap = 377.781
PHY-3002 : Step(72): len = 293111, overlap = 378.781
PHY-3002 : Step(73): len = 292263, overlap = 381.094
PHY-3002 : Step(74): len = 290875, overlap = 374.25
PHY-3002 : Step(75): len = 288831, overlap = 362.562
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 7.24162e-05
PHY-3002 : Step(76): len = 297843, overlap = 345.25
PHY-3002 : Step(77): len = 304576, overlap = 317.375
PHY-3002 : Step(78): len = 305492, overlap = 302.719
PHY-3002 : Step(79): len = 305605, overlap = 293.25
PHY-3002 : Step(80): len = 303926, overlap = 287.656
PHY-3002 : Step(81): len = 303348, overlap = 281.094
PHY-3002 : Step(82): len = 301677, overlap = 273.031
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000144832
PHY-3002 : Step(83): len = 307331, overlap = 290.875
PHY-3002 : Step(84): len = 313217, overlap = 298.062
PHY-3002 : Step(85): len = 316061, overlap = 292.031
PHY-3002 : Step(86): len = 321205, overlap = 279.094
PHY-3002 : Step(87): len = 320031, overlap = 262.656
PHY-3002 : Step(88): len = 318777, overlap = 258.656
PHY-3002 : Step(89): len = 316788, overlap = 247.531
PHY-3002 : Step(90): len = 315745, overlap = 245.75
PHY-3002 : Step(91): len = 315945, overlap = 240.812
PHY-3002 : Step(92): len = 316984, overlap = 243.5
PHY-3002 : Step(93): len = 314852, overlap = 242.562
PHY-3002 : Step(94): len = 315169, overlap = 244.75
PHY-3002 : Step(95): len = 314827, overlap = 242.844
PHY-3002 : Step(96): len = 315401, overlap = 242.312
PHY-3002 : Step(97): len = 314252, overlap = 255
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000254779
PHY-3002 : Step(98): len = 317084, overlap = 246.094
PHY-3002 : Step(99): len = 320682, overlap = 224.312
PHY-3002 : Step(100): len = 321219, overlap = 221.25
PHY-3002 : Step(101): len = 322084, overlap = 216.625
PHY-3002 : Step(102): len = 321874, overlap = 217.688
PHY-3002 : Step(103): len = 322070, overlap = 220.906
PHY-3002 : Step(104): len = 321081, overlap = 221.75
PHY-3002 : Step(105): len = 321227, overlap = 235.188
PHY-3002 : Step(106): len = 322167, overlap = 241.938
PHY-3002 : Step(107): len = 323092, overlap = 254.312
PHY-3002 : Step(108): len = 322771, overlap = 259.375
PHY-3002 : Step(109): len = 322775, overlap = 269.938
PHY-3002 : Step(110): len = 322721, overlap = 274.062
PHY-3002 : Step(111): len = 322308, overlap = 274.156
PHY-3002 : Step(112): len = 322787, overlap = 265.469
PHY-3002 : Step(113): len = 322379, overlap = 250.812
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(114): len = 323662, overlap = 247.688
PHY-3002 : Step(115): len = 327503, overlap = 231.844
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.015311s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (102.1%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21966.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 440528, over cnt = 1199(3%), over = 5196, worst = 32
PHY-1001 : End global iterations;  1.006109s wall, 1.343750s user + 0.187500s system = 1.531250s CPU (152.2%)

PHY-1001 : Congestion index: top1 = 72.54, top5 = 52.35, top10 = 43.12, top15 = 37.57.
PHY-3001 : End congestion estimation;  1.285168s wall, 1.625000s user + 0.187500s system = 1.812500s CPU (141.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21964 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.028532s wall, 1.000000s user + 0.031250s system = 1.031250s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.38424e-05
PHY-3002 : Step(116): len = 364617, overlap = 185.594
PHY-3002 : Step(117): len = 380515, overlap = 165.188
PHY-3002 : Step(118): len = 378029, overlap = 158.469
PHY-3002 : Step(119): len = 378526, overlap = 153.438
PHY-3002 : Step(120): len = 384161, overlap = 139.969
PHY-3002 : Step(121): len = 384842, overlap = 131.156
PHY-3002 : Step(122): len = 387609, overlap = 125.562
PHY-3002 : Step(123): len = 392614, overlap = 125.719
PHY-3002 : Step(124): len = 392674, overlap = 118.562
PHY-3002 : Step(125): len = 395357, overlap = 112.156
PHY-3002 : Step(126): len = 395597, overlap = 105.906
PHY-3002 : Step(127): len = 393973, overlap = 110.75
PHY-3002 : Step(128): len = 394883, overlap = 114.469
PHY-3002 : Step(129): len = 395432, overlap = 116
PHY-3002 : Step(130): len = 397167, overlap = 118.25
PHY-3002 : Step(131): len = 396091, overlap = 123.438
PHY-3002 : Step(132): len = 395777, overlap = 123.562
PHY-3002 : Step(133): len = 396815, overlap = 125.312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000187685
PHY-3002 : Step(134): len = 396594, overlap = 123.781
PHY-3002 : Step(135): len = 399321, overlap = 117.375
PHY-3002 : Step(136): len = 402520, overlap = 113.156
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000375369
PHY-3002 : Step(137): len = 407128, overlap = 106.125
PHY-3002 : Step(138): len = 414834, overlap = 100.75
PHY-3002 : Step(139): len = 421913, overlap = 98.4688
PHY-3002 : Step(140): len = 422072, overlap = 98.1562
PHY-3002 : Step(141): len = 424974, overlap = 96.9375
PHY-3002 : Step(142): len = 428252, overlap = 109.312
PHY-3002 : Step(143): len = 426846, overlap = 110.875
PHY-3002 : Step(144): len = 427077, overlap = 114.625
PHY-3002 : Step(145): len = 429166, overlap = 116.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000750739
PHY-3002 : Step(146): len = 428048, overlap = 119.625
PHY-3002 : Step(147): len = 431662, overlap = 118.719
PHY-3002 : Step(148): len = 435505, overlap = 121.688
PHY-3002 : Step(149): len = 447035, overlap = 117.656
PHY-3002 : Step(150): len = 450474, overlap = 112.531
PHY-3002 : Step(151): len = 452052, overlap = 111.094
PHY-3002 : Step(152): len = 454005, overlap = 111.438
PHY-3002 : Step(153): len = 454866, overlap = 116.188
PHY-3002 : Step(154): len = 454306, overlap = 116.406
PHY-3002 : Step(155): len = 455172, overlap = 126.031
PHY-3002 : Step(156): len = 453741, overlap = 128.5
PHY-3002 : Step(157): len = 453687, overlap = 129.125
PHY-3002 : Step(158): len = 453458, overlap = 127.844
PHY-3002 : Step(159): len = 451608, overlap = 123.594
PHY-3002 : Step(160): len = 450982, overlap = 120.438
PHY-3002 : Step(161): len = 450674, overlap = 121.5
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(162): len = 450909, overlap = 124.5
PHY-3002 : Step(163): len = 452682, overlap = 123.375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 59/21966.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 527064, over cnt = 2126(6%), over = 10015, worst = 46
PHY-1001 : End global iterations;  1.215512s wall, 1.968750s user + 0.015625s system = 1.984375s CPU (163.3%)

PHY-1001 : Congestion index: top1 = 83.47, top5 = 61.88, top10 = 51.79, top15 = 45.98.
PHY-3001 : End congestion estimation;  1.531254s wall, 2.281250s user + 0.015625s system = 2.296875s CPU (150.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21964 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.260584s wall, 1.250000s user + 0.015625s system = 1.265625s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.91709e-05
PHY-3002 : Step(164): len = 460276, overlap = 361.688
PHY-3002 : Step(165): len = 467325, overlap = 305.438
PHY-3002 : Step(166): len = 454228, overlap = 300.5
PHY-3002 : Step(167): len = 449550, overlap = 283.844
PHY-3002 : Step(168): len = 450953, overlap = 257.562
PHY-3002 : Step(169): len = 447485, overlap = 243.031
PHY-3002 : Step(170): len = 444744, overlap = 232.938
PHY-3002 : Step(171): len = 443471, overlap = 216.875
PHY-3002 : Step(172): len = 441148, overlap = 219.156
PHY-3002 : Step(173): len = 439960, overlap = 215.5
PHY-3002 : Step(174): len = 439184, overlap = 221.531
PHY-3002 : Step(175): len = 438846, overlap = 228.938
PHY-3002 : Step(176): len = 436952, overlap = 226.344
PHY-3002 : Step(177): len = 435109, overlap = 220.656
PHY-3002 : Step(178): len = 434292, overlap = 217.469
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000198342
PHY-3002 : Step(179): len = 433130, overlap = 215.5
PHY-3002 : Step(180): len = 434468, overlap = 208.156
PHY-3002 : Step(181): len = 435630, overlap = 201.781
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000396684
PHY-3002 : Step(182): len = 439858, overlap = 184.844
PHY-3002 : Step(183): len = 446479, overlap = 172.125
PHY-3002 : Step(184): len = 449605, overlap = 168.188
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000793367
PHY-3002 : Step(185): len = 449826, overlap = 168.656
PHY-3002 : Step(186): len = 453032, overlap = 160.375
PHY-3002 : Step(187): len = 456308, overlap = 153.031
PHY-3002 : Step(188): len = 458925, overlap = 152.344
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.0014614
PHY-3002 : Step(189): len = 459521, overlap = 151.562
PHY-3002 : Step(190): len = 461856, overlap = 151
PHY-3002 : Step(191): len = 463958, overlap = 146.875
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81533, tnet num: 21964, tinst num: 19599, tnode num: 115249, tedge num: 128074.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.672999s wall, 1.593750s user + 0.078125s system = 1.671875s CPU (99.9%)

RUN-1004 : used memory is 569 MB, reserved memory is 544 MB, peak memory is 703 MB
OPT-1001 : Total overflow 506.00 peak overflow 4.28
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 422/21966.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 550328, over cnt = 2548(7%), over = 8738, worst = 23
PHY-1001 : End global iterations;  1.449021s wall, 2.203125s user + 0.093750s system = 2.296875s CPU (158.5%)

PHY-1001 : Congestion index: top1 = 57.22, top5 = 47.47, top10 = 42.48, top15 = 39.42.
PHY-1001 : End incremental global routing;  1.726316s wall, 2.468750s user + 0.093750s system = 2.562500s CPU (148.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21964 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.113621s wall, 1.078125s user + 0.031250s system = 1.109375s CPU (99.6%)

OPT-1001 : 21 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19515 has valid locations, 282 needs to be replaced
PHY-3001 : design contains 19860 instances, 5723 luts, 12608 seqs, 1424 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 482554
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17294/22227.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 565192, over cnt = 2570(7%), over = 8798, worst = 23
PHY-1001 : End global iterations;  0.252662s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (105.1%)

PHY-1001 : Congestion index: top1 = 57.35, top5 = 47.73, top10 = 42.76, top15 = 39.70.
PHY-3001 : End congestion estimation;  0.584788s wall, 0.562500s user + 0.015625s system = 0.578125s CPU (98.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82433, tnet num: 22225, tinst num: 19860, tnode num: 116476, tedge num: 129352.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.892113s wall, 1.843750s user + 0.046875s system = 1.890625s CPU (99.9%)

RUN-1004 : used memory is 615 MB, reserved memory is 612 MB, peak memory is 706 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22225 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.149882s wall, 3.093750s user + 0.062500s system = 3.156250s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(192): len = 482072, overlap = 1.9375
PHY-3002 : Step(193): len = 483127, overlap = 1.8125
PHY-3002 : Step(194): len = 484342, overlap = 1.4375
PHY-3002 : Step(195): len = 485403, overlap = 1.4375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17323/22227.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 564224, over cnt = 2600(7%), over = 8915, worst = 23
PHY-1001 : End global iterations;  0.218192s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (136.1%)

PHY-1001 : Congestion index: top1 = 58.30, top5 = 48.19, top10 = 43.09, top15 = 39.93.
PHY-3001 : End congestion estimation;  0.552138s wall, 0.625000s user + 0.000000s system = 0.625000s CPU (113.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22225 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.125700s wall, 1.125000s user + 0.000000s system = 1.125000s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00105979
PHY-3002 : Step(196): len = 485440, overlap = 149.688
PHY-3002 : Step(197): len = 485626, overlap = 149.375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00211958
PHY-3002 : Step(198): len = 485924, overlap = 149.688
PHY-3002 : Step(199): len = 486106, overlap = 149.5
PHY-3001 : Final: Len = 486106, Over = 149.5
PHY-3001 : End incremental placement;  6.613429s wall, 6.750000s user + 0.265625s system = 7.015625s CPU (106.1%)

OPT-1001 : Total overflow 511.84 peak overflow 4.28
OPT-1001 : End high-fanout net optimization;  10.068991s wall, 11.078125s user + 0.406250s system = 11.484375s CPU (114.1%)

OPT-1001 : Current memory(MB): used = 710, reserve = 691, peak = 726.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17359/22227.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 566040, over cnt = 2574(7%), over = 8367, worst = 22
PHY-1002 : len = 603344, over cnt = 1833(5%), over = 4727, worst = 22
PHY-1002 : len = 646392, over cnt = 646(1%), over = 1476, worst = 20
PHY-1002 : len = 666400, over cnt = 94(0%), over = 188, worst = 10
PHY-1002 : len = 669696, over cnt = 1(0%), over = 1, worst = 1
PHY-1001 : End global iterations;  1.484825s wall, 2.140625s user + 0.000000s system = 2.140625s CPU (144.2%)

PHY-1001 : Congestion index: top1 = 49.22, top5 = 43.16, top10 = 40.09, top15 = 38.05.
OPT-1001 : End congestion update;  1.771540s wall, 2.421875s user + 0.000000s system = 2.421875s CPU (136.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22225 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.984374s wall, 0.984375s user + 0.000000s system = 0.984375s CPU (100.0%)

OPT-0007 : Start: WNS 4119 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.762764s wall, 3.421875s user + 0.000000s system = 3.421875s CPU (123.9%)

OPT-1001 : Current memory(MB): used = 707, reserve = 688, peak = 726.
OPT-1001 : End physical optimization;  14.847418s wall, 16.515625s user + 0.484375s system = 17.000000s CPU (114.5%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5723 LUT to BLE ...
SYN-4008 : Packed 5723 LUT and 2860 SEQ to BLE.
SYN-4003 : Packing 9748 remaining SEQ's ...
SYN-4005 : Packed 3246 SEQ with LUT/SLICE
SYN-4006 : 124 single LUT's are left
SYN-4006 : 6502 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 12225/13876 primitive instances ...
PHY-3001 : End packing;  3.208593s wall, 3.203125s user + 0.000000s system = 3.203125s CPU (99.8%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8208 instances
RUN-1001 : 4050 mslices, 4051 lslices, 60 pads, 42 brams, 0 dsps
RUN-1001 : There are total 19410 nets
RUN-1001 : 13538 nets have 2 pins
RUN-1001 : 4494 nets have [3 - 5] pins
RUN-1001 : 857 nets have [6 - 10] pins
RUN-1001 : 359 nets have [11 - 20] pins
RUN-1001 : 152 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
PHY-3001 : design contains 8206 instances, 8101 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Cell area utilization is 86%
PHY-3001 : After packing: Len = 502496, Over = 389.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7960/19410.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 634712, over cnt = 1532(4%), over = 2364, worst = 6
PHY-1002 : len = 641600, over cnt = 964(2%), over = 1296, worst = 6
PHY-1002 : len = 649488, over cnt = 470(1%), over = 613, worst = 6
PHY-1002 : len = 655496, over cnt = 179(0%), over = 246, worst = 5
PHY-1002 : len = 660000, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.289624s wall, 2.031250s user + 0.031250s system = 2.062500s CPU (159.9%)

PHY-1001 : Congestion index: top1 = 49.35, top5 = 43.20, top10 = 39.95, top15 = 37.71.
PHY-3001 : End congestion estimation;  1.672854s wall, 2.406250s user + 0.031250s system = 2.437500s CPU (145.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67965, tnet num: 19408, tinst num: 8206, tnode num: 92713, tedge num: 111951.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.951887s wall, 1.921875s user + 0.031250s system = 1.953125s CPU (100.1%)

RUN-1004 : used memory is 601 MB, reserved memory is 588 MB, peak memory is 726 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19408 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.961991s wall, 2.937500s user + 0.031250s system = 2.968750s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.58091e-05
PHY-3002 : Step(200): len = 504357, overlap = 364.25
PHY-3002 : Step(201): len = 503330, overlap = 370
PHY-3002 : Step(202): len = 501711, overlap = 385.75
PHY-3002 : Step(203): len = 500754, overlap = 385
PHY-3002 : Step(204): len = 498725, overlap = 389.5
PHY-3002 : Step(205): len = 498560, overlap = 396.75
PHY-3002 : Step(206): len = 495974, overlap = 406.5
PHY-3002 : Step(207): len = 494646, overlap = 409.75
PHY-3002 : Step(208): len = 491961, overlap = 407.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.16181e-05
PHY-3002 : Step(209): len = 495685, overlap = 400.5
PHY-3002 : Step(210): len = 498007, overlap = 395.5
PHY-3002 : Step(211): len = 497704, overlap = 393.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(212): len = 509047, overlap = 371.25
PHY-3002 : Step(213): len = 514827, overlap = 353
PHY-3002 : Step(214): len = 511326, overlap = 354.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.723995s wall, 0.703125s user + 0.890625s system = 1.593750s CPU (220.1%)

PHY-3001 : Trial Legalized: Len = 631056
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 480/19410.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 723584, over cnt = 2448(6%), over = 3923, worst = 8
PHY-1002 : len = 737664, over cnt = 1493(4%), over = 2061, worst = 7
PHY-1002 : len = 753312, over cnt = 683(1%), over = 874, worst = 7
PHY-1002 : len = 759832, over cnt = 363(1%), over = 459, worst = 7
PHY-1002 : len = 768336, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.056634s wall, 3.515625s user + 0.078125s system = 3.593750s CPU (174.7%)

PHY-1001 : Congestion index: top1 = 50.37, top5 = 45.46, top10 = 42.60, top15 = 40.82.
PHY-3001 : End congestion estimation;  2.447745s wall, 3.890625s user + 0.093750s system = 3.984375s CPU (162.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19408 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.969418s wall, 0.953125s user + 0.015625s system = 0.968750s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000177744
PHY-3002 : Step(215): len = 584122, overlap = 88.25
PHY-3002 : Step(216): len = 564886, overlap = 135.25
PHY-3002 : Step(217): len = 552994, overlap = 175.75
PHY-3002 : Step(218): len = 544753, overlap = 210
PHY-3002 : Step(219): len = 540385, overlap = 232.5
PHY-3002 : Step(220): len = 537441, overlap = 246.25
PHY-3002 : Step(221): len = 535949, overlap = 261.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000355488
PHY-3002 : Step(222): len = 540138, overlap = 253.5
PHY-3002 : Step(223): len = 544044, overlap = 256
PHY-3002 : Step(224): len = 543816, overlap = 264
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(225): len = 546941, overlap = 258.5
PHY-3002 : Step(226): len = 553248, overlap = 251.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.038052s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (82.1%)

PHY-3001 : Legalized: Len = 593302, Over = 0
PHY-3001 : Spreading special nets. 46 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.088178s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (106.3%)

PHY-3001 : 63 instances has been re-located, deltaX = 36, deltaY = 27, maxDist = 2.
PHY-3001 : Final: Len = 594272, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67965, tnet num: 19408, tinst num: 8206, tnode num: 92713, tedge num: 111951.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.062040s wall, 2.046875s user + 0.015625s system = 2.062500s CPU (100.0%)

RUN-1004 : used memory is 597 MB, reserved memory is 576 MB, peak memory is 726 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3512/19410.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 695656, over cnt = 2249(6%), over = 3467, worst = 7
PHY-1002 : len = 708328, over cnt = 1287(3%), over = 1714, worst = 6
PHY-1002 : len = 722784, over cnt = 453(1%), over = 565, worst = 4
PHY-1002 : len = 729264, over cnt = 143(0%), over = 170, worst = 3
PHY-1002 : len = 732248, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.713860s wall, 2.890625s user + 0.046875s system = 2.937500s CPU (171.4%)

PHY-1001 : Congestion index: top1 = 48.36, top5 = 43.19, top10 = 40.44, top15 = 38.74.
PHY-1001 : End incremental global routing;  2.043491s wall, 3.218750s user + 0.046875s system = 3.265625s CPU (159.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19408 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.997525s wall, 0.984375s user + 0.015625s system = 1.000000s CPU (100.2%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8142 has valid locations, 7 needs to be replaced
PHY-3001 : design contains 8212 instances, 8107 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 595532
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17501/19415.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 733384, over cnt = 13(0%), over = 13, worst = 1
PHY-1002 : len = 733352, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 733352, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 733448, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.544782s wall, 0.578125s user + 0.015625s system = 0.593750s CPU (109.0%)

PHY-1001 : Congestion index: top1 = 48.49, top5 = 43.35, top10 = 40.50, top15 = 38.76.
PHY-3001 : End congestion estimation;  0.867361s wall, 0.875000s user + 0.031250s system = 0.906250s CPU (104.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68000, tnet num: 19413, tinst num: 8212, tnode num: 92754, tedge num: 111991.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.071684s wall, 2.062500s user + 0.015625s system = 2.078125s CPU (100.3%)

RUN-1004 : used memory is 642 MB, reserved memory is 627 MB, peak memory is 726 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19413 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.110373s wall, 3.093750s user + 0.015625s system = 3.109375s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(227): len = 595488, overlap = 0.5
PHY-3002 : Step(228): len = 595475, overlap = 0.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17498/19415.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 733184, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 733160, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 733176, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 733192, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.540828s wall, 0.562500s user + 0.000000s system = 0.562500s CPU (104.0%)

PHY-1001 : Congestion index: top1 = 48.45, top5 = 43.34, top10 = 40.52, top15 = 38.80.
PHY-3001 : End congestion estimation;  1.065871s wall, 1.078125s user + 0.000000s system = 1.078125s CPU (101.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19413 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.034380s wall, 1.046875s user + 0.000000s system = 1.046875s CPU (101.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000694814
PHY-3002 : Step(229): len = 595486, overlap = 1
PHY-3002 : Step(230): len = 595490, overlap = 1.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006490s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (240.7%)

PHY-3001 : Legalized: Len = 595496, Over = 0
PHY-3001 : End spreading;  0.073139s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (106.8%)

PHY-3001 : Final: Len = 595496, Over = 0
PHY-3001 : End incremental placement;  6.716115s wall, 6.890625s user + 0.140625s system = 7.031250s CPU (104.7%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.277415s wall, 11.609375s user + 0.203125s system = 11.812500s CPU (114.9%)

OPT-1001 : Current memory(MB): used = 717, reserve = 703, peak = 726.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17498/19415.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 733112, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 733024, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 733072, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 733104, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.542129s wall, 0.578125s user + 0.015625s system = 0.593750s CPU (109.5%)

PHY-1001 : Congestion index: top1 = 48.47, top5 = 43.30, top10 = 40.48, top15 = 38.76.
OPT-1001 : End congestion update;  0.875875s wall, 0.906250s user + 0.015625s system = 0.921875s CPU (105.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19413 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.939129s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (99.8%)

OPT-0007 : Start: WNS 4287 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.820043s wall, 1.859375s user + 0.015625s system = 1.875000s CPU (103.0%)

OPT-1001 : Current memory(MB): used = 716, reserve = 701, peak = 726.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19413 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.937101s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (100.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17507/19415.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 733104, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.124168s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (100.7%)

PHY-1001 : Congestion index: top1 = 48.47, top5 = 43.30, top10 = 40.48, top15 = 38.76.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19413 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.044449s wall, 1.046875s user + 0.000000s system = 1.046875s CPU (100.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4287 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 48.034483
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4287ps with logic level 4 
OPT-1001 : End physical optimization;  16.878681s wall, 18.218750s user + 0.234375s system = 18.453125s CPU (109.3%)

RUN-1003 : finish command "place" in  77.481178s wall, 138.078125s user + 9.265625s system = 147.343750s CPU (190.2%)

RUN-1004 : used memory is 596 MB, reserved memory is 588 MB, peak memory is 726 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.824060s wall, 3.093750s user + 0.015625s system = 3.109375s CPU (170.5%)

RUN-1004 : used memory is 597 MB, reserved memory is 589 MB, peak memory is 726 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8214 instances
RUN-1001 : 4050 mslices, 4057 lslices, 60 pads, 42 brams, 0 dsps
RUN-1001 : There are total 19415 nets
RUN-1001 : 13538 nets have 2 pins
RUN-1001 : 4492 nets have [3 - 5] pins
RUN-1001 : 860 nets have [6 - 10] pins
RUN-1001 : 363 nets have [11 - 20] pins
RUN-1001 : 152 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68000, tnet num: 19413, tinst num: 8212, tnode num: 92754, tedge num: 111991.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.888876s wall, 1.875000s user + 0.015625s system = 1.890625s CPU (100.1%)

RUN-1004 : used memory is 590 MB, reserved memory is 575 MB, peak memory is 726 MB
PHY-1001 : 4050 mslices, 4057 lslices, 60 pads, 42 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19413 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 676936, over cnt = 2351(6%), over = 3856, worst = 8
PHY-1002 : len = 690496, over cnt = 1557(4%), over = 2252, worst = 6
PHY-1002 : len = 711192, over cnt = 520(1%), over = 708, worst = 6
PHY-1002 : len = 722256, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 722464, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.859120s wall, 3.359375s user + 0.062500s system = 3.421875s CPU (184.1%)

PHY-1001 : Congestion index: top1 = 48.04, top5 = 43.02, top10 = 40.31, top15 = 38.62.
PHY-1001 : End global routing;  2.224095s wall, 3.718750s user + 0.062500s system = 3.781250s CPU (170.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 699, reserve = 690, peak = 726.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net DATA/DIV_Dtemp/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 970, reserve = 961, peak = 970.
PHY-1001 : End build detailed router design. 4.979788s wall, 4.937500s user + 0.031250s system = 4.968750s CPU (99.8%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 187832, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.021500s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (101.0%)

PHY-1001 : Current memory(MB): used = 1006, reserve = 999, peak = 1006.
PHY-1001 : End phase 1; 1.032998s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (99.8%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.75809e+06, over cnt = 1295(0%), over = 1301, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1025, reserve = 1017, peak = 1025.
PHY-1001 : End initial routed; 20.793649s wall, 51.343750s user + 0.484375s system = 51.828125s CPU (249.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18211(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.271   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.786167s wall, 3.750000s user + 0.031250s system = 3.781250s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 1028, reserve = 1021, peak = 1028.
PHY-1001 : End phase 2; 24.579989s wall, 55.093750s user + 0.515625s system = 55.609375s CPU (226.2%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.75809e+06, over cnt = 1295(0%), over = 1301, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.261128s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (101.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.74543e+06, over cnt = 505(0%), over = 505, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 1.173526s wall, 1.578125s user + 0.000000s system = 1.578125s CPU (134.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.74506e+06, over cnt = 102(0%), over = 102, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.786426s wall, 1.109375s user + 0.000000s system = 1.109375s CPU (141.1%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.74631e+06, over cnt = 9(0%), over = 9, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.265662s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (105.9%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.74654e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 4; 0.200464s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (93.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18211(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.863   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 4.262029s wall, 4.265625s user + 0.000000s system = 4.265625s CPU (100.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 330 feed throughs used by 288 nets
PHY-1001 : End commit to database; 2.581815s wall, 2.562500s user + 0.015625s system = 2.578125s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 1119, reserve = 1116, peak = 1119.
PHY-1001 : End phase 3; 10.052072s wall, 10.765625s user + 0.015625s system = 10.781250s CPU (107.3%)

PHY-1003 : Routed, final wirelength = 1.74654e+06
PHY-1001 : Current memory(MB): used = 1123, reserve = 1120, peak = 1123.
PHY-1001 : End export database. 0.077266s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (101.1%)

PHY-1001 : End detail routing;  41.189452s wall, 72.390625s user + 0.562500s system = 72.953125s CPU (177.1%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68000, tnet num: 19413, tinst num: 8212, tnode num: 92754, tedge num: 111991.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.882043s wall, 1.875000s user + 0.000000s system = 1.875000s CPU (99.6%)

RUN-1004 : used memory is 1005 MB, reserved memory is 1019 MB, peak memory is 1123 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  50.065139s wall, 82.671875s user + 0.718750s system = 83.390625s CPU (166.6%)

RUN-1004 : used memory is 1026 MB, reserved memory is 1044 MB, peak memory is 1123 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8709   out of  19600   44.43%
#reg                    12705   out of  19600   64.82%
#le                     15178
  #lut only              2473   out of  15178   16.29%
  #reg only              6469   out of  15178   42.62%
  #lut&reg               6236   out of  15178   41.09%
#dsp                        0   out of     29    0.00%
#bram                      42   out of     64   65.62%
  #bram9k                  42
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                     9
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet              Type               DriverType         Driver                    Fanout
#1        DATA/DIV_Dtemp/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6942
#2        config_inst_syn_9     GCLK               config             config_inst.jtck          175
#3        clk_in_dup_1          GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         M3        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          NONE       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D14        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         P6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT        D16        LVCMOS33           8            NONE           NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        R15        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15178  |7285    |1424    |12747   |42      |0       |
|  AnyFog_dataX                      |AnyFog          |205    |79      |22      |169     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |83     |52      |22      |47      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |207    |71      |22      |172     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |83     |50      |22      |48      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |203    |99      |22      |168     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |83     |57      |22      |48      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |3489   |906     |34      |3406    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |728    |67      |5       |717     |0       |0       |
|    STADOP_com2                     |STADOP          |555    |42      |0       |550     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |43      |14      |41      |0       |0       |
|    head_com2                       |uniheading      |283    |78      |5       |268     |0       |0       |
|    rmc_com2                        |Gprmc           |150    |34      |0       |145     |0       |0       |
|    uart_com2                       |Agrica          |1419   |353     |10      |1396    |0       |0       |
|  DATA                              |Data_Processing |8600   |4277    |1062    |6932    |0       |0       |
|    DIV_Dtemp                       |Divider         |806    |289     |84      |685     |0       |0       |
|    DIV_Utemp                       |Divider         |627    |286     |84      |502     |0       |0       |
|    DIV_accX                        |Divider         |560    |317     |84      |438     |0       |0       |
|    DIV_accY                        |Divider         |707    |383     |111     |535     |0       |0       |
|    DIV_accZ                        |Divider         |690    |351     |132     |485     |0       |0       |
|    DIV_rateX                       |Divider         |646    |368     |132     |440     |0       |0       |
|    DIV_rateY                       |Divider         |573    |337     |132     |371     |0       |0       |
|    DIV_rateZ                       |Divider         |548    |394     |132     |343     |0       |0       |
|    genclk                          |genclk          |84     |52      |20      |51      |0       |0       |
|  FMC                               |FMC_Ctrl        |482    |427     |43      |367     |0       |0       |
|  IIC                               |I2C_master      |293    |261     |11      |245     |0       |0       |
|  IMU_CTRL                          |SCHA634         |908    |692     |61      |723     |0       |0       |
|    CtrlData                        |CtrlData        |459    |406     |47      |330     |0       |0       |
|      usms                          |Time_1ms        |29     |23      |5       |16      |0       |0       |
|    SPIM                            |SPI_SCHA634     |449    |286     |14      |393     |0       |0       |
|  POWER                             |POWER_EN        |97     |53      |38      |36      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |678    |420     |109     |471     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |678    |420     |109     |471     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |309    |171     |0       |292     |0       |0       |
|        reg_inst                    |register        |306    |168     |0       |289     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |369    |249     |109     |179     |0       |0       |
|        bus_inst                    |bus_top         |150    |98      |52      |59      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |26     |16      |10      |9       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |17     |11      |6       |8       |0       |0       |
|          BUS_DETECTOR[5]$bus_nodes |bus_det         |53     |35      |18      |20      |0       |0       |
|          BUS_DETECTOR[6]$bus_nodes |bus_det         |52     |34      |18      |20      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |139    |99      |29      |89      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13477  
    #2          2       3558   
    #3          3        672   
    #4          4        262   
    #5        5-10       914   
    #6        11-50      446   
    #7       51-100      15    
    #8       101-500      4    
    #9        >500        2    
  Average     2.14             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.304609s wall, 3.937500s user + 0.031250s system = 3.968750s CPU (172.2%)

RUN-1004 : used memory is 1027 MB, reserved memory is 1044 MB, peak memory is 1123 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68000, tnet num: 19413, tinst num: 8212, tnode num: 92754, tedge num: 111991.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  2.035895s wall, 2.015625s user + 0.015625s system = 2.031250s CPU (99.8%)

RUN-1004 : used memory is 1029 MB, reserved memory is 1046 MB, peak memory is 1123 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19413 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.538058s wall, 1.531250s user + 0.015625s system = 1.546875s CPU (100.6%)

RUN-1004 : used memory is 1056 MB, reserved memory is 1060 MB, peak memory is 1123 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 8f169df8acfcb835781910b1ac29267cd80e16d8a5e1b8cf9d032ec93c0a2865 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8212
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19415, pip num: 148264
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 330
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3256 valid insts, and 413522 bits set as '1'.
BIT-1004 : the usercode register value: 00000000110101000001111110010110
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.662339s wall, 127.437500s user + 0.234375s system = 127.671875s CPU (1008.3%)

RUN-1004 : used memory is 1192 MB, reserved memory is 1179 MB, peak memory is 1307 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250520_110553.log"

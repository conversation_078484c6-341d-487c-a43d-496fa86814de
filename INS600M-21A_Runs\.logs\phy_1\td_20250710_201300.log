============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Jul 10 20:13:00 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(520)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.175585s wall, 1.546875s user + 3.640625s system = 5.187500s CPU (100.2%)

RUN-1004 : used memory is 80 MB, reserved memory is 40 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.677261s wall, 1.593750s user + 0.093750s system = 1.687500s CPU (100.6%)

RUN-1004 : used memory is 299 MB, reserved memory is 267 MB, peak memory is 302 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 10 view nodes, 47 trigger nets, 47 data nets.
KIT-1004 : Chipwatcher code = 0101101001110001
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=10,BUS_DIN_NUM=47,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb0100101,32'sb0101101,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01100110,32'sb01111010,32'sb010000000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=156) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=156) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=10,BUS_DIN_NUM=47,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb0100101,32'sb0101101,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01100110,32'sb01111010,32'sb010000000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=10,BUS_DIN_NUM=47,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb0100101,32'sb0101101,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01100110,32'sb01111010,32'sb010000000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=10,BUS_DIN_NUM=47,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb0100101,32'sb0101101,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01100110,32'sb01111010,32'sb010000000})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=156)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=156)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=10,BUS_DIN_NUM=47,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb0100101,32'sb0101101,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01100110,32'sb01111010,32'sb010000000})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=10,BUS_DIN_NUM=47,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb0100101,32'sb0101101,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01100110,32'sb01111010,32'sb010000000})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22588/42 useful/useless nets, 19289/25 useful/useless insts
SYN-1016 : Merged 49 instances.
SYN-1032 : 22176/26 useful/useless nets, 19760/22 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 5 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 5 mux instances.
SYN-1015 : Optimize round 1, 472 better
SYN-1014 : Optimize round 2
SYN-1032 : 21765/75 useful/useless nets, 19349/80 useful/useless insts
SYN-1015 : Optimize round 2, 160 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.192225s wall, 2.156250s user + 0.031250s system = 2.187500s CPU (99.8%)

RUN-1004 : used memory is 325 MB, reserved memory is 293 MB, peak memory is 328 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 21825/367 useful/useless nets, 19450/52 useful/useless insts
SYN-1016 : Merged 57 instances.
SYN-2571 : Optimize after map_dsp, round 1, 476 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 43 instances.
SYN-2501 : Optimize round 1, 87 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 20 macro adder
SYN-1019 : Optimized 21 mux instances.
SYN-1016 : Merged 17 instances.
SYN-1032 : 22302/5 useful/useless nets, 19927/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81629, tnet num: 22302, tinst num: 19926, tnode num: 114416, tedge num: 127313.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.123631s wall, 1.046875s user + 0.078125s system = 1.125000s CPU (100.1%)

RUN-1004 : used memory is 465 MB, reserved memory is 433 MB, peak memory is 465 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22302 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 265 (3.48), #lev = 7 (1.86)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 265 (3.48), #lev = 7 (1.86)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 656 instances into 265 LUTs, name keeping = 71%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 477 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 132 adder to BLE ...
SYN-4008 : Packed 132 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.138956s wall, 3.984375s user + 0.156250s system = 4.140625s CPU (100.0%)

RUN-1004 : used memory is 362 MB, reserved memory is 339 MB, peak memory is 572 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  6.623865s wall, 6.437500s user + 0.187500s system = 6.625000s CPU (100.0%)

RUN-1004 : used memory is 362 MB, reserved memory is 339 MB, peak memory is 572 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/uart_com2/AGRIC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/uart_com2/AGRIC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/uart_com2/AGRIC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/uart_com2/AGRIC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/uart_com2/AGRIC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/uart_com2/AGRIC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/uart_com2/AGRIC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/uart_com2/AGRIC_data[0]
SYN-5055 WARNING: The kept net COM3/rmc_com3/GPRMC_end will be merged to another kept net COM3/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM3/rmc_com3/GPRMC_sta will be merged to another kept net COM3/GNRMC/GPRMC_sta
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (328 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19152 instances
RUN-0007 : 5456 luts, 12149 seqs, 943 mslices, 496 lslices, 60 pads, 43 brams, 0 dsps
RUN-1001 : There are total 21551 nets
RUN-1001 : 16159 nets have 2 pins
RUN-1001 : 4228 nets have [3 - 5] pins
RUN-1001 : 794 nets have [6 - 10] pins
RUN-1001 : 246 nets have [11 - 20] pins
RUN-1001 : 102 nets have [21 - 99] pins
RUN-1001 : 22 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4756     
RUN-1001 :   No   |  No   |  Yes  |     667     
RUN-1001 :   No   |  Yes  |  No   |     92      
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     500     
RUN-1001 :   Yes  |  Yes  |  No   |     51      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  113  |     12     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 121
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19150 instances, 5456 luts, 12149 seqs, 1439 slices, 286 macros(1439 instances: 943 mslices 496 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 79925, tnet num: 21549, tinst num: 19150, tnode num: 112729, tedge num: 125777.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.116592s wall, 1.109375s user + 0.000000s system = 1.109375s CPU (99.4%)

RUN-1004 : used memory is 525 MB, reserved memory is 497 MB, peak memory is 572 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21549 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.992497s wall, 1.968750s user + 0.015625s system = 1.984375s CPU (99.6%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.70592e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19150.
PHY-3001 : Level 1 #clusters 2109.
PHY-3001 : End clustering;  0.127465s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (110.3%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 861750, overlap = 618.031
PHY-3002 : Step(2): len = 778929, overlap = 713.375
PHY-3002 : Step(3): len = 508560, overlap = 864.75
PHY-3002 : Step(4): len = 441319, overlap = 939.406
PHY-3002 : Step(5): len = 358716, overlap = 1002.62
PHY-3002 : Step(6): len = 322148, overlap = 1079.56
PHY-3002 : Step(7): len = 264616, overlap = 1138.19
PHY-3002 : Step(8): len = 235779, overlap = 1167.69
PHY-3002 : Step(9): len = 210785, overlap = 1204.06
PHY-3002 : Step(10): len = 196011, overlap = 1252.72
PHY-3002 : Step(11): len = 181370, overlap = 1306.72
PHY-3002 : Step(12): len = 164790, overlap = 1340.94
PHY-3002 : Step(13): len = 149730, overlap = 1367.12
PHY-3002 : Step(14): len = 137662, overlap = 1393.38
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.07232e-06
PHY-3002 : Step(15): len = 143348, overlap = 1372.59
PHY-3002 : Step(16): len = 174467, overlap = 1278.94
PHY-3002 : Step(17): len = 186825, overlap = 1199.12
PHY-3002 : Step(18): len = 192537, overlap = 1129.06
PHY-3002 : Step(19): len = 185071, overlap = 1093.56
PHY-3002 : Step(20): len = 179588, overlap = 1100.91
PHY-3002 : Step(21): len = 174283, overlap = 1098.88
PHY-3002 : Step(22): len = 169531, overlap = 1104.81
PHY-3002 : Step(23): len = 166011, overlap = 1105.34
PHY-3002 : Step(24): len = 163016, overlap = 1101.62
PHY-3002 : Step(25): len = 161061, overlap = 1112.16
PHY-3002 : Step(26): len = 159869, overlap = 1106
PHY-3002 : Step(27): len = 158470, overlap = 1101.97
PHY-3002 : Step(28): len = 157770, overlap = 1099.28
PHY-3002 : Step(29): len = 157696, overlap = 1091.72
PHY-3002 : Step(30): len = 157555, overlap = 1081.22
PHY-3002 : Step(31): len = 156189, overlap = 1071.03
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.14463e-06
PHY-3002 : Step(32): len = 162340, overlap = 1041.16
PHY-3002 : Step(33): len = 176287, overlap = 951.188
PHY-3002 : Step(34): len = 178466, overlap = 900.406
PHY-3002 : Step(35): len = 181196, overlap = 874.406
PHY-3002 : Step(36): len = 181687, overlap = 870.156
PHY-3002 : Step(37): len = 180802, overlap = 851.062
PHY-3002 : Step(38): len = 179289, overlap = 854.125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.28927e-06
PHY-3002 : Step(39): len = 189828, overlap = 832.781
PHY-3002 : Step(40): len = 205211, overlap = 773.5
PHY-3002 : Step(41): len = 210722, overlap = 714.562
PHY-3002 : Step(42): len = 213735, overlap = 686.438
PHY-3002 : Step(43): len = 213669, overlap = 673.219
PHY-3002 : Step(44): len = 213079, overlap = 664.688
PHY-3002 : Step(45): len = 211092, overlap = 670.406
PHY-3002 : Step(46): len = 210480, overlap = 678.938
PHY-3002 : Step(47): len = 209562, overlap = 683.125
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 8.57854e-06
PHY-3002 : Step(48): len = 221281, overlap = 660.375
PHY-3002 : Step(49): len = 234267, overlap = 617.875
PHY-3002 : Step(50): len = 237303, overlap = 601.156
PHY-3002 : Step(51): len = 240106, overlap = 574.625
PHY-3002 : Step(52): len = 240142, overlap = 561.031
PHY-3002 : Step(53): len = 238490, overlap = 548.656
PHY-3002 : Step(54): len = 236951, overlap = 543.531
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.71571e-05
PHY-3002 : Step(55): len = 250338, overlap = 547.812
PHY-3002 : Step(56): len = 262833, overlap = 513.219
PHY-3002 : Step(57): len = 266762, overlap = 495.688
PHY-3002 : Step(58): len = 268340, overlap = 470.781
PHY-3002 : Step(59): len = 267253, overlap = 468.438
PHY-3002 : Step(60): len = 265965, overlap = 457.062
PHY-3002 : Step(61): len = 263917, overlap = 472.062
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.43142e-05
PHY-3002 : Step(62): len = 271868, overlap = 430.688
PHY-3002 : Step(63): len = 281453, overlap = 378.75
PHY-3002 : Step(64): len = 285975, overlap = 365.375
PHY-3002 : Step(65): len = 287612, overlap = 346.969
PHY-3002 : Step(66): len = 284982, overlap = 354.781
PHY-3002 : Step(67): len = 283351, overlap = 353.188
PHY-3002 : Step(68): len = 281571, overlap = 347.656
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 6.86283e-05
PHY-3002 : Step(69): len = 287986, overlap = 332.969
PHY-3002 : Step(70): len = 296678, overlap = 320.312
PHY-3002 : Step(71): len = 300803, overlap = 298.688
PHY-3002 : Step(72): len = 302794, overlap = 286.875
PHY-3002 : Step(73): len = 302538, overlap = 296.719
PHY-3002 : Step(74): len = 301266, overlap = 296.469
PHY-3002 : Step(75): len = 299300, overlap = 292.562
PHY-3002 : Step(76): len = 298542, overlap = 285.656
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000136526
PHY-3002 : Step(77): len = 302753, overlap = 287.25
PHY-3002 : Step(78): len = 309894, overlap = 282.25
PHY-3002 : Step(79): len = 313214, overlap = 277.75
PHY-3002 : Step(80): len = 315515, overlap = 248.5
PHY-3002 : Step(81): len = 315313, overlap = 230.219
PHY-3002 : Step(82): len = 314005, overlap = 214.438
PHY-3002 : Step(83): len = 311782, overlap = 207.625
PHY-3002 : Step(84): len = 312881, overlap = 214.969
PHY-3002 : Step(85): len = 313395, overlap = 233.156
PHY-3002 : Step(86): len = 312767, overlap = 262.438
PHY-3002 : Step(87): len = 311751, overlap = 266.688
PHY-3002 : Step(88): len = 311561, overlap = 276.031
PHY-3002 : Step(89): len = 311796, overlap = 263.562
PHY-3002 : Step(90): len = 311077, overlap = 261.281
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000251927
PHY-3002 : Step(91): len = 313648, overlap = 253.781
PHY-3002 : Step(92): len = 317485, overlap = 242.656
PHY-3002 : Step(93): len = 318547, overlap = 242.594
PHY-3002 : Step(94): len = 320064, overlap = 242.688
PHY-3002 : Step(95): len = 320263, overlap = 241.031
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.00040922
PHY-3002 : Step(96): len = 321111, overlap = 241.062
PHY-3002 : Step(97): len = 323165, overlap = 230.969
PHY-3002 : Step(98): len = 324053, overlap = 232.688
PHY-3002 : Step(99): len = 325681, overlap = 225.938
PHY-3002 : Step(100): len = 325299, overlap = 228.625
PHY-3002 : Step(101): len = 325506, overlap = 236.938
PHY-3002 : Step(102): len = 324945, overlap = 232.688
PHY-3002 : Step(103): len = 324589, overlap = 234.75
PHY-3002 : Step(104): len = 324366, overlap = 235.062
PHY-3002 : Step(105): len = 324821, overlap = 229.531
PHY-3002 : Step(106): len = 324791, overlap = 222.312
PHY-3002 : Step(107): len = 325163, overlap = 221.062
PHY-3002 : Step(108): len = 325385, overlap = 241.406
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.014874s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (315.1%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21551.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 428888, over cnt = 1164(3%), over = 5070, worst = 32
PHY-1001 : End global iterations;  0.871838s wall, 1.203125s user + 0.046875s system = 1.250000s CPU (143.4%)

PHY-1001 : Congestion index: top1 = 69.14, top5 = 50.43, top10 = 41.67, top15 = 36.45.
PHY-3001 : End congestion estimation;  1.091974s wall, 1.421875s user + 0.046875s system = 1.468750s CPU (134.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21549 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.836295s wall, 0.812500s user + 0.031250s system = 0.843750s CPU (100.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000105037
PHY-3002 : Step(109): len = 367595, overlap = 162.344
PHY-3002 : Step(110): len = 379781, overlap = 155.25
PHY-3002 : Step(111): len = 380779, overlap = 153.344
PHY-3002 : Step(112): len = 380713, overlap = 143.906
PHY-3002 : Step(113): len = 385458, overlap = 125.781
PHY-3002 : Step(114): len = 390665, overlap = 128.312
PHY-3002 : Step(115): len = 392609, overlap = 119.656
PHY-3002 : Step(116): len = 396610, overlap = 116.906
PHY-3002 : Step(117): len = 400645, overlap = 118.188
PHY-3002 : Step(118): len = 401807, overlap = 114.344
PHY-3002 : Step(119): len = 402687, overlap = 117.625
PHY-3002 : Step(120): len = 403545, overlap = 120.156
PHY-3002 : Step(121): len = 406299, overlap = 126.344
PHY-3002 : Step(122): len = 406755, overlap = 127.844
PHY-3002 : Step(123): len = 406751, overlap = 127.906
PHY-3002 : Step(124): len = 409111, overlap = 127.531
PHY-3002 : Step(125): len = 408466, overlap = 131.031
PHY-3002 : Step(126): len = 407284, overlap = 126.594
PHY-3002 : Step(127): len = 407375, overlap = 123.875
PHY-3002 : Step(128): len = 406447, overlap = 126.062
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(129): len = 406706, overlap = 120.531
PHY-3002 : Step(130): len = 407827, overlap = 119.562
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 95/21551.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 478248, over cnt = 2069(5%), over = 9447, worst = 33
PHY-1001 : End global iterations;  0.954138s wall, 1.562500s user + 0.015625s system = 1.578125s CPU (165.4%)

PHY-1001 : Congestion index: top1 = 76.75, top5 = 57.94, top10 = 49.09, top15 = 43.81.
PHY-3001 : End congestion estimation;  1.200223s wall, 1.796875s user + 0.031250s system = 1.828125s CPU (152.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21549 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.823585s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.08841e-05
PHY-3002 : Step(131): len = 413649, overlap = 407.844
PHY-3002 : Step(132): len = 426050, overlap = 342.812
PHY-3002 : Step(133): len = 422398, overlap = 312.781
PHY-3002 : Step(134): len = 418557, overlap = 295.438
PHY-3002 : Step(135): len = 417155, overlap = 280.844
PHY-3002 : Step(136): len = 415599, overlap = 269.594
PHY-3002 : Step(137): len = 413085, overlap = 269.875
PHY-3002 : Step(138): len = 411291, overlap = 264.531
PHY-3002 : Step(139): len = 410285, overlap = 254.812
PHY-3002 : Step(140): len = 409139, overlap = 245.844
PHY-3002 : Step(141): len = 406990, overlap = 242.906
PHY-3002 : Step(142): len = 404680, overlap = 239.719
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000141768
PHY-3002 : Step(143): len = 405024, overlap = 232.906
PHY-3002 : Step(144): len = 407025, overlap = 232.969
PHY-3002 : Step(145): len = 408346, overlap = 220.312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000283536
PHY-3002 : Step(146): len = 411760, overlap = 206.812
PHY-3002 : Step(147): len = 416284, overlap = 200.406
PHY-3002 : Step(148): len = 420191, overlap = 183.25
PHY-3002 : Step(149): len = 419829, overlap = 181.25
PHY-3002 : Step(150): len = 420678, overlap = 177.281
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000567073
PHY-3002 : Step(151): len = 422356, overlap = 169.812
PHY-3002 : Step(152): len = 425830, overlap = 167.562
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.000936354
PHY-3002 : Step(153): len = 428699, overlap = 162.031
PHY-3002 : Step(154): len = 436368, overlap = 145.281
PHY-3002 : Step(155): len = 442477, overlap = 137.844
PHY-3002 : Step(156): len = 443814, overlap = 129.781
PHY-3002 : Step(157): len = 443660, overlap = 128.125
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 79925, tnet num: 21549, tinst num: 19150, tnode num: 112729, tedge num: 125777.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.433664s wall, 1.390625s user + 0.046875s system = 1.437500s CPU (100.3%)

RUN-1004 : used memory is 563 MB, reserved memory is 537 MB, peak memory is 694 MB
OPT-1001 : Total overflow 486.41 peak overflow 4.09
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 611/21551.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 529496, over cnt = 2402(6%), over = 8664, worst = 27
PHY-1001 : End global iterations;  1.121392s wall, 1.843750s user + 0.000000s system = 1.843750s CPU (164.4%)

PHY-1001 : Congestion index: top1 = 56.34, top5 = 47.10, top10 = 42.76, top15 = 39.74.
PHY-1001 : End incremental global routing;  1.342804s wall, 2.046875s user + 0.000000s system = 2.046875s CPU (152.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21549 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.891391s wall, 0.890625s user + 0.000000s system = 0.890625s CPU (99.9%)

OPT-1001 : 15 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19072 has valid locations, 232 needs to be replaced
PHY-3001 : design contains 19367 instances, 5539 luts, 12283 seqs, 1439 slices, 286 macros(1439 instances: 943 mslices 496 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 458491
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16895/21768.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 539632, over cnt = 2408(6%), over = 8654, worst = 27
PHY-1001 : End global iterations;  0.170741s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (173.9%)

PHY-1001 : Congestion index: top1 = 57.00, top5 = 47.47, top10 = 43.10, top15 = 40.05.
PHY-3001 : End congestion estimation;  0.403475s wall, 0.515625s user + 0.015625s system = 0.531250s CPU (131.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80628, tnet num: 21766, tinst num: 19367, tnode num: 113752, tedge num: 126749.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.425757s wall, 1.406250s user + 0.031250s system = 1.437500s CPU (100.8%)

RUN-1004 : used memory is 605 MB, reserved memory is 597 MB, peak memory is 695 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21766 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.326291s wall, 2.265625s user + 0.062500s system = 2.328125s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(158): len = 458470, overlap = 1.75
PHY-3002 : Step(159): len = 458964, overlap = 1.75
PHY-3002 : Step(160): len = 459707, overlap = 1.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 16947/21768.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 539792, over cnt = 2446(6%), over = 8732, worst = 27
PHY-1001 : End global iterations;  0.163413s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (114.7%)

PHY-1001 : Congestion index: top1 = 56.77, top5 = 47.57, top10 = 43.20, top15 = 40.16.
PHY-3001 : End congestion estimation;  0.385506s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (109.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21766 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.904109s wall, 0.906250s user + 0.000000s system = 0.906250s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000792919
PHY-3002 : Step(161): len = 459946, overlap = 130.25
PHY-3002 : Step(162): len = 460853, overlap = 131.281
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00158584
PHY-3002 : Step(163): len = 461044, overlap = 131
PHY-3002 : Step(164): len = 461275, overlap = 130.594
PHY-3001 : Final: Len = 461275, Over = 130.594
PHY-3001 : End incremental placement;  4.798811s wall, 5.062500s user + 0.312500s system = 5.375000s CPU (112.0%)

OPT-1001 : Total overflow 491.59 peak overflow 4.09
OPT-1001 : End high-fanout net optimization;  7.492057s wall, 8.640625s user + 0.312500s system = 8.953125s CPU (119.5%)

OPT-1001 : Current memory(MB): used = 699, reserve = 678, peak = 715.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16932/21768.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 542048, over cnt = 2411(6%), over = 8265, worst = 27
PHY-1002 : len = 579496, over cnt = 1690(4%), over = 4559, worst = 23
PHY-1002 : len = 620000, over cnt = 811(2%), over = 1687, worst = 22
PHY-1002 : len = 635536, over cnt = 361(1%), over = 769, worst = 15
PHY-1002 : len = 648288, over cnt = 9(0%), over = 10, worst = 2
PHY-1001 : End global iterations;  1.251817s wall, 1.781250s user + 0.000000s system = 1.781250s CPU (142.3%)

PHY-1001 : Congestion index: top1 = 48.51, top5 = 42.96, top10 = 40.11, top15 = 38.14.
OPT-1001 : End congestion update;  1.472999s wall, 2.000000s user + 0.000000s system = 2.000000s CPU (135.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 21766 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.770996s wall, 0.750000s user + 0.031250s system = 0.781250s CPU (101.3%)

OPT-0007 : Start: WNS 4119 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.249242s wall, 2.750000s user + 0.031250s system = 2.781250s CPU (123.7%)

OPT-1001 : Current memory(MB): used = 676, reserve = 659, peak = 715.
OPT-1001 : End physical optimization;  11.474132s wall, 13.203125s user + 0.406250s system = 13.609375s CPU (118.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5539 LUT to BLE ...
SYN-4008 : Packed 5539 LUT and 2639 SEQ to BLE.
SYN-4003 : Packing 9644 remaining SEQ's ...
SYN-4005 : Packed 3279 SEQ with LUT/SLICE
SYN-4006 : 138 single LUT's are left
SYN-4006 : 6365 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11904/13623 primitive instances ...
PHY-3001 : End packing;  2.595580s wall, 2.593750s user + 0.000000s system = 2.593750s CPU (99.9%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8054 instances
RUN-1001 : 3973 mslices, 3973 lslices, 60 pads, 43 brams, 0 dsps
RUN-1001 : There are total 19181 nets
RUN-1001 : 13422 nets have 2 pins
RUN-1001 : 4383 nets have [3 - 5] pins
RUN-1001 : 852 nets have [6 - 10] pins
RUN-1001 : 382 nets have [11 - 20] pins
RUN-1001 : 132 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
PHY-3001 : design contains 8052 instances, 7946 slices, 286 macros(1439 instances: 943 mslices 496 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 478887, Over = 368.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7801/19181.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 614920, over cnt = 1537(4%), over = 2478, worst = 8
PHY-1002 : len = 621696, over cnt = 1004(2%), over = 1345, worst = 7
PHY-1002 : len = 634528, over cnt = 323(0%), over = 392, worst = 4
PHY-1002 : len = 639080, over cnt = 128(0%), over = 157, worst = 4
PHY-1002 : len = 642104, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.132629s wall, 1.640625s user + 0.000000s system = 1.640625s CPU (144.9%)

PHY-1001 : Congestion index: top1 = 50.45, top5 = 43.81, top10 = 40.47, top15 = 38.26.
PHY-3001 : End congestion estimation;  1.414014s wall, 1.921875s user + 0.000000s system = 1.921875s CPU (135.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66917, tnet num: 19179, tinst num: 8052, tnode num: 90996, tedge num: 110320.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.581685s wall, 1.578125s user + 0.015625s system = 1.593750s CPU (100.8%)

RUN-1004 : used memory is 596 MB, reserved memory is 583 MB, peak memory is 715 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19179 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.436695s wall, 2.406250s user + 0.031250s system = 2.437500s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.05465e-05
PHY-3002 : Step(165): len = 480522, overlap = 350
PHY-3002 : Step(166): len = 478533, overlap = 362
PHY-3002 : Step(167): len = 479924, overlap = 380.75
PHY-3002 : Step(168): len = 478484, overlap = 389.75
PHY-3002 : Step(169): len = 476781, overlap = 384.5
PHY-3002 : Step(170): len = 475496, overlap = 382.25
PHY-3002 : Step(171): len = 472920, overlap = 387
PHY-3002 : Step(172): len = 471874, overlap = 382
PHY-3002 : Step(173): len = 469842, overlap = 386.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000101093
PHY-3002 : Step(174): len = 473939, overlap = 378.75
PHY-3002 : Step(175): len = 478404, overlap = 375.5
PHY-3002 : Step(176): len = 479655, overlap = 367.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000202186
PHY-3002 : Step(177): len = 485661, overlap = 363.25
PHY-3002 : Step(178): len = 498885, overlap = 337
PHY-3002 : Step(179): len = 500358, overlap = 333
PHY-3002 : Step(180): len = 498970, overlap = 330.25
PHY-3002 : Step(181): len = 499294, overlap = 331.5
PHY-3002 : Step(182): len = 500521, overlap = 329.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.651622s wall, 0.671875s user + 0.796875s system = 1.468750s CPU (225.4%)

PHY-3001 : Trial Legalized: Len = 607158
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 598/19181.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 697200, over cnt = 2264(6%), over = 3758, worst = 9
PHY-1002 : len = 713856, over cnt = 1289(3%), over = 1743, worst = 8
PHY-1002 : len = 729776, over cnt = 386(1%), over = 505, worst = 8
PHY-1002 : len = 736000, over cnt = 95(0%), over = 109, worst = 3
PHY-1002 : len = 738376, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.687645s wall, 2.984375s user + 0.031250s system = 3.015625s CPU (178.7%)

PHY-1001 : Congestion index: top1 = 50.43, top5 = 44.63, top10 = 42.01, top15 = 40.29.
PHY-3001 : End congestion estimation;  2.003774s wall, 3.296875s user + 0.031250s system = 3.328125s CPU (166.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19179 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.029965s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00018713
PHY-3002 : Step(183): len = 564294, overlap = 80
PHY-3002 : Step(184): len = 545722, overlap = 124.75
PHY-3002 : Step(185): len = 535400, overlap = 163.25
PHY-3002 : Step(186): len = 529287, overlap = 201.75
PHY-3002 : Step(187): len = 526994, overlap = 226
PHY-3002 : Step(188): len = 524789, overlap = 233.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000374259
PHY-3002 : Step(189): len = 529570, overlap = 225.75
PHY-3002 : Step(190): len = 534380, overlap = 224.5
PHY-3002 : Step(191): len = 534471, overlap = 228.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000677057
PHY-3002 : Step(192): len = 536308, overlap = 228
PHY-3002 : Step(193): len = 546746, overlap = 223.25
PHY-3002 : Step(194): len = 556795, overlap = 225.75
PHY-3002 : Step(195): len = 553643, overlap = 224.25
PHY-3002 : Step(196): len = 551846, overlap = 225.5
PHY-3002 : Step(197): len = 550797, overlap = 228.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.029664s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (105.3%)

PHY-3001 : Legalized: Len = 587702, Over = 0
PHY-3001 : Spreading special nets. 45 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.069575s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (112.3%)

PHY-3001 : 70 instances has been re-located, deltaX = 28, deltaY = 34, maxDist = 2.
PHY-3001 : Final: Len = 588730, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66917, tnet num: 19179, tinst num: 8052, tnode num: 90996, tedge num: 110320.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.797963s wall, 1.796875s user + 0.000000s system = 1.796875s CPU (99.9%)

RUN-1004 : used memory is 621 MB, reserved memory is 614 MB, peak memory is 715 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3512/19181.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 688304, over cnt = 2110(5%), over = 3274, worst = 7
PHY-1002 : len = 700472, over cnt = 1206(3%), over = 1630, worst = 7
PHY-1002 : len = 713896, over cnt = 467(1%), over = 564, worst = 6
PHY-1002 : len = 717744, over cnt = 254(0%), over = 305, worst = 6
PHY-1002 : len = 720328, over cnt = 123(0%), over = 151, worst = 6
PHY-1001 : End global iterations;  1.411258s wall, 2.546875s user + 0.031250s system = 2.578125s CPU (182.7%)

PHY-1001 : Congestion index: top1 = 49.48, top5 = 43.87, top10 = 41.10, top15 = 39.29.
PHY-1001 : End incremental global routing;  1.672794s wall, 2.812500s user + 0.031250s system = 2.843750s CPU (170.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19179 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.791216s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (100.7%)

OPT-1001 : 3 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 7986 has valid locations, 17 needs to be replaced
PHY-3001 : design contains 8066 instances, 7960 slices, 286 macros(1439 instances: 943 mslices 496 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 592108
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17220/19201.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 724800, over cnt = 163(0%), over = 198, worst = 6
PHY-1002 : len = 724936, over cnt = 119(0%), over = 135, worst = 4
PHY-1002 : len = 726496, over cnt = 14(0%), over = 14, worst = 1
PHY-1002 : len = 726752, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 727024, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.692213s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (103.8%)

PHY-1001 : Congestion index: top1 = 49.55, top5 = 44.01, top10 = 41.23, top15 = 39.44.
PHY-3001 : End congestion estimation;  0.957949s wall, 0.968750s user + 0.015625s system = 0.984375s CPU (102.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67058, tnet num: 19199, tinst num: 8066, tnode num: 91170, tedge num: 110502.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.882916s wall, 1.859375s user + 0.015625s system = 1.875000s CPU (99.6%)

RUN-1004 : used memory is 659 MB, reserved memory is 649 MB, peak memory is 715 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19199 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.773394s wall, 2.750000s user + 0.015625s system = 2.765625s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(198): len = 591395, overlap = 0.75
PHY-3002 : Step(199): len = 591346, overlap = 1
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17211/19201.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 724808, over cnt = 37(0%), over = 56, worst = 5
PHY-1002 : len = 724912, over cnt = 26(0%), over = 27, worst = 2
PHY-1002 : len = 725040, over cnt = 12(0%), over = 13, worst = 2
PHY-1002 : len = 725336, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.519940s wall, 0.562500s user + 0.000000s system = 0.562500s CPU (108.2%)

PHY-1001 : Congestion index: top1 = 49.98, top5 = 44.04, top10 = 41.23, top15 = 39.41.
PHY-3001 : End congestion estimation;  0.858209s wall, 0.890625s user + 0.000000s system = 0.890625s CPU (103.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19199 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.800589s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000865686
PHY-3002 : Step(200): len = 591252, overlap = 2.75
PHY-3002 : Step(201): len = 591304, overlap = 1.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005823s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 591318, Over = 0
PHY-3001 : End spreading;  0.063029s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.2%)

PHY-3001 : Final: Len = 591318, Over = 0
PHY-3001 : End incremental placement;  6.110124s wall, 6.156250s user + 0.156250s system = 6.312500s CPU (103.3%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  8.994382s wall, 10.359375s user + 0.187500s system = 10.546875s CPU (117.3%)

OPT-1001 : Current memory(MB): used = 706, reserve = 687, peak = 715.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17213/19201.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 725392, over cnt = 27(0%), over = 39, worst = 4
PHY-1002 : len = 725344, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 725520, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.378071s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (107.5%)

PHY-1001 : Congestion index: top1 = 49.53, top5 = 43.82, top10 = 41.10, top15 = 39.31.
OPT-1001 : End congestion update;  0.660720s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (101.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19199 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.711122s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (101.1%)

OPT-0007 : Start: WNS 3823 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.376081s wall, 1.390625s user + 0.000000s system = 1.390625s CPU (101.1%)

OPT-1001 : Current memory(MB): used = 706, reserve = 687, peak = 715.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19199 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.695870s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (98.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17234/19201.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 725520, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.112319s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (97.4%)

PHY-1001 : Congestion index: top1 = 49.53, top5 = 43.82, top10 = 41.10, top15 = 39.31.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19199 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.765573s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (100.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3823 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 49.137931
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3823ps with logic level 4 
OPT-1001 : End physical optimization;  14.261049s wall, 15.625000s user + 0.187500s system = 15.812500s CPU (110.9%)

RUN-1003 : finish command "place" in  63.487996s wall, 101.171875s user + 6.671875s system = 107.843750s CPU (169.9%)

RUN-1004 : used memory is 625 MB, reserved memory is 603 MB, peak memory is 715 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.546693s wall, 2.640625s user + 0.015625s system = 2.656250s CPU (171.7%)

RUN-1004 : used memory is 626 MB, reserved memory is 604 MB, peak memory is 715 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8068 instances
RUN-1001 : 3976 mslices, 3984 lslices, 60 pads, 43 brams, 0 dsps
RUN-1001 : There are total 19201 nets
RUN-1001 : 13428 nets have 2 pins
RUN-1001 : 4377 nets have [3 - 5] pins
RUN-1001 : 862 nets have [6 - 10] pins
RUN-1001 : 391 nets have [11 - 20] pins
RUN-1001 : 133 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67058, tnet num: 19199, tinst num: 8066, tnode num: 91170, tedge num: 110502.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.584197s wall, 1.593750s user + 0.000000s system = 1.593750s CPU (100.6%)

RUN-1004 : used memory is 634 MB, reserved memory is 623 MB, peak memory is 715 MB
PHY-1001 : 3976 mslices, 3984 lslices, 60 pads, 43 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19199 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 671440, over cnt = 2261(6%), over = 3673, worst = 7
PHY-1002 : len = 684560, over cnt = 1437(4%), over = 2079, worst = 7
PHY-1002 : len = 702632, over cnt = 528(1%), over = 726, worst = 6
PHY-1002 : len = 714440, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 714688, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.548049s wall, 2.781250s user + 0.046875s system = 2.828125s CPU (182.7%)

PHY-1001 : Congestion index: top1 = 49.05, top5 = 43.52, top10 = 40.67, top15 = 38.93.
PHY-1001 : End global routing;  1.849020s wall, 3.078125s user + 0.046875s system = 3.125000s CPU (169.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 696, reserve = 682, peak = 715.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 964, reserve = 947, peak = 964.
PHY-1001 : End build detailed router design. 4.339822s wall, 4.296875s user + 0.046875s system = 4.343750s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 192920, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.807963s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (100.6%)

PHY-1001 : Current memory(MB): used = 999, reserve = 983, peak = 999.
PHY-1001 : End phase 1; 0.815275s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (99.7%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.76029e+06, over cnt = 1251(0%), over = 1253, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1014, reserve = 1000, peak = 1014.
PHY-1001 : End initial routed; 16.458943s wall, 44.406250s user + 0.484375s system = 44.890625s CPU (272.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17986(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.300   |   0.000   |   0   
RUN-1001 :   Hold   |   0.172   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.103365s wall, 3.109375s user + 0.000000s system = 3.109375s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 1025, reserve = 1011, peak = 1025.
PHY-1001 : End phase 2; 19.562449s wall, 47.515625s user + 0.484375s system = 48.000000s CPU (245.4%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.76029e+06, over cnt = 1251(0%), over = 1253, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.219413s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (106.8%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.74878e+06, over cnt = 417(0%), over = 417, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.552150s wall, 1.046875s user + 0.000000s system = 1.046875s CPU (189.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.74966e+06, over cnt = 76(0%), over = 76, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.336100s wall, 0.468750s user + 0.000000s system = 0.468750s CPU (139.5%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.75047e+06, over cnt = 18(0%), over = 18, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.223660s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (125.7%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.75085e+06, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.197696s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (110.6%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.75094e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.163065s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (95.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17986(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.195   |   0.000   |   0   
RUN-1001 :   Hold   |   0.172   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.061480s wall, 3.062500s user + 0.000000s system = 3.062500s CPU (100.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 297 feed throughs used by 261 nets
PHY-1001 : End commit to database; 2.032214s wall, 2.015625s user + 0.015625s system = 2.031250s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1111, reserve = 1101, peak = 1111.
PHY-1001 : End phase 3; 7.261178s wall, 7.937500s user + 0.015625s system = 7.953125s CPU (109.5%)

PHY-1003 : Routed, final wirelength = 1.75094e+06
PHY-1001 : Current memory(MB): used = 1116, reserve = 1105, peak = 1116.
PHY-1001 : End export database. 0.057065s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.5%)

PHY-1001 : End detail routing;  32.416787s wall, 61.000000s user + 0.546875s system = 61.546875s CPU (189.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67058, tnet num: 19199, tinst num: 8066, tnode num: 91170, tedge num: 110502.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.548260s wall, 1.562500s user + 0.000000s system = 1.562500s CPU (100.9%)

RUN-1004 : used memory is 1048 MB, reserved memory is 1044 MB, peak memory is 1116 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  39.695037s wall, 69.484375s user + 0.609375s system = 70.093750s CPU (176.6%)

RUN-1004 : used memory is 1048 MB, reserved memory is 1044 MB, peak memory is 1116 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8585   out of  19600   43.80%
#reg                    12393   out of  19600   63.23%
#le                     14906
  #lut only              2513   out of  14906   16.86%
  #reg only              6321   out of  14906   42.41%
  #lut&reg               6072   out of  14906   40.74%
#dsp                        0   out of     29    0.00%
#bram                      43   out of     64   67.19%
  #bram9k                  42
  #fifo9k                   1
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6764
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          184
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         M3        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         P6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |14906  |7146    |1439    |12437   |43      |0       |
|  AnyFog_dataX                      |AnyFog          |212    |72      |22      |178     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |53      |22      |52      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |206    |75      |22      |170     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |88     |55      |22      |52      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |208    |98      |22      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |60      |22      |51      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2867   |483     |34      |2786    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |219    |74      |5       |207     |0       |0       |
|    STADOP_com2                     |STADOP          |559    |56      |0       |550     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |41      |14      |41      |0       |0       |
|    head_com2                       |uniheading      |268    |78      |5       |249     |0       |0       |
|    uart_com2                       |Agrica          |1434   |214     |10      |1417    |0       |0       |
|  COM3                              |COM3_Control    |276    |157     |19      |233     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |60     |42      |5       |51      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |62     |39      |14      |39      |0       |0       |
|    rmc_com3                        |Gprmc           |154    |76      |0       |143     |0       |0       |
|  DATA                              |Data_Processing |8659   |4413    |1062    |6987    |0       |0       |
|    DIV_Dtemp                       |Divider         |775    |285     |84      |647     |0       |0       |
|    DIV_Utemp                       |Divider         |623    |322     |84      |499     |0       |0       |
|    DIV_accX                        |Divider         |607    |325     |84      |486     |0       |0       |
|    DIV_accY                        |Divider         |648    |325     |111     |479     |0       |0       |
|    DIV_accZ                        |Divider         |642    |393     |132     |436     |0       |0       |
|    DIV_rateX                       |Divider         |637    |326     |132     |429     |0       |0       |
|    DIV_rateY                       |Divider         |591    |361     |132     |389     |0       |0       |
|    DIV_rateZ                       |Divider         |641    |393     |132     |435     |0       |0       |
|    genclk                          |genclk          |95     |64      |20      |54      |0       |0       |
|  FMC                               |FMC_Ctrl        |440    |386     |43      |340     |0       |0       |
|  IIC                               |I2C_master      |301    |266     |11      |263     |0       |0       |
|  IMU_CTRL                          |SCHA634         |936    |720     |61      |714     |0       |0       |
|    CtrlData                        |CtrlData        |522    |468     |47      |337     |0       |0       |
|      usms                          |Time_1ms        |28     |23      |5       |20      |0       |0       |
|    SPIM                            |SPI_SCHA634     |414    |252     |14      |377     |0       |0       |
|  POWER                             |POWER_EN        |97     |50      |38      |38      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |690    |420     |105     |497     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |690    |420     |105     |497     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |326    |174     |0       |309     |0       |0       |
|        reg_inst                    |register        |324    |172     |0       |307     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |364    |246     |105     |188     |0       |0       |
|        bus_inst                    |bus_top         |139    |91      |48      |59      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |28     |18      |10      |12      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |58     |40      |18      |26      |0       |0       |
|          BUS_DETECTOR[6]$bus_nodes |bus_det         |26     |16      |10      |10      |0       |0       |
|          BUS_DETECTOR[7]$bus_nodes |bus_det         |27     |17      |10      |11      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |137    |104     |29      |89      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13367  
    #2          2       3433   
    #3          3        680   
    #4          4        264   
    #5        5-10       938   
    #6        11-50      443   
    #7       51-100       5    
    #8       101-500      4    
    #9        >500        2    
  Average     2.13             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  1.835737s wall, 3.171875s user + 0.000000s system = 3.171875s CPU (172.8%)

RUN-1004 : used memory is 1048 MB, reserved memory is 1044 MB, peak memory is 1116 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67058, tnet num: 19199, tinst num: 8066, tnode num: 91170, tedge num: 110502.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.543573s wall, 1.546875s user + 0.000000s system = 1.546875s CPU (100.2%)

RUN-1004 : used memory is 1050 MB, reserved memory is 1045 MB, peak memory is 1116 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19199 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.213517s wall, 1.203125s user + 0.000000s system = 1.203125s CPU (99.1%)

RUN-1004 : used memory is 1055 MB, reserved memory is 1050 MB, peak memory is 1116 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 8f169df8acfcb835781910b1ac29267cd80e16d8a5e1b8cf9d032ec93c0a2865 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8066
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19201, pip num: 146588
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 297
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3262 valid insts, and 408410 bits set as '1'.
BIT-1004 : the usercode register value: 00000000110101000101101001110001
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  10.311609s wall, 103.656250s user + 0.078125s system = 103.734375s CPU (1006.0%)

RUN-1004 : used memory is 1183 MB, reserved memory is 1167 MB, peak memory is 1298 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250710_201300.log"

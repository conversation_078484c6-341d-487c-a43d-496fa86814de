============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Aug 28 13:56:26 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param rtl default_reg_initial 0"
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |     0      |       auto       |   *    
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'gnss_timestamp_usec', assumed default net type 'wire' in ../../Src/INS600M-21A.v(113)
HDL-1007 : undeclared symbol 'gnss_latitude_deg_1e8', assumed default net type 'wire' in ../../Src/INS600M-21A.v(114)
HDL-1007 : undeclared symbol 'gnss_longitude_deg_1e8', assumed default net type 'wire' in ../../Src/INS600M-21A.v(115)
HDL-1007 : undeclared symbol 'gnss_height_ellipsoid_mm', assumed default net type 'wire' in ../../Src/INS600M-21A.v(116)
HDL-1007 : undeclared symbol 'gnss_height_msl_mm', assumed default net type 'wire' in ../../Src/INS600M-21A.v(117)
HDL-1007 : undeclared symbol 'gnss_velocity_north_m_s', assumed default net type 'wire' in ../../Src/INS600M-21A.v(118)
HDL-1007 : undeclared symbol 'gnss_velocity_east_m_s', assumed default net type 'wire' in ../../Src/INS600M-21A.v(119)
HDL-1007 : undeclared symbol 'gnss_velocity_down_m_s', assumed default net type 'wire' in ../../Src/INS600M-21A.v(120)
HDL-1007 : undeclared symbol 'gnss_velocity_accuracy_m_s', assumed default net type 'wire' in ../../Src/INS600M-21A.v(121)
HDL-1007 : undeclared symbol 'gnss_course_over_ground', assumed default net type 'wire' in ../../Src/INS600M-21A.v(122)
HDL-1007 : undeclared symbol 'gnss_ground_speed_m_s', assumed default net type 'wire' in ../../Src/INS600M-21A.v(123)
HDL-1007 : undeclared symbol 'gnss_position_accuracy_mm', assumed default net type 'wire' in ../../Src/INS600M-21A.v(124)
HDL-1007 : undeclared symbol 'gnss_hdop', assumed default net type 'wire' in ../../Src/INS600M-21A.v(125)
HDL-1007 : undeclared symbol 'gnss_vdop', assumed default net type 'wire' in ../../Src/INS600M-21A.v(126)
HDL-1007 : undeclared symbol 'gnss_sats_used', assumed default net type 'wire' in ../../Src/INS600M-21A.v(127)
HDL-1007 : undeclared symbol 'gnss_sats_visible', assumed default net type 'wire' in ../../Src/INS600M-21A.v(128)
HDL-1007 : undeclared symbol 'gnss_fix_type', assumed default net type 'wire' in ../../Src/INS600M-21A.v(129)
HDL-1007 : undeclared symbol 'gnss_status', assumed default net type 'wire' in ../../Src/INS600M-21A.v(130)
HDL-1007 : undeclared symbol 'gnss_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(131)
HDL-1007 : analyze verilog file ../../Src/DroneCAN/dronecan_protocol_parser.v
RUN-1001 : Project manager successfully analyzed 3 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "elaborate -top INS600M_21A"
RUN-1002 : start command "set_param rtl default_reg_initial 0"
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |     0      |       auto       |   *    
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param rtl default_reg_initial 0"
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |     0      |       auto       |   *    
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : port 'node_status_health' remains unconnected for this instance in ../../Src/INS600M-21A.v(105)
HDL-1007 : port 'node_status_mode' remains unconnected for this instance in ../../Src/INS600M-21A.v(105)
HDL-1007 : port 'node_status_uptime' remains unconnected for this instance in ../../Src/INS600M-21A.v(105)
HDL-1007 : port 'node_status_vendor_code' remains unconnected for this instance in ../../Src/INS600M-21A.v(105)
HDL-1007 : port 'node_status_valid' remains unconnected for this instance in ../../Src/INS600M-21A.v(105)
HDL-1007 : port 'mag_field_ga_x' remains unconnected for this instance in ../../Src/INS600M-21A.v(105)
HDL-1007 : port 'mag_field_ga_y' remains unconnected for this instance in ../../Src/INS600M-21A.v(105)
HDL-1007 : port 'mag_field_ga_z' remains unconnected for this instance in ../../Src/INS600M-21A.v(105)
HDL-1007 : port 'mag_field_valid' remains unconnected for this instance in ../../Src/INS600M-21A.v(105)
HDL-1007 : port 'imu_timestamp_usec' remains unconnected for this instance in ../../Src/INS600M-21A.v(105)
HDL-1007 : port 'imu_rate_gyro_x' remains unconnected for this instance in ../../Src/INS600M-21A.v(105)
HDL-1007 : port 'imu_rate_gyro_y' remains unconnected for this instance in ../../Src/INS600M-21A.v(105)
HDL-1007 : port 'imu_rate_gyro_z' remains unconnected for this instance in ../../Src/INS600M-21A.v(105)
HDL-1007 : port 'imu_accelerometer_x' remains unconnected for this instance in ../../Src/INS600M-21A.v(105)
HDL-1007 : port 'imu_accelerometer_y' remains unconnected for this instance in ../../Src/INS600M-21A.v(105)
HDL-1007 : port 'imu_accelerometer_z' remains unconnected for this instance in ../../Src/INS600M-21A.v(105)
HDL-1007 : port 'imu_valid' remains unconnected for this instance in ../../Src/INS600M-21A.v(105)
HDL-1007 : port 'battery_voltage' remains unconnected for this instance in ../../Src/INS600M-21A.v(105)
HDL-1007 : port 'battery_current' remains unconnected for this instance in ../../Src/INS600M-21A.v(105)
HDL-1007 : port 'battery_remaining_pct' remains unconnected for this instance in ../../Src/INS600M-21A.v(105)
HDL-1007 : port 'battery_valid' remains unconnected for this instance in ../../Src/INS600M-21A.v(105)
HDL-1007 : port 'active_node_mask' remains unconnected for this instance in ../../Src/INS600M-21A.v(105)
HDL-1007 : port 'protocol_errors' remains unconnected for this instance in ../../Src/INS600M-21A.v(105)
HDL-1007 : port 'current_node_id' remains unconnected for this instance in ../../Src/INS600M-21A.v(105)
HDL-1007 : port 'data_ready' remains unconnected for this instance in ../../Src/INS600M-21A.v(105)
HDL-1007 : elaborate module INS600M_21A in ../../Src/INS600M-21A.v(16)
HDL-1007 : elaborate module global_clock in ../../al_ip/global_clock.v(22)
HDL-1007 : elaborate module EG_PHY_PLL(FIN="25.000",FBCLK_DIV=40,CLKC0_DIV=10,CLKC0_ENABLE="ENABLE",FEEDBK_MODE="NOCOMP",STDBY_ENABLE="DISABLE",CLKC0_CPHASE=9,GMC_GAIN=2,ICP_CURRENT=9,KVCO=2,LPF_CAPACITOR=1,LPF_RESISTOR=8,SYNC_ENABLE="DISABLE") in D:/softwawe/Anlogic/TD_5.6.2/arch/eagle_macro.v(930)
HDL-1007 : elaborate module dronecan_protocol_parser in ../../Src/DroneCAN/dronecan_protocol_parser.v(17)
HDL-8007 ERROR: index 8 is out of range [0:7] for 'rx_data' in ../../Src/DroneCAN/dronecan_protocol_parser.v(355)
HDL-1007 : module 'dronecan_protocol_parser' remains a black box due to errors in its contents in ../../Src/DroneCAN/dronecan_protocol_parser.v(17)
HDL-5007 WARNING: actual bit length 1 differs from formal bit length 64 for port 'gnss_timestamp_usec' in ../../Src/INS600M-21A.v(113)
HDL-5007 WARNING: actual bit length 1 differs from formal bit length 64 for port 'gnss_latitude_deg_1e8' in ../../Src/INS600M-21A.v(114)
HDL-5007 WARNING: actual bit length 1 differs from formal bit length 64 for port 'gnss_longitude_deg_1e8' in ../../Src/INS600M-21A.v(115)
HDL-5007 WARNING: actual bit length 1 differs from formal bit length 32 for port 'gnss_height_ellipsoid_mm' in ../../Src/INS600M-21A.v(116)
HDL-5007 WARNING: actual bit length 1 differs from formal bit length 32 for port 'gnss_height_msl_mm' in ../../Src/INS600M-21A.v(117)
HDL-5007 WARNING: actual bit length 1 differs from formal bit length 32 for port 'gnss_velocity_north_m_s' in ../../Src/INS600M-21A.v(118)
HDL-5007 WARNING: actual bit length 1 differs from formal bit length 32 for port 'gnss_velocity_east_m_s' in ../../Src/INS600M-21A.v(119)
HDL-5007 WARNING: actual bit length 1 differs from formal bit length 32 for port 'gnss_velocity_down_m_s' in ../../Src/INS600M-21A.v(120)
HDL-5007 WARNING: actual bit length 1 differs from formal bit length 16 for port 'gnss_velocity_accuracy_m_s' in ../../Src/INS600M-21A.v(121)
HDL-5007 WARNING: actual bit length 1 differs from formal bit length 32 for port 'gnss_course_over_ground' in ../../Src/INS600M-21A.v(122)
HDL-5007 Similar messages will be suppressed.
RUN-1002 : start command "backup_run_log run.log ../.logs/syn_1/td_20250828_135626.log"

============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Wed Aug 20 15:18:35 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.754269s wall, 1.421875s user + 4.312500s system = 5.734375s CPU (99.7%)

RUN-1004 : used memory is 80 MB, reserved memory is 41 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.973799s wall, 1.906250s user + 0.062500s system = 1.968750s CPU (99.7%)

RUN-1004 : used memory is 303 MB, reserved memory is 270 MB, peak memory is 306 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 6 view nodes, 43 trigger nets, 43 data nets.
KIT-1004 : Chipwatcher code = 1011100100001111
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb01001010}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=132) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=132) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb01001010}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb01001010}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb01001010})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=132)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=132)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb01001010})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb01001010})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22854/26 useful/useless nets, 19576/15 useful/useless insts
SYN-1016 : Merged 34 instances.
SYN-1032 : 22505/22 useful/useless nets, 20010/18 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 412 better
SYN-1014 : Optimize round 2
SYN-1032 : 22174/45 useful/useless nets, 19679/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.564457s wall, 2.468750s user + 0.109375s system = 2.578125s CPU (100.5%)

RUN-1004 : used memory is 329 MB, reserved memory is 296 MB, peak memory is 331 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22222/300 useful/useless nets, 19765/48 useful/useless insts
SYN-1016 : Merged 45 instances.
SYN-2571 : Optimize after map_dsp, round 1, 393 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 40 instances.
SYN-2501 : Optimize round 1, 82 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 22676/5 useful/useless nets, 20219/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82775, tnet num: 22676, tinst num: 20218, tnode num: 115907, tedge num: 129397.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.239641s wall, 1.171875s user + 0.062500s system = 1.234375s CPU (99.6%)

RUN-1004 : used memory is 470 MB, reserved memory is 438 MB, peak memory is 470 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22676 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 242 (3.42), #lev = 7 (1.80)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 242 (3.42), #lev = 7 (1.80)
SYN-3001 : Logic optimization runtime opt =   0.04 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 559 instances into 242 LUTs, name keeping = 72%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 421 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 128 adder to BLE ...
SYN-4008 : Packed 128 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.811849s wall, 4.718750s user + 0.093750s system = 4.812500s CPU (100.0%)

RUN-1004 : used memory is 354 MB, reserved memory is 319 MB, peak memory is 579 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.733076s wall, 7.500000s user + 0.218750s system = 7.718750s CPU (99.8%)

RUN-1004 : used memory is 354 MB, reserved memory is 320 MB, peak memory is 579 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (282 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19442 instances
RUN-0007 : 5617 luts, 12218 seqs, 983 mslices, 519 lslices, 59 pads, 41 brams, 0 dsps
RUN-1001 : There are total 21907 nets
RUN-1001 : 16426 nets have 2 pins
RUN-1001 : 4289 nets have [3 - 5] pins
RUN-1001 : 815 nets have [6 - 10] pins
RUN-1001 : 250 nets have [11 - 20] pins
RUN-1001 : 105 nets have [21 - 99] pins
RUN-1001 : 22 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4788     
RUN-1001 :   No   |  No   |  Yes  |     707     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     454     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19440 instances, 5617 luts, 12218 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81105, tnet num: 21905, tinst num: 19440, tnode num: 114042, tedge num: 127673.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.268311s wall, 1.250000s user + 0.015625s system = 1.265625s CPU (99.8%)

RUN-1004 : used memory is 529 MB, reserved memory is 501 MB, peak memory is 579 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.216598s wall, 2.171875s user + 0.046875s system = 2.218750s CPU (100.1%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.66051e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19440.
PHY-3001 : Level 1 #clusters 2178.
PHY-3001 : End clustering;  0.160992s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (174.7%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 879129, overlap = 617.844
PHY-3002 : Step(2): len = 818024, overlap = 683.75
PHY-3002 : Step(3): len = 515638, overlap = 839.969
PHY-3002 : Step(4): len = 465686, overlap = 932.031
PHY-3002 : Step(5): len = 370266, overlap = 1013.38
PHY-3002 : Step(6): len = 335560, overlap = 1083.22
PHY-3002 : Step(7): len = 286106, overlap = 1167.16
PHY-3002 : Step(8): len = 254066, overlap = 1236.44
PHY-3002 : Step(9): len = 225684, overlap = 1284.03
PHY-3002 : Step(10): len = 206224, overlap = 1318.91
PHY-3002 : Step(11): len = 188613, overlap = 1347.84
PHY-3002 : Step(12): len = 171289, overlap = 1367.34
PHY-3002 : Step(13): len = 159026, overlap = 1415.38
PHY-3002 : Step(14): len = 148883, overlap = 1429.5
PHY-3002 : Step(15): len = 139918, overlap = 1447.38
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.71262e-07
PHY-3002 : Step(16): len = 143157, overlap = 1441.31
PHY-3002 : Step(17): len = 179663, overlap = 1342.75
PHY-3002 : Step(18): len = 188234, overlap = 1283.75
PHY-3002 : Step(19): len = 191366, overlap = 1199.34
PHY-3002 : Step(20): len = 188230, overlap = 1187.34
PHY-3002 : Step(21): len = 183730, overlap = 1166.03
PHY-3002 : Step(22): len = 181424, overlap = 1171.78
PHY-3002 : Step(23): len = 177993, overlap = 1153.09
PHY-3002 : Step(24): len = 175828, overlap = 1155.31
PHY-3002 : Step(25): len = 171838, overlap = 1156.84
PHY-3002 : Step(26): len = 169515, overlap = 1149.56
PHY-3002 : Step(27): len = 168560, overlap = 1138.34
PHY-3002 : Step(28): len = 168774, overlap = 1131.75
PHY-3002 : Step(29): len = 167544, overlap = 1142.44
PHY-3002 : Step(30): len = 167985, overlap = 1144.44
PHY-3002 : Step(31): len = 167766, overlap = 1151.22
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 1.94252e-06
PHY-3002 : Step(32): len = 174440, overlap = 1138.5
PHY-3002 : Step(33): len = 187890, overlap = 1066.66
PHY-3002 : Step(34): len = 192061, overlap = 1002.31
PHY-3002 : Step(35): len = 194419, overlap = 970.531
PHY-3002 : Step(36): len = 194362, overlap = 950.844
PHY-3002 : Step(37): len = 193662, overlap = 940.594
PHY-3002 : Step(38): len = 192692, overlap = 956.094
PHY-3002 : Step(39): len = 192936, overlap = 950.25
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 3.88505e-06
PHY-3002 : Step(40): len = 200904, overlap = 906.281
PHY-3002 : Step(41): len = 218654, overlap = 837.406
PHY-3002 : Step(42): len = 225163, overlap = 826.375
PHY-3002 : Step(43): len = 228087, overlap = 816.969
PHY-3002 : Step(44): len = 226579, overlap = 820.25
PHY-3002 : Step(45): len = 223879, overlap = 812.969
PHY-3002 : Step(46): len = 221366, overlap = 798.312
PHY-3002 : Step(47): len = 219946, overlap = 802.656
PHY-3002 : Step(48): len = 218948, overlap = 794.969
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 7.7701e-06
PHY-3002 : Step(49): len = 229772, overlap = 760.094
PHY-3002 : Step(50): len = 246485, overlap = 683.562
PHY-3002 : Step(51): len = 249729, overlap = 643.969
PHY-3002 : Step(52): len = 251641, overlap = 628.594
PHY-3002 : Step(53): len = 249761, overlap = 625.781
PHY-3002 : Step(54): len = 248625, overlap = 623.281
PHY-3002 : Step(55): len = 247985, overlap = 635.5
PHY-3002 : Step(56): len = 247748, overlap = 612.438
PHY-3002 : Step(57): len = 245904, overlap = 624.125
PHY-3002 : Step(58): len = 244909, overlap = 641.031
PHY-3002 : Step(59): len = 243979, overlap = 619.844
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.55402e-05
PHY-3002 : Step(60): len = 254690, overlap = 589.188
PHY-3002 : Step(61): len = 267436, overlap = 530.719
PHY-3002 : Step(62): len = 270314, overlap = 500.156
PHY-3002 : Step(63): len = 271665, overlap = 475.281
PHY-3002 : Step(64): len = 271915, overlap = 458.5
PHY-3002 : Step(65): len = 272243, overlap = 431.781
PHY-3002 : Step(66): len = 271091, overlap = 429.812
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.10804e-05
PHY-3002 : Step(67): len = 281329, overlap = 396.188
PHY-3002 : Step(68): len = 291269, overlap = 382.5
PHY-3002 : Step(69): len = 293868, overlap = 377.344
PHY-3002 : Step(70): len = 295325, overlap = 366.094
PHY-3002 : Step(71): len = 294257, overlap = 347.781
PHY-3002 : Step(72): len = 292102, overlap = 341.094
PHY-3002 : Step(73): len = 290686, overlap = 339.438
PHY-3002 : Step(74): len = 290288, overlap = 329.406
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 6.21608e-05
PHY-3002 : Step(75): len = 297698, overlap = 304.125
PHY-3002 : Step(76): len = 305105, overlap = 285.812
PHY-3002 : Step(77): len = 307309, overlap = 273.688
PHY-3002 : Step(78): len = 308253, overlap = 261.594
PHY-3002 : Step(79): len = 307323, overlap = 251.469
PHY-3002 : Step(80): len = 305462, overlap = 255.75
PHY-3002 : Step(81): len = 303827, overlap = 261.25
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000124322
PHY-3002 : Step(82): len = 308558, overlap = 253.688
PHY-3002 : Step(83): len = 314087, overlap = 239.656
PHY-3002 : Step(84): len = 316728, overlap = 236.438
PHY-3002 : Step(85): len = 318133, overlap = 227.125
PHY-3002 : Step(86): len = 317953, overlap = 231.531
PHY-3002 : Step(87): len = 317260, overlap = 223.312
PHY-3002 : Step(88): len = 315824, overlap = 234.938
PHY-3002 : Step(89): len = 315158, overlap = 232.688
PHY-3002 : Step(90): len = 315556, overlap = 212.219
PHY-3002 : Step(91): len = 315589, overlap = 200.156
PHY-3002 : Step(92): len = 315160, overlap = 190.625
PHY-3002 : Step(93): len = 314080, overlap = 192.469
PHY-3002 : Step(94): len = 312529, overlap = 191.812
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000222335
PHY-3002 : Step(95): len = 315643, overlap = 190.375
PHY-3002 : Step(96): len = 318307, overlap = 187
PHY-3002 : Step(97): len = 318493, overlap = 186.594
PHY-3002 : Step(98): len = 319357, overlap = 183.312
PHY-3002 : Step(99): len = 319634, overlap = 185.969
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000396212
PHY-3002 : Step(100): len = 320866, overlap = 185.719
PHY-3002 : Step(101): len = 325399, overlap = 186.094
PHY-3002 : Step(102): len = 326936, overlap = 190.188
PHY-3002 : Step(103): len = 328366, overlap = 192.75
PHY-3002 : Step(104): len = 328206, overlap = 197.312
PHY-3002 : Step(105): len = 327293, overlap = 212.875
PHY-3002 : Step(106): len = 326748, overlap = 209.219
PHY-3002 : Step(107): len = 325732, overlap = 211.656
PHY-3002 : Step(108): len = 325650, overlap = 195.094
PHY-3002 : Step(109): len = 325198, overlap = 200.375
PHY-3002 : Step(110): len = 325673, overlap = 193.438
PHY-3002 : Step(111): len = 325282, overlap = 193.656
PHY-3001 : :::11::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(112): len = 326230, overlap = 197.562
PHY-3002 : Step(113): len = 328553, overlap = 191.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.015817s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (98.8%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21907.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 441072, over cnt = 1211(3%), over = 5243, worst = 44
PHY-1001 : End global iterations;  0.900314s wall, 1.218750s user + 0.015625s system = 1.234375s CPU (137.1%)

PHY-1001 : Congestion index: top1 = 72.16, top5 = 51.82, top10 = 42.37, top15 = 37.01.
PHY-3001 : End congestion estimation;  1.142466s wall, 1.453125s user + 0.031250s system = 1.484375s CPU (129.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.991186s wall, 0.984375s user + 0.000000s system = 0.984375s CPU (99.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000114504
PHY-3002 : Step(114): len = 369637, overlap = 144.594
PHY-3002 : Step(115): len = 380739, overlap = 132.406
PHY-3002 : Step(116): len = 385318, overlap = 123.969
PHY-3002 : Step(117): len = 386686, overlap = 116.906
PHY-3002 : Step(118): len = 390807, overlap = 116.406
PHY-3002 : Step(119): len = 395702, overlap = 109.25
PHY-3002 : Step(120): len = 397867, overlap = 110.25
PHY-3002 : Step(121): len = 400201, overlap = 112.625
PHY-3002 : Step(122): len = 401636, overlap = 104.094
PHY-3002 : Step(123): len = 403994, overlap = 96.125
PHY-3002 : Step(124): len = 403875, overlap = 95.9062
PHY-3002 : Step(125): len = 402998, overlap = 94
PHY-3002 : Step(126): len = 402992, overlap = 95.9062
PHY-3002 : Step(127): len = 404739, overlap = 101.625
PHY-3002 : Step(128): len = 405699, overlap = 106.562
PHY-3002 : Step(129): len = 404916, overlap = 104.406
PHY-3002 : Step(130): len = 405860, overlap = 104.812
PHY-3002 : Step(131): len = 407742, overlap = 106.75
PHY-3002 : Step(132): len = 407499, overlap = 113.688
PHY-3002 : Step(133): len = 407073, overlap = 118.125
PHY-3002 : Step(134): len = 407921, overlap = 128.25
PHY-3002 : Step(135): len = 408477, overlap = 131.969
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000229007
PHY-3002 : Step(136): len = 408634, overlap = 129.781
PHY-3002 : Step(137): len = 410451, overlap = 130.969
PHY-3002 : Step(138): len = 411488, overlap = 132.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(139): len = 417400, overlap = 134.531
PHY-3002 : Step(140): len = 424490, overlap = 125.844
PHY-3002 : Step(141): len = 425408, overlap = 123.5
PHY-3002 : Step(142): len = 425186, overlap = 126.656
PHY-3002 : Step(143): len = 426281, overlap = 120.344
PHY-3002 : Step(144): len = 428388, overlap = 113.656
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 65/21907.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 499376, over cnt = 2187(6%), over = 9991, worst = 36
PHY-1001 : End global iterations;  1.138981s wall, 1.734375s user + 0.062500s system = 1.796875s CPU (157.8%)

PHY-1001 : Congestion index: top1 = 79.25, top5 = 58.98, top10 = 50.14, top15 = 44.70.
PHY-3001 : End congestion estimation;  1.469853s wall, 2.062500s user + 0.062500s system = 2.125000s CPU (144.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.036185s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.65525e-05
PHY-3002 : Step(145): len = 435456, overlap = 388.625
PHY-3002 : Step(146): len = 442000, overlap = 320.219
PHY-3002 : Step(147): len = 435821, overlap = 307.875
PHY-3002 : Step(148): len = 432800, overlap = 293.719
PHY-3002 : Step(149): len = 431161, overlap = 284.375
PHY-3002 : Step(150): len = 427010, overlap = 268.281
PHY-3002 : Step(151): len = 426506, overlap = 256.219
PHY-3002 : Step(152): len = 424592, overlap = 247.844
PHY-3002 : Step(153): len = 423532, overlap = 242.375
PHY-3002 : Step(154): len = 422729, overlap = 237.094
PHY-3002 : Step(155): len = 420604, overlap = 221.031
PHY-3002 : Step(156): len = 420381, overlap = 211
PHY-3002 : Step(157): len = 418080, overlap = 210.781
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000193105
PHY-3002 : Step(158): len = 418557, overlap = 202.469
PHY-3002 : Step(159): len = 421124, overlap = 201
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00038621
PHY-3002 : Step(160): len = 424759, overlap = 193.5
PHY-3002 : Step(161): len = 432784, overlap = 184.906
PHY-3002 : Step(162): len = 435598, overlap = 179.844
PHY-3002 : Step(163): len = 435643, overlap = 172.812
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00077242
PHY-3002 : Step(164): len = 436655, overlap = 169.219
PHY-3002 : Step(165): len = 444594, overlap = 160.344
PHY-3002 : Step(166): len = 454586, overlap = 142.25
PHY-3002 : Step(167): len = 455762, overlap = 136.312
PHY-3002 : Step(168): len = 454707, overlap = 135.25
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81105, tnet num: 21905, tinst num: 19440, tnode num: 114042, tedge num: 127673.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.697323s wall, 1.656250s user + 0.046875s system = 1.703125s CPU (100.3%)

RUN-1004 : used memory is 569 MB, reserved memory is 543 MB, peak memory is 702 MB
OPT-1001 : Total overflow 469.16 peak overflow 3.34
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 483/21907.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 540168, over cnt = 2528(7%), over = 8490, worst = 20
PHY-1001 : End global iterations;  1.459947s wall, 2.078125s user + 0.046875s system = 2.125000s CPU (145.6%)

PHY-1001 : Congestion index: top1 = 54.81, top5 = 45.37, top10 = 41.00, top15 = 38.30.
PHY-1001 : End incremental global routing;  1.762853s wall, 2.375000s user + 0.046875s system = 2.421875s CPU (137.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.136648s wall, 1.109375s user + 0.031250s system = 1.140625s CPU (100.3%)

OPT-1001 : 17 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19361 has valid locations, 236 needs to be replaced
PHY-3001 : design contains 19659 instances, 5704 luts, 12350 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 470165
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17161/22126.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 553184, over cnt = 2541(7%), over = 8556, worst = 20
PHY-1001 : End global iterations;  0.211449s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (140.4%)

PHY-1001 : Congestion index: top1 = 55.19, top5 = 45.85, top10 = 41.33, top15 = 38.64.
PHY-3001 : End congestion estimation;  0.623272s wall, 0.687500s user + 0.015625s system = 0.703125s CPU (112.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81838, tnet num: 22124, tinst num: 19659, tnode num: 115091, tedge num: 128701.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.722886s wall, 1.640625s user + 0.078125s system = 1.718750s CPU (99.8%)

RUN-1004 : used memory is 614 MB, reserved memory is 603 MB, peak memory is 705 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22124 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.863558s wall, 2.781250s user + 0.078125s system = 2.859375s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(169): len = 470409, overlap = 0.75
PHY-3002 : Step(170): len = 471963, overlap = 1
PHY-3002 : Step(171): len = 472850, overlap = 1.125
PHY-3002 : Step(172): len = 473785, overlap = 0.625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17187/22126.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 552480, over cnt = 2559(7%), over = 8625, worst = 20
PHY-1001 : End global iterations;  0.205146s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (159.9%)

PHY-1001 : Congestion index: top1 = 54.98, top5 = 45.86, top10 = 41.38, top15 = 38.76.
PHY-3001 : End congestion estimation;  0.481855s wall, 0.593750s user + 0.015625s system = 0.609375s CPU (126.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22124 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.089268s wall, 1.062500s user + 0.015625s system = 1.078125s CPU (99.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000477206
PHY-3002 : Step(173): len = 473808, overlap = 137.375
PHY-3002 : Step(174): len = 474358, overlap = 137.219
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000954413
PHY-3002 : Step(175): len = 474366, overlap = 137.375
PHY-3002 : Step(176): len = 474545, overlap = 137.875
PHY-3001 : Final: Len = 474545, Over = 137.875
PHY-3001 : End incremental placement;  6.031897s wall, 6.421875s user + 0.390625s system = 6.812500s CPU (112.9%)

OPT-1001 : Total overflow 473.75 peak overflow 3.34
OPT-1001 : End high-fanout net optimization;  9.523922s wall, 10.671875s user + 0.468750s system = 11.140625s CPU (117.0%)

OPT-1001 : Current memory(MB): used = 708, reserve = 688, peak = 725.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17194/22126.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 554408, over cnt = 2485(7%), over = 8074, worst = 20
PHY-1002 : len = 599832, over cnt = 1673(4%), over = 3731, worst = 17
PHY-1002 : len = 635416, over cnt = 537(1%), over = 1008, worst = 11
PHY-1002 : len = 641632, over cnt = 302(0%), over = 542, worst = 10
PHY-1002 : len = 651408, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.323622s wall, 2.093750s user + 0.046875s system = 2.140625s CPU (161.7%)

PHY-1001 : Congestion index: top1 = 48.34, top5 = 42.06, top10 = 39.03, top15 = 37.18.
OPT-1001 : End congestion update;  1.602955s wall, 2.359375s user + 0.046875s system = 2.406250s CPU (150.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22124 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.122689s wall, 1.109375s user + 0.015625s system = 1.125000s CPU (100.2%)

OPT-0007 : Start: WNS 3887 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.731865s wall, 3.468750s user + 0.062500s system = 3.531250s CPU (129.3%)

OPT-1001 : Current memory(MB): used = 684, reserve = 665, peak = 725.
OPT-1001 : End physical optimization;  14.297032s wall, 16.234375s user + 0.593750s system = 16.828125s CPU (117.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5704 LUT to BLE ...
SYN-4008 : Packed 5704 LUT and 2732 SEQ to BLE.
SYN-4003 : Packing 9618 remaining SEQ's ...
SYN-4005 : Packed 3352 SEQ with LUT/SLICE
SYN-4006 : 152 single LUT's are left
SYN-4006 : 6266 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11970/13841 primitive instances ...
PHY-3001 : End packing;  3.236682s wall, 3.234375s user + 0.000000s system = 3.234375s CPU (99.9%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8187 instances
RUN-1001 : 4041 mslices, 4041 lslices, 59 pads, 41 brams, 0 dsps
RUN-1001 : There are total 19448 nets
RUN-1001 : 13607 nets have 2 pins
RUN-1001 : 4441 nets have [3 - 5] pins
RUN-1001 : 874 nets have [6 - 10] pins
RUN-1001 : 374 nets have [11 - 20] pins
RUN-1001 : 143 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8185 instances, 8082 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Cell area utilization is 86%
PHY-3001 : After packing: Len = 491008, Over = 380.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7886/19448.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 620984, over cnt = 1553(4%), over = 2445, worst = 8
PHY-1002 : len = 628072, over cnt = 945(2%), over = 1293, worst = 8
PHY-1002 : len = 638288, over cnt = 385(1%), over = 502, worst = 8
PHY-1002 : len = 644656, over cnt = 113(0%), over = 147, worst = 6
PHY-1002 : len = 647616, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.332795s wall, 2.046875s user + 0.078125s system = 2.125000s CPU (159.4%)

PHY-1001 : Congestion index: top1 = 50.00, top5 = 42.85, top10 = 39.47, top15 = 37.34.
PHY-3001 : End congestion estimation;  1.690787s wall, 2.406250s user + 0.078125s system = 2.484375s CPU (146.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68034, tnet num: 19446, tinst num: 8185, tnode num: 92289, tedge num: 112266.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.063843s wall, 2.046875s user + 0.015625s system = 2.062500s CPU (99.9%)

RUN-1004 : used memory is 607 MB, reserved memory is 600 MB, peak memory is 725 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19446 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.060285s wall, 3.031250s user + 0.031250s system = 3.062500s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.85509e-05
PHY-3002 : Step(177): len = 494099, overlap = 348
PHY-3002 : Step(178): len = 492829, overlap = 363.5
PHY-3002 : Step(179): len = 492236, overlap = 374.5
PHY-3002 : Step(180): len = 492722, overlap = 380
PHY-3002 : Step(181): len = 491011, overlap = 389.25
PHY-3002 : Step(182): len = 489503, overlap = 391.5
PHY-3002 : Step(183): len = 489086, overlap = 384
PHY-3002 : Step(184): len = 486834, overlap = 386.25
PHY-3002 : Step(185): len = 485694, overlap = 388.75
PHY-3002 : Step(186): len = 484062, overlap = 399.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.71018e-05
PHY-3002 : Step(187): len = 488179, overlap = 380.25
PHY-3002 : Step(188): len = 490617, overlap = 371
PHY-3002 : Step(189): len = 491052, overlap = 368
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(190): len = 499183, overlap = 349.75
PHY-3002 : Step(191): len = 507625, overlap = 340.5
PHY-3002 : Step(192): len = 506418, overlap = 342
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.686147s wall, 0.703125s user + 0.843750s system = 1.546875s CPU (225.4%)

PHY-3001 : Trial Legalized: Len = 616379
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 547/19448.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 704568, over cnt = 2471(7%), over = 4152, worst = 8
PHY-1002 : len = 719384, over cnt = 1608(4%), over = 2357, worst = 8
PHY-1002 : len = 740872, over cnt = 615(1%), over = 870, worst = 6
PHY-1002 : len = 751112, over cnt = 194(0%), over = 269, worst = 4
PHY-1002 : len = 755624, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.998606s wall, 3.187500s user + 0.015625s system = 3.203125s CPU (160.3%)

PHY-1001 : Congestion index: top1 = 52.05, top5 = 46.48, top10 = 43.58, top15 = 41.65.
PHY-3001 : End congestion estimation;  2.384289s wall, 3.593750s user + 0.015625s system = 3.609375s CPU (151.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19446 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.966125s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000198629
PHY-3002 : Step(193): len = 573377, overlap = 88.25
PHY-3002 : Step(194): len = 554799, overlap = 132.25
PHY-3002 : Step(195): len = 543491, overlap = 176.75
PHY-3002 : Step(196): len = 536784, overlap = 210.75
PHY-3002 : Step(197): len = 533638, overlap = 234
PHY-3002 : Step(198): len = 531828, overlap = 241.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000397258
PHY-3002 : Step(199): len = 535280, overlap = 239.5
PHY-3002 : Step(200): len = 539979, overlap = 236
PHY-3002 : Step(201): len = 540998, overlap = 239
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(202): len = 543122, overlap = 238
PHY-3002 : Step(203): len = 549909, overlap = 233.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.059226s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.5%)

PHY-3001 : Legalized: Len = 592203, Over = 0
PHY-3001 : Spreading special nets. 44 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.088891s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (87.9%)

PHY-3001 : 66 instances has been re-located, deltaX = 20, deltaY = 40, maxDist = 2.
PHY-3001 : Final: Len = 593449, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68034, tnet num: 19446, tinst num: 8185, tnode num: 92289, tedge num: 112266.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.064843s wall, 2.015625s user + 0.046875s system = 2.062500s CPU (99.9%)

RUN-1004 : used memory is 609 MB, reserved memory is 592 MB, peak memory is 725 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3791/19448.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 690256, over cnt = 2258(6%), over = 3570, worst = 7
PHY-1002 : len = 699704, over cnt = 1511(4%), over = 2152, worst = 7
PHY-1002 : len = 719648, over cnt = 479(1%), over = 654, worst = 7
PHY-1002 : len = 729088, over cnt = 86(0%), over = 112, worst = 4
PHY-1002 : len = 731816, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.810336s wall, 2.953125s user + 0.031250s system = 2.984375s CPU (164.9%)

PHY-1001 : Congestion index: top1 = 49.48, top5 = 43.98, top10 = 41.26, top15 = 39.53.
PHY-1001 : End incremental global routing;  2.135433s wall, 3.281250s user + 0.031250s system = 3.312500s CPU (155.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19446 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.005057s wall, 0.968750s user + 0.031250s system = 1.000000s CPU (99.5%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8122 has valid locations, 9 needs to be replaced
PHY-3001 : design contains 8193 instances, 8090 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 594654
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17457/19455.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 732928, over cnt = 17(0%), over = 18, worst = 2
PHY-1002 : len = 732976, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 732968, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 733056, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 733056, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.705181s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (101.9%)

PHY-1001 : Congestion index: top1 = 49.78, top5 = 44.12, top10 = 41.33, top15 = 39.58.
PHY-3001 : End congestion estimation;  1.029705s wall, 1.046875s user + 0.000000s system = 1.046875s CPU (101.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68081, tnet num: 19453, tinst num: 8193, tnode num: 92344, tedge num: 112320.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.061949s wall, 2.046875s user + 0.000000s system = 2.046875s CPU (99.3%)

RUN-1004 : used memory is 673 MB, reserved memory is 653 MB, peak memory is 725 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19453 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.053892s wall, 3.046875s user + 0.000000s system = 3.046875s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(204): len = 594614, overlap = 0.25
PHY-3002 : Step(205): len = 594577, overlap = 0.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17456/19455.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 732800, over cnt = 5(0%), over = 7, worst = 2
PHY-1002 : len = 732792, over cnt = 3(0%), over = 4, worst = 2
PHY-1002 : len = 732856, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.390466s wall, 0.406250s user + 0.031250s system = 0.437500s CPU (112.0%)

PHY-1001 : Congestion index: top1 = 49.61, top5 = 43.99, top10 = 41.26, top15 = 39.54.
PHY-3001 : End congestion estimation;  0.703810s wall, 0.718750s user + 0.031250s system = 0.750000s CPU (106.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19453 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.025335s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000307492
PHY-3002 : Step(206): len = 594595, overlap = 1.75
PHY-3002 : Step(207): len = 594592, overlap = 1.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007225s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (216.3%)

PHY-3001 : Legalized: Len = 594632, Over = 0
PHY-3001 : End spreading;  0.073530s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (85.0%)

PHY-3001 : Final: Len = 594632, Over = 0
PHY-3001 : End incremental placement;  6.504216s wall, 6.687500s user + 0.062500s system = 6.750000s CPU (103.8%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.179733s wall, 11.656250s user + 0.125000s system = 11.781250s CPU (115.7%)

OPT-1001 : Current memory(MB): used = 719, reserve = 701, peak = 725.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17455/19455.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 732968, over cnt = 10(0%), over = 12, worst = 2
PHY-1002 : len = 732864, over cnt = 3(0%), over = 4, worst = 2
PHY-1002 : len = 732896, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 732944, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.521428s wall, 0.531250s user + 0.015625s system = 0.546875s CPU (104.9%)

PHY-1001 : Congestion index: top1 = 49.70, top5 = 43.98, top10 = 41.27, top15 = 39.54.
OPT-1001 : End congestion update;  0.833628s wall, 0.828125s user + 0.015625s system = 0.843750s CPU (101.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19453 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.808619s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (100.5%)

OPT-0007 : Start: WNS 3861 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.647568s wall, 1.640625s user + 0.015625s system = 1.656250s CPU (100.5%)

OPT-1001 : Current memory(MB): used = 720, reserve = 702, peak = 725.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19453 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.812142s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (100.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17465/19455.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 732944, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.117060s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (93.4%)

PHY-1001 : Congestion index: top1 = 49.70, top5 = 43.98, top10 = 41.27, top15 = 39.54.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19453 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.805261s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3861 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 49.310345
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3861ps with logic level 8 
RUN-1001 :       #2 path slack 3937ps with logic level 8 
OPT-1001 : End physical optimization;  16.206747s wall, 17.625000s user + 0.187500s system = 17.812500s CPU (109.9%)

RUN-1003 : finish command "place" in  73.130401s wall, 135.296875s user + 8.031250s system = 143.328125s CPU (196.0%)

RUN-1004 : used memory is 635 MB, reserved memory is 615 MB, peak memory is 725 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.678406s wall, 2.890625s user + 0.015625s system = 2.906250s CPU (173.2%)

RUN-1004 : used memory is 635 MB, reserved memory is 616 MB, peak memory is 725 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8195 instances
RUN-1001 : 4041 mslices, 4049 lslices, 59 pads, 41 brams, 0 dsps
RUN-1001 : There are total 19455 nets
RUN-1001 : 13607 nets have 2 pins
RUN-1001 : 4442 nets have [3 - 5] pins
RUN-1001 : 876 nets have [6 - 10] pins
RUN-1001 : 379 nets have [11 - 20] pins
RUN-1001 : 142 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68081, tnet num: 19453, tinst num: 8193, tnode num: 92344, tedge num: 112320.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.791586s wall, 1.796875s user + 0.000000s system = 1.796875s CPU (100.3%)

RUN-1004 : used memory is 620 MB, reserved memory is 600 MB, peak memory is 725 MB
PHY-1001 : 4041 mslices, 4049 lslices, 59 pads, 41 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19453 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 669648, over cnt = 2412(6%), over = 4014, worst = 9
PHY-1002 : len = 683880, over cnt = 1535(4%), over = 2292, worst = 7
PHY-1002 : len = 702968, over cnt = 655(1%), over = 919, worst = 5
PHY-1002 : len = 718544, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 718896, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.940157s wall, 3.265625s user + 0.062500s system = 3.328125s CPU (171.5%)

PHY-1001 : Congestion index: top1 = 49.20, top5 = 43.67, top10 = 40.90, top15 = 39.24.
PHY-1001 : End global routing;  2.298314s wall, 3.625000s user + 0.062500s system = 3.687500s CPU (160.4%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 701, reserve = 687, peak = 725.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 971, reserve = 958, peak = 971.
PHY-1001 : End build detailed router design. 4.786077s wall, 4.718750s user + 0.046875s system = 4.765625s CPU (99.6%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 188504, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.009104s wall, 1.000000s user + 0.015625s system = 1.015625s CPU (100.6%)

PHY-1001 : Current memory(MB): used = 1007, reserve = 994, peak = 1007.
PHY-1001 : End phase 1; 1.016524s wall, 1.000000s user + 0.015625s system = 1.015625s CPU (99.9%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 55% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.71158e+06, over cnt = 1625(0%), over = 1642, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1024, reserve = 1013, peak = 1024.
PHY-1001 : End initial routed; 15.780594s wall, 44.312500s user + 0.468750s system = 44.781250s CPU (283.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18183(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.281   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.557211s wall, 3.562500s user + 0.000000s system = 3.562500s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1035, reserve = 1024, peak = 1035.
PHY-1001 : End phase 2; 19.337974s wall, 47.875000s user + 0.468750s system = 48.343750s CPU (250.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.71158e+06, over cnt = 1625(0%), over = 1642, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.252535s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (99.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.69578e+06, over cnt = 562(0%), over = 562, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 1.462311s wall, 2.125000s user + 0.000000s system = 2.125000s CPU (145.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.6966e+06, over cnt = 124(0%), over = 124, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.544642s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (140.6%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.69732e+06, over cnt = 39(0%), over = 39, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.309531s wall, 0.312500s user + 0.031250s system = 0.343750s CPU (111.1%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.69786e+06, over cnt = 14(0%), over = 14, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.231604s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (94.5%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.69817e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.201496s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (100.8%)

PHY-1001 : ===== DR Iter 6 =====
PHY-1022 : len = 1.69825e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 6; 0.162335s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (105.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18183(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.139   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.652470s wall, 3.640625s user + 0.015625s system = 3.656250s CPU (100.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 392 feed throughs used by 323 nets
PHY-1001 : End commit to database; 2.214803s wall, 2.203125s user + 0.015625s system = 2.218750s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 1121, reserve = 1113, peak = 1121.
PHY-1001 : End phase 3; 9.520449s wall, 10.375000s user + 0.062500s system = 10.437500s CPU (109.6%)

PHY-1003 : Routed, final wirelength = 1.69825e+06
PHY-1001 : Current memory(MB): used = 1125, reserve = 1117, peak = 1125.
PHY-1001 : End export database. 0.062527s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (100.0%)

PHY-1001 : End detail routing;  35.159870s wall, 64.437500s user + 0.609375s system = 65.046875s CPU (185.0%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68081, tnet num: 19453, tinst num: 8193, tnode num: 92344, tedge num: 112320.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.739812s wall, 1.734375s user + 0.000000s system = 1.734375s CPU (99.7%)

RUN-1004 : used memory is 1059 MB, reserved memory is 1056 MB, peak memory is 1125 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  43.741968s wall, 74.343750s user + 0.671875s system = 75.015625s CPU (171.5%)

RUN-1004 : used memory is 1059 MB, reserved memory is 1057 MB, peak memory is 1125 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8875   out of  19600   45.28%
#reg                    12444   out of  19600   63.49%
#le                     15093
  #lut only              2649   out of  15093   17.55%
  #reg only              6218   out of  15093   41.20%
  #lut&reg               6226   out of  15093   41.25%
#dsp                        0   out of     29    0.00%
#bram                      41   out of     64   64.06%
  #bram9k                  39
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       59   out of    188   31.38%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6844
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          164
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         F2        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         D1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         B3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         C1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         A3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         A2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         B2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         B1        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F15        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDZ        INPUT        K15        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         P2        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         N4        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         M5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         N6        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         N5        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         P5        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M3        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         M4        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         H2        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         M1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         L1        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         N1        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         N3        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         M2        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT         T5        LVCMOS33           8            NONE           OREG       
    Fog_ENY        OUTPUT         T4        LVCMOS33           8            NONE           OREG       
    Fog_ENZ        OUTPUT         T7        LVCMOS33           8            NONE           OREG       
    Fog_INT        OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         T6        LVCMOS33           8            N/A            OREG       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E1        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        G11        LVCMOS25           8            N/A            NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15093  |7373    |1502    |12488   |41      |0       |
|  AnyFog_dataX                      |AnyFog          |209    |76      |22      |175     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |55      |22      |52      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |210    |75      |22      |174     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |53      |22      |50      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |208    |80      |22      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |85     |58      |22      |50      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2947   |639     |39      |2857    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |59     |35      |5       |50      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |213    |73      |5       |203     |0       |0       |
|    STADOP_com2                     |STADOP          |561    |113     |0       |554     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |47      |14      |42      |0       |0       |
|    head_com2                       |uniheading      |272    |68      |5       |254     |0       |0       |
|    rmc_com2                        |Gprmc           |39     |37      |0       |33      |0       |0       |
|    uart_com2                       |Agrica          |1420   |240     |10      |1403    |0       |0       |
|  COM3                              |COM3_Control    |276    |149     |19      |237     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |61     |40      |5       |52      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |61     |47      |14      |38      |0       |0       |
|    rmc_com3                        |Gprmc           |154    |62      |0       |147     |0       |0       |
|  DATA                              |Data_Processing |8828   |4511    |1122    |7050    |0       |0       |
|    DIV_Dtemp                       |Divider         |820    |325     |84      |698     |0       |0       |
|    DIV_Utemp                       |Divider         |599    |293     |84      |476     |0       |0       |
|    DIV_accX                        |Divider         |578    |320     |84      |455     |0       |0       |
|    DIV_accY                        |Divider         |648    |328     |102     |496     |0       |0       |
|    DIV_accZ                        |Divider         |685    |391     |132     |482     |0       |0       |
|    DIV_rateX                       |Divider         |636    |368     |132     |435     |0       |0       |
|    DIV_rateY                       |Divider         |582    |337     |132     |380     |0       |0       |
|    DIV_rateZ                       |Divider         |587    |374     |132     |381     |0       |0       |
|    genclk                          |genclk          |271    |172     |89      |111     |0       |0       |
|  FMC                               |FMC_Ctrl        |509    |453     |43      |355     |0       |0       |
|  IIC                               |I2C_master      |265    |224     |11      |237     |0       |0       |
|  IMU_CTRL                          |SCHA634         |905    |684     |61      |704     |0       |0       |
|    CtrlData                        |CtrlData        |494    |441     |47      |328     |0       |0       |
|      usms                          |Time_1ms        |27     |22      |5       |18      |0       |0       |
|    SPIM                            |SPI_SCHA634     |411    |243     |14      |376     |0       |0       |
|  POWER                             |POWER_EN        |101    |56      |38      |42      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |628    |426     |103     |433     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |628    |426     |103     |433     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |282    |202     |0       |265     |0       |0       |
|        reg_inst                    |register        |280    |200     |0       |263     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |346    |224     |103     |168     |0       |0       |
|        bus_inst                    |bus_top         |134    |88      |46      |51      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |28     |18      |10      |10      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |52     |34      |18      |20      |0       |0       |
|          BUS_DETECTOR[5]$bus_nodes |bus_det         |51     |33      |18      |18      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |133    |99      |29      |85      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13547  
    #2          2       3438   
    #3          3        683   
    #4          4        321   
    #5        5-10       956   
    #6        11-50      432   
    #7       51-100       9    
    #8       101-500      3    
    #9        >500        2    
  Average     2.14             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.128409s wall, 3.656250s user + 0.000000s system = 3.656250s CPU (171.8%)

RUN-1004 : used memory is 1059 MB, reserved memory is 1059 MB, peak memory is 1125 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68081, tnet num: 19453, tinst num: 8193, tnode num: 92344, tedge num: 112320.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.777787s wall, 1.765625s user + 0.000000s system = 1.765625s CPU (99.3%)

RUN-1004 : used memory is 1061 MB, reserved memory is 1060 MB, peak memory is 1125 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19453 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.489077s wall, 1.484375s user + 0.015625s system = 1.500000s CPU (100.7%)

RUN-1004 : used memory is 1066 MB, reserved memory is 1064 MB, peak memory is 1125 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: e7acc41f3f3c0493db2e7c96172f62b19e1071d28814331b86e973fbb54fbf81 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8193
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19455, pip num: 147220
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 392
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3226 valid insts, and 413366 bits set as '1'.
BIT-1004 : the usercode register value: 00000000000000111011100100001111
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.104596s wall, 121.593750s user + 0.156250s system = 121.750000s CPU (1005.8%)

RUN-1004 : used memory is 1192 MB, reserved memory is 1177 MB, peak memory is 1307 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250820_151835.log"

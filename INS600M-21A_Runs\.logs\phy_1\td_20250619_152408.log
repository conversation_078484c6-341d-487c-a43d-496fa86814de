============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Jun 19 15:24:08 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(516)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
RUN-1001 : Project manager successfully analyzed 22 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.233069s wall, 1.531250s user + 3.671875s system = 5.203125s CPU (99.4%)

RUN-1004 : used memory is 78 MB, reserved memory is 40 MB, peak memory is 89 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.715668s wall, 1.593750s user + 0.109375s system = 1.703125s CPU (99.3%)

RUN-1004 : used memory is 299 MB, reserved memory is 269 MB, peak memory is 302 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 58 trigger nets, 58 data nets.
KIT-1004 : Chipwatcher code = 1110111010000001
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=158) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=158) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=158)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=158)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 23133/23 useful/useless nets, 19837/12 useful/useless insts
SYN-1016 : Merged 28 instances.
SYN-1032 : 22738/20 useful/useless nets, 20344/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 478 better
SYN-1014 : Optimize round 2
SYN-1032 : 22354/45 useful/useless nets, 19960/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.351308s wall, 2.296875s user + 0.046875s system = 2.343750s CPU (99.7%)

RUN-1004 : used memory is 327 MB, reserved memory is 296 MB, peak memory is 329 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22426/441 useful/useless nets, 20083/63 useful/useless insts
SYN-1016 : Merged 69 instances.
SYN-2571 : Optimize after map_dsp, round 1, 573 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 56 instances.
SYN-2501 : Optimize round 1, 114 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 22930/5 useful/useless nets, 20587/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 84632, tnet num: 22930, tinst num: 20586, tnode num: 118625, tedge num: 131866.
TMR-2508 : Levelizing timing graph completed, there are 85 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.202299s wall, 1.171875s user + 0.031250s system = 1.203125s CPU (100.1%)

RUN-1004 : used memory is 472 MB, reserved memory is 441 MB, peak memory is 472 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22930 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 282 (3.29), #lev = 7 (1.66)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 282 (3.29), #lev = 7 (1.66)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 614 instances into 282 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 487 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 160 adder to BLE ...
SYN-4008 : Packed 160 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.448742s wall, 4.359375s user + 0.109375s system = 4.468750s CPU (100.4%)

RUN-1004 : used memory is 355 MB, reserved memory is 324 MB, peak memory is 583 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.112781s wall, 6.953125s user + 0.171875s system = 7.125000s CPU (100.2%)

RUN-1004 : used memory is 356 MB, reserved memory is 325 MB, peak memory is 583 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[7] will be merged to another kept net COM3/rmc_com3/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[6] will be merged to another kept net COM3/rmc_com3/GPRMC_data[6]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[5] will be merged to another kept net COM3/rmc_com3/GPRMC_data[5]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[4] will be merged to another kept net COM3/rmc_com3/GPRMC_data[4]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[3] will be merged to another kept net COM3/rmc_com3/GPRMC_data[3]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[2] will be merged to another kept net COM3/rmc_com3/GPRMC_data[2]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[1] will be merged to another kept net COM3/rmc_com3/GPRMC_data[1]
SYN-5055 WARNING: The kept net FMC/Error_state[0] will be merged to another kept net COM3/rmc_com3/GPRMC_sub_state_cnt0[3]
SYN-5055 WARNING: The kept net COM3/rmc_com3/GPRMC_sub_state_cnt0[3] will be merged to another kept net COM3/rmc_com3/GPRMC_sub_state_cnt0[2]
SYN-5055 WARNING: The kept net COM3/rmc_com3/GPRMC_sub_state_cnt0[2] will be merged to another kept net COM3/rmc_com3/GPRMC_sub_state_cnt0[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (335 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19911 instances
RUN-0007 : 5772 luts, 12577 seqs, 951 mslices, 494 lslices, 60 pads, 52 brams, 0 dsps
RUN-1001 : There are total 22278 nets
RUN-1001 : 16651 nets have 2 pins
RUN-1001 : 4439 nets have [3 - 5] pins
RUN-1001 : 793 nets have [6 - 10] pins
RUN-1001 : 265 nets have [11 - 20] pins
RUN-1001 : 106 nets have [21 - 99] pins
RUN-1001 : 24 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4744     
RUN-1001 :   No   |  No   |  Yes  |     670     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    6556     
RUN-1001 :   Yes  |  No   |  Yes  |     507     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  119  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 127
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19909 instances, 5772 luts, 12577 seqs, 1445 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1131 pins
PHY-3001 : Huge net DATA/done_div with 1644 pins
PHY-0007 : Cell area utilization is 64%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83159, tnet num: 22276, tinst num: 19909, tnode num: 117287, tedge num: 130712.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.214351s wall, 1.203125s user + 0.015625s system = 1.218750s CPU (100.4%)

RUN-1004 : used memory is 533 MB, reserved memory is 506 MB, peak memory is 583 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22276 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.072713s wall, 2.000000s user + 0.046875s system = 2.046875s CPU (98.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.47011e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19909.
PHY-3001 : Level 1 #clusters 2112.
PHY-3001 : End clustering;  0.151285s wall, 0.281250s user + 0.031250s system = 0.312500s CPU (206.6%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 64%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 921732, overlap = 618.969
PHY-3002 : Step(2): len = 837111, overlap = 681.812
PHY-3002 : Step(3): len = 837111, overlap = 681.812
PHY-3002 : Step(4): len = 528748, overlap = 877.75
PHY-3002 : Step(5): len = 487199, overlap = 914.906
PHY-3002 : Step(6): len = 374002, overlap = 1004.22
PHY-3002 : Step(7): len = 349840, overlap = 1061.19
PHY-3002 : Step(8): len = 290627, overlap = 1149.31
PHY-3002 : Step(9): len = 269707, overlap = 1200.91
PHY-3002 : Step(10): len = 241109, overlap = 1278.69
PHY-3002 : Step(11): len = 224650, overlap = 1328.91
PHY-3002 : Step(12): len = 194775, overlap = 1392.41
PHY-3002 : Step(13): len = 180334, overlap = 1415.5
PHY-3002 : Step(14): len = 161132, overlap = 1454.31
PHY-3002 : Step(15): len = 151404, overlap = 1496.91
PHY-3002 : Step(16): len = 139322, overlap = 1525.22
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.14078e-06
PHY-3002 : Step(17): len = 140525, overlap = 1508.19
PHY-3002 : Step(18): len = 181468, overlap = 1433.25
PHY-3002 : Step(19): len = 194663, overlap = 1317.09
PHY-3002 : Step(20): len = 197451, overlap = 1275.97
PHY-3002 : Step(21): len = 195449, overlap = 1241.31
PHY-3002 : Step(22): len = 192204, overlap = 1207.69
PHY-3002 : Step(23): len = 189575, overlap = 1199.66
PHY-3002 : Step(24): len = 184392, overlap = 1211.81
PHY-3002 : Step(25): len = 182265, overlap = 1209.88
PHY-3002 : Step(26): len = 179420, overlap = 1191.34
PHY-3002 : Step(27): len = 177986, overlap = 1167.12
PHY-3002 : Step(28): len = 176774, overlap = 1164.84
PHY-3002 : Step(29): len = 176271, overlap = 1153.97
PHY-3002 : Step(30): len = 174332, overlap = 1176.69
PHY-3002 : Step(31): len = 173618, overlap = 1181.09
PHY-3002 : Step(32): len = 173059, overlap = 1197.22
PHY-3002 : Step(33): len = 172090, overlap = 1187.19
PHY-3002 : Step(34): len = 172022, overlap = 1177.56
PHY-3002 : Step(35): len = 171844, overlap = 1157.19
PHY-3002 : Step(36): len = 172475, overlap = 1154.97
PHY-3002 : Step(37): len = 171636, overlap = 1178.12
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.28157e-06
PHY-3002 : Step(38): len = 178289, overlap = 1129.28
PHY-3002 : Step(39): len = 190109, overlap = 1059.78
PHY-3002 : Step(40): len = 193067, overlap = 1016.97
PHY-3002 : Step(41): len = 196187, overlap = 991.344
PHY-3002 : Step(42): len = 197866, overlap = 954.344
PHY-3002 : Step(43): len = 197446, overlap = 921.344
PHY-3002 : Step(44): len = 196757, overlap = 911.375
PHY-3002 : Step(45): len = 195356, overlap = 908.219
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.56313e-06
PHY-3002 : Step(46): len = 203252, overlap = 888.688
PHY-3002 : Step(47): len = 217502, overlap = 789.188
PHY-3002 : Step(48): len = 223045, overlap = 754.031
PHY-3002 : Step(49): len = 224996, overlap = 757.25
PHY-3002 : Step(50): len = 225313, overlap = 744.375
PHY-3002 : Step(51): len = 224892, overlap = 735.438
PHY-3002 : Step(52): len = 224810, overlap = 742.625
PHY-3002 : Step(53): len = 223622, overlap = 747.875
PHY-3002 : Step(54): len = 222127, overlap = 754.875
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 9.12627e-06
PHY-3002 : Step(55): len = 231070, overlap = 726.031
PHY-3002 : Step(56): len = 243965, overlap = 719.312
PHY-3002 : Step(57): len = 247332, overlap = 719.656
PHY-3002 : Step(58): len = 248809, overlap = 724.75
PHY-3002 : Step(59): len = 249489, overlap = 711.25
PHY-3002 : Step(60): len = 249777, overlap = 695.375
PHY-3002 : Step(61): len = 248560, overlap = 687.438
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.82525e-05
PHY-3002 : Step(62): len = 259708, overlap = 611.25
PHY-3002 : Step(63): len = 275136, overlap = 542.844
PHY-3002 : Step(64): len = 280509, overlap = 524.75
PHY-3002 : Step(65): len = 281637, overlap = 530
PHY-3002 : Step(66): len = 280271, overlap = 538.688
PHY-3002 : Step(67): len = 278226, overlap = 526.25
PHY-3002 : Step(68): len = 275020, overlap = 536.625
PHY-3002 : Step(69): len = 273520, overlap = 536.656
PHY-3002 : Step(70): len = 272563, overlap = 542.594
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.65051e-05
PHY-3002 : Step(71): len = 280949, overlap = 544.656
PHY-3002 : Step(72): len = 288542, overlap = 512.688
PHY-3002 : Step(73): len = 292402, overlap = 481.5
PHY-3002 : Step(74): len = 294918, overlap = 466.219
PHY-3002 : Step(75): len = 294275, overlap = 443.938
PHY-3002 : Step(76): len = 294031, overlap = 441.719
PHY-3002 : Step(77): len = 292947, overlap = 442.719
PHY-3002 : Step(78): len = 291567, overlap = 425.375
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 7.30101e-05
PHY-3002 : Step(79): len = 296505, overlap = 423.688
PHY-3002 : Step(80): len = 303565, overlap = 382.875
PHY-3002 : Step(81): len = 305700, overlap = 371.5
PHY-3002 : Step(82): len = 306288, overlap = 356.719
PHY-3002 : Step(83): len = 306053, overlap = 368.5
PHY-3002 : Step(84): len = 305387, overlap = 361.406
PHY-3002 : Step(85): len = 304894, overlap = 361.156
PHY-3002 : Step(86): len = 304617, overlap = 359.312
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000142063
PHY-3002 : Step(87): len = 307332, overlap = 367.188
PHY-3002 : Step(88): len = 311986, overlap = 335.156
PHY-3002 : Step(89): len = 315002, overlap = 317.062
PHY-3002 : Step(90): len = 316461, overlap = 306.344
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000255159
PHY-3002 : Step(91): len = 318148, overlap = 294.844
PHY-3002 : Step(92): len = 322598, overlap = 289.75
PHY-3002 : Step(93): len = 324399, overlap = 279.375
PHY-3002 : Step(94): len = 328489, overlap = 266.844
PHY-3002 : Step(95): len = 329185, overlap = 256.281
PHY-3002 : Step(96): len = 328596, overlap = 266.219
PHY-3002 : Step(97): len = 327297, overlap = 277.719
PHY-3002 : Step(98): len = 327703, overlap = 268.844
PHY-3002 : Step(99): len = 327239, overlap = 259.969
PHY-3002 : Step(100): len = 328710, overlap = 265.781
PHY-3002 : Step(101): len = 329191, overlap = 273.062
PHY-3002 : Step(102): len = 330058, overlap = 256.5
PHY-3002 : Step(103): len = 330929, overlap = 254.531
PHY-3002 : Step(104): len = 330885, overlap = 263.531
PHY-3002 : Step(105): len = 329979, overlap = 259.562
PHY-3002 : Step(106): len = 330458, overlap = 246.188
PHY-3002 : Step(107): len = 329716, overlap = 243.875
PHY-3002 : Step(108): len = 329885, overlap = 251.156
PHY-3002 : Step(109): len = 329038, overlap = 241.75
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(110): len = 329830, overlap = 247.031
PHY-3002 : Step(111): len = 332388, overlap = 242.156
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.012657s wall, 0.015625s user + 0.031250s system = 0.046875s CPU (370.4%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/22278.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 441608, over cnt = 1294(3%), over = 5709, worst = 46
PHY-1001 : End global iterations;  0.869478s wall, 1.109375s user + 0.046875s system = 1.156250s CPU (133.0%)

PHY-1001 : Congestion index: top1 = 74.38, top5 = 53.69, top10 = 44.07, top15 = 38.49.
PHY-3001 : End congestion estimation;  1.110142s wall, 1.312500s user + 0.078125s system = 1.390625s CPU (125.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22276 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.861147s wall, 0.843750s user + 0.031250s system = 0.875000s CPU (101.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.31381e-05
PHY-3002 : Step(112): len = 373100, overlap = 146.625
PHY-3002 : Step(113): len = 389932, overlap = 129.719
PHY-3002 : Step(114): len = 388150, overlap = 126.344
PHY-3002 : Step(115): len = 386837, overlap = 119.031
PHY-3002 : Step(116): len = 393649, overlap = 113.438
PHY-3002 : Step(117): len = 398777, overlap = 110.094
PHY-3002 : Step(118): len = 401330, overlap = 111.875
PHY-3002 : Step(119): len = 406073, overlap = 122.031
PHY-3002 : Step(120): len = 409757, overlap = 125.625
PHY-3002 : Step(121): len = 414143, overlap = 126
PHY-3002 : Step(122): len = 416346, overlap = 126.656
PHY-3002 : Step(123): len = 417179, overlap = 124.594
PHY-3002 : Step(124): len = 421394, overlap = 125.5
PHY-3002 : Step(125): len = 422384, overlap = 125.406
PHY-3002 : Step(126): len = 425185, overlap = 124.094
PHY-3002 : Step(127): len = 424750, overlap = 125.312
PHY-3002 : Step(128): len = 424780, overlap = 128.875
PHY-3002 : Step(129): len = 426002, overlap = 126.438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000186276
PHY-3002 : Step(130): len = 424881, overlap = 122.375
PHY-3002 : Step(131): len = 426785, overlap = 122.719
PHY-3002 : Step(132): len = 430497, overlap = 120.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000372552
PHY-3002 : Step(133): len = 436868, overlap = 111.594
PHY-3002 : Step(134): len = 446741, overlap = 100.344
PHY-3002 : Step(135): len = 455113, overlap = 96
PHY-3002 : Step(136): len = 456754, overlap = 96.7812
PHY-3002 : Step(137): len = 459749, overlap = 100.938
PHY-3002 : Step(138): len = 461408, overlap = 107.281
PHY-3002 : Step(139): len = 461696, overlap = 104.406
PHY-3002 : Step(140): len = 461913, overlap = 105.562
PHY-3002 : Step(141): len = 462226, overlap = 106.312
PHY-3002 : Step(142): len = 460376, overlap = 108.031
PHY-3002 : Step(143): len = 460441, overlap = 104.625
PHY-3002 : Step(144): len = 460014, overlap = 104.031
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000745105
PHY-3002 : Step(145): len = 460372, overlap = 104.656
PHY-3002 : Step(146): len = 463852, overlap = 102.25
PHY-3002 : Step(147): len = 469634, overlap = 102.031
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.0014478
PHY-3002 : Step(148): len = 469783, overlap = 104.062
PHY-3002 : Step(149): len = 480976, overlap = 99.25
PHY-3002 : Step(150): len = 497584, overlap = 95.8125
PHY-3002 : Step(151): len = 496514, overlap = 95.1875
PHY-3002 : Step(152): len = 495289, overlap = 90.4375
PHY-3002 : Step(153): len = 493923, overlap = 91.625
PHY-3002 : Step(154): len = 491408, overlap = 92.375
PHY-3002 : Step(155): len = 490294, overlap = 90.1875
PHY-3002 : Step(156): len = 490349, overlap = 89.875
PHY-3002 : Step(157): len = 492035, overlap = 91.1875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 47/22278.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 561304, over cnt = 2250(6%), over = 10745, worst = 44
PHY-1001 : End global iterations;  1.091836s wall, 1.593750s user + 0.015625s system = 1.609375s CPU (147.4%)

PHY-1001 : Congestion index: top1 = 81.66, top5 = 61.05, top10 = 51.86, top15 = 46.67.
PHY-3001 : End congestion estimation;  1.335244s wall, 1.843750s user + 0.015625s system = 1.859375s CPU (139.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22276 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.928003s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (101.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000120094
PHY-3002 : Step(158): len = 493355, overlap = 326.406
PHY-3002 : Step(159): len = 490776, overlap = 267.656
PHY-3002 : Step(160): len = 485283, overlap = 243.719
PHY-3002 : Step(161): len = 479778, overlap = 213.688
PHY-3002 : Step(162): len = 475373, overlap = 190.031
PHY-3002 : Step(163): len = 471242, overlap = 188.469
PHY-3002 : Step(164): len = 466861, overlap = 190.156
PHY-3002 : Step(165): len = 464191, overlap = 203.375
PHY-3002 : Step(166): len = 461751, overlap = 201.594
PHY-3002 : Step(167): len = 458334, overlap = 206.656
PHY-3002 : Step(168): len = 457771, overlap = 199.75
PHY-3002 : Step(169): len = 455158, overlap = 202.281
PHY-3002 : Step(170): len = 451707, overlap = 202.031
PHY-3002 : Step(171): len = 449728, overlap = 208.594
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000240187
PHY-3002 : Step(172): len = 450137, overlap = 203.875
PHY-3002 : Step(173): len = 452380, overlap = 194.719
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000480374
PHY-3002 : Step(174): len = 453841, overlap = 191.688
PHY-3002 : Step(175): len = 462353, overlap = 180.625
PHY-3002 : Step(176): len = 467034, overlap = 165.781
PHY-3002 : Step(177): len = 466160, overlap = 175.094
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000960749
PHY-3002 : Step(178): len = 466814, overlap = 166.625
PHY-3002 : Step(179): len = 473415, overlap = 162.469
PHY-3002 : Step(180): len = 478428, overlap = 163.906
PHY-3002 : Step(181): len = 478584, overlap = 154.969
PHY-3002 : Step(182): len = 478471, overlap = 154.062
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00177951
PHY-3002 : Step(183): len = 479148, overlap = 157.75
PHY-3002 : Step(184): len = 481504, overlap = 156.656
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83159, tnet num: 22276, tinst num: 19909, tnode num: 117287, tedge num: 130712.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.428151s wall, 1.390625s user + 0.031250s system = 1.421875s CPU (99.6%)

RUN-1004 : used memory is 576 MB, reserved memory is 553 MB, peak memory is 712 MB
OPT-1001 : Total overflow 515.78 peak overflow 3.41
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 446/22278.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 572224, over cnt = 2601(7%), over = 8955, worst = 30
PHY-1001 : End global iterations;  1.189023s wall, 1.812500s user + 0.031250s system = 1.843750s CPU (155.1%)

PHY-1001 : Congestion index: top1 = 58.00, top5 = 47.97, top10 = 43.21, top15 = 40.16.
PHY-1001 : End incremental global routing;  1.408064s wall, 2.031250s user + 0.031250s system = 2.062500s CPU (146.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22276 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.894188s wall, 0.843750s user + 0.046875s system = 0.890625s CPU (99.6%)

OPT-1001 : 20 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19826 has valid locations, 306 needs to be replaced
PHY-3001 : design contains 20195 instances, 5918 luts, 12717 seqs, 1445 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 500821
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17536/22564.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 591640, over cnt = 2644(7%), over = 9107, worst = 30
PHY-1001 : End global iterations;  0.197135s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (111.0%)

PHY-1001 : Congestion index: top1 = 58.04, top5 = 48.18, top10 = 43.49, top15 = 40.55.
PHY-3001 : End congestion estimation;  0.415466s wall, 0.421875s user + 0.015625s system = 0.437500s CPU (105.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 84180, tnet num: 22562, tinst num: 20195, tnode num: 118649, tedge num: 132182.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.457272s wall, 1.421875s user + 0.031250s system = 1.453125s CPU (99.7%)

RUN-1004 : used memory is 622 MB, reserved memory is 615 MB, peak memory is 715 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22562 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.382775s wall, 2.312500s user + 0.062500s system = 2.375000s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(185): len = 501040, overlap = 1.1875
PHY-3002 : Step(186): len = 502479, overlap = 1.1875
PHY-3002 : Step(187): len = 503016, overlap = 1.1875
PHY-3002 : Step(188): len = 503605, overlap = 1.1875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17583/22564.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 587320, over cnt = 2662(7%), over = 9228, worst = 30
PHY-1001 : End global iterations;  0.189369s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (132.0%)

PHY-1001 : Congestion index: top1 = 58.43, top5 = 48.51, top10 = 43.69, top15 = 40.75.
PHY-3001 : End congestion estimation;  0.415397s wall, 0.437500s user + 0.031250s system = 0.468750s CPU (112.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22562 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.911615s wall, 0.890625s user + 0.015625s system = 0.906250s CPU (99.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.0007597
PHY-3002 : Step(189): len = 503761, overlap = 159.156
PHY-3002 : Step(190): len = 504351, overlap = 158.875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0015194
PHY-3002 : Step(191): len = 504638, overlap = 159.031
PHY-3002 : Step(192): len = 505322, overlap = 158.844
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00303231
PHY-3002 : Step(193): len = 505160, overlap = 158.938
PHY-3002 : Step(194): len = 505415, overlap = 158.219
PHY-3001 : Final: Len = 505415, Over = 158.219
PHY-3001 : End incremental placement;  5.162965s wall, 4.937500s user + 0.296875s system = 5.234375s CPU (101.4%)

OPT-1001 : Total overflow 521.69 peak overflow 3.41
OPT-1001 : End high-fanout net optimization;  7.958444s wall, 8.484375s user + 0.375000s system = 8.859375s CPU (111.3%)

OPT-1001 : Current memory(MB): used = 719, reserve = 700, peak = 736.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17575/22564.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 591200, over cnt = 2621(7%), over = 8623, worst = 30
PHY-1002 : len = 623584, over cnt = 1943(5%), over = 5267, worst = 18
PHY-1002 : len = 649576, over cnt = 1210(3%), over = 3310, worst = 18
PHY-1002 : len = 661640, over cnt = 883(2%), over = 2466, worst = 18
PHY-1002 : len = 700968, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.199729s wall, 1.687500s user + 0.015625s system = 1.703125s CPU (142.0%)

PHY-1001 : Congestion index: top1 = 49.76, top5 = 43.81, top10 = 40.87, top15 = 39.01.
OPT-1001 : End congestion update;  1.446129s wall, 1.921875s user + 0.015625s system = 1.937500s CPU (134.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22562 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.822512s wall, 0.812500s user + 0.015625s system = 0.828125s CPU (100.7%)

OPT-0007 : Start: WNS 4403 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.275293s wall, 2.734375s user + 0.031250s system = 2.765625s CPU (121.6%)

OPT-1001 : Current memory(MB): used = 695, reserve = 680, peak = 736.
OPT-1001 : End physical optimization;  11.964465s wall, 13.062500s user + 0.453125s system = 13.515625s CPU (113.0%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5918 LUT to BLE ...
SYN-4008 : Packed 5918 LUT and 2907 SEQ to BLE.
SYN-4003 : Packing 9810 remaining SEQ's ...
SYN-4005 : Packed 3416 SEQ with LUT/SLICE
SYN-4006 : 106 single LUT's are left
SYN-4006 : 6394 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 12312/13994 primitive instances ...
PHY-3001 : End packing;  2.797879s wall, 2.796875s user + 0.000000s system = 2.796875s CPU (100.0%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8310 instances
RUN-1001 : 4096 mslices, 4097 lslices, 60 pads, 52 brams, 0 dsps
RUN-1001 : There are total 19706 nets
RUN-1001 : 13723 nets have 2 pins
RUN-1001 : 4548 nets have [3 - 5] pins
RUN-1001 : 885 nets have [6 - 10] pins
RUN-1001 : 396 nets have [11 - 20] pins
RUN-1001 : 144 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
PHY-3001 : design contains 8308 instances, 8193 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Cell area utilization is 87%
PHY-3001 : After packing: Len = 521751, Over = 371.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7917/19706.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 657888, over cnt = 1651(4%), over = 2613, worst = 7
PHY-1002 : len = 663752, over cnt = 1113(3%), over = 1538, worst = 7
PHY-1002 : len = 673384, over cnt = 537(1%), over = 713, worst = 7
PHY-1002 : len = 683216, over cnt = 119(0%), over = 162, worst = 7
PHY-1002 : len = 686512, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.176775s wall, 1.796875s user + 0.093750s system = 1.890625s CPU (160.7%)

PHY-1001 : Congestion index: top1 = 51.96, top5 = 44.54, top10 = 41.02, top15 = 38.82.
PHY-3001 : End congestion estimation;  1.463068s wall, 2.093750s user + 0.093750s system = 2.187500s CPU (149.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69511, tnet num: 19704, tinst num: 8308, tnode num: 94592, tedge num: 114466.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.666070s wall, 1.640625s user + 0.031250s system = 1.671875s CPU (100.3%)

RUN-1004 : used memory is 612 MB, reserved memory is 603 MB, peak memory is 736 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19704 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.723999s wall, 2.656250s user + 0.062500s system = 2.718750s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.9814e-05
PHY-3002 : Step(195): len = 522488, overlap = 355.75
PHY-3002 : Step(196): len = 518725, overlap = 359.25
PHY-3002 : Step(197): len = 515869, overlap = 358.5
PHY-3002 : Step(198): len = 515588, overlap = 376
PHY-3002 : Step(199): len = 515069, overlap = 395
PHY-3002 : Step(200): len = 513881, overlap = 396.75
PHY-3002 : Step(201): len = 512851, overlap = 403.5
PHY-3002 : Step(202): len = 510887, overlap = 402.5
PHY-3002 : Step(203): len = 508186, overlap = 395.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.96281e-05
PHY-3002 : Step(204): len = 512208, overlap = 390.75
PHY-3002 : Step(205): len = 514746, overlap = 388.5
PHY-3002 : Step(206): len = 514320, overlap = 386
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000188536
PHY-3002 : Step(207): len = 521830, overlap = 383.25
PHY-3002 : Step(208): len = 531119, overlap = 366.75
PHY-3002 : Step(209): len = 529084, overlap = 365.25
PHY-3002 : Step(210): len = 528039, overlap = 358.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.571196s wall, 0.609375s user + 0.812500s system = 1.421875s CPU (248.9%)

PHY-3001 : Trial Legalized: Len = 646566
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 508/19706.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 742144, over cnt = 2555(7%), over = 4141, worst = 7
PHY-1002 : len = 756752, over cnt = 1608(4%), over = 2301, worst = 7
PHY-1002 : len = 775312, over cnt = 634(1%), over = 912, worst = 7
PHY-1002 : len = 789184, over cnt = 107(0%), over = 120, worst = 3
PHY-1002 : len = 791600, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.916088s wall, 3.062500s user + 0.031250s system = 3.093750s CPU (161.5%)

PHY-1001 : Congestion index: top1 = 51.44, top5 = 46.42, top10 = 43.62, top15 = 41.84.
PHY-3001 : End congestion estimation;  2.262775s wall, 3.406250s user + 0.031250s system = 3.437500s CPU (151.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19704 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.816337s wall, 0.812500s user + 0.015625s system = 0.828125s CPU (101.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000200103
PHY-3002 : Step(211): len = 601482, overlap = 80.75
PHY-3002 : Step(212): len = 582827, overlap = 128.75
PHY-3002 : Step(213): len = 570222, overlap = 170.25
PHY-3002 : Step(214): len = 562750, overlap = 211.5
PHY-3002 : Step(215): len = 558567, overlap = 249.75
PHY-3002 : Step(216): len = 555980, overlap = 264.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000400205
PHY-3002 : Step(217): len = 560753, overlap = 257.5
PHY-3002 : Step(218): len = 564897, overlap = 256.25
PHY-3002 : Step(219): len = 563025, overlap = 259.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(220): len = 566536, overlap = 252.25
PHY-3002 : Step(221): len = 571839, overlap = 249
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.027787s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (112.5%)

PHY-3001 : Legalized: Len = 614128, Over = 0
PHY-3001 : Spreading special nets. 55 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.093764s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (100.0%)

PHY-3001 : 80 instances has been re-located, deltaX = 44, deltaY = 44, maxDist = 8.
PHY-3001 : Final: Len = 615716, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69511, tnet num: 19704, tinst num: 8308, tnode num: 94592, tedge num: 114466.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.851612s wall, 1.828125s user + 0.015625s system = 1.843750s CPU (99.6%)

RUN-1004 : used memory is 629 MB, reserved memory is 636 MB, peak memory is 736 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3976/19706.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 722896, over cnt = 2333(6%), over = 3688, worst = 8
PHY-1002 : len = 734624, over cnt = 1444(4%), over = 1996, worst = 6
PHY-1002 : len = 748504, over cnt = 655(1%), over = 944, worst = 6
PHY-1002 : len = 754480, over cnt = 410(1%), over = 582, worst = 6
PHY-1002 : len = 764544, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.640172s wall, 2.562500s user + 0.031250s system = 2.593750s CPU (158.1%)

PHY-1001 : Congestion index: top1 = 49.38, top5 = 44.32, top10 = 41.82, top15 = 40.12.
PHY-1001 : End incremental global routing;  1.909937s wall, 2.843750s user + 0.031250s system = 2.875000s CPU (150.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19704 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.843167s wall, 0.828125s user + 0.015625s system = 0.843750s CPU (100.1%)

OPT-1001 : 4 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8241 has valid locations, 26 needs to be replaced
PHY-3001 : design contains 8330 instances, 8215 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 620777
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17757/19738.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 771136, over cnt = 67(0%), over = 76, worst = 3
PHY-1002 : len = 770976, over cnt = 51(0%), over = 54, worst = 2
PHY-1002 : len = 771296, over cnt = 27(0%), over = 28, worst = 2
PHY-1002 : len = 771712, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 771832, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.679949s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (103.4%)

PHY-1001 : Congestion index: top1 = 49.40, top5 = 44.47, top10 = 41.97, top15 = 40.32.
PHY-3001 : End congestion estimation;  0.950595s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (101.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69706, tnet num: 19736, tinst num: 8330, tnode num: 94834, tedge num: 114738.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.844002s wall, 1.828125s user + 0.015625s system = 1.843750s CPU (100.0%)

RUN-1004 : used memory is 655 MB, reserved memory is 645 MB, peak memory is 736 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19736 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.954544s wall, 2.921875s user + 0.031250s system = 2.953125s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(222): len = 620021, overlap = 1
PHY-3002 : Step(223): len = 619677, overlap = 1
PHY-3002 : Step(224): len = 619497, overlap = 2
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17743/19738.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 767648, over cnt = 73(0%), over = 98, worst = 5
PHY-1002 : len = 767656, over cnt = 53(0%), over = 61, worst = 3
PHY-1002 : len = 768104, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 768336, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 768376, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.695817s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (101.1%)

PHY-1001 : Congestion index: top1 = 49.57, top5 = 44.61, top10 = 42.00, top15 = 40.31.
PHY-3001 : End congestion estimation;  0.976632s wall, 0.984375s user + 0.015625s system = 1.000000s CPU (102.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19736 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.826476s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(225): len = 619508, overlap = 3.5
PHY-3002 : Step(226): len = 619504, overlap = 3.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006428s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (243.1%)

PHY-3001 : Legalized: Len = 619784, Over = 0
PHY-3001 : Spreading special nets. 1 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.065316s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (119.6%)

PHY-3001 : 2 instances has been re-located, deltaX = 2, deltaY = 2, maxDist = 2.
PHY-3001 : Final: Len = 619856, Over = 0
PHY-3001 : End incremental placement;  6.349665s wall, 6.484375s user + 0.187500s system = 6.671875s CPU (105.1%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  9.541805s wall, 10.593750s user + 0.234375s system = 10.828125s CPU (113.5%)

OPT-1001 : Current memory(MB): used = 726, reserve = 713, peak = 736.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17740/19738.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 769392, over cnt = 40(0%), over = 49, worst = 4
PHY-1002 : len = 769344, over cnt = 28(0%), over = 29, worst = 2
PHY-1002 : len = 769560, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 769640, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 769688, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.687975s wall, 0.718750s user + 0.031250s system = 0.750000s CPU (109.0%)

PHY-1001 : Congestion index: top1 = 49.31, top5 = 44.46, top10 = 41.94, top15 = 40.26.
OPT-1001 : End congestion update;  0.969954s wall, 1.015625s user + 0.031250s system = 1.046875s CPU (107.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19736 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.687987s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (99.9%)

OPT-0007 : Start: WNS 4720 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.662292s wall, 1.718750s user + 0.031250s system = 1.750000s CPU (105.3%)

OPT-1001 : Current memory(MB): used = 724, reserve = 712, peak = 736.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19736 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.712914s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (98.6%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17781/19738.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 769688, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.111735s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (111.9%)

PHY-1001 : Congestion index: top1 = 49.31, top5 = 44.46, top10 = 41.94, top15 = 40.26.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19736 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.698575s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (100.7%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4720 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 48.862069
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4720ps with logic level 4 
RUN-1001 :       #2 path slack 4721ps with logic level 3 
RUN-1001 :       #3 path slack 4766ps with logic level 3 
RUN-1001 :       #4 path slack 4790ps with logic level 8 
RUN-1001 :       #5 path slack 4792ps with logic level 8 
RUN-1001 :       #6 path slack 4805ps with logic level 8 
RUN-1001 :       #7 path slack 4807ps with logic level 8 
OPT-1001 : End physical optimization;  15.112278s wall, 16.187500s user + 0.281250s system = 16.468750s CPU (109.0%)

RUN-1003 : finish command "place" in  71.081521s wall, 136.109375s user + 8.046875s system = 144.156250s CPU (202.8%)

RUN-1004 : used memory is 607 MB, reserved memory is 599 MB, peak memory is 736 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.504276s wall, 2.640625s user + 0.031250s system = 2.671875s CPU (177.6%)

RUN-1004 : used memory is 607 MB, reserved memory is 599 MB, peak memory is 736 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8332 instances
RUN-1001 : 4105 mslices, 4110 lslices, 60 pads, 52 brams, 0 dsps
RUN-1001 : There are total 19738 nets
RUN-1001 : 13731 nets have 2 pins
RUN-1001 : 4547 nets have [3 - 5] pins
RUN-1001 : 898 nets have [6 - 10] pins
RUN-1001 : 406 nets have [11 - 20] pins
RUN-1001 : 146 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69706, tnet num: 19736, tinst num: 8330, tnode num: 94834, tedge num: 114738.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.809072s wall, 1.796875s user + 0.000000s system = 1.796875s CPU (99.3%)

RUN-1004 : used memory is 596 MB, reserved memory is 583 MB, peak memory is 736 MB
PHY-1001 : 4105 mslices, 4110 lslices, 60 pads, 52 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19736 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 705256, over cnt = 2501(7%), over = 4118, worst = 8
PHY-1002 : len = 719104, over cnt = 1654(4%), over = 2441, worst = 6
PHY-1002 : len = 739728, over cnt = 631(1%), over = 928, worst = 6
PHY-1002 : len = 750720, over cnt = 159(0%), over = 244, worst = 5
PHY-1002 : len = 755728, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.624552s wall, 2.906250s user + 0.078125s system = 2.984375s CPU (183.7%)

PHY-1001 : Congestion index: top1 = 48.90, top5 = 44.03, top10 = 41.51, top15 = 39.82.
PHY-1001 : End global routing;  1.919592s wall, 3.187500s user + 0.078125s system = 3.265625s CPU (170.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 710, reserve = 702, peak = 736.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 978, reserve = 969, peak = 978.
PHY-1001 : End build detailed router design. 4.301874s wall, 4.203125s user + 0.109375s system = 4.312500s CPU (100.2%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 198056, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.864466s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (97.6%)

PHY-1001 : Current memory(MB): used = 1015, reserve = 1007, peak = 1015.
PHY-1001 : End phase 1; 0.872394s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (98.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 55% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.87558e+06, over cnt = 1505(0%), over = 1512, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1030, reserve = 1021, peak = 1030.
PHY-1001 : End initial routed; 21.062311s wall, 49.765625s user + 0.546875s system = 50.312500s CPU (238.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18514(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.414   |   0.000   |   0   
RUN-1001 :   Hold   |   0.186   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.183484s wall, 3.171875s user + 0.000000s system = 3.171875s CPU (99.6%)

PHY-1001 : Current memory(MB): used = 1042, reserve = 1034, peak = 1042.
PHY-1001 : End phase 2; 24.245951s wall, 52.937500s user + 0.546875s system = 53.484375s CPU (220.6%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.87558e+06, over cnt = 1505(0%), over = 1512, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.220010s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (99.4%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.85898e+06, over cnt = 489(0%), over = 489, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.882658s wall, 1.390625s user + 0.000000s system = 1.390625s CPU (157.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.8601e+06, over cnt = 89(0%), over = 89, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.407199s wall, 0.578125s user + 0.046875s system = 0.625000s CPU (153.5%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.86137e+06, over cnt = 20(0%), over = 20, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.226686s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (110.3%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.86185e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.196278s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (103.5%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.86188e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.173772s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (98.9%)

PHY-1001 : ===== DR Iter 6 =====
PHY-1022 : len = 1.86188e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 6; 0.241017s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (110.2%)

PHY-1001 : ===== DR Iter 7 =====
PHY-1022 : len = 1.86188e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 7; 0.333088s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (98.5%)

PHY-1001 : ===== DR Iter 8 =====
PHY-1022 : len = 1.86194e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 8; 0.163592s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (105.1%)

PHY-1001 : ==== DR Iter 9 ====
PHY-1022 : len = 1.86194e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 9; 0.148562s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (105.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18514(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.414   |   0.000   |   0   
RUN-1001 :   Hold   |   0.186   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 4.949814s wall, 4.609375s user + 0.015625s system = 4.625000s CPU (93.4%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 380 feed throughs used by 329 nets
PHY-1001 : End commit to database; 2.200116s wall, 2.203125s user + 0.000000s system = 2.203125s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1134, reserve = 1129, peak = 1134.
PHY-1001 : End phase 3; 10.641996s wall, 11.078125s user + 0.062500s system = 11.140625s CPU (104.7%)

PHY-1003 : Routed, final wirelength = 1.86194e+06
PHY-1001 : Current memory(MB): used = 1139, reserve = 1133, peak = 1139.
PHY-1001 : End export database. 0.058647s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.6%)

PHY-1001 : End detail routing;  40.501501s wall, 69.515625s user + 0.718750s system = 70.234375s CPU (173.4%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69706, tnet num: 19736, tinst num: 8330, tnode num: 94834, tedge num: 114738.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.604311s wall, 1.609375s user + 0.000000s system = 1.609375s CPU (100.3%)

RUN-1004 : used memory is 1070 MB, reserved memory is 1067 MB, peak memory is 1139 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  48.238921s wall, 78.515625s user + 0.796875s system = 79.312500s CPU (164.4%)

RUN-1004 : used memory is 1070 MB, reserved memory is 1066 MB, peak memory is 1139 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8973   out of  19600   45.78%
#reg                    12829   out of  19600   65.45%
#le                     15326
  #lut only              2497   out of  15326   16.29%
  #reg only              6353   out of  15326   41.45%
  #lut&reg               6476   out of  15326   42.25%
#dsp                        0   out of     29    0.00%
#bram                      52   out of     64   81.25%
  #bram9k                  52
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                    10
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    7018
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          195
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         M3        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         P6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15326  |7528    |1445    |12872   |52      |0       |
|  AnyFog_dataX                      |AnyFog          |216    |106     |22      |177     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |60      |22      |48      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |224    |131     |22      |185     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |56      |22      |51      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |234    |128     |22      |187     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |93     |70      |22      |53      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |3347   |912     |34      |3267    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |736    |98      |5       |724     |0       |0       |
|    STADOP_com2                     |STADOP          |560    |88      |0       |551     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |66     |50      |14      |43      |0       |0       |
|    head_com2                       |uniheading      |261    |93      |5       |248     |0       |0       |
|    uart_com2                       |Agrica          |1433   |292     |10      |1410    |0       |0       |
|  COM3                              |COM3_Control    |212    |106     |14      |184     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |61     |38      |14      |38      |0       |0       |
|    rmc_com3                        |Gprmc           |151    |68      |0       |146     |0       |0       |
|  DATA                              |Data_Processing |8607   |4302    |1059    |6933    |0       |0       |
|    DIV_Dtemp                       |Divider         |805    |331     |84      |682     |0       |0       |
|    DIV_Utemp                       |Divider         |607    |267     |84      |486     |0       |0       |
|    DIV_accX                        |Divider         |665    |276     |84      |537     |0       |0       |
|    DIV_accY                        |Divider         |602    |357     |108     |435     |0       |0       |
|    DIV_accZ                        |Divider         |614    |379     |132     |404     |0       |0       |
|    DIV_rateX                       |Divider         |614    |334     |132     |409     |0       |0       |
|    DIV_rateY                       |Divider         |651    |352     |132     |447     |0       |0       |
|    DIV_rateZ                       |Divider         |577    |384     |132     |368     |0       |0       |
|    genclk                          |genclk          |88     |64      |20      |54      |0       |0       |
|  FMC                               |FMC_Ctrl        |402    |349     |43      |330     |0       |0       |
|  IIC                               |I2C_master      |324    |284     |11      |266     |0       |0       |
|  IMU_CTRL                          |SCHA634         |902    |690     |61      |739     |0       |0       |
|    CtrlData                        |CtrlData        |460    |411     |47      |329     |0       |0       |
|      usms                          |Time_1ms        |29     |24      |5       |21      |0       |0       |
|    SPIM                            |SPI_SCHA634     |442    |279     |14      |410     |0       |0       |
|  POWER                             |POWER_EN        |100    |51      |38      |41      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |738    |469     |119     |500     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |738    |469     |119     |500     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |325    |197     |0       |308     |0       |0       |
|        reg_inst                    |register        |322    |194     |0       |305     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |413    |272     |119     |192     |0       |0       |
|        bus_inst                    |bus_top         |189    |127     |62      |65      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |40     |30      |10      |12      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |52     |34      |18      |20      |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |96     |62      |34      |32      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |149    |105     |29      |99      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13670  
    #2          2       3557   
    #3          3        675   
    #4          4        315   
    #5        5-10       995   
    #6        11-50      437   
    #7       51-100      18    
    #8       101-500      4    
    #9        >500        2    
  Average     2.17             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  1.905733s wall, 3.296875s user + 0.015625s system = 3.312500s CPU (173.8%)

RUN-1004 : used memory is 1070 MB, reserved memory is 1067 MB, peak memory is 1139 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69706, tnet num: 19736, tinst num: 8330, tnode num: 94834, tedge num: 114738.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.612504s wall, 1.593750s user + 0.015625s system = 1.609375s CPU (99.8%)

RUN-1004 : used memory is 1072 MB, reserved memory is 1069 MB, peak memory is 1139 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19736 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.288826s wall, 1.281250s user + 0.015625s system = 1.296875s CPU (100.6%)

RUN-1004 : used memory is 1077 MB, reserved memory is 1073 MB, peak memory is 1139 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 28b752c1c0e7d7ba92ac5beed3c46754b7b56ac64969e4b0149cfdbc1d295011 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8330
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19738, pip num: 153947
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 380
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3259 valid insts, and 428037 bits set as '1'.
BIT-1004 : the usercode register value: 00000000000000001110111010000001
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  10.808715s wall, 107.671875s user + 0.109375s system = 107.781250s CPU (997.2%)

RUN-1004 : used memory is 1208 MB, reserved memory is 1196 MB, peak memory is 1323 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250619_152408.log"

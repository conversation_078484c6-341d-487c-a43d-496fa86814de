============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Aug 21 09:54:27 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.874480s wall, 1.703125s user + 4.171875s system = 5.875000s CPU (100.0%)

RUN-1004 : used memory is 79 MB, reserved memory is 41 MB, peak memory is 90 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.973959s wall, 1.937500s user + 0.031250s system = 1.968750s CPU (99.7%)

RUN-1004 : used memory is 302 MB, reserved memory is 270 MB, peak memory is 305 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 27 trigger nets, 27 data nets.
KIT-1004 : Chipwatcher code = 1011100110110011
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=27,BUS_CTRL_NUM=74,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=96) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=96) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=27,BUS_CTRL_NUM=74,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=27,BUS_CTRL_NUM=74,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=27,BUS_CTRL_NUM=74,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=96)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=96)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=27,BUS_CTRL_NUM=74,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=27,BUS_CTRL_NUM=74,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22576/21 useful/useless nets, 19458/13 useful/useless insts
SYN-1016 : Merged 29 instances.
SYN-1032 : 22304/22 useful/useless nets, 19809/18 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 2 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 2 mux instances.
SYN-1015 : Optimize round 1, 321 better
SYN-1014 : Optimize round 2
SYN-1032 : 22062/30 useful/useless nets, 19567/32 useful/useless insts
SYN-1015 : Optimize round 2, 64 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.464177s wall, 2.375000s user + 0.078125s system = 2.453125s CPU (99.6%)

RUN-1004 : used memory is 328 MB, reserved memory is 294 MB, peak memory is 330 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22098/221 useful/useless nets, 19626/32 useful/useless insts
SYN-1016 : Merged 33 instances.
SYN-2571 : Optimize after map_dsp, round 1, 286 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 24 instances.
SYN-2501 : Optimize round 1, 50 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 14 macro adder
SYN-1019 : Optimized 9 mux instances.
SYN-1016 : Merged 10 instances.
SYN-1032 : 22475/5 useful/useless nets, 20003/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81633, tnet num: 22475, tinst num: 20002, tnode num: 114440, tedge num: 127649.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.266428s wall, 1.250000s user + 0.015625s system = 1.265625s CPU (99.9%)

RUN-1004 : used memory is 466 MB, reserved memory is 434 MB, peak memory is 466 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22475 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 188 (3.61), #lev = 7 (1.95)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 188 (3.61), #lev = 7 (1.95)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 462 instances into 188 LUTs, name keeping = 75%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 332 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 108 adder to BLE ...
SYN-4008 : Packed 108 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.753351s wall, 4.656250s user + 0.109375s system = 4.765625s CPU (100.3%)

RUN-1004 : used memory is 347 MB, reserved memory is 329 MB, peak memory is 573 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.580705s wall, 7.328125s user + 0.218750s system = 7.546875s CPU (99.6%)

RUN-1004 : used memory is 348 MB, reserved memory is 330 MB, peak memory is 573 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (210 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19291 instances
RUN-0007 : 5580 luts, 12133 seqs, 973 mslices, 515 lslices, 59 pads, 26 brams, 0 dsps
RUN-1001 : There are total 21771 nets
RUN-1001 : 16385 nets have 2 pins
RUN-1001 : 4206 nets have [3 - 5] pins
RUN-1001 : 814 nets have [6 - 10] pins
RUN-1001 : 240 nets have [11 - 20] pins
RUN-1001 : 107 nets have [21 - 99] pins
RUN-1001 : 19 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4792     
RUN-1001 :   No   |  No   |  Yes  |     690     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     382     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19289 instances, 5580 luts, 12133 seqs, 1488 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80111, tnet num: 21769, tinst num: 19289, tnode num: 112634, tedge num: 125985.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.286714s wall, 1.218750s user + 0.062500s system = 1.281250s CPU (99.6%)

RUN-1004 : used memory is 525 MB, reserved memory is 497 MB, peak memory is 573 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21769 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.242833s wall, 2.171875s user + 0.078125s system = 2.250000s CPU (100.3%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.58508e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19289.
PHY-3001 : Level 1 #clusters 2088.
PHY-3001 : End clustering;  0.172499s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (144.9%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 877016, overlap = 599.844
PHY-3002 : Step(2): len = 799264, overlap = 686.844
PHY-3002 : Step(3): len = 532455, overlap = 855.344
PHY-3002 : Step(4): len = 466056, overlap = 914.5
PHY-3002 : Step(5): len = 365705, overlap = 1028.84
PHY-3002 : Step(6): len = 323304, overlap = 1100.78
PHY-3002 : Step(7): len = 270604, overlap = 1171.84
PHY-3002 : Step(8): len = 234837, overlap = 1212.09
PHY-3002 : Step(9): len = 212792, overlap = 1239.31
PHY-3002 : Step(10): len = 191145, overlap = 1270.22
PHY-3002 : Step(11): len = 179517, overlap = 1314.28
PHY-3002 : Step(12): len = 157709, overlap = 1352.41
PHY-3002 : Step(13): len = 150122, overlap = 1399.38
PHY-3002 : Step(14): len = 137594, overlap = 1405.19
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.35635e-06
PHY-3002 : Step(15): len = 144253, overlap = 1371.03
PHY-3002 : Step(16): len = 199893, overlap = 1233.62
PHY-3002 : Step(17): len = 207313, overlap = 1130.53
PHY-3002 : Step(18): len = 204436, overlap = 1095.88
PHY-3002 : Step(19): len = 197923, overlap = 1068.16
PHY-3002 : Step(20): len = 190761, overlap = 1059.53
PHY-3002 : Step(21): len = 184682, overlap = 1053.5
PHY-3002 : Step(22): len = 179299, overlap = 1042.25
PHY-3002 : Step(23): len = 175504, overlap = 1028.34
PHY-3002 : Step(24): len = 172895, overlap = 1018.5
PHY-3002 : Step(25): len = 170256, overlap = 1015.03
PHY-3002 : Step(26): len = 168799, overlap = 1024.34
PHY-3002 : Step(27): len = 167816, overlap = 1027.56
PHY-3002 : Step(28): len = 167278, overlap = 1021.84
PHY-3002 : Step(29): len = 166766, overlap = 1032.34
PHY-3002 : Step(30): len = 166712, overlap = 1031.56
PHY-3002 : Step(31): len = 165469, overlap = 1020.62
PHY-3002 : Step(32): len = 164267, overlap = 1026.22
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.7127e-06
PHY-3002 : Step(33): len = 172920, overlap = 995.156
PHY-3002 : Step(34): len = 188511, overlap = 914.469
PHY-3002 : Step(35): len = 193835, overlap = 874.375
PHY-3002 : Step(36): len = 195621, overlap = 844.938
PHY-3002 : Step(37): len = 195765, overlap = 833.312
PHY-3002 : Step(38): len = 195164, overlap = 819.812
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 5.42541e-06
PHY-3002 : Step(39): len = 202769, overlap = 784.625
PHY-3002 : Step(40): len = 218772, overlap = 681.188
PHY-3002 : Step(41): len = 224357, overlap = 622.844
PHY-3002 : Step(42): len = 225285, overlap = 619.719
PHY-3002 : Step(43): len = 225017, overlap = 599
PHY-3002 : Step(44): len = 222856, overlap = 598.531
PHY-3002 : Step(45): len = 221645, overlap = 578.438
PHY-3002 : Step(46): len = 220256, overlap = 580.094
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 1.08508e-05
PHY-3002 : Step(47): len = 232082, overlap = 542.781
PHY-3002 : Step(48): len = 245561, overlap = 491.938
PHY-3002 : Step(49): len = 250107, overlap = 508.688
PHY-3002 : Step(50): len = 252628, overlap = 515.906
PHY-3002 : Step(51): len = 252028, overlap = 528
PHY-3002 : Step(52): len = 250322, overlap = 544.062
PHY-3002 : Step(53): len = 249068, overlap = 557.844
PHY-3002 : Step(54): len = 248571, overlap = 561.594
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 2.17016e-05
PHY-3002 : Step(55): len = 258698, overlap = 550.938
PHY-3002 : Step(56): len = 271574, overlap = 488.875
PHY-3002 : Step(57): len = 276029, overlap = 461.438
PHY-3002 : Step(58): len = 277863, overlap = 441.625
PHY-3002 : Step(59): len = 276496, overlap = 426.906
PHY-3002 : Step(60): len = 276004, overlap = 426.156
PHY-3002 : Step(61): len = 273099, overlap = 403.656
PHY-3002 : Step(62): len = 273055, overlap = 384.5
PHY-3002 : Step(63): len = 271940, overlap = 393.5
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 4.34033e-05
PHY-3002 : Step(64): len = 282134, overlap = 352.031
PHY-3002 : Step(65): len = 293655, overlap = 308.844
PHY-3002 : Step(66): len = 296759, overlap = 286.75
PHY-3002 : Step(67): len = 297285, overlap = 273.031
PHY-3002 : Step(68): len = 295839, overlap = 289.219
PHY-3002 : Step(69): len = 294630, overlap = 315.219
PHY-3002 : Step(70): len = 292582, overlap = 305.219
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 8.68065e-05
PHY-3002 : Step(71): len = 298692, overlap = 280.5
PHY-3002 : Step(72): len = 306634, overlap = 262.25
PHY-3002 : Step(73): len = 310209, overlap = 258.281
PHY-3002 : Step(74): len = 310669, overlap = 235.594
PHY-3002 : Step(75): len = 309551, overlap = 245.188
PHY-3002 : Step(76): len = 308713, overlap = 236.688
PHY-3002 : Step(77): len = 307753, overlap = 236.438
PHY-3002 : Step(78): len = 308040, overlap = 224.438
PHY-3002 : Step(79): len = 307476, overlap = 223.875
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000173613
PHY-3002 : Step(80): len = 310725, overlap = 211.906
PHY-3002 : Step(81): len = 316857, overlap = 201.094
PHY-3002 : Step(82): len = 319366, overlap = 202.281
PHY-3002 : Step(83): len = 320705, overlap = 206.656
PHY-3002 : Step(84): len = 321312, overlap = 200
PHY-3002 : Step(85): len = 321406, overlap = 202.219
PHY-3002 : Step(86): len = 321063, overlap = 202
PHY-3002 : Step(87): len = 320827, overlap = 201.031
PHY-3002 : Step(88): len = 319841, overlap = 208.75
PHY-3002 : Step(89): len = 320106, overlap = 207.312
PHY-3002 : Step(90): len = 319904, overlap = 222.344
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000317368
PHY-3002 : Step(91): len = 321599, overlap = 211.719
PHY-3002 : Step(92): len = 324351, overlap = 206.062
PHY-3002 : Step(93): len = 325520, overlap = 210.875
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000525763
PHY-3002 : Step(94): len = 326254, overlap = 214.844
PHY-3002 : Step(95): len = 330514, overlap = 199
PHY-3002 : Step(96): len = 332235, overlap = 210.625
PHY-3002 : Step(97): len = 333706, overlap = 200.875
PHY-3002 : Step(98): len = 334588, overlap = 191.094
PHY-3002 : Step(99): len = 334766, overlap = 189.75
PHY-3002 : Step(100): len = 334723, overlap = 183.844
PHY-3002 : Step(101): len = 334449, overlap = 180.938
PHY-3002 : Step(102): len = 334874, overlap = 179.281
PHY-3002 : Step(103): len = 334986, overlap = 183.594
PHY-3002 : Step(104): len = 335308, overlap = 201.469
PHY-3002 : Step(105): len = 334927, overlap = 196.844
PHY-3001 : :::11::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(106): len = 335700, overlap = 195.656
PHY-3002 : Step(107): len = 339520, overlap = 189.594
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.013023s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (120.0%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21771.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 435840, over cnt = 1171(3%), over = 5224, worst = 42
PHY-1001 : End global iterations;  0.853997s wall, 1.046875s user + 0.046875s system = 1.093750s CPU (128.1%)

PHY-1001 : Congestion index: top1 = 77.24, top5 = 51.99, top10 = 42.14, top15 = 36.74.
PHY-3001 : End congestion estimation;  1.167645s wall, 1.343750s user + 0.062500s system = 1.406250s CPU (120.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21769 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.978586s wall, 0.921875s user + 0.062500s system = 0.984375s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000123214
PHY-3002 : Step(108): len = 385145, overlap = 154.156
PHY-3002 : Step(109): len = 398687, overlap = 141.375
PHY-3002 : Step(110): len = 397022, overlap = 131.906
PHY-3002 : Step(111): len = 395543, overlap = 128.781
PHY-3002 : Step(112): len = 401954, overlap = 120.625
PHY-3002 : Step(113): len = 411129, overlap = 109.094
PHY-3002 : Step(114): len = 415010, overlap = 104.875
PHY-3002 : Step(115): len = 418245, overlap = 97.5938
PHY-3002 : Step(116): len = 420312, overlap = 95.0938
PHY-3002 : Step(117): len = 425646, overlap = 90.4062
PHY-3002 : Step(118): len = 427197, overlap = 84.6875
PHY-3002 : Step(119): len = 427421, overlap = 86.1562
PHY-3002 : Step(120): len = 428526, overlap = 80.7812
PHY-3002 : Step(121): len = 429941, overlap = 82.1562
PHY-3002 : Step(122): len = 432006, overlap = 86.6875
PHY-3002 : Step(123): len = 433827, overlap = 84.7188
PHY-3002 : Step(124): len = 435795, overlap = 87.3125
PHY-3002 : Step(125): len = 439271, overlap = 90.8125
PHY-3002 : Step(126): len = 441935, overlap = 92
PHY-3002 : Step(127): len = 442799, overlap = 94.0938
PHY-3002 : Step(128): len = 444122, overlap = 99.75
PHY-3002 : Step(129): len = 445614, overlap = 106.188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000246427
PHY-3002 : Step(130): len = 446185, overlap = 108.938
PHY-3002 : Step(131): len = 448120, overlap = 108.281
PHY-3002 : Step(132): len = 449784, overlap = 105.656
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(133): len = 453770, overlap = 102.594
PHY-3002 : Step(134): len = 459359, overlap = 100.25
PHY-3002 : Step(135): len = 463266, overlap = 105.156
PHY-3002 : Step(136): len = 463650, overlap = 110.719
PHY-3002 : Step(137): len = 464680, overlap = 110
PHY-3002 : Step(138): len = 464033, overlap = 105.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 48/21771.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 527624, over cnt = 2103(5%), over = 9841, worst = 53
PHY-1001 : End global iterations;  1.063936s wall, 1.828125s user + 0.062500s system = 1.890625s CPU (177.7%)

PHY-1001 : Congestion index: top1 = 78.06, top5 = 58.30, top10 = 49.50, top15 = 44.54.
PHY-3001 : End congestion estimation;  1.429916s wall, 2.187500s user + 0.062500s system = 2.250000s CPU (157.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21769 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.037090s wall, 1.031250s user + 0.015625s system = 1.046875s CPU (100.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000109393
PHY-3002 : Step(139): len = 473241, overlap = 365.375
PHY-3002 : Step(140): len = 480060, overlap = 295
PHY-3002 : Step(141): len = 473303, overlap = 275.469
PHY-3002 : Step(142): len = 471343, overlap = 262.969
PHY-3002 : Step(143): len = 469762, overlap = 251.219
PHY-3002 : Step(144): len = 465874, overlap = 234.531
PHY-3002 : Step(145): len = 464453, overlap = 227.094
PHY-3002 : Step(146): len = 464635, overlap = 220.125
PHY-3002 : Step(147): len = 461392, overlap = 223.469
PHY-3002 : Step(148): len = 460763, overlap = 224.781
PHY-3002 : Step(149): len = 460153, overlap = 212.438
PHY-3002 : Step(150): len = 459353, overlap = 202.75
PHY-3002 : Step(151): len = 459569, overlap = 200.406
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000218787
PHY-3002 : Step(152): len = 459689, overlap = 200.594
PHY-3002 : Step(153): len = 462005, overlap = 192.469
PHY-3002 : Step(154): len = 464191, overlap = 186.094
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000437573
PHY-3002 : Step(155): len = 467247, overlap = 176.406
PHY-3002 : Step(156): len = 474312, overlap = 167.281
PHY-3002 : Step(157): len = 481038, overlap = 156.25
PHY-3002 : Step(158): len = 480204, overlap = 153.062
PHY-3002 : Step(159): len = 479911, overlap = 153.062
PHY-3002 : Step(160): len = 479914, overlap = 150.625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000860178
PHY-3002 : Step(161): len = 482533, overlap = 149.094
PHY-3002 : Step(162): len = 484805, overlap = 145.219
PHY-3002 : Step(163): len = 490783, overlap = 143.531
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00155241
PHY-3002 : Step(164): len = 491759, overlap = 138.25
PHY-3002 : Step(165): len = 496386, overlap = 131.812
PHY-3002 : Step(166): len = 502352, overlap = 115.5
PHY-3002 : Step(167): len = 503968, overlap = 118.281
PHY-3002 : Step(168): len = 503727, overlap = 121.219
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00284774
PHY-3002 : Step(169): len = 504264, overlap = 116.75
PHY-3002 : Step(170): len = 506199, overlap = 115.781
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80111, tnet num: 21769, tinst num: 19289, tnode num: 112634, tedge num: 125985.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.622266s wall, 1.593750s user + 0.046875s system = 1.640625s CPU (101.1%)

RUN-1004 : used memory is 565 MB, reserved memory is 539 MB, peak memory is 698 MB
OPT-1001 : Total overflow 472.38 peak overflow 3.59
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 633/21771.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 585488, over cnt = 2595(7%), over = 8835, worst = 23
PHY-1001 : End global iterations;  1.211220s wall, 2.031250s user + 0.015625s system = 2.046875s CPU (169.0%)

PHY-1001 : Congestion index: top1 = 57.72, top5 = 47.10, top10 = 42.74, top15 = 40.04.
PHY-1001 : End incremental global routing;  1.491614s wall, 2.312500s user + 0.015625s system = 2.328125s CPU (156.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21769 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.077235s wall, 1.078125s user + 0.000000s system = 1.078125s CPU (100.1%)

OPT-1001 : 16 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19211 has valid locations, 232 needs to be replaced
PHY-3001 : design contains 19505 instances, 5673 luts, 12256 seqs, 1488 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 520761
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17463/21987.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 596184, over cnt = 2606(7%), over = 8873, worst = 23
PHY-1001 : End global iterations;  0.197253s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (110.9%)

PHY-1001 : Congestion index: top1 = 57.76, top5 = 47.18, top10 = 42.89, top15 = 40.21.
PHY-3001 : End congestion estimation;  0.463760s wall, 0.484375s user + 0.000000s system = 0.484375s CPU (104.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80816, tnet num: 21985, tinst num: 19505, tnode num: 113622, tedge num: 126963.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.597067s wall, 1.578125s user + 0.015625s system = 1.593750s CPU (99.8%)

RUN-1004 : used memory is 611 MB, reserved memory is 608 MB, peak memory is 700 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21985 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.705660s wall, 2.671875s user + 0.031250s system = 2.703125s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(171): len = 520611, overlap = 0.8125
PHY-3002 : Step(172): len = 521417, overlap = 0.8125
PHY-3002 : Step(173): len = 522656, overlap = 1.1875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17506/21987.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 596000, over cnt = 2631(7%), over = 8935, worst = 23
PHY-1001 : End global iterations;  0.191863s wall, 0.281250s user + 0.046875s system = 0.328125s CPU (171.0%)

PHY-1001 : Congestion index: top1 = 57.95, top5 = 47.29, top10 = 43.02, top15 = 40.32.
PHY-3001 : End congestion estimation;  0.456565s wall, 0.546875s user + 0.046875s system = 0.593750s CPU (130.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21985 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.057689s wall, 1.031250s user + 0.031250s system = 1.062500s CPU (100.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000783522
PHY-3002 : Step(174): len = 522785, overlap = 118.062
PHY-3002 : Step(175): len = 523270, overlap = 118.219
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00156704
PHY-3002 : Step(176): len = 523224, overlap = 118.031
PHY-3002 : Step(177): len = 523522, overlap = 118.188
PHY-3001 : Final: Len = 523522, Over = 118.188
PHY-3001 : End incremental placement;  5.515720s wall, 5.687500s user + 0.265625s system = 5.953125s CPU (107.9%)

OPT-1001 : Total overflow 476.56 peak overflow 3.59
OPT-1001 : End high-fanout net optimization;  8.630321s wall, 9.781250s user + 0.281250s system = 10.062500s CPU (116.6%)

OPT-1001 : Current memory(MB): used = 705, reserve = 685, peak = 721.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17498/21987.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 596376, over cnt = 2573(7%), over = 8466, worst = 23
PHY-1002 : len = 632936, over cnt = 1848(5%), over = 4856, worst = 23
PHY-1002 : len = 662312, over cnt = 1072(3%), over = 2607, worst = 17
PHY-1002 : len = 693496, over cnt = 311(0%), over = 661, worst = 9
PHY-1002 : len = 703088, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.331036s wall, 1.921875s user + 0.000000s system = 1.921875s CPU (144.4%)

PHY-1001 : Congestion index: top1 = 50.54, top5 = 43.88, top10 = 40.77, top15 = 38.74.
OPT-1001 : End congestion update;  1.603802s wall, 2.187500s user + 0.000000s system = 2.187500s CPU (136.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 21985 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.080647s wall, 1.078125s user + 0.000000s system = 1.078125s CPU (99.8%)

OPT-0007 : Start: WNS 4087 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.690571s wall, 3.281250s user + 0.000000s system = 3.281250s CPU (122.0%)

OPT-1001 : Current memory(MB): used = 680, reserve = 662, peak = 721.
OPT-1001 : End physical optimization;  13.278168s wall, 15.109375s user + 0.375000s system = 15.484375s CPU (116.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5673 LUT to BLE ...
SYN-4008 : Packed 5673 LUT and 2733 SEQ to BLE.
SYN-4003 : Packing 9523 remaining SEQ's ...
SYN-4005 : Packed 3363 SEQ with LUT/SLICE
SYN-4006 : 112 single LUT's are left
SYN-4006 : 6160 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11833/13675 primitive instances ...
PHY-3001 : End packing;  2.848350s wall, 2.843750s user + 0.000000s system = 2.843750s CPU (99.8%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8067 instances
RUN-1001 : 3989 mslices, 3988 lslices, 59 pads, 26 brams, 0 dsps
RUN-1001 : There are total 19306 nets
RUN-1001 : 13571 nets have 2 pins
RUN-1001 : 4328 nets have [3 - 5] pins
RUN-1001 : 890 nets have [6 - 10] pins
RUN-1001 : 380 nets have [11 - 20] pins
RUN-1001 : 128 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8065 instances, 7977 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 537667, Over = 345.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7989/19306.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 668376, over cnt = 1517(4%), over = 2332, worst = 8
PHY-1002 : len = 673816, over cnt = 890(2%), over = 1180, worst = 7
PHY-1002 : len = 681512, over cnt = 440(1%), over = 582, worst = 6
PHY-1002 : len = 689152, over cnt = 76(0%), over = 88, worst = 3
PHY-1002 : len = 690240, over cnt = 3(0%), over = 3, worst = 1
PHY-1001 : End global iterations;  1.255338s wall, 1.953125s user + 0.031250s system = 1.984375s CPU (158.1%)

PHY-1001 : Congestion index: top1 = 49.85, top5 = 43.82, top10 = 40.40, top15 = 38.26.
PHY-3001 : End congestion estimation;  1.604316s wall, 2.281250s user + 0.031250s system = 2.312500s CPU (144.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67044, tnet num: 19304, tinst num: 8065, tnode num: 90936, tedge num: 110544.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.789678s wall, 1.781250s user + 0.015625s system = 1.796875s CPU (100.4%)

RUN-1004 : used memory is 601 MB, reserved memory is 588 MB, peak memory is 721 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19304 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.792289s wall, 2.781250s user + 0.015625s system = 2.796875s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.68362e-05
PHY-3002 : Step(178): len = 537438, overlap = 320.25
PHY-3002 : Step(179): len = 532751, overlap = 324.5
PHY-3002 : Step(180): len = 529130, overlap = 332.5
PHY-3002 : Step(181): len = 527044, overlap = 342.5
PHY-3002 : Step(182): len = 524024, overlap = 360
PHY-3002 : Step(183): len = 522074, overlap = 357.25
PHY-3002 : Step(184): len = 519105, overlap = 363.5
PHY-3002 : Step(185): len = 516181, overlap = 363.5
PHY-3002 : Step(186): len = 513817, overlap = 360.75
PHY-3002 : Step(187): len = 511511, overlap = 366.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000113672
PHY-3002 : Step(188): len = 515915, overlap = 357.25
PHY-3002 : Step(189): len = 520743, overlap = 352
PHY-3002 : Step(190): len = 521868, overlap = 346.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000227345
PHY-3002 : Step(191): len = 527546, overlap = 342
PHY-3002 : Step(192): len = 535372, overlap = 323.75
PHY-3002 : Step(193): len = 535477, overlap = 320.25
PHY-3002 : Step(194): len = 534475, overlap = 322.5
PHY-3002 : Step(195): len = 534212, overlap = 322.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.826506s wall, 0.781250s user + 1.062500s system = 1.843750s CPU (223.1%)

PHY-3001 : Trial Legalized: Len = 637357
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 529/19306.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 722248, over cnt = 2395(6%), over = 3849, worst = 8
PHY-1002 : len = 737072, over cnt = 1460(4%), over = 2043, worst = 6
PHY-1002 : len = 760688, over cnt = 276(0%), over = 314, worst = 4
PHY-1002 : len = 763560, over cnt = 116(0%), over = 127, worst = 3
PHY-1002 : len = 766456, over cnt = 4(0%), over = 5, worst = 2
PHY-1001 : End global iterations;  1.981810s wall, 3.187500s user + 0.031250s system = 3.218750s CPU (162.4%)

PHY-1001 : Congestion index: top1 = 51.21, top5 = 46.16, top10 = 43.29, top15 = 41.39.
PHY-3001 : End congestion estimation;  2.370855s wall, 3.578125s user + 0.031250s system = 3.609375s CPU (152.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19304 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.953410s wall, 0.937500s user + 0.015625s system = 0.953125s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000177419
PHY-3002 : Step(196): len = 596745, overlap = 70.25
PHY-3002 : Step(197): len = 578703, overlap = 111.25
PHY-3002 : Step(198): len = 567285, overlap = 153.5
PHY-3002 : Step(199): len = 559476, overlap = 196
PHY-3002 : Step(200): len = 555300, overlap = 228
PHY-3002 : Step(201): len = 552601, overlap = 237.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000354837
PHY-3002 : Step(202): len = 555614, overlap = 236
PHY-3002 : Step(203): len = 559665, overlap = 228.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000709675
PHY-3002 : Step(204): len = 561633, overlap = 223
PHY-3002 : Step(205): len = 568076, overlap = 223.25
PHY-3002 : Step(206): len = 569495, overlap = 219.25
PHY-3002 : Step(207): len = 570122, overlap = 223
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.032583s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (95.9%)

PHY-3001 : Legalized: Len = 611268, Over = 0
PHY-3001 : Spreading special nets. 33 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.086197s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (90.6%)

PHY-3001 : 51 instances has been re-located, deltaX = 22, deltaY = 29, maxDist = 2.
PHY-3001 : Final: Len = 612000, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67044, tnet num: 19304, tinst num: 8065, tnode num: 90936, tedge num: 110544.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.003912s wall, 2.000000s user + 0.015625s system = 2.015625s CPU (100.6%)

RUN-1004 : used memory is 594 MB, reserved memory is 572 MB, peak memory is 721 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 4170/19306.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 706168, over cnt = 2145(6%), over = 3297, worst = 6
PHY-1002 : len = 718200, over cnt = 1172(3%), over = 1520, worst = 6
PHY-1002 : len = 730304, over cnt = 466(1%), over = 605, worst = 5
PHY-1002 : len = 736624, over cnt = 146(0%), over = 180, worst = 5
PHY-1002 : len = 739936, over cnt = 10(0%), over = 13, worst = 2
PHY-1001 : End global iterations;  1.740256s wall, 2.953125s user + 0.046875s system = 3.000000s CPU (172.4%)

PHY-1001 : Congestion index: top1 = 48.17, top5 = 43.62, top10 = 41.21, top15 = 39.49.
PHY-1001 : End incremental global routing;  2.073987s wall, 3.265625s user + 0.062500s system = 3.328125s CPU (160.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19304 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.982158s wall, 0.968750s user + 0.015625s system = 0.984375s CPU (100.2%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8002 has valid locations, 8 needs to be replaced
PHY-3001 : design contains 8072 instances, 7984 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 613334
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17354/19312.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 741184, over cnt = 21(0%), over = 24, worst = 2
PHY-1002 : len = 741168, over cnt = 11(0%), over = 12, worst = 2
PHY-1002 : len = 741248, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 741264, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 741280, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.706436s wall, 0.765625s user + 0.031250s system = 0.796875s CPU (112.8%)

PHY-1001 : Congestion index: top1 = 48.15, top5 = 43.66, top10 = 41.24, top15 = 39.53.
PHY-3001 : End congestion estimation;  1.026895s wall, 1.078125s user + 0.031250s system = 1.109375s CPU (108.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67085, tnet num: 19310, tinst num: 8072, tnode num: 90984, tedge num: 110591.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.034718s wall, 2.031250s user + 0.000000s system = 2.031250s CPU (99.8%)

RUN-1004 : used memory is 643 MB, reserved memory is 628 MB, peak memory is 721 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19310 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.052983s wall, 3.031250s user + 0.031250s system = 3.062500s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(208): len = 613278, overlap = 0
PHY-3002 : Step(209): len = 613274, overlap = 0
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17351/19312.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 740888, over cnt = 9(0%), over = 11, worst = 2
PHY-1002 : len = 740856, over cnt = 7(0%), over = 8, worst = 2
PHY-1002 : len = 740928, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 740992, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 741032, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.717454s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (102.4%)

PHY-1001 : Congestion index: top1 = 48.10, top5 = 43.67, top10 = 41.26, top15 = 39.56.
PHY-3001 : End congestion estimation;  1.032478s wall, 1.046875s user + 0.000000s system = 1.046875s CPU (101.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19310 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.962761s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (99.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000499438
PHY-3002 : Step(210): len = 613282, overlap = 0.25
PHY-3002 : Step(211): len = 613308, overlap = 0.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007198s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (217.1%)

PHY-3001 : Legalized: Len = 613325, Over = 0
PHY-3001 : End spreading;  0.074316s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (84.1%)

PHY-3001 : Final: Len = 613325, Over = 0
PHY-3001 : End incremental placement;  6.670988s wall, 6.796875s user + 0.156250s system = 6.953125s CPU (104.2%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.250748s wall, 11.531250s user + 0.234375s system = 11.765625s CPU (114.8%)

OPT-1001 : Current memory(MB): used = 715, reserve = 701, peak = 721.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17354/19312.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 741160, over cnt = 8(0%), over = 10, worst = 2
PHY-1002 : len = 741168, over cnt = 7(0%), over = 8, worst = 2
PHY-1002 : len = 741184, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 741264, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.523648s wall, 0.578125s user + 0.031250s system = 0.609375s CPU (116.4%)

PHY-1001 : Congestion index: top1 = 48.19, top5 = 43.74, top10 = 41.30, top15 = 39.58.
OPT-1001 : End congestion update;  0.841000s wall, 0.890625s user + 0.031250s system = 0.921875s CPU (109.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19310 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.808022s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (98.6%)

OPT-0007 : Start: WNS 4405 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.653918s wall, 1.687500s user + 0.031250s system = 1.718750s CPU (103.9%)

OPT-1001 : Current memory(MB): used = 715, reserve = 701, peak = 721.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19310 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.818779s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (101.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17361/19312.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 741264, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.241957s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (103.3%)

PHY-1001 : Congestion index: top1 = 48.19, top5 = 43.74, top10 = 41.30, top15 = 39.58.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19310 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.808954s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (98.5%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4405 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 47.689655
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4405ps with logic level 5 
RUN-1001 :       #2 path slack 4472ps with logic level 5 
RUN-1001 :       #3 path slack 4492ps with logic level 5 
OPT-1001 : End physical optimization;  16.374797s wall, 17.671875s user + 0.281250s system = 17.953125s CPU (109.6%)

RUN-1003 : finish command "place" in  72.666360s wall, 138.593750s user + 8.437500s system = 147.031250s CPU (202.3%)

RUN-1004 : used memory is 597 MB, reserved memory is 572 MB, peak memory is 721 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.722714s wall, 2.953125s user + 0.031250s system = 2.984375s CPU (173.2%)

RUN-1004 : used memory is 597 MB, reserved memory is 573 MB, peak memory is 721 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8074 instances
RUN-1001 : 3989 mslices, 3995 lslices, 59 pads, 26 brams, 0 dsps
RUN-1001 : There are total 19312 nets
RUN-1001 : 13571 nets have 2 pins
RUN-1001 : 4328 nets have [3 - 5] pins
RUN-1001 : 891 nets have [6 - 10] pins
RUN-1001 : 385 nets have [11 - 20] pins
RUN-1001 : 128 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67085, tnet num: 19310, tinst num: 8072, tnode num: 90984, tedge num: 110591.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.734349s wall, 1.734375s user + 0.000000s system = 1.734375s CPU (100.0%)

RUN-1004 : used memory is 610 MB, reserved memory is 609 MB, peak memory is 721 MB
PHY-1001 : 3989 mslices, 3995 lslices, 59 pads, 26 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19310 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 685344, over cnt = 2300(6%), over = 3721, worst = 7
PHY-1002 : len = 701936, over cnt = 1290(3%), over = 1768, worst = 7
PHY-1002 : len = 716776, over cnt = 484(1%), over = 646, worst = 5
PHY-1002 : len = 727240, over cnt = 13(0%), over = 20, worst = 3
PHY-1002 : len = 727512, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.793979s wall, 3.109375s user + 0.046875s system = 3.156250s CPU (175.9%)

PHY-1001 : Congestion index: top1 = 48.49, top5 = 43.37, top10 = 40.84, top15 = 39.17.
PHY-1001 : End global routing;  2.143562s wall, 3.468750s user + 0.046875s system = 3.515625s CPU (164.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 700, reserve = 687, peak = 721.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 967, reserve = 953, peak = 967.
PHY-1001 : End build detailed router design. 4.699209s wall, 4.625000s user + 0.078125s system = 4.703125s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 189584, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.956652s wall, 0.937500s user + 0.015625s system = 0.953125s CPU (99.6%)

PHY-1001 : Current memory(MB): used = 1002, reserve = 989, peak = 1002.
PHY-1001 : End phase 1; 0.964134s wall, 0.953125s user + 0.015625s system = 0.968750s CPU (100.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 55% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.63646e+06, over cnt = 1422(0%), over = 1432, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1017, reserve = 1004, peak = 1017.
PHY-1001 : End initial routed; 17.194697s wall, 44.375000s user + 0.515625s system = 44.890625s CPU (261.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18052(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.153   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.792094s wall, 3.796875s user + 0.000000s system = 3.796875s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1028, reserve = 1016, peak = 1028.
PHY-1001 : End phase 2; 20.986976s wall, 48.171875s user + 0.515625s system = 48.687500s CPU (232.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.63646e+06, over cnt = 1422(0%), over = 1432, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.256132s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (103.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.62224e+06, over cnt = 477(0%), over = 477, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 1.091128s wall, 1.750000s user + 0.015625s system = 1.765625s CPU (161.8%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.62304e+06, over cnt = 89(0%), over = 89, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.472942s wall, 0.578125s user + 0.015625s system = 0.593750s CPU (125.5%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.62417e+06, over cnt = 5(0%), over = 5, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.256058s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (115.9%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.6243e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 4; 0.179778s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (95.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18052(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.067   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.646482s wall, 3.640625s user + 0.000000s system = 3.640625s CPU (99.8%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 321 feed throughs used by 283 nets
PHY-1001 : End commit to database; 2.230017s wall, 2.218750s user + 0.015625s system = 2.234375s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 1116, reserve = 1106, peak = 1116.
PHY-1001 : End phase 3; 8.642367s wall, 9.421875s user + 0.062500s system = 9.484375s CPU (109.7%)

PHY-1003 : Routed, final wirelength = 1.6243e+06
PHY-1001 : Current memory(MB): used = 1120, reserve = 1110, peak = 1120.
PHY-1001 : End export database. 0.062543s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.9%)

PHY-1001 : End detail routing;  35.798135s wall, 63.656250s user + 0.687500s system = 64.343750s CPU (179.7%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67085, tnet num: 19310, tinst num: 8072, tnode num: 90984, tedge num: 110591.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.769543s wall, 1.765625s user + 0.000000s system = 1.765625s CPU (99.8%)

RUN-1004 : used memory is 1051 MB, reserved memory is 1049 MB, peak memory is 1120 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  44.279296s wall, 73.437500s user + 0.750000s system = 74.187500s CPU (167.5%)

RUN-1004 : used memory is 1051 MB, reserved memory is 1050 MB, peak memory is 1120 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8802   out of  19600   44.91%
#reg                    12350   out of  19600   63.01%
#le                     14916
  #lut only              2566   out of  14916   17.20%
  #reg only              6114   out of  14916   40.99%
  #lut&reg               6236   out of  14916   41.81%
#dsp                        0   out of     29    0.00%
#bram                      26   out of     64   40.62%
  #bram9k                  24
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       59   out of    188   31.38%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6798
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          123
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         F2        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         D1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         B3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         C1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         A3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         A2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         B2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         B1        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F15        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDZ        INPUT        K15        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         P2        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         N4        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         M5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         N6        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         N5        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         P5        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M3        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         M4        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         H2        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         M1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         L1        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         N1        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         N3        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         M2        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT         T5        LVCMOS33           8            NONE           OREG       
    Fog_ENY        OUTPUT         T4        LVCMOS33           8            NONE           OREG       
    Fog_ENZ        OUTPUT         T7        LVCMOS33           8            NONE           OREG       
    Fog_INT        OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         T6        LVCMOS33           8            N/A            OREG       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E1        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        G11        LVCMOS25           8            N/A            NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |14916  |7314    |1488    |12394   |26      |0       |
|  AnyFog_dataX                      |AnyFog          |209    |97      |22      |174     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |87     |62      |22      |52      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |203    |80      |22      |169     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |82     |55      |22      |48      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |204    |108     |22      |169     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |64      |22      |51      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2916   |722     |39      |2829    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |59     |34      |5       |50      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |223    |83      |5       |211     |0       |0       |
|    STADOP_com2                     |STADOP          |551    |87      |0       |542     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |46      |14      |43      |0       |0       |
|    head_com2                       |uniheading      |254    |60      |5       |241     |0       |0       |
|    rmc_com2                        |Gprmc           |21     |19      |0       |20      |0       |0       |
|    uart_com2                       |Agrica          |1418   |363     |10      |1397    |0       |0       |
|  COM3                              |COM3_Control    |278    |130     |19      |239     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |63     |38      |5       |54      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |61     |38      |14      |39      |0       |0       |
|    rmc_com3                        |Gprmc           |154    |54      |0       |146     |0       |0       |
|  DATA                              |Data_Processing |8835   |4447    |1122    |7052    |0       |0       |
|    DIV_Dtemp                       |Divider         |820    |304     |84      |693     |0       |0       |
|    DIV_Utemp                       |Divider         |590    |330     |84      |470     |0       |0       |
|    DIV_accX                        |Divider         |553    |303     |84      |432     |0       |0       |
|    DIV_accY                        |Divider         |635    |327     |102     |482     |0       |0       |
|    DIV_accZ                        |Divider         |638    |344     |132     |432     |0       |0       |
|    DIV_rateX                       |Divider         |670    |387     |132     |464     |0       |0       |
|    DIV_rateY                       |Divider         |606    |365     |132     |402     |0       |0       |
|    DIV_rateZ                       |Divider         |609    |375     |132     |403     |0       |0       |
|    genclk                          |genclk          |264    |157     |89      |102     |0       |0       |
|  FMC                               |FMC_Ctrl        |473    |422     |43      |360     |0       |0       |
|  IIC                               |I2C_master      |298    |259     |11      |265     |0       |0       |
|  IMU_CTRL                          |SCHA634         |901    |702     |61      |719     |0       |0       |
|    CtrlData                        |CtrlData        |474    |421     |47      |331     |0       |0       |
|      usms                          |Time_1ms        |28     |23      |5       |20      |0       |0       |
|    SPIM                            |SPI_SCHA634     |427    |281     |14      |388     |0       |0       |
|  POWER                             |POWER_EN        |98     |54      |38      |39      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |492    |293     |89      |326     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |492    |293     |89      |326     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |209    |109     |0       |198     |0       |0       |
|        reg_inst                    |register        |206    |107     |0       |195     |0       |0       |
|        tap_inst                    |tap             |3      |2       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |283    |184     |89      |128     |0       |0       |
|        bus_inst                    |bus_top         |78     |50      |28      |29      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |25     |15      |10      |8       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |3      |3       |0       |3       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |48     |30      |18      |16      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |117    |83      |29      |68      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13511  
    #2          2       3405   
    #3          3        632   
    #4          4        291   
    #5        5-10       949   
    #6        11-50      444   
    #7       51-100      11    
    #8       101-500      3    
    #9        >500        2    
  Average     2.12             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.198005s wall, 3.703125s user + 0.031250s system = 3.734375s CPU (169.9%)

RUN-1004 : used memory is 1051 MB, reserved memory is 1051 MB, peak memory is 1120 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67085, tnet num: 19310, tinst num: 8072, tnode num: 90984, tedge num: 110591.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.924225s wall, 1.921875s user + 0.000000s system = 1.921875s CPU (99.9%)

RUN-1004 : used memory is 1053 MB, reserved memory is 1052 MB, peak memory is 1120 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19310 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.470278s wall, 1.453125s user + 0.015625s system = 1.468750s CPU (99.9%)

RUN-1004 : used memory is 1058 MB, reserved memory is 1057 MB, peak memory is 1120 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 40c9047af6c60b8681e7bd8c3a1ad8946a1d4edd384cda8aef5d685a0a84c04c -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8072
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19312, pip num: 143698
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 321
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3210 valid insts, and 404581 bits set as '1'.
BIT-1004 : the usercode register value: 00000000010111011011100110110011
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  11.920730s wall, 116.234375s user + 0.265625s system = 116.500000s CPU (977.3%)

RUN-1004 : used memory is 1181 MB, reserved memory is 1167 MB, peak memory is 1296 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250821_095427.log"

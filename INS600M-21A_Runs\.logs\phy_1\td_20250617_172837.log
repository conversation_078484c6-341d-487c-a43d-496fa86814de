============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Tue Jun 17 17:28:37 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(516)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
RUN-1001 : Project manager successfully analyzed 22 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.548122s wall, 1.671875s user + 3.843750s system = 5.515625s CPU (99.4%)

RUN-1004 : used memory is 78 MB, reserved memory is 41 MB, peak memory is 90 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.840999s wall, 1.796875s user + 0.046875s system = 1.843750s CPU (100.1%)

RUN-1004 : used memory is 301 MB, reserved memory is 270 MB, peak memory is 304 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 58 trigger nets, 58 data nets.
KIT-1004 : Chipwatcher code = 1110111010000001
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=158) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=158) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=158)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=158)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 23276/23 useful/useless nets, 19984/12 useful/useless insts
SYN-1016 : Merged 30 instances.
SYN-1032 : 22879/20 useful/useless nets, 20489/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 478 better
SYN-1014 : Optimize round 2
SYN-1032 : 22495/45 useful/useless nets, 20105/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.475660s wall, 2.390625s user + 0.093750s system = 2.484375s CPU (100.4%)

RUN-1004 : used memory is 329 MB, reserved memory is 297 MB, peak memory is 331 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22567/441 useful/useless nets, 20228/63 useful/useless insts
SYN-1016 : Merged 69 instances.
SYN-2571 : Optimize after map_dsp, round 1, 573 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 56 instances.
SYN-2501 : Optimize round 1, 114 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 23071/5 useful/useless nets, 20732/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 85008, tnet num: 23071, tinst num: 20731, tnode num: 119221, tedge num: 132480.
TMR-2508 : Levelizing timing graph completed, there are 85 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.221914s wall, 1.203125s user + 0.015625s system = 1.218750s CPU (99.7%)

RUN-1004 : used memory is 475 MB, reserved memory is 443 MB, peak memory is 475 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 23071 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 282 (3.29), #lev = 7 (1.66)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 282 (3.29), #lev = 7 (1.66)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 614 instances into 282 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 487 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 160 adder to BLE ...
SYN-4008 : Packed 160 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.628681s wall, 4.578125s user + 0.046875s system = 4.625000s CPU (99.9%)

RUN-1004 : used memory is 357 MB, reserved memory is 322 MB, peak memory is 586 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.425128s wall, 7.250000s user + 0.171875s system = 7.421875s CPU (100.0%)

RUN-1004 : used memory is 357 MB, reserved memory is 323 MB, peak memory is 586 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/rmc_com2/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/rmc_com2/GPRMC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/rmc_com2/GPRMC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/rmc_com2/GPRMC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/rmc_com2/GPRMC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/rmc_com2/GPRMC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/rmc_com2/GPRMC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/rmc_com2/GPRMC_data[0]
SYN-5055 WARNING: The kept net COM2/rx_err will be merged to another kept net COM2/rmc_com2/GPRMC_err
SYN-5055 WARNING: The kept net FMC/Error_state[0] will be merged to another kept net COM2/rmc_com2/GPRMC_sub_state_cnt0[3]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (335 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19976 instances
RUN-0007 : 5811 luts, 12607 seqs, 951 mslices, 494 lslices, 56 pads, 52 brams, 0 dsps
RUN-1001 : There are total 22343 nets
RUN-1001 : 16623 nets have 2 pins
RUN-1001 : 4530 nets have [3 - 5] pins
RUN-1001 : 802 nets have [6 - 10] pins
RUN-1001 : 262 nets have [11 - 20] pins
RUN-1001 : 102 nets have [21 - 99] pins
RUN-1001 : 24 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4774     
RUN-1001 :   No   |  No   |  Yes  |     670     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    6556     
RUN-1001 :   Yes  |  No   |  Yes  |     507     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  119  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 127
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19974 instances, 5811 luts, 12607 seqs, 1445 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1131 pins
PHY-3001 : Huge net DATA/done_div with 1644 pins
PHY-0007 : Cell area utilization is 64%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83375, tnet num: 22341, tinst num: 19974, tnode num: 117563, tedge num: 131018.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.271296s wall, 1.265625s user + 0.015625s system = 1.281250s CPU (100.8%)

RUN-1004 : used memory is 536 MB, reserved memory is 508 MB, peak memory is 586 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22341 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.111059s wall, 2.046875s user + 0.062500s system = 2.109375s CPU (99.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.42346e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19974.
PHY-3001 : Level 1 #clusters 2021.
PHY-3001 : End clustering;  0.159864s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (136.8%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 64%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 874876, overlap = 661.781
PHY-3002 : Step(2): len = 805811, overlap = 705.531
PHY-3002 : Step(3): len = 531921, overlap = 912.094
PHY-3002 : Step(4): len = 470584, overlap = 974.688
PHY-3002 : Step(5): len = 390223, overlap = 1064.38
PHY-3002 : Step(6): len = 349788, overlap = 1102.03
PHY-3002 : Step(7): len = 300258, overlap = 1189.88
PHY-3002 : Step(8): len = 259776, overlap = 1237.03
PHY-3002 : Step(9): len = 229013, overlap = 1304.19
PHY-3002 : Step(10): len = 208995, overlap = 1359.19
PHY-3002 : Step(11): len = 191325, overlap = 1410.41
PHY-3002 : Step(12): len = 171103, overlap = 1453.97
PHY-3002 : Step(13): len = 154862, overlap = 1495.25
PHY-3002 : Step(14): len = 143211, overlap = 1537.53
PHY-3002 : Step(15): len = 132020, overlap = 1555.53
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.03334e-06
PHY-3002 : Step(16): len = 138330, overlap = 1536.56
PHY-3002 : Step(17): len = 183272, overlap = 1413.69
PHY-3002 : Step(18): len = 195302, overlap = 1310.16
PHY-3002 : Step(19): len = 198468, overlap = 1210.56
PHY-3002 : Step(20): len = 188534, overlap = 1190.78
PHY-3002 : Step(21): len = 185636, overlap = 1191.62
PHY-3002 : Step(22): len = 181613, overlap = 1198.28
PHY-3002 : Step(23): len = 177685, overlap = 1189.56
PHY-3002 : Step(24): len = 172722, overlap = 1176.12
PHY-3002 : Step(25): len = 169688, overlap = 1177.66
PHY-3002 : Step(26): len = 168504, overlap = 1186.78
PHY-3002 : Step(27): len = 167118, overlap = 1185.78
PHY-3002 : Step(28): len = 165868, overlap = 1177.31
PHY-3002 : Step(29): len = 164706, overlap = 1169.09
PHY-3002 : Step(30): len = 164347, overlap = 1168.56
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.06669e-06
PHY-3002 : Step(31): len = 172797, overlap = 1145.06
PHY-3002 : Step(32): len = 187664, overlap = 1061.56
PHY-3002 : Step(33): len = 190799, overlap = 990.5
PHY-3002 : Step(34): len = 192355, overlap = 947.938
PHY-3002 : Step(35): len = 191928, overlap = 942.438
PHY-3002 : Step(36): len = 191739, overlap = 953.656
PHY-3002 : Step(37): len = 191039, overlap = 968.062
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.13337e-06
PHY-3002 : Step(38): len = 201054, overlap = 904.906
PHY-3002 : Step(39): len = 220828, overlap = 841.281
PHY-3002 : Step(40): len = 227520, overlap = 817.719
PHY-3002 : Step(41): len = 228260, overlap = 800.031
PHY-3002 : Step(42): len = 227559, overlap = 797.75
PHY-3002 : Step(43): len = 225595, overlap = 798.344
PHY-3002 : Step(44): len = 225005, overlap = 795.281
PHY-3002 : Step(45): len = 223251, overlap = 782.969
PHY-3002 : Step(46): len = 222456, overlap = 777.125
PHY-3002 : Step(47): len = 221191, overlap = 779.719
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 8.26674e-06
PHY-3002 : Step(48): len = 234047, overlap = 737.594
PHY-3002 : Step(49): len = 249873, overlap = 658.438
PHY-3002 : Step(50): len = 251987, overlap = 631.344
PHY-3002 : Step(51): len = 252762, overlap = 596.469
PHY-3002 : Step(52): len = 252889, overlap = 581.562
PHY-3002 : Step(53): len = 252937, overlap = 586.438
PHY-3002 : Step(54): len = 251158, overlap = 598.906
PHY-3002 : Step(55): len = 250224, overlap = 626.344
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.65335e-05
PHY-3002 : Step(56): len = 261322, overlap = 580.781
PHY-3002 : Step(57): len = 271105, overlap = 560.938
PHY-3002 : Step(58): len = 274232, overlap = 517.531
PHY-3002 : Step(59): len = 277349, overlap = 480
PHY-3002 : Step(60): len = 277866, overlap = 470.062
PHY-3002 : Step(61): len = 277496, overlap = 474.438
PHY-3002 : Step(62): len = 276203, overlap = 468.375
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.3067e-05
PHY-3002 : Step(63): len = 286072, overlap = 457.594
PHY-3002 : Step(64): len = 295296, overlap = 428.781
PHY-3002 : Step(65): len = 297753, overlap = 412.406
PHY-3002 : Step(66): len = 299259, overlap = 409.25
PHY-3002 : Step(67): len = 299781, overlap = 393.531
PHY-3002 : Step(68): len = 302102, overlap = 383.75
PHY-3002 : Step(69): len = 300946, overlap = 391.625
PHY-3002 : Step(70): len = 299210, overlap = 397.25
PHY-3002 : Step(71): len = 297291, overlap = 405.969
PHY-3002 : Step(72): len = 298794, overlap = 422.656
PHY-3002 : Step(73): len = 297229, overlap = 427.25
PHY-3002 : Step(74): len = 295324, overlap = 425.094
PHY-3002 : Step(75): len = 293591, overlap = 435.781
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 6.55137e-05
PHY-3002 : Step(76): len = 301881, overlap = 449.75
PHY-3002 : Step(77): len = 306162, overlap = 468.75
PHY-3002 : Step(78): len = 306989, overlap = 474.031
PHY-3002 : Step(79): len = 307967, overlap = 474.312
PHY-3002 : Step(80): len = 307577, overlap = 454.438
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000131027
PHY-3002 : Step(81): len = 310703, overlap = 453.344
PHY-3002 : Step(82): len = 317368, overlap = 414.719
PHY-3002 : Step(83): len = 319959, overlap = 359
PHY-3002 : Step(84): len = 322045, overlap = 364.969
PHY-3002 : Step(85): len = 321133, overlap = 362.781
PHY-3002 : Step(86): len = 320110, overlap = 364.875
PHY-3002 : Step(87): len = 319479, overlap = 362.125
PHY-3002 : Step(88): len = 318265, overlap = 354.75
PHY-3002 : Step(89): len = 318055, overlap = 359.75
PHY-3002 : Step(90): len = 318993, overlap = 351.75
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000224953
PHY-3002 : Step(91): len = 320798, overlap = 343.75
PHY-3002 : Step(92): len = 323908, overlap = 337.531
PHY-3002 : Step(93): len = 326597, overlap = 345.844
PHY-3002 : Step(94): len = 329329, overlap = 340.781
PHY-3002 : Step(95): len = 329899, overlap = 336.531
PHY-3002 : Step(96): len = 330213, overlap = 321.375
PHY-3002 : Step(97): len = 328511, overlap = 324
PHY-3002 : Step(98): len = 327971, overlap = 320.438
PHY-3002 : Step(99): len = 327823, overlap = 315.188
PHY-3002 : Step(100): len = 327895, overlap = 324.125
PHY-3002 : Step(101): len = 326593, overlap = 326.625
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.016043s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (97.4%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/22343.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 428984, over cnt = 1230(3%), over = 5793, worst = 49
PHY-1001 : End global iterations;  0.901053s wall, 1.218750s user + 0.046875s system = 1.265625s CPU (140.5%)

PHY-1001 : Congestion index: top1 = 78.10, top5 = 55.37, top10 = 44.93, top15 = 38.94.
PHY-3001 : End congestion estimation;  1.176872s wall, 1.437500s user + 0.093750s system = 1.531250s CPU (130.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22341 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.022453s wall, 1.015625s user + 0.031250s system = 1.046875s CPU (102.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.06978e-05
PHY-3002 : Step(102): len = 368274, overlap = 261.094
PHY-3002 : Step(103): len = 386779, overlap = 245.875
PHY-3002 : Step(104): len = 386454, overlap = 237.656
PHY-3002 : Step(105): len = 383045, overlap = 233.875
PHY-3002 : Step(106): len = 385605, overlap = 228.531
PHY-3002 : Step(107): len = 388292, overlap = 204.719
PHY-3002 : Step(108): len = 392157, overlap = 189.781
PHY-3002 : Step(109): len = 396001, overlap = 189.25
PHY-3002 : Step(110): len = 396078, overlap = 186.25
PHY-3002 : Step(111): len = 398422, overlap = 180.219
PHY-3002 : Step(112): len = 397041, overlap = 184.219
PHY-3002 : Step(113): len = 396356, overlap = 175.219
PHY-3002 : Step(114): len = 396448, overlap = 162.312
PHY-3002 : Step(115): len = 395992, overlap = 158
PHY-3002 : Step(116): len = 397116, overlap = 153.562
PHY-3002 : Step(117): len = 399195, overlap = 152.219
PHY-3002 : Step(118): len = 398520, overlap = 147.125
PHY-3002 : Step(119): len = 399896, overlap = 138.469
PHY-3002 : Step(120): len = 401401, overlap = 131.625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000181396
PHY-3002 : Step(121): len = 400526, overlap = 121.219
PHY-3002 : Step(122): len = 402151, overlap = 115.781
PHY-3002 : Step(123): len = 404117, overlap = 111.438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000362791
PHY-3002 : Step(124): len = 408991, overlap = 105.406
PHY-3002 : Step(125): len = 417326, overlap = 99.2188
PHY-3002 : Step(126): len = 422702, overlap = 101.938
PHY-3002 : Step(127): len = 423045, overlap = 104.219
PHY-3002 : Step(128): len = 425259, overlap = 104.594
PHY-3002 : Step(129): len = 428871, overlap = 105.5
PHY-3002 : Step(130): len = 428382, overlap = 105.25
PHY-3002 : Step(131): len = 430747, overlap = 104.5
PHY-3002 : Step(132): len = 433354, overlap = 103.781
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000725582
PHY-3002 : Step(133): len = 431553, overlap = 103.75
PHY-3002 : Step(134): len = 433806, overlap = 105.219
PHY-3002 : Step(135): len = 435989, overlap = 109.938
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00117399
PHY-3002 : Step(136): len = 436322, overlap = 106.125
PHY-3002 : Step(137): len = 442525, overlap = 111.281
PHY-3002 : Step(138): len = 459238, overlap = 115.781
PHY-3002 : Step(139): len = 466777, overlap = 117.812
PHY-3002 : Step(140): len = 462979, overlap = 122.906
PHY-3002 : Step(141): len = 461498, overlap = 126.094
PHY-3002 : Step(142): len = 460864, overlap = 115.5
PHY-3002 : Step(143): len = 460362, overlap = 108.781
PHY-3002 : Step(144): len = 458005, overlap = 113.969
PHY-3002 : Step(145): len = 458597, overlap = 109.625
PHY-3002 : Step(146): len = 461716, overlap = 110.281
PHY-3002 : Step(147): len = 462205, overlap = 111.438
PHY-3002 : Step(148): len = 462157, overlap = 104.281
PHY-3002 : Step(149): len = 461094, overlap = 115.75
PHY-3002 : Step(150): len = 462278, overlap = 114.469
PHY-3002 : Step(151): len = 462134, overlap = 113.281
PHY-3002 : Step(152): len = 461540, overlap = 111.938
PHY-3002 : Step(153): len = 462004, overlap = 115.75
PHY-3002 : Step(154): len = 461895, overlap = 116.594
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(155): len = 462175, overlap = 117.5
PHY-3002 : Step(156): len = 464192, overlap = 115.438
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 53/22343.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 534536, over cnt = 2198(6%), over = 10695, worst = 45
PHY-1001 : End global iterations;  1.133616s wall, 1.953125s user + 0.031250s system = 1.984375s CPU (175.0%)

PHY-1001 : Congestion index: top1 = 83.90, top5 = 62.95, top10 = 53.16, top15 = 47.25.
PHY-3001 : End congestion estimation;  1.468190s wall, 2.281250s user + 0.031250s system = 2.312500s CPU (157.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22341 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.067720s wall, 1.046875s user + 0.015625s system = 1.062500s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.20165e-05
PHY-3002 : Step(157): len = 473942, overlap = 408.625
PHY-3002 : Step(158): len = 484099, overlap = 353.406
PHY-3002 : Step(159): len = 480381, overlap = 325.938
PHY-3002 : Step(160): len = 472459, overlap = 311.281
PHY-3002 : Step(161): len = 466285, overlap = 292.188
PHY-3002 : Step(162): len = 461806, overlap = 279.406
PHY-3002 : Step(163): len = 459039, overlap = 275.156
PHY-3002 : Step(164): len = 455862, overlap = 253.594
PHY-3002 : Step(165): len = 454479, overlap = 249.844
PHY-3002 : Step(166): len = 453460, overlap = 245.781
PHY-3002 : Step(167): len = 451165, overlap = 247.812
PHY-3002 : Step(168): len = 449473, overlap = 236.562
PHY-3002 : Step(169): len = 448952, overlap = 232.219
PHY-3002 : Step(170): len = 449150, overlap = 228.656
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000164033
PHY-3002 : Step(171): len = 448467, overlap = 228.938
PHY-3002 : Step(172): len = 449908, overlap = 215.5
PHY-3002 : Step(173): len = 451346, overlap = 209.906
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000328066
PHY-3002 : Step(174): len = 453597, overlap = 206.938
PHY-3002 : Step(175): len = 459625, overlap = 195.406
PHY-3002 : Step(176): len = 462700, overlap = 187.312
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000656132
PHY-3002 : Step(177): len = 464441, overlap = 178.25
PHY-3002 : Step(178): len = 470906, overlap = 172.219
PHY-3002 : Step(179): len = 478617, overlap = 164.406
PHY-3002 : Step(180): len = 478911, overlap = 153.156
PHY-3002 : Step(181): len = 477008, overlap = 155.875
PHY-3002 : Step(182): len = 476223, overlap = 150.719
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83375, tnet num: 22341, tinst num: 19974, tnode num: 117563, tedge num: 131018.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.718916s wall, 1.640625s user + 0.078125s system = 1.718750s CPU (100.0%)

RUN-1004 : used memory is 573 MB, reserved memory is 549 MB, peak memory is 712 MB
OPT-1001 : Total overflow 540.16 peak overflow 3.50
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 369/22343.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 558888, over cnt = 2532(7%), over = 9317, worst = 35
PHY-1001 : End global iterations;  1.328617s wall, 2.109375s user + 0.000000s system = 2.109375s CPU (158.8%)

PHY-1001 : Congestion index: top1 = 60.50, top5 = 48.79, top10 = 43.65, top15 = 40.59.
PHY-1001 : End incremental global routing;  1.593676s wall, 2.375000s user + 0.000000s system = 2.375000s CPU (149.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22341 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.110990s wall, 1.109375s user + 0.000000s system = 1.109375s CPU (99.9%)

OPT-1001 : 17 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 56 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19898 has valid locations, 262 needs to be replaced
PHY-3001 : design contains 20219 instances, 5912 luts, 12751 seqs, 1445 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 494109
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17402/22588.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 573760, over cnt = 2567(7%), over = 9385, worst = 35
PHY-1001 : End global iterations;  0.215347s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (101.6%)

PHY-1001 : Congestion index: top1 = 60.80, top5 = 48.98, top10 = 43.92, top15 = 40.89.
PHY-3001 : End congestion estimation;  0.489805s wall, 0.468750s user + 0.015625s system = 0.484375s CPU (98.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 84201, tnet num: 22586, tinst num: 20219, tnode num: 118739, tedge num: 132180.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.646223s wall, 1.640625s user + 0.015625s system = 1.656250s CPU (100.6%)

RUN-1004 : used memory is 621 MB, reserved memory is 614 MB, peak memory is 713 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22586 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.798254s wall, 2.781250s user + 0.015625s system = 2.796875s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(183): len = 493794, overlap = 2.5
PHY-3002 : Step(184): len = 494860, overlap = 2.625
PHY-3002 : Step(185): len = 496165, overlap = 2.625
PHY-3002 : Step(186): len = 496418, overlap = 2.5625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17431/22588.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 571968, over cnt = 2578(7%), over = 9497, worst = 35
PHY-1001 : End global iterations;  0.222231s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (119.5%)

PHY-1001 : Congestion index: top1 = 60.78, top5 = 49.30, top10 = 44.19, top15 = 41.15.
PHY-3001 : End congestion estimation;  0.495716s wall, 0.515625s user + 0.015625s system = 0.531250s CPU (107.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22586 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.100466s wall, 1.062500s user + 0.031250s system = 1.093750s CPU (99.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000596736
PHY-3002 : Step(187): len = 496522, overlap = 153.938
PHY-3002 : Step(188): len = 496758, overlap = 153.281
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00119347
PHY-3002 : Step(189): len = 497057, overlap = 153.844
PHY-3002 : Step(190): len = 497453, overlap = 153.781
PHY-3001 : Final: Len = 497453, Over = 153.781
PHY-3001 : End incremental placement;  5.865066s wall, 6.250000s user + 0.234375s system = 6.484375s CPU (110.6%)

OPT-1001 : Total overflow 545.88 peak overflow 3.50
OPT-1001 : End high-fanout net optimization;  9.180413s wall, 10.500000s user + 0.250000s system = 10.750000s CPU (117.1%)

OPT-1001 : Current memory(MB): used = 715, reserve = 696, peak = 732.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17445/22588.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 574304, over cnt = 2530(7%), over = 8979, worst = 35
PHY-1002 : len = 616376, over cnt = 1904(5%), over = 5164, worst = 22
PHY-1002 : len = 666688, over cnt = 719(2%), over = 1605, worst = 22
PHY-1002 : len = 680848, over cnt = 299(0%), over = 645, worst = 11
PHY-1002 : len = 691256, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.562461s wall, 2.203125s user + 0.031250s system = 2.234375s CPU (143.0%)

PHY-1001 : Congestion index: top1 = 50.71, top5 = 44.31, top10 = 41.27, top15 = 39.33.
OPT-1001 : End congestion update;  1.856365s wall, 2.484375s user + 0.031250s system = 2.515625s CPU (135.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22586 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.969894s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (99.9%)

OPT-0007 : Start: WNS 4377 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.832621s wall, 3.468750s user + 0.031250s system = 3.500000s CPU (123.6%)

OPT-1001 : Current memory(MB): used = 690, reserve = 671, peak = 732.
OPT-1001 : End physical optimization;  14.106618s wall, 16.140625s user + 0.375000s system = 16.515625s CPU (117.1%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5912 LUT to BLE ...
SYN-4008 : Packed 5912 LUT and 2931 SEQ to BLE.
SYN-4003 : Packing 9820 remaining SEQ's ...
SYN-4005 : Packed 3336 SEQ with LUT/SLICE
SYN-4006 : 159 single LUT's are left
SYN-4006 : 6484 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 12396/14146 primitive instances ...
PHY-3001 : End packing;  3.276467s wall, 3.265625s user + 0.000000s system = 3.265625s CPU (99.7%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8361 instances
RUN-1001 : 4124 mslices, 4124 lslices, 56 pads, 52 brams, 0 dsps
RUN-1001 : There are total 19707 nets
RUN-1001 : 13623 nets have 2 pins
RUN-1001 : 4661 nets have [3 - 5] pins
RUN-1001 : 859 nets have [6 - 10] pins
RUN-1001 : 405 nets have [11 - 20] pins
RUN-1001 : 149 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
PHY-3001 : design contains 8359 instances, 8248 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Cell area utilization is 87%
PHY-3001 : After packing: Len = 517108, Over = 396.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 8094/19707.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 656208, over cnt = 1675(4%), over = 2580, worst = 7
PHY-1002 : len = 661928, over cnt = 1085(3%), over = 1449, worst = 6
PHY-1002 : len = 675448, over cnt = 278(0%), over = 353, worst = 4
PHY-1002 : len = 680560, over cnt = 48(0%), over = 51, worst = 3
PHY-1002 : len = 681680, over cnt = 2(0%), over = 2, worst = 1
PHY-1001 : End global iterations;  1.350977s wall, 2.093750s user + 0.031250s system = 2.125000s CPU (157.3%)

PHY-1001 : Congestion index: top1 = 51.06, top5 = 44.24, top10 = 40.88, top15 = 38.68.
PHY-3001 : End congestion estimation;  1.703783s wall, 2.453125s user + 0.031250s system = 2.484375s CPU (145.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69575, tnet num: 19705, tinst num: 8359, tnode num: 94735, tedge num: 114626.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.830820s wall, 1.828125s user + 0.000000s system = 1.828125s CPU (99.9%)

RUN-1004 : used memory is 611 MB, reserved memory is 601 MB, peak memory is 732 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19705 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.857164s wall, 2.843750s user + 0.015625s system = 2.859375s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.74364e-05
PHY-3002 : Step(191): len = 518334, overlap = 373.75
PHY-3002 : Step(192): len = 515946, overlap = 382
PHY-3002 : Step(193): len = 515194, overlap = 385
PHY-3002 : Step(194): len = 515326, overlap = 398.75
PHY-3002 : Step(195): len = 514227, overlap = 410
PHY-3002 : Step(196): len = 513830, overlap = 420
PHY-3002 : Step(197): len = 511426, overlap = 416.5
PHY-3002 : Step(198): len = 510830, overlap = 418
PHY-3002 : Step(199): len = 508906, overlap = 424
PHY-3002 : Step(200): len = 507651, overlap = 422
PHY-3002 : Step(201): len = 507213, overlap = 420
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.48728e-05
PHY-3002 : Step(202): len = 509716, overlap = 412
PHY-3002 : Step(203): len = 513284, overlap = 407.25
PHY-3002 : Step(204): len = 515333, overlap = 395.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000189746
PHY-3002 : Step(205): len = 521870, overlap = 391.5
PHY-3002 : Step(206): len = 529757, overlap = 372
PHY-3002 : Step(207): len = 529109, overlap = 370
PHY-3002 : Step(208): len = 528217, overlap = 370.25
PHY-3002 : Step(209): len = 528275, overlap = 370.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.724947s wall, 0.843750s user + 0.906250s system = 1.750000s CPU (241.4%)

PHY-3001 : Trial Legalized: Len = 645987
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 602/19707.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 739248, over cnt = 2580(7%), over = 4159, worst = 8
PHY-1002 : len = 754200, over cnt = 1664(4%), over = 2345, worst = 8
PHY-1002 : len = 770072, over cnt = 835(2%), over = 1143, worst = 8
PHY-1002 : len = 784760, over cnt = 251(0%), over = 338, worst = 8
PHY-1002 : len = 791016, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.052114s wall, 3.390625s user + 0.078125s system = 3.468750s CPU (169.0%)

PHY-1001 : Congestion index: top1 = 51.03, top5 = 46.31, top10 = 43.66, top15 = 41.93.
PHY-3001 : End congestion estimation;  2.448374s wall, 3.765625s user + 0.093750s system = 3.859375s CPU (157.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19705 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.005235s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000193652
PHY-3002 : Step(210): len = 601895, overlap = 79.5
PHY-3002 : Step(211): len = 582919, overlap = 128.25
PHY-3002 : Step(212): len = 569866, overlap = 174
PHY-3002 : Step(213): len = 562424, overlap = 205
PHY-3002 : Step(214): len = 556823, overlap = 239.5
PHY-3002 : Step(215): len = 554085, overlap = 259
PHY-3002 : Step(216): len = 551904, overlap = 267.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000387303
PHY-3002 : Step(217): len = 556209, overlap = 263
PHY-3002 : Step(218): len = 559971, overlap = 257.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000774606
PHY-3002 : Step(219): len = 562470, overlap = 255
PHY-3002 : Step(220): len = 571244, overlap = 253.5
PHY-3002 : Step(221): len = 573467, overlap = 246.75
PHY-3002 : Step(222): len = 573354, overlap = 247.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.034130s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (91.6%)

PHY-3001 : Legalized: Len = 615575, Over = 0
PHY-3001 : Spreading special nets. 44 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.094036s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (99.7%)

PHY-3001 : 67 instances has been re-located, deltaX = 16, deltaY = 41, maxDist = 2.
PHY-3001 : Final: Len = 616459, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69575, tnet num: 19705, tinst num: 8359, tnode num: 94735, tedge num: 114626.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.141907s wall, 2.093750s user + 0.046875s system = 2.140625s CPU (99.9%)

RUN-1004 : used memory is 624 MB, reserved memory is 628 MB, peak memory is 732 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3605/19707.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 719680, over cnt = 2371(6%), over = 3700, worst = 6
PHY-1002 : len = 731040, over cnt = 1479(4%), over = 2061, worst = 6
PHY-1002 : len = 747160, over cnt = 679(1%), over = 901, worst = 5
PHY-1002 : len = 758480, over cnt = 218(0%), over = 280, worst = 4
PHY-1002 : len = 763288, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.857978s wall, 3.062500s user + 0.031250s system = 3.093750s CPU (166.5%)

PHY-1001 : Congestion index: top1 = 48.49, top5 = 44.33, top10 = 41.83, top15 = 40.03.
PHY-1001 : End incremental global routing;  2.181500s wall, 3.375000s user + 0.031250s system = 3.406250s CPU (156.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19705 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.020555s wall, 1.015625s user + 0.015625s system = 1.031250s CPU (101.0%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 56 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8298 has valid locations, 16 needs to be replaced
PHY-3001 : design contains 8373 instances, 8262 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 621397
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17856/19724.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 769664, over cnt = 52(0%), over = 67, worst = 5
PHY-1002 : len = 769688, over cnt = 37(0%), over = 41, worst = 4
PHY-1002 : len = 769552, over cnt = 18(0%), over = 19, worst = 2
PHY-1002 : len = 769904, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 769984, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.795339s wall, 0.812500s user + 0.031250s system = 0.843750s CPU (106.1%)

PHY-1001 : Congestion index: top1 = 48.62, top5 = 44.58, top10 = 42.11, top15 = 40.32.
PHY-3001 : End congestion estimation;  1.149115s wall, 1.156250s user + 0.031250s system = 1.187500s CPU (103.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69691, tnet num: 19722, tinst num: 8373, tnode num: 94875, tedge num: 114784.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.235151s wall, 2.234375s user + 0.000000s system = 2.234375s CPU (100.0%)

RUN-1004 : used memory is 652 MB, reserved memory is 639 MB, peak memory is 732 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19722 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.301997s wall, 3.296875s user + 0.015625s system = 3.312500s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(223): len = 620827, overlap = 0.5
PHY-3002 : Step(224): len = 620474, overlap = 0.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17844/19724.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 767728, over cnt = 29(0%), over = 39, worst = 4
PHY-1002 : len = 767720, over cnt = 23(0%), over = 26, worst = 3
PHY-1002 : len = 767784, over cnt = 9(0%), over = 10, worst = 2
PHY-1002 : len = 767984, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 768000, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.773459s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (103.0%)

PHY-1001 : Congestion index: top1 = 49.66, top5 = 44.83, top10 = 42.14, top15 = 40.29.
PHY-3001 : End congestion estimation;  1.105978s wall, 1.140625s user + 0.000000s system = 1.140625s CPU (103.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19722 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.036832s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00110927
PHY-3002 : Step(225): len = 620431, overlap = 1.25
PHY-3002 : Step(226): len = 620267, overlap = 1
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007451s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 620304, Over = 0
PHY-3001 : End spreading;  0.077312s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (101.1%)

PHY-3001 : Final: Len = 620304, Over = 0
PHY-3001 : End incremental placement;  7.315423s wall, 7.515625s user + 0.093750s system = 7.609375s CPU (104.0%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  11.071312s wall, 12.453125s user + 0.140625s system = 12.593750s CPU (113.8%)

OPT-1001 : Current memory(MB): used = 728, reserve = 713, peak = 734.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17844/19724.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 767744, over cnt = 34(0%), over = 47, worst = 5
PHY-1002 : len = 767760, over cnt = 23(0%), over = 27, worst = 5
PHY-1002 : len = 767944, over cnt = 14(0%), over = 14, worst = 1
PHY-1002 : len = 767992, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 768200, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.764786s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (106.2%)

PHY-1001 : Congestion index: top1 = 49.18, top5 = 44.69, top10 = 42.09, top15 = 40.28.
OPT-1001 : End congestion update;  1.098558s wall, 1.156250s user + 0.000000s system = 1.156250s CPU (105.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19722 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.869036s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (98.9%)

OPT-0007 : Start: WNS 4530 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.973592s wall, 2.031250s user + 0.000000s system = 2.031250s CPU (102.9%)

OPT-1001 : Current memory(MB): used = 728, reserve = 713, peak = 734.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19722 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.915848s wall, 0.906250s user + 0.000000s system = 0.906250s CPU (99.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17870/19724.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 768200, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.129155s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (96.8%)

PHY-1001 : Congestion index: top1 = 49.18, top5 = 44.69, top10 = 42.09, top15 = 40.28.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19722 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.891347s wall, 0.875000s user + 0.000000s system = 0.875000s CPU (98.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4530 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 48.758621
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4530ps with logic level 3 
RUN-1001 :       #2 path slack 4534ps with logic level 3 
RUN-1001 :       #3 path slack 4561ps with logic level 5 
RUN-1001 :       #4 path slack 4613ps with logic level 4 
RUN-1001 :       #5 path slack 4620ps with logic level 4 
OPT-1001 : End physical optimization;  17.743108s wall, 19.281250s user + 0.187500s system = 19.468750s CPU (109.7%)

RUN-1003 : finish command "place" in  78.166598s wall, 146.046875s user + 8.828125s system = 154.875000s CPU (198.1%)

RUN-1004 : used memory is 609 MB, reserved memory is 587 MB, peak memory is 734 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.813797s wall, 3.109375s user + 0.062500s system = 3.171875s CPU (174.9%)

RUN-1004 : used memory is 610 MB, reserved memory is 588 MB, peak memory is 734 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8375 instances
RUN-1001 : 4133 mslices, 4129 lslices, 56 pads, 52 brams, 0 dsps
RUN-1001 : There are total 19724 nets
RUN-1001 : 13621 nets have 2 pins
RUN-1001 : 4660 nets have [3 - 5] pins
RUN-1001 : 872 nets have [6 - 10] pins
RUN-1001 : 413 nets have [11 - 20] pins
RUN-1001 : 148 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69691, tnet num: 19722, tinst num: 8373, tnode num: 94875, tedge num: 114784.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.881632s wall, 1.890625s user + 0.000000s system = 1.890625s CPU (100.5%)

RUN-1004 : used memory is 620 MB, reserved memory is 616 MB, peak memory is 734 MB
PHY-1001 : 4133 mslices, 4129 lslices, 56 pads, 52 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19722 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 700504, over cnt = 2485(7%), over = 4070, worst = 8
PHY-1002 : len = 715712, over cnt = 1645(4%), over = 2331, worst = 6
PHY-1002 : len = 736192, over cnt = 613(1%), over = 842, worst = 5
PHY-1002 : len = 750984, over cnt = 54(0%), over = 76, worst = 4
PHY-1002 : len = 752688, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.941293s wall, 3.437500s user + 0.125000s system = 3.562500s CPU (183.5%)

PHY-1001 : Congestion index: top1 = 48.75, top5 = 43.82, top10 = 41.31, top15 = 39.62.
PHY-1001 : End global routing;  2.317022s wall, 3.812500s user + 0.125000s system = 3.937500s CPU (169.9%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 713, reserve = 702, peak = 734.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 983, reserve = 969, peak = 983.
PHY-1001 : End build detailed router design. 4.737592s wall, 4.656250s user + 0.078125s system = 4.734375s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 194424, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.926119s wall, 0.906250s user + 0.015625s system = 0.921875s CPU (99.5%)

PHY-1001 : Current memory(MB): used = 1019, reserve = 1006, peak = 1019.
PHY-1001 : End phase 1; 0.933132s wall, 0.906250s user + 0.015625s system = 0.921875s CPU (98.8%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 55% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.8557e+06, over cnt = 1449(0%), over = 1459, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1034, reserve = 1022, peak = 1034.
PHY-1001 : End initial routed; 24.321047s wall, 50.921875s user + 0.421875s system = 51.343750s CPU (211.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18504(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.726   |   0.000   |   0   
RUN-1001 :   Hold   |   0.137   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.645331s wall, 3.640625s user + 0.000000s system = 3.640625s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 1046, reserve = 1034, peak = 1046.
PHY-1001 : End phase 2; 27.966538s wall, 54.562500s user + 0.421875s system = 54.984375s CPU (196.6%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.8557e+06, over cnt = 1449(0%), over = 1459, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.276624s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (101.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.8418e+06, over cnt = 485(0%), over = 485, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.834025s wall, 1.546875s user + 0.000000s system = 1.546875s CPU (185.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.84261e+06, over cnt = 123(0%), over = 123, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.383803s wall, 0.546875s user + 0.015625s system = 0.562500s CPU (146.6%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.84359e+06, over cnt = 5(0%), over = 5, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.287913s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (119.4%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.8437e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 4; 0.172824s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (108.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18504(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.373   |   0.000   |   0   
RUN-1001 :   Hold   |   0.137   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.638299s wall, 3.640625s user + 0.000000s system = 3.640625s CPU (100.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 359 feed throughs used by 317 nets
PHY-1001 : End commit to database; 2.394748s wall, 2.406250s user + 0.000000s system = 2.406250s CPU (100.5%)

PHY-1001 : Current memory(MB): used = 1135, reserve = 1127, peak = 1135.
PHY-1001 : End phase 3; 8.501923s wall, 9.421875s user + 0.031250s system = 9.453125s CPU (111.2%)

PHY-1003 : Routed, final wirelength = 1.8437e+06
PHY-1001 : Current memory(MB): used = 1140, reserve = 1131, peak = 1140.
PHY-1001 : End export database. 0.075718s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (103.2%)

PHY-1001 : End detail routing;  42.667867s wall, 70.093750s user + 0.546875s system = 70.640625s CPU (165.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69691, tnet num: 19722, tinst num: 8373, tnode num: 94875, tedge num: 114784.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.817766s wall, 1.812500s user + 0.000000s system = 1.812500s CPU (99.7%)

RUN-1004 : used memory is 1070 MB, reserved memory is 1070 MB, peak memory is 1140 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  51.556854s wall, 80.437500s user + 0.703125s system = 81.140625s CPU (157.4%)

RUN-1004 : used memory is 1069 MB, reserved memory is 1070 MB, peak memory is 1140 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8971   out of  19600   45.77%
#reg                    12852   out of  19600   65.57%
#le                     15425
  #lut only              2573   out of  15425   16.68%
  #reg only              6454   out of  15425   41.84%
  #lut&reg               6398   out of  15425   41.48%
#dsp                        0   out of     29    0.00%
#bram                      52   out of     64   81.25%
  #bram9k                  52
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       56   out of    188   29.79%
  #ireg                    10
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    7049
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          192
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT        E11        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT        A13        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT        F10        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         E8        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         E7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT        A10        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C6        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         D6        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         C7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT        E12        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         F1        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         A7        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H13        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J11        LVCMOS33          N/A          PULLUP          IREG       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT        E13        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         R7        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         M7        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         P7        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         T8        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         P8        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         N8        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M9        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         T9        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         R9        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         T6        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         P9        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         N9        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT        P11        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         T5        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         B2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        L10        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        E15        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        R14        LVCMOS33           8            N/A            OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT         R2        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT        N12        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        B10        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         J6        LVCMOS33           8            N/A            NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        R15        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E6        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15425  |7526    |1445    |12895   |52      |0       |
|  AnyFog_dataX                      |AnyFog          |223    |91      |22      |185     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |53      |22      |52      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |223    |121     |22      |183     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |85     |62      |22      |49      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |227    |127     |22      |187     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |62      |22      |51      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |3396   |869     |34      |3301    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |741    |98      |5       |727     |0       |0       |
|    STADOP_com2                     |STADOP          |554    |73      |0       |550     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |64     |46      |14      |40      |0       |0       |
|    head_com2                       |uniheading      |265    |63      |5       |249     |0       |0       |
|    rmc_com2                        |Gprmc           |45     |40      |0       |36      |0       |0       |
|    uart_com2                       |Agrica          |1440   |262     |10      |1412    |0       |0       |
|  COM3                              |COM3_Control    |216    |101     |14      |185     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |61     |39      |14      |39      |0       |0       |
|    rmc_com3                        |Gprmc           |155    |62      |0       |146     |0       |0       |
|  DATA                              |Data_Processing |8628   |4324    |1059    |6957    |0       |0       |
|    DIV_Dtemp                       |Divider         |811    |342     |84      |688     |0       |0       |
|    DIV_Utemp                       |Divider         |642    |312     |84      |519     |0       |0       |
|    DIV_accX                        |Divider         |596    |339     |84      |475     |0       |0       |
|    DIV_accY                        |Divider         |644    |353     |108     |478     |0       |0       |
|    DIV_accZ                        |Divider         |661    |370     |132     |456     |0       |0       |
|    DIV_rateX                       |Divider         |738    |397     |132     |530     |0       |0       |
|    DIV_rateY                       |Divider         |533    |343     |132     |327     |0       |0       |
|    DIV_rateZ                       |Divider         |578    |360     |132     |358     |0       |0       |
|    genclk                          |genclk          |71     |35      |20      |38      |0       |0       |
|  FMC                               |FMC_Ctrl        |522    |469     |43      |359     |0       |0       |
|  IIC                               |I2C_master      |280    |212     |11      |242     |0       |0       |
|  IMU_CTRL                          |SCHA634         |860    |685     |61      |701     |0       |0       |
|    CtrlData                        |CtrlData        |453    |402     |47      |330     |0       |0       |
|      usms                          |Time_1ms        |32     |27      |5       |23      |0       |0       |
|    SPIM                            |SPI_SCHA634     |407    |283     |14      |371     |0       |0       |
|  POWER                             |POWER_EN        |98     |49      |38      |37      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |746    |478     |119     |509     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |746    |478     |119     |509     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |331    |196     |0       |314     |0       |0       |
|        reg_inst                    |register        |330    |195     |0       |313     |0       |0       |
|        tap_inst                    |tap             |1      |1       |0       |1       |0       |0       |
|      trigger_inst                  |trigger         |415    |282     |119     |195     |0       |0       |
|        bus_inst                    |bus_top         |182    |120     |62      |67      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |26     |16      |10      |10      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |3      |3       |0       |3       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |54     |36      |18      |20      |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |97     |63      |34      |32      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |149    |116     |29      |95      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13564  
    #2          2       3645   
    #3          3        704   
    #4          4        311   
    #5        5-10       967   
    #6        11-50      440   
    #7       51-100      26    
    #8       101-500      4    
    #9        >500        2    
  Average     2.17             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.176157s wall, 3.703125s user + 0.015625s system = 3.718750s CPU (170.9%)

RUN-1004 : used memory is 1070 MB, reserved memory is 1070 MB, peak memory is 1140 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69691, tnet num: 19722, tinst num: 8373, tnode num: 94875, tedge num: 114784.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.817709s wall, 1.812500s user + 0.000000s system = 1.812500s CPU (99.7%)

RUN-1004 : used memory is 1072 MB, reserved memory is 1072 MB, peak memory is 1140 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19722 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.526329s wall, 1.531250s user + 0.000000s system = 1.531250s CPU (100.3%)

RUN-1004 : used memory is 1076 MB, reserved memory is 1075 MB, peak memory is 1140 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 28b752c1c0e7d7ba92ac5beed3c46754b7b56ac64969e4b0149cfdbc1d295011 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8373
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19724, pip num: 153389
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 359
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3223 valid insts, and 426987 bits set as '1'.
BIT-1004 : the usercode register value: 00000000000000001110111010000001
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  13.178041s wall, 132.515625s user + 0.187500s system = 132.703125s CPU (1007.0%)

RUN-1004 : used memory is 1203 MB, reserved memory is 1190 MB, peak memory is 1318 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250617_172837.log"

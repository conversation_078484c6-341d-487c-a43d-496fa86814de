============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Aug 21 10:09:29 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.609275s wall, 1.343750s user + 4.250000s system = 5.593750s CPU (99.7%)

RUN-1004 : used memory is 79 MB, reserved memory is 42 MB, peak memory is 90 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.782818s wall, 1.640625s user + 0.125000s system = 1.765625s CPU (99.0%)

RUN-1004 : used memory is 302 MB, reserved memory is 270 MB, peak memory is 305 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 27 trigger nets, 27 data nets.
KIT-1004 : Chipwatcher code = 1011100110110011
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=27,BUS_CTRL_NUM=74,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=96) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=96) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=27,BUS_CTRL_NUM=74,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=27,BUS_CTRL_NUM=74,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=27,BUS_CTRL_NUM=74,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=96)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=96)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=27,BUS_CTRL_NUM=74,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=27,BUS_CTRL_NUM=74,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22544/21 useful/useless nets, 19426/13 useful/useless insts
SYN-1016 : Merged 30 instances.
SYN-1032 : 22271/22 useful/useless nets, 19776/18 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 2 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 2 mux instances.
SYN-1015 : Optimize round 1, 321 better
SYN-1014 : Optimize round 2
SYN-1032 : 22029/30 useful/useless nets, 19534/32 useful/useless insts
SYN-1015 : Optimize round 2, 64 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.441390s wall, 2.390625s user + 0.046875s system = 2.437500s CPU (99.8%)

RUN-1004 : used memory is 327 MB, reserved memory is 294 MB, peak memory is 329 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22065/221 useful/useless nets, 19593/32 useful/useless insts
SYN-1016 : Merged 33 instances.
SYN-2571 : Optimize after map_dsp, round 1, 286 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 24 instances.
SYN-2501 : Optimize round 1, 50 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 14 macro adder
SYN-1019 : Optimized 9 mux instances.
SYN-1016 : Merged 10 instances.
SYN-1032 : 22442/5 useful/useless nets, 19970/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81455, tnet num: 22442, tinst num: 19969, tnode num: 114180, tedge num: 127327.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.262488s wall, 1.250000s user + 0.015625s system = 1.265625s CPU (100.2%)

RUN-1004 : used memory is 466 MB, reserved memory is 434 MB, peak memory is 466 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22442 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 188 (3.61), #lev = 7 (1.95)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 188 (3.61), #lev = 7 (1.95)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 462 instances into 188 LUTs, name keeping = 75%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 332 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 108 adder to BLE ...
SYN-4008 : Packed 108 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.636014s wall, 4.593750s user + 0.062500s system = 4.656250s CPU (100.4%)

RUN-1004 : used memory is 350 MB, reserved memory is 315 MB, peak memory is 572 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.421382s wall, 7.328125s user + 0.109375s system = 7.437500s CPU (100.2%)

RUN-1004 : used memory is 351 MB, reserved memory is 315 MB, peak memory is 572 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (210 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4024 : Net "FMC/FMC_data_n" drives clk pins.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net FMC/FMC_data_n as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 4 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net FMC/FMC_data_n to drive 32 clock pins.
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19259 instances
RUN-0007 : 5572 luts, 12108 seqs, 973 mslices, 515 lslices, 59 pads, 26 brams, 0 dsps
RUN-1001 : There are total 21739 nets
RUN-1001 : 16368 nets have 2 pins
RUN-1001 : 4189 nets have [3 - 5] pins
RUN-1001 : 817 nets have [6 - 10] pins
RUN-1001 : 238 nets have [11 - 20] pins
RUN-1001 : 108 nets have [21 - 99] pins
RUN-1001 : 19 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4767     
RUN-1001 :   No   |  No   |  Yes  |     690     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     382     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 124
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19257 instances, 5572 luts, 12108 seqs, 1488 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 79935, tnet num: 21737, tinst num: 19257, tnode num: 112376, tedge num: 125665.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.244964s wall, 1.218750s user + 0.031250s system = 1.250000s CPU (100.4%)

RUN-1004 : used memory is 524 MB, reserved memory is 495 MB, peak memory is 572 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21737 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.171140s wall, 2.109375s user + 0.062500s system = 2.171875s CPU (100.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.60353e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19257.
PHY-3001 : Level 1 #clusters 2208.
PHY-3001 : End clustering;  0.162300s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (154.0%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 836160, overlap = 623.031
PHY-3002 : Step(2): len = 756895, overlap = 709.156
PHY-3002 : Step(3): len = 494983, overlap = 857.469
PHY-3002 : Step(4): len = 434319, overlap = 927.719
PHY-3002 : Step(5): len = 337874, overlap = 1054.28
PHY-3002 : Step(6): len = 296176, overlap = 1112.62
PHY-3002 : Step(7): len = 251753, overlap = 1178.38
PHY-3002 : Step(8): len = 227316, overlap = 1218.81
PHY-3002 : Step(9): len = 201622, overlap = 1271.72
PHY-3002 : Step(10): len = 185914, overlap = 1308.06
PHY-3002 : Step(11): len = 166304, overlap = 1364.59
PHY-3002 : Step(12): len = 155462, overlap = 1397.12
PHY-3002 : Step(13): len = 143866, overlap = 1425.16
PHY-3002 : Step(14): len = 134008, overlap = 1436.16
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.46007e-06
PHY-3002 : Step(15): len = 142934, overlap = 1385.91
PHY-3002 : Step(16): len = 191172, overlap = 1240
PHY-3002 : Step(17): len = 205103, overlap = 1111.69
PHY-3002 : Step(18): len = 205912, overlap = 1025.22
PHY-3002 : Step(19): len = 201112, overlap = 999.906
PHY-3002 : Step(20): len = 193072, overlap = 1022.22
PHY-3002 : Step(21): len = 187916, overlap = 1019.56
PHY-3002 : Step(22): len = 182736, overlap = 1034.59
PHY-3002 : Step(23): len = 178688, overlap = 1033.12
PHY-3002 : Step(24): len = 175386, overlap = 1039.78
PHY-3002 : Step(25): len = 173008, overlap = 1030.53
PHY-3002 : Step(26): len = 171615, overlap = 1033.84
PHY-3002 : Step(27): len = 170030, overlap = 1043.88
PHY-3002 : Step(28): len = 168992, overlap = 1021.34
PHY-3002 : Step(29): len = 167552, overlap = 1044.56
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.92013e-06
PHY-3002 : Step(30): len = 178237, overlap = 1013.38
PHY-3002 : Step(31): len = 191908, overlap = 928.719
PHY-3002 : Step(32): len = 195420, overlap = 880.312
PHY-3002 : Step(33): len = 197284, overlap = 863.5
PHY-3002 : Step(34): len = 198284, overlap = 846.656
PHY-3002 : Step(35): len = 198303, overlap = 842.156
PHY-3002 : Step(36): len = 197757, overlap = 843.469
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 5.84027e-06
PHY-3002 : Step(37): len = 209613, overlap = 817.5
PHY-3002 : Step(38): len = 225447, overlap = 733.844
PHY-3002 : Step(39): len = 230280, overlap = 691.594
PHY-3002 : Step(40): len = 232988, overlap = 658.281
PHY-3002 : Step(41): len = 231437, overlap = 640.625
PHY-3002 : Step(42): len = 229646, overlap = 631.688
PHY-3002 : Step(43): len = 228071, overlap = 613.969
PHY-3002 : Step(44): len = 227762, overlap = 601.844
PHY-3002 : Step(45): len = 226960, overlap = 577.531
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 1.16805e-05
PHY-3002 : Step(46): len = 237117, overlap = 556.188
PHY-3002 : Step(47): len = 253714, overlap = 494.5
PHY-3002 : Step(48): len = 259574, overlap = 465.656
PHY-3002 : Step(49): len = 260947, overlap = 465.688
PHY-3002 : Step(50): len = 259195, overlap = 475.875
PHY-3002 : Step(51): len = 257138, overlap = 477.344
PHY-3002 : Step(52): len = 255197, overlap = 484.062
PHY-3002 : Step(53): len = 253772, overlap = 495.719
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 2.33611e-05
PHY-3002 : Step(54): len = 265277, overlap = 475.219
PHY-3002 : Step(55): len = 277738, overlap = 466
PHY-3002 : Step(56): len = 280723, overlap = 435.812
PHY-3002 : Step(57): len = 282026, overlap = 429.719
PHY-3002 : Step(58): len = 281013, overlap = 425.812
PHY-3002 : Step(59): len = 280357, overlap = 428.781
PHY-3002 : Step(60): len = 278160, overlap = 422.562
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 4.67221e-05
PHY-3002 : Step(61): len = 287611, overlap = 420.531
PHY-3002 : Step(62): len = 298599, overlap = 369.969
PHY-3002 : Step(63): len = 301978, overlap = 366.906
PHY-3002 : Step(64): len = 302029, overlap = 359.688
PHY-3002 : Step(65): len = 300731, overlap = 364.531
PHY-3002 : Step(66): len = 299465, overlap = 361.594
PHY-3002 : Step(67): len = 299012, overlap = 338.375
PHY-3002 : Step(68): len = 299234, overlap = 311.094
PHY-3002 : Step(69): len = 297443, overlap = 300.25
PHY-3002 : Step(70): len = 296712, overlap = 297.906
PHY-3002 : Step(71): len = 295780, overlap = 276.438
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 9.34443e-05
PHY-3002 : Step(72): len = 303781, overlap = 284.875
PHY-3002 : Step(73): len = 310012, overlap = 256.906
PHY-3002 : Step(74): len = 311222, overlap = 251.688
PHY-3002 : Step(75): len = 311727, overlap = 243.531
PHY-3002 : Step(76): len = 310867, overlap = 244.562
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000186889
PHY-3002 : Step(77): len = 315844, overlap = 255
PHY-3002 : Step(78): len = 323002, overlap = 251.719
PHY-3002 : Step(79): len = 325053, overlap = 228.031
PHY-3002 : Step(80): len = 326948, overlap = 240.312
PHY-3002 : Step(81): len = 324531, overlap = 229.406
PHY-3002 : Step(82): len = 322189, overlap = 235.094
PHY-3002 : Step(83): len = 321342, overlap = 235.812
PHY-3002 : Step(84): len = 321760, overlap = 220.281
PHY-3002 : Step(85): len = 321234, overlap = 214.344
PHY-3002 : Step(86): len = 319721, overlap = 215.844
PHY-3002 : Step(87): len = 320867, overlap = 193.969
PHY-3002 : Step(88): len = 320110, overlap = 202.344
PHY-3002 : Step(89): len = 320051, overlap = 191.312
PHY-3002 : Step(90): len = 319376, overlap = 179.562
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000356316
PHY-3002 : Step(91): len = 322033, overlap = 188.188
PHY-3002 : Step(92): len = 324793, overlap = 179.5
PHY-3002 : Step(93): len = 324792, overlap = 176.531
PHY-3002 : Step(94): len = 325462, overlap = 178.906
PHY-3002 : Step(95): len = 325445, overlap = 179.469
PHY-3002 : Step(96): len = 325643, overlap = 169.875
PHY-3002 : Step(97): len = 324831, overlap = 171.969
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(98): len = 325717, overlap = 174.812
PHY-3002 : Step(99): len = 328140, overlap = 175.438
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.033328s wall, 0.015625s user + 0.031250s system = 0.046875s CPU (140.6%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21739.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 430568, over cnt = 1176(3%), over = 5201, worst = 39
PHY-1001 : End global iterations;  0.865085s wall, 1.078125s user + 0.093750s system = 1.171875s CPU (135.5%)

PHY-1001 : Congestion index: top1 = 69.96, top5 = 50.36, top10 = 41.21, top15 = 36.08.
PHY-3001 : End congestion estimation;  1.147143s wall, 1.359375s user + 0.109375s system = 1.468750s CPU (128.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21737 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.001283s wall, 0.984375s user + 0.015625s system = 1.000000s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.60966e-05
PHY-3002 : Step(100): len = 367622, overlap = 166.344
PHY-3002 : Step(101): len = 381205, overlap = 142.219
PHY-3002 : Step(102): len = 380127, overlap = 131.906
PHY-3002 : Step(103): len = 378777, overlap = 125.688
PHY-3002 : Step(104): len = 387814, overlap = 119.25
PHY-3002 : Step(105): len = 392669, overlap = 115.062
PHY-3002 : Step(106): len = 397991, overlap = 111.688
PHY-3002 : Step(107): len = 403233, overlap = 115.938
PHY-3002 : Step(108): len = 405928, overlap = 114.594
PHY-3002 : Step(109): len = 410540, overlap = 112.156
PHY-3002 : Step(110): len = 412826, overlap = 110.219
PHY-3002 : Step(111): len = 413065, overlap = 113.438
PHY-3002 : Step(112): len = 415526, overlap = 119.531
PHY-3002 : Step(113): len = 418545, overlap = 125.562
PHY-3002 : Step(114): len = 418997, overlap = 117.562
PHY-3002 : Step(115): len = 421230, overlap = 117.344
PHY-3002 : Step(116): len = 421211, overlap = 120.625
PHY-3002 : Step(117): len = 421477, overlap = 120
PHY-3002 : Step(118): len = 422697, overlap = 122.406
PHY-3002 : Step(119): len = 422672, overlap = 123.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000172193
PHY-3002 : Step(120): len = 423174, overlap = 121.688
PHY-3002 : Step(121): len = 424584, overlap = 118.594
PHY-3002 : Step(122): len = 427311, overlap = 116.812
PHY-3002 : Step(123): len = 431029, overlap = 116.531
PHY-3002 : Step(124): len = 434494, overlap = 116.594
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000344386
PHY-3002 : Step(125): len = 434968, overlap = 113.688
PHY-3002 : Step(126): len = 438317, overlap = 111.125
PHY-3002 : Step(127): len = 442903, overlap = 108.094
PHY-3002 : Step(128): len = 447847, overlap = 109.969
PHY-3002 : Step(129): len = 450350, overlap = 108.969
PHY-3002 : Step(130): len = 452609, overlap = 104.75
PHY-3002 : Step(131): len = 451780, overlap = 105.656
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000688773
PHY-3002 : Step(132): len = 451957, overlap = 103.281
PHY-3002 : Step(133): len = 455891, overlap = 105.531
PHY-3002 : Step(134): len = 462322, overlap = 107.531
PHY-3002 : Step(135): len = 466145, overlap = 111.281
PHY-3002 : Step(136): len = 467042, overlap = 110.594
PHY-3002 : Step(137): len = 467454, overlap = 111.25
PHY-3002 : Step(138): len = 467718, overlap = 116.188
PHY-3002 : Step(139): len = 469327, overlap = 121.188
PHY-3002 : Step(140): len = 469740, overlap = 115.656
PHY-3002 : Step(141): len = 468151, overlap = 119.344
PHY-3002 : Step(142): len = 467816, overlap = 114.094
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 52/21739.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 531560, over cnt = 2197(6%), over = 9892, worst = 37
PHY-1001 : End global iterations;  1.134015s wall, 1.906250s user + 0.015625s system = 1.921875s CPU (169.5%)

PHY-1001 : Congestion index: top1 = 72.20, top5 = 56.79, top10 = 49.31, top15 = 44.66.
PHY-3001 : End congestion estimation;  1.420990s wall, 2.203125s user + 0.015625s system = 2.218750s CPU (156.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21737 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.200775s wall, 1.187500s user + 0.015625s system = 1.203125s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000103459
PHY-3002 : Step(143): len = 473089, overlap = 334.281
PHY-3002 : Step(144): len = 475030, overlap = 277.219
PHY-3002 : Step(145): len = 468620, overlap = 252.625
PHY-3002 : Step(146): len = 461889, overlap = 236.75
PHY-3002 : Step(147): len = 457337, overlap = 220.219
PHY-3002 : Step(148): len = 454114, overlap = 219.094
PHY-3002 : Step(149): len = 449995, overlap = 214.312
PHY-3002 : Step(150): len = 447718, overlap = 215.125
PHY-3002 : Step(151): len = 443378, overlap = 213.594
PHY-3002 : Step(152): len = 440388, overlap = 212.188
PHY-3002 : Step(153): len = 439210, overlap = 202.688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000206919
PHY-3002 : Step(154): len = 439726, overlap = 195.625
PHY-3002 : Step(155): len = 442213, overlap = 190.688
PHY-3002 : Step(156): len = 444948, overlap = 179.938
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000413837
PHY-3002 : Step(157): len = 447282, overlap = 172.75
PHY-3002 : Step(158): len = 452255, overlap = 157.688
PHY-3002 : Step(159): len = 457472, overlap = 144.906
PHY-3002 : Step(160): len = 457632, overlap = 139.719
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 79935, tnet num: 21737, tinst num: 19257, tnode num: 112376, tedge num: 125665.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.595103s wall, 1.546875s user + 0.046875s system = 1.593750s CPU (99.9%)

RUN-1004 : used memory is 564 MB, reserved memory is 538 MB, peak memory is 696 MB
OPT-1001 : Total overflow 485.59 peak overflow 4.47
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 616/21739.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 537000, over cnt = 2442(6%), over = 8473, worst = 20
PHY-1001 : End global iterations;  1.274625s wall, 1.906250s user + 0.062500s system = 1.968750s CPU (154.5%)

PHY-1001 : Congestion index: top1 = 56.36, top5 = 46.79, top10 = 41.81, top15 = 38.82.
PHY-1001 : End incremental global routing;  1.541884s wall, 2.171875s user + 0.062500s system = 2.234375s CPU (144.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21737 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.052583s wall, 1.031250s user + 0.031250s system = 1.062500s CPU (100.9%)

OPT-1001 : 17 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19177 has valid locations, 230 needs to be replaced
PHY-3001 : design contains 19470 instances, 5666 luts, 12227 seqs, 1488 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 473535
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17182/21952.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 548888, over cnt = 2479(7%), over = 8537, worst = 20
PHY-1001 : End global iterations;  0.201864s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (131.6%)

PHY-1001 : Congestion index: top1 = 56.72, top5 = 47.03, top10 = 42.17, top15 = 39.22.
PHY-3001 : End congestion estimation;  0.463851s wall, 0.531250s user + 0.000000s system = 0.531250s CPU (114.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80628, tnet num: 21950, tinst num: 19470, tnode num: 113347, tedge num: 126625.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.531341s wall, 1.500000s user + 0.031250s system = 1.531250s CPU (100.0%)

RUN-1004 : used memory is 609 MB, reserved memory is 601 MB, peak memory is 698 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21950 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.624173s wall, 2.578125s user + 0.031250s system = 2.609375s CPU (99.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(161): len = 473508, overlap = 1.375
PHY-3002 : Step(162): len = 474270, overlap = 1.375
PHY-3002 : Step(163): len = 475052, overlap = 1.375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17238/21952.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 548240, over cnt = 2478(7%), over = 8581, worst = 19
PHY-1001 : End global iterations;  0.184070s wall, 0.312500s user + 0.031250s system = 0.343750s CPU (186.7%)

PHY-1001 : Congestion index: top1 = 56.83, top5 = 47.25, top10 = 42.22, top15 = 39.26.
PHY-3001 : End congestion estimation;  0.449788s wall, 0.578125s user + 0.031250s system = 0.609375s CPU (135.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21950 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.051980s wall, 1.046875s user + 0.000000s system = 1.046875s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000802397
PHY-3002 : Step(164): len = 475248, overlap = 141.812
PHY-3002 : Step(165): len = 476095, overlap = 141.312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00160479
PHY-3002 : Step(166): len = 476135, overlap = 141.25
PHY-3002 : Step(167): len = 476477, overlap = 141.594
PHY-3001 : Final: Len = 476477, Over = 141.594
PHY-3001 : End incremental placement;  5.438691s wall, 5.765625s user + 0.203125s system = 5.968750s CPU (109.7%)

OPT-1001 : Total overflow 491.03 peak overflow 4.47
OPT-1001 : End high-fanout net optimization;  8.598187s wall, 9.671875s user + 0.312500s system = 9.984375s CPU (116.1%)

OPT-1001 : Current memory(MB): used = 702, reserve = 682, peak = 718.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17212/21952.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 550504, over cnt = 2448(6%), over = 8106, worst = 19
PHY-1002 : len = 586448, over cnt = 1725(4%), over = 4557, worst = 18
PHY-1002 : len = 628920, over cnt = 599(1%), over = 1302, worst = 18
PHY-1002 : len = 640064, over cnt = 263(0%), over = 566, worst = 11
PHY-1002 : len = 647760, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.309970s wall, 1.890625s user + 0.031250s system = 1.921875s CPU (146.7%)

PHY-1001 : Congestion index: top1 = 48.32, top5 = 42.68, top10 = 39.41, top15 = 37.33.
OPT-1001 : End congestion update;  1.579968s wall, 2.156250s user + 0.031250s system = 2.187500s CPU (138.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 21950 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.927053s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (99.4%)

OPT-0007 : Start: WNS 3669 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.513157s wall, 3.093750s user + 0.031250s system = 3.125000s CPU (124.3%)

OPT-1001 : Current memory(MB): used = 679, reserve = 662, peak = 718.
OPT-1001 : End physical optimization;  13.038797s wall, 14.765625s user + 0.437500s system = 15.203125s CPU (116.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5666 LUT to BLE ...
SYN-4008 : Packed 5666 LUT and 2727 SEQ to BLE.
SYN-4003 : Packing 9500 remaining SEQ's ...
SYN-4005 : Packed 3328 SEQ with LUT/SLICE
SYN-4006 : 137 single LUT's are left
SYN-4006 : 6172 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11838/13681 primitive instances ...
PHY-3001 : End packing;  2.889002s wall, 2.875000s user + 0.031250s system = 2.906250s CPU (100.6%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8048 instances
RUN-1001 : 3979 mslices, 3978 lslices, 59 pads, 26 brams, 0 dsps
RUN-1001 : There are total 19275 nets
RUN-1001 : 13619 nets have 2 pins
RUN-1001 : 4257 nets have [3 - 5] pins
RUN-1001 : 878 nets have [6 - 10] pins
RUN-1001 : 387 nets have [11 - 20] pins
RUN-1001 : 125 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8046 instances, 7957 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 497587, Over = 361
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7779/19275.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 624024, over cnt = 1574(4%), over = 2487, worst = 8
PHY-1002 : len = 630936, over cnt = 913(2%), over = 1259, worst = 6
PHY-1002 : len = 640816, over cnt = 370(1%), over = 492, worst = 5
PHY-1002 : len = 646120, over cnt = 164(0%), over = 198, worst = 5
PHY-1002 : len = 649264, over cnt = 29(0%), over = 33, worst = 3
PHY-1001 : End global iterations;  1.254718s wall, 2.109375s user + 0.078125s system = 2.187500s CPU (174.3%)

PHY-1001 : Congestion index: top1 = 50.45, top5 = 43.07, top10 = 39.60, top15 = 37.45.
PHY-3001 : End congestion estimation;  1.596034s wall, 2.437500s user + 0.078125s system = 2.515625s CPU (157.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66789, tnet num: 19273, tinst num: 8046, tnode num: 90577, tedge num: 110019.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.749363s wall, 1.703125s user + 0.046875s system = 1.750000s CPU (100.0%)

RUN-1004 : used memory is 599 MB, reserved memory is 588 MB, peak memory is 718 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19273 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.721788s wall, 2.656250s user + 0.062500s system = 2.718750s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.44152e-05
PHY-3002 : Step(168): len = 492021, overlap = 343
PHY-3002 : Step(169): len = 487927, overlap = 351.25
PHY-3002 : Step(170): len = 484734, overlap = 352
PHY-3002 : Step(171): len = 482957, overlap = 371
PHY-3002 : Step(172): len = 482302, overlap = 379.75
PHY-3002 : Step(173): len = 482652, overlap = 383.75
PHY-3002 : Step(174): len = 481519, overlap = 386.75
PHY-3002 : Step(175): len = 480400, overlap = 382.75
PHY-3002 : Step(176): len = 478712, overlap = 391
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.88304e-05
PHY-3002 : Step(177): len = 482399, overlap = 378.5
PHY-3002 : Step(178): len = 487120, overlap = 364
PHY-3002 : Step(179): len = 487555, overlap = 359
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000177661
PHY-3002 : Step(180): len = 495774, overlap = 349.5
PHY-3002 : Step(181): len = 502075, overlap = 343.75
PHY-3002 : Step(182): len = 500211, overlap = 331.75
PHY-3002 : Step(183): len = 498868, overlap = 328.5
PHY-3002 : Step(184): len = 498490, overlap = 330.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.787418s wall, 0.984375s user + 0.796875s system = 1.781250s CPU (226.2%)

PHY-3001 : Trial Legalized: Len = 605362
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 577/19275.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 692752, over cnt = 2292(6%), over = 3757, worst = 7
PHY-1002 : len = 706432, over cnt = 1242(3%), over = 1768, worst = 7
PHY-1002 : len = 720632, over cnt = 506(1%), over = 671, worst = 6
PHY-1002 : len = 727600, over cnt = 209(0%), over = 263, worst = 3
PHY-1002 : len = 732104, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.933472s wall, 3.484375s user + 0.093750s system = 3.578125s CPU (185.1%)

PHY-1001 : Congestion index: top1 = 47.31, top5 = 43.17, top10 = 40.93, top15 = 39.33.
PHY-3001 : End congestion estimation;  2.322887s wall, 3.875000s user + 0.093750s system = 3.968750s CPU (170.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19273 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.953250s wall, 0.890625s user + 0.062500s system = 0.953125s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000176536
PHY-3002 : Step(185): len = 561900, overlap = 80.5
PHY-3002 : Step(186): len = 543234, overlap = 129.75
PHY-3002 : Step(187): len = 530864, overlap = 182
PHY-3002 : Step(188): len = 524975, overlap = 215.5
PHY-3002 : Step(189): len = 521138, overlap = 226
PHY-3002 : Step(190): len = 519887, overlap = 239.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000353072
PHY-3002 : Step(191): len = 524071, overlap = 236
PHY-3002 : Step(192): len = 527543, overlap = 232
PHY-3002 : Step(193): len = 527359, overlap = 232.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(194): len = 531034, overlap = 225.75
PHY-3002 : Step(195): len = 537770, overlap = 223
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.034776s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (89.9%)

PHY-3001 : Legalized: Len = 578701, Over = 0
PHY-3001 : Spreading special nets. 38 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.086860s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (107.9%)

PHY-3001 : 67 instances has been re-located, deltaX = 20, deltaY = 39, maxDist = 2.
PHY-3001 : Final: Len = 579855, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66789, tnet num: 19273, tinst num: 8046, tnode num: 90577, tedge num: 110019.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.992130s wall, 1.968750s user + 0.031250s system = 2.000000s CPU (100.4%)

RUN-1004 : used memory is 594 MB, reserved memory is 571 MB, peak memory is 718 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 4395/19275.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 675856, over cnt = 2163(6%), over = 3349, worst = 6
PHY-1002 : len = 687000, over cnt = 1210(3%), over = 1643, worst = 6
PHY-1002 : len = 699296, over cnt = 570(1%), over = 730, worst = 5
PHY-1002 : len = 707920, over cnt = 224(0%), over = 265, worst = 4
PHY-1002 : len = 711624, over cnt = 44(0%), over = 52, worst = 3
PHY-1001 : End global iterations;  1.736915s wall, 2.750000s user + 0.078125s system = 2.828125s CPU (162.8%)

PHY-1001 : Congestion index: top1 = 46.29, top5 = 42.05, top10 = 39.72, top15 = 38.07.
PHY-1001 : End incremental global routing;  2.074711s wall, 3.078125s user + 0.093750s system = 3.171875s CPU (152.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19273 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.964755s wall, 0.953125s user + 0.015625s system = 0.968750s CPU (100.4%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 7981 has valid locations, 19 needs to be replaced
PHY-3001 : design contains 8063 instances, 7974 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 584195
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17396/19295.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 717824, over cnt = 85(0%), over = 116, worst = 5
PHY-1002 : len = 717976, over cnt = 64(0%), over = 72, worst = 3
PHY-1002 : len = 718496, over cnt = 28(0%), over = 31, worst = 2
PHY-1002 : len = 718664, over cnt = 20(0%), over = 22, worst = 2
PHY-1002 : len = 718936, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.721057s wall, 0.734375s user + 0.015625s system = 0.750000s CPU (104.0%)

PHY-1001 : Congestion index: top1 = 46.70, top5 = 42.35, top10 = 39.96, top15 = 38.29.
PHY-3001 : End congestion estimation;  1.044676s wall, 1.062500s user + 0.015625s system = 1.078125s CPU (103.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66936, tnet num: 19293, tinst num: 8063, tnode num: 90750, tedge num: 110222.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.957849s wall, 1.953125s user + 0.000000s system = 1.953125s CPU (99.8%)

RUN-1004 : used memory is 642 MB, reserved memory is 630 MB, peak memory is 718 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19293 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.002757s wall, 2.968750s user + 0.031250s system = 3.000000s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(196): len = 583071, overlap = 1
PHY-3002 : Step(197): len = 582915, overlap = 1.5
PHY-3002 : Step(198): len = 582829, overlap = 1.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17394/19295.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 716584, over cnt = 38(0%), over = 57, worst = 5
PHY-1002 : len = 716488, over cnt = 30(0%), over = 33, worst = 4
PHY-1002 : len = 716832, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 716840, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 716888, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.744531s wall, 0.750000s user + 0.015625s system = 0.765625s CPU (102.8%)

PHY-1001 : Congestion index: top1 = 46.64, top5 = 42.22, top10 = 39.85, top15 = 38.22.
PHY-3001 : End congestion estimation;  1.065171s wall, 1.062500s user + 0.015625s system = 1.078125s CPU (101.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19293 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.954102s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000563504
PHY-3002 : Step(199): len = 582899, overlap = 3
PHY-3002 : Step(200): len = 582922, overlap = 2.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00112701
PHY-3002 : Step(201): len = 582990, overlap = 2.25
PHY-3002 : Step(202): len = 582940, overlap = 2.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00225402
PHY-3002 : Step(203): len = 583096, overlap = 1
PHY-3002 : Step(204): len = 583110, overlap = 1
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007009s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (222.9%)

PHY-3001 : Legalized: Len = 583164, Over = 0
PHY-3001 : End spreading;  0.072277s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (108.1%)

PHY-3001 : Final: Len = 583164, Over = 0
PHY-3001 : End incremental placement;  6.896542s wall, 7.265625s user + 0.234375s system = 7.500000s CPU (108.8%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.449767s wall, 11.984375s user + 0.343750s system = 12.328125s CPU (118.0%)

OPT-1001 : Current memory(MB): used = 713, reserve = 698, peak = 718.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17395/19295.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 717104, over cnt = 38(0%), over = 45, worst = 3
PHY-1002 : len = 717104, over cnt = 25(0%), over = 26, worst = 2
PHY-1002 : len = 717272, over cnt = 8(0%), over = 9, worst = 2
PHY-1002 : len = 717384, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 717408, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.731046s wall, 0.765625s user + 0.062500s system = 0.828125s CPU (113.3%)

PHY-1001 : Congestion index: top1 = 46.55, top5 = 42.26, top10 = 39.90, top15 = 38.26.
OPT-1001 : End congestion update;  1.046518s wall, 1.093750s user + 0.062500s system = 1.156250s CPU (110.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19293 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.804880s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.0%)

OPT-0007 : Start: WNS 3590 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.856859s wall, 1.906250s user + 0.062500s system = 1.968750s CPU (106.0%)

OPT-1001 : Current memory(MB): used = 713, reserve = 698, peak = 718.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19293 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.794671s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (100.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17414/19295.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 717408, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.126296s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (99.0%)

PHY-1001 : Congestion index: top1 = 46.55, top5 = 42.26, top10 = 39.90, top15 = 38.26.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19293 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.794410s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (100.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3590 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 46.034483
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3590ps with logic level 4 
OPT-1001 : End physical optimization;  16.602796s wall, 18.109375s user + 0.468750s system = 18.578125s CPU (111.9%)

RUN-1003 : finish command "place" in  71.073865s wall, 138.546875s user + 7.968750s system = 146.515625s CPU (206.1%)

RUN-1004 : used memory is 593 MB, reserved memory is 580 MB, peak memory is 718 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.664151s wall, 2.843750s user + 0.015625s system = 2.859375s CPU (171.8%)

RUN-1004 : used memory is 594 MB, reserved memory is 581 MB, peak memory is 718 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8065 instances
RUN-1001 : 3979 mslices, 3995 lslices, 59 pads, 26 brams, 0 dsps
RUN-1001 : There are total 19295 nets
RUN-1001 : 13616 nets have 2 pins
RUN-1001 : 4262 nets have [3 - 5] pins
RUN-1001 : 888 nets have [6 - 10] pins
RUN-1001 : 396 nets have [11 - 20] pins
RUN-1001 : 124 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66936, tnet num: 19293, tinst num: 8063, tnode num: 90750, tedge num: 110222.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.745618s wall, 1.750000s user + 0.000000s system = 1.750000s CPU (100.3%)

RUN-1004 : used memory is 585 MB, reserved memory is 576 MB, peak memory is 718 MB
PHY-1001 : 3979 mslices, 3995 lslices, 59 pads, 26 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19293 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 658752, over cnt = 2322(6%), over = 3825, worst = 7
PHY-1002 : len = 675592, over cnt = 1264(3%), over = 1792, worst = 7
PHY-1002 : len = 689776, over cnt = 521(1%), over = 714, worst = 5
PHY-1002 : len = 700680, over cnt = 30(0%), over = 49, worst = 5
PHY-1002 : len = 701592, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.833995s wall, 3.015625s user + 0.000000s system = 3.015625s CPU (164.4%)

PHY-1001 : Congestion index: top1 = 46.01, top5 = 41.65, top10 = 39.29, top15 = 37.70.
PHY-1001 : End global routing;  2.190913s wall, 3.375000s user + 0.000000s system = 3.375000s CPU (154.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 695, reserve = 685, peak = 718.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Processed IO instance FMC_data[15]_syn_2 with INV attribute.
PHY-1001 : Processed IO instance FMC_data[14]_syn_2 with INV attribute.
PHY-1001 : Processed IO instance FMC_data[13]_syn_2 with INV attribute.
PHY-1001 : Processed IO instance FMC_data[12]_syn_2 with INV attribute.
PHY-1001 : Processed IO instance FMC_data[11]_syn_2 with INV attribute.
PHY-1001 : Processed IO instance FMC_data[10]_syn_2 with INV attribute.
PHY-1001 : Processed IO instance FMC_data[9]_syn_2 with INV attribute.
PHY-1001 : Processed IO instance FMC_data[8]_syn_2 with INV attribute.
PHY-1001 : Processed IO instance FMC_data[7]_syn_2 with INV attribute.
PHY-1001 : Processed IO instance FMC_data[6]_syn_2 with INV attribute.
PHY-1001 : Processed IO instance FMC_data[5]_syn_2 with INV attribute.
PHY-1001 : Processed IO instance FMC_data[4]_syn_2 with INV attribute.
PHY-1001 : Processed IO instance FMC_data[3]_syn_2 with INV attribute.
PHY-1001 : Processed IO instance FMC_data[2]_syn_2 with INV attribute.
PHY-1001 : Processed IO instance FMC_data[1]_syn_2 with INV attribute.
PHY-1001 : Processed IO instance FMC_data[0]_syn_2 with INV attribute.
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : clock net FMC/FMC_data_n_syn_3 will be merged with clock FMC/FMC_data_n
PHY-1001 : Current memory(MB): used = 961, reserve = 950, peak = 961.
PHY-1001 : End build detailed router design. 4.621870s wall, 4.500000s user + 0.109375s system = 4.609375s CPU (99.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 192856, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.446179s wall, 1.421875s user + 0.015625s system = 1.437500s CPU (99.4%)

PHY-1001 : Current memory(MB): used = 997, reserve = 987, peak = 997.
PHY-1001 : End phase 1; 1.453879s wall, 1.437500s user + 0.015625s system = 1.453125s CPU (99.9%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.67347e+06, over cnt = 1324(0%), over = 1328, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1014, reserve = 1003, peak = 1014.
PHY-1001 : End initial routed; 16.758657s wall, 44.343750s user + 0.265625s system = 44.609375s CPU (266.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18035(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.711   |   0.000   |   0   
RUN-1001 :   Hold   |   0.195   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.503544s wall, 3.515625s user + 0.000000s system = 3.515625s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 1017, reserve = 1003, peak = 1017.
PHY-1001 : End phase 2; 20.262380s wall, 47.859375s user + 0.265625s system = 48.125000s CPU (237.5%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.67347e+06, over cnt = 1324(0%), over = 1328, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.245154s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (102.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.66074e+06, over cnt = 421(0%), over = 421, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.768354s wall, 1.187500s user + 0.000000s system = 1.187500s CPU (154.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.66214e+06, over cnt = 79(0%), over = 79, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.365707s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (141.0%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.6629e+06, over cnt = 15(0%), over = 15, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.206173s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (113.7%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.66318e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 4; 0.193232s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (113.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18035(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.711   |   0.000   |   0   
RUN-1001 :   Hold   |   0.195   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.620368s wall, 3.609375s user + 0.015625s system = 3.625000s CPU (100.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 282 feed throughs used by 244 nets
PHY-1001 : End commit to database; 2.262031s wall, 2.250000s user + 0.015625s system = 2.265625s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 1106, reserve = 1099, peak = 1106.
PHY-1001 : End phase 3; 8.187288s wall, 8.750000s user + 0.031250s system = 8.781250s CPU (107.3%)

PHY-1003 : Routed, final wirelength = 1.66318e+06
PHY-1001 : Current memory(MB): used = 1110, reserve = 1103, peak = 1110.
PHY-1001 : End export database. 0.176658s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (97.3%)

PHY-1001 : End detail routing;  35.122578s wall, 63.125000s user + 0.437500s system = 63.562500s CPU (181.0%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66936, tnet num: 19293, tinst num: 8063, tnode num: 90750, tedge num: 110222.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.743088s wall, 1.750000s user + 0.000000s system = 1.750000s CPU (100.4%)

RUN-1004 : used memory is 1049 MB, reserved memory is 1053 MB, peak memory is 1110 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  43.545037s wall, 72.703125s user + 0.468750s system = 73.171875s CPU (168.0%)

RUN-1004 : used memory is 1048 MB, reserved memory is 1054 MB, peak memory is 1110 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8800   out of  19600   44.90%
#reg                    12330   out of  19600   62.91%
#le                     14914
  #lut only              2584   out of  14914   17.33%
  #reg only              6114   out of  14914   41.00%
  #lut&reg               6216   out of  14914   41.68%
#dsp                        0   out of     29    0.00%
#bram                      26   out of     64   40.62%
  #bram9k                  24
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       59   out of    188   31.38%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                            Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0            6750
#2        config_inst_syn_9                GCLK               config             config_inst.jtck                  124
#3        FMC/FMC_data_n                   GCLK               mslice             COM2/uart_com2/reg9_syn_116.f0    27
#4        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di                   1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         F2        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         D1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         B3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         C1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         A3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         A2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         B2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         B1        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F15        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDZ        INPUT        K15        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         P2        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         N4        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         M5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         N6        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         N5        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         P5        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M3        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         M4        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         H2        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         M1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         L1        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         N1        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         N3        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         M2        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT         T5        LVCMOS33           8            NONE           OREG       
    Fog_ENY        OUTPUT         T4        LVCMOS33           8            NONE           OREG       
    Fog_ENZ        OUTPUT         T7        LVCMOS33           8            NONE           OREG       
    Fog_INT        OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         T6        LVCMOS33           8            N/A            OREG       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E1        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        G11        LVCMOS25           8            N/A            NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |14914  |7312    |1488    |12374   |26      |0       |
|  AnyFog_dataX                      |AnyFog          |210    |71      |22      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |88     |56      |22      |51      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |209    |83      |22      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |59      |22      |50      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |207    |89      |22      |172     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |85     |55      |22      |50      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2933   |677     |39      |2855    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |60     |33      |5       |52      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |212    |60      |5       |204     |0       |0       |
|    STADOP_com2                     |STADOP          |553    |115     |0       |546     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |64     |46      |14      |41      |0       |0       |
|    head_com2                       |uniheading      |262    |82      |5       |252     |0       |0       |
|    rmc_com2                        |Gprmc           |46     |46      |0       |36      |0       |0       |
|    uart_com2                       |Agrica          |1429   |263     |10      |1417    |0       |0       |
|  COM3                              |COM3_Control    |275    |140     |19      |238     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |58     |32      |5       |50      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |64     |41      |14      |41      |0       |0       |
|    rmc_com3                        |Gprmc           |153    |67      |0       |147     |0       |0       |
|  DATA                              |Data_Processing |8836   |4626    |1122    |7023    |0       |0       |
|    DIV_Dtemp                       |Divider         |844    |404     |84      |711     |0       |0       |
|    DIV_Utemp                       |Divider         |637    |328     |84      |488     |0       |0       |
|    DIV_accX                        |Divider         |564    |313     |84      |439     |0       |0       |
|    DIV_accY                        |Divider         |648    |314     |102     |487     |0       |0       |
|    DIV_accZ                        |Divider         |617    |367     |132     |413     |0       |0       |
|    DIV_rateX                       |Divider         |646    |329     |132     |441     |0       |0       |
|    DIV_rateY                       |Divider         |607    |415     |132     |403     |0       |0       |
|    DIV_rateZ                       |Divider         |595    |338     |132     |389     |0       |0       |
|    genclk                          |genclk          |272    |174     |89      |112     |0       |0       |
|  FMC                               |FMC_Ctrl        |387    |336     |43      |316     |0       |0       |
|  IIC                               |I2C_master      |338    |277     |11      |268     |0       |0       |
|  IMU_CTRL                          |SCHA634         |907    |638     |61      |732     |0       |0       |
|    CtrlData                        |CtrlData        |479    |423     |47      |335     |0       |0       |
|      usms                          |Time_1ms        |30     |25      |5       |20      |0       |0       |
|    SPIM                            |SPI_SCHA634     |428    |215     |14      |397     |0       |0       |
|  POWER                             |POWER_EN        |100    |56      |38      |41      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |494    |302     |89      |323     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |494    |302     |89      |323     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |210    |122     |0       |195     |0       |0       |
|        reg_inst                    |register        |207    |119     |0       |192     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |284    |180     |89      |128     |0       |0       |
|        bus_inst                    |bus_top         |81     |52      |28      |31      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |26     |16      |10      |9       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |2      |1       |0       |2       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |49     |31      |18      |16      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |118    |87      |29      |67      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13556  
    #2          2       3332   
    #3          3        635   
    #4          4        295   
    #5        5-10       952   
    #6        11-50      448   
    #7       51-100       7    
    #8       101-500      3    
    #9        >500        2    
  Average     2.12             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.053560s wall, 3.546875s user + 0.046875s system = 3.593750s CPU (175.0%)

RUN-1004 : used memory is 1049 MB, reserved memory is 1055 MB, peak memory is 1110 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66936, tnet num: 19293, tinst num: 8063, tnode num: 90750, tedge num: 110222.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.709605s wall, 1.703125s user + 0.000000s system = 1.703125s CPU (99.6%)

RUN-1004 : used memory is 1051 MB, reserved memory is 1056 MB, peak memory is 1110 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19293 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 4 (2 unconstrainted).
TMR-5009 WARNING: No clock constraint on 2 clock net(s): 
		FMC/FMC_data_n_syn_3
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.455139s wall, 1.437500s user + 0.015625s system = 1.453125s CPU (99.9%)

RUN-1004 : used memory is 1055 MB, reserved memory is 1060 MB, peak memory is 1110 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 40c9047af6c60b8681e7bd8c3a1ad8946a1d4edd384cda8aef5d685a0a84c04c -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8063
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19295, pip num: 144073
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 282
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3243 valid insts, and 405597 bits set as '1'.
BIT-1004 : the usercode register value: 00000000010111011011100110110011
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  11.838658s wall, 118.171875s user + 0.156250s system = 118.328125s CPU (999.5%)

RUN-1004 : used memory is 1177 MB, reserved memory is 1162 MB, peak memory is 1292 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250821_100929.log"

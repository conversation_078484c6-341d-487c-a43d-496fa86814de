--------------------------------------------------------------
 --     Copyright (c) 2012-2023 Anlogic Inc.
 --  All Right Reserved.
--------------------------------------------------------------
 -- Log	:	This file is generated by Anlogic IP Generator.
 -- File	:	D:/GitProject/GitProject/Anlogic/INS_FPGA_FMC_V1.0/al_ip/global_clock.vhd
 -- Date	:	2023 12 05
 -- TD version	:	5.6.71036
--------------------------------------------------------------

-------------------------------------------------------------------------------
--	Input frequency:             25.000MHz
--	Clock multiplication factor: 40
--	Clock division factor:       1
--	Clock information:
--		Clock name	| Frequency 	| Phase shift
--		C0        	| 100.000000MHZ	| 0  DEG     
-------------------------------------------------------------------------------

LIBRARY ieee;
USE ieee.std_logic_1164.ALL;
USE ieee.numeric_std.ALL;
USE ieee.std_logic_unsigned.ALL;
USE ieee.std_logic_arith.ALL;
LIBRARY eagle_macro;
USE eagle_macro.EAGLE_COMPONENTS.ALL;

ENTITY global_clock IS
  PORT (
    refclk : IN STD_LOGIC;
    reset : IN STD_LOGIC;
    extlock : OUT STD_LOGIC;
    clk0_out : OUT STD_LOGIC 
  );
END global_clock;

ARCHITECTURE rtl OF global_clock IS
  SIGNAL clkc_wire :  STD_LOGIC_VECTOR (4 DOWNTO 0);
BEGIN
  pll_inst : EG_PHY_PLL
  GENERIC MAP (
    DPHASE_SOURCE => "DISABLE",
    DYNCFG => "DISABLE",
    FIN => "25.000",
    FEEDBK_MODE => "NOCOMP",
    FEEDBK_PATH => "VCO_PHASE_0",
    STDBY_ENABLE => "DISABLE",
    PLLRST_ENA => "ENABLE",
    SYNC_ENABLE => "DISABLE",
    GMC_GAIN => 2,
    ICP_CURRENT => 9,
    KVCO => 2,
    LPF_CAPACITOR => 1,
    LPF_RESISTOR => 8,
    REFCLK_DIV => 1,
    FBCLK_DIV => 40,
    CLKC0_ENABLE => "ENABLE",
    CLKC0_DIV => 10,
    CLKC0_CPHASE => 9,
    CLKC0_FPHASE => 0 
  )
  PORT MAP (
    refclk => refclk,
    reset => reset,
    stdby => '0',
    extlock => extlock,
    load_reg => '0',
    psclk => '0',
    psdown => '0',
    psstep => '0',
    psclksel => b"000",
    dclk => '0',
    dcs => '0',
    dwe => '0',
    di => b"00000000",
    daddr => b"000000",
    fbclk => '0',
    clkc => clkc_wire 
  );

  clk0_out <= clkc_wire(0);

END rtl;


============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Wed Aug 20 14:55:19 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.573840s wall, 1.515625s user + 4.062500s system = 5.578125s CPU (100.1%)

RUN-1004 : used memory is 80 MB, reserved memory is 42 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.860330s wall, 1.765625s user + 0.093750s system = 1.859375s CPU (99.9%)

RUN-1004 : used memory is 303 MB, reserved memory is 271 MB, peak memory is 306 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 6 view nodes, 43 trigger nets, 43 data nets.
KIT-1004 : Chipwatcher code = 0010111001101000
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb01001010}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=132) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=132) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb01001010}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb01001010}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb01001010})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=132)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=132)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb01001010})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb01001010})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22854/26 useful/useless nets, 19576/15 useful/useless insts
SYN-1016 : Merged 34 instances.
SYN-1032 : 22505/22 useful/useless nets, 20010/18 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 412 better
SYN-1014 : Optimize round 2
SYN-1032 : 22174/45 useful/useless nets, 19679/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.638920s wall, 2.546875s user + 0.093750s system = 2.640625s CPU (100.1%)

RUN-1004 : used memory is 328 MB, reserved memory is 295 MB, peak memory is 330 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22190/155 useful/useless nets, 19714/47 useful/useless insts
SYN-1016 : Merged 14 instances.
SYN-2571 : Optimize after map_dsp, round 1, 216 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 40 instances.
SYN-2501 : Optimize round 1, 82 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 22676/4 useful/useless nets, 20200/3 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82269, tnet num: 22676, tinst num: 20199, tnode num: 115290, tedge num: 128442.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.347664s wall, 1.343750s user + 0.015625s system = 1.359375s CPU (100.9%)

RUN-1004 : used memory is 469 MB, reserved memory is 436 MB, peak memory is 469 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22676 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 242 (3.42), #lev = 7 (1.80)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 242 (3.42), #lev = 7 (1.80)
SYN-3001 : Logic optimization runtime opt =   0.04 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 559 instances into 242 LUTs, name keeping = 72%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 421 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 128 adder to BLE ...
SYN-4008 : Packed 128 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  5.078170s wall, 4.968750s user + 0.109375s system = 5.078125s CPU (100.0%)

RUN-1004 : used memory is 352 MB, reserved memory is 319 MB, peak memory is 577 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  8.078743s wall, 7.859375s user + 0.218750s system = 8.078125s CPU (100.0%)

RUN-1004 : used memory is 352 MB, reserved memory is 319 MB, peak memory is 577 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (282 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19423 instances
RUN-0007 : 5617 luts, 12218 seqs, 983 mslices, 519 lslices, 59 pads, 22 brams, 0 dsps
RUN-1001 : There are total 21907 nets
RUN-1001 : 16426 nets have 2 pins
RUN-1001 : 4289 nets have [3 - 5] pins
RUN-1001 : 832 nets have [6 - 10] pins
RUN-1001 : 234 nets have [11 - 20] pins
RUN-1001 : 105 nets have [21 - 99] pins
RUN-1001 : 21 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4788     
RUN-1001 :   No   |  No   |  Yes  |     707     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     454     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19421 instances, 5617 luts, 12218 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80599, tnet num: 21905, tinst num: 19421, tnode num: 113425, tedge num: 126718.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.265019s wall, 1.203125s user + 0.062500s system = 1.265625s CPU (100.0%)

RUN-1004 : used memory is 527 MB, reserved memory is 499 MB, peak memory is 577 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.220806s wall, 2.125000s user + 0.093750s system = 2.218750s CPU (99.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.60756e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19421.
PHY-3001 : Level 1 #clusters 2150.
PHY-3001 : End clustering;  0.167383s wall, 0.265625s user + 0.031250s system = 0.296875s CPU (177.4%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 859389, overlap = 584.625
PHY-3002 : Step(2): len = 801181, overlap = 632.969
PHY-3002 : Step(3): len = 514663, overlap = 799.781
PHY-3002 : Step(4): len = 462476, overlap = 854.812
PHY-3002 : Step(5): len = 367596, overlap = 938.469
PHY-3002 : Step(6): len = 334096, overlap = 995.812
PHY-3002 : Step(7): len = 280333, overlap = 1096.88
PHY-3002 : Step(8): len = 256461, overlap = 1169.28
PHY-3002 : Step(9): len = 221659, overlap = 1245.44
PHY-3002 : Step(10): len = 208699, overlap = 1282.22
PHY-3002 : Step(11): len = 188929, overlap = 1317.06
PHY-3002 : Step(12): len = 177548, overlap = 1343.19
PHY-3002 : Step(13): len = 161801, overlap = 1381
PHY-3002 : Step(14): len = 151642, overlap = 1406.62
PHY-3002 : Step(15): len = 141606, overlap = 1428
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.12338e-06
PHY-3002 : Step(16): len = 143682, overlap = 1415.84
PHY-3002 : Step(17): len = 184583, overlap = 1287.75
PHY-3002 : Step(18): len = 199521, overlap = 1204.28
PHY-3002 : Step(19): len = 204102, overlap = 1138.59
PHY-3002 : Step(20): len = 198711, overlap = 1119.94
PHY-3002 : Step(21): len = 191020, overlap = 1117.75
PHY-3002 : Step(22): len = 187886, overlap = 1105.59
PHY-3002 : Step(23): len = 182218, overlap = 1137.53
PHY-3002 : Step(24): len = 178186, overlap = 1137.06
PHY-3002 : Step(25): len = 174896, overlap = 1145.25
PHY-3002 : Step(26): len = 172578, overlap = 1152.16
PHY-3002 : Step(27): len = 170692, overlap = 1153.88
PHY-3002 : Step(28): len = 170202, overlap = 1145.94
PHY-3002 : Step(29): len = 170297, overlap = 1120.81
PHY-3002 : Step(30): len = 170562, overlap = 1123.47
PHY-3002 : Step(31): len = 170620, overlap = 1129.53
PHY-3002 : Step(32): len = 169685, overlap = 1117
PHY-3002 : Step(33): len = 169114, overlap = 1107.34
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.24677e-06
PHY-3002 : Step(34): len = 174579, overlap = 1088.22
PHY-3002 : Step(35): len = 189884, overlap = 1034.38
PHY-3002 : Step(36): len = 195841, overlap = 988.281
PHY-3002 : Step(37): len = 199167, overlap = 957.5
PHY-3002 : Step(38): len = 198850, overlap = 956.469
PHY-3002 : Step(39): len = 198669, overlap = 937.625
PHY-3002 : Step(40): len = 196901, overlap = 944.562
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.49353e-06
PHY-3002 : Step(41): len = 206610, overlap = 913.938
PHY-3002 : Step(42): len = 223079, overlap = 860.969
PHY-3002 : Step(43): len = 229058, overlap = 825.688
PHY-3002 : Step(44): len = 231952, overlap = 781.469
PHY-3002 : Step(45): len = 231239, overlap = 775.781
PHY-3002 : Step(46): len = 229155, overlap = 765.25
PHY-3002 : Step(47): len = 227807, overlap = 752.406
PHY-3002 : Step(48): len = 225739, overlap = 745.188
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 8.98706e-06
PHY-3002 : Step(49): len = 235986, overlap = 739.844
PHY-3002 : Step(50): len = 252778, overlap = 671.5
PHY-3002 : Step(51): len = 257810, overlap = 606.062
PHY-3002 : Step(52): len = 260225, overlap = 585.906
PHY-3002 : Step(53): len = 259313, overlap = 576.656
PHY-3002 : Step(54): len = 258843, overlap = 549.938
PHY-3002 : Step(55): len = 256742, overlap = 542.094
PHY-3002 : Step(56): len = 257017, overlap = 542.25
PHY-3002 : Step(57): len = 256189, overlap = 539.438
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.79741e-05
PHY-3002 : Step(58): len = 264942, overlap = 520.781
PHY-3002 : Step(59): len = 277085, overlap = 439.094
PHY-3002 : Step(60): len = 280547, overlap = 415.844
PHY-3002 : Step(61): len = 283163, overlap = 388.156
PHY-3002 : Step(62): len = 282588, overlap = 380.344
PHY-3002 : Step(63): len = 281819, overlap = 382.312
PHY-3002 : Step(64): len = 280572, overlap = 385.312
PHY-3002 : Step(65): len = 279913, overlap = 382.156
PHY-3002 : Step(66): len = 279197, overlap = 383.156
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.59483e-05
PHY-3002 : Step(67): len = 288679, overlap = 363.656
PHY-3002 : Step(68): len = 297263, overlap = 366.031
PHY-3002 : Step(69): len = 299749, overlap = 340.562
PHY-3002 : Step(70): len = 301161, overlap = 339.719
PHY-3002 : Step(71): len = 299835, overlap = 343.5
PHY-3002 : Step(72): len = 298887, overlap = 352.406
PHY-3002 : Step(73): len = 297646, overlap = 358.312
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 7.18965e-05
PHY-3002 : Step(74): len = 306609, overlap = 320.75
PHY-3002 : Step(75): len = 314208, overlap = 274.438
PHY-3002 : Step(76): len = 317160, overlap = 251.781
PHY-3002 : Step(77): len = 317389, overlap = 241.375
PHY-3002 : Step(78): len = 316120, overlap = 243.062
PHY-3002 : Step(79): len = 314756, overlap = 245.5
PHY-3002 : Step(80): len = 313329, overlap = 241.719
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000143793
PHY-3002 : Step(81): len = 319000, overlap = 234.531
PHY-3002 : Step(82): len = 325168, overlap = 216.188
PHY-3002 : Step(83): len = 326054, overlap = 215.094
PHY-3002 : Step(84): len = 327208, overlap = 224.594
PHY-3002 : Step(85): len = 327396, overlap = 229.094
PHY-3002 : Step(86): len = 326923, overlap = 241.156
PHY-3002 : Step(87): len = 325616, overlap = 232.406
PHY-3002 : Step(88): len = 325676, overlap = 243.094
PHY-3002 : Step(89): len = 325386, overlap = 245.812
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000287586
PHY-3002 : Step(90): len = 327970, overlap = 233.312
PHY-3002 : Step(91): len = 332170, overlap = 218.656
PHY-3002 : Step(92): len = 333858, overlap = 213.812
PHY-3002 : Step(93): len = 335622, overlap = 211.531
PHY-3002 : Step(94): len = 335556, overlap = 213.719
PHY-3002 : Step(95): len = 336694, overlap = 213.594
PHY-3002 : Step(96): len = 335692, overlap = 213.406
PHY-3002 : Step(97): len = 336161, overlap = 202.938
PHY-3002 : Step(98): len = 335254, overlap = 203.25
PHY-3002 : Step(99): len = 335275, overlap = 210.656
PHY-3002 : Step(100): len = 334729, overlap = 203.781
PHY-3002 : Step(101): len = 335178, overlap = 195
PHY-3002 : Step(102): len = 334393, overlap = 186.625
PHY-3002 : Step(103): len = 334554, overlap = 169.156
PHY-3002 : Step(104): len = 333992, overlap = 179.375
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000497546
PHY-3002 : Step(105): len = 335147, overlap = 175.531
PHY-3002 : Step(106): len = 337286, overlap = 176.625
PHY-3002 : Step(107): len = 337870, overlap = 184.281
PHY-3001 : :::11::: Try harder cell spreading with beta_ = 0.000829773
PHY-3002 : Step(108): len = 338749, overlap = 171.156
PHY-3002 : Step(109): len = 341989, overlap = 162.844
PHY-3002 : Step(110): len = 342420, overlap = 160.812
PHY-3002 : Step(111): len = 342644, overlap = 160.625
PHY-3002 : Step(112): len = 343121, overlap = 149.594
PHY-3002 : Step(113): len = 343561, overlap = 145.594
PHY-3002 : Step(114): len = 343145, overlap = 146.875
PHY-3002 : Step(115): len = 343014, overlap = 141.25
PHY-3002 : Step(116): len = 343565, overlap = 137.656
PHY-3002 : Step(117): len = 344149, overlap = 126.969
PHY-3002 : Step(118): len = 344028, overlap = 124.375
PHY-3002 : Step(119): len = 344255, overlap = 122.688
PHY-3002 : Step(120): len = 344343, overlap = 122.188
PHY-3002 : Step(121): len = 344351, overlap = 123.156
PHY-3002 : Step(122): len = 344231, overlap = 132.844
PHY-3001 : :::12::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(123): len = 344786, overlap = 132.594
PHY-3002 : Step(124): len = 347102, overlap = 136.062
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.019824s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (157.6%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21907.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 448536, over cnt = 1186(3%), over = 5273, worst = 36
PHY-1001 : End global iterations;  0.924032s wall, 1.140625s user + 0.125000s system = 1.265625s CPU (137.0%)

PHY-1001 : Congestion index: top1 = 73.75, top5 = 52.51, top10 = 42.88, top15 = 37.25.
PHY-3001 : End congestion estimation;  1.163244s wall, 1.359375s user + 0.140625s system = 1.500000s CPU (128.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.017810s wall, 1.000000s user + 0.031250s system = 1.031250s CPU (101.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000125777
PHY-3002 : Step(125): len = 385554, overlap = 131.281
PHY-3002 : Step(126): len = 390137, overlap = 131.812
PHY-3002 : Step(127): len = 390302, overlap = 111.156
PHY-3002 : Step(128): len = 390655, overlap = 108.438
PHY-3002 : Step(129): len = 395855, overlap = 107.031
PHY-3002 : Step(130): len = 400716, overlap = 106.094
PHY-3002 : Step(131): len = 406250, overlap = 101.344
PHY-3002 : Step(132): len = 409583, overlap = 97.9375
PHY-3002 : Step(133): len = 411415, overlap = 95.375
PHY-3002 : Step(134): len = 411361, overlap = 92.2812
PHY-3002 : Step(135): len = 412820, overlap = 87.0938
PHY-3002 : Step(136): len = 414784, overlap = 92.7188
PHY-3002 : Step(137): len = 414869, overlap = 94.1875
PHY-3002 : Step(138): len = 416095, overlap = 89.9062
PHY-3002 : Step(139): len = 417095, overlap = 91.8125
PHY-3002 : Step(140): len = 416341, overlap = 94.4375
PHY-3002 : Step(141): len = 416782, overlap = 95.375
PHY-3002 : Step(142): len = 415899, overlap = 97.3125
PHY-3002 : Step(143): len = 415656, overlap = 108.281
PHY-3002 : Step(144): len = 415394, overlap = 119.281
PHY-3002 : Step(145): len = 415054, overlap = 123.469
PHY-3002 : Step(146): len = 415539, overlap = 124.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000251554
PHY-3002 : Step(147): len = 416101, overlap = 117.688
PHY-3002 : Step(148): len = 417652, overlap = 113.531
PHY-3002 : Step(149): len = 419591, overlap = 108.406
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000503109
PHY-3002 : Step(150): len = 425365, overlap = 99.2188
PHY-3002 : Step(151): len = 430503, overlap = 94.2188
PHY-3002 : Step(152): len = 433136, overlap = 92.1562
PHY-3002 : Step(153): len = 434016, overlap = 91.2188
PHY-3002 : Step(154): len = 435491, overlap = 89.2188
PHY-3002 : Step(155): len = 435692, overlap = 96
PHY-3002 : Step(156): len = 436601, overlap = 97
PHY-3002 : Step(157): len = 436347, overlap = 92.3438
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 56/21907.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 499368, over cnt = 2039(5%), over = 9457, worst = 33
PHY-1001 : End global iterations;  1.028020s wall, 1.703125s user + 0.109375s system = 1.812500s CPU (176.3%)

PHY-1001 : Congestion index: top1 = 73.30, top5 = 56.08, top10 = 48.18, top15 = 43.24.
PHY-3001 : End congestion estimation;  1.318016s wall, 1.984375s user + 0.109375s system = 2.093750s CPU (158.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.999037s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000103319
PHY-3002 : Step(158): len = 443086, overlap = 371.719
PHY-3002 : Step(159): len = 445111, overlap = 320.344
PHY-3002 : Step(160): len = 444845, overlap = 298.156
PHY-3002 : Step(161): len = 444691, overlap = 275.75
PHY-3002 : Step(162): len = 442571, overlap = 250.812
PHY-3002 : Step(163): len = 442322, overlap = 238.344
PHY-3002 : Step(164): len = 440560, overlap = 229.5
PHY-3002 : Step(165): len = 439106, overlap = 217.062
PHY-3002 : Step(166): len = 438136, overlap = 198.875
PHY-3002 : Step(167): len = 435976, overlap = 200.406
PHY-3002 : Step(168): len = 435500, overlap = 198.688
PHY-3002 : Step(169): len = 433853, overlap = 201.781
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000206638
PHY-3002 : Step(170): len = 433714, overlap = 193.625
PHY-3002 : Step(171): len = 435153, overlap = 193.625
PHY-3002 : Step(172): len = 436622, overlap = 195.094
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000413275
PHY-3002 : Step(173): len = 440054, overlap = 182.031
PHY-3002 : Step(174): len = 445014, overlap = 161.25
PHY-3002 : Step(175): len = 449009, overlap = 153.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000826551
PHY-3002 : Step(176): len = 449617, overlap = 151.344
PHY-3002 : Step(177): len = 453831, overlap = 141
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80599, tnet num: 21905, tinst num: 19421, tnode num: 113425, tedge num: 126718.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.570753s wall, 1.531250s user + 0.046875s system = 1.578125s CPU (100.5%)

RUN-1004 : used memory is 566 MB, reserved memory is 541 MB, peak memory is 700 MB
OPT-1001 : Total overflow 530.44 peak overflow 3.62
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 652/21907.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 531848, over cnt = 2445(6%), over = 8507, worst = 20
PHY-1001 : End global iterations;  1.199866s wall, 1.875000s user + 0.000000s system = 1.875000s CPU (156.3%)

PHY-1001 : Congestion index: top1 = 57.95, top5 = 46.82, top10 = 41.82, top15 = 38.87.
PHY-1001 : End incremental global routing;  1.475389s wall, 2.156250s user + 0.000000s system = 2.156250s CPU (146.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.204996s wall, 1.187500s user + 0.000000s system = 1.187500s CPU (98.5%)

OPT-1001 : 13 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19346 has valid locations, 191 needs to be replaced
PHY-3001 : design contains 19599 instances, 5699 luts, 12314 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 467560
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17149/22085.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 539336, over cnt = 2465(7%), over = 8563, worst = 20
PHY-1001 : End global iterations;  0.188841s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (148.9%)

PHY-1001 : Congestion index: top1 = 58.19, top5 = 47.06, top10 = 42.21, top15 = 39.24.
PHY-3001 : End congestion estimation;  0.448584s wall, 0.515625s user + 0.015625s system = 0.531250s CPU (118.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81147, tnet num: 22083, tinst num: 19599, tnode num: 114177, tedge num: 127458.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.617093s wall, 1.609375s user + 0.015625s system = 1.625000s CPU (100.5%)

RUN-1004 : used memory is 610 MB, reserved memory is 603 MB, peak memory is 702 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22083 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.720105s wall, 2.640625s user + 0.078125s system = 2.718750s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(178): len = 467525, overlap = 1.875
PHY-3002 : Step(179): len = 468763, overlap = 1.9375
PHY-3002 : Step(180): len = 469924, overlap = 2
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17181/22085.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 539432, over cnt = 2481(7%), over = 8643, worst = 20
PHY-1001 : End global iterations;  0.194271s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (96.5%)

PHY-1001 : Congestion index: top1 = 58.53, top5 = 47.19, top10 = 42.24, top15 = 39.24.
PHY-3001 : End congestion estimation;  0.458281s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (98.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22083 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.066811s wall, 1.031250s user + 0.031250s system = 1.062500s CPU (99.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000706435
PHY-3002 : Step(181): len = 469586, overlap = 142.875
PHY-3002 : Step(182): len = 469570, overlap = 143.094
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00141287
PHY-3002 : Step(183): len = 469800, overlap = 143.125
PHY-3002 : Step(184): len = 470143, overlap = 142.812
PHY-3001 : Final: Len = 470143, Over = 142.812
PHY-3001 : End incremental placement;  5.580231s wall, 5.656250s user + 0.312500s system = 5.968750s CPU (107.0%)

OPT-1001 : Total overflow 535.62 peak overflow 3.62
OPT-1001 : End high-fanout net optimization;  8.800845s wall, 9.703125s user + 0.328125s system = 10.031250s CPU (114.0%)

OPT-1001 : Current memory(MB): used = 705, reserve = 684, peak = 720.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17169/22085.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 540616, over cnt = 2425(6%), over = 8114, worst = 20
PHY-1002 : len = 579672, over cnt = 1760(5%), over = 4522, worst = 20
PHY-1002 : len = 607808, over cnt = 937(2%), over = 2418, worst = 17
PHY-1002 : len = 632448, over cnt = 372(1%), over = 919, worst = 17
PHY-1002 : len = 648336, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.329727s wall, 1.890625s user + 0.000000s system = 1.890625s CPU (142.2%)

PHY-1001 : Congestion index: top1 = 49.16, top5 = 42.64, top10 = 39.56, top15 = 37.73.
OPT-1001 : End congestion update;  1.596267s wall, 2.156250s user + 0.000000s system = 2.156250s CPU (135.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22083 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.914432s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (100.8%)

OPT-0007 : Start: WNS 4087 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.516807s wall, 3.078125s user + 0.000000s system = 3.078125s CPU (122.3%)

OPT-1001 : Current memory(MB): used = 678, reserve = 657, peak = 720.
OPT-1001 : End physical optimization;  13.217994s wall, 14.812500s user + 0.375000s system = 15.187500s CPU (114.9%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5699 LUT to BLE ...
SYN-4008 : Packed 5699 LUT and 2732 SEQ to BLE.
SYN-4003 : Packing 9582 remaining SEQ's ...
SYN-4005 : Packed 3319 SEQ with LUT/SLICE
SYN-4006 : 172 single LUT's are left
SYN-4006 : 6263 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11962/13814 primitive instances ...
PHY-3001 : End packing;  2.887181s wall, 2.890625s user + 0.000000s system = 2.890625s CPU (100.1%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8130 instances
RUN-1001 : 4022 mslices, 4022 lslices, 59 pads, 22 brams, 0 dsps
RUN-1001 : There are total 19406 nets
RUN-1001 : 13620 nets have 2 pins
RUN-1001 : 4384 nets have [3 - 5] pins
RUN-1001 : 896 nets have [6 - 10] pins
RUN-1001 : 367 nets have [11 - 20] pins
RUN-1001 : 130 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8128 instances, 8044 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 487218, Over = 368
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7956/19406.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 614848, over cnt = 1528(4%), over = 2430, worst = 10
PHY-1002 : len = 620872, over cnt = 1033(2%), over = 1450, worst = 6
PHY-1002 : len = 632256, over cnt = 421(1%), over = 539, worst = 6
PHY-1002 : len = 637296, over cnt = 215(0%), over = 260, worst = 6
PHY-1002 : len = 642040, over cnt = 13(0%), over = 13, worst = 1
PHY-1001 : End global iterations;  1.230542s wall, 1.937500s user + 0.015625s system = 1.953125s CPU (158.7%)

PHY-1001 : Congestion index: top1 = 49.31, top5 = 43.07, top10 = 39.87, top15 = 37.57.
PHY-3001 : End congestion estimation;  1.579388s wall, 2.296875s user + 0.015625s system = 2.312500s CPU (146.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67267, tnet num: 19404, tinst num: 8128, tnode num: 91292, tedge num: 110867.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.755581s wall, 1.718750s user + 0.031250s system = 1.750000s CPU (99.7%)

RUN-1004 : used memory is 601 MB, reserved memory is 591 MB, peak memory is 720 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19404 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.735806s wall, 2.687500s user + 0.046875s system = 2.734375s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.85567e-05
PHY-3002 : Step(185): len = 494244, overlap = 361.25
PHY-3002 : Step(186): len = 494237, overlap = 359.25
PHY-3002 : Step(187): len = 496015, overlap = 360.5
PHY-3002 : Step(188): len = 496879, overlap = 374
PHY-3002 : Step(189): len = 494627, overlap = 378.25
PHY-3002 : Step(190): len = 493343, overlap = 384
PHY-3002 : Step(191): len = 492094, overlap = 396.5
PHY-3002 : Step(192): len = 489986, overlap = 401.75
PHY-3002 : Step(193): len = 488645, overlap = 404.5
PHY-3002 : Step(194): len = 487601, overlap = 402.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.71133e-05
PHY-3002 : Step(195): len = 491749, overlap = 390
PHY-3002 : Step(196): len = 496655, overlap = 369.25
PHY-3002 : Step(197): len = 497924, overlap = 368
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(198): len = 504630, overlap = 356.25
PHY-3002 : Step(199): len = 514604, overlap = 352.75
PHY-3002 : Step(200): len = 514586, overlap = 352.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.786496s wall, 0.875000s user + 0.828125s system = 1.703125s CPU (216.5%)

PHY-3001 : Trial Legalized: Len = 625601
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 521/19406.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 707896, over cnt = 2410(6%), over = 3916, worst = 8
PHY-1002 : len = 723640, over cnt = 1383(3%), over = 1913, worst = 6
PHY-1002 : len = 741360, over cnt = 461(1%), over = 611, worst = 5
PHY-1002 : len = 749144, over cnt = 100(0%), over = 121, worst = 3
PHY-1002 : len = 751696, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.009579s wall, 3.265625s user + 0.000000s system = 3.265625s CPU (162.5%)

PHY-1001 : Congestion index: top1 = 48.25, top5 = 44.18, top10 = 41.67, top15 = 39.94.
PHY-3001 : End congestion estimation;  2.389202s wall, 3.640625s user + 0.000000s system = 3.640625s CPU (152.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19404 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.949020s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000190595
PHY-3002 : Step(201): len = 581093, overlap = 82.25
PHY-3002 : Step(202): len = 563279, overlap = 114.5
PHY-3002 : Step(203): len = 551581, overlap = 158
PHY-3002 : Step(204): len = 544017, overlap = 195.75
PHY-3002 : Step(205): len = 540452, overlap = 220
PHY-3002 : Step(206): len = 538093, overlap = 234.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000381189
PHY-3002 : Step(207): len = 543005, overlap = 227.5
PHY-3002 : Step(208): len = 547496, overlap = 228.25
PHY-3002 : Step(209): len = 546686, overlap = 233.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000724528
PHY-3002 : Step(210): len = 549333, overlap = 230.25
PHY-3002 : Step(211): len = 557065, overlap = 235.25
PHY-3002 : Step(212): len = 562348, overlap = 232
PHY-3002 : Step(213): len = 562251, overlap = 229.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.035414s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (132.4%)

PHY-3001 : Legalized: Len = 601535, Over = 0
PHY-3001 : Spreading special nets. 38 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.087435s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (89.4%)

PHY-3001 : 56 instances has been re-located, deltaX = 26, deltaY = 24, maxDist = 2.
PHY-3001 : Final: Len = 602213, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67267, tnet num: 19404, tinst num: 8128, tnode num: 91292, tedge num: 110867.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.982784s wall, 1.968750s user + 0.015625s system = 1.984375s CPU (100.1%)

RUN-1004 : used memory is 627 MB, reserved memory is 626 MB, peak memory is 720 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3273/19406.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 689232, over cnt = 2219(6%), over = 3510, worst = 6
PHY-1002 : len = 702736, over cnt = 1233(3%), over = 1694, worst = 6
PHY-1002 : len = 717960, over cnt = 428(1%), over = 562, worst = 5
PHY-1002 : len = 723480, over cnt = 157(0%), over = 196, worst = 4
PHY-1002 : len = 727664, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.789533s wall, 2.828125s user + 0.031250s system = 2.859375s CPU (159.8%)

PHY-1001 : Congestion index: top1 = 48.08, top5 = 42.78, top10 = 40.01, top15 = 38.23.
PHY-1001 : End incremental global routing;  2.117679s wall, 3.171875s user + 0.031250s system = 3.203125s CPU (151.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19404 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.965723s wall, 0.937500s user + 0.015625s system = 0.953125s CPU (98.7%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8064 has valid locations, 30 needs to be replaced
PHY-3001 : design contains 8156 instances, 8072 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 604339
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17469/19432.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 730496, over cnt = 33(0%), over = 41, worst = 3
PHY-1002 : len = 730368, over cnt = 18(0%), over = 22, worst = 3
PHY-1002 : len = 730384, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 730424, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 730712, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.731501s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (100.4%)

PHY-1001 : Congestion index: top1 = 48.17, top5 = 42.87, top10 = 40.12, top15 = 38.38.
PHY-3001 : End congestion estimation;  1.048563s wall, 1.046875s user + 0.000000s system = 1.046875s CPU (99.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67413, tnet num: 19430, tinst num: 8156, tnode num: 91505, tedge num: 111078.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.012492s wall, 2.015625s user + 0.000000s system = 2.015625s CPU (100.2%)

RUN-1004 : used memory is 704 MB, reserved memory is 694 MB, peak memory is 720 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19430 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.986042s wall, 2.984375s user + 0.000000s system = 2.984375s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(214): len = 604552, overlap = 1.75
PHY-3002 : Step(215): len = 604730, overlap = 2
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17467/19432.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 729928, over cnt = 25(0%), over = 34, worst = 3
PHY-1002 : len = 729928, over cnt = 15(0%), over = 18, worst = 2
PHY-1002 : len = 730008, over cnt = 7(0%), over = 9, worst = 2
PHY-1002 : len = 730120, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 730176, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.732768s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (100.2%)

PHY-1001 : Congestion index: top1 = 48.19, top5 = 42.95, top10 = 40.17, top15 = 38.42.
PHY-3001 : End congestion estimation;  1.053315s wall, 1.062500s user + 0.000000s system = 1.062500s CPU (100.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19430 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.939595s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000281002
PHY-3002 : Step(216): len = 604774, overlap = 3
PHY-3002 : Step(217): len = 604864, overlap = 3.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007839s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (199.3%)

PHY-3001 : Legalized: Len = 604936, Over = 0
PHY-3001 : End spreading;  0.076743s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (101.8%)

PHY-3001 : Final: Len = 604936, Over = 0
PHY-3001 : End incremental placement;  6.657172s wall, 6.625000s user + 0.109375s system = 6.734375s CPU (101.2%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.255905s wall, 11.234375s user + 0.156250s system = 11.390625s CPU (111.1%)

OPT-1001 : Current memory(MB): used = 721, reserve = 710, peak = 726.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17465/19432.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 730856, over cnt = 23(0%), over = 28, worst = 3
PHY-1002 : len = 730824, over cnt = 15(0%), over = 16, worst = 2
PHY-1002 : len = 730912, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 730960, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 731120, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.696855s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (100.9%)

PHY-1001 : Congestion index: top1 = 48.25, top5 = 43.01, top10 = 40.20, top15 = 38.46.
OPT-1001 : End congestion update;  1.010120s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (100.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19430 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.808702s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (100.5%)

OPT-0007 : Start: WNS 4164 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.823777s wall, 1.828125s user + 0.000000s system = 1.828125s CPU (100.2%)

OPT-1001 : Current memory(MB): used = 722, reserve = 711, peak = 726.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19430 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.801744s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.4%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17497/19432.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 731120, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.123162s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (101.5%)

PHY-1001 : Congestion index: top1 = 48.25, top5 = 43.01, top10 = 40.20, top15 = 38.46.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19430 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.804385s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4164 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 47.827586
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4164ps with logic level 8 
RUN-1001 :       #2 path slack 4239ps with logic level 8 
OPT-1001 : End physical optimization;  16.374994s wall, 17.328125s user + 0.187500s system = 17.515625s CPU (107.0%)

RUN-1003 : finish command "place" in  67.170129s wall, 119.500000s user + 7.750000s system = 127.250000s CPU (189.4%)

RUN-1004 : used memory is 634 MB, reserved memory is 612 MB, peak memory is 726 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.732453s wall, 3.015625s user + 0.000000s system = 3.015625s CPU (174.1%)

RUN-1004 : used memory is 634 MB, reserved memory is 613 MB, peak memory is 726 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8158 instances
RUN-1001 : 4030 mslices, 4042 lslices, 59 pads, 22 brams, 0 dsps
RUN-1001 : There are total 19432 nets
RUN-1001 : 13619 nets have 2 pins
RUN-1001 : 4402 nets have [3 - 5] pins
RUN-1001 : 901 nets have [6 - 10] pins
RUN-1001 : 372 nets have [11 - 20] pins
RUN-1001 : 129 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67413, tnet num: 19430, tinst num: 8156, tnode num: 91505, tedge num: 111078.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.794769s wall, 1.781250s user + 0.000000s system = 1.781250s CPU (99.2%)

RUN-1004 : used memory is 616 MB, reserved memory is 595 MB, peak memory is 726 MB
PHY-1001 : 4030 mslices, 4042 lslices, 59 pads, 22 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19430 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 673248, over cnt = 2282(6%), over = 3812, worst = 7
PHY-1002 : len = 690072, over cnt = 1357(3%), over = 1935, worst = 6
PHY-1002 : len = 711480, over cnt = 328(0%), over = 412, worst = 5
PHY-1002 : len = 718816, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 718992, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.801559s wall, 3.015625s user + 0.031250s system = 3.046875s CPU (169.1%)

PHY-1001 : Congestion index: top1 = 47.72, top5 = 42.63, top10 = 39.90, top15 = 38.11.
PHY-1001 : End global routing;  2.162169s wall, 3.343750s user + 0.046875s system = 3.390625s CPU (156.8%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 698, reserve = 685, peak = 726.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 966, reserve = 953, peak = 966.
PHY-1001 : End build detailed router design. 4.645499s wall, 4.609375s user + 0.031250s system = 4.640625s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 194984, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.920784s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1002, reserve = 990, peak = 1002.
PHY-1001 : End phase 1; 0.927989s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (99.3%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.67149e+06, over cnt = 1262(0%), over = 1267, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1019, reserve = 1007, peak = 1019.
PHY-1001 : End initial routed; 13.955244s wall, 42.593750s user + 0.406250s system = 43.000000s CPU (308.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18160(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.205   |   0.000   |   0   
RUN-1001 :   Hold   |   0.104   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.502109s wall, 3.500000s user + 0.000000s system = 3.500000s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 1029, reserve = 1017, peak = 1029.
PHY-1001 : End phase 2; 17.457512s wall, 46.093750s user + 0.406250s system = 46.500000s CPU (266.4%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.67149e+06, over cnt = 1262(0%), over = 1267, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.254531s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (98.2%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.6595e+06, over cnt = 437(0%), over = 437, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.742932s wall, 1.390625s user + 0.000000s system = 1.390625s CPU (187.2%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.65978e+06, over cnt = 77(0%), over = 77, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.392503s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (139.3%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.66029e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.260949s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (107.8%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.6603e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 4; 0.175994s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (106.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18160(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.205   |   0.000   |   0   
RUN-1001 :   Hold   |   0.104   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.499841s wall, 3.500000s user + 0.000000s system = 3.500000s CPU (100.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 304 feed throughs used by 276 nets
PHY-1001 : End commit to database; 2.255080s wall, 2.234375s user + 0.000000s system = 2.234375s CPU (99.1%)

PHY-1001 : Current memory(MB): used = 1117, reserve = 1108, peak = 1117.
PHY-1001 : End phase 3; 8.070272s wall, 8.890625s user + 0.000000s system = 8.890625s CPU (110.2%)

PHY-1003 : Routed, final wirelength = 1.6603e+06
PHY-1001 : Current memory(MB): used = 1121, reserve = 1112, peak = 1121.
PHY-1001 : End export database. 0.063258s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.8%)

PHY-1001 : End detail routing;  31.618120s wall, 61.046875s user + 0.437500s system = 61.484375s CPU (194.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67413, tnet num: 19430, tinst num: 8156, tnode num: 91505, tedge num: 111078.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.878357s wall, 1.890625s user + 0.000000s system = 1.890625s CPU (100.7%)

RUN-1004 : used memory is 1053 MB, reserved memory is 1052 MB, peak memory is 1121 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  40.293619s wall, 70.859375s user + 0.515625s system = 71.375000s CPU (177.1%)

RUN-1004 : used memory is 1052 MB, reserved memory is 1053 MB, peak memory is 1121 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8873   out of  19600   45.27%
#reg                    12427   out of  19600   63.40%
#le                     15098
  #lut only              2671   out of  15098   17.69%
  #reg only              6225   out of  15098   41.23%
  #lut&reg               6202   out of  15098   41.08%
#dsp                        0   out of     29    0.00%
#bram                      22   out of     64   34.38%
  #bram9k                  20
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       59   out of    188   31.38%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6811
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          162
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         F2        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         D1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         B3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         C1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         A3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         A2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         B2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         B1        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F15        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDZ        INPUT        K15        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         P2        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         N4        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         M5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         N6        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         N5        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         P5        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M3        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         M4        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         H2        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         M1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         L1        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         N1        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         N3        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         M2        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         T4        LVCMOS33           8            NONE           OREG       
    Fog_ENZ        OUTPUT         T7        LVCMOS33           8            NONE           OREG       
    Fog_INT        OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT         C3        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT        B15        LVCMOS33           8            N/A            OREG       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        R15        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         K2        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT         F6        LVCMOS25           8            N/A            NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15098  |7371    |1502    |12471   |22      |0       |
|  AnyFog_dataX                      |AnyFog          |213    |85      |22      |174     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |91     |62      |22      |52      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |220    |97      |22      |174     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |101    |69      |22      |55      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |217    |100     |22      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |95     |68      |22      |51      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2931   |658     |39      |2840    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |63     |39      |5       |54      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |214    |63      |5       |204     |0       |0       |
|    STADOP_com2                     |STADOP          |546    |87      |0       |542     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |42      |14      |42      |0       |0       |
|    head_com2                       |uniheading      |262    |57      |5       |248     |0       |0       |
|    rmc_com2                        |Gprmc           |52     |52      |0       |43      |0       |0       |
|    uart_com2                       |Agrica          |1422   |302     |10      |1400    |0       |0       |
|  COM3                              |COM3_Control    |279    |147     |19      |238     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |59     |43      |5       |50      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |60     |46      |14      |37      |0       |0       |
|    rmc_com3                        |Gprmc           |160    |58      |0       |151     |0       |0       |
|  DATA                              |Data_Processing |8834   |4511    |1122    |7025    |0       |0       |
|    DIV_Dtemp                       |Divider         |822    |308     |84      |697     |0       |0       |
|    DIV_Utemp                       |Divider         |618    |286     |84      |489     |0       |0       |
|    DIV_accX                        |Divider         |604    |359     |84      |482     |0       |0       |
|    DIV_accY                        |Divider         |650    |381     |102     |489     |0       |0       |
|    DIV_accZ                        |Divider         |644    |364     |132     |440     |0       |0       |
|    DIV_rateX                       |Divider         |660    |394     |132     |450     |0       |0       |
|    DIV_rateY                       |Divider         |601    |365     |132     |399     |0       |0       |
|    DIV_rateZ                       |Divider         |553    |370     |132     |342     |0       |0       |
|    genclk                          |genclk          |259    |159     |89      |95      |0       |0       |
|  FMC                               |FMC_Ctrl        |475    |426     |43      |343     |0       |0       |
|  IIC                               |I2C_master      |302    |278     |11      |255     |0       |0       |
|  IMU_CTRL                          |SCHA634         |913    |649     |61      |744     |0       |0       |
|    CtrlData                        |CtrlData        |463    |407     |47      |333     |0       |0       |
|      usms                          |Time_1ms        |29     |24      |5       |21      |0       |0       |
|    SPIM                            |SPI_SCHA634     |450    |242     |14      |411     |0       |0       |
|  POWER                             |POWER_EN        |102    |49      |38      |42      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |600    |367     |103     |407     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |600    |367     |103     |407     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |272    |151     |0       |259     |0       |0       |
|        reg_inst                    |register        |270    |149     |0       |257     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |328    |216     |103     |148     |0       |0       |
|        bus_inst                    |bus_top         |120    |73      |46      |39      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |23     |13      |10      |7       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |49     |30      |18      |16      |0       |0       |
|          BUS_DETECTOR[5]$bus_nodes |bus_det         |46     |28      |18      |14      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |126    |97      |29      |74      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13559  
    #2          2       3440   
    #3          3        671   
    #4          4        291   
    #5        5-10       962   
    #6        11-50      430   
    #7       51-100      10    
    #8       101-500      3    
    #9        >500        2    
  Average     2.11             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.105951s wall, 3.593750s user + 0.000000s system = 3.593750s CPU (170.6%)

RUN-1004 : used memory is 1053 MB, reserved memory is 1053 MB, peak memory is 1121 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67413, tnet num: 19430, tinst num: 8156, tnode num: 91505, tedge num: 111078.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.779716s wall, 1.781250s user + 0.000000s system = 1.781250s CPU (100.1%)

RUN-1004 : used memory is 1055 MB, reserved memory is 1055 MB, peak memory is 1121 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19430 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.448397s wall, 1.421875s user + 0.031250s system = 1.453125s CPU (100.3%)

RUN-1004 : used memory is 1061 MB, reserved memory is 1060 MB, peak memory is 1121 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 853bcefd6f8683164bbfb7926f14a230bf207ba9ebe1befe0692b850b36b2763 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8156
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19432, pip num: 144573
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 304
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3230 valid insts, and 406530 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101000110010111001101000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.644003s wall, 126.906250s user + 0.187500s system = 127.093750s CPU (1005.2%)

RUN-1004 : used memory is 1182 MB, reserved memory is 1166 MB, peak memory is 1297 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250820_145519.log"

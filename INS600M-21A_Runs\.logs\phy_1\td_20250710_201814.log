============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Jul 10 20:18:14 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(520)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.148528s wall, 1.375000s user + 3.765625s system = 5.140625s CPU (99.8%)

RUN-1004 : used memory is 79 MB, reserved memory is 40 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.751327s wall, 1.703125s user + 0.046875s system = 1.750000s CPU (99.9%)

RUN-1004 : used memory is 299 MB, reserved memory is 266 MB, peak memory is 302 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 10 view nodes, 47 trigger nets, 47 data nets.
KIT-1004 : Chipwatcher code = 0101101001110001
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=10,BUS_DIN_NUM=47,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb0100101,32'sb0101101,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01100110,32'sb01111010,32'sb010000000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=156) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=156) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=10,BUS_DIN_NUM=47,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb0100101,32'sb0101101,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01100110,32'sb01111010,32'sb010000000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=10,BUS_DIN_NUM=47,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb0100101,32'sb0101101,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01100110,32'sb01111010,32'sb010000000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=10,BUS_DIN_NUM=47,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb0100101,32'sb0101101,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01100110,32'sb01111010,32'sb010000000})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=156)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=156)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=10,BUS_DIN_NUM=47,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb0100101,32'sb0101101,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01100110,32'sb01111010,32'sb010000000})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=10,BUS_DIN_NUM=47,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb0100101,32'sb0101101,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01100110,32'sb01111010,32'sb010000000})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22583/42 useful/useless nets, 19284/25 useful/useless insts
SYN-1016 : Merged 49 instances.
SYN-1032 : 22171/26 useful/useless nets, 19755/22 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 5 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 5 mux instances.
SYN-1015 : Optimize round 1, 472 better
SYN-1014 : Optimize round 2
SYN-1032 : 21760/75 useful/useless nets, 19344/80 useful/useless insts
SYN-1015 : Optimize round 2, 160 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.213711s wall, 2.171875s user + 0.046875s system = 2.218750s CPU (100.2%)

RUN-1004 : used memory is 326 MB, reserved memory is 292 MB, peak memory is 328 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 21820/367 useful/useless nets, 19445/52 useful/useless insts
SYN-1016 : Merged 57 instances.
SYN-2571 : Optimize after map_dsp, round 1, 476 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 43 instances.
SYN-2501 : Optimize round 1, 87 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 20 macro adder
SYN-1019 : Optimized 21 mux instances.
SYN-1016 : Merged 17 instances.
SYN-1032 : 22297/5 useful/useless nets, 19922/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81619, tnet num: 22297, tinst num: 19921, tnode num: 114396, tedge num: 127303.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.158687s wall, 1.156250s user + 0.015625s system = 1.171875s CPU (101.1%)

RUN-1004 : used memory is 464 MB, reserved memory is 432 MB, peak memory is 464 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22297 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 265 (3.48), #lev = 7 (1.86)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 265 (3.48), #lev = 7 (1.86)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 656 instances into 265 LUTs, name keeping = 71%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 477 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 132 adder to BLE ...
SYN-4008 : Packed 132 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.193105s wall, 4.062500s user + 0.109375s system = 4.171875s CPU (99.5%)

RUN-1004 : used memory is 366 MB, reserved memory is 348 MB, peak memory is 572 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  6.703715s wall, 6.484375s user + 0.203125s system = 6.687500s CPU (99.8%)

RUN-1004 : used memory is 366 MB, reserved memory is 348 MB, peak memory is 572 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/uart_com2/AGRIC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/uart_com2/AGRIC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/uart_com2/AGRIC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/uart_com2/AGRIC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/uart_com2/AGRIC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/uart_com2/AGRIC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/uart_com2/AGRIC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/uart_com2/AGRIC_data[0]
SYN-5055 WARNING: The kept net COM3/rmc_com3/GPRMC_end will be merged to another kept net COM3/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM3/rmc_com3/GPRMC_sta will be merged to another kept net COM3/GNRMC/GPRMC_sta
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (328 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19147 instances
RUN-0007 : 5456 luts, 12144 seqs, 943 mslices, 496 lslices, 60 pads, 43 brams, 0 dsps
RUN-1001 : There are total 21546 nets
RUN-1001 : 16164 nets have 2 pins
RUN-1001 : 4213 nets have [3 - 5] pins
RUN-1001 : 800 nets have [6 - 10] pins
RUN-1001 : 245 nets have [11 - 20] pins
RUN-1001 : 102 nets have [21 - 99] pins
RUN-1001 : 22 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4751     
RUN-1001 :   No   |  No   |  Yes  |     667     
RUN-1001 :   No   |  Yes  |  No   |     92      
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     500     
RUN-1001 :   Yes  |  Yes  |  No   |     51      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  113  |     12     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 121
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19145 instances, 5456 luts, 12144 seqs, 1439 slices, 286 macros(1439 instances: 943 mslices 496 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 79915, tnet num: 21544, tinst num: 19145, tnode num: 112709, tedge num: 125767.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.118051s wall, 1.093750s user + 0.031250s system = 1.125000s CPU (100.6%)

RUN-1004 : used memory is 523 MB, reserved memory is 495 MB, peak memory is 572 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21544 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.923033s wall, 1.859375s user + 0.062500s system = 1.921875s CPU (99.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.68506e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19145.
PHY-3001 : Level 1 #clusters 2120.
PHY-3001 : End clustering;  0.141406s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (165.7%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 877905, overlap = 645.031
PHY-3002 : Step(2): len = 803707, overlap = 693.969
PHY-3002 : Step(3): len = 520053, overlap = 896.812
PHY-3002 : Step(4): len = 450896, overlap = 946.094
PHY-3002 : Step(5): len = 367608, overlap = 1039.41
PHY-3002 : Step(6): len = 331520, overlap = 1098.94
PHY-3002 : Step(7): len = 276791, overlap = 1171.25
PHY-3002 : Step(8): len = 245102, overlap = 1212.22
PHY-3002 : Step(9): len = 213773, overlap = 1261.62
PHY-3002 : Step(10): len = 197782, overlap = 1311.12
PHY-3002 : Step(11): len = 176687, overlap = 1341.03
PHY-3002 : Step(12): len = 164774, overlap = 1354.66
PHY-3002 : Step(13): len = 149469, overlap = 1375.41
PHY-3002 : Step(14): len = 139987, overlap = 1395
PHY-3002 : Step(15): len = 128473, overlap = 1430.22
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.51093e-07
PHY-3002 : Step(16): len = 132175, overlap = 1420.25
PHY-3002 : Step(17): len = 167379, overlap = 1341.38
PHY-3002 : Step(18): len = 177431, overlap = 1262.09
PHY-3002 : Step(19): len = 178719, overlap = 1204.25
PHY-3002 : Step(20): len = 179345, overlap = 1156.28
PHY-3002 : Step(21): len = 176818, overlap = 1149.34
PHY-3002 : Step(22): len = 172742, overlap = 1158.78
PHY-3002 : Step(23): len = 168087, overlap = 1136.06
PHY-3002 : Step(24): len = 165300, overlap = 1127.94
PHY-3002 : Step(25): len = 162860, overlap = 1142.56
PHY-3002 : Step(26): len = 160692, overlap = 1147.44
PHY-3002 : Step(27): len = 158929, overlap = 1147.94
PHY-3002 : Step(28): len = 158131, overlap = 1131.16
PHY-3002 : Step(29): len = 157655, overlap = 1136.59
PHY-3002 : Step(30): len = 156663, overlap = 1132.16
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 1.90219e-06
PHY-3002 : Step(31): len = 163375, overlap = 1098.34
PHY-3002 : Step(32): len = 177397, overlap = 990.625
PHY-3002 : Step(33): len = 182157, overlap = 943.906
PHY-3002 : Step(34): len = 184639, overlap = 934.75
PHY-3002 : Step(35): len = 184800, overlap = 947.062
PHY-3002 : Step(36): len = 184189, overlap = 931.219
PHY-3002 : Step(37): len = 183128, overlap = 936.25
PHY-3002 : Step(38): len = 181924, overlap = 957.812
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 3.80437e-06
PHY-3002 : Step(39): len = 192032, overlap = 907.594
PHY-3002 : Step(40): len = 204411, overlap = 831.656
PHY-3002 : Step(41): len = 209239, overlap = 800.875
PHY-3002 : Step(42): len = 211884, overlap = 781.875
PHY-3002 : Step(43): len = 211143, overlap = 796.906
PHY-3002 : Step(44): len = 210494, overlap = 794.781
PHY-3002 : Step(45): len = 209179, overlap = 796.094
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 7.60875e-06
PHY-3002 : Step(46): len = 220707, overlap = 733.781
PHY-3002 : Step(47): len = 234369, overlap = 686.719
PHY-3002 : Step(48): len = 240343, overlap = 658.781
PHY-3002 : Step(49): len = 243195, overlap = 624.312
PHY-3002 : Step(50): len = 243481, overlap = 596.344
PHY-3002 : Step(51): len = 243016, overlap = 589.875
PHY-3002 : Step(52): len = 241841, overlap = 586
PHY-3002 : Step(53): len = 239872, overlap = 586.531
PHY-3002 : Step(54): len = 238772, overlap = 586.344
PHY-3002 : Step(55): len = 237865, overlap = 579.531
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.52175e-05
PHY-3002 : Step(56): len = 248935, overlap = 545.469
PHY-3002 : Step(57): len = 264420, overlap = 477.031
PHY-3002 : Step(58): len = 269176, overlap = 400.25
PHY-3002 : Step(59): len = 270474, overlap = 382.844
PHY-3002 : Step(60): len = 269280, overlap = 381.375
PHY-3002 : Step(61): len = 268046, overlap = 381.25
PHY-3002 : Step(62): len = 265680, overlap = 376.125
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.0435e-05
PHY-3002 : Step(63): len = 275846, overlap = 381.5
PHY-3002 : Step(64): len = 286824, overlap = 373.406
PHY-3002 : Step(65): len = 290377, overlap = 359.688
PHY-3002 : Step(66): len = 291445, overlap = 359.844
PHY-3002 : Step(67): len = 289424, overlap = 374.062
PHY-3002 : Step(68): len = 287252, overlap = 388.875
PHY-3002 : Step(69): len = 285654, overlap = 384.312
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 6.087e-05
PHY-3002 : Step(70): len = 293165, overlap = 371.844
PHY-3002 : Step(71): len = 301792, overlap = 355.656
PHY-3002 : Step(72): len = 305978, overlap = 338.219
PHY-3002 : Step(73): len = 307715, overlap = 323.375
PHY-3002 : Step(74): len = 306308, overlap = 311.438
PHY-3002 : Step(75): len = 304885, overlap = 321.5
PHY-3002 : Step(76): len = 302735, overlap = 323.406
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000121678
PHY-3002 : Step(77): len = 307314, overlap = 310.719
PHY-3002 : Step(78): len = 312626, overlap = 276.125
PHY-3002 : Step(79): len = 315929, overlap = 270.75
PHY-3002 : Step(80): len = 317727, overlap = 254.031
PHY-3002 : Step(81): len = 317622, overlap = 260.406
PHY-3002 : Step(82): len = 316639, overlap = 253.688
PHY-3002 : Step(83): len = 314995, overlap = 259.719
PHY-3002 : Step(84): len = 315667, overlap = 262.125
PHY-3002 : Step(85): len = 315146, overlap = 257.938
PHY-3002 : Step(86): len = 315909, overlap = 253.656
PHY-3002 : Step(87): len = 314231, overlap = 262.312
PHY-3002 : Step(88): len = 314089, overlap = 265.781
PHY-3002 : Step(89): len = 313637, overlap = 264.688
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000239801
PHY-3002 : Step(90): len = 316983, overlap = 265.906
PHY-3002 : Step(91): len = 321892, overlap = 260.75
PHY-3002 : Step(92): len = 324322, overlap = 252.5
PHY-3002 : Step(93): len = 326384, overlap = 266.75
PHY-3002 : Step(94): len = 326611, overlap = 262
PHY-3002 : Step(95): len = 326439, overlap = 249.594
PHY-3002 : Step(96): len = 326984, overlap = 238.312
PHY-3002 : Step(97): len = 325702, overlap = 238.344
PHY-3002 : Step(98): len = 326244, overlap = 232.031
PHY-3002 : Step(99): len = 326183, overlap = 235.719
PHY-3002 : Step(100): len = 326230, overlap = 250.031
PHY-3002 : Step(101): len = 325255, overlap = 259.906
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(102): len = 326365, overlap = 258.688
PHY-3002 : Step(103): len = 330022, overlap = 244.812
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.017735s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (88.1%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21546.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 434112, over cnt = 1212(3%), over = 5348, worst = 33
PHY-1001 : End global iterations;  0.864130s wall, 1.218750s user + 0.015625s system = 1.234375s CPU (142.8%)

PHY-1001 : Congestion index: top1 = 73.12, top5 = 53.51, top10 = 43.96, top15 = 38.00.
PHY-3001 : End congestion estimation;  1.079920s wall, 1.421875s user + 0.031250s system = 1.453125s CPU (134.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21544 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.842007s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000100348
PHY-3002 : Step(104): len = 373299, overlap = 187
PHY-3002 : Step(105): len = 383924, overlap = 165.312
PHY-3002 : Step(106): len = 390661, overlap = 146.875
PHY-3002 : Step(107): len = 390169, overlap = 132.188
PHY-3002 : Step(108): len = 394795, overlap = 120.156
PHY-3002 : Step(109): len = 404137, overlap = 115.125
PHY-3002 : Step(110): len = 404967, overlap = 106.5
PHY-3002 : Step(111): len = 407965, overlap = 109.562
PHY-3002 : Step(112): len = 410467, overlap = 112.594
PHY-3002 : Step(113): len = 411781, overlap = 107.438
PHY-3002 : Step(114): len = 413922, overlap = 111.406
PHY-3002 : Step(115): len = 414257, overlap = 113.75
PHY-3002 : Step(116): len = 416074, overlap = 111.438
PHY-3002 : Step(117): len = 416766, overlap = 109.844
PHY-3002 : Step(118): len = 416012, overlap = 112.156
PHY-3002 : Step(119): len = 416489, overlap = 115.094
PHY-3002 : Step(120): len = 417082, overlap = 110.875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000200696
PHY-3002 : Step(121): len = 417317, overlap = 110.031
PHY-3002 : Step(122): len = 419341, overlap = 110.094
PHY-3002 : Step(123): len = 420196, overlap = 107.938
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000401392
PHY-3002 : Step(124): len = 428034, overlap = 107.531
PHY-3002 : Step(125): len = 436951, overlap = 112.938
PHY-3002 : Step(126): len = 437150, overlap = 114.594
PHY-3002 : Step(127): len = 439047, overlap = 116
PHY-3002 : Step(128): len = 441736, overlap = 115.781
PHY-3002 : Step(129): len = 442340, overlap = 107.156
PHY-3002 : Step(130): len = 442564, overlap = 106.875
PHY-3002 : Step(131): len = 443901, overlap = 100.781
PHY-3002 : Step(132): len = 445878, overlap = 98.7188
PHY-3002 : Step(133): len = 445091, overlap = 100.844
PHY-3002 : Step(134): len = 444732, overlap = 103.031
PHY-3002 : Step(135): len = 445094, overlap = 102.625
PHY-3002 : Step(136): len = 445347, overlap = 98.4062
PHY-3002 : Step(137): len = 446313, overlap = 104.25
PHY-3002 : Step(138): len = 447668, overlap = 101.344
PHY-3002 : Step(139): len = 448593, overlap = 101.375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(140): len = 448159, overlap = 101.531
PHY-3002 : Step(141): len = 450717, overlap = 100.281
PHY-3002 : Step(142): len = 453712, overlap = 103.625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 78/21546.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 525232, over cnt = 2196(6%), over = 10394, worst = 50
PHY-1001 : End global iterations;  1.009440s wall, 1.546875s user + 0.015625s system = 1.562500s CPU (154.8%)

PHY-1001 : Congestion index: top1 = 79.98, top5 = 60.68, top10 = 51.55, top15 = 46.17.
PHY-3001 : End congestion estimation;  1.247867s wall, 1.781250s user + 0.015625s system = 1.796875s CPU (144.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21544 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.864186s wall, 0.859375s user + 0.015625s system = 0.875000s CPU (101.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.23255e-05
PHY-3002 : Step(143): len = 461355, overlap = 373
PHY-3002 : Step(144): len = 466884, overlap = 328.469
PHY-3002 : Step(145): len = 464046, overlap = 287.75
PHY-3002 : Step(146): len = 461837, overlap = 260.188
PHY-3002 : Step(147): len = 456382, overlap = 244.594
PHY-3002 : Step(148): len = 452544, overlap = 233.781
PHY-3002 : Step(149): len = 450229, overlap = 226.719
PHY-3002 : Step(150): len = 448999, overlap = 221.844
PHY-3002 : Step(151): len = 447391, overlap = 218.156
PHY-3002 : Step(152): len = 445694, overlap = 218.875
PHY-3002 : Step(153): len = 444230, overlap = 227
PHY-3002 : Step(154): len = 441484, overlap = 227.594
PHY-3002 : Step(155): len = 439930, overlap = 222.906
PHY-3002 : Step(156): len = 437423, overlap = 231.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000184651
PHY-3002 : Step(157): len = 437981, overlap = 212.625
PHY-3002 : Step(158): len = 439419, overlap = 203.625
PHY-3002 : Step(159): len = 440079, overlap = 197.656
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000369302
PHY-3002 : Step(160): len = 442548, overlap = 184.625
PHY-3002 : Step(161): len = 448666, overlap = 175.875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000738604
PHY-3002 : Step(162): len = 450315, overlap = 171.062
PHY-3002 : Step(163): len = 456073, overlap = 160.625
PHY-3002 : Step(164): len = 461694, overlap = 148.25
PHY-3002 : Step(165): len = 462883, overlap = 145.781
PHY-3002 : Step(166): len = 462127, overlap = 146.812
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 79915, tnet num: 21544, tinst num: 19145, tnode num: 112709, tedge num: 125767.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.421166s wall, 1.390625s user + 0.031250s system = 1.421875s CPU (100.0%)

RUN-1004 : used memory is 563 MB, reserved memory is 537 MB, peak memory is 693 MB
OPT-1001 : Total overflow 513.41 peak overflow 4.84
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 338/21546.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 544968, over cnt = 2456(6%), over = 8610, worst = 22
PHY-1001 : End global iterations;  1.138491s wall, 1.875000s user + 0.078125s system = 1.953125s CPU (171.6%)

PHY-1001 : Congestion index: top1 = 60.65, top5 = 49.42, top10 = 43.80, top15 = 40.35.
PHY-1001 : End incremental global routing;  1.356264s wall, 2.093750s user + 0.078125s system = 2.171875s CPU (160.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21544 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.887894s wall, 0.875000s user + 0.015625s system = 0.890625s CPU (100.3%)

OPT-1001 : 16 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19066 has valid locations, 240 needs to be replaced
PHY-3001 : design contains 19369 instances, 5539 luts, 12285 seqs, 1439 slices, 286 macros(1439 instances: 943 mslices 496 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 477638
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16991/21770.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 556560, over cnt = 2487(7%), over = 8680, worst = 22
PHY-1001 : End global iterations;  0.170271s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (128.5%)

PHY-1001 : Congestion index: top1 = 60.54, top5 = 49.80, top10 = 44.11, top15 = 40.72.
PHY-3001 : End congestion estimation;  0.387822s wall, 0.437500s user + 0.015625s system = 0.453125s CPU (116.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80647, tnet num: 21768, tinst num: 19369, tnode num: 113777, tedge num: 126783.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.435254s wall, 1.406250s user + 0.031250s system = 1.437500s CPU (100.2%)

RUN-1004 : used memory is 608 MB, reserved memory is 603 MB, peak memory is 697 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21768 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.360260s wall, 2.312500s user + 0.046875s system = 2.359375s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(167): len = 477558, overlap = 2.71875
PHY-3002 : Step(168): len = 478846, overlap = 2.59375
PHY-3002 : Step(169): len = 480076, overlap = 2.65625
PHY-3002 : Step(170): len = 481395, overlap = 2.59375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17005/21770.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 557160, over cnt = 2506(7%), over = 8719, worst = 22
PHY-1001 : End global iterations;  0.169825s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (110.4%)

PHY-1001 : Congestion index: top1 = 61.10, top5 = 49.99, top10 = 44.31, top15 = 40.87.
PHY-3001 : End congestion estimation;  0.395972s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (102.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21768 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.889321s wall, 0.875000s user + 0.015625s system = 0.890625s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000520485
PHY-3002 : Step(171): len = 481356, overlap = 150.625
PHY-3002 : Step(172): len = 481524, overlap = 150
PHY-3001 : Final: Len = 481524, Over = 150
PHY-3001 : End incremental placement;  4.792727s wall, 4.781250s user + 0.203125s system = 4.984375s CPU (104.0%)

OPT-1001 : Total overflow 518.19 peak overflow 4.84
OPT-1001 : End high-fanout net optimization;  7.498856s wall, 8.375000s user + 0.296875s system = 8.671875s CPU (115.6%)

OPT-1001 : Current memory(MB): used = 700, reserve = 679, peak = 716.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17044/21770.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 558072, over cnt = 2463(6%), over = 8293, worst = 22
PHY-1002 : len = 605464, over cnt = 1606(4%), over = 3690, worst = 19
PHY-1002 : len = 636264, over cnt = 746(2%), over = 1437, worst = 19
PHY-1002 : len = 648912, over cnt = 339(0%), over = 587, worst = 8
PHY-1002 : len = 658840, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.190104s wall, 1.781250s user + 0.000000s system = 1.781250s CPU (149.7%)

PHY-1001 : Congestion index: top1 = 50.71, top5 = 44.37, top10 = 41.10, top15 = 38.89.
OPT-1001 : End congestion update;  1.415862s wall, 2.000000s user + 0.000000s system = 2.000000s CPU (141.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 21768 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.813003s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (101.9%)

OPT-0007 : Start: WNS 3819 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.234041s wall, 2.828125s user + 0.000000s system = 2.828125s CPU (126.6%)

OPT-1001 : Current memory(MB): used = 675, reserve = 657, peak = 716.
OPT-1001 : End physical optimization;  11.460565s wall, 13.031250s user + 0.343750s system = 13.375000s CPU (116.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5539 LUT to BLE ...
SYN-4008 : Packed 5539 LUT and 2635 SEQ to BLE.
SYN-4003 : Packing 9650 remaining SEQ's ...
SYN-4005 : Packed 3307 SEQ with LUT/SLICE
SYN-4006 : 108 single LUT's are left
SYN-4006 : 6343 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11882/13601 primitive instances ...
PHY-3001 : End packing;  2.653443s wall, 2.656250s user + 0.000000s system = 2.656250s CPU (100.1%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8016 instances
RUN-1001 : 3954 mslices, 3954 lslices, 60 pads, 43 brams, 0 dsps
RUN-1001 : There are total 19185 nets
RUN-1001 : 13453 nets have 2 pins
RUN-1001 : 4351 nets have [3 - 5] pins
RUN-1001 : 867 nets have [6 - 10] pins
RUN-1001 : 376 nets have [11 - 20] pins
RUN-1001 : 128 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
PHY-3001 : design contains 8014 instances, 7908 slices, 286 macros(1439 instances: 943 mslices 496 lslices)
PHY-3001 : Cell area utilization is 84%
PHY-3001 : After packing: Len = 497521, Over = 356.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7752/19185.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 625936, over cnt = 1580(4%), over = 2534, worst = 8
PHY-1002 : len = 631904, over cnt = 1013(2%), over = 1431, worst = 8
PHY-1002 : len = 644064, over cnt = 397(1%), over = 507, worst = 7
PHY-1002 : len = 650368, over cnt = 97(0%), over = 129, worst = 7
PHY-1002 : len = 652760, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.172467s wall, 1.734375s user + 0.015625s system = 1.750000s CPU (149.3%)

PHY-1001 : Congestion index: top1 = 52.46, top5 = 44.42, top10 = 40.74, top15 = 38.45.
PHY-3001 : End congestion estimation;  1.462787s wall, 2.015625s user + 0.031250s system = 2.046875s CPU (139.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66850, tnet num: 19183, tinst num: 8014, tnode num: 90895, tedge num: 110182.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.621243s wall, 1.609375s user + 0.000000s system = 1.609375s CPU (99.3%)

RUN-1004 : used memory is 601 MB, reserved memory is 586 MB, peak memory is 716 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19183 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.430699s wall, 2.406250s user + 0.015625s system = 2.421875s CPU (99.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.63147e-05
PHY-3002 : Step(173): len = 498214, overlap = 338.75
PHY-3002 : Step(174): len = 496606, overlap = 355.5
PHY-3002 : Step(175): len = 496822, overlap = 349.5
PHY-3002 : Step(176): len = 497273, overlap = 380.5
PHY-3002 : Step(177): len = 495699, overlap = 383.75
PHY-3002 : Step(178): len = 494882, overlap = 379.25
PHY-3002 : Step(179): len = 492018, overlap = 389.75
PHY-3002 : Step(180): len = 489556, overlap = 395.5
PHY-3002 : Step(181): len = 487627, overlap = 400.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.26294e-05
PHY-3002 : Step(182): len = 491642, overlap = 390.5
PHY-3002 : Step(183): len = 496203, overlap = 372.75
PHY-3002 : Step(184): len = 496820, overlap = 367.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000185259
PHY-3002 : Step(185): len = 502758, overlap = 354.25
PHY-3002 : Step(186): len = 511996, overlap = 330.75
PHY-3002 : Step(187): len = 511966, overlap = 335
PHY-3002 : Step(188): len = 511705, overlap = 337.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.605624s wall, 0.703125s user + 0.656250s system = 1.359375s CPU (224.5%)

PHY-3001 : Trial Legalized: Len = 620022
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 643/19185.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 710944, over cnt = 2248(6%), over = 3726, worst = 8
PHY-1002 : len = 723800, over cnt = 1426(4%), over = 2079, worst = 7
PHY-1002 : len = 742528, over cnt = 537(1%), over = 702, worst = 5
PHY-1002 : len = 752616, over cnt = 111(0%), over = 137, worst = 4
PHY-1002 : len = 754976, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.747874s wall, 2.765625s user + 0.031250s system = 2.796875s CPU (160.0%)

PHY-1001 : Congestion index: top1 = 51.10, top5 = 45.14, top10 = 42.25, top15 = 40.31.
PHY-3001 : End congestion estimation;  2.067965s wall, 3.078125s user + 0.031250s system = 3.109375s CPU (150.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19183 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.799269s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000173641
PHY-3002 : Step(189): len = 578614, overlap = 74.25
PHY-3002 : Step(190): len = 560393, overlap = 105.25
PHY-3002 : Step(191): len = 548302, overlap = 160.25
PHY-3002 : Step(192): len = 541773, overlap = 202.25
PHY-3002 : Step(193): len = 537927, overlap = 223.25
PHY-3002 : Step(194): len = 536165, overlap = 243
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000347282
PHY-3002 : Step(195): len = 541421, overlap = 236.75
PHY-3002 : Step(196): len = 546262, overlap = 231.75
PHY-3002 : Step(197): len = 547526, overlap = 229.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(198): len = 550924, overlap = 228.25
PHY-3002 : Step(199): len = 556969, overlap = 229.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.029029s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (53.8%)

PHY-3001 : Legalized: Len = 597114, Over = 0
PHY-3001 : Spreading special nets. 39 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.071889s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (108.7%)

PHY-3001 : 56 instances has been re-located, deltaX = 12, deltaY = 34, maxDist = 2.
PHY-3001 : Final: Len = 597896, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66850, tnet num: 19183, tinst num: 8014, tnode num: 90895, tedge num: 110182.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.842958s wall, 1.843750s user + 0.000000s system = 1.843750s CPU (100.0%)

RUN-1004 : used memory is 616 MB, reserved memory is 612 MB, peak memory is 716 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3977/19185.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 697712, over cnt = 2124(6%), over = 3290, worst = 8
PHY-1002 : len = 709600, over cnt = 1191(3%), over = 1582, worst = 7
PHY-1002 : len = 720176, over cnt = 559(1%), over = 742, worst = 5
PHY-1002 : len = 729488, over cnt = 126(0%), over = 174, worst = 4
PHY-1002 : len = 732208, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.501360s wall, 2.531250s user + 0.046875s system = 2.578125s CPU (171.7%)

PHY-1001 : Congestion index: top1 = 49.57, top5 = 43.35, top10 = 40.44, top15 = 38.79.
PHY-1001 : End incremental global routing;  1.789149s wall, 2.828125s user + 0.046875s system = 2.875000s CPU (160.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19183 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.813076s wall, 0.781250s user + 0.031250s system = 0.812500s CPU (99.9%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 7950 has valid locations, 10 needs to be replaced
PHY-3001 : design contains 8023 instances, 7917 slices, 286 macros(1439 instances: 943 mslices 496 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 600817
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17333/19202.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 736272, over cnt = 24(0%), over = 36, worst = 5
PHY-1002 : len = 736328, over cnt = 21(0%), over = 21, worst = 1
PHY-1002 : len = 736416, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 736536, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 736552, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.665723s wall, 0.671875s user + 0.015625s system = 0.687500s CPU (103.3%)

PHY-1001 : Congestion index: top1 = 49.68, top5 = 43.50, top10 = 40.56, top15 = 38.88.
PHY-3001 : End congestion estimation;  0.935706s wall, 0.937500s user + 0.015625s system = 0.953125s CPU (101.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66930, tnet num: 19200, tinst num: 8023, tnode num: 90993, tedge num: 110306.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.841881s wall, 1.828125s user + 0.015625s system = 1.843750s CPU (100.1%)

RUN-1004 : used memory is 640 MB, reserved memory is 629 MB, peak memory is 716 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19200 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.675346s wall, 2.625000s user + 0.046875s system = 2.671875s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(200): len = 600493, overlap = 0
PHY-3002 : Step(201): len = 600370, overlap = 0.25
PHY-3002 : Step(202): len = 600194, overlap = 0
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17332/19202.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 734232, over cnt = 26(0%), over = 40, worst = 5
PHY-1002 : len = 734160, over cnt = 15(0%), over = 19, worst = 5
PHY-1002 : len = 734240, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 734256, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 734384, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.668589s wall, 0.687500s user + 0.062500s system = 0.750000s CPU (112.2%)

PHY-1001 : Congestion index: top1 = 49.48, top5 = 43.37, top10 = 40.48, top15 = 38.82.
PHY-3001 : End congestion estimation;  0.941490s wall, 0.953125s user + 0.062500s system = 1.015625s CPU (107.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19200 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.812805s wall, 0.796875s user + 0.015625s system = 0.812500s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000890095
PHY-3002 : Step(203): len = 600202, overlap = 0.75
PHY-3002 : Step(204): len = 600183, overlap = 0.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007930s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (197.0%)

PHY-3001 : Legalized: Len = 600258, Over = 0
PHY-3001 : End spreading;  0.060603s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.1%)

PHY-3001 : Final: Len = 600258, Over = 0
PHY-3001 : End incremental placement;  6.036336s wall, 5.921875s user + 0.265625s system = 6.187500s CPU (102.5%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  9.064215s wall, 9.937500s user + 0.343750s system = 10.281250s CPU (113.4%)

OPT-1001 : Current memory(MB): used = 712, reserve = 696, peak = 718.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17331/19202.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 734648, over cnt = 11(0%), over = 14, worst = 3
PHY-1002 : len = 734664, over cnt = 10(0%), over = 10, worst = 1
PHY-1002 : len = 734760, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 734760, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 734776, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.651413s wall, 0.656250s user + 0.000000s system = 0.656250s CPU (100.7%)

PHY-1001 : Congestion index: top1 = 49.46, top5 = 43.36, top10 = 40.47, top15 = 38.81.
OPT-1001 : End congestion update;  0.925496s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (101.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19200 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.696175s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (96.5%)

OPT-0007 : Start: WNS 3790 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.625933s wall, 1.609375s user + 0.000000s system = 1.609375s CPU (99.0%)

OPT-1001 : Current memory(MB): used = 712, reserve = 696, peak = 718.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19200 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.690943s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (101.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17342/19202.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 734776, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.113259s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (96.6%)

PHY-1001 : Congestion index: top1 = 49.46, top5 = 43.36, top10 = 40.47, top15 = 38.81.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19200 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.738123s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (99.5%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3790 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 49.137931
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3790ps with logic level 4 
OPT-1001 : End physical optimization;  14.602669s wall, 15.453125s user + 0.343750s system = 15.796875s CPU (108.2%)

RUN-1003 : finish command "place" in  66.739550s wall, 107.625000s user + 6.703125s system = 114.328125s CPU (171.3%)

RUN-1004 : used memory is 594 MB, reserved memory is 581 MB, peak memory is 718 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.540283s wall, 2.671875s user + 0.000000s system = 2.671875s CPU (173.5%)

RUN-1004 : used memory is 594 MB, reserved memory is 584 MB, peak memory is 718 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8025 instances
RUN-1001 : 3954 mslices, 3963 lslices, 60 pads, 43 brams, 0 dsps
RUN-1001 : There are total 19202 nets
RUN-1001 : 13461 nets have 2 pins
RUN-1001 : 4353 nets have [3 - 5] pins
RUN-1001 : 866 nets have [6 - 10] pins
RUN-1001 : 385 nets have [11 - 20] pins
RUN-1001 : 127 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66930, tnet num: 19200, tinst num: 8023, tnode num: 90993, tedge num: 110306.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.609516s wall, 1.609375s user + 0.000000s system = 1.609375s CPU (100.0%)

RUN-1004 : used memory is 586 MB, reserved memory is 569 MB, peak memory is 718 MB
PHY-1001 : 3954 mslices, 3963 lslices, 60 pads, 43 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19200 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 676424, over cnt = 2218(6%), over = 3723, worst = 8
PHY-1002 : len = 693176, over cnt = 1300(3%), over = 1773, worst = 7
PHY-1002 : len = 706472, over cnt = 570(1%), over = 777, worst = 5
PHY-1002 : len = 718376, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 718664, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.600118s wall, 2.671875s user + 0.015625s system = 2.687500s CPU (168.0%)

PHY-1001 : Congestion index: top1 = 48.38, top5 = 43.06, top10 = 40.26, top15 = 38.52.
PHY-1001 : End global routing;  1.908146s wall, 2.984375s user + 0.015625s system = 3.000000s CPU (157.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 696, reserve = 685, peak = 718.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 964, reserve = 952, peak = 964.
PHY-1001 : End build detailed router design. 4.345728s wall, 4.312500s user + 0.031250s system = 4.343750s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 192864, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.807080s wall, 0.796875s user + 0.015625s system = 0.812500s CPU (100.7%)

PHY-1001 : Current memory(MB): used = 1000, reserve = 989, peak = 1000.
PHY-1001 : End phase 1; 0.814968s wall, 0.796875s user + 0.015625s system = 0.812500s CPU (99.7%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.77646e+06, over cnt = 1213(0%), over = 1218, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1016, reserve = 1004, peak = 1016.
PHY-1001 : End initial routed; 18.903734s wall, 47.234375s user + 0.234375s system = 47.468750s CPU (251.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17987(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.954   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.151955s wall, 3.140625s user + 0.015625s system = 3.156250s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1030, reserve = 1019, peak = 1030.
PHY-1001 : End phase 2; 22.055821s wall, 50.375000s user + 0.250000s system = 50.625000s CPU (229.5%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.77646e+06, over cnt = 1213(0%), over = 1218, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.221443s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (98.8%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.76401e+06, over cnt = 391(0%), over = 391, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.787213s wall, 1.281250s user + 0.000000s system = 1.281250s CPU (162.8%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.76462e+06, over cnt = 115(0%), over = 115, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.296453s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (137.0%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.76581e+06, over cnt = 14(0%), over = 14, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.221185s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (106.0%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.7661e+06, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.176349s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (106.3%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.76613e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.155969s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (100.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17987(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.954   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.164084s wall, 3.171875s user + 0.000000s system = 3.171875s CPU (100.2%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 306 feed throughs used by 264 nets
PHY-1001 : End commit to database; 2.104136s wall, 2.093750s user + 0.015625s system = 2.109375s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 1115, reserve = 1107, peak = 1115.
PHY-1001 : End phase 3; 7.590269s wall, 8.203125s user + 0.015625s system = 8.218750s CPU (108.3%)

PHY-1003 : Routed, final wirelength = 1.76613e+06
PHY-1001 : Current memory(MB): used = 1119, reserve = 1111, peak = 1119.
PHY-1001 : End export database. 0.057584s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.5%)

PHY-1001 : End detail routing;  35.252260s wall, 64.125000s user + 0.312500s system = 64.437500s CPU (182.8%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66930, tnet num: 19200, tinst num: 8023, tnode num: 90993, tedge num: 110306.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.587470s wall, 1.593750s user + 0.000000s system = 1.593750s CPU (100.4%)

RUN-1004 : used memory is 1051 MB, reserved memory is 1047 MB, peak memory is 1120 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  42.717387s wall, 72.640625s user + 0.359375s system = 73.000000s CPU (170.9%)

RUN-1004 : used memory is 1050 MB, reserved memory is 1047 MB, peak memory is 1120 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8562   out of  19600   43.68%
#reg                    12391   out of  19600   63.22%
#le                     14863
  #lut only              2472   out of  14863   16.63%
  #reg only              6301   out of  14863   42.39%
  #lut&reg               6090   out of  14863   40.97%
#dsp                        0   out of     29    0.00%
#bram                      43   out of     64   67.19%
  #bram9k                  42
  #fifo9k                   1
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6734
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          185
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         M3        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         P6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |14863  |7123    |1439    |12435   |43      |0       |
|  AnyFog_dataX                      |AnyFog          |206    |90      |22      |172     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |84     |57      |22      |50      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |206    |73      |22      |170     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |60      |22      |50      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |209    |89      |22      |175     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |60      |22      |52      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2865   |581     |34      |2783    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |218    |69      |5       |203     |0       |0       |
|    STADOP_com2                     |STADOP          |564    |155     |0       |552     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |68     |48      |14      |43      |0       |0       |
|    head_com2                       |uniheading      |278    |85      |5       |262     |0       |0       |
|    uart_com2                       |Agrica          |1435   |206     |10      |1421    |0       |0       |
|  COM3                              |COM3_Control    |278    |142     |19      |236     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |61     |35      |5       |52      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |61     |38      |14      |37      |0       |0       |
|    rmc_com3                        |Gprmc           |156    |69      |0       |147     |0       |0       |
|  DATA                              |Data_Processing |8697   |4361    |1062    |7016    |0       |0       |
|    DIV_Dtemp                       |Divider         |799    |281     |84      |676     |0       |0       |
|    DIV_Utemp                       |Divider         |647    |305     |84      |520     |0       |0       |
|    DIV_accX                        |Divider         |610    |320     |84      |485     |0       |0       |
|    DIV_accY                        |Divider         |570    |327     |111     |400     |0       |0       |
|    DIV_accZ                        |Divider         |689    |357     |132     |484     |0       |0       |
|    DIV_rateX                       |Divider         |672    |422     |132     |467     |0       |0       |
|    DIV_rateY                       |Divider         |611    |373     |132     |405     |0       |0       |
|    DIV_rateZ                       |Divider         |575    |370     |132     |370     |0       |0       |
|    genclk                          |genclk          |84     |56      |20      |50      |0       |0       |
|  FMC                               |FMC_Ctrl        |425    |370     |43      |333     |0       |0       |
|  IIC                               |I2C_master      |277    |246     |11      |250     |0       |0       |
|  IMU_CTRL                          |SCHA634         |912    |689     |61      |725     |0       |0       |
|    CtrlData                        |CtrlData        |467    |414     |47      |327     |0       |0       |
|      usms                          |Time_1ms        |31     |26      |5       |18      |0       |0       |
|    SPIM                            |SPI_SCHA634     |445    |275     |14      |398     |0       |0       |
|  POWER                             |POWER_EN        |98     |49      |38      |38      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |677    |431     |105     |480     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |677    |431     |105     |480     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |324    |196     |0       |307     |0       |0       |
|        reg_inst                    |register        |322    |194     |0       |305     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |353    |235     |105     |173     |0       |0       |
|        bus_inst                    |bus_top         |139    |91      |48      |55      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |28     |18      |10      |9       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |53     |35      |18      |20      |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[5]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[6]$bus_nodes |bus_det         |23     |13      |10      |7       |0       |0       |
|          BUS_DETECTOR[7]$bus_nodes |bus_det         |29     |19      |10      |13      |0       |0       |
|          BUS_DETECTOR[8]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[9]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |132    |101     |29      |84      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13400  
    #2          2       3411   
    #3          3        674   
    #4          4        268   
    #5        5-10       940   
    #6        11-50      433   
    #7       51-100       5    
    #8       101-500      4    
    #9        >500        2    
  Average     2.13             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  1.899555s wall, 3.265625s user + 0.015625s system = 3.281250s CPU (172.7%)

RUN-1004 : used memory is 1051 MB, reserved memory is 1049 MB, peak memory is 1120 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66930, tnet num: 19200, tinst num: 8023, tnode num: 90993, tedge num: 110306.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.609446s wall, 1.578125s user + 0.000000s system = 1.578125s CPU (98.1%)

RUN-1004 : used memory is 1053 MB, reserved memory is 1050 MB, peak memory is 1120 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19200 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.238154s wall, 1.218750s user + 0.015625s system = 1.234375s CPU (99.7%)

RUN-1004 : used memory is 1058 MB, reserved memory is 1055 MB, peak memory is 1120 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 8f169df8acfcb835781910b1ac29267cd80e16d8a5e1b8cf9d032ec93c0a2865 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8023
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19202, pip num: 146402
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 306
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3260 valid insts, and 407815 bits set as '1'.
BIT-1004 : the usercode register value: 00000000110101000101101001110001
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  10.167574s wall, 99.046875s user + 0.109375s system = 99.156250s CPU (975.2%)

RUN-1004 : used memory is 1180 MB, reserved memory is 1164 MB, peak memory is 1295 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250710_201814.log"

============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Tue Jul  1 18:33:58 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(519)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
RUN-1001 : Project manager successfully analyzed 22 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.215060s wall, 1.703125s user + 3.500000s system = 5.203125s CPU (99.8%)

RUN-1004 : used memory is 79 MB, reserved memory is 40 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.749246s wall, 1.718750s user + 0.031250s system = 1.750000s CPU (100.0%)

RUN-1004 : used memory is 302 MB, reserved memory is 269 MB, peak memory is 305 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 4 view nodes, 26 trigger nets, 26 data nets.
KIT-1004 : Chipwatcher code = 0110100010110101
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=4,BUS_DIN_NUM=26,BUS_CTRL_NUM=68,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=90) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=90) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=4,BUS_DIN_NUM=26,BUS_CTRL_NUM=68,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=4,BUS_DIN_NUM=26,BUS_CTRL_NUM=68,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=4,BUS_DIN_NUM=26,BUS_CTRL_NUM=68,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=90)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=90)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=4,BUS_DIN_NUM=26,BUS_CTRL_NUM=68,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=4,BUS_DIN_NUM=26,BUS_CTRL_NUM=68,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22667/18 useful/useless nets, 19675/10 useful/useless insts
SYN-1016 : Merged 25 instances.
SYN-1032 : 22411/20 useful/useless nets, 20017/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 2 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 2 mux instances.
SYN-1015 : Optimize round 1, 307 better
SYN-1014 : Optimize round 2
SYN-1032 : 22180/30 useful/useless nets, 19786/32 useful/useless insts
SYN-1015 : Optimize round 2, 64 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.057221s wall, 2.046875s user + 0.000000s system = 2.046875s CPU (99.5%)

RUN-1004 : used memory is 327 MB, reserved memory is 294 MB, peak memory is 329 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22204/157 useful/useless nets, 19833/31 useful/useless insts
SYN-1016 : Merged 21 instances.
SYN-2571 : Optimize after map_dsp, round 1, 209 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 24 instances.
SYN-2501 : Optimize round 1, 50 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 14 macro adder
SYN-1019 : Optimized 9 mux instances.
SYN-1016 : Merged 10 instances.
SYN-1032 : 22587/5 useful/useless nets, 20216/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82389, tnet num: 22587, tinst num: 20215, tnode num: 116085, tedge num: 128351.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.135136s wall, 1.093750s user + 0.046875s system = 1.140625s CPU (100.5%)

RUN-1004 : used memory is 467 MB, reserved memory is 435 MB, peak memory is 467 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22587 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 184 (3.58), #lev = 7 (1.95)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 184 (3.58), #lev = 7 (1.95)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 437 instances into 184 LUTs, name keeping = 74%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 318 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 108 adder to BLE ...
SYN-4008 : Packed 108 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.159020s wall, 4.046875s user + 0.109375s system = 4.156250s CPU (99.9%)

RUN-1004 : used memory is 353 MB, reserved memory is 318 MB, peak memory is 576 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  6.510208s wall, 6.359375s user + 0.140625s system = 6.500000s CPU (99.8%)

RUN-1004 : used memory is 353 MB, reserved memory is 318 MB, peak memory is 576 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/uart_com2/AGRIC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/uart_com2/AGRIC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/uart_com2/AGRIC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/uart_com2/AGRIC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/uart_com2/AGRIC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/uart_com2/AGRIC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/uart_com2/AGRIC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/uart_com2/AGRIC_data[0]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[7] will be merged to another kept net COM3/rmc_com3/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[6] will be merged to another kept net COM3/rmc_com3/GPRMC_data[6]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (199 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19641 instances
RUN-0007 : 5622 luts, 12515 seqs, 925 mslices, 490 lslices, 60 pads, 24 brams, 0 dsps
RUN-1001 : There are total 22036 nets
RUN-1001 : 16591 nets have 2 pins
RUN-1001 : 4304 nets have [3 - 5] pins
RUN-1001 : 801 nets have [6 - 10] pins
RUN-1001 : 215 nets have [11 - 20] pins
RUN-1001 : 104 nets have [21 - 99] pins
RUN-1001 : 21 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4748     
RUN-1001 :   No   |  No   |  Yes  |     637     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    6659     
RUN-1001 :   Yes  |  No   |  Yes  |     371     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  117  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 125
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19639 instances, 5622 luts, 12515 seqs, 1415 slices, 281 macros(1415 instances: 925 mslices 490 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 64%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81156, tnet num: 22034, tinst num: 19639, tnode num: 114786, tedge num: 127257.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.140904s wall, 1.093750s user + 0.046875s system = 1.140625s CPU (100.0%)

RUN-1004 : used memory is 529 MB, reserved memory is 500 MB, peak memory is 576 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22034 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.945317s wall, 1.890625s user + 0.062500s system = 1.953125s CPU (100.4%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.45172e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19639.
PHY-3001 : Level 1 #clusters 2060.
PHY-3001 : End clustering;  0.125989s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (136.4%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 64%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 881113, overlap = 612.375
PHY-3002 : Step(2): len = 786660, overlap = 674.438
PHY-3002 : Step(3): len = 521394, overlap = 828.062
PHY-3002 : Step(4): len = 452860, overlap = 887.781
PHY-3002 : Step(5): len = 358767, overlap = 1026.47
PHY-3002 : Step(6): len = 321829, overlap = 1074.75
PHY-3002 : Step(7): len = 269468, overlap = 1144.25
PHY-3002 : Step(8): len = 237134, overlap = 1207.53
PHY-3002 : Step(9): len = 209145, overlap = 1239.56
PHY-3002 : Step(10): len = 193491, overlap = 1293.97
PHY-3002 : Step(11): len = 175853, overlap = 1323.34
PHY-3002 : Step(12): len = 159349, overlap = 1349.88
PHY-3002 : Step(13): len = 150990, overlap = 1388.19
PHY-3002 : Step(14): len = 137945, overlap = 1424.22
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.20238e-06
PHY-3002 : Step(15): len = 144233, overlap = 1401.19
PHY-3002 : Step(16): len = 188778, overlap = 1267.84
PHY-3002 : Step(17): len = 198630, overlap = 1160.31
PHY-3002 : Step(18): len = 196527, overlap = 1116.75
PHY-3002 : Step(19): len = 193726, overlap = 1063.69
PHY-3002 : Step(20): len = 189024, overlap = 1044.28
PHY-3002 : Step(21): len = 184598, overlap = 1029.56
PHY-3002 : Step(22): len = 180666, overlap = 1057.66
PHY-3002 : Step(23): len = 175844, overlap = 1053.81
PHY-3002 : Step(24): len = 173235, overlap = 1050.47
PHY-3002 : Step(25): len = 171628, overlap = 1038.53
PHY-3002 : Step(26): len = 168934, overlap = 1037.66
PHY-3002 : Step(27): len = 168210, overlap = 1032.44
PHY-3002 : Step(28): len = 167240, overlap = 1017.5
PHY-3002 : Step(29): len = 167380, overlap = 1010.5
PHY-3002 : Step(30): len = 166817, overlap = 1011.56
PHY-3002 : Step(31): len = 166315, overlap = 1011.25
PHY-3002 : Step(32): len = 165185, overlap = 1003.78
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.40477e-06
PHY-3002 : Step(33): len = 175253, overlap = 993.5
PHY-3002 : Step(34): len = 190180, overlap = 933.188
PHY-3002 : Step(35): len = 194900, overlap = 919.812
PHY-3002 : Step(36): len = 197818, overlap = 907.75
PHY-3002 : Step(37): len = 200230, overlap = 895.375
PHY-3002 : Step(38): len = 199599, overlap = 872.406
PHY-3002 : Step(39): len = 197885, overlap = 868.281
PHY-3002 : Step(40): len = 195474, overlap = 868.281
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.80953e-06
PHY-3002 : Step(41): len = 207597, overlap = 834.438
PHY-3002 : Step(42): len = 221146, overlap = 788.156
PHY-3002 : Step(43): len = 224234, overlap = 752.156
PHY-3002 : Step(44): len = 225858, overlap = 736.812
PHY-3002 : Step(45): len = 226137, overlap = 729.844
PHY-3002 : Step(46): len = 225528, overlap = 705.156
PHY-3002 : Step(47): len = 223901, overlap = 702.531
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 9.61906e-06
PHY-3002 : Step(48): len = 235022, overlap = 652.188
PHY-3002 : Step(49): len = 250875, overlap = 559.781
PHY-3002 : Step(50): len = 255919, overlap = 525.438
PHY-3002 : Step(51): len = 258391, overlap = 511.906
PHY-3002 : Step(52): len = 257537, overlap = 518.625
PHY-3002 : Step(53): len = 255944, overlap = 518.562
PHY-3002 : Step(54): len = 254124, overlap = 523.531
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.92381e-05
PHY-3002 : Step(55): len = 266154, overlap = 470.844
PHY-3002 : Step(56): len = 279344, overlap = 429.344
PHY-3002 : Step(57): len = 283890, overlap = 421.156
PHY-3002 : Step(58): len = 284481, overlap = 405.938
PHY-3002 : Step(59): len = 281947, overlap = 422.312
PHY-3002 : Step(60): len = 280008, overlap = 400.281
PHY-3002 : Step(61): len = 277976, overlap = 406.531
PHY-3002 : Step(62): len = 277767, overlap = 395.062
PHY-3002 : Step(63): len = 276670, overlap = 392.406
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.84763e-05
PHY-3002 : Step(64): len = 287060, overlap = 373.281
PHY-3002 : Step(65): len = 297665, overlap = 352.25
PHY-3002 : Step(66): len = 300008, overlap = 349.312
PHY-3002 : Step(67): len = 300290, overlap = 336.312
PHY-3002 : Step(68): len = 299527, overlap = 327.5
PHY-3002 : Step(69): len = 298328, overlap = 333.969
PHY-3002 : Step(70): len = 296837, overlap = 350.344
PHY-3002 : Step(71): len = 296084, overlap = 345.906
PHY-3002 : Step(72): len = 295427, overlap = 333.375
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 7.69525e-05
PHY-3002 : Step(73): len = 303120, overlap = 320.188
PHY-3002 : Step(74): len = 311085, overlap = 306.625
PHY-3002 : Step(75): len = 314272, overlap = 292.156
PHY-3002 : Step(76): len = 315386, overlap = 283.656
PHY-3002 : Step(77): len = 314510, overlap = 281.969
PHY-3002 : Step(78): len = 314295, overlap = 269.562
PHY-3002 : Step(79): len = 312956, overlap = 262.75
PHY-3002 : Step(80): len = 313381, overlap = 274.312
PHY-3002 : Step(81): len = 313236, overlap = 279.5
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000153905
PHY-3002 : Step(82): len = 317095, overlap = 262.344
PHY-3002 : Step(83): len = 322735, overlap = 253.25
PHY-3002 : Step(84): len = 323922, overlap = 245.031
PHY-3002 : Step(85): len = 324897, overlap = 247.844
PHY-3002 : Step(86): len = 324730, overlap = 258.688
PHY-3002 : Step(87): len = 323991, overlap = 271.594
PHY-3002 : Step(88): len = 323047, overlap = 294.062
PHY-3002 : Step(89): len = 323322, overlap = 290.75
PHY-3002 : Step(90): len = 322032, overlap = 298.75
PHY-3002 : Step(91): len = 322339, overlap = 292.375
PHY-3002 : Step(92): len = 321656, overlap = 287.156
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.00030781
PHY-3002 : Step(93): len = 323852, overlap = 288.781
PHY-3002 : Step(94): len = 327300, overlap = 287.5
PHY-3002 : Step(95): len = 328267, overlap = 281.969
PHY-3002 : Step(96): len = 329733, overlap = 268.219
PHY-3002 : Step(97): len = 330655, overlap = 250.906
PHY-3002 : Step(98): len = 331052, overlap = 250.312
PHY-3002 : Step(99): len = 330449, overlap = 246
PHY-3002 : Step(100): len = 330804, overlap = 250.594
PHY-3002 : Step(101): len = 330904, overlap = 249.938
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(102): len = 331847, overlap = 250.562
PHY-3002 : Step(103): len = 334663, overlap = 251.875
PHY-3002 : Step(104): len = 336155, overlap = 251.188
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.009438s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/22036.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 448656, over cnt = 1175(3%), over = 5256, worst = 56
PHY-1001 : End global iterations;  0.829242s wall, 1.125000s user + 0.015625s system = 1.140625s CPU (137.6%)

PHY-1001 : Congestion index: top1 = 79.76, top5 = 53.62, top10 = 43.56, top15 = 37.90.
PHY-3001 : End congestion estimation;  1.032912s wall, 1.343750s user + 0.015625s system = 1.359375s CPU (131.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22034 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.839655s wall, 0.796875s user + 0.031250s system = 0.828125s CPU (98.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000105155
PHY-3002 : Step(105): len = 381600, overlap = 219.219
PHY-3002 : Step(106): len = 398743, overlap = 191.875
PHY-3002 : Step(107): len = 393280, overlap = 189.844
PHY-3002 : Step(108): len = 392645, overlap = 183.75
PHY-3002 : Step(109): len = 400073, overlap = 170.719
PHY-3002 : Step(110): len = 402450, overlap = 166.562
PHY-3002 : Step(111): len = 405237, overlap = 158.125
PHY-3002 : Step(112): len = 410523, overlap = 148.969
PHY-3002 : Step(113): len = 412233, overlap = 154.531
PHY-3002 : Step(114): len = 416534, overlap = 148.688
PHY-3002 : Step(115): len = 418566, overlap = 144.188
PHY-3002 : Step(116): len = 418344, overlap = 142
PHY-3002 : Step(117): len = 420559, overlap = 144.656
PHY-3002 : Step(118): len = 423800, overlap = 139.469
PHY-3002 : Step(119): len = 425590, overlap = 142.375
PHY-3002 : Step(120): len = 427395, overlap = 142.875
PHY-3002 : Step(121): len = 429371, overlap = 144.344
PHY-3002 : Step(122): len = 432397, overlap = 144.875
PHY-3002 : Step(123): len = 432246, overlap = 151.469
PHY-3002 : Step(124): len = 433613, overlap = 148.094
PHY-3002 : Step(125): len = 434745, overlap = 133.312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00021031
PHY-3002 : Step(126): len = 433687, overlap = 130.844
PHY-3002 : Step(127): len = 435414, overlap = 126.625
PHY-3002 : Step(128): len = 437054, overlap = 123.188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00042062
PHY-3002 : Step(129): len = 443931, overlap = 128.062
PHY-3002 : Step(130): len = 453462, overlap = 131.406
PHY-3002 : Step(131): len = 458923, overlap = 139.875
PHY-3002 : Step(132): len = 457748, overlap = 137
PHY-3002 : Step(133): len = 459396, overlap = 131.781
PHY-3002 : Step(134): len = 458810, overlap = 127.625
PHY-3002 : Step(135): len = 458356, overlap = 124.75
PHY-3002 : Step(136): len = 459123, overlap = 117.906
PHY-3002 : Step(137): len = 458189, overlap = 117.469
PHY-3002 : Step(138): len = 456675, overlap = 111.656
PHY-3002 : Step(139): len = 459070, overlap = 105.219
PHY-3002 : Step(140): len = 459474, overlap = 102.562
PHY-3002 : Step(141): len = 459047, overlap = 102.062
PHY-3002 : Step(142): len = 459420, overlap = 106.406
PHY-3002 : Step(143): len = 460922, overlap = 109.656
PHY-3002 : Step(144): len = 458897, overlap = 107.75
PHY-3002 : Step(145): len = 457854, overlap = 110.406
PHY-3002 : Step(146): len = 458264, overlap = 108.219
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000841241
PHY-3002 : Step(147): len = 457658, overlap = 104.688
PHY-3002 : Step(148): len = 459378, overlap = 101.438
PHY-3002 : Step(149): len = 463088, overlap = 104.5
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00168248
PHY-3002 : Step(150): len = 463423, overlap = 101.688
PHY-3002 : Step(151): len = 470654, overlap = 102.438
PHY-3002 : Step(152): len = 480626, overlap = 107.312
PHY-3002 : Step(153): len = 479410, overlap = 110.25
PHY-3002 : Step(154): len = 477192, overlap = 105.875
PHY-3002 : Step(155): len = 476771, overlap = 104.188
PHY-3002 : Step(156): len = 474668, overlap = 104.312
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 60/22036.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 544816, over cnt = 2238(6%), over = 10263, worst = 37
PHY-1001 : End global iterations;  1.019420s wall, 1.609375s user + 0.015625s system = 1.625000s CPU (159.4%)

PHY-1001 : Congestion index: top1 = 78.10, top5 = 60.54, top10 = 51.70, top15 = 46.09.
PHY-3001 : End congestion estimation;  1.254708s wall, 1.843750s user + 0.015625s system = 1.859375s CPU (148.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22034 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.870888s wall, 0.859375s user + 0.015625s system = 0.875000s CPU (100.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.49741e-05
PHY-3002 : Step(157): len = 484276, overlap = 355.656
PHY-3002 : Step(158): len = 488922, overlap = 286.656
PHY-3002 : Step(159): len = 482119, overlap = 262.844
PHY-3002 : Step(160): len = 476423, overlap = 246.438
PHY-3002 : Step(161): len = 472521, overlap = 236.625
PHY-3002 : Step(162): len = 468766, overlap = 229.25
PHY-3002 : Step(163): len = 466842, overlap = 231.438
PHY-3002 : Step(164): len = 464807, overlap = 216.438
PHY-3002 : Step(165): len = 460277, overlap = 218.625
PHY-3002 : Step(166): len = 457967, overlap = 217.031
PHY-3002 : Step(167): len = 455680, overlap = 215.344
PHY-3002 : Step(168): len = 454281, overlap = 213.031
PHY-3002 : Step(169): len = 452802, overlap = 209.656
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000189948
PHY-3002 : Step(170): len = 451687, overlap = 209.688
PHY-3002 : Step(171): len = 452779, overlap = 198.719
PHY-3002 : Step(172): len = 453656, overlap = 188.469
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000379897
PHY-3002 : Step(173): len = 455660, overlap = 179.281
PHY-3002 : Step(174): len = 460737, overlap = 183.125
PHY-3002 : Step(175): len = 464645, overlap = 174.188
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000759793
PHY-3002 : Step(176): len = 464720, overlap = 168.312
PHY-3002 : Step(177): len = 467679, overlap = 157.156
PHY-3002 : Step(178): len = 471287, overlap = 155.281
PHY-3002 : Step(179): len = 474828, overlap = 152.875
PHY-3002 : Step(180): len = 475764, overlap = 145.719
PHY-3002 : Step(181): len = 476710, overlap = 139.781
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00151959
PHY-3002 : Step(182): len = 477362, overlap = 138.625
PHY-3002 : Step(183): len = 479494, overlap = 142.375
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81156, tnet num: 22034, tinst num: 19639, tnode num: 114786, tedge num: 127257.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.441533s wall, 1.437500s user + 0.000000s system = 1.437500s CPU (99.7%)

RUN-1004 : used memory is 570 MB, reserved memory is 543 MB, peak memory is 705 MB
OPT-1001 : Total overflow 499.50 peak overflow 4.56
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 399/22036.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 562456, over cnt = 2546(7%), over = 8838, worst = 26
PHY-1001 : End global iterations;  1.252256s wall, 1.921875s user + 0.046875s system = 1.968750s CPU (157.2%)

PHY-1001 : Congestion index: top1 = 58.73, top5 = 47.53, top10 = 42.72, top15 = 39.81.
PHY-1001 : End incremental global routing;  1.471790s wall, 2.140625s user + 0.046875s system = 2.187500s CPU (148.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22034 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.898174s wall, 0.875000s user + 0.015625s system = 0.890625s CPU (99.2%)

OPT-1001 : 22 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19554 has valid locations, 265 needs to be replaced
PHY-3001 : design contains 19882 instances, 5742 luts, 12638 seqs, 1415 slices, 281 macros(1415 instances: 925 mslices 490 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 500326
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17393/22279.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 582056, over cnt = 2556(7%), over = 8873, worst = 26
PHY-1001 : End global iterations;  0.182729s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (153.9%)

PHY-1001 : Congestion index: top1 = 59.33, top5 = 48.01, top10 = 43.15, top15 = 40.29.
PHY-3001 : End congestion estimation;  0.403634s wall, 0.484375s user + 0.015625s system = 0.500000s CPU (123.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81978, tnet num: 22277, tinst num: 19882, tnode num: 115891, tedge num: 128415.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.449196s wall, 1.406250s user + 0.046875s system = 1.453125s CPU (100.3%)

RUN-1004 : used memory is 616 MB, reserved memory is 608 MB, peak memory is 707 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22277 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.386153s wall, 2.312500s user + 0.078125s system = 2.390625s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(184): len = 499732, overlap = 0.875
PHY-3002 : Step(185): len = 500420, overlap = 0.9375
PHY-3002 : Step(186): len = 501287, overlap = 0.9375
PHY-3002 : Step(187): len = 501889, overlap = 0.875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17423/22279.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 579248, over cnt = 2585(7%), over = 9045, worst = 26
PHY-1001 : End global iterations;  0.189604s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (131.9%)

PHY-1001 : Congestion index: top1 = 59.78, top5 = 48.77, top10 = 43.74, top15 = 40.70.
PHY-3001 : End congestion estimation;  0.414521s wall, 0.484375s user + 0.000000s system = 0.484375s CPU (116.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22277 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.891559s wall, 0.890625s user + 0.000000s system = 0.890625s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00131538
PHY-3002 : Step(188): len = 502130, overlap = 144.719
PHY-3002 : Step(189): len = 502329, overlap = 144.781
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00263076
PHY-3002 : Step(190): len = 502454, overlap = 144.938
PHY-3002 : Step(191): len = 502710, overlap = 144.969
PHY-3001 : Final: Len = 502710, Over = 144.969
PHY-3001 : End incremental placement;  4.899869s wall, 5.328125s user + 0.343750s system = 5.671875s CPU (115.8%)

OPT-1001 : Total overflow 504.88 peak overflow 4.56
OPT-1001 : End high-fanout net optimization;  7.743428s wall, 9.000000s user + 0.406250s system = 9.406250s CPU (121.5%)

OPT-1001 : Current memory(MB): used = 711, reserve = 690, peak = 728.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17436/22279.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 581416, over cnt = 2520(7%), over = 8342, worst = 26
PHY-1002 : len = 617072, over cnt = 1722(4%), over = 4599, worst = 19
PHY-1002 : len = 656336, over cnt = 760(2%), over = 1823, worst = 16
PHY-1002 : len = 674480, over cnt = 277(0%), over = 645, worst = 16
PHY-1002 : len = 686352, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.208279s wall, 1.750000s user + 0.000000s system = 1.750000s CPU (144.8%)

PHY-1001 : Congestion index: top1 = 49.18, top5 = 43.65, top10 = 40.79, top15 = 38.83.
OPT-1001 : End congestion update;  1.437228s wall, 1.984375s user + 0.000000s system = 1.984375s CPU (138.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22277 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.800464s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.6%)

OPT-0007 : Start: WNS 3922 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.243924s wall, 2.796875s user + 0.000000s system = 2.796875s CPU (124.6%)

OPT-1001 : Current memory(MB): used = 686, reserve = 667, peak = 728.
OPT-1001 : End physical optimization;  11.728422s wall, 13.671875s user + 0.406250s system = 14.078125s CPU (120.0%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5742 LUT to BLE ...
SYN-4008 : Packed 5742 LUT and 2891 SEQ to BLE.
SYN-4003 : Packing 9747 remaining SEQ's ...
SYN-4005 : Packed 3279 SEQ with LUT/SLICE
SYN-4006 : 75 single LUT's are left
SYN-4006 : 6468 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 12210/13850 primitive instances ...
PHY-3001 : End packing;  2.680521s wall, 2.687500s user + 0.000000s system = 2.687500s CPU (100.3%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8173 instances
RUN-1001 : 4042 mslices, 4042 lslices, 60 pads, 24 brams, 0 dsps
RUN-1001 : There are total 19440 nets
RUN-1001 : 13613 nets have 2 pins
RUN-1001 : 4436 nets have [3 - 5] pins
RUN-1001 : 873 nets have [6 - 10] pins
RUN-1001 : 369 nets have [11 - 20] pins
RUN-1001 : 140 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8171 instances, 8084 slices, 281 macros(1415 instances: 925 mslices 490 lslices)
PHY-3001 : Cell area utilization is 86%
PHY-3001 : After packing: Len = 520330, Over = 368.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 8029/19440.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 651824, over cnt = 1516(4%), over = 2428, worst = 8
PHY-1002 : len = 657976, over cnt = 975(2%), over = 1346, worst = 7
PHY-1002 : len = 670944, over cnt = 270(0%), over = 335, worst = 6
PHY-1002 : len = 675696, over cnt = 40(0%), over = 50, worst = 5
PHY-1002 : len = 676832, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.137634s wall, 1.703125s user + 0.015625s system = 1.718750s CPU (151.1%)

PHY-1001 : Congestion index: top1 = 49.66, top5 = 43.55, top10 = 40.35, top15 = 38.33.
PHY-3001 : End congestion estimation;  1.431523s wall, 1.984375s user + 0.015625s system = 2.000000s CPU (139.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67452, tnet num: 19438, tinst num: 8171, tnode num: 92049, tedge num: 111013.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.632695s wall, 1.640625s user + 0.000000s system = 1.640625s CPU (100.5%)

RUN-1004 : used memory is 606 MB, reserved memory is 594 MB, peak memory is 728 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19438 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.484072s wall, 2.484375s user + 0.000000s system = 2.484375s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.03814e-05
PHY-3002 : Step(192): len = 527615, overlap = 359
PHY-3002 : Step(193): len = 527369, overlap = 364.75
PHY-3002 : Step(194): len = 526279, overlap = 377
PHY-3002 : Step(195): len = 527046, overlap = 390.75
PHY-3002 : Step(196): len = 527630, overlap = 390.25
PHY-3002 : Step(197): len = 526955, overlap = 392.75
PHY-3002 : Step(198): len = 526626, overlap = 391.75
PHY-3002 : Step(199): len = 524118, overlap = 383.25
PHY-3002 : Step(200): len = 522944, overlap = 372.75
PHY-3002 : Step(201): len = 520004, overlap = 378.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000100763
PHY-3002 : Step(202): len = 523795, overlap = 362
PHY-3002 : Step(203): len = 526318, overlap = 363
PHY-3002 : Step(204): len = 526680, overlap = 362.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000189184
PHY-3002 : Step(205): len = 537207, overlap = 340.25
PHY-3002 : Step(206): len = 547035, overlap = 330.75
PHY-3002 : Step(207): len = 543864, overlap = 336.25
PHY-3002 : Step(208): len = 541474, overlap = 330.25
PHY-3002 : Step(209): len = 540390, overlap = 329.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.610484s wall, 0.796875s user + 0.734375s system = 1.531250s CPU (250.8%)

PHY-3001 : Trial Legalized: Len = 652465
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 572/19440.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 742328, over cnt = 2405(6%), over = 4075, worst = 8
PHY-1002 : len = 758096, over cnt = 1488(4%), over = 2181, worst = 7
PHY-1002 : len = 777168, over cnt = 602(1%), over = 833, worst = 6
PHY-1002 : len = 784960, over cnt = 246(0%), over = 353, worst = 6
PHY-1002 : len = 792152, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.836942s wall, 2.875000s user + 0.031250s system = 2.906250s CPU (158.2%)

PHY-1001 : Congestion index: top1 = 52.11, top5 = 47.40, top10 = 44.59, top15 = 42.68.
PHY-3001 : End congestion estimation;  2.163194s wall, 3.203125s user + 0.031250s system = 3.234375s CPU (149.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19438 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.803195s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000192382
PHY-3002 : Step(210): len = 608776, overlap = 93
PHY-3002 : Step(211): len = 591504, overlap = 134.5
PHY-3002 : Step(212): len = 581278, overlap = 172.5
PHY-3002 : Step(213): len = 574307, overlap = 207.25
PHY-3002 : Step(214): len = 571142, overlap = 243.5
PHY-3002 : Step(215): len = 569171, overlap = 252.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000384764
PHY-3002 : Step(216): len = 573577, overlap = 246.5
PHY-3002 : Step(217): len = 579269, overlap = 235.75
PHY-3002 : Step(218): len = 582009, overlap = 236.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(219): len = 584762, overlap = 236.25
PHY-3002 : Step(220): len = 590313, overlap = 241
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.026729s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (116.9%)

PHY-3001 : Legalized: Len = 628081, Over = 0
PHY-3001 : Spreading special nets. 28 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.071345s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (87.6%)

PHY-3001 : 37 instances has been re-located, deltaX = 9, deltaY = 17, maxDist = 2.
PHY-3001 : Final: Len = 628527, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67452, tnet num: 19438, tinst num: 8171, tnode num: 92049, tedge num: 111013.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.860477s wall, 1.812500s user + 0.046875s system = 1.859375s CPU (99.9%)

RUN-1004 : used memory is 616 MB, reserved memory is 621 MB, peak memory is 728 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3521/19440.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 728896, over cnt = 2319(6%), over = 3655, worst = 9
PHY-1002 : len = 742896, over cnt = 1357(3%), over = 1811, worst = 6
PHY-1002 : len = 758056, over cnt = 499(1%), over = 630, worst = 5
PHY-1002 : len = 763296, over cnt = 254(0%), over = 303, worst = 5
PHY-1002 : len = 768984, over cnt = 5(0%), over = 7, worst = 3
PHY-1001 : End global iterations;  1.693705s wall, 2.750000s user + 0.109375s system = 2.859375s CPU (168.8%)

PHY-1001 : Congestion index: top1 = 50.45, top5 = 45.26, top10 = 42.54, top15 = 40.81.
PHY-1001 : End incremental global routing;  1.978887s wall, 3.046875s user + 0.109375s system = 3.156250s CPU (159.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19438 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.823094s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (100.6%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8107 has valid locations, 9 needs to be replaced
PHY-3001 : design contains 8179 instances, 8092 slices, 281 macros(1415 instances: 925 mslices 490 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 630012
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17630/19447.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 770408, over cnt = 19(0%), over = 21, worst = 3
PHY-1002 : len = 770304, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 770344, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 770360, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 770376, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.655819s wall, 0.656250s user + 0.000000s system = 0.656250s CPU (100.1%)

PHY-1001 : Congestion index: top1 = 50.43, top5 = 45.26, top10 = 42.55, top15 = 40.84.
PHY-3001 : End congestion estimation;  0.932135s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (100.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67499, tnet num: 19445, tinst num: 8179, tnode num: 92104, tedge num: 111067.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.857302s wall, 1.843750s user + 0.015625s system = 1.859375s CPU (100.1%)

RUN-1004 : used memory is 647 MB, reserved memory is 638 MB, peak memory is 728 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19445 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.693770s wall, 2.656250s user + 0.031250s system = 2.687500s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(221): len = 630012, overlap = 0.25
PHY-3002 : Step(222): len = 630012, overlap = 0.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17638/19447.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 770376, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.119864s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (91.2%)

PHY-1001 : Congestion index: top1 = 50.43, top5 = 45.26, top10 = 42.55, top15 = 40.84.
PHY-3001 : End congestion estimation;  0.401559s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (101.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19445 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.786736s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (99.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00124405
PHY-3002 : Step(223): len = 630151, overlap = 1
PHY-3002 : Step(224): len = 630155, overlap = 1
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005603s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 630161, Over = 0
PHY-3001 : End spreading;  0.063617s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.2%)

PHY-3001 : Final: Len = 630161, Over = 0
PHY-3001 : End incremental placement;  5.449082s wall, 5.562500s user + 0.125000s system = 5.687500s CPU (104.4%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  8.674584s wall, 10.031250s user + 0.234375s system = 10.265625s CPU (118.3%)

OPT-1001 : Current memory(MB): used = 721, reserve = 705, peak = 728.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17627/19447.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 770416, over cnt = 16(0%), over = 17, worst = 2
PHY-1002 : len = 770392, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 770328, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 770408, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.526443s wall, 0.546875s user + 0.015625s system = 0.562500s CPU (106.8%)

PHY-1001 : Congestion index: top1 = 50.43, top5 = 45.30, top10 = 42.58, top15 = 40.88.
OPT-1001 : End congestion update;  0.802347s wall, 0.812500s user + 0.015625s system = 0.828125s CPU (103.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19445 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.696793s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (100.9%)

OPT-0007 : Start: WNS 4068 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.503841s wall, 1.515625s user + 0.015625s system = 1.531250s CPU (101.8%)

OPT-1001 : Current memory(MB): used = 721, reserve = 705, peak = 728.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19445 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.694002s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (99.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17638/19447.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 770408, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.112589s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (97.1%)

PHY-1001 : Congestion index: top1 = 50.43, top5 = 45.30, top10 = 42.58, top15 = 40.88.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19445 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.709545s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (99.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4068 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 50.000000
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4068ps with logic level 6 
RUN-1001 :       #2 path slack 4068ps with logic level 6 
RUN-1001 :       #3 path slack 4075ps with logic level 6 
RUN-1001 :       #4 path slack 4075ps with logic level 6 
RUN-1001 :       #5 path slack 4118ps with logic level 6 
RUN-1001 :       #6 path slack 4118ps with logic level 6 
RUN-1001 :       #7 path slack 4125ps with logic level 6 
RUN-1001 :       #8 path slack 4125ps with logic level 6 
OPT-1001 : End physical optimization;  14.071070s wall, 15.359375s user + 0.328125s system = 15.687500s CPU (111.5%)

RUN-1003 : finish command "place" in  68.820474s wall, 137.375000s user + 8.234375s system = 145.609375s CPU (211.6%)

RUN-1004 : used memory is 636 MB, reserved memory is 623 MB, peak memory is 728 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.519365s wall, 2.671875s user + 0.031250s system = 2.703125s CPU (177.9%)

RUN-1004 : used memory is 636 MB, reserved memory is 623 MB, peak memory is 728 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8181 instances
RUN-1001 : 4042 mslices, 4050 lslices, 60 pads, 24 brams, 0 dsps
RUN-1001 : There are total 19447 nets
RUN-1001 : 13613 nets have 2 pins
RUN-1001 : 4435 nets have [3 - 5] pins
RUN-1001 : 876 nets have [6 - 10] pins
RUN-1001 : 375 nets have [11 - 20] pins
RUN-1001 : 139 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67499, tnet num: 19445, tinst num: 8179, tnode num: 92104, tedge num: 111067.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.593828s wall, 1.593750s user + 0.000000s system = 1.593750s CPU (100.0%)

RUN-1004 : used memory is 618 MB, reserved memory is 598 MB, peak memory is 728 MB
PHY-1001 : 4042 mslices, 4050 lslices, 60 pads, 24 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19445 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 708424, over cnt = 2385(6%), over = 3966, worst = 9
PHY-1002 : len = 725136, over cnt = 1472(4%), over = 2123, worst = 9
PHY-1002 : len = 745392, over cnt = 499(1%), over = 675, worst = 6
PHY-1002 : len = 756120, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 756296, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.624349s wall, 2.781250s user + 0.078125s system = 2.859375s CPU (176.0%)

PHY-1001 : Congestion index: top1 = 50.24, top5 = 44.89, top10 = 42.12, top15 = 40.31.
PHY-1001 : End global routing;  1.926184s wall, 3.062500s user + 0.093750s system = 3.156250s CPU (163.9%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 700, reserve = 689, peak = 728.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 973, reserve = 963, peak = 973.
PHY-1001 : End build detailed router design. 4.381646s wall, 4.281250s user + 0.078125s system = 4.359375s CPU (99.5%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 187376, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.775366s wall, 0.765625s user + 0.015625s system = 0.781250s CPU (100.8%)

PHY-1001 : Current memory(MB): used = 1009, reserve = 1000, peak = 1009.
PHY-1001 : End phase 1; 0.783336s wall, 0.781250s user + 0.015625s system = 0.796875s CPU (101.7%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 55% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.75758e+06, over cnt = 1277(0%), over = 1279, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1024, reserve = 1011, peak = 1024.
PHY-1001 : End initial routed; 17.236130s wall, 47.500000s user + 0.546875s system = 48.046875s CPU (278.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18251(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.395   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.149360s wall, 3.140625s user + 0.000000s system = 3.140625s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 1034, reserve = 1020, peak = 1034.
PHY-1001 : End phase 2; 20.385642s wall, 50.640625s user + 0.546875s system = 51.187500s CPU (251.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.75758e+06, over cnt = 1277(0%), over = 1279, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.222245s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (105.5%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.74439e+06, over cnt = 443(0%), over = 444, worst = 2, crit = 0
PHY-1001 : End DR Iter 1; 0.738780s wall, 1.156250s user + 0.015625s system = 1.171875s CPU (158.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.74426e+06, over cnt = 68(0%), over = 68, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.365037s wall, 0.500000s user + 0.015625s system = 0.515625s CPU (141.3%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.74464e+06, over cnt = 16(0%), over = 16, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.232194s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (107.7%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.74513e+06, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.194415s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (96.4%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.74525e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.165511s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (94.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18251(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.209   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.236404s wall, 3.234375s user + 0.000000s system = 3.234375s CPU (99.9%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 302 feed throughs used by 265 nets
PHY-1001 : End commit to database; 2.113696s wall, 2.093750s user + 0.015625s system = 2.109375s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 1124, reserve = 1114, peak = 1124.
PHY-1001 : End phase 3; 7.731323s wall, 8.312500s user + 0.046875s system = 8.359375s CPU (108.1%)

PHY-1003 : Routed, final wirelength = 1.74525e+06
PHY-1001 : Current memory(MB): used = 1128, reserve = 1119, peak = 1128.
PHY-1001 : End export database. 0.148890s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (104.9%)

PHY-1001 : End detail routing;  33.810394s wall, 64.546875s user + 0.687500s system = 65.234375s CPU (192.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67499, tnet num: 19445, tinst num: 8179, tnode num: 92104, tedge num: 111067.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.607910s wall, 1.609375s user + 0.000000s system = 1.609375s CPU (100.1%)

RUN-1004 : used memory is 1063 MB, reserved memory is 1062 MB, peak memory is 1128 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  41.313392s wall, 73.171875s user + 0.796875s system = 73.968750s CPU (179.0%)

RUN-1004 : used memory is 1062 MB, reserved memory is 1062 MB, peak memory is 1128 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8718   out of  19600   44.48%
#reg                    12734   out of  19600   64.97%
#le                     15152
  #lut only              2418   out of  15152   15.96%
  #reg only              6434   out of  15152   42.46%
  #lut&reg               6300   out of  15152   41.58%
#dsp                        0   out of     29    0.00%
#bram                      24   out of     64   37.50%
  #bram9k                  24
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                    10
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6981
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          118
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         M3        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         P6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15152  |7303    |1415    |12777   |24      |0       |
|  AnyFog_dataX                      |AnyFog          |209    |86      |22      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |87     |57      |22      |51      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |209    |91      |22      |174     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |60      |22      |51      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |204    |122     |22      |166     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |87     |63      |22      |49      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |3412   |915     |34      |3325    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |794    |98      |5       |782     |0       |0       |
|    STADOP_com2                     |STADOP          |557    |87      |0       |545     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |43      |14      |42      |0       |0       |
|    head_com2                       |uniheading      |269    |97      |5       |257     |0       |0       |
|    uart_com2                       |Agrica          |1432   |295     |10      |1404    |0       |0       |
|  COM3                              |COM3_Control    |214    |85      |14      |183     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |61     |40      |14      |39      |0       |0       |
|    rmc_com3                        |Gprmc           |153    |45      |0       |144     |0       |0       |
|  DATA                              |Data_Processing |8700   |4379    |1059    |7013    |0       |0       |
|    DIV_Dtemp                       |Divider         |822    |334     |84      |694     |0       |0       |
|    DIV_Utemp                       |Divider         |604    |293     |84      |478     |0       |0       |
|    DIV_accX                        |Divider         |575    |318     |84      |447     |0       |0       |
|    DIV_accY                        |Divider         |675    |341     |108     |507     |0       |0       |
|    DIV_accZ                        |Divider         |672    |382     |132     |469     |0       |0       |
|    DIV_rateX                       |Divider         |660    |365     |132     |454     |0       |0       |
|    DIV_rateY                       |Divider         |582    |411     |132     |376     |0       |0       |
|    DIV_rateZ                       |Divider         |608    |361     |132     |401     |0       |0       |
|    genclk                          |genclk          |75     |48      |20      |42      |0       |0       |
|  FMC                               |FMC_Ctrl        |444    |394     |43      |342     |0       |0       |
|  IIC                               |I2C_master      |282    |236     |11      |253     |0       |0       |
|  IMU_CTRL                          |SCHA634         |874    |621     |61      |729     |0       |0       |
|    CtrlData                        |CtrlData        |449    |396     |47      |337     |0       |0       |
|      usms                          |Time_1ms        |27     |22      |5       |19      |0       |0       |
|    SPIM                            |SPI_SCHA634     |425    |225     |14      |392     |0       |0       |
|  POWER                             |POWER_EN        |97     |50      |38      |38      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |490    |324     |89      |321     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |490    |324     |89      |321     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |203    |134     |0       |187     |0       |0       |
|        reg_inst                    |register        |200    |131     |0       |184     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |287    |190     |89      |134     |0       |0       |
|        bus_inst                    |bus_top         |78     |50      |28      |29      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |22     |12      |10      |5       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |3      |3       |0       |3       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |52     |34      |18      |20      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |122    |90      |29      |73      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13552  
    #2          2       3588   
    #3          3        603   
    #4          4        244   
    #5        5-10       924   
    #6        11-50      458   
    #7       51-100       8    
    #8       101-500      3    
    #9        >500        2    
  Average     2.11             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  1.859117s wall, 3.187500s user + 0.046875s system = 3.234375s CPU (174.0%)

RUN-1004 : used memory is 1062 MB, reserved memory is 1062 MB, peak memory is 1128 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67499, tnet num: 19445, tinst num: 8179, tnode num: 92104, tedge num: 111067.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.676676s wall, 1.671875s user + 0.015625s system = 1.687500s CPU (100.6%)

RUN-1004 : used memory is 1064 MB, reserved memory is 1063 MB, peak memory is 1128 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19445 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.271142s wall, 1.265625s user + 0.000000s system = 1.265625s CPU (99.6%)

RUN-1004 : used memory is 1070 MB, reserved memory is 1068 MB, peak memory is 1128 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 89dcf97a3cd86c8370cef48ec94e5c7c51982d2733d868157b75ae7b794c4fe9 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8179
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19447, pip num: 147241
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 302
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3250 valid insts, and 411325 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111110110100010110101
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  10.435670s wall, 102.984375s user + 0.109375s system = 103.093750s CPU (987.9%)

RUN-1004 : used memory is 1195 MB, reserved memory is 1180 MB, peak memory is 1310 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250701_183358.log"

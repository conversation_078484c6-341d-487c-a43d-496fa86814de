============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Tue May 27 10:27:32 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(514)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
RUN-1001 : Project manager successfully analyzed 21 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.867122s wall, 1.578125s user + 4.296875s system = 5.875000s CPU (100.1%)

RUN-1004 : used memory is 79 MB, reserved memory is 40 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.828730s wall, 1.781250s user + 0.046875s system = 1.828125s CPU (100.0%)

RUN-1004 : used memory is 299 MB, reserved memory is 267 MB, peak memory is 303 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 58 trigger nets, 58 data nets.
KIT-1004 : Chipwatcher code = 1110111010000001
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=158) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=158) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=158)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=158)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22983/23 useful/useless nets, 19699/12 useful/useless insts
SYN-1016 : Merged 29 instances.
SYN-1032 : 22587/20 useful/useless nets, 20205/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 478 better
SYN-1014 : Optimize round 2
SYN-1032 : 22203/45 useful/useless nets, 19821/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.790563s wall, 2.671875s user + 0.093750s system = 2.765625s CPU (99.1%)

RUN-1004 : used memory is 328 MB, reserved memory is 295 MB, peak memory is 330 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22275/441 useful/useless nets, 19944/63 useful/useless insts
SYN-1016 : Merged 69 instances.
SYN-2571 : Optimize after map_dsp, round 1, 573 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 56 instances.
SYN-2501 : Optimize round 1, 114 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 22779/5 useful/useless nets, 20448/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83920, tnet num: 22779, tinst num: 20447, tnode num: 117803, tedge num: 130708.
TMR-2508 : Levelizing timing graph completed, there are 85 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.225994s wall, 1.234375s user + 0.000000s system = 1.234375s CPU (100.7%)

RUN-1004 : used memory is 471 MB, reserved memory is 439 MB, peak memory is 471 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22779 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 282 (3.29), #lev = 7 (1.66)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 282 (3.29), #lev = 7 (1.66)
SYN-3001 : Logic optimization runtime opt =   0.04 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 614 instances into 282 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 487 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 160 adder to BLE ...
SYN-4008 : Packed 160 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  5.074677s wall, 5.031250s user + 0.062500s system = 5.093750s CPU (100.4%)

RUN-1004 : used memory is 354 MB, reserved memory is 319 MB, peak memory is 581 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  8.233398s wall, 8.015625s user + 0.218750s system = 8.234375s CPU (100.0%)

RUN-1004 : used memory is 355 MB, reserved memory is 320 MB, peak memory is 581 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/rmc_com2/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/rmc_com2/GPRMC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/rmc_com2/GPRMC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/rmc_com2/GPRMC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/rmc_com2/GPRMC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/rmc_com2/GPRMC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/rmc_com2/GPRMC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/rmc_com2/GPRMC_data[0]
SYN-5055 WARNING: The kept net COM2/rx_err will be merged to another kept net COM2/rmc_com2/GPRMC_err
SYN-5055 WARNING: The kept net FMC/Error_state[0] will be merged to another kept net COM2/rmc_com2/GPRMC_sub_state_cnt0[3]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (335 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19772 instances
RUN-0007 : 5682 luts, 12543 seqs, 943 mslices, 491 lslices, 56 pads, 52 brams, 0 dsps
RUN-1001 : There are total 22127 nets
RUN-1001 : 16539 nets have 2 pins
RUN-1001 : 4433 nets have [3 - 5] pins
RUN-1001 : 770 nets have [6 - 10] pins
RUN-1001 : 261 nets have [11 - 20] pins
RUN-1001 : 100 nets have [21 - 99] pins
RUN-1001 : 24 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4743     
RUN-1001 :   No   |  No   |  Yes  |     645     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    6556     
RUN-1001 :   Yes  |  No   |  Yes  |     499     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  118  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 126
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19770 instances, 5682 luts, 12543 seqs, 1434 slices, 280 macros(1434 instances: 943 mslices 491 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1131 pins
PHY-3001 : Huge net DATA/done_div with 1644 pins
PHY-0007 : Cell area utilization is 64%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82447, tnet num: 22125, tinst num: 19770, tnode num: 116465, tedge num: 129554.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.249342s wall, 1.218750s user + 0.031250s system = 1.250000s CPU (100.1%)

RUN-1004 : used memory is 532 MB, reserved memory is 504 MB, peak memory is 581 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22125 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.209087s wall, 2.140625s user + 0.062500s system = 2.203125s CPU (99.7%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.40445e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19770.
PHY-3001 : Level 1 #clusters 2106.
PHY-3001 : End clustering;  0.167343s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (149.4%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 64%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 882069, overlap = 680.312
PHY-3002 : Step(2): len = 787967, overlap = 783.562
PHY-3002 : Step(3): len = 531695, overlap = 929.344
PHY-3002 : Step(4): len = 458334, overlap = 1009.28
PHY-3002 : Step(5): len = 361018, overlap = 1086.81
PHY-3002 : Step(6): len = 319697, overlap = 1161.5
PHY-3002 : Step(7): len = 271126, overlap = 1249.19
PHY-3002 : Step(8): len = 245102, overlap = 1312.72
PHY-3002 : Step(9): len = 220665, overlap = 1379.19
PHY-3002 : Step(10): len = 197428, overlap = 1421.75
PHY-3002 : Step(11): len = 183796, overlap = 1448.41
PHY-3002 : Step(12): len = 170639, overlap = 1476.16
PHY-3002 : Step(13): len = 152055, overlap = 1530.12
PHY-3002 : Step(14): len = 142626, overlap = 1546.5
PHY-3002 : Step(15): len = 128529, overlap = 1555.62
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.26435e-07
PHY-3002 : Step(16): len = 131934, overlap = 1549.97
PHY-3002 : Step(17): len = 170962, overlap = 1494.47
PHY-3002 : Step(18): len = 181287, overlap = 1374.56
PHY-3002 : Step(19): len = 183692, overlap = 1321.88
PHY-3002 : Step(20): len = 182713, overlap = 1275.94
PHY-3002 : Step(21): len = 180505, overlap = 1257.69
PHY-3002 : Step(22): len = 178554, overlap = 1229.69
PHY-3002 : Step(23): len = 174004, overlap = 1240.03
PHY-3002 : Step(24): len = 172148, overlap = 1238.34
PHY-3002 : Step(25): len = 168956, overlap = 1243.25
PHY-3002 : Step(26): len = 167805, overlap = 1251.28
PHY-3002 : Step(27): len = 164604, overlap = 1245.22
PHY-3002 : Step(28): len = 162991, overlap = 1255.81
PHY-3002 : Step(29): len = 161787, overlap = 1264.56
PHY-3002 : Step(30): len = 161424, overlap = 1248.62
PHY-3002 : Step(31): len = 160041, overlap = 1268.31
PHY-3002 : Step(32): len = 159537, overlap = 1280.94
PHY-3002 : Step(33): len = 159215, overlap = 1267.62
PHY-3002 : Step(34): len = 158553, overlap = 1247.22
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 1.65287e-06
PHY-3002 : Step(35): len = 164798, overlap = 1215.19
PHY-3002 : Step(36): len = 177456, overlap = 1140.19
PHY-3002 : Step(37): len = 180347, overlap = 1088.16
PHY-3002 : Step(38): len = 183151, overlap = 1060.56
PHY-3002 : Step(39): len = 183478, overlap = 1040.88
PHY-3002 : Step(40): len = 183634, overlap = 1021.66
PHY-3002 : Step(41): len = 183171, overlap = 1024.5
PHY-3002 : Step(42): len = 182719, overlap = 1048.81
PHY-3002 : Step(43): len = 181614, overlap = 1050.72
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 3.30574e-06
PHY-3002 : Step(44): len = 190502, overlap = 995.344
PHY-3002 : Step(45): len = 205700, overlap = 905.094
PHY-3002 : Step(46): len = 211681, overlap = 865.938
PHY-3002 : Step(47): len = 214645, overlap = 827.219
PHY-3002 : Step(48): len = 215096, overlap = 815.75
PHY-3002 : Step(49): len = 213817, overlap = 806
PHY-3002 : Step(50): len = 211359, overlap = 831.281
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 6.61148e-06
PHY-3002 : Step(51): len = 224426, overlap = 778.969
PHY-3002 : Step(52): len = 240816, overlap = 722.406
PHY-3002 : Step(53): len = 244535, overlap = 715.125
PHY-3002 : Step(54): len = 244812, overlap = 714.031
PHY-3002 : Step(55): len = 243302, overlap = 710.688
PHY-3002 : Step(56): len = 241982, overlap = 700.281
PHY-3002 : Step(57): len = 240665, overlap = 709.125
PHY-3002 : Step(58): len = 239497, overlap = 711.781
PHY-3002 : Step(59): len = 238881, overlap = 711.938
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.3223e-05
PHY-3002 : Step(60): len = 251186, overlap = 639.531
PHY-3002 : Step(61): len = 265600, overlap = 586.438
PHY-3002 : Step(62): len = 268126, overlap = 545.344
PHY-3002 : Step(63): len = 269050, overlap = 544.875
PHY-3002 : Step(64): len = 267956, overlap = 534.5
PHY-3002 : Step(65): len = 267292, overlap = 537.375
PHY-3002 : Step(66): len = 264611, overlap = 532.906
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 2.64459e-05
PHY-3002 : Step(67): len = 278553, overlap = 496.281
PHY-3002 : Step(68): len = 289340, overlap = 440.438
PHY-3002 : Step(69): len = 292022, overlap = 434.375
PHY-3002 : Step(70): len = 292319, overlap = 418.531
PHY-3002 : Step(71): len = 290927, overlap = 420.781
PHY-3002 : Step(72): len = 289504, overlap = 423.719
PHY-3002 : Step(73): len = 287260, overlap = 431.812
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 5.28919e-05
PHY-3002 : Step(74): len = 296762, overlap = 415.344
PHY-3002 : Step(75): len = 304472, overlap = 389.094
PHY-3002 : Step(76): len = 307290, overlap = 352.688
PHY-3002 : Step(77): len = 308659, overlap = 349.031
PHY-3002 : Step(78): len = 307588, overlap = 352.438
PHY-3002 : Step(79): len = 306610, overlap = 345.75
PHY-3002 : Step(80): len = 304657, overlap = 348.219
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000103159
PHY-3002 : Step(81): len = 310602, overlap = 341.156
PHY-3002 : Step(82): len = 315943, overlap = 349.469
PHY-3002 : Step(83): len = 318492, overlap = 357.031
PHY-3002 : Step(84): len = 319402, overlap = 358.562
PHY-3002 : Step(85): len = 318592, overlap = 331.812
PHY-3002 : Step(86): len = 317937, overlap = 331.656
PHY-3002 : Step(87): len = 316628, overlap = 333.25
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000192046
PHY-3002 : Step(88): len = 319501, overlap = 313.969
PHY-3002 : Step(89): len = 325375, overlap = 319.156
PHY-3002 : Step(90): len = 327619, overlap = 320.875
PHY-3002 : Step(91): len = 329053, overlap = 313.344
PHY-3002 : Step(92): len = 328806, overlap = 313.844
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.013672s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/22127.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 437128, over cnt = 1283(3%), over = 6057, worst = 35
PHY-1001 : End global iterations;  0.861877s wall, 1.203125s user + 0.062500s system = 1.265625s CPU (146.8%)

PHY-1001 : Congestion index: top1 = 79.40, top5 = 56.19, top10 = 45.19, top15 = 39.08.
PHY-3001 : End congestion estimation;  1.128916s wall, 1.437500s user + 0.078125s system = 1.515625s CPU (134.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22125 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.009139s wall, 1.000000s user + 0.015625s system = 1.015625s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.04976e-05
PHY-3002 : Step(93): len = 376160, overlap = 214.125
PHY-3002 : Step(94): len = 384834, overlap = 194.969
PHY-3002 : Step(95): len = 383436, overlap = 201.5
PHY-3002 : Step(96): len = 383331, overlap = 195.344
PHY-3002 : Step(97): len = 387499, overlap = 178.188
PHY-3002 : Step(98): len = 389588, overlap = 170.781
PHY-3002 : Step(99): len = 391998, overlap = 161.188
PHY-3002 : Step(100): len = 396734, overlap = 154.188
PHY-3002 : Step(101): len = 396079, overlap = 147.719
PHY-3002 : Step(102): len = 395696, overlap = 149.375
PHY-3002 : Step(103): len = 398056, overlap = 155.5
PHY-3002 : Step(104): len = 397888, overlap = 156.438
PHY-3002 : Step(105): len = 398659, overlap = 156.969
PHY-3002 : Step(106): len = 400651, overlap = 158.156
PHY-3002 : Step(107): len = 400051, overlap = 153.25
PHY-3002 : Step(108): len = 400882, overlap = 155.344
PHY-3002 : Step(109): len = 400346, overlap = 158.594
PHY-3002 : Step(110): len = 399398, overlap = 155.188
PHY-3002 : Step(111): len = 399978, overlap = 154.281
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000180995
PHY-3002 : Step(112): len = 399895, overlap = 149.438
PHY-3002 : Step(113): len = 401005, overlap = 149.688
PHY-3002 : Step(114): len = 402758, overlap = 142.188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000339472
PHY-3002 : Step(115): len = 408343, overlap = 134.312
PHY-3002 : Step(116): len = 415106, overlap = 126.531
PHY-3002 : Step(117): len = 420466, overlap = 116.781
PHY-3002 : Step(118): len = 422487, overlap = 109.594
PHY-3002 : Step(119): len = 427919, overlap = 105.031
PHY-3002 : Step(120): len = 429997, overlap = 106
PHY-3002 : Step(121): len = 429090, overlap = 113
PHY-3002 : Step(122): len = 430538, overlap = 113.531
PHY-3002 : Step(123): len = 433828, overlap = 126.219
PHY-3002 : Step(124): len = 431055, overlap = 132.281
PHY-3002 : Step(125): len = 428723, overlap = 139.062
PHY-3002 : Step(126): len = 428930, overlap = 143.656
PHY-3002 : Step(127): len = 427800, overlap = 152.344
PHY-3002 : Step(128): len = 427066, overlap = 150.969
PHY-3002 : Step(129): len = 428113, overlap = 149.156
PHY-3002 : Step(130): len = 429053, overlap = 150.312
PHY-3002 : Step(131): len = 429441, overlap = 150.594
PHY-3002 : Step(132): len = 431111, overlap = 156.062
PHY-3002 : Step(133): len = 433719, overlap = 144.969
PHY-3002 : Step(134): len = 434098, overlap = 144.719
PHY-3002 : Step(135): len = 434278, overlap = 150.469
PHY-3002 : Step(136): len = 436606, overlap = 152.344
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000678945
PHY-3002 : Step(137): len = 435910, overlap = 149.938
PHY-3002 : Step(138): len = 437210, overlap = 153.969
PHY-3002 : Step(139): len = 440008, overlap = 149.781
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00115939
PHY-3002 : Step(140): len = 439814, overlap = 144.094
PHY-3002 : Step(141): len = 443465, overlap = 129.875
PHY-3002 : Step(142): len = 448481, overlap = 123.438
PHY-3002 : Step(143): len = 448832, overlap = 123.625
PHY-3002 : Step(144): len = 448341, overlap = 120.969
PHY-3002 : Step(145): len = 451589, overlap = 125.594
PHY-3002 : Step(146): len = 454325, overlap = 127.906
PHY-3002 : Step(147): len = 453469, overlap = 125.406
PHY-3002 : Step(148): len = 454385, overlap = 128.75
PHY-3002 : Step(149): len = 455515, overlap = 127.312
PHY-3002 : Step(150): len = 456337, overlap = 123.625
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(151): len = 456313, overlap = 123.906
PHY-3002 : Step(152): len = 458085, overlap = 117.906
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 20/22127.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 527120, over cnt = 2152(6%), over = 11133, worst = 58
PHY-1001 : End global iterations;  1.124264s wall, 1.843750s user + 0.093750s system = 1.937500s CPU (172.3%)

PHY-1001 : Congestion index: top1 = 90.86, top5 = 66.34, top10 = 55.39, top15 = 48.86.
PHY-3001 : End congestion estimation;  1.438446s wall, 2.156250s user + 0.093750s system = 2.250000s CPU (156.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22125 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.041057s wall, 1.031250s user + 0.015625s system = 1.046875s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.82103e-05
PHY-3002 : Step(153): len = 464033, overlap = 397.625
PHY-3002 : Step(154): len = 469251, overlap = 310.906
PHY-3002 : Step(155): len = 465561, overlap = 288.125
PHY-3002 : Step(156): len = 463131, overlap = 269.594
PHY-3002 : Step(157): len = 458967, overlap = 250.156
PHY-3002 : Step(158): len = 454494, overlap = 239.625
PHY-3002 : Step(159): len = 451870, overlap = 249.438
PHY-3002 : Step(160): len = 450842, overlap = 244.344
PHY-3002 : Step(161): len = 450379, overlap = 240.781
PHY-3002 : Step(162): len = 448899, overlap = 235.875
PHY-3002 : Step(163): len = 446497, overlap = 235.531
PHY-3002 : Step(164): len = 445488, overlap = 228.188
PHY-3002 : Step(165): len = 445134, overlap = 228.031
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000196421
PHY-3002 : Step(166): len = 444818, overlap = 209.031
PHY-3002 : Step(167): len = 446821, overlap = 205.469
PHY-3002 : Step(168): len = 447346, overlap = 202.656
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000392841
PHY-3002 : Step(169): len = 448722, overlap = 195.25
PHY-3002 : Step(170): len = 454926, overlap = 185.625
PHY-3002 : Step(171): len = 458903, overlap = 178.312
PHY-3002 : Step(172): len = 460953, overlap = 169.594
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000785683
PHY-3002 : Step(173): len = 460973, overlap = 169.062
PHY-3002 : Step(174): len = 462918, overlap = 165.406
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82447, tnet num: 22125, tinst num: 19770, tnode num: 116465, tedge num: 129554.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.549070s wall, 1.515625s user + 0.031250s system = 1.546875s CPU (99.9%)

RUN-1004 : used memory is 574 MB, reserved memory is 548 MB, peak memory is 708 MB
OPT-1001 : Total overflow 540.03 peak overflow 4.94
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 358/22127.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 549536, over cnt = 2602(7%), over = 9296, worst = 25
PHY-1001 : End global iterations;  1.326902s wall, 2.031250s user + 0.046875s system = 2.078125s CPU (156.6%)

PHY-1001 : Congestion index: top1 = 57.74, top5 = 48.48, top10 = 43.59, top15 = 40.39.
PHY-1001 : End incremental global routing;  1.589384s wall, 2.296875s user + 0.046875s system = 2.343750s CPU (147.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22125 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.103840s wall, 1.093750s user + 0.015625s system = 1.109375s CPU (100.5%)

OPT-1001 : 17 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 56 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19694 has valid locations, 266 needs to be replaced
PHY-3001 : design contains 20019 instances, 5786 luts, 12688 seqs, 1434 slices, 280 macros(1434 instances: 943 mslices 491 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 479712
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17465/22376.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 564768, over cnt = 2638(7%), over = 9356, worst = 26
PHY-1001 : End global iterations;  0.206562s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (128.6%)

PHY-1001 : Congestion index: top1 = 57.93, top5 = 48.55, top10 = 43.70, top15 = 40.59.
PHY-3001 : End congestion estimation;  0.463509s wall, 0.515625s user + 0.015625s system = 0.531250s CPU (114.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83286, tnet num: 22374, tinst num: 20019, tnode num: 117658, tedge num: 130734.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.530467s wall, 1.500000s user + 0.031250s system = 1.531250s CPU (100.1%)

RUN-1004 : used memory is 618 MB, reserved memory is 608 MB, peak memory is 712 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22374 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.649584s wall, 2.593750s user + 0.046875s system = 2.640625s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(175): len = 479389, overlap = 3.125
PHY-3002 : Step(176): len = 480071, overlap = 3.125
PHY-3002 : Step(177): len = 480580, overlap = 3.1875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17521/22376.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 563000, over cnt = 2657(7%), over = 9429, worst = 25
PHY-1001 : End global iterations;  0.180462s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (129.9%)

PHY-1001 : Congestion index: top1 = 57.91, top5 = 48.66, top10 = 43.78, top15 = 40.64.
PHY-3001 : End congestion estimation;  0.499042s wall, 0.546875s user + 0.015625s system = 0.562500s CPU (112.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22374 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.078528s wall, 1.078125s user + 0.000000s system = 1.078125s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000614067
PHY-3002 : Step(178): len = 480540, overlap = 167.969
PHY-3002 : Step(179): len = 481159, overlap = 167.781
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00122813
PHY-3002 : Step(180): len = 481662, overlap = 167.125
PHY-3002 : Step(181): len = 482153, overlap = 167.062
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00235305
PHY-3002 : Step(182): len = 482139, overlap = 167.125
PHY-3002 : Step(183): len = 482339, overlap = 166.875
PHY-3001 : Final: Len = 482339, Over = 166.875
PHY-3001 : End incremental placement;  5.676150s wall, 6.265625s user + 0.203125s system = 6.468750s CPU (114.0%)

OPT-1001 : Total overflow 544.97 peak overflow 4.94
OPT-1001 : End high-fanout net optimization;  9.131404s wall, 10.593750s user + 0.265625s system = 10.859375s CPU (118.9%)

OPT-1001 : Current memory(MB): used = 716, reserve = 696, peak = 732.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17508/22376.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 564936, over cnt = 2618(7%), over = 8906, worst = 25
PHY-1002 : len = 614888, over cnt = 1761(5%), over = 4373, worst = 25
PHY-1002 : len = 644784, over cnt = 1041(2%), over = 2234, worst = 18
PHY-1002 : len = 665216, over cnt = 507(1%), over = 1022, worst = 10
PHY-1002 : len = 683664, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.468393s wall, 2.281250s user + 0.062500s system = 2.343750s CPU (159.6%)

PHY-1001 : Congestion index: top1 = 50.71, top5 = 44.90, top10 = 41.68, top15 = 39.53.
OPT-1001 : End congestion update;  1.736170s wall, 2.546875s user + 0.078125s system = 2.625000s CPU (151.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22374 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.117343s wall, 1.125000s user + 0.000000s system = 1.125000s CPU (100.7%)

OPT-0007 : Start: WNS 4517 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.860421s wall, 3.671875s user + 0.078125s system = 3.750000s CPU (131.1%)

OPT-1001 : Current memory(MB): used = 712, reserve = 692, peak = 732.
OPT-1001 : End physical optimization;  13.880422s wall, 16.281250s user + 0.375000s system = 16.656250s CPU (120.0%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5786 LUT to BLE ...
SYN-4008 : Packed 5786 LUT and 2885 SEQ to BLE.
SYN-4003 : Packing 9803 remaining SEQ's ...
SYN-4005 : Packed 3223 SEQ with LUT/SLICE
SYN-4006 : 183 single LUT's are left
SYN-4006 : 6580 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 12366/14033 primitive instances ...
PHY-3001 : End packing;  2.996098s wall, 3.000000s user + 0.000000s system = 3.000000s CPU (100.1%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8266 instances
RUN-1001 : 4077 mslices, 4076 lslices, 56 pads, 52 brams, 0 dsps
RUN-1001 : There are total 19542 nets
RUN-1001 : 13637 nets have 2 pins
RUN-1001 : 4524 nets have [3 - 5] pins
RUN-1001 : 828 nets have [6 - 10] pins
RUN-1001 : 402 nets have [11 - 20] pins
RUN-1001 : 141 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
PHY-3001 : design contains 8264 instances, 8153 slices, 280 macros(1434 instances: 943 mslices 491 lslices)
PHY-3001 : Cell area utilization is 86%
PHY-3001 : After packing: Len = 499795, Over = 393.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 8054/19542.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 644632, over cnt = 1631(4%), over = 2605, worst = 6
PHY-1002 : len = 649952, over cnt = 1058(3%), over = 1504, worst = 6
PHY-1002 : len = 663128, over cnt = 392(1%), over = 530, worst = 6
PHY-1002 : len = 668504, over cnt = 170(0%), over = 205, worst = 6
PHY-1002 : len = 672040, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.283448s wall, 2.109375s user + 0.062500s system = 2.171875s CPU (169.2%)

PHY-1001 : Congestion index: top1 = 51.42, top5 = 45.04, top10 = 41.44, top15 = 39.13.
PHY-3001 : End congestion estimation;  1.626382s wall, 2.453125s user + 0.062500s system = 2.515625s CPU (154.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68566, tnet num: 19540, tinst num: 8264, tnode num: 93529, tedge num: 112902.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.717951s wall, 1.687500s user + 0.015625s system = 1.703125s CPU (99.1%)

RUN-1004 : used memory is 605 MB, reserved memory is 589 MB, peak memory is 732 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19540 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.709809s wall, 2.656250s user + 0.046875s system = 2.703125s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.30608e-05
PHY-3002 : Step(184): len = 504019, overlap = 377
PHY-3002 : Step(185): len = 503292, overlap = 379.75
PHY-3002 : Step(186): len = 503792, overlap = 393.25
PHY-3002 : Step(187): len = 506587, overlap = 402
PHY-3002 : Step(188): len = 504978, overlap = 401.75
PHY-3002 : Step(189): len = 505250, overlap = 397.25
PHY-3002 : Step(190): len = 503100, overlap = 400.75
PHY-3002 : Step(191): len = 501532, overlap = 400.5
PHY-3002 : Step(192): len = 500120, overlap = 395.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000106122
PHY-3002 : Step(193): len = 505278, overlap = 380.5
PHY-3002 : Step(194): len = 508038, overlap = 380.25
PHY-3002 : Step(195): len = 507805, overlap = 373.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00020588
PHY-3002 : Step(196): len = 515628, overlap = 357.5
PHY-3002 : Step(197): len = 524673, overlap = 348
PHY-3002 : Step(198): len = 523825, overlap = 355.75
PHY-3002 : Step(199): len = 522978, overlap = 362.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.799323s wall, 0.937500s user + 0.937500s system = 1.875000s CPU (234.6%)

PHY-3001 : Trial Legalized: Len = 634674
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 696/19542.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 727936, over cnt = 2526(7%), over = 4148, worst = 7
PHY-1002 : len = 740408, over cnt = 1735(4%), over = 2549, worst = 7
PHY-1002 : len = 757704, over cnt = 868(2%), over = 1242, worst = 6
PHY-1002 : len = 774592, over cnt = 166(0%), over = 225, worst = 4
PHY-1002 : len = 778512, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.149538s wall, 3.500000s user + 0.078125s system = 3.578125s CPU (166.5%)

PHY-1001 : Congestion index: top1 = 50.50, top5 = 45.51, top10 = 42.93, top15 = 41.22.
PHY-3001 : End congestion estimation;  2.523322s wall, 3.843750s user + 0.093750s system = 3.937500s CPU (156.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19540 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.961671s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (100.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000189402
PHY-3002 : Step(200): len = 590225, overlap = 84.25
PHY-3002 : Step(201): len = 572449, overlap = 125.5
PHY-3002 : Step(202): len = 560542, overlap = 175.5
PHY-3002 : Step(203): len = 552990, overlap = 213.75
PHY-3002 : Step(204): len = 548769, overlap = 232.5
PHY-3002 : Step(205): len = 545993, overlap = 255.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000378804
PHY-3002 : Step(206): len = 549378, overlap = 248.25
PHY-3002 : Step(207): len = 553322, overlap = 251
PHY-3002 : Step(208): len = 555820, overlap = 255.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(209): len = 557806, overlap = 252.5
PHY-3002 : Step(210): len = 563574, overlap = 241
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.037588s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (83.1%)

PHY-3001 : Legalized: Len = 607414, Over = 0
PHY-3001 : Spreading special nets. 47 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.092389s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (101.5%)

PHY-3001 : 73 instances has been re-located, deltaX = 29, deltaY = 52, maxDist = 5.
PHY-3001 : Final: Len = 609130, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68566, tnet num: 19540, tinst num: 8264, tnode num: 93529, tedge num: 112902.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.983182s wall, 1.953125s user + 0.015625s system = 1.968750s CPU (99.3%)

RUN-1004 : used memory is 605 MB, reserved memory is 582 MB, peak memory is 732 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 4170/19542.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 711920, over cnt = 2311(6%), over = 3686, worst = 8
PHY-1002 : len = 723088, over cnt = 1469(4%), over = 2061, worst = 5
PHY-1002 : len = 739400, over cnt = 600(1%), over = 817, worst = 5
PHY-1002 : len = 749960, over cnt = 142(0%), over = 205, worst = 5
PHY-1002 : len = 753072, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.913695s wall, 3.109375s user + 0.093750s system = 3.203125s CPU (167.4%)

PHY-1001 : Congestion index: top1 = 46.83, top5 = 43.03, top10 = 40.89, top15 = 39.31.
PHY-1001 : End incremental global routing;  2.243181s wall, 3.437500s user + 0.093750s system = 3.531250s CPU (157.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19540 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.992503s wall, 0.968750s user + 0.015625s system = 0.984375s CPU (99.2%)

OPT-1001 : 4 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 56 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8201 has valid locations, 26 needs to be replaced
PHY-3001 : design contains 8286 instances, 8175 slices, 280 macros(1434 instances: 943 mslices 491 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 614520
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17691/19570.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 758800, over cnt = 55(0%), over = 64, worst = 3
PHY-1002 : len = 758912, over cnt = 31(0%), over = 33, worst = 2
PHY-1002 : len = 759152, over cnt = 13(0%), over = 13, worst = 1
PHY-1002 : len = 759272, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 759432, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.759544s wall, 0.750000s user + 0.031250s system = 0.781250s CPU (102.9%)

PHY-1001 : Congestion index: top1 = 46.85, top5 = 43.16, top10 = 41.06, top15 = 39.46.
PHY-3001 : End congestion estimation;  1.085997s wall, 1.062500s user + 0.046875s system = 1.109375s CPU (102.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68752, tnet num: 19568, tinst num: 8286, tnode num: 93755, tedge num: 113125.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.975901s wall, 1.968750s user + 0.015625s system = 1.984375s CPU (100.4%)

RUN-1004 : used memory is 669 MB, reserved memory is 660 MB, peak memory is 732 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19568 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.976880s wall, 2.968750s user + 0.015625s system = 2.984375s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(211): len = 613346, overlap = 0.5
PHY-3002 : Step(212): len = 612550, overlap = 0.5
PHY-3002 : Step(213): len = 612500, overlap = 1.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17677/19570.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 755912, over cnt = 44(0%), over = 60, worst = 4
PHY-1002 : len = 755880, over cnt = 31(0%), over = 34, worst = 3
PHY-1002 : len = 756176, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 756296, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 756392, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.767477s wall, 0.843750s user + 0.046875s system = 0.890625s CPU (116.0%)

PHY-1001 : Congestion index: top1 = 46.96, top5 = 43.19, top10 = 41.08, top15 = 39.50.
PHY-3001 : End congestion estimation;  1.087476s wall, 1.140625s user + 0.046875s system = 1.187500s CPU (109.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19568 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.969055s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(214): len = 612361, overlap = 3
PHY-3002 : Step(215): len = 612418, overlap = 3.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006766s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (230.9%)

PHY-3001 : Legalized: Len = 612544, Over = 0
PHY-3001 : Spreading special nets. 1 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.077081s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (101.4%)

PHY-3001 : 1 instances has been re-located, deltaX = 2, deltaY = 0, maxDist = 2.
PHY-3001 : Final: Len = 612544, Over = 0
PHY-3001 : End incremental placement;  6.797465s wall, 6.859375s user + 0.203125s system = 7.062500s CPU (103.9%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.550326s wall, 11.828125s user + 0.312500s system = 12.140625s CPU (115.1%)

OPT-1001 : Current memory(MB): used = 725, reserve = 708, peak = 732.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17679/19570.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 756480, over cnt = 23(0%), over = 28, worst = 2
PHY-1002 : len = 756512, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 756544, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 756576, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 756672, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.754609s wall, 0.750000s user + 0.015625s system = 0.765625s CPU (101.5%)

PHY-1001 : Congestion index: top1 = 46.94, top5 = 43.03, top10 = 40.92, top15 = 39.36.
OPT-1001 : End congestion update;  1.075792s wall, 1.078125s user + 0.015625s system = 1.093750s CPU (101.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19568 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.815121s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (99.7%)

OPT-0007 : Start: WNS 4598 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.896213s wall, 1.906250s user + 0.015625s system = 1.921875s CPU (101.4%)

OPT-1001 : Current memory(MB): used = 725, reserve = 708, peak = 732.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19568 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.813299s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (99.9%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17713/19570.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 756672, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.146868s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (106.4%)

PHY-1001 : Congestion index: top1 = 46.94, top5 = 43.03, top10 = 40.92, top15 = 39.36.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19568 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.810504s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (100.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4598 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 46.620690
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4598ps with logic level 8 
RUN-1001 :       #2 path slack 4600ps with logic level 8 
RUN-1001 :       #3 path slack 4698ps with logic level 8 
RUN-1001 :       #4 path slack 4698ps with logic level 8 
OPT-1001 : End physical optimization;  16.896912s wall, 18.250000s user + 0.375000s system = 18.625000s CPU (110.2%)

RUN-1003 : finish command "place" in  74.280850s wall, 143.250000s user + 7.671875s system = 150.921875s CPU (203.2%)

RUN-1004 : used memory is 605 MB, reserved memory is 581 MB, peak memory is 732 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.754680s wall, 3.000000s user + 0.000000s system = 3.000000s CPU (171.0%)

RUN-1004 : used memory is 605 MB, reserved memory is 582 MB, peak memory is 732 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8288 instances
RUN-1001 : 4077 mslices, 4098 lslices, 56 pads, 52 brams, 0 dsps
RUN-1001 : There are total 19570 nets
RUN-1001 : 13639 nets have 2 pins
RUN-1001 : 4528 nets have [3 - 5] pins
RUN-1001 : 841 nets have [6 - 10] pins
RUN-1001 : 409 nets have [11 - 20] pins
RUN-1001 : 143 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68752, tnet num: 19568, tinst num: 8286, tnode num: 93755, tedge num: 113125.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.704101s wall, 1.703125s user + 0.000000s system = 1.703125s CPU (99.9%)

RUN-1004 : used memory is 614 MB, reserved memory is 603 MB, peak memory is 732 MB
PHY-1001 : 4077 mslices, 4098 lslices, 56 pads, 52 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19568 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 691200, over cnt = 2440(6%), over = 4111, worst = 7
PHY-1002 : len = 705400, over cnt = 1675(4%), over = 2483, worst = 7
PHY-1002 : len = 723000, over cnt = 804(2%), over = 1168, worst = 5
PHY-1002 : len = 742872, over cnt = 12(0%), over = 14, worst = 2
PHY-1002 : len = 743208, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.842150s wall, 3.062500s user + 0.109375s system = 3.171875s CPU (172.2%)

PHY-1001 : Congestion index: top1 = 47.13, top5 = 43.17, top10 = 40.75, top15 = 39.13.
PHY-1001 : End global routing;  2.200355s wall, 3.421875s user + 0.109375s system = 3.531250s CPU (160.5%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 708, reserve = 698, peak = 732.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 978, reserve = 967, peak = 978.
PHY-1001 : End build detailed router design. 4.539850s wall, 4.484375s user + 0.062500s system = 4.546875s CPU (100.2%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 196264, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.956866s wall, 0.937500s user + 0.015625s system = 0.953125s CPU (99.6%)

PHY-1001 : Current memory(MB): used = 1015, reserve = 1004, peak = 1015.
PHY-1001 : End phase 1; 0.963755s wall, 0.953125s user + 0.015625s system = 0.968750s CPU (100.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.80401e+06, over cnt = 1428(0%), over = 1435, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1031, reserve = 1020, peak = 1031.
PHY-1001 : End initial routed; 21.352349s wall, 49.234375s user + 0.437500s system = 49.671875s CPU (232.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18358(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.047   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.495074s wall, 3.484375s user + 0.015625s system = 3.500000s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1039, reserve = 1028, peak = 1039.
PHY-1001 : End phase 2; 24.847613s wall, 52.718750s user + 0.453125s system = 53.171875s CPU (214.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.80401e+06, over cnt = 1428(0%), over = 1435, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.262080s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (95.4%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.79303e+06, over cnt = 511(0%), over = 511, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.773171s wall, 1.390625s user + 0.000000s system = 1.390625s CPU (179.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.79344e+06, over cnt = 84(0%), over = 84, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.423243s wall, 0.671875s user + 0.015625s system = 0.687500s CPU (162.4%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.79426e+06, over cnt = 15(0%), over = 15, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.236596s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (118.9%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.79454e+06, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.174557s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (107.4%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.79462e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.172254s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (99.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18358(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.759   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.482523s wall, 3.484375s user + 0.000000s system = 3.484375s CPU (100.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 307 feed throughs used by 272 nets
PHY-1001 : End commit to database; 2.221003s wall, 2.187500s user + 0.031250s system = 2.218750s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 1130, reserve = 1122, peak = 1130.
PHY-1001 : End phase 3; 8.257197s wall, 9.171875s user + 0.046875s system = 9.218750s CPU (111.6%)

PHY-1003 : Routed, final wirelength = 1.79462e+06
PHY-1001 : Current memory(MB): used = 1134, reserve = 1127, peak = 1134.
PHY-1001 : End export database. 0.062007s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.8%)

PHY-1001 : End detail routing;  39.108318s wall, 67.812500s user + 0.578125s system = 68.390625s CPU (174.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68752, tnet num: 19568, tinst num: 8286, tnode num: 93755, tedge num: 113125.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.691406s wall, 1.687500s user + 0.000000s system = 1.687500s CPU (99.8%)

RUN-1004 : used memory is 1068 MB, reserved memory is 1068 MB, peak memory is 1134 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  47.447755s wall, 77.343750s user + 0.718750s system = 78.062500s CPU (164.5%)

RUN-1004 : used memory is 1068 MB, reserved memory is 1068 MB, peak memory is 1134 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8800   out of  19600   44.90%
#reg                    12796   out of  19600   65.29%
#le                     15350
  #lut only              2554   out of  15350   16.64%
  #reg only              6550   out of  15350   42.67%
  #lut&reg               6246   out of  15350   40.69%
#dsp                        0   out of     29    0.00%
#bram                      52   out of     64   81.25%
  #bram9k                  52
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       56   out of    188   29.79%
  #ireg                     9
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6974
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          192
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT        E11        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT        A13        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT        F10        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         E8        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         E7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT        A10        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C6        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         D6        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         C7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT        E12        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         A9        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         A7        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H13        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J11        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT        E13        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         R7        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         M7        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         P7        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         T8        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         P8        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         N8        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M9        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         T9        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         R9        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         T6        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         P9        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         N9        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT        P11        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         T5        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         B2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        L10        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        E15        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        R14        LVCMOS33           8            N/A            OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT         R1        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT        M12        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        K12        LVCMOS33           8            N/A            NONE       
    TXD_RMC        OUTPUT         C3        LVCMOS33           8            N/A            NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        R15        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E6        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15350  |7366    |1434    |12838   |52      |0       |
|  AnyFog_dataX                      |AnyFog          |232    |108     |22      |189     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |93     |67      |22      |54      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |235    |105     |22      |189     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |97     |70      |22      |55      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |224    |129     |22      |184     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |87     |58      |22      |51      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |3503   |835     |34      |3400    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |746    |85      |5       |729     |0       |0       |
|    STADOP_com2                     |STADOP          |555    |56      |0       |550     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |64     |40      |14      |40      |0       |0       |
|    head_com2                       |uniheading      |266    |84      |5       |249     |0       |0       |
|    rmc_com2                        |Gprmc           |161    |57      |0       |145     |0       |0       |
|    uart_com2                       |Agrica          |1427   |229     |10      |1403    |0       |0       |
|  DATA                              |Data_Processing |8636   |4332    |1062    |6928    |0       |0       |
|    DIV_Dtemp                       |Divider         |812    |287     |84      |689     |0       |0       |
|    DIV_Utemp                       |Divider         |661    |323     |84      |529     |0       |0       |
|    DIV_accX                        |Divider         |626    |337     |84      |494     |0       |0       |
|    DIV_accY                        |Divider         |621    |345     |111     |451     |0       |0       |
|    DIV_accZ                        |Divider         |715    |384     |132     |507     |0       |0       |
|    DIV_rateX                       |Divider         |649    |402     |132     |436     |0       |0       |
|    DIV_rateY                       |Divider         |536    |357     |132     |329     |0       |0       |
|    DIV_rateZ                       |Divider         |575    |378     |132     |370     |0       |0       |
|    genclk                          |genclk          |91     |54      |20      |57      |0       |0       |
|  FMC                               |FMC_Ctrl        |536    |482     |43      |371     |0       |0       |
|  IIC                               |I2C_master      |261    |218     |11      |238     |0       |0       |
|  IMU_CTRL                          |SCHA634         |876    |651     |61      |744     |0       |0       |
|    CtrlData                        |CtrlData        |438    |387     |47      |326     |0       |0       |
|      usms                          |Time_1ms        |29     |24      |5       |21      |0       |0       |
|    SPIM                            |SPI_SCHA634     |438    |264     |14      |418     |0       |0       |
|  POWER                             |POWER_EN        |102    |44      |38      |42      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |737    |462     |119     |503     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |737    |462     |119     |503     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |316    |174     |0       |301     |0       |0       |
|        reg_inst                    |register        |315    |174     |0       |300     |0       |0       |
|        tap_inst                    |tap             |1      |0       |0       |1       |0       |0       |
|      trigger_inst                  |trigger         |421    |288     |119     |202     |0       |0       |
|        bus_inst                    |bus_top         |178    |116     |62      |64      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |26     |16      |10      |9       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |52     |34      |18      |19      |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |100    |66      |34      |36      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |166    |126     |29      |108     |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13582  
    #2          2       3562   
    #3          3        673   
    #4          4        293   
    #5        5-10       926   
    #6        11-50      443   
    #7       51-100      24    
    #8       101-500      4    
    #9        >500        2    
  Average     2.15             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.091993s wall, 3.562500s user + 0.015625s system = 3.578125s CPU (171.0%)

RUN-1004 : used memory is 1068 MB, reserved memory is 1068 MB, peak memory is 1134 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68752, tnet num: 19568, tinst num: 8286, tnode num: 93755, tedge num: 113125.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.706931s wall, 1.703125s user + 0.000000s system = 1.703125s CPU (99.8%)

RUN-1004 : used memory is 1070 MB, reserved memory is 1070 MB, peak memory is 1134 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19568 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.486611s wall, 1.468750s user + 0.015625s system = 1.484375s CPU (99.8%)

RUN-1004 : used memory is 1076 MB, reserved memory is 1074 MB, peak memory is 1134 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 28b752c1c0e7d7ba92ac5beed3c46754b7b56ac64969e4b0149cfdbc1d295011 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8286
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19570, pip num: 150796
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 307
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3238 valid insts, and 419234 bits set as '1'.
BIT-1004 : the usercode register value: 00000000000000001110111010000001
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.935633s wall, 131.031250s user + 0.218750s system = 131.250000s CPU (1014.6%)

RUN-1004 : used memory is 1198 MB, reserved memory is 1183 MB, peak memory is 1313 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250527_102732.log"

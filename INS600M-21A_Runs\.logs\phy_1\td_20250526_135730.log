============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Mon May 26 13:57:30 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(247)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(252)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(273)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(278)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(421)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(428)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(435)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(442)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(449)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(456)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(463)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(470)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(647)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(652)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(680)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(685)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(919)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(926)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(933)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(940)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(947)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(952)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(959)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(966)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(973)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(980)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(514)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
RUN-1001 : Project manager successfully analyzed 21 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.546219s wall, 1.484375s user + 4.062500s system = 5.546875s CPU (100.0%)

RUN-1004 : used memory is 78 MB, reserved memory is 39 MB, peak memory is 89 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.939755s wall, 1.765625s user + 0.140625s system = 1.906250s CPU (98.3%)

RUN-1004 : used memory is 298 MB, reserved memory is 267 MB, peak memory is 301 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 7 view nodes, 47 trigger nets, 47 data nets.
KIT-1004 : Chipwatcher code = 0001111110010110
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=144) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=144) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=144)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=144)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22828/31 useful/useless nets, 19611/17 useful/useless insts
SYN-1016 : Merged 37 instances.
SYN-1032 : 22452/22 useful/useless nets, 20070/18 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 4 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 4 mux instances.
SYN-1015 : Optimize round 1, 443 better
SYN-1014 : Optimize round 2
SYN-1032 : 22080/60 useful/useless nets, 19698/64 useful/useless insts
SYN-1015 : Optimize round 2, 128 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.829749s wall, 2.750000s user + 0.078125s system = 2.828125s CPU (99.9%)

RUN-1004 : used memory is 325 MB, reserved memory is 293 MB, peak memory is 327 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22140/367 useful/useless nets, 19799/52 useful/useless insts
SYN-1016 : Merged 57 instances.
SYN-2571 : Optimize after map_dsp, round 1, 476 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 44 instances.
SYN-2501 : Optimize round 1, 90 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 18 macro adder
SYN-1019 : Optimized 17 mux instances.
SYN-1016 : Merged 12 instances.
SYN-1032 : 22611/5 useful/useless nets, 20270/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82994, tnet num: 22611, tinst num: 20269, tnode num: 116612, tedge num: 129259.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.164015s wall, 1.109375s user + 0.031250s system = 1.140625s CPU (98.0%)

RUN-1004 : used memory is 467 MB, reserved memory is 436 MB, peak memory is 467 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22611 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 257 (3.41), #lev = 7 (1.78)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 257 (3.41), #lev = 7 (1.78)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 596 instances into 257 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 450 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 140 adder to BLE ...
SYN-4008 : Packed 140 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.451296s wall, 4.406250s user + 0.031250s system = 4.437500s CPU (99.7%)

RUN-1004 : used memory is 352 MB, reserved memory is 324 MB, peak memory is 577 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.597490s wall, 7.437500s user + 0.140625s system = 7.578125s CPU (99.7%)

RUN-1004 : used memory is 353 MB, reserved memory is 325 MB, peak memory is 577 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/rmc_com2/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/rmc_com2/GPRMC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/rmc_com2/GPRMC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/rmc_com2/GPRMC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/rmc_com2/GPRMC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/rmc_com2/GPRMC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/rmc_com2/GPRMC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/rmc_com2/GPRMC_data[0]
SYN-5055 WARNING: The kept net COM2/rx_err will be merged to another kept net COM2/rmc_com2/GPRMC_err
SYN-5055 WARNING: The kept net FMC/Error_state[0] will be merged to another kept net COM2/rmc_com2/GPRMC_sub_state_cnt0[3]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (306 clock/control pins, 0 other pins).
SYN-4027 : Net DATA/DIV_Dtemp/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net DATA/DIV_Dtemp/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19597 instances
RUN-0007 : 5599 luts, 12471 seqs, 933 mslices, 491 lslices, 56 pads, 42 brams, 0 dsps
RUN-1001 : There are total 21962 nets
RUN-1001 : 16477 nets have 2 pins
RUN-1001 : 4348 nets have [3 - 5] pins
RUN-1001 : 790 nets have [6 - 10] pins
RUN-1001 : 220 nets have [11 - 20] pins
RUN-1001 : 103 nets have [21 - 99] pins
RUN-1001 : 24 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4742     
RUN-1001 :   No   |  No   |  Yes  |     628     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    6531     
RUN-1001 :   Yes  |  No   |  Yes  |     470     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19595 instances, 5599 luts, 12471 seqs, 1424 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1131 pins
PHY-3001 : Huge net DATA/done_div with 1650 pins
PHY-0007 : Cell area utilization is 64%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81525, tnet num: 21960, tinst num: 19595, tnode num: 115241, tedge num: 128070.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.173310s wall, 1.140625s user + 0.046875s system = 1.187500s CPU (101.2%)

RUN-1004 : used memory is 527 MB, reserved memory is 500 MB, peak memory is 577 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21960 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.008419s wall, 1.953125s user + 0.062500s system = 2.015625s CPU (100.4%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.43232e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19595.
PHY-3001 : Level 1 #clusters 2051.
PHY-3001 : End clustering;  0.145848s wall, 0.250000s user + 0.031250s system = 0.281250s CPU (192.8%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 64%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 890982, overlap = 671.219
PHY-3002 : Step(2): len = 806284, overlap = 722.656
PHY-3002 : Step(3): len = 532840, overlap = 926.844
PHY-3002 : Step(4): len = 478986, overlap = 972.438
PHY-3002 : Step(5): len = 383245, overlap = 1065
PHY-3002 : Step(6): len = 335031, overlap = 1147.22
PHY-3002 : Step(7): len = 280170, overlap = 1261.28
PHY-3002 : Step(8): len = 244459, overlap = 1305.78
PHY-3002 : Step(9): len = 219658, overlap = 1335.88
PHY-3002 : Step(10): len = 198994, overlap = 1375.78
PHY-3002 : Step(11): len = 181782, overlap = 1436.06
PHY-3002 : Step(12): len = 164495, overlap = 1446.81
PHY-3002 : Step(13): len = 152265, overlap = 1468.62
PHY-3002 : Step(14): len = 135937, overlap = 1514.81
PHY-3002 : Step(15): len = 126588, overlap = 1534.19
PHY-3002 : Step(16): len = 115968, overlap = 1558.09
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.59768e-07
PHY-3002 : Step(17): len = 121558, overlap = 1545.06
PHY-3002 : Step(18): len = 150659, overlap = 1489.38
PHY-3002 : Step(19): len = 165712, overlap = 1366.06
PHY-3002 : Step(20): len = 180022, overlap = 1288.53
PHY-3002 : Step(21): len = 181746, overlap = 1263.81
PHY-3002 : Step(22): len = 178366, overlap = 1210.56
PHY-3002 : Step(23): len = 175482, overlap = 1184.12
PHY-3002 : Step(24): len = 171617, overlap = 1186.19
PHY-3002 : Step(25): len = 169699, overlap = 1174.12
PHY-3002 : Step(26): len = 166005, overlap = 1161.38
PHY-3002 : Step(27): len = 164383, overlap = 1160.53
PHY-3002 : Step(28): len = 162283, overlap = 1156.53
PHY-3002 : Step(29): len = 161944, overlap = 1170.84
PHY-3002 : Step(30): len = 160124, overlap = 1174.97
PHY-3002 : Step(31): len = 159322, overlap = 1165.78
PHY-3002 : Step(32): len = 158934, overlap = 1163.41
PHY-3002 : Step(33): len = 159179, overlap = 1159.81
PHY-3002 : Step(34): len = 159334, overlap = 1168.59
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 1.91954e-06
PHY-3002 : Step(35): len = 166098, overlap = 1114.25
PHY-3002 : Step(36): len = 181066, overlap = 1053.75
PHY-3002 : Step(37): len = 184604, overlap = 1017
PHY-3002 : Step(38): len = 186714, overlap = 996.594
PHY-3002 : Step(39): len = 186894, overlap = 984.031
PHY-3002 : Step(40): len = 187230, overlap = 956.188
PHY-3002 : Step(41): len = 185751, overlap = 959.094
PHY-3002 : Step(42): len = 185489, overlap = 969.125
PHY-3002 : Step(43): len = 184398, overlap = 978.188
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 3.83907e-06
PHY-3002 : Step(44): len = 194118, overlap = 952.25
PHY-3002 : Step(45): len = 209642, overlap = 899.031
PHY-3002 : Step(46): len = 213721, overlap = 840.781
PHY-3002 : Step(47): len = 215664, overlap = 814.875
PHY-3002 : Step(48): len = 214982, overlap = 795.844
PHY-3002 : Step(49): len = 214671, overlap = 778.25
PHY-3002 : Step(50): len = 213417, overlap = 773.844
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 7.67814e-06
PHY-3002 : Step(51): len = 226268, overlap = 730.188
PHY-3002 : Step(52): len = 243060, overlap = 617.344
PHY-3002 : Step(53): len = 249180, overlap = 565.188
PHY-3002 : Step(54): len = 251644, overlap = 561.094
PHY-3002 : Step(55): len = 249999, overlap = 569.094
PHY-3002 : Step(56): len = 247723, overlap = 573.375
PHY-3002 : Step(57): len = 245976, overlap = 587.219
PHY-3002 : Step(58): len = 244264, overlap = 606.938
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.53563e-05
PHY-3002 : Step(59): len = 254113, overlap = 579.062
PHY-3002 : Step(60): len = 266602, overlap = 550.344
PHY-3002 : Step(61): len = 269788, overlap = 513.688
PHY-3002 : Step(62): len = 272535, overlap = 490.031
PHY-3002 : Step(63): len = 272312, overlap = 482.781
PHY-3002 : Step(64): len = 271813, overlap = 482.344
PHY-3002 : Step(65): len = 270755, overlap = 493.219
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.07126e-05
PHY-3002 : Step(66): len = 281113, overlap = 448.438
PHY-3002 : Step(67): len = 291420, overlap = 407.969
PHY-3002 : Step(68): len = 294370, overlap = 380.188
PHY-3002 : Step(69): len = 295542, overlap = 366.844
PHY-3002 : Step(70): len = 294566, overlap = 352.688
PHY-3002 : Step(71): len = 293879, overlap = 338.094
PHY-3002 : Step(72): len = 292928, overlap = 348.406
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 6.14251e-05
PHY-3002 : Step(73): len = 299523, overlap = 337.719
PHY-3002 : Step(74): len = 307013, overlap = 324.438
PHY-3002 : Step(75): len = 310223, overlap = 299.344
PHY-3002 : Step(76): len = 311869, overlap = 297.281
PHY-3002 : Step(77): len = 310610, overlap = 299.406
PHY-3002 : Step(78): len = 309857, overlap = 306.656
PHY-3002 : Step(79): len = 306810, overlap = 314.094
PHY-3002 : Step(80): len = 305752, overlap = 326.25
PHY-3002 : Step(81): len = 304592, overlap = 337.75
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.00012285
PHY-3002 : Step(82): len = 308825, overlap = 324.562
PHY-3002 : Step(83): len = 314636, overlap = 317.562
PHY-3002 : Step(84): len = 317934, overlap = 313
PHY-3002 : Step(85): len = 318847, overlap = 297.469
PHY-3002 : Step(86): len = 318435, overlap = 309.281
PHY-3002 : Step(87): len = 317037, overlap = 292.562
PHY-3002 : Step(88): len = 315504, overlap = 297.406
PHY-3002 : Step(89): len = 316032, overlap = 296.938
PHY-3002 : Step(90): len = 317216, overlap = 298.5
PHY-3002 : Step(91): len = 316328, overlap = 303.562
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000233188
PHY-3002 : Step(92): len = 318624, overlap = 295.219
PHY-3002 : Step(93): len = 322890, overlap = 286.688
PHY-3002 : Step(94): len = 323904, overlap = 284.75
PHY-3002 : Step(95): len = 325238, overlap = 284.719
PHY-3002 : Step(96): len = 325484, overlap = 273.938
PHY-3002 : Step(97): len = 326235, overlap = 268.031
PHY-3002 : Step(98): len = 326135, overlap = 265.062
PHY-3002 : Step(99): len = 327052, overlap = 259.781
PHY-3002 : Step(100): len = 327048, overlap = 266.875
PHY-3002 : Step(101): len = 327549, overlap = 260.5
PHY-3002 : Step(102): len = 326985, overlap = 268.219
PHY-3002 : Step(103): len = 327014, overlap = 267.688
PHY-3002 : Step(104): len = 326911, overlap = 261.031
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000444065
PHY-3002 : Step(105): len = 328643, overlap = 261.469
PHY-3002 : Step(106): len = 334013, overlap = 228.562
PHY-3002 : Step(107): len = 336352, overlap = 218.344
PHY-3002 : Step(108): len = 337133, overlap = 200.656
PHY-3002 : Step(109): len = 337543, overlap = 204.969
PHY-3002 : Step(110): len = 338063, overlap = 206.344
PHY-3002 : Step(111): len = 338382, overlap = 211.406
PHY-3002 : Step(112): len = 338281, overlap = 217.5
PHY-3002 : Step(113): len = 338598, overlap = 218.031
PHY-3002 : Step(114): len = 337699, overlap = 219.375
PHY-3002 : Step(115): len = 337129, overlap = 223.438
PHY-3002 : Step(116): len = 337128, overlap = 227.812
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.009263s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21962.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 446552, over cnt = 1213(3%), over = 5308, worst = 35
PHY-1001 : End global iterations;  0.861305s wall, 1.093750s user + 0.046875s system = 1.140625s CPU (132.4%)

PHY-1001 : Congestion index: top1 = 69.09, top5 = 52.45, top10 = 43.04, top15 = 37.46.
PHY-3001 : End congestion estimation;  1.108693s wall, 1.328125s user + 0.062500s system = 1.390625s CPU (125.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21960 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.883329s wall, 0.812500s user + 0.046875s system = 0.859375s CPU (97.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000111982
PHY-3002 : Step(117): len = 376128, overlap = 179.969
PHY-3002 : Step(118): len = 388450, overlap = 174.656
PHY-3002 : Step(119): len = 393394, overlap = 163.062
PHY-3002 : Step(120): len = 392970, overlap = 162.344
PHY-3002 : Step(121): len = 394895, overlap = 162.531
PHY-3002 : Step(122): len = 396207, overlap = 142.5
PHY-3002 : Step(123): len = 401330, overlap = 127.969
PHY-3002 : Step(124): len = 402062, overlap = 116.562
PHY-3002 : Step(125): len = 403997, overlap = 101.656
PHY-3002 : Step(126): len = 406352, overlap = 102.062
PHY-3002 : Step(127): len = 403820, overlap = 106.688
PHY-3002 : Step(128): len = 403824, overlap = 112.875
PHY-3002 : Step(129): len = 402555, overlap = 109.75
PHY-3002 : Step(130): len = 401495, overlap = 109.75
PHY-3002 : Step(131): len = 401373, overlap = 109.625
PHY-3002 : Step(132): len = 400852, overlap = 106.938
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000223965
PHY-3002 : Step(133): len = 401434, overlap = 102.219
PHY-3002 : Step(134): len = 403126, overlap = 102.938
PHY-3002 : Step(135): len = 406777, overlap = 101.188
PHY-3002 : Step(136): len = 409932, overlap = 101.125
PHY-3002 : Step(137): len = 410015, overlap = 97.2188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(138): len = 410777, overlap = 98.9375
PHY-3002 : Step(139): len = 415446, overlap = 97.2812
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 107/21962.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 494040, over cnt = 2245(6%), over = 9314, worst = 26
PHY-1001 : End global iterations;  1.185169s wall, 1.656250s user + 0.031250s system = 1.687500s CPU (142.4%)

PHY-1001 : Congestion index: top1 = 73.34, top5 = 56.12, top10 = 47.48, top15 = 42.64.
PHY-3001 : End congestion estimation;  1.430019s wall, 1.906250s user + 0.031250s system = 1.937500s CPU (135.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21960 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.007391s wall, 0.968750s user + 0.031250s system = 1.000000s CPU (99.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.45876e-05
PHY-3002 : Step(140): len = 418213, overlap = 344.5
PHY-3002 : Step(141): len = 427857, overlap = 305.281
PHY-3002 : Step(142): len = 422397, overlap = 278.594
PHY-3002 : Step(143): len = 420339, overlap = 255.906
PHY-3002 : Step(144): len = 421987, overlap = 224.094
PHY-3002 : Step(145): len = 423117, overlap = 206.531
PHY-3002 : Step(146): len = 420953, overlap = 206.5
PHY-3002 : Step(147): len = 421715, overlap = 206.688
PHY-3002 : Step(148): len = 421610, overlap = 198.031
PHY-3002 : Step(149): len = 419725, overlap = 199.656
PHY-3002 : Step(150): len = 420104, overlap = 195.125
PHY-3002 : Step(151): len = 418950, overlap = 191.219
PHY-3002 : Step(152): len = 417663, overlap = 190.625
PHY-3002 : Step(153): len = 417250, overlap = 192.031
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000189175
PHY-3002 : Step(154): len = 417566, overlap = 178.531
PHY-3002 : Step(155): len = 418736, overlap = 174.719
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000366346
PHY-3002 : Step(156): len = 422617, overlap = 177.344
PHY-3002 : Step(157): len = 429159, overlap = 153.875
PHY-3002 : Step(158): len = 430848, overlap = 148.188
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81525, tnet num: 21960, tinst num: 19595, tnode num: 115241, tedge num: 128070.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.505804s wall, 1.468750s user + 0.015625s system = 1.484375s CPU (98.6%)

RUN-1004 : used memory is 566 MB, reserved memory is 542 MB, peak memory is 702 MB
OPT-1001 : Total overflow 520.25 peak overflow 3.94
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 698/21962.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 521200, over cnt = 2539(7%), over = 8843, worst = 24
PHY-1001 : End global iterations;  1.223405s wall, 1.765625s user + 0.015625s system = 1.781250s CPU (145.6%)

PHY-1001 : Congestion index: top1 = 56.51, top5 = 46.93, top10 = 41.97, top15 = 39.01.
PHY-1001 : End incremental global routing;  1.455539s wall, 2.015625s user + 0.015625s system = 2.031250s CPU (139.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21960 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.933745s wall, 0.906250s user + 0.000000s system = 0.906250s CPU (97.1%)

OPT-1001 : 16 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 56 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19520 has valid locations, 259 needs to be replaced
PHY-3001 : design contains 19838 instances, 5703 luts, 12610 seqs, 1424 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 448209
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16616/22205.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 533936, over cnt = 2582(7%), over = 8920, worst = 24
PHY-1001 : End global iterations;  0.208553s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (97.4%)

PHY-1001 : Congestion index: top1 = 56.53, top5 = 47.06, top10 = 42.14, top15 = 39.22.
PHY-3001 : End congestion estimation;  0.453272s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (100.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82342, tnet num: 22203, tinst num: 19838, tnode num: 116390, tedge num: 129218.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.481119s wall, 1.468750s user + 0.031250s system = 1.500000s CPU (101.3%)

RUN-1004 : used memory is 611 MB, reserved memory is 605 MB, peak memory is 704 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22203 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.540591s wall, 2.531250s user + 0.031250s system = 2.562500s CPU (100.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(159): len = 448103, overlap = 5.0625
PHY-3002 : Step(160): len = 449503, overlap = 4.875
PHY-3002 : Step(161): len = 450135, overlap = 4.75
PHY-3002 : Step(162): len = 450641, overlap = 5.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 16641/22205.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 531768, over cnt = 2564(7%), over = 8959, worst = 24
PHY-1001 : End global iterations;  0.180302s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (112.7%)

PHY-1001 : Congestion index: top1 = 56.77, top5 = 47.13, top10 = 42.26, top15 = 39.36.
PHY-3001 : End congestion estimation;  0.418137s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (108.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22203 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.084238s wall, 1.046875s user + 0.031250s system = 1.078125s CPU (99.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000486132
PHY-3002 : Step(163): len = 450602, overlap = 150.844
PHY-3002 : Step(164): len = 450717, overlap = 150.844
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000972264
PHY-3002 : Step(165): len = 450855, overlap = 150.281
PHY-3002 : Step(166): len = 451041, overlap = 150.156
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00194453
PHY-3002 : Step(167): len = 451343, overlap = 149.812
PHY-3002 : Step(168): len = 451693, overlap = 149.719
PHY-3001 : Final: Len = 451693, Over = 149.719
PHY-3001 : End incremental placement;  5.474951s wall, 5.656250s user + 0.234375s system = 5.890625s CPU (107.6%)

OPT-1001 : Total overflow 523.88 peak overflow 3.94
OPT-1001 : End high-fanout net optimization;  8.374613s wall, 9.218750s user + 0.250000s system = 9.468750s CPU (113.1%)

OPT-1001 : Current memory(MB): used = 708, reserve = 688, peak = 724.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16638/22205.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 535168, over cnt = 2500(7%), over = 8309, worst = 24
PHY-1002 : len = 583344, over cnt = 1815(5%), over = 4147, worst = 19
PHY-1002 : len = 620912, over cnt = 690(1%), over = 1337, worst = 15
PHY-1002 : len = 628056, over cnt = 401(1%), over = 832, worst = 15
PHY-1002 : len = 641616, over cnt = 10(0%), over = 10, worst = 1
PHY-1001 : End global iterations;  1.422605s wall, 2.156250s user + 0.031250s system = 2.187500s CPU (153.8%)

PHY-1001 : Congestion index: top1 = 48.84, top5 = 43.12, top10 = 40.15, top15 = 38.14.
OPT-1001 : End congestion update;  1.690503s wall, 2.437500s user + 0.031250s system = 2.468750s CPU (146.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22203 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.883711s wall, 0.875000s user + 0.000000s system = 0.875000s CPU (99.0%)

OPT-0007 : Start: WNS 4277 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.579868s wall, 3.312500s user + 0.031250s system = 3.343750s CPU (129.6%)

OPT-1001 : Current memory(MB): used = 705, reserve = 684, peak = 724.
OPT-1001 : End physical optimization;  12.786221s wall, 14.468750s user + 0.328125s system = 14.796875s CPU (115.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5703 LUT to BLE ...
SYN-4008 : Packed 5703 LUT and 2859 SEQ to BLE.
SYN-4003 : Packing 9751 remaining SEQ's ...
SYN-4005 : Packed 3129 SEQ with LUT/SLICE
SYN-4006 : 206 single LUT's are left
SYN-4006 : 6622 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 12325/13972 primitive instances ...
PHY-3001 : End packing;  3.124295s wall, 3.125000s user + 0.000000s system = 3.125000s CPU (100.0%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8317 instances
RUN-1001 : 4107 mslices, 4107 lslices, 56 pads, 42 brams, 0 dsps
RUN-1001 : There are total 19390 nets
RUN-1001 : 13538 nets have 2 pins
RUN-1001 : 4483 nets have [3 - 5] pins
RUN-1001 : 854 nets have [6 - 10] pins
RUN-1001 : 349 nets have [11 - 20] pins
RUN-1001 : 156 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
PHY-3001 : design contains 8315 instances, 8214 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Cell area utilization is 87%
PHY-3001 : After packing: Len = 473961, Over = 378.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7675/19390.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 613400, over cnt = 1672(4%), over = 2652, worst = 10
PHY-1002 : len = 620352, over cnt = 1102(3%), over = 1592, worst = 6
PHY-1002 : len = 631984, over cnt = 502(1%), over = 670, worst = 5
PHY-1002 : len = 640168, over cnt = 143(0%), over = 194, worst = 5
PHY-1002 : len = 643600, over cnt = 8(0%), over = 8, worst = 1
PHY-1001 : End global iterations;  1.100290s wall, 1.671875s user + 0.015625s system = 1.687500s CPU (153.4%)

PHY-1001 : Congestion index: top1 = 49.44, top5 = 43.12, top10 = 39.99, top15 = 37.88.
PHY-3001 : End congestion estimation;  1.390440s wall, 1.968750s user + 0.015625s system = 1.984375s CPU (142.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68056, tnet num: 19388, tinst num: 8315, tnode num: 92972, tedge num: 112094.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.648912s wall, 1.640625s user + 0.000000s system = 1.640625s CPU (99.5%)

RUN-1004 : used memory is 599 MB, reserved memory is 582 MB, peak memory is 724 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19388 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.601371s wall, 2.562500s user + 0.031250s system = 2.593750s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.74881e-05
PHY-3002 : Step(169): len = 479758, overlap = 385.25
PHY-3002 : Step(170): len = 482466, overlap = 388.25
PHY-3002 : Step(171): len = 482634, overlap = 390.75
PHY-3002 : Step(172): len = 484650, overlap = 400.75
PHY-3002 : Step(173): len = 484481, overlap = 410.75
PHY-3002 : Step(174): len = 484483, overlap = 404
PHY-3002 : Step(175): len = 483866, overlap = 404.5
PHY-3002 : Step(176): len = 483199, overlap = 407.75
PHY-3002 : Step(177): len = 482972, overlap = 408.25
PHY-3002 : Step(178): len = 482394, overlap = 408.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.49761e-05
PHY-3002 : Step(179): len = 486370, overlap = 392
PHY-3002 : Step(180): len = 489920, overlap = 388.5
PHY-3002 : Step(181): len = 489893, overlap = 383.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000183875
PHY-3002 : Step(182): len = 498941, overlap = 362.25
PHY-3002 : Step(183): len = 508488, overlap = 334.75
PHY-3002 : Step(184): len = 506437, overlap = 332
PHY-3002 : Step(185): len = 505089, overlap = 335.25
PHY-3002 : Step(186): len = 504695, overlap = 335.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.541309s wall, 0.500000s user + 0.687500s system = 1.187500s CPU (219.4%)

PHY-3001 : Trial Legalized: Len = 629261
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 554/19390.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 723296, over cnt = 2455(6%), over = 3964, worst = 7
PHY-1002 : len = 736880, over cnt = 1473(4%), over = 2140, worst = 7
PHY-1002 : len = 757192, over cnt = 499(1%), over = 722, worst = 5
PHY-1002 : len = 763720, over cnt = 232(0%), over = 330, worst = 5
PHY-1002 : len = 771152, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.850446s wall, 2.937500s user + 0.078125s system = 3.015625s CPU (163.0%)

PHY-1001 : Congestion index: top1 = 51.36, top5 = 46.51, top10 = 43.74, top15 = 41.73.
PHY-3001 : End congestion estimation;  2.196019s wall, 3.281250s user + 0.078125s system = 3.359375s CPU (153.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19388 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.856454s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000190192
PHY-3002 : Step(187): len = 583380, overlap = 92.75
PHY-3002 : Step(188): len = 563558, overlap = 128.75
PHY-3002 : Step(189): len = 550514, overlap = 172.25
PHY-3002 : Step(190): len = 542081, overlap = 203.75
PHY-3002 : Step(191): len = 537004, overlap = 232.25
PHY-3002 : Step(192): len = 533718, overlap = 249
PHY-3002 : Step(193): len = 532191, overlap = 259.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000380384
PHY-3002 : Step(194): len = 537097, overlap = 251.75
PHY-3002 : Step(195): len = 541965, overlap = 248
PHY-3002 : Step(196): len = 543250, overlap = 247.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(197): len = 546377, overlap = 242.25
PHY-3002 : Step(198): len = 551942, overlap = 239.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.030371s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (102.9%)

PHY-3001 : Legalized: Len = 595349, Over = 0
PHY-3001 : Spreading special nets. 60 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.074961s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (104.2%)

PHY-3001 : 84 instances has been re-located, deltaX = 26, deltaY = 49, maxDist = 2.
PHY-3001 : Final: Len = 596561, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68056, tnet num: 19388, tinst num: 8315, tnode num: 92972, tedge num: 112094.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.870811s wall, 1.875000s user + 0.000000s system = 1.875000s CPU (100.2%)

RUN-1004 : used memory is 601 MB, reserved memory is 577 MB, peak memory is 724 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3592/19390.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 696704, over cnt = 2323(6%), over = 3566, worst = 9
PHY-1002 : len = 708792, over cnt = 1417(4%), over = 1879, worst = 5
PHY-1002 : len = 725736, over cnt = 473(1%), over = 606, worst = 4
PHY-1002 : len = 731752, over cnt = 204(0%), over = 259, worst = 4
PHY-1002 : len = 736184, over cnt = 1(0%), over = 1, worst = 1
PHY-1001 : End global iterations;  1.780592s wall, 2.734375s user + 0.046875s system = 2.781250s CPU (156.2%)

PHY-1001 : Congestion index: top1 = 48.90, top5 = 44.03, top10 = 41.30, top15 = 39.48.
PHY-1001 : End incremental global routing;  2.056068s wall, 3.015625s user + 0.046875s system = 3.062500s CPU (148.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19388 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.866999s wall, 0.843750s user + 0.031250s system = 0.875000s CPU (100.9%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 56 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8255 has valid locations, 10 needs to be replaced
PHY-3001 : design contains 8324 instances, 8223 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 600256
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17340/19398.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 741048, over cnt = 25(0%), over = 34, worst = 6
PHY-1002 : len = 740944, over cnt = 25(0%), over = 28, worst = 2
PHY-1002 : len = 741120, over cnt = 11(0%), over = 12, worst = 2
PHY-1002 : len = 741328, over cnt = 2(0%), over = 2, worst = 1
PHY-1001 : End global iterations;  0.538264s wall, 0.562500s user + 0.000000s system = 0.562500s CPU (104.5%)

PHY-1001 : Congestion index: top1 = 49.66, top5 = 44.53, top10 = 41.63, top15 = 39.72.
PHY-3001 : End congestion estimation;  0.821901s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (100.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68127, tnet num: 19396, tinst num: 8324, tnode num: 93052, tedge num: 112173.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.855438s wall, 1.859375s user + 0.000000s system = 1.859375s CPU (100.2%)

RUN-1004 : used memory is 668 MB, reserved memory is 666 MB, peak memory is 724 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19396 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.852836s wall, 2.828125s user + 0.031250s system = 2.859375s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(199): len = 599703, overlap = 0.5
PHY-3002 : Step(200): len = 599662, overlap = 0.75
PHY-3002 : Step(201): len = 599459, overlap = 1
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17334/19398.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 738608, over cnt = 27(0%), over = 37, worst = 4
PHY-1002 : len = 738512, over cnt = 20(0%), over = 20, worst = 1
PHY-1002 : len = 738688, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 738704, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 738784, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.737312s wall, 0.781250s user + 0.015625s system = 0.796875s CPU (108.1%)

PHY-1001 : Congestion index: top1 = 49.66, top5 = 44.49, top10 = 41.59, top15 = 39.68.
PHY-3001 : End congestion estimation;  1.030358s wall, 1.078125s user + 0.015625s system = 1.093750s CPU (106.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19396 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.863339s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000601694
PHY-3002 : Step(202): len = 599537, overlap = 1.25
PHY-3002 : Step(203): len = 599497, overlap = 1.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005934s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 599599, Over = 0
PHY-3001 : End spreading;  0.074465s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (104.9%)

PHY-3001 : Final: Len = 599599, Over = 0
PHY-3001 : End incremental placement;  6.181280s wall, 6.250000s user + 0.093750s system = 6.343750s CPU (102.6%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  9.564398s wall, 10.562500s user + 0.171875s system = 10.734375s CPU (112.2%)

OPT-1001 : Current memory(MB): used = 722, reserve = 706, peak = 726.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17337/19398.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 739624, over cnt = 19(0%), over = 40, worst = 5
PHY-1002 : len = 739520, over cnt = 26(0%), over = 29, worst = 4
PHY-1002 : len = 739624, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 739792, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 739904, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.814607s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (97.8%)

PHY-1001 : Congestion index: top1 = 50.19, top5 = 44.61, top10 = 41.66, top15 = 39.73.
OPT-1001 : End congestion update;  1.139516s wall, 1.093750s user + 0.000000s system = 1.093750s CPU (96.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19396 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.864618s wall, 0.875000s user + 0.000000s system = 0.875000s CPU (101.2%)

OPT-0007 : Start: WNS 4169 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  2.009411s wall, 1.968750s user + 0.000000s system = 1.968750s CPU (98.0%)

OPT-1001 : Current memory(MB): used = 722, reserve = 706, peak = 726.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19396 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.834580s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (101.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17349/19398.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 739904, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.129005s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (109.0%)

PHY-1001 : Congestion index: top1 = 50.19, top5 = 44.61, top10 = 41.66, top15 = 39.73.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19396 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.735677s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (99.8%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4169 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 49.896552
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4169ps with logic level 5 
RUN-1001 :       #2 path slack 4171ps with logic level 3 
OPT-1001 : End physical optimization;  15.724914s wall, 16.671875s user + 0.187500s system = 16.859375s CPU (107.2%)

RUN-1003 : finish command "place" in  68.558178s wall, 103.750000s user + 5.375000s system = 109.125000s CPU (159.2%)

RUN-1004 : used memory is 636 MB, reserved memory is 614 MB, peak memory is 726 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.618326s wall, 2.703125s user + 0.031250s system = 2.734375s CPU (169.0%)

RUN-1004 : used memory is 636 MB, reserved memory is 614 MB, peak memory is 726 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8326 instances
RUN-1001 : 4116 mslices, 4107 lslices, 56 pads, 42 brams, 0 dsps
RUN-1001 : There are total 19398 nets
RUN-1001 : 13535 nets have 2 pins
RUN-1001 : 4484 nets have [3 - 5] pins
RUN-1001 : 857 nets have [6 - 10] pins
RUN-1001 : 356 nets have [11 - 20] pins
RUN-1001 : 156 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68127, tnet num: 19396, tinst num: 8324, tnode num: 93052, tedge num: 112173.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.001465s wall, 2.015625s user + 0.000000s system = 2.015625s CPU (100.7%)

RUN-1004 : used memory is 618 MB, reserved memory is 600 MB, peak memory is 726 MB
PHY-1001 : 4116 mslices, 4107 lslices, 56 pads, 42 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19396 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 678176, over cnt = 2397(6%), over = 3869, worst = 9
PHY-1002 : len = 692896, over cnt = 1513(4%), over = 2095, worst = 6
PHY-1002 : len = 707968, over cnt = 731(2%), over = 983, worst = 6
PHY-1002 : len = 724608, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 725136, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.822096s wall, 3.109375s user + 0.015625s system = 3.125000s CPU (171.5%)

PHY-1001 : Congestion index: top1 = 49.68, top5 = 44.16, top10 = 41.15, top15 = 39.34.
PHY-1001 : End global routing;  2.165242s wall, 3.453125s user + 0.015625s system = 3.468750s CPU (160.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 700, reserve = 688, peak = 726.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net DATA/DIV_Dtemp/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 972, reserve = 959, peak = 972.
PHY-1001 : End build detailed router design. 4.620485s wall, 4.593750s user + 0.015625s system = 4.609375s CPU (99.8%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 194688, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.901694s wall, 0.906250s user + 0.000000s system = 0.906250s CPU (100.5%)

PHY-1001 : Current memory(MB): used = 1009, reserve = 997, peak = 1009.
PHY-1001 : End phase 1; 0.909576s wall, 0.906250s user + 0.000000s system = 0.906250s CPU (99.6%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 56% nets.
PHY-1001 : Routed 66% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.78853e+06, over cnt = 1371(0%), over = 1376, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1023, reserve = 1011, peak = 1023.
PHY-1001 : End initial routed; 21.040199s wall, 50.328125s user + 0.531250s system = 50.859375s CPU (241.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18198(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.115   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.615296s wall, 3.609375s user + 0.000000s system = 3.609375s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 1032, reserve = 1020, peak = 1032.
PHY-1001 : End phase 2; 24.655628s wall, 53.937500s user + 0.531250s system = 54.468750s CPU (220.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.78853e+06, over cnt = 1371(0%), over = 1376, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.247026s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (94.9%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.76947e+06, over cnt = 458(0%), over = 458, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.965597s wall, 1.796875s user + 0.000000s system = 1.796875s CPU (186.1%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.76914e+06, over cnt = 109(0%), over = 109, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.478568s wall, 0.578125s user + 0.000000s system = 0.578125s CPU (120.8%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.7704e+06, over cnt = 20(0%), over = 20, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.282589s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (132.7%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.77074e+06, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.191166s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (106.3%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.7708e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.190856s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (106.4%)

PHY-1001 : ===== DR Iter 6 =====
PHY-1022 : len = 1.7708e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 6; 0.214245s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (102.1%)

PHY-1001 : ===== DR Iter 7 =====
PHY-1022 : len = 1.7708e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 7; 0.347957s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (98.8%)

PHY-1001 : ===== DR Iter 8 =====
PHY-1022 : len = 1.7708e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 8; 0.147249s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (116.7%)

PHY-1001 : ==== DR Iter 9 ====
PHY-1022 : len = 1.7708e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 9; 0.146869s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (95.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18198(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.808   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.219577s wall, 3.218750s user + 0.000000s system = 3.218750s CPU (100.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 397 feed throughs used by 316 nets
PHY-1001 : End commit to database; 2.151515s wall, 2.125000s user + 0.031250s system = 2.156250s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 1121, reserve = 1111, peak = 1121.
PHY-1001 : End phase 3; 9.116817s wall, 10.125000s user + 0.046875s system = 10.171875s CPU (111.6%)

PHY-1003 : Routed, final wirelength = 1.7708e+06
PHY-1001 : Current memory(MB): used = 1125, reserve = 1116, peak = 1125.
PHY-1001 : End export database. 0.058009s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (107.7%)

PHY-1001 : End detail routing;  39.769641s wall, 70.031250s user + 0.593750s system = 70.625000s CPU (177.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68127, tnet num: 19396, tinst num: 8324, tnode num: 93052, tedge num: 112173.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.638843s wall, 1.640625s user + 0.000000s system = 1.640625s CPU (100.1%)

RUN-1004 : used memory is 1061 MB, reserved memory is 1057 MB, peak memory is 1125 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  48.188768s wall, 79.718750s user + 0.640625s system = 80.359375s CPU (166.8%)

RUN-1004 : used memory is 1060 MB, reserved memory is 1058 MB, peak memory is 1125 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8699   out of  19600   44.38%
#reg                    12707   out of  19600   64.83%
#le                     15294
  #lut only              2587   out of  15294   16.92%
  #reg only              6595   out of  15294   43.12%
  #lut&reg               6112   out of  15294   39.96%
#dsp                        0   out of     29    0.00%
#bram                      42   out of     64   65.62%
  #bram9k                  42
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       56   out of    188   29.79%
  #ireg                     9
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet              Type               DriverType         Driver                    Fanout
#1        DATA/DIV_Dtemp/clk    GCLK               pll                CLK100M/pll_inst.clkc0    7025
#2        config_inst_syn_9     GCLK               config             config_inst.jtck          178
#3        clk_in_dup_1          GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT        E11        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT        A13        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT        F10        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         E8        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         E7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT        A10        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C6        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         D6        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         C7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT        E12        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         A9        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         A7        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H13        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J11        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT        E13        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         R7        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         M7        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         P7        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         T8        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         P8        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         N8        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M9        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         T9        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         R9        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         T6        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         P9        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         N9        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT        P11        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         T5        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         B2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        L10        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        E15        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        R14        LVCMOS33           8            N/A            OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT         R1        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT        M12        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        K12        LVCMOS33           8            N/A            NONE       
    TXD_RMC        OUTPUT         C3        LVCMOS33           8            N/A            NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        R15        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E6        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15294  |7275    |1424    |12749   |42      |0       |
|  AnyFog_dataX                      |AnyFog          |205    |69      |22      |170     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |82     |49      |22      |47      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |219    |93      |22      |174     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |97     |71      |22      |52      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |199    |83      |22      |161     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |84     |58      |22      |46      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |3495   |798     |34      |3409    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |726    |68      |5       |716     |0       |0       |
|    STADOP_com2                     |STADOP          |552    |57      |0       |546     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |45      |14      |40      |0       |0       |
|    head_com2                       |uniheading      |267    |62      |5       |253     |0       |0       |
|    rmc_com2                        |Gprmc           |155    |66      |0       |149     |0       |0       |
|    uart_com2                       |Agrica          |1435   |205     |10      |1410    |0       |0       |
|  DATA                              |Data_Processing |8633   |4363    |1062    |6922    |0       |0       |
|    DIV_Dtemp                       |Divider         |809    |345     |84      |661     |0       |0       |
|    DIV_Utemp                       |Divider         |622    |304     |84      |494     |0       |0       |
|    DIV_accX                        |Divider         |615    |283     |84      |483     |0       |0       |
|    DIV_accY                        |Divider         |639    |317     |111     |470     |0       |0       |
|    DIV_accZ                        |Divider         |641    |365     |132     |433     |0       |0       |
|    DIV_rateX                       |Divider         |647    |364     |132     |439     |0       |0       |
|    DIV_rateY                       |Divider         |652    |387     |132     |444     |0       |0       |
|    DIV_rateZ                       |Divider         |585    |400     |132     |376     |0       |0       |
|    genclk                          |genclk          |82     |56      |20      |48      |0       |0       |
|  FMC                               |FMC_Ctrl        |514    |459     |43      |368     |0       |0       |
|  IIC                               |I2C_master      |318    |287     |11      |262     |0       |0       |
|  IMU_CTRL                          |SCHA634         |947    |648     |61      |741     |0       |0       |
|    CtrlData                        |CtrlData        |482    |432     |47      |325     |0       |0       |
|      usms                          |Time_1ms        |29     |24      |5       |21      |0       |0       |
|    SPIM                            |SPI_SCHA634     |465    |216     |14      |416     |0       |0       |
|  POWER                             |POWER_EN        |100    |50      |38      |41      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |655    |425     |109     |450     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |655    |425     |109     |450     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |301    |192     |0       |284     |0       |0       |
|        reg_inst                    |register        |298    |189     |0       |281     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |354    |233     |109     |166     |0       |0       |
|        bus_inst                    |bus_top         |145    |93      |52      |54      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |24     |14      |10      |7       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |3      |3       |0       |3       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |14     |8       |6       |5       |0       |0       |
|          BUS_DETECTOR[5]$bus_nodes |bus_det         |51     |33      |18      |18      |0       |0       |
|          BUS_DETECTOR[6]$bus_nodes |bus_det         |51     |33      |18      |19      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |129    |94      |29      |79      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13478  
    #2          2       3561   
    #3          3        666   
    #4          4        257   
    #5        5-10       899   
    #6        11-50      453   
    #7       51-100      17    
    #8       101-500      4    
    #9        >500        2    
  Average     2.14             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  1.988367s wall, 3.406250s user + 0.031250s system = 3.437500s CPU (172.9%)

RUN-1004 : used memory is 1060 MB, reserved memory is 1058 MB, peak memory is 1125 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68127, tnet num: 19396, tinst num: 8324, tnode num: 93052, tedge num: 112173.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.642931s wall, 1.640625s user + 0.000000s system = 1.640625s CPU (99.9%)

RUN-1004 : used memory is 1062 MB, reserved memory is 1060 MB, peak memory is 1125 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19396 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.291137s wall, 1.296875s user + 0.000000s system = 1.296875s CPU (100.4%)

RUN-1004 : used memory is 1067 MB, reserved memory is 1064 MB, peak memory is 1125 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 8f169df8acfcb835781910b1ac29267cd80e16d8a5e1b8cf9d032ec93c0a2865 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8324
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19398, pip num: 149135
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 397
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3216 valid insts, and 415482 bits set as '1'.
BIT-1004 : the usercode register value: 00000000110101000001111110010110
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  10.831917s wall, 106.171875s user + 0.140625s system = 106.312500s CPU (981.5%)

RUN-1004 : used memory is 1195 MB, reserved memory is 1181 MB, peak memory is 1310 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250526_135730.log"

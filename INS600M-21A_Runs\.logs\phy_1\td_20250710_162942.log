============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Jul 10 16:29:43 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(520)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  7.287943s wall, 2.000000s user + 5.250000s system = 7.250000s CPU (99.5%)

RUN-1004 : used memory is 80 MB, reserved memory is 40 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  2.578785s wall, 2.468750s user + 0.078125s system = 2.546875s CPU (98.8%)

RUN-1004 : used memory is 302 MB, reserved memory is 270 MB, peak memory is 305 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "read_sdc -ip Asys_fifo8x8 ../../al_ip/Asys_fifo8x8.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 95176475279360"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 95176475279360"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  9.6999999999999993 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 9.6999999999999993"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  9.6999999999999993 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 9.6999999999999993"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 12 view nodes, 49 trigger nets, 49 data nets.
KIT-1004 : Chipwatcher code = 1011110111100010
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=12,BUS_DIN_NUM=49,BUS_CTRL_NUM=146,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb011110,32'sb011111,32'sb0100111,32'sb0101111,32'sb0110000},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01011000,32'sb01011110,32'sb01110010,32'sb010000110,32'sb010001100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=168) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=168) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=12,BUS_DIN_NUM=49,BUS_CTRL_NUM=146,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb011110,32'sb011111,32'sb0100111,32'sb0101111,32'sb0110000},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01011000,32'sb01011110,32'sb01110010,32'sb010000110,32'sb010001100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=12,BUS_DIN_NUM=49,BUS_CTRL_NUM=146,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb011110,32'sb011111,32'sb0100111,32'sb0101111,32'sb0110000},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01011000,32'sb01011110,32'sb01110010,32'sb010000110,32'sb010001100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=12,BUS_DIN_NUM=49,BUS_CTRL_NUM=146,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb011110,32'sb011111,32'sb0100111,32'sb0101111,32'sb0110000},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01011000,32'sb01011110,32'sb01110010,32'sb010000110,32'sb010001100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=168)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=168)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=12,BUS_DIN_NUM=49,BUS_CTRL_NUM=146,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb011110,32'sb011111,32'sb0100111,32'sb0101111,32'sb0110000},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01011000,32'sb01011110,32'sb01110010,32'sb010000110,32'sb010001100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=12,BUS_DIN_NUM=49,BUS_CTRL_NUM=146,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb011110,32'sb011111,32'sb0100111,32'sb0101111,32'sb0110000},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01011000,32'sb01011110,32'sb01110010,32'sb010000110,32'sb010001100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22939/48 useful/useless nets, 19562/31 useful/useless insts
SYN-1016 : Merged 57 instances.
SYN-1032 : 22495/30 useful/useless nets, 20051/26 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 5 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 5 mux instances.
SYN-1015 : Optimize round 1, 500 better
SYN-1014 : Optimize round 2
SYN-1032 : 22062/75 useful/useless nets, 19618/80 useful/useless insts
SYN-1015 : Optimize round 2, 160 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  3.275113s wall, 3.203125s user + 0.078125s system = 3.281250s CPU (100.2%)

RUN-1004 : used memory is 330 MB, reserved memory is 297 MB, peak memory is 332 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22122/369 useful/useless nets, 19721/54 useful/useless insts
SYN-1016 : Merged 57 instances.
SYN-2571 : Optimize after map_dsp, round 1, 480 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 43 instances.
SYN-2501 : Optimize round 1, 87 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 20 macro adder
SYN-1019 : Optimized 21 mux instances.
SYN-1016 : Merged 17 instances.
SYN-1032 : 22611/5 useful/useless nets, 20210/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82667, tnet num: 22611, tinst num: 20209, tnode num: 115946, tedge num: 128984.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.596243s wall, 1.562500s user + 0.031250s system = 1.593750s CPU (99.8%)

RUN-1004 : used memory is 471 MB, reserved memory is 439 MB, peak memory is 471 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22611 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 273 (3.51), #lev = 7 (1.87)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 273 (3.51), #lev = 7 (1.87)
SYN-3001 : Logic optimization runtime opt =   0.05 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 706 instances into 273 LUTs, name keeping = 71%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 505 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 132 adder to BLE ...
SYN-4008 : Packed 132 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  6.127934s wall, 6.046875s user + 0.078125s system = 6.125000s CPU (100.0%)

RUN-1004 : used memory is 354 MB, reserved memory is 318 MB, peak memory is 579 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  9.851886s wall, 9.640625s user + 0.218750s system = 9.859375s CPU (100.1%)

RUN-1004 : used memory is 354 MB, reserved memory is 318 MB, peak memory is 579 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/uart_com2/AGRIC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/uart_com2/AGRIC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/uart_com2/AGRIC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/uart_com2/AGRIC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/uart_com2/AGRIC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/uart_com2/AGRIC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/uart_com2/AGRIC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/uart_com2/AGRIC_data[0]
SYN-5055 WARNING: The kept net COM3/rmc_com3/GPRMC_end will be merged to another kept net COM3/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM3/rmc_com3/GPRMC_sta will be merged to another kept net COM3/GNRMC/GPRMC_sta
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (350 clock/control pins, 0 other pins).
SYN-4027 : Net COM3/GNRMC/u_fifo/wr_to_rd_cross_inst/secondary_clk_i is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net COM3/GNRMC/u_fifo/wr_to_rd_cross_inst/secondary_clk_i as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19321 instances
RUN-0007 : 5499 luts, 12253 seqs, 956 mslices, 502 lslices, 60 pads, 46 brams, 0 dsps
RUN-1001 : There are total 21746 nets
RUN-1001 : 16293 nets have 2 pins
RUN-1001 : 4270 nets have [3 - 5] pins
RUN-1001 : 818 nets have [6 - 10] pins
RUN-1001 : 240 nets have [11 - 20] pins
RUN-1001 : 103 nets have [21 - 99] pins
RUN-1001 : 22 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4769     
RUN-1001 :   No   |  No   |  Yes  |     741     
RUN-1001 :   No   |  Yes  |  No   |     85      
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     524     
RUN-1001 :   Yes  |  Yes  |  No   |     51      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 124
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19319 instances, 5499 luts, 12253 seqs, 1458 slices, 291 macros(1458 instances: 956 mslices 502 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 63%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80702, tnet num: 21744, tinst num: 19319, tnode num: 113882, tedge num: 127038.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.552624s wall, 1.500000s user + 0.062500s system = 1.562500s CPU (100.6%)

RUN-1004 : used memory is 530 MB, reserved memory is 502 MB, peak memory is 579 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21744 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.823415s wall, 2.750000s user + 0.078125s system = 2.828125s CPU (100.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.70063e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19319.
PHY-3001 : Level 1 #clusters 2135.
PHY-3001 : End clustering;  0.187720s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (141.5%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 63%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 850196, overlap = 683
PHY-3002 : Step(2): len = 785119, overlap = 736.969
PHY-3002 : Step(3): len = 510858, overlap = 920.312
PHY-3002 : Step(4): len = 446076, overlap = 944.375
PHY-3002 : Step(5): len = 356891, overlap = 1052.47
PHY-3002 : Step(6): len = 314815, overlap = 1107.81
PHY-3002 : Step(7): len = 266027, overlap = 1154.88
PHY-3002 : Step(8): len = 237634, overlap = 1203
PHY-3002 : Step(9): len = 210616, overlap = 1262.75
PHY-3002 : Step(10): len = 194403, overlap = 1291.75
PHY-3002 : Step(11): len = 174244, overlap = 1326.25
PHY-3002 : Step(12): len = 164994, overlap = 1340.22
PHY-3002 : Step(13): len = 147338, overlap = 1351.12
PHY-3002 : Step(14): len = 139795, overlap = 1370.56
PHY-3002 : Step(15): len = 126361, overlap = 1384.47
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.3551e-07
PHY-3002 : Step(16): len = 130587, overlap = 1381.5
PHY-3002 : Step(17): len = 159751, overlap = 1351
PHY-3002 : Step(18): len = 168733, overlap = 1248.44
PHY-3002 : Step(19): len = 169740, overlap = 1202.31
PHY-3002 : Step(20): len = 169807, overlap = 1163.12
PHY-3002 : Step(21): len = 166982, overlap = 1171.16
PHY-3002 : Step(22): len = 161777, overlap = 1183.16
PHY-3002 : Step(23): len = 159400, overlap = 1182.69
PHY-3002 : Step(24): len = 156496, overlap = 1182.03
PHY-3002 : Step(25): len = 154282, overlap = 1174.22
PHY-3002 : Step(26): len = 151711, overlap = 1178.56
PHY-3002 : Step(27): len = 150721, overlap = 1180.12
PHY-3002 : Step(28): len = 149382, overlap = 1180.41
PHY-3002 : Step(29): len = 147709, overlap = 1178.38
PHY-3002 : Step(30): len = 147510, overlap = 1178.12
PHY-3002 : Step(31): len = 146979, overlap = 1190.47
PHY-3002 : Step(32): len = 146144, overlap = 1172.38
PHY-3002 : Step(33): len = 145042, overlap = 1161.84
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 1.67102e-06
PHY-3002 : Step(34): len = 152317, overlap = 1116.38
PHY-3002 : Step(35): len = 165635, overlap = 1035.75
PHY-3002 : Step(36): len = 168118, overlap = 997.938
PHY-3002 : Step(37): len = 170714, overlap = 979
PHY-3002 : Step(38): len = 171239, overlap = 977.031
PHY-3002 : Step(39): len = 172355, overlap = 976.875
PHY-3002 : Step(40): len = 170447, overlap = 963.406
PHY-3002 : Step(41): len = 170405, overlap = 951.406
PHY-3002 : Step(42): len = 169139, overlap = 951.719
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 3.34204e-06
PHY-3002 : Step(43): len = 179998, overlap = 934.656
PHY-3002 : Step(44): len = 191592, overlap = 908.25
PHY-3002 : Step(45): len = 195672, overlap = 861.781
PHY-3002 : Step(46): len = 198216, overlap = 825.781
PHY-3002 : Step(47): len = 198598, overlap = 813.938
PHY-3002 : Step(48): len = 198588, overlap = 808.031
PHY-3002 : Step(49): len = 197427, overlap = 822.688
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 6.68408e-06
PHY-3002 : Step(50): len = 209113, overlap = 762.75
PHY-3002 : Step(51): len = 224479, overlap = 670.875
PHY-3002 : Step(52): len = 228656, overlap = 634.312
PHY-3002 : Step(53): len = 230233, overlap = 650.656
PHY-3002 : Step(54): len = 229266, overlap = 623.125
PHY-3002 : Step(55): len = 228209, overlap = 626.875
PHY-3002 : Step(56): len = 227133, overlap = 620.438
PHY-3002 : Step(57): len = 226840, overlap = 613.625
PHY-3002 : Step(58): len = 227494, overlap = 601.781
PHY-3002 : Step(59): len = 227196, overlap = 602.312
PHY-3002 : Step(60): len = 226025, overlap = 612.406
PHY-3002 : Step(61): len = 225065, overlap = 613.094
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.33682e-05
PHY-3002 : Step(62): len = 234163, overlap = 587.625
PHY-3002 : Step(63): len = 248448, overlap = 526.125
PHY-3002 : Step(64): len = 252549, overlap = 501.344
PHY-3002 : Step(65): len = 254233, overlap = 478.281
PHY-3002 : Step(66): len = 251895, overlap = 481.781
PHY-3002 : Step(67): len = 250542, overlap = 484.781
PHY-3002 : Step(68): len = 249080, overlap = 487.688
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 2.67363e-05
PHY-3002 : Step(69): len = 259654, overlap = 456.438
PHY-3002 : Step(70): len = 270075, overlap = 433.094
PHY-3002 : Step(71): len = 273204, overlap = 398
PHY-3002 : Step(72): len = 273063, overlap = 391.469
PHY-3002 : Step(73): len = 272197, overlap = 387.438
PHY-3002 : Step(74): len = 270673, overlap = 381.156
PHY-3002 : Step(75): len = 269816, overlap = 361.781
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 5.34726e-05
PHY-3002 : Step(76): len = 276698, overlap = 353.156
PHY-3002 : Step(77): len = 285840, overlap = 337.25
PHY-3002 : Step(78): len = 289556, overlap = 313.469
PHY-3002 : Step(79): len = 291169, overlap = 298.594
PHY-3002 : Step(80): len = 289496, overlap = 291.406
PHY-3002 : Step(81): len = 288212, overlap = 292.625
PHY-3002 : Step(82): len = 286452, overlap = 299.719
PHY-3002 : Step(83): len = 286291, overlap = 304.875
PHY-3002 : Step(84): len = 285406, overlap = 296.344
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000106945
PHY-3002 : Step(85): len = 290833, overlap = 300.375
PHY-3002 : Step(86): len = 298410, overlap = 288.094
PHY-3002 : Step(87): len = 302461, overlap = 278.812
PHY-3002 : Step(88): len = 305280, overlap = 263.844
PHY-3002 : Step(89): len = 304347, overlap = 272.25
PHY-3002 : Step(90): len = 301579, overlap = 285.75
PHY-3002 : Step(91): len = 299688, overlap = 275.656
PHY-3002 : Step(92): len = 299810, overlap = 288.312
PHY-3002 : Step(93): len = 300279, overlap = 286.438
PHY-3002 : Step(94): len = 300720, overlap = 293.625
PHY-3002 : Step(95): len = 299758, overlap = 308.062
PHY-3002 : Step(96): len = 298809, overlap = 307.5
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000193297
PHY-3002 : Step(97): len = 302386, overlap = 308.531
PHY-3002 : Step(98): len = 305124, overlap = 291.969
PHY-3002 : Step(99): len = 304832, overlap = 291.688
PHY-3002 : Step(100): len = 306141, overlap = 286.5
PHY-3002 : Step(101): len = 306389, overlap = 274.5
PHY-3002 : Step(102): len = 307570, overlap = 269.781
PHY-3002 : Step(103): len = 306629, overlap = 269.844
PHY-3002 : Step(104): len = 307094, overlap = 278.875
PHY-3002 : Step(105): len = 306502, overlap = 269.25
PHY-3002 : Step(106): len = 306592, overlap = 272.219
PHY-3002 : Step(107): len = 306079, overlap = 265.375
PHY-3002 : Step(108): len = 306920, overlap = 260.281
PHY-3002 : Step(109): len = 306999, overlap = 266.219
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000373537
PHY-3002 : Step(110): len = 308685, overlap = 263.312
PHY-3002 : Step(111): len = 312266, overlap = 270.75
PHY-3002 : Step(112): len = 313534, overlap = 271.688
PHY-3002 : Step(113): len = 314796, overlap = 264.969
PHY-3002 : Step(114): len = 314395, overlap = 256.969
PHY-3002 : Step(115): len = 314113, overlap = 259.75
PHY-3002 : Step(116): len = 313810, overlap = 251.844
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.020102s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (155.5%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21746.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 422528, over cnt = 1140(3%), over = 5125, worst = 44
PHY-1001 : End global iterations;  1.022529s wall, 1.281250s user + 0.046875s system = 1.328125s CPU (129.9%)

PHY-1001 : Congestion index: top1 = 74.89, top5 = 51.33, top10 = 41.77, top15 = 36.39.
PHY-3001 : End congestion estimation;  1.350325s wall, 1.609375s user + 0.046875s system = 1.656250s CPU (122.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21744 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.301340s wall, 1.312500s user + 0.000000s system = 1.312500s CPU (100.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000100451
PHY-3002 : Step(117): len = 354356, overlap = 188.156
PHY-3002 : Step(118): len = 369730, overlap = 176.5
PHY-3002 : Step(119): len = 372981, overlap = 177.75
PHY-3002 : Step(120): len = 372806, overlap = 172.25
PHY-3002 : Step(121): len = 380417, overlap = 161.75
PHY-3002 : Step(122): len = 387604, overlap = 154.312
PHY-3002 : Step(123): len = 391091, overlap = 151.625
PHY-3002 : Step(124): len = 396487, overlap = 142.938
PHY-3002 : Step(125): len = 400530, overlap = 129
PHY-3002 : Step(126): len = 403843, overlap = 116
PHY-3002 : Step(127): len = 406728, overlap = 113.562
PHY-3002 : Step(128): len = 409119, overlap = 110
PHY-3002 : Step(129): len = 410929, overlap = 104.969
PHY-3002 : Step(130): len = 415058, overlap = 98.375
PHY-3002 : Step(131): len = 416382, overlap = 90.75
PHY-3002 : Step(132): len = 418572, overlap = 87.375
PHY-3002 : Step(133): len = 423325, overlap = 79.2812
PHY-3002 : Step(134): len = 424500, overlap = 76.1875
PHY-3002 : Step(135): len = 426721, overlap = 75.875
PHY-3002 : Step(136): len = 429022, overlap = 72.5625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000200902
PHY-3002 : Step(137): len = 428947, overlap = 78.2188
PHY-3002 : Step(138): len = 429930, overlap = 77.0312
PHY-3002 : Step(139): len = 430430, overlap = 74.7812
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000345855
PHY-3002 : Step(140): len = 435327, overlap = 70.5
PHY-3002 : Step(141): len = 443058, overlap = 67.5312
PHY-3002 : Step(142): len = 444748, overlap = 68.6562
PHY-3002 : Step(143): len = 446841, overlap = 73.25
PHY-3002 : Step(144): len = 448869, overlap = 74.25
PHY-3002 : Step(145): len = 452479, overlap = 66.2812
PHY-3002 : Step(146): len = 453185, overlap = 71.1562
PHY-3002 : Step(147): len = 456297, overlap = 66.3125
PHY-3002 : Step(148): len = 458487, overlap = 63.0938
PHY-3002 : Step(149): len = 456348, overlap = 69.5938
PHY-3002 : Step(150): len = 455990, overlap = 74.5625
PHY-3002 : Step(151): len = 457067, overlap = 75.7188
PHY-3002 : Step(152): len = 456710, overlap = 88.8125
PHY-3002 : Step(153): len = 456890, overlap = 93.0938
PHY-3002 : Step(154): len = 459184, overlap = 91.2812
PHY-3002 : Step(155): len = 459778, overlap = 93.7812
PHY-3002 : Step(156): len = 459081, overlap = 96.6562
PHY-3002 : Step(157): len = 459020, overlap = 97.75
PHY-3002 : Step(158): len = 460379, overlap = 99.5312
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00069171
PHY-3002 : Step(159): len = 459892, overlap = 94.3125
PHY-3002 : Step(160): len = 462323, overlap = 94.7188
PHY-3002 : Step(161): len = 466260, overlap = 93.6875
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00132422
PHY-3002 : Step(162): len = 466304, overlap = 91.5938
PHY-3002 : Step(163): len = 473237, overlap = 92.625
PHY-3002 : Step(164): len = 484481, overlap = 92.5
PHY-3002 : Step(165): len = 487018, overlap = 97.9375
PHY-3002 : Step(166): len = 488561, overlap = 101.438
PHY-3002 : Step(167): len = 487793, overlap = 102.312
PHY-3002 : Step(168): len = 486402, overlap = 102.844
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 60/21746.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 557400, over cnt = 2269(6%), over = 10607, worst = 38
PHY-1001 : End global iterations;  1.380780s wall, 2.390625s user + 0.031250s system = 2.421875s CPU (175.4%)

PHY-1001 : Congestion index: top1 = 82.26, top5 = 61.28, top10 = 52.45, top15 = 47.16.
PHY-3001 : End congestion estimation;  1.774012s wall, 2.781250s user + 0.031250s system = 2.812500s CPU (158.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21744 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.156558s wall, 1.140625s user + 0.015625s system = 1.156250s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000116395
PHY-3002 : Step(169): len = 493032, overlap = 354.875
PHY-3002 : Step(170): len = 493811, overlap = 295.438
PHY-3002 : Step(171): len = 489040, overlap = 243.375
PHY-3002 : Step(172): len = 483204, overlap = 240.875
PHY-3002 : Step(173): len = 479197, overlap = 214.188
PHY-3002 : Step(174): len = 477522, overlap = 204.5
PHY-3002 : Step(175): len = 471470, overlap = 209.938
PHY-3002 : Step(176): len = 468270, overlap = 212.125
PHY-3002 : Step(177): len = 466764, overlap = 209.719
PHY-3002 : Step(178): len = 462471, overlap = 205.75
PHY-3002 : Step(179): len = 458760, overlap = 201.25
PHY-3002 : Step(180): len = 457350, overlap = 198.219
PHY-3002 : Step(181): len = 454956, overlap = 202.625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00023279
PHY-3002 : Step(182): len = 454854, overlap = 187.906
PHY-3002 : Step(183): len = 455872, overlap = 185.375
PHY-3002 : Step(184): len = 456210, overlap = 180.531
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000465369
PHY-3002 : Step(185): len = 459339, overlap = 165.531
PHY-3002 : Step(186): len = 468569, overlap = 155.719
PHY-3002 : Step(187): len = 472200, overlap = 145.031
PHY-3002 : Step(188): len = 472395, overlap = 146.875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000930738
PHY-3002 : Step(189): len = 473083, overlap = 144.938
PHY-3002 : Step(190): len = 475739, overlap = 135.938
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80702, tnet num: 21744, tinst num: 19319, tnode num: 113882, tedge num: 127038.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.911353s wall, 1.828125s user + 0.078125s system = 1.906250s CPU (99.7%)

RUN-1004 : used memory is 571 MB, reserved memory is 545 MB, peak memory is 703 MB
OPT-1001 : Total overflow 496.88 peak overflow 4.12
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 479/21746.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 560960, over cnt = 2534(7%), over = 8822, worst = 25
PHY-1001 : End global iterations;  1.615351s wall, 2.375000s user + 0.000000s system = 2.375000s CPU (147.0%)

PHY-1001 : Congestion index: top1 = 58.12, top5 = 48.75, top10 = 43.83, top15 = 40.78.
PHY-1001 : End incremental global routing;  1.930482s wall, 2.671875s user + 0.000000s system = 2.671875s CPU (138.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21744 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.274662s wall, 1.265625s user + 0.015625s system = 1.281250s CPU (100.5%)

OPT-1001 : 17 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19239 has valid locations, 247 needs to be replaced
PHY-3001 : design contains 19549 instances, 5588 luts, 12394 seqs, 1458 slices, 291 macros(1458 instances: 956 mslices 502 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 490787
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17417/21976.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 572464, over cnt = 2570(7%), over = 8881, worst = 25
PHY-1001 : End global iterations;  1.042103s wall, 1.265625s user + 0.046875s system = 1.312500s CPU (125.9%)

PHY-1001 : Congestion index: top1 = 58.25, top5 = 48.97, top10 = 44.12, top15 = 41.14.
PHY-3001 : End congestion estimation;  2.100151s wall, 2.203125s user + 0.046875s system = 2.250000s CPU (107.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81466, tnet num: 21974, tinst num: 19549, tnode num: 114986, tedge num: 128106.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.939903s wall, 1.875000s user + 0.046875s system = 1.921875s CPU (99.1%)

RUN-1004 : used memory is 613 MB, reserved memory is 606 MB, peak memory is 704 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21974 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.984425s wall, 2.859375s user + 0.078125s system = 2.937500s CPU (98.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(191): len = 491176, overlap = 3.4375
PHY-3002 : Step(192): len = 492660, overlap = 3.5625
PHY-3002 : Step(193): len = 493767, overlap = 3.625
PHY-3002 : Step(194): len = 494670, overlap = 3.625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(195): len = 494789, overlap = 3.625
PHY-3002 : Step(196): len = 495803, overlap = 3.5625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(197): len = 496933, overlap = 3.4375
PHY-3002 : Step(198): len = 498004, overlap = 3.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17423/21976.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 576024, over cnt = 2579(7%), over = 9002, worst = 26
PHY-1001 : End global iterations;  0.243174s wall, 0.343750s user + 0.015625s system = 0.359375s CPU (147.8%)

PHY-1001 : Congestion index: top1 = 58.49, top5 = 49.15, top10 = 44.33, top15 = 41.25.
PHY-3001 : End congestion estimation;  0.608243s wall, 0.703125s user + 0.015625s system = 0.718750s CPU (118.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21974 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.090633s wall, 1.062500s user + 0.031250s system = 1.093750s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00145378
PHY-3002 : Step(199): len = 497509, overlap = 137.812
PHY-3002 : Step(200): len = 497484, overlap = 137.375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00279983
PHY-3002 : Step(201): len = 497815, overlap = 137.5
PHY-3002 : Step(202): len = 497978, overlap = 137.438
PHY-3001 : Final: Len = 497978, Over = 137.438
PHY-3001 : End incremental placement;  8.108176s wall, 8.625000s user + 0.531250s system = 9.156250s CPU (112.9%)

OPT-1001 : Total overflow 500.53 peak overflow 4.12
OPT-1001 : End high-fanout net optimization;  11.949506s wall, 13.296875s user + 0.546875s system = 13.843750s CPU (115.9%)

OPT-1001 : Current memory(MB): used = 707, reserve = 686, peak = 724.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17461/21976.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 577560, over cnt = 2540(7%), over = 8516, worst = 25
PHY-1002 : len = 614680, over cnt = 1953(5%), over = 5104, worst = 25
PHY-1002 : len = 660232, over cnt = 804(2%), over = 1893, worst = 25
PHY-1002 : len = 674200, over cnt = 466(1%), over = 973, worst = 11
PHY-1002 : len = 688440, over cnt = 46(0%), over = 82, worst = 6
PHY-1001 : End global iterations;  1.248951s wall, 1.828125s user + 0.000000s system = 1.828125s CPU (146.4%)

PHY-1001 : Congestion index: top1 = 50.65, top5 = 44.27, top10 = 41.20, top15 = 39.22.
OPT-1001 : End congestion update;  1.487327s wall, 2.062500s user + 0.000000s system = 2.062500s CPU (138.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 21974 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.791947s wall, 0.765625s user + 0.031250s system = 0.796875s CPU (100.6%)

OPT-0007 : Start: WNS 3769 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.284535s wall, 2.828125s user + 0.031250s system = 2.859375s CPU (125.2%)

OPT-1001 : Current memory(MB): used = 684, reserve = 667, peak = 724.
OPT-1001 : End physical optimization;  16.459770s wall, 18.437500s user + 0.656250s system = 19.093750s CPU (116.0%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5588 LUT to BLE ...
SYN-4008 : Packed 5588 LUT and 2671 SEQ to BLE.
SYN-4003 : Packing 9723 remaining SEQ's ...
SYN-4005 : Packed 3367 SEQ with LUT/SLICE
SYN-4006 : 67 single LUT's are left
SYN-4006 : 6356 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11944/13759 primitive instances ...
PHY-3001 : End packing;  2.712232s wall, 2.718750s user + 0.000000s system = 2.718750s CPU (100.2%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8146 instances
RUN-1001 : 4017 mslices, 4018 lslices, 60 pads, 46 brams, 0 dsps
RUN-1001 : There are total 19374 nets
RUN-1001 : 13558 nets have 2 pins
RUN-1001 : 4423 nets have [3 - 5] pins
RUN-1001 : 884 nets have [6 - 10] pins
RUN-1001 : 356 nets have [11 - 20] pins
RUN-1001 : 143 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
PHY-3001 : design contains 8144 instances, 8035 slices, 291 macros(1458 instances: 956 mslices 502 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 514331, Over = 358.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 8032/19374.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 651488, over cnt = 1536(4%), over = 2350, worst = 9
PHY-1002 : len = 656496, over cnt = 988(2%), over = 1364, worst = 7
PHY-1002 : len = 667656, over cnt = 380(1%), over = 501, worst = 7
PHY-1002 : len = 673592, over cnt = 95(0%), over = 101, worst = 2
PHY-1002 : len = 675928, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.177028s wall, 1.750000s user + 0.031250s system = 1.781250s CPU (151.3%)

PHY-1001 : Congestion index: top1 = 51.42, top5 = 43.79, top10 = 40.47, top15 = 38.35.
PHY-3001 : End congestion estimation;  1.465773s wall, 2.046875s user + 0.031250s system = 2.078125s CPU (141.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67652, tnet num: 19372, tinst num: 8144, tnode num: 92095, tedge num: 111556.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.620553s wall, 1.609375s user + 0.000000s system = 1.609375s CPU (99.3%)

RUN-1004 : used memory is 602 MB, reserved memory is 598 MB, peak memory is 724 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19372 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.440826s wall, 2.406250s user + 0.031250s system = 2.437500s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.74287e-05
PHY-3002 : Step(203): len = 514355, overlap = 338.25
PHY-3002 : Step(204): len = 511832, overlap = 351.5
PHY-3002 : Step(205): len = 510362, overlap = 362.5
PHY-3002 : Step(206): len = 509469, overlap = 363.25
PHY-3002 : Step(207): len = 508636, overlap = 372.75
PHY-3002 : Step(208): len = 506815, overlap = 380
PHY-3002 : Step(209): len = 504018, overlap = 389
PHY-3002 : Step(210): len = 500822, overlap = 392.5
PHY-3002 : Step(211): len = 498269, overlap = 394.75
PHY-3002 : Step(212): len = 495770, overlap = 402
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.48573e-05
PHY-3002 : Step(213): len = 500191, overlap = 390.75
PHY-3002 : Step(214): len = 504583, overlap = 375.5
PHY-3002 : Step(215): len = 503965, overlap = 374
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000189715
PHY-3002 : Step(216): len = 513085, overlap = 356.75
PHY-3002 : Step(217): len = 521250, overlap = 342.5
PHY-3002 : Step(218): len = 518513, overlap = 340
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000378438
PHY-3002 : Step(219): len = 522853, overlap = 334
PHY-3002 : Step(220): len = 530138, overlap = 315
PHY-3002 : Step(221): len = 531298, overlap = 310
PHY-3002 : Step(222): len = 530531, overlap = 312.25
PHY-3002 : Step(223): len = 529493, overlap = 312
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.702597s wall, 0.593750s user + 0.953125s system = 1.546875s CPU (220.2%)

PHY-3001 : Trial Legalized: Len = 628207
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 520/19374.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 717296, over cnt = 2282(6%), over = 3662, worst = 6
PHY-1002 : len = 730128, over cnt = 1366(3%), over = 1931, worst = 6
PHY-1002 : len = 749432, over cnt = 426(1%), over = 595, worst = 5
PHY-1002 : len = 757288, over cnt = 65(0%), over = 91, worst = 4
PHY-1002 : len = 759176, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.767081s wall, 2.968750s user + 0.062500s system = 3.031250s CPU (171.5%)

PHY-1001 : Congestion index: top1 = 48.64, top5 = 43.79, top10 = 41.35, top15 = 39.64.
PHY-3001 : End congestion estimation;  2.089102s wall, 3.265625s user + 0.062500s system = 3.328125s CPU (159.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19372 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.836343s wall, 0.812500s user + 0.031250s system = 0.843750s CPU (100.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000174605
PHY-3002 : Step(224): len = 586525, overlap = 77.25
PHY-3002 : Step(225): len = 568183, overlap = 125.25
PHY-3002 : Step(226): len = 556132, overlap = 169
PHY-3002 : Step(227): len = 548004, overlap = 210.25
PHY-3002 : Step(228): len = 543416, overlap = 229
PHY-3002 : Step(229): len = 540700, overlap = 239.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00034921
PHY-3002 : Step(230): len = 544599, overlap = 232.5
PHY-3002 : Step(231): len = 547357, overlap = 231.75
PHY-3002 : Step(232): len = 546156, overlap = 232
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(233): len = 549498, overlap = 231.25
PHY-3002 : Step(234): len = 555599, overlap = 226.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.029731s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (105.1%)

PHY-3001 : Legalized: Len = 594585, Over = 0
PHY-3001 : Spreading special nets. 50 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.077883s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (100.3%)

PHY-3001 : 73 instances has been re-located, deltaX = 21, deltaY = 40, maxDist = 1.
PHY-3001 : Final: Len = 595383, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67652, tnet num: 19372, tinst num: 8144, tnode num: 92095, tedge num: 111556.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.864682s wall, 1.828125s user + 0.031250s system = 1.859375s CPU (99.7%)

RUN-1004 : used memory is 622 MB, reserved memory is 622 MB, peak memory is 724 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 4749/19374.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 696400, over cnt = 2189(6%), over = 3308, worst = 6
PHY-1002 : len = 707008, over cnt = 1274(3%), over = 1721, worst = 6
PHY-1002 : len = 723384, over cnt = 376(1%), over = 477, worst = 5
PHY-1002 : len = 728408, over cnt = 137(0%), over = 180, worst = 4
PHY-1002 : len = 731512, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.516240s wall, 2.546875s user + 0.015625s system = 2.562500s CPU (169.0%)

PHY-1001 : Congestion index: top1 = 47.00, top5 = 42.21, top10 = 39.79, top15 = 38.29.
PHY-1001 : End incremental global routing;  1.793824s wall, 2.828125s user + 0.015625s system = 2.843750s CPU (158.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19372 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.827497s wall, 0.812500s user + 0.015625s system = 0.828125s CPU (100.1%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8079 has valid locations, 14 needs to be replaced
PHY-3001 : design contains 8156 instances, 8047 slices, 291 macros(1458 instances: 956 mslices 502 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 599768
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17463/19384.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 739032, over cnt = 30(0%), over = 40, worst = 4
PHY-1002 : len = 739104, over cnt = 13(0%), over = 13, worst = 1
PHY-1002 : len = 739232, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 739248, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 739264, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.642552s wall, 0.671875s user + 0.015625s system = 0.687500s CPU (107.0%)

PHY-1001 : Congestion index: top1 = 47.13, top5 = 42.50, top10 = 39.99, top15 = 38.48.
PHY-3001 : End congestion estimation;  0.919667s wall, 0.921875s user + 0.031250s system = 0.953125s CPU (103.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67762, tnet num: 19382, tinst num: 8156, tnode num: 92220, tedge num: 111681.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.845032s wall, 1.843750s user + 0.000000s system = 1.843750s CPU (99.9%)

RUN-1004 : used memory is 647 MB, reserved memory is 636 MB, peak memory is 724 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19382 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.690574s wall, 2.687500s user + 0.015625s system = 2.703125s CPU (100.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(235): len = 599336, overlap = 0.25
PHY-3002 : Step(236): len = 599113, overlap = 0.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17457/19384.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 736976, over cnt = 36(0%), over = 56, worst = 7
PHY-1002 : len = 737168, over cnt = 29(0%), over = 38, worst = 4
PHY-1002 : len = 737360, over cnt = 15(0%), over = 15, worst = 1
PHY-1002 : len = 737464, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 737624, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.650711s wall, 0.671875s user + 0.031250s system = 0.703125s CPU (108.1%)

PHY-1001 : Congestion index: top1 = 46.98, top5 = 42.36, top10 = 39.96, top15 = 38.47.
PHY-3001 : End congestion estimation;  0.923642s wall, 0.953125s user + 0.031250s system = 0.984375s CPU (106.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19382 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.826314s wall, 0.812500s user + 0.015625s system = 0.828125s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.0010978
PHY-3002 : Step(237): len = 599015, overlap = 1.75
PHY-3002 : Step(238): len = 598921, overlap = 1.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005626s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 598921, Over = 0
PHY-3001 : End spreading;  0.062996s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.2%)

PHY-3001 : Final: Len = 598921, Over = 0
PHY-3001 : End incremental placement;  5.956320s wall, 6.015625s user + 0.140625s system = 6.156250s CPU (103.4%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  9.003504s wall, 10.234375s user + 0.171875s system = 10.406250s CPU (115.6%)

OPT-1001 : Current memory(MB): used = 719, reserve = 703, peak = 724.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17457/19384.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 736552, over cnt = 23(0%), over = 29, worst = 4
PHY-1002 : len = 736672, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 736712, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 736872, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 736952, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.664504s wall, 0.703125s user + 0.046875s system = 0.750000s CPU (112.9%)

PHY-1001 : Congestion index: top1 = 46.98, top5 = 42.44, top10 = 39.98, top15 = 38.48.
OPT-1001 : End congestion update;  0.942423s wall, 0.984375s user + 0.046875s system = 1.031250s CPU (109.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19382 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.703157s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (100.0%)

OPT-0007 : Start: WNS 3675 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.649508s wall, 1.687500s user + 0.046875s system = 1.734375s CPU (105.1%)

OPT-1001 : Current memory(MB): used = 720, reserve = 703, peak = 724.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19382 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.694895s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (101.2%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17474/19384.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 736952, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.113936s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (109.7%)

PHY-1001 : Congestion index: top1 = 46.98, top5 = 42.44, top10 = 39.98, top15 = 38.48.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19382 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.699732s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (98.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3675 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 46.655172
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3675ps with logic level 4 
OPT-1001 : End physical optimization;  14.549194s wall, 15.750000s user + 0.281250s system = 16.031250s CPU (110.2%)

RUN-1003 : finish command "place" in  82.415967s wall, 177.984375s user + 10.203125s system = 188.187500s CPU (228.3%)

RUN-1004 : used memory is 600 MB, reserved memory is 596 MB, peak memory is 724 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.541756s wall, 2.734375s user + 0.000000s system = 2.734375s CPU (177.4%)

RUN-1004 : used memory is 600 MB, reserved memory is 598 MB, peak memory is 724 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8158 instances
RUN-1001 : 4027 mslices, 4020 lslices, 60 pads, 46 brams, 0 dsps
RUN-1001 : There are total 19384 nets
RUN-1001 : 13553 nets have 2 pins
RUN-1001 : 4422 nets have [3 - 5] pins
RUN-1001 : 893 nets have [6 - 10] pins
RUN-1001 : 362 nets have [11 - 20] pins
RUN-1001 : 144 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67762, tnet num: 19382, tinst num: 8156, tnode num: 92220, tedge num: 111681.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.606688s wall, 1.593750s user + 0.000000s system = 1.593750s CPU (99.2%)

RUN-1004 : used memory is 594 MB, reserved memory is 580 MB, peak memory is 724 MB
PHY-1001 : 4027 mslices, 4020 lslices, 60 pads, 46 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19382 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 678480, over cnt = 2313(6%), over = 3733, worst = 7
PHY-1002 : len = 692840, over cnt = 1462(4%), over = 2040, worst = 6
PHY-1002 : len = 713344, over cnt = 385(1%), over = 494, worst = 5
PHY-1002 : len = 721712, over cnt = 5(0%), over = 7, worst = 3
PHY-1002 : len = 721832, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.565191s wall, 2.625000s user + 0.000000s system = 2.625000s CPU (167.7%)

PHY-1001 : Congestion index: top1 = 46.64, top5 = 41.82, top10 = 39.58, top15 = 38.03.
PHY-1001 : End global routing;  1.881171s wall, 2.937500s user + 0.000000s system = 2.937500s CPU (156.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 702, reserve = 695, peak = 724.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net COM3/GNRMC/u_fifo/wr_to_rd_cross_inst/secondary_clk_i will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 971, reserve = 959, peak = 971.
PHY-1001 : End build detailed router design. 4.402946s wall, 4.343750s user + 0.062500s system = 4.406250s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 194408, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.796707s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1007, reserve = 995, peak = 1007.
PHY-1001 : End phase 1; 0.804523s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 55% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.79822e+06, over cnt = 1245(0%), over = 1247, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1025, reserve = 1013, peak = 1025.
PHY-1001 : End initial routed; 18.985237s wall, 46.109375s user + 0.437500s system = 46.546875s CPU (245.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18155(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.739   |   0.000   |   0   
RUN-1001 :   Hold   |   0.075   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.186996s wall, 3.187500s user + 0.000000s system = 3.187500s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1019, reserve = 1022, peak = 1028.
PHY-1001 : End phase 2; 22.172351s wall, 49.296875s user + 0.437500s system = 49.734375s CPU (224.3%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.79822e+06, over cnt = 1245(0%), over = 1247, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.220701s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (99.1%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.78599e+06, over cnt = 461(0%), over = 462, worst = 2, crit = 0
PHY-1001 : End DR Iter 1; 0.717655s wall, 1.234375s user + 0.000000s system = 1.234375s CPU (172.0%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.7869e+06, over cnt = 68(0%), over = 68, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.344609s wall, 0.468750s user + 0.000000s system = 0.468750s CPU (136.0%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.78792e+06, over cnt = 6(0%), over = 6, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.206034s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (136.5%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.78802e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.146725s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (95.8%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.78807e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.142040s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (99.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18155(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.310   |   0.000   |   0   
RUN-1001 :   Hold   |   0.075   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.169013s wall, 3.171875s user + 0.000000s system = 3.171875s CPU (100.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 313 feed throughs used by 264 nets
PHY-1001 : End commit to database; 2.058716s wall, 2.046875s user + 0.015625s system = 2.062500s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 1113, reserve = 1113, peak = 1113.
PHY-1001 : End phase 3; 7.517400s wall, 8.218750s user + 0.031250s system = 8.250000s CPU (109.7%)

PHY-1003 : Routed, final wirelength = 1.78807e+06
PHY-1001 : Current memory(MB): used = 1117, reserve = 1118, peak = 1117.
PHY-1001 : End export database. 0.058054s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (107.7%)

PHY-1001 : End detail routing;  35.343048s wall, 63.078125s user + 0.546875s system = 63.625000s CPU (180.0%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67762, tnet num: 19382, tinst num: 8156, tnode num: 92220, tedge num: 111681.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.582300s wall, 1.578125s user + 0.000000s system = 1.578125s CPU (99.7%)

RUN-1004 : used memory is 1054 MB, reserved memory is 1055 MB, peak memory is 1117 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  42.760283s wall, 71.531250s user + 0.562500s system = 72.093750s CPU (168.6%)

RUN-1004 : used memory is 1053 MB, reserved memory is 1056 MB, peak memory is 1117 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8662   out of  19600   44.19%
#reg                    12505   out of  19600   63.80%
#le                     14976
  #lut only              2471   out of  14976   16.50%
  #reg only              6314   out of  14976   42.16%
  #lut&reg               6191   out of  14976   41.34%
#dsp                        0   out of     29    0.00%
#bram                      46   out of     64   71.88%
  #bram9k                  46
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                                                 Type               DriverType         Driver                    Fanout
#1        COM3/GNRMC/u_fifo/wr_to_rd_cross_inst/secondary_clk_i    GCLK               pll                CLK100M/pll_inst.clkc0    6856
#2        config_inst_syn_9                                        GCLK               config             config_inst.jtck          198
#3        clk_in_dup_1                                             GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         M3        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         P6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+------------------------------------------------------------------------------------------------------------------------------------------+
|Instance                             |Module                                         |le     |lut     |ripple  |seq     |bram    |dsp     |
+------------------------------------------------------------------------------------------------------------------------------------------+
|top                                  |INS600M_21A                                    |14976  |7204    |1458    |12549   |46      |0       |
|  AnyFog_dataX                       |AnyFog                                         |208    |86      |22      |173     |0       |0       |
|    UART_RX_COM3                     |UART_RX115200E                                 |86     |60      |22      |51      |0       |0       |
|  AnyFog_dataY                       |AnyFog                                         |206    |74      |22      |171     |0       |0       |
|    UART_RX_COM3                     |UART_RX115200E                                 |86     |53      |22      |51      |0       |0       |
|  AnyFog_dataZ                       |AnyFog                                         |216    |125     |22      |171     |0       |0       |
|    UART_RX_COM3                     |UART_RX115200E                                 |94     |68      |22      |50      |0       |0       |
|  CLK100M                            |global_clock                                   |0      |0       |0       |0       |0       |0       |
|  COM2                               |COM2_Control                                   |2858   |621     |34      |2787    |0       |0       |
|    PPPNAV_com2                      |PPPNAV                                         |219    |70      |5       |210     |0       |0       |
|    STADOP_com2                      |STADOP                                         |553    |83      |0       |548     |0       |0       |
|    UART_RX_COM3                     |UART_RX460800                                  |65     |47      |14      |40      |0       |0       |
|    head_com2                        |uniheading                                     |269    |105     |5       |254     |0       |0       |
|    uart_com2                        |Agrica                                         |1430   |302     |10      |1413    |0       |0       |
|  COM3                               |COM3_Control                                   |396    |194     |44      |328     |2       |0       |
|    GNRMC                            |GNRMC_Tx                                       |185    |95      |30      |145     |2       |0       |
|      u_fifo                         |Asys_fifo8x8                                   |120    |58      |25      |91      |1       |0       |
|        ram_inst                     |ram_infer_Asys_fifo8x8                         |0      |0       |0       |0       |1       |0       |
|        rd_to_wr_cross_inst          |fifo_cross_domain_addr_process_al_Asys_fifo8x8 |31     |13      |0       |31      |0       |0       |
|        wr_to_rd_cross_inst          |fifo_cross_domain_addr_process_al_Asys_fifo8x8 |33     |18      |0       |33      |0       |0       |
|    UART_RX_COM3                     |UART_RX460800                                  |57     |38      |14      |35      |0       |0       |
|    rmc_com3                         |Gprmc                                          |154    |61      |0       |148     |0       |0       |
|  DATA                               |Data_Processing                                |8668   |4378    |1056    |6984    |0       |0       |
|    DIV_Dtemp                        |Divider                                        |784    |344     |84      |660     |0       |0       |
|    DIV_Utemp                        |Divider                                        |606    |320     |84      |479     |0       |0       |
|    DIV_accX                         |Divider                                        |657    |285     |84      |532     |0       |0       |
|    DIV_accY                         |Divider                                        |669    |360     |105     |508     |0       |0       |
|    DIV_accZ                         |Divider                                        |669    |379     |132     |459     |0       |0       |
|    DIV_rateX                        |Divider                                        |636    |393     |132     |432     |0       |0       |
|    DIV_rateY                        |Divider                                        |582    |382     |132     |374     |0       |0       |
|    DIV_rateZ                        |Divider                                        |595    |335     |132     |372     |0       |0       |
|    genclk                           |genclk                                         |83     |52      |20      |49      |0       |0       |
|  FMC                                |FMC_Ctrl                                       |419    |367     |43      |331     |0       |0       |
|  IIC                                |I2C_master                                     |287    |224     |11      |261     |0       |0       |
|  IMU_CTRL                           |SCHA634                                        |886    |649     |61      |726     |0       |0       |
|    CtrlData                         |CtrlData                                       |459    |402     |47      |337     |0       |0       |
|      usms                           |Time_1ms                                       |32     |26      |5       |22      |0       |0       |
|    SPIM                             |SPI_SCHA634                                    |427    |247     |14      |389     |0       |0       |
|  POWER                              |POWER_EN                                       |103    |61      |38      |41      |0       |0       |
|  cw_top                             |CW_TOP_WRAPPER                                 |723    |425     |105     |526     |0       |0       |
|    wrapper_cwc_top                  |cwc_top                                        |723    |425     |105     |526     |0       |0       |
|      cfg_int_inst                   |cwc_cfg_int                                    |349    |184     |0       |332     |0       |0       |
|        reg_inst                     |register                                       |347    |182     |0       |330     |0       |0       |
|        tap_inst                     |tap                                            |2      |2       |0       |2       |0       |0       |
|      trigger_inst                   |trigger                                        |374    |241     |105     |194     |0       |0       |
|        bus_inst                     |bus_top                                        |151    |103     |48      |67      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes  |bus_det                                        |26     |16      |10      |9       |0       |0       |
|          BUS_DETECTOR[10]$bus_nodes |bus_det                                        |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[11]$bus_nodes |bus_det                                        |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes  |bus_det                                        |54     |36      |18      |21      |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes  |bus_det                                        |3      |3       |0       |3       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes  |bus_det                                        |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes  |bus_det                                        |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[5]$bus_nodes  |bus_det                                        |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[6]$bus_nodes  |bus_det                                        |3      |3       |0       |3       |0       |0       |
|          BUS_DETECTOR[7]$bus_nodes  |bus_det                                        |3      |3       |0       |3       |0       |0       |
|          BUS_DETECTOR[8]$bus_nodes  |bus_det                                        |28     |18      |10      |12      |0       |0       |
|          BUS_DETECTOR[9]$bus_nodes  |bus_det                                        |28     |18      |10      |10      |0       |0       |
|        emb_ctrl_inst                |emb_ctrl                                       |140    |93      |29      |91      |0       |0       |
+------------------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13492  
    #2          2       3468   
    #3          3        681   
    #4          4        273   
    #5        5-10       961   
    #6        11-50      433   
    #7       51-100       5    
    #8       101-500      4    
    #9        >500        2    
  Average     2.14             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  1.879347s wall, 3.265625s user + 0.015625s system = 3.281250s CPU (174.6%)

RUN-1004 : used memory is 1054 MB, reserved memory is 1056 MB, peak memory is 1117 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67762, tnet num: 19382, tinst num: 8156, tnode num: 92220, tedge num: 111681.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.586880s wall, 1.578125s user + 0.000000s system = 1.578125s CPU (99.4%)

RUN-1004 : used memory is 1055 MB, reserved memory is 1058 MB, peak memory is 1117 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19382 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.269487s wall, 1.250000s user + 0.000000s system = 1.250000s CPU (98.5%)

RUN-1004 : used memory is 1061 MB, reserved memory is 1062 MB, peak memory is 1117 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: e34d6fd8734b0965b109e6170ddd6e9d7448fe786851304b49f53d5faf7f761f -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8156
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19384, pip num: 148585
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 313
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3261 valid insts, and 413174 bits set as '1'.
BIT-1004 : the usercode register value: 00000000111111011011110111100010
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  10.529720s wall, 104.843750s user + 0.156250s system = 105.000000s CPU (997.2%)

RUN-1004 : used memory is 1190 MB, reserved memory is 1182 MB, peak memory is 1305 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250710_162942.log"

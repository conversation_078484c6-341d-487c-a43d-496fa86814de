============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Wed Aug 20 14:37:03 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.681418s wall, 1.406250s user + 4.265625s system = 5.671875s CPU (99.8%)

RUN-1004 : used memory is 79 MB, reserved memory is 41 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.912072s wall, 1.703125s user + 0.171875s system = 1.875000s CPU (98.1%)

RUN-1004 : used memory is 303 MB, reserved memory is 271 MB, peak memory is 306 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 64 trigger nets, 64 data nets.
KIT-1004 : Chipwatcher code = 0101000011111110
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=64,BUS_CTRL_NUM=148,BUS_WIDTH='{32'sb01000,32'sb01000,32'sb010000,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb010000,32'sb0100000,32'sb0110000},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0101000,32'sb01001100,32'sb01110000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=170) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=170) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=64,BUS_CTRL_NUM=148,BUS_WIDTH='{32'sb01000,32'sb01000,32'sb010000,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb010000,32'sb0100000,32'sb0110000},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0101000,32'sb01001100,32'sb01110000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=64,BUS_CTRL_NUM=148,BUS_WIDTH='{32'sb01000,32'sb01000,32'sb010000,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb010000,32'sb0100000,32'sb0110000},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0101000,32'sb01001100,32'sb01110000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=64,BUS_CTRL_NUM=148,BUS_WIDTH='{32'sb01000,32'sb01000,32'sb010000,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb010000,32'sb0100000,32'sb0110000},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0101000,32'sb01001100,32'sb01110000})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=170)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=170)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=64,BUS_CTRL_NUM=148,BUS_WIDTH='{32'sb01000,32'sb01000,32'sb010000,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb010000,32'sb0100000,32'sb0110000},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0101000,32'sb01001100,32'sb01110000})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=64,BUS_CTRL_NUM=148,BUS_WIDTH='{32'sb01000,32'sb01000,32'sb010000,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb010000,32'sb0100000,32'sb0110000},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0101000,32'sb01001100,32'sb01110000})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 23168/27 useful/useless nets, 19719/10 useful/useless insts
SYN-1016 : Merged 29 instances.
SYN-1032 : 22748/16 useful/useless nets, 20255/12 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 5 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 5 mux instances.
SYN-1015 : Optimize round 1, 512 better
SYN-1014 : Optimize round 2
SYN-1032 : 22304/75 useful/useless nets, 19811/80 useful/useless insts
SYN-1015 : Optimize round 2, 160 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.717405s wall, 2.640625s user + 0.062500s system = 2.703125s CPU (99.5%)

RUN-1004 : used memory is 330 MB, reserved memory is 297 MB, peak memory is 332 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22332/257 useful/useless nets, 19867/68 useful/useless insts
SYN-1016 : Merged 26 instances.
SYN-2571 : Optimize after map_dsp, round 1, 351 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 64 instances.
SYN-2501 : Optimize round 1, 130 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 20 macro adder
SYN-1019 : Optimized 21 mux instances.
SYN-1016 : Merged 13 instances.
SYN-1032 : 22926/4 useful/useless nets, 20461/3 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83511, tnet num: 22926, tinst num: 20460, tnode num: 116868, tedge num: 130301.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.237921s wall, 1.218750s user + 0.015625s system = 1.234375s CPU (99.7%)

RUN-1004 : used memory is 472 MB, reserved memory is 440 MB, peak memory is 472 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22926 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 302 (3.26), #lev = 7 (1.56)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 302 (3.26), #lev = 7 (1.56)
SYN-3001 : Logic optimization runtime opt =   0.04 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 638 instances into 302 LUTs, name keeping = 76%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 517 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 184 adder to BLE ...
SYN-4008 : Packed 184 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.938608s wall, 4.828125s user + 0.109375s system = 4.937500s CPU (100.0%)

RUN-1004 : used memory is 354 MB, reserved memory is 320 MB, peak memory is 580 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  8.026482s wall, 7.828125s user + 0.203125s system = 8.031250s CPU (100.1%)

RUN-1004 : used memory is 355 MB, reserved memory is 321 MB, peak memory is 580 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (361 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19637 instances
RUN-0007 : 5694 luts, 12318 seqs, 1011 mslices, 519 lslices, 59 pads, 31 brams, 0 dsps
RUN-1001 : There are total 22110 nets
RUN-1001 : 16492 nets have 2 pins
RUN-1001 : 4400 nets have [3 - 5] pins
RUN-1001 : 854 nets have [6 - 10] pins
RUN-1001 : 239 nets have [11 - 20] pins
RUN-1001 : 103 nets have [21 - 99] pins
RUN-1001 : 22 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4792     
RUN-1001 :   No   |  No   |  Yes  |     724     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     533     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19635 instances, 5694 luts, 12318 seqs, 1530 slices, 295 macros(1530 instances: 1011 mslices 519 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 63%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81756, tnet num: 22108, tinst num: 19635, tnode num: 115014, tedge num: 128597.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.290858s wall, 1.250000s user + 0.046875s system = 1.296875s CPU (100.5%)

RUN-1004 : used memory is 532 MB, reserved memory is 503 MB, peak memory is 580 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22108 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.257833s wall, 2.171875s user + 0.093750s system = 2.265625s CPU (100.3%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.70624e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19635.
PHY-3001 : Level 1 #clusters 2145.
PHY-3001 : End clustering;  0.173603s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (162.0%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 63%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 926578, overlap = 616.281
PHY-3002 : Step(2): len = 847840, overlap = 690.094
PHY-3002 : Step(3): len = 563721, overlap = 859.062
PHY-3002 : Step(4): len = 502456, overlap = 912.875
PHY-3002 : Step(5): len = 399586, overlap = 998.875
PHY-3002 : Step(6): len = 355921, overlap = 1047.16
PHY-3002 : Step(7): len = 292451, overlap = 1134.81
PHY-3002 : Step(8): len = 262052, overlap = 1191.53
PHY-3002 : Step(9): len = 229343, overlap = 1247.12
PHY-3002 : Step(10): len = 208953, overlap = 1277.5
PHY-3002 : Step(11): len = 190722, overlap = 1330.47
PHY-3002 : Step(12): len = 174616, overlap = 1369
PHY-3002 : Step(13): len = 163271, overlap = 1411.56
PHY-3002 : Step(14): len = 146217, overlap = 1439.47
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.18442e-06
PHY-3002 : Step(15): len = 151772, overlap = 1419.78
PHY-3002 : Step(16): len = 203231, overlap = 1303.66
PHY-3002 : Step(17): len = 213904, overlap = 1227.41
PHY-3002 : Step(18): len = 215680, overlap = 1148.94
PHY-3002 : Step(19): len = 211872, overlap = 1128.75
PHY-3002 : Step(20): len = 206117, overlap = 1108.06
PHY-3002 : Step(21): len = 200589, overlap = 1088.84
PHY-3002 : Step(22): len = 194474, overlap = 1083.69
PHY-3002 : Step(23): len = 191133, overlap = 1076.12
PHY-3002 : Step(24): len = 186446, overlap = 1066.22
PHY-3002 : Step(25): len = 184545, overlap = 1061.84
PHY-3002 : Step(26): len = 182194, overlap = 1063.31
PHY-3002 : Step(27): len = 181542, overlap = 1042.94
PHY-3002 : Step(28): len = 180232, overlap = 1043.53
PHY-3002 : Step(29): len = 179851, overlap = 1048.94
PHY-3002 : Step(30): len = 178652, overlap = 1065.88
PHY-3002 : Step(31): len = 177197, overlap = 1062.66
PHY-3002 : Step(32): len = 176314, overlap = 1075.62
PHY-3002 : Step(33): len = 174909, overlap = 1074.41
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.36884e-06
PHY-3002 : Step(34): len = 182060, overlap = 1050.19
PHY-3002 : Step(35): len = 196931, overlap = 955.844
PHY-3002 : Step(36): len = 201330, overlap = 914
PHY-3002 : Step(37): len = 203759, overlap = 886.906
PHY-3002 : Step(38): len = 203935, overlap = 864.875
PHY-3002 : Step(39): len = 204159, overlap = 837
PHY-3002 : Step(40): len = 201732, overlap = 831.969
PHY-3002 : Step(41): len = 200558, overlap = 844.719
PHY-3002 : Step(42): len = 199079, overlap = 848.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.73769e-06
PHY-3002 : Step(43): len = 210233, overlap = 841.625
PHY-3002 : Step(44): len = 227176, overlap = 821.281
PHY-3002 : Step(45): len = 231680, overlap = 770.406
PHY-3002 : Step(46): len = 233612, overlap = 714.875
PHY-3002 : Step(47): len = 233278, overlap = 691.938
PHY-3002 : Step(48): len = 232170, overlap = 690.844
PHY-3002 : Step(49): len = 230634, overlap = 682.344
PHY-3002 : Step(50): len = 228728, overlap = 686.344
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 9.47538e-06
PHY-3002 : Step(51): len = 241518, overlap = 638.75
PHY-3002 : Step(52): len = 256749, overlap = 557.281
PHY-3002 : Step(53): len = 260173, overlap = 550.844
PHY-3002 : Step(54): len = 262117, overlap = 526.812
PHY-3002 : Step(55): len = 261158, overlap = 508.031
PHY-3002 : Step(56): len = 259507, overlap = 490.688
PHY-3002 : Step(57): len = 256923, overlap = 501.938
PHY-3002 : Step(58): len = 255798, overlap = 493.5
PHY-3002 : Step(59): len = 254380, overlap = 516
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.89508e-05
PHY-3002 : Step(60): len = 266955, overlap = 490.562
PHY-3002 : Step(61): len = 281546, overlap = 405.812
PHY-3002 : Step(62): len = 284274, overlap = 366.062
PHY-3002 : Step(63): len = 285967, overlap = 366.875
PHY-3002 : Step(64): len = 284930, overlap = 366
PHY-3002 : Step(65): len = 282643, overlap = 373.5
PHY-3002 : Step(66): len = 281317, overlap = 387.5
PHY-3002 : Step(67): len = 280459, overlap = 406.031
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.79015e-05
PHY-3002 : Step(68): len = 293618, overlap = 394.188
PHY-3002 : Step(69): len = 304981, overlap = 347.625
PHY-3002 : Step(70): len = 307488, overlap = 333.969
PHY-3002 : Step(71): len = 308128, overlap = 318.812
PHY-3002 : Step(72): len = 306469, overlap = 326.875
PHY-3002 : Step(73): len = 304597, overlap = 307.281
PHY-3002 : Step(74): len = 302449, overlap = 319.156
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 7.5803e-05
PHY-3002 : Step(75): len = 309839, overlap = 285.312
PHY-3002 : Step(76): len = 318312, overlap = 243.75
PHY-3002 : Step(77): len = 322385, overlap = 222.156
PHY-3002 : Step(78): len = 324068, overlap = 205.25
PHY-3002 : Step(79): len = 323314, overlap = 201.375
PHY-3002 : Step(80): len = 322582, overlap = 211.812
PHY-3002 : Step(81): len = 319746, overlap = 216.906
PHY-3002 : Step(82): len = 319687, overlap = 229.375
PHY-3002 : Step(83): len = 319030, overlap = 233.688
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000151606
PHY-3002 : Step(84): len = 325718, overlap = 228.5
PHY-3002 : Step(85): len = 333777, overlap = 235.938
PHY-3002 : Step(86): len = 335110, overlap = 231.125
PHY-3002 : Step(87): len = 334430, overlap = 245.5
PHY-3002 : Step(88): len = 332711, overlap = 254.062
PHY-3002 : Step(89): len = 331041, overlap = 264.406
PHY-3002 : Step(90): len = 329865, overlap = 244.906
PHY-3002 : Step(91): len = 330809, overlap = 238.156
PHY-3002 : Step(92): len = 331303, overlap = 242.5
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000303212
PHY-3002 : Step(93): len = 333433, overlap = 240.25
PHY-3002 : Step(94): len = 337504, overlap = 243.969
PHY-3002 : Step(95): len = 338413, overlap = 240.5
PHY-3002 : Step(96): len = 339019, overlap = 243.406
PHY-3002 : Step(97): len = 338523, overlap = 255.531
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000583838
PHY-3002 : Step(98): len = 340027, overlap = 248.875
PHY-3002 : Step(99): len = 343501, overlap = 233.781
PHY-3002 : Step(100): len = 344373, overlap = 235.906
PHY-3002 : Step(101): len = 344999, overlap = 224.688
PHY-3002 : Step(102): len = 346117, overlap = 225.219
PHY-3002 : Step(103): len = 347558, overlap = 210.438
PHY-3002 : Step(104): len = 348727, overlap = 212.094
PHY-3002 : Step(105): len = 348681, overlap = 217.844
PHY-3002 : Step(106): len = 348510, overlap = 206
PHY-3002 : Step(107): len = 348033, overlap = 214.406
PHY-3002 : Step(108): len = 348179, overlap = 208.094
PHY-3002 : Step(109): len = 348314, overlap = 213.938
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.022064s wall, 0.046875s user + 0.031250s system = 0.078125s CPU (354.1%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/22110.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 444880, over cnt = 1205(3%), over = 5631, worst = 41
PHY-1001 : End global iterations;  0.884909s wall, 1.203125s user + 0.031250s system = 1.234375s CPU (139.5%)

PHY-1001 : Congestion index: top1 = 71.98, top5 = 53.59, top10 = 44.37, top15 = 38.54.
PHY-3001 : End congestion estimation;  1.130208s wall, 1.437500s user + 0.046875s system = 1.484375s CPU (131.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22108 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.037113s wall, 1.015625s user + 0.015625s system = 1.031250s CPU (99.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000108435
PHY-3002 : Step(110): len = 389907, overlap = 203.719
PHY-3002 : Step(111): len = 401905, overlap = 173.625
PHY-3002 : Step(112): len = 399365, overlap = 159.25
PHY-3002 : Step(113): len = 396376, overlap = 135.906
PHY-3002 : Step(114): len = 401458, overlap = 124.25
PHY-3002 : Step(115): len = 405963, overlap = 122.594
PHY-3002 : Step(116): len = 407941, overlap = 116.438
PHY-3002 : Step(117): len = 410374, overlap = 112.062
PHY-3002 : Step(118): len = 413478, overlap = 112.75
PHY-3002 : Step(119): len = 417502, overlap = 111.062
PHY-3002 : Step(120): len = 417499, overlap = 111.188
PHY-3002 : Step(121): len = 417480, overlap = 117.688
PHY-3002 : Step(122): len = 418934, overlap = 122.781
PHY-3002 : Step(123): len = 419028, overlap = 123.844
PHY-3002 : Step(124): len = 419764, overlap = 117.344
PHY-3002 : Step(125): len = 420785, overlap = 111.594
PHY-3002 : Step(126): len = 420246, overlap = 110.469
PHY-3002 : Step(127): len = 421370, overlap = 110.312
PHY-3002 : Step(128): len = 420292, overlap = 111.438
PHY-3002 : Step(129): len = 419279, overlap = 113.094
PHY-3002 : Step(130): len = 419536, overlap = 115.062
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00021687
PHY-3002 : Step(131): len = 419994, overlap = 112.656
PHY-3002 : Step(132): len = 421251, overlap = 111.656
PHY-3002 : Step(133): len = 422855, overlap = 111
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00042207
PHY-3002 : Step(134): len = 430400, overlap = 103.656
PHY-3002 : Step(135): len = 437534, overlap = 105.219
PHY-3002 : Step(136): len = 439621, overlap = 103.156
PHY-3002 : Step(137): len = 441898, overlap = 100.5
PHY-3002 : Step(138): len = 443495, overlap = 100.531
PHY-3002 : Step(139): len = 442972, overlap = 100.219
PHY-3002 : Step(140): len = 444296, overlap = 105.781
PHY-3002 : Step(141): len = 444727, overlap = 107.281
PHY-3002 : Step(142): len = 444112, overlap = 112.156
PHY-3002 : Step(143): len = 443369, overlap = 116.781
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000844139
PHY-3002 : Step(144): len = 443891, overlap = 113.969
PHY-3002 : Step(145): len = 446670, overlap = 110.375
PHY-3002 : Step(146): len = 450283, overlap = 107.375
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.001554
PHY-3002 : Step(147): len = 450240, overlap = 109.531
PHY-3002 : Step(148): len = 452977, overlap = 110.375
PHY-3002 : Step(149): len = 459913, overlap = 114.031
PHY-3002 : Step(150): len = 465850, overlap = 119.156
PHY-3002 : Step(151): len = 465255, overlap = 119.844
PHY-3002 : Step(152): len = 465828, overlap = 119.906
PHY-3002 : Step(153): len = 465672, overlap = 121.625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 74/22110.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 529152, over cnt = 2198(6%), over = 10544, worst = 44
PHY-1001 : End global iterations;  1.105271s wall, 1.890625s user + 0.000000s system = 1.890625s CPU (171.1%)

PHY-1001 : Congestion index: top1 = 83.02, top5 = 62.10, top10 = 52.36, top15 = 46.63.
PHY-3001 : End congestion estimation;  1.400731s wall, 2.171875s user + 0.000000s system = 2.171875s CPU (155.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22108 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.038558s wall, 1.046875s user + 0.000000s system = 1.046875s CPU (100.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.54131e-05
PHY-3002 : Step(154): len = 471330, overlap = 365
PHY-3002 : Step(155): len = 473821, overlap = 315.5
PHY-3002 : Step(156): len = 465334, overlap = 302.531
PHY-3002 : Step(157): len = 461408, overlap = 275.719
PHY-3002 : Step(158): len = 459569, overlap = 251.594
PHY-3002 : Step(159): len = 457491, overlap = 241.188
PHY-3002 : Step(160): len = 454722, overlap = 240.219
PHY-3002 : Step(161): len = 454588, overlap = 238.594
PHY-3002 : Step(162): len = 450440, overlap = 224.375
PHY-3002 : Step(163): len = 447095, overlap = 224.062
PHY-3002 : Step(164): len = 445667, overlap = 220.188
PHY-3002 : Step(165): len = 443370, overlap = 212.469
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000190826
PHY-3002 : Step(166): len = 442655, overlap = 189.5
PHY-3002 : Step(167): len = 443552, overlap = 183.938
PHY-3002 : Step(168): len = 443728, overlap = 179.531
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000381652
PHY-3002 : Step(169): len = 448347, overlap = 163.281
PHY-3002 : Step(170): len = 455496, overlap = 154.062
PHY-3002 : Step(171): len = 457590, overlap = 148.906
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000763305
PHY-3002 : Step(172): len = 458174, overlap = 146.156
PHY-3002 : Step(173): len = 462661, overlap = 135.781
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81756, tnet num: 22108, tinst num: 19635, tnode num: 115014, tedge num: 128597.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.587324s wall, 1.531250s user + 0.062500s system = 1.593750s CPU (100.4%)

RUN-1004 : used memory is 573 MB, reserved memory is 547 MB, peak memory is 706 MB
OPT-1001 : Total overflow 518.00 peak overflow 3.66
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 455/22110.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 536184, over cnt = 2479(7%), over = 8676, worst = 25
PHY-1001 : End global iterations;  1.293107s wall, 1.968750s user + 0.031250s system = 2.000000s CPU (154.7%)

PHY-1001 : Congestion index: top1 = 56.03, top5 = 46.10, top10 = 41.67, top15 = 38.84.
PHY-1001 : End incremental global routing;  1.563280s wall, 2.234375s user + 0.031250s system = 2.265625s CPU (144.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22108 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.075229s wall, 1.046875s user + 0.015625s system = 1.062500s CPU (98.8%)

OPT-1001 : 17 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19556 has valid locations, 231 needs to be replaced
PHY-3001 : design contains 19849 instances, 5790 luts, 12436 seqs, 1530 slices, 295 macros(1530 instances: 1011 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 478180
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17360/22324.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 548760, over cnt = 2487(7%), over = 8663, worst = 25
PHY-1001 : End global iterations;  0.191460s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (106.1%)

PHY-1001 : Congestion index: top1 = 56.40, top5 = 46.44, top10 = 41.97, top15 = 39.17.
PHY-3001 : End congestion estimation;  0.499263s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (103.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82458, tnet num: 22322, tinst num: 19849, tnode num: 115990, tedge num: 129573.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.573281s wall, 1.546875s user + 0.015625s system = 1.562500s CPU (99.3%)

RUN-1004 : used memory is 616 MB, reserved memory is 609 MB, peak memory is 709 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22322 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.687715s wall, 2.656250s user + 0.031250s system = 2.687500s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(174): len = 478253, overlap = 2.4375
PHY-3002 : Step(175): len = 479472, overlap = 2.4375
PHY-3002 : Step(176): len = 480013, overlap = 2.5
PHY-3002 : Step(177): len = 480885, overlap = 2.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17390/22324.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 548224, over cnt = 2501(7%), over = 8755, worst = 25
PHY-1001 : End global iterations;  0.201417s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (124.1%)

PHY-1001 : Congestion index: top1 = 57.13, top5 = 46.82, top10 = 42.21, top15 = 39.37.
PHY-3001 : End congestion estimation;  0.470511s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (109.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22322 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.155390s wall, 1.140625s user + 0.015625s system = 1.156250s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000636269
PHY-3002 : Step(178): len = 480602, overlap = 138.125
PHY-3002 : Step(179): len = 480697, overlap = 137.938
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00127254
PHY-3002 : Step(180): len = 480870, overlap = 137.875
PHY-3002 : Step(181): len = 481165, overlap = 137.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00249858
PHY-3002 : Step(182): len = 481155, overlap = 137.188
PHY-3002 : Step(183): len = 481169, overlap = 137.344
PHY-3001 : Final: Len = 481169, Over = 137.344
PHY-3001 : End incremental placement;  5.865599s wall, 6.296875s user + 0.234375s system = 6.531250s CPU (111.3%)

OPT-1001 : Total overflow 521.59 peak overflow 3.66
OPT-1001 : End high-fanout net optimization;  9.073681s wall, 10.328125s user + 0.281250s system = 10.609375s CPU (116.9%)

OPT-1001 : Current memory(MB): used = 711, reserve = 691, peak = 727.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17385/22324.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 549064, over cnt = 2449(6%), over = 8208, worst = 25
PHY-1002 : len = 589632, over cnt = 1665(4%), over = 4301, worst = 22
PHY-1002 : len = 628768, over cnt = 673(1%), over = 1514, worst = 21
PHY-1002 : len = 653712, over cnt = 84(0%), over = 174, worst = 17
PHY-1002 : len = 657192, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.306626s wall, 1.890625s user + 0.093750s system = 1.984375s CPU (151.9%)

PHY-1001 : Congestion index: top1 = 49.01, top5 = 42.90, top10 = 39.78, top15 = 37.86.
OPT-1001 : End congestion update;  1.578906s wall, 2.171875s user + 0.093750s system = 2.265625s CPU (143.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22322 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.939147s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (99.8%)

OPT-0007 : Start: WNS 3787 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.524161s wall, 3.125000s user + 0.093750s system = 3.218750s CPU (127.5%)

OPT-1001 : Current memory(MB): used = 685, reserve = 664, peak = 727.
OPT-1001 : End physical optimization;  13.524093s wall, 15.468750s user + 0.453125s system = 15.921875s CPU (117.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5790 LUT to BLE ...
SYN-4008 : Packed 5790 LUT and 2733 SEQ to BLE.
SYN-4003 : Packing 9703 remaining SEQ's ...
SYN-4005 : Packed 3438 SEQ with LUT/SLICE
SYN-4006 : 156 single LUT's are left
SYN-4006 : 6265 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 12055/13944 primitive instances ...
PHY-3001 : End packing;  2.885518s wall, 2.890625s user + 0.000000s system = 2.890625s CPU (100.2%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8183 instances
RUN-1001 : 4044 mslices, 4044 lslices, 59 pads, 31 brams, 0 dsps
RUN-1001 : There are total 19641 nets
RUN-1001 : 13691 nets have 2 pins
RUN-1001 : 4529 nets have [3 - 5] pins
RUN-1001 : 910 nets have [6 - 10] pins
RUN-1001 : 371 nets have [11 - 20] pins
RUN-1001 : 131 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8181 instances, 8088 slices, 295 macros(1530 instances: 1011 mslices 519 lslices)
PHY-3001 : Cell area utilization is 86%
PHY-3001 : After packing: Len = 501023, Over = 363.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7948/19641.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 622696, over cnt = 1609(4%), over = 2644, worst = 8
PHY-1002 : len = 629816, over cnt = 992(2%), over = 1392, worst = 7
PHY-1002 : len = 641536, over cnt = 399(1%), over = 519, worst = 6
PHY-1002 : len = 647336, over cnt = 127(0%), over = 172, worst = 6
PHY-1002 : len = 651072, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.311924s wall, 2.046875s user + 0.015625s system = 2.062500s CPU (157.2%)

PHY-1001 : Congestion index: top1 = 49.44, top5 = 42.83, top10 = 39.51, top15 = 37.53.
PHY-3001 : End congestion estimation;  1.657993s wall, 2.390625s user + 0.015625s system = 2.406250s CPU (145.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68314, tnet num: 19639, tinst num: 8181, tnode num: 92638, tedge num: 112517.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.763976s wall, 1.734375s user + 0.031250s system = 1.765625s CPU (100.1%)

RUN-1004 : used memory is 611 MB, reserved memory is 604 MB, peak memory is 727 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19639 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.753506s wall, 2.703125s user + 0.046875s system = 2.750000s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.97386e-05
PHY-3002 : Step(184): len = 504427, overlap = 355.5
PHY-3002 : Step(185): len = 505870, overlap = 354.5
PHY-3002 : Step(186): len = 507024, overlap = 373
PHY-3002 : Step(187): len = 505956, overlap = 382.25
PHY-3002 : Step(188): len = 505185, overlap = 381
PHY-3002 : Step(189): len = 503806, overlap = 403.25
PHY-3002 : Step(190): len = 501577, overlap = 420.25
PHY-3002 : Step(191): len = 499500, overlap = 424.5
PHY-3002 : Step(192): len = 497487, overlap = 425.25
PHY-3002 : Step(193): len = 495711, overlap = 425.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.94771e-05
PHY-3002 : Step(194): len = 500273, overlap = 418.5
PHY-3002 : Step(195): len = 504913, overlap = 403
PHY-3002 : Step(196): len = 505647, overlap = 399.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000198954
PHY-3002 : Step(197): len = 513915, overlap = 380.25
PHY-3002 : Step(198): len = 525420, overlap = 358.25
PHY-3002 : Step(199): len = 525316, overlap = 345.25
PHY-3002 : Step(200): len = 524216, overlap = 340.75
PHY-3002 : Step(201): len = 524320, overlap = 329.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.814712s wall, 0.906250s user + 0.875000s system = 1.781250s CPU (218.6%)

PHY-3001 : Trial Legalized: Len = 638258
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 439/19641.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 719280, over cnt = 2515(7%), over = 4181, worst = 7
PHY-1002 : len = 736664, over cnt = 1501(4%), over = 2071, worst = 6
PHY-1002 : len = 750736, over cnt = 760(2%), over = 1012, worst = 6
PHY-1002 : len = 763000, over cnt = 235(0%), over = 313, worst = 4
PHY-1002 : len = 768664, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.251808s wall, 3.406250s user + 0.093750s system = 3.500000s CPU (155.4%)

PHY-1001 : Congestion index: top1 = 49.87, top5 = 45.35, top10 = 42.74, top15 = 40.98.
PHY-3001 : End congestion estimation;  2.631044s wall, 3.781250s user + 0.093750s system = 3.875000s CPU (147.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19639 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.958539s wall, 0.953125s user + 0.015625s system = 0.968750s CPU (101.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000180317
PHY-3002 : Step(202): len = 592179, overlap = 94.5
PHY-3002 : Step(203): len = 572959, overlap = 128.75
PHY-3002 : Step(204): len = 560901, overlap = 170.5
PHY-3002 : Step(205): len = 553985, overlap = 206.25
PHY-3002 : Step(206): len = 549446, overlap = 233.5
PHY-3002 : Step(207): len = 547109, overlap = 245.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000360634
PHY-3002 : Step(208): len = 551592, overlap = 238.5
PHY-3002 : Step(209): len = 555900, overlap = 243
PHY-3002 : Step(210): len = 556644, overlap = 242.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(211): len = 559099, overlap = 244.75
PHY-3002 : Step(212): len = 565828, overlap = 239
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.033828s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (92.4%)

PHY-3001 : Legalized: Len = 610104, Over = 0
PHY-3001 : Spreading special nets. 26 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.084859s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (110.5%)

PHY-3001 : 38 instances has been re-located, deltaX = 18, deltaY = 16, maxDist = 2.
PHY-3001 : Final: Len = 610868, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68314, tnet num: 19639, tinst num: 8181, tnode num: 92638, tedge num: 112517.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.076290s wall, 2.078125s user + 0.000000s system = 2.078125s CPU (100.1%)

RUN-1004 : used memory is 613 MB, reserved memory is 607 MB, peak memory is 727 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 4338/19641.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 706936, over cnt = 2295(6%), over = 3601, worst = 6
PHY-1002 : len = 719712, over cnt = 1303(3%), over = 1723, worst = 6
PHY-1002 : len = 732792, over cnt = 535(1%), over = 697, worst = 6
PHY-1002 : len = 743592, over cnt = 29(0%), over = 35, worst = 3
PHY-1002 : len = 744128, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.856359s wall, 3.078125s user + 0.031250s system = 3.109375s CPU (167.5%)

PHY-1001 : Congestion index: top1 = 46.92, top5 = 43.42, top10 = 41.05, top15 = 39.30.
PHY-1001 : End incremental global routing;  2.209352s wall, 3.437500s user + 0.031250s system = 3.468750s CPU (157.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19639 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.992681s wall, 0.937500s user + 0.031250s system = 0.968750s CPU (97.6%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8118 has valid locations, 11 needs to be replaced
PHY-3001 : design contains 8191 instances, 8098 slices, 295 macros(1530 instances: 1011 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 612162
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17699/19650.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 745440, over cnt = 12(0%), over = 13, worst = 2
PHY-1002 : len = 745464, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 745480, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.410696s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (110.3%)

PHY-1001 : Congestion index: top1 = 46.90, top5 = 43.42, top10 = 41.08, top15 = 39.34.
PHY-3001 : End congestion estimation;  0.737168s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (103.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68373, tnet num: 19648, tinst num: 8191, tnode num: 92707, tedge num: 112585.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.970721s wall, 1.968750s user + 0.000000s system = 1.968750s CPU (99.9%)

RUN-1004 : used memory is 645 MB, reserved memory is 632 MB, peak memory is 727 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19648 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.985463s wall, 2.937500s user + 0.046875s system = 2.984375s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(213): len = 612220, overlap = 0.5
PHY-3002 : Step(214): len = 612280, overlap = 0.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17695/19650.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 744832, over cnt = 16(0%), over = 19, worst = 2
PHY-1002 : len = 744880, over cnt = 10(0%), over = 11, worst = 2
PHY-1002 : len = 744992, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 744992, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 745032, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.713665s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (100.7%)

PHY-1001 : Congestion index: top1 = 47.00, top5 = 43.44, top10 = 41.07, top15 = 39.33.
PHY-3001 : End congestion estimation;  1.030745s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (100.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19648 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.965513s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (98.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000443452
PHY-3002 : Step(215): len = 612258, overlap = 0.75
PHY-3002 : Step(216): len = 612256, overlap = 1
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007142s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 612256, Over = 0
PHY-3001 : End spreading;  0.075343s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (103.7%)

PHY-3001 : Final: Len = 612256, Over = 0
PHY-3001 : End incremental placement;  6.397206s wall, 6.453125s user + 0.109375s system = 6.562500s CPU (102.6%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.116094s wall, 11.500000s user + 0.171875s system = 11.671875s CPU (115.4%)

OPT-1001 : Current memory(MB): used = 723, reserve = 711, peak = 728.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17697/19650.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 745040, over cnt = 10(0%), over = 11, worst = 2
PHY-1002 : len = 745080, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 745160, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 745208, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.547425s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (99.9%)

PHY-1001 : Congestion index: top1 = 46.90, top5 = 43.43, top10 = 41.06, top15 = 39.33.
OPT-1001 : End congestion update;  0.866909s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (99.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19648 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.815447s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (99.6%)

OPT-0007 : Start: WNS 4072 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.687517s wall, 1.687500s user + 0.000000s system = 1.687500s CPU (100.0%)

OPT-1001 : Current memory(MB): used = 723, reserve = 711, peak = 728.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19648 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.807736s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (98.7%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17709/19650.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 745208, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.121193s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (103.1%)

PHY-1001 : Congestion index: top1 = 46.90, top5 = 43.43, top10 = 41.06, top15 = 39.33.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19648 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.828198s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (100.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4072 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 46.551724
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4072ps with logic level 5 
RUN-1001 :       #2 path slack 4160ps with logic level 8 
OPT-1001 : End physical optimization;  16.227970s wall, 17.578125s user + 0.203125s system = 17.781250s CPU (109.6%)

RUN-1003 : finish command "place" in  73.458784s wall, 145.781250s user + 8.453125s system = 154.234375s CPU (210.0%)

RUN-1004 : used memory is 604 MB, reserved memory is 591 MB, peak memory is 728 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.757356s wall, 3.046875s user + 0.000000s system = 3.046875s CPU (173.4%)

RUN-1004 : used memory is 604 MB, reserved memory is 592 MB, peak memory is 728 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8193 instances
RUN-1001 : 4044 mslices, 4054 lslices, 59 pads, 31 brams, 0 dsps
RUN-1001 : There are total 19650 nets
RUN-1001 : 13692 nets have 2 pins
RUN-1001 : 4529 nets have [3 - 5] pins
RUN-1001 : 916 nets have [6 - 10] pins
RUN-1001 : 372 nets have [11 - 20] pins
RUN-1001 : 132 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68373, tnet num: 19648, tinst num: 8191, tnode num: 92707, tedge num: 112585.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.776758s wall, 1.750000s user + 0.015625s system = 1.765625s CPU (99.4%)

RUN-1004 : used memory is 601 MB, reserved memory is 594 MB, peak memory is 728 MB
PHY-1001 : 4044 mslices, 4054 lslices, 59 pads, 31 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19648 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 682608, over cnt = 2465(7%), over = 4100, worst = 7
PHY-1002 : len = 701616, over cnt = 1430(4%), over = 1941, worst = 7
PHY-1002 : len = 720032, over cnt = 441(1%), over = 570, worst = 6
PHY-1002 : len = 729376, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 729552, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.968148s wall, 3.265625s user + 0.062500s system = 3.328125s CPU (169.1%)

PHY-1001 : Congestion index: top1 = 47.20, top5 = 43.42, top10 = 40.89, top15 = 39.20.
PHY-1001 : End global routing;  2.336826s wall, 3.640625s user + 0.062500s system = 3.703125s CPU (158.5%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 708, reserve = 698, peak = 728.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 976, reserve = 965, peak = 976.
PHY-1001 : End build detailed router design. 4.794424s wall, 4.765625s user + 0.015625s system = 4.781250s CPU (99.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 187584, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.955806s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 1011, reserve = 1002, peak = 1011.
PHY-1001 : End phase 1; 0.962724s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (100.6%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.68135e+06, over cnt = 1458(0%), over = 1466, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1029, reserve = 1020, peak = 1029.
PHY-1001 : End initial routed; 14.060337s wall, 40.828125s user + 0.593750s system = 41.421875s CPU (294.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18354(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.974   |   0.000   |   0   
RUN-1001 :   Hold   |   0.112   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.573748s wall, 3.500000s user + 0.062500s system = 3.562500s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 1035, reserve = 1028, peak = 1035.
PHY-1001 : End phase 2; 17.634262s wall, 44.328125s user + 0.656250s system = 44.984375s CPU (255.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.68135e+06, over cnt = 1458(0%), over = 1466, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.252276s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (99.1%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.66746e+06, over cnt = 517(0%), over = 517, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 1.122151s wall, 1.562500s user + 0.000000s system = 1.562500s CPU (139.2%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.66819e+06, over cnt = 146(0%), over = 146, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.444830s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (122.9%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.6695e+06, over cnt = 27(0%), over = 27, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.277014s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (112.8%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.67008e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.184040s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (110.4%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.67013e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.162324s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (96.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18354(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.974   |   0.000   |   0   
RUN-1001 :   Hold   |   0.156   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.648396s wall, 3.640625s user + 0.000000s system = 3.640625s CPU (99.8%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 363 feed throughs used by 305 nets
PHY-1001 : End commit to database; 2.325385s wall, 2.296875s user + 0.031250s system = 2.328125s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1125, reserve = 1120, peak = 1125.
PHY-1001 : End phase 3; 8.932658s wall, 9.515625s user + 0.031250s system = 9.546875s CPU (106.9%)

PHY-1003 : Routed, final wirelength = 1.67013e+06
PHY-1001 : Current memory(MB): used = 1129, reserve = 1125, peak = 1129.
PHY-1001 : End export database. 0.179999s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (95.5%)

PHY-1001 : End detail routing;  32.931811s wall, 60.203125s user + 0.703125s system = 60.906250s CPU (184.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68373, tnet num: 19648, tinst num: 8191, tnode num: 92707, tedge num: 112585.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.745677s wall, 1.734375s user + 0.000000s system = 1.734375s CPU (99.4%)

RUN-1004 : used memory is 963 MB, reserved memory is 945 MB, peak memory is 1129 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  41.641376s wall, 70.171875s user + 0.796875s system = 70.968750s CPU (170.4%)

RUN-1004 : used memory is 965 MB, reserved memory is 958 MB, peak memory is 1129 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8990   out of  19600   45.87%
#reg                    12530   out of  19600   63.93%
#le                     15210
  #lut only              2680   out of  15210   17.62%
  #reg only              6220   out of  15210   40.89%
  #lut&reg               6310   out of  15210   41.49%
#dsp                        0   out of     29    0.00%
#bram                      31   out of     64   48.44%
  #bram9k                  29
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       59   out of    188   31.38%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6795
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          203
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         F2        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         D1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         B3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         C1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         A3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         A2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         B2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         B1        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F15        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDZ        INPUT        K15        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         P2        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         N4        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         M5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         N6        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         N5        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         P5        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M3        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         M4        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         H2        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         M1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         L1        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         N1        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         N3        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         M2        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         T4        LVCMOS33           8            NONE           OREG       
    Fog_ENZ        OUTPUT         T7        LVCMOS33           8            NONE           OREG       
    Fog_INT        OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT         C3        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT        B15        LVCMOS33           8            N/A            OREG       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        R15        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         K2        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT         F6        LVCMOS25           8            N/A            NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15210  |7460    |1530    |12574   |31      |0       |
|  AnyFog_dataX                      |AnyFog          |219    |89      |22      |175     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |99     |70      |22      |55      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |206    |76      |22      |170     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |84     |59      |22      |48      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |212    |120     |22      |170     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |92     |66      |22      |50      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2895   |650     |39      |2816    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |59     |38      |5       |50      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |221    |74      |5       |212     |0       |0       |
|    STADOP_com2                     |STADOP          |544    |83      |0       |542     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |63     |42      |14      |38      |0       |0       |
|    head_com2                       |uniheading      |272    |81      |5       |261     |0       |0       |
|    rmc_com2                        |Gprmc           |42     |42      |0       |33      |0       |0       |
|    uart_com2                       |Agrica          |1388   |272     |10      |1374    |0       |0       |
|  COM3                              |COM3_Control    |273    |162     |19      |232     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |61     |43      |5       |51      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |60     |42      |14      |38      |0       |0       |
|    rmc_com3                        |Gprmc           |152    |77      |0       |143     |0       |0       |
|  DATA                              |Data_Processing |8818   |4509    |1122    |7019    |0       |0       |
|    DIV_Dtemp                       |Divider         |805    |335     |84      |676     |0       |0       |
|    DIV_Utemp                       |Divider         |584    |312     |84      |458     |0       |0       |
|    DIV_accX                        |Divider         |543    |288     |84      |417     |0       |0       |
|    DIV_accY                        |Divider         |673    |344     |102     |520     |0       |0       |
|    DIV_accZ                        |Divider         |697    |381     |132     |491     |0       |0       |
|    DIV_rateX                       |Divider         |704    |396     |132     |500     |0       |0       |
|    DIV_rateY                       |Divider         |556    |343     |132     |348     |0       |0       |
|    DIV_rateZ                       |Divider         |559    |359     |132     |356     |0       |0       |
|    genclk                          |genclk          |266    |157     |89      |106     |0       |0       |
|  FMC                               |FMC_Ctrl        |502    |449     |43      |362     |0       |0       |
|  IIC                               |I2C_master      |286    |228     |11      |265     |0       |0       |
|  IMU_CTRL                          |SCHA634         |914    |679     |61      |738     |0       |0       |
|    CtrlData                        |CtrlData        |465    |399     |47      |329     |0       |0       |
|      usms                          |Time_1ms        |28     |22      |5       |20      |0       |0       |
|    SPIM                            |SPI_SCHA634     |449    |280     |14      |409     |0       |0       |
|  POWER                             |POWER_EN        |101    |47      |38      |42      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |771    |451     |131     |528     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |771    |451     |131     |528     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |357    |184     |0       |340     |0       |0       |
|        reg_inst                    |register        |355    |182     |0       |338     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |414    |267     |131     |188     |0       |0       |
|        bus_inst                    |bus_top         |209    |135     |74      |78      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |26     |16      |10      |10      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |28     |18      |10      |11      |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |51     |33      |18      |19      |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |52     |34      |18      |19      |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |52     |34      |18      |19      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |128    |93      |29      |79      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13632  
    #2          2       3477   
    #3          3        749   
    #4          4        303   
    #5        5-10       974   
    #6        11-50      436   
    #7       51-100      10    
    #8       101-500      3    
    #9        >500        2    
  Average     2.13             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.127461s wall, 3.593750s user + 0.000000s system = 3.593750s CPU (168.9%)

RUN-1004 : used memory is 966 MB, reserved memory is 960 MB, peak memory is 1129 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68373, tnet num: 19648, tinst num: 8191, tnode num: 92707, tedge num: 112585.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.783611s wall, 1.781250s user + 0.000000s system = 1.781250s CPU (99.9%)

RUN-1004 : used memory is 970 MB, reserved memory is 963 MB, peak memory is 1129 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19648 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.517168s wall, 1.500000s user + 0.031250s system = 1.531250s CPU (100.9%)

RUN-1004 : used memory is 1038 MB, reserved memory is 1038 MB, peak memory is 1129 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 0bce363d9dc7be58103beed49e9329709c7ad8523877bc4c778ece02c7dc868c -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8191
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19650, pip num: 147311
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 363
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3235 valid insts, and 413739 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011111010101000011111110
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.603688s wall, 127.015625s user + 0.203125s system = 127.218750s CPU (1009.4%)

RUN-1004 : used memory is 1132 MB, reserved memory is 1117 MB, peak memory is 1304 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250820_143703.log"

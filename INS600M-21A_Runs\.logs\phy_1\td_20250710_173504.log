============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Jul 10 17:35:04 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(520)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
HDL-5007 WARNING: data object 'header_index' is already declared in ../../Src/GNSS/GNRMC_Tx.v(39)
HDL-1007 : previous declaration of 'header_index' is from here in ../../Src/GNSS/GNRMC_Tx.v(28)
HDL-5007 WARNING: second declaration of 'header_index' is ignored in ../../Src/GNSS/GNRMC_Tx.v(39)
HDL-5007 WARNING: data object 'header_sending' is already declared in ../../Src/GNSS/GNRMC_Tx.v(40)
HDL-1007 : previous declaration of 'header_sending' is from here in ../../Src/GNSS/GNRMC_Tx.v(27)
HDL-5007 WARNING: second declaration of 'header_sending' is ignored in ../../Src/GNSS/GNRMC_Tx.v(40)
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.114104s wall, 1.609375s user + 3.500000s system = 5.109375s CPU (99.9%)

RUN-1004 : used memory is 79 MB, reserved memory is 41 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  2.040825s wall, 2.000000s user + 0.046875s system = 2.046875s CPU (100.3%)

RUN-1004 : used memory is 326 MB, reserved memory is 295 MB, peak memory is 330 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "read_sdc -ip Asys_fifo8x8 ../../al_ip/Asys_fifo8x8.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 95223719919616"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 95223719919616"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  9.6999999999999993 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 9.6999999999999993"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  9.6999999999999993 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 9.6999999999999993"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 10 view nodes, 47 trigger nets, 47 data nets.
KIT-1004 : Chipwatcher code = 1100100110110000
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=10,BUS_DIN_NUM=47,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb0100101,32'sb0101101,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01100110,32'sb01111010,32'sb010000000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=156) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=156) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=10,BUS_DIN_NUM=47,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb0100101,32'sb0101101,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01100110,32'sb01111010,32'sb010000000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=10,BUS_DIN_NUM=47,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb0100101,32'sb0101101,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01100110,32'sb01111010,32'sb010000000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=10,BUS_DIN_NUM=47,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb0100101,32'sb0101101,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01100110,32'sb01111010,32'sb010000000})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=156)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=156)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=10,BUS_DIN_NUM=47,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb0100101,32'sb0101101,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01100110,32'sb01111010,32'sb010000000})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=10,BUS_DIN_NUM=47,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb0100101,32'sb0101101,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01100110,32'sb01111010,32'sb010000000})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 26502/42 useful/useless nets, 23189/25 useful/useless insts
SYN-1016 : Merged 49 instances.
SYN-1032 : 26090/26 useful/useless nets, 23660/22 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 5 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 5 mux instances.
SYN-1015 : Optimize round 1, 472 better
SYN-1014 : Optimize round 2
SYN-1032 : 25679/75 useful/useless nets, 23249/80 useful/useless insts
SYN-1015 : Optimize round 2, 160 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.547215s wall, 2.437500s user + 0.109375s system = 2.546875s CPU (100.0%)

RUN-1004 : used memory is 354 MB, reserved memory is 321 MB, peak memory is 356 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 25739/367 useful/useless nets, 23350/52 useful/useless insts
SYN-1016 : Merged 57 instances.
SYN-2571 : Optimize after map_dsp, round 1, 476 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 43 instances.
SYN-2501 : Optimize round 1, 87 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 20 macro adder
SYN-1019 : Optimized 21 mux instances.
SYN-1016 : Merged 17 instances.
SYN-1032 : 26216/5 useful/useless nets, 23827/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 98653, tnet num: 26216, tinst num: 23826, tnode num: 137983, tedge num: 153751.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.277196s wall, 1.250000s user + 0.031250s system = 1.281250s CPU (100.3%)

RUN-1004 : used memory is 520 MB, reserved memory is 490 MB, peak memory is 520 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 26216 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 265 (3.48), #lev = 7 (1.86)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 265 (3.48), #lev = 7 (1.86)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 656 instances into 265 LUTs, name keeping = 71%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 477 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 132 adder to BLE ...
SYN-4008 : Packed 132 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.860170s wall, 4.734375s user + 0.125000s system = 4.859375s CPU (100.0%)

RUN-1004 : used memory is 387 MB, reserved memory is 371 MB, peak memory is 649 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.782863s wall, 7.515625s user + 0.265625s system = 7.781250s CPU (100.0%)

RUN-1004 : used memory is 388 MB, reserved memory is 372 MB, peak memory is 649 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/uart_com2/AGRIC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/uart_com2/AGRIC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/uart_com2/AGRIC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/uart_com2/AGRIC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/uart_com2/AGRIC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/uart_com2/AGRIC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/uart_com2/AGRIC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/uart_com2/AGRIC_data[0]
SYN-5055 WARNING: The kept net COM3/rmc_com3/GPRMC_end will be merged to another kept net COM3/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM3/rmc_com3/GPRMC_sta will be merged to another kept net COM3/GNRMC/GPRMC_sta
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (328 clock/control pins, 0 other pins).
SYN-4027 : Net COM3/GNRMC/u_fifo/wr_to_rd_cross_inst/secondary_clk_i is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net COM3/GNRMC/u_fifo/wr_to_rd_cross_inst/secondary_clk_i as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 22988 instances
RUN-0007 : 7149 luts, 14274 seqs, 956 mslices, 502 lslices, 60 pads, 42 brams, 0 dsps
RUN-1001 : There are total 25401 nets
RUN-1001 : 19637 nets have 2 pins
RUN-1001 : 4271 nets have [3 - 5] pins
RUN-1001 : 1084 nets have [6 - 10] pins
RUN-1001 : 253 nets have [11 - 20] pins
RUN-1001 : 121 nets have [21 - 99] pins
RUN-1001 : 35 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4759     
RUN-1001 :   No   |  No   |  Yes  |     735     
RUN-1001 :   No   |  Yes  |  No   |     88      
RUN-1001 :   Yes  |  No   |  No   |    8131     
RUN-1001 :   Yes  |  No   |  Yes  |     510     
RUN-1001 :   Yes  |  Yes  |  No   |     51      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  371  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 380
PHY-3001 : Initial placement ...
PHY-3001 : design contains 22986 instances, 7149 luts, 14274 seqs, 1458 slices, 291 macros(1458 instances: 956 mslices 502 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 73%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 96821, tnet num: 25399, tinst num: 22986, tnode num: 136040, tedge num: 151959.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.319030s wall, 1.281250s user + 0.031250s system = 1.312500s CPU (99.5%)

RUN-1004 : used memory is 588 MB, reserved memory is 563 MB, peak memory is 649 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 25399 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.368615s wall, 2.312500s user + 0.046875s system = 2.359375s CPU (99.6%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.85368e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 22986.
PHY-3001 : Level 1 #clusters 3019.
PHY-3001 : End clustering;  0.298431s wall, 0.531250s user + 0.015625s system = 0.546875s CPU (183.2%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 73%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 1.00707e+06, overlap = 768.625
PHY-3002 : Step(2): len = 892395, overlap = 887.875
PHY-3002 : Step(3): len = 592533, overlap = 1059
PHY-3002 : Step(4): len = 531238, overlap = 1114.09
PHY-3002 : Step(5): len = 420552, overlap = 1260.5
PHY-3002 : Step(6): len = 369109, overlap = 1341.09
PHY-3002 : Step(7): len = 306781, overlap = 1453.12
PHY-3002 : Step(8): len = 272465, overlap = 1534.91
PHY-3002 : Step(9): len = 233839, overlap = 1618.62
PHY-3002 : Step(10): len = 205401, overlap = 1664.22
PHY-3002 : Step(11): len = 178708, overlap = 1698.38
PHY-3002 : Step(12): len = 167429, overlap = 1714.41
PHY-3002 : Step(13): len = 146925, overlap = 1731.34
PHY-3002 : Step(14): len = 136151, overlap = 1754.88
PHY-3002 : Step(15): len = 122833, overlap = 1770.03
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.15382e-07
PHY-3002 : Step(16): len = 123021, overlap = 1756.16
PHY-3002 : Step(17): len = 157772, overlap = 1702.69
PHY-3002 : Step(18): len = 161054, overlap = 1653.06
PHY-3002 : Step(19): len = 175564, overlap = 1577.62
PHY-3002 : Step(20): len = 172320, overlap = 1523.62
PHY-3002 : Step(21): len = 175190, overlap = 1491
PHY-3002 : Step(22): len = 166468, overlap = 1473.34
PHY-3002 : Step(23): len = 167032, overlap = 1457.69
PHY-3002 : Step(24): len = 163013, overlap = 1442.47
PHY-3002 : Step(25): len = 161299, overlap = 1443.81
PHY-3002 : Step(26): len = 159036, overlap = 1432.5
PHY-3002 : Step(27): len = 158418, overlap = 1420.56
PHY-3002 : Step(28): len = 156488, overlap = 1421.59
PHY-3002 : Step(29): len = 154271, overlap = 1408.38
PHY-3002 : Step(30): len = 152340, overlap = 1408.28
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 1.43076e-06
PHY-3002 : Step(31): len = 160263, overlap = 1413.25
PHY-3002 : Step(32): len = 176121, overlap = 1380.66
PHY-3002 : Step(33): len = 173853, overlap = 1337.62
PHY-3002 : Step(34): len = 176853, overlap = 1329.5
PHY-3002 : Step(35): len = 174772, overlap = 1322.19
PHY-3002 : Step(36): len = 176514, overlap = 1326
PHY-3002 : Step(37): len = 174250, overlap = 1320.59
PHY-3002 : Step(38): len = 174938, overlap = 1316.28
PHY-3002 : Step(39): len = 171691, overlap = 1318.84
PHY-3002 : Step(40): len = 172180, overlap = 1309.97
PHY-3002 : Step(41): len = 170220, overlap = 1324.28
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 2.86153e-06
PHY-3002 : Step(42): len = 179589, overlap = 1308.22
PHY-3002 : Step(43): len = 194407, overlap = 1233.06
PHY-3002 : Step(44): len = 197425, overlap = 1131.34
PHY-3002 : Step(45): len = 201672, overlap = 1096.03
PHY-3002 : Step(46): len = 201184, overlap = 1090.59
PHY-3002 : Step(47): len = 203078, overlap = 1071.97
PHY-3002 : Step(48): len = 201825, overlap = 1065.69
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 5.72306e-06
PHY-3002 : Step(49): len = 213456, overlap = 1040.53
PHY-3002 : Step(50): len = 237566, overlap = 980.406
PHY-3002 : Step(51): len = 245828, overlap = 942.844
PHY-3002 : Step(52): len = 253717, overlap = 915.219
PHY-3002 : Step(53): len = 255512, overlap = 915.406
PHY-3002 : Step(54): len = 256212, overlap = 927.25
PHY-3002 : Step(55): len = 254955, overlap = 932.594
PHY-3002 : Step(56): len = 250956, overlap = 916.969
PHY-3002 : Step(57): len = 249785, overlap = 913.906
PHY-3002 : Step(58): len = 248051, overlap = 926.75
PHY-3002 : Step(59): len = 246845, overlap = 926.719
PHY-3002 : Step(60): len = 244513, overlap = 919.812
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.14461e-05
PHY-3002 : Step(61): len = 258396, overlap = 863.781
PHY-3002 : Step(62): len = 273510, overlap = 799.875
PHY-3002 : Step(63): len = 277681, overlap = 767.656
PHY-3002 : Step(64): len = 281762, overlap = 764.531
PHY-3002 : Step(65): len = 282742, overlap = 755.656
PHY-3002 : Step(66): len = 283320, overlap = 734.656
PHY-3002 : Step(67): len = 283453, overlap = 725.656
PHY-3002 : Step(68): len = 285445, overlap = 688.156
PHY-3002 : Step(69): len = 284897, overlap = 682.312
PHY-3002 : Step(70): len = 282345, overlap = 686.031
PHY-3002 : Step(71): len = 281301, overlap = 698.375
PHY-3002 : Step(72): len = 279143, overlap = 712.719
PHY-3002 : Step(73): len = 277066, overlap = 710.719
PHY-3002 : Step(74): len = 274518, overlap = 719.125
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 2.28922e-05
PHY-3002 : Step(75): len = 286971, overlap = 682.25
PHY-3002 : Step(76): len = 295787, overlap = 648.625
PHY-3002 : Step(77): len = 298932, overlap = 620.688
PHY-3002 : Step(78): len = 300774, overlap = 602.281
PHY-3002 : Step(79): len = 299037, overlap = 609.969
PHY-3002 : Step(80): len = 298812, overlap = 616.125
PHY-3002 : Step(81): len = 297788, overlap = 616.406
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 4.55667e-05
PHY-3002 : Step(82): len = 308692, overlap = 592.938
PHY-3002 : Step(83): len = 318112, overlap = 582.531
PHY-3002 : Step(84): len = 322171, overlap = 555.625
PHY-3002 : Step(85): len = 323158, overlap = 555.219
PHY-3002 : Step(86): len = 322302, overlap = 550.25
PHY-3002 : Step(87): len = 322040, overlap = 541.719
PHY-3002 : Step(88): len = 320494, overlap = 540.25
PHY-3002 : Step(89): len = 321098, overlap = 547
PHY-3002 : Step(90): len = 320379, overlap = 544.906
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 9.11333e-05
PHY-3002 : Step(91): len = 325936, overlap = 530.875
PHY-3002 : Step(92): len = 333793, overlap = 489.281
PHY-3002 : Step(93): len = 337206, overlap = 479.688
PHY-3002 : Step(94): len = 338657, overlap = 471.406
PHY-3002 : Step(95): len = 338436, overlap = 469.406
PHY-3002 : Step(96): len = 338529, overlap = 455.125
PHY-3002 : Step(97): len = 337689, overlap = 461.344
PHY-3002 : Step(98): len = 336855, overlap = 452.5
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000168225
PHY-3002 : Step(99): len = 339800, overlap = 447.219
PHY-3002 : Step(100): len = 343904, overlap = 429.062
PHY-3002 : Step(101): len = 346341, overlap = 428.719
PHY-3002 : Step(102): len = 348521, overlap = 426.438
PHY-3002 : Step(103): len = 350123, overlap = 424.719
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000284803
PHY-3002 : Step(104): len = 352297, overlap = 422.625
PHY-3002 : Step(105): len = 355194, overlap = 417.875
PHY-3002 : Step(106): len = 356991, overlap = 410.969
PHY-3002 : Step(107): len = 358577, overlap = 386.938
PHY-3002 : Step(108): len = 359516, overlap = 365.75
PHY-3002 : Step(109): len = 361069, overlap = 351.469
PHY-3002 : Step(110): len = 360709, overlap = 355.812
PHY-3002 : Step(111): len = 360872, overlap = 355.344
PHY-3002 : Step(112): len = 360211, overlap = 372.062
PHY-3002 : Step(113): len = 359618, overlap = 361
PHY-3002 : Step(114): len = 358624, overlap = 366.156
PHY-3001 : :::11::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(115): len = 359729, overlap = 362.562
PHY-3002 : Step(116): len = 362453, overlap = 355.219
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.010339s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 78%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/25401.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 483392, over cnt = 1585(4%), over = 7873, worst = 34
PHY-1001 : End global iterations;  0.750822s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (137.3%)

PHY-1001 : Congestion index: top1 = 89.44, top5 = 63.08, top10 = 51.27, top15 = 44.90.
PHY-3001 : End congestion estimation;  0.983046s wall, 1.265625s user + 0.000000s system = 1.265625s CPU (128.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 25399 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.020641s wall, 0.968750s user + 0.046875s system = 1.015625s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000118613
PHY-3002 : Step(117): len = 402974, overlap = 311.312
PHY-3002 : Step(118): len = 420604, overlap = 306.844
PHY-3002 : Step(119): len = 412518, overlap = 301.062
PHY-3002 : Step(120): len = 416821, overlap = 288.656
PHY-3002 : Step(121): len = 421864, overlap = 273.312
PHY-3002 : Step(122): len = 421971, overlap = 255.531
PHY-3002 : Step(123): len = 425983, overlap = 248.344
PHY-3002 : Step(124): len = 427463, overlap = 240.562
PHY-3002 : Step(125): len = 427571, overlap = 231.281
PHY-3002 : Step(126): len = 428622, overlap = 228.562
PHY-3002 : Step(127): len = 431018, overlap = 227.875
PHY-3002 : Step(128): len = 430421, overlap = 224.188
PHY-3002 : Step(129): len = 431290, overlap = 218.469
PHY-3002 : Step(130): len = 429342, overlap = 218.188
PHY-3002 : Step(131): len = 430058, overlap = 220.875
PHY-3002 : Step(132): len = 431929, overlap = 224.062
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000237227
PHY-3002 : Step(133): len = 431543, overlap = 220.406
PHY-3002 : Step(134): len = 433707, overlap = 221.094
PHY-3002 : Step(135): len = 436885, overlap = 221.219
PHY-3002 : Step(136): len = 439828, overlap = 225.75
PHY-3002 : Step(137): len = 440385, overlap = 231.969
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(138): len = 443080, overlap = 228.406
PHY-3002 : Step(139): len = 443080, overlap = 228.406
PHY-3002 : Step(140): len = 443375, overlap = 232.031
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 78%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 189/25401.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 523216, over cnt = 2409(6%), over = 12613, worst = 69
PHY-1001 : End global iterations;  0.926376s wall, 1.609375s user + 0.062500s system = 1.671875s CPU (180.5%)

PHY-1001 : Congestion index: top1 = 90.60, top5 = 65.76, top10 = 55.82, top15 = 50.04.
PHY-3001 : End congestion estimation;  1.272522s wall, 1.953125s user + 0.062500s system = 2.015625s CPU (158.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 25399 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.044755s wall, 1.000000s user + 0.046875s system = 1.046875s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.64187e-05
PHY-3002 : Step(141): len = 441866, overlap = 528.031
PHY-3002 : Step(142): len = 451884, overlap = 457.156
PHY-3002 : Step(143): len = 443601, overlap = 435.031
PHY-3002 : Step(144): len = 444946, overlap = 403.312
PHY-3002 : Step(145): len = 441451, overlap = 362.406
PHY-3002 : Step(146): len = 438398, overlap = 341.688
PHY-3002 : Step(147): len = 436636, overlap = 325.281
PHY-3002 : Step(148): len = 432973, overlap = 318.531
PHY-3002 : Step(149): len = 431203, overlap = 318.875
PHY-3002 : Step(150): len = 426892, overlap = 314.562
PHY-3002 : Step(151): len = 425127, overlap = 312.406
PHY-3002 : Step(152): len = 423630, overlap = 315.688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000192837
PHY-3002 : Step(153): len = 423494, overlap = 311
PHY-3002 : Step(154): len = 427476, overlap = 305.938
PHY-3002 : Step(155): len = 432692, overlap = 282.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000385675
PHY-3002 : Step(156): len = 434847, overlap = 264.781
PHY-3002 : Step(157): len = 442959, overlap = 245.219
PHY-3002 : Step(158): len = 447602, overlap = 227.969
PHY-3002 : Step(159): len = 444880, overlap = 237.094
PHY-3002 : Step(160): len = 445117, overlap = 237.031
PHY-3002 : Step(161): len = 445453, overlap = 237.719
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00077135
PHY-3002 : Step(162): len = 448380, overlap = 230.969
PHY-3002 : Step(163): len = 452562, overlap = 223.969
PHY-3002 : Step(164): len = 455375, overlap = 220.406
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00147909
PHY-3002 : Step(165): len = 456406, overlap = 221.344
PHY-3002 : Step(166): len = 458716, overlap = 213.531
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 96821, tnet num: 25399, tinst num: 22986, tnode num: 136040, tedge num: 151959.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.590187s wall, 1.578125s user + 0.031250s system = 1.609375s CPU (101.2%)

RUN-1004 : used memory is 628 MB, reserved memory is 606 MB, peak memory is 790 MB
OPT-1001 : Total overflow 634.88 peak overflow 3.78
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 717/25401.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 563424, over cnt = 2889(8%), over = 11332, worst = 35
PHY-1001 : End global iterations;  1.215515s wall, 1.984375s user + 0.062500s system = 2.046875s CPU (168.4%)

PHY-1001 : Congestion index: top1 = 65.69, top5 = 53.90, top10 = 47.84, top15 = 44.08.
PHY-1001 : End incremental global routing;  1.449932s wall, 2.218750s user + 0.062500s system = 2.281250s CPU (157.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 25399 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.434512s wall, 1.421875s user + 0.015625s system = 1.437500s CPU (100.2%)

OPT-1001 : 23 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 22900 has valid locations, 336 needs to be replaced
PHY-3001 : design contains 23299 instances, 7305 luts, 14431 seqs, 1458 slices, 291 macros(1458 instances: 956 mslices 502 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 483101
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 79%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 18753/25714.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 582976, over cnt = 2967(8%), over = 11492, worst = 35
PHY-1001 : End global iterations;  0.242353s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (135.4%)

PHY-1001 : Congestion index: top1 = 67.00, top5 = 54.99, top10 = 48.79, top15 = 44.96.
PHY-3001 : End congestion estimation;  0.495873s wall, 0.546875s user + 0.015625s system = 0.562500s CPU (113.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 97981, tnet num: 25712, tinst num: 23299, tnode num: 137636, tedge num: 153653.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.635894s wall, 1.593750s user + 0.031250s system = 1.625000s CPU (99.3%)

RUN-1004 : used memory is 683 MB, reserved memory is 678 MB, peak memory is 790 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 25712 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.761484s wall, 2.687500s user + 0.062500s system = 2.750000s CPU (99.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(167): len = 482582, overlap = 13.25
PHY-3002 : Step(168): len = 483423, overlap = 13.75
PHY-3002 : Step(169): len = 484553, overlap = 13.25
PHY-3002 : Step(170): len = 486162, overlap = 14.625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(171): len = 485969, overlap = 14.125
PHY-3002 : Step(172): len = 486880, overlap = 13.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(173): len = 487295, overlap = 13
PHY-3002 : Step(174): len = 488652, overlap = 12.6875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 79%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 18770/25714.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 584352, over cnt = 2959(8%), over = 11596, worst = 35
PHY-1001 : End global iterations;  0.242241s wall, 0.281250s user + 0.031250s system = 0.312500s CPU (129.0%)

PHY-1001 : Congestion index: top1 = 66.83, top5 = 54.91, top10 = 48.83, top15 = 45.04.
PHY-3001 : End congestion estimation;  0.494226s wall, 0.515625s user + 0.031250s system = 0.546875s CPU (110.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 25712 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.101123s wall, 1.078125s user + 0.031250s system = 1.109375s CPU (100.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000706342
PHY-3002 : Step(175): len = 488495, overlap = 217.875
PHY-3002 : Step(176): len = 488654, overlap = 217.375
PHY-3002 : Step(177): len = 489018, overlap = 217.312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00141268
PHY-3002 : Step(178): len = 489441, overlap = 217.125
PHY-3002 : Step(179): len = 489729, overlap = 218.031
PHY-3001 : Final: Len = 489729, Over = 218.031
PHY-3001 : End incremental placement;  6.220256s wall, 6.750000s user + 0.406250s system = 7.156250s CPU (115.0%)

OPT-1001 : Total overflow 642.97 peak overflow 3.78
OPT-1001 : End high-fanout net optimization;  9.697266s wall, 11.156250s user + 0.484375s system = 11.640625s CPU (120.0%)

OPT-1001 : Current memory(MB): used = 793, reserve = 777, peak = 813.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 18806/25714.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 588208, over cnt = 2908(8%), over = 10509, worst = 35
PHY-1002 : len = 654320, over cnt = 2125(6%), over = 5222, worst = 20
PHY-1002 : len = 707904, over cnt = 1022(2%), over = 1939, worst = 14
PHY-1002 : len = 736544, over cnt = 156(0%), over = 243, worst = 8
PHY-1002 : len = 741912, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.645674s wall, 2.468750s user + 0.015625s system = 2.484375s CPU (151.0%)

PHY-1001 : Congestion index: top1 = 56.12, top5 = 49.04, top10 = 45.29, top15 = 42.95.
OPT-1001 : End congestion update;  1.908666s wall, 2.734375s user + 0.015625s system = 2.750000s CPU (144.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 25712 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.969127s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (100.0%)

OPT-0007 : Start: WNS 3274 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.885433s wall, 3.718750s user + 0.015625s system = 3.734375s CPU (129.4%)

OPT-1001 : Current memory(MB): used = 764, reserve = 748, peak = 813.
OPT-1001 : End physical optimization;  14.543678s wall, 16.984375s user + 0.531250s system = 17.515625s CPU (120.4%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 7305 LUT to BLE ...
SYN-4008 : Packed 7305 LUT and 2742 SEQ to BLE.
SYN-4003 : Packing 11689 remaining SEQ's ...
SYN-4005 : Packed 4938 SEQ with LUT/SLICE
SYN-4006 : 157 single LUT's are left
SYN-4006 : 6751 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 14056/15859 primitive instances ...
PHY-3001 : End packing;  3.682364s wall, 3.656250s user + 0.000000s system = 3.656250s CPU (99.3%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 9318 instances
RUN-1001 : 4606 mslices, 4605 lslices, 60 pads, 42 brams, 0 dsps
RUN-1001 : There are total 23099 nets
RUN-1001 : 16938 nets have 2 pins
RUN-1001 : 4457 nets have [3 - 5] pins
RUN-1001 : 1059 nets have [6 - 10] pins
RUN-1001 : 369 nets have [11 - 20] pins
RUN-1001 : 264 nets have [21 - 99] pins
RUN-1001 : 12 nets have 100+ pins
PHY-3001 : design contains 9316 instances, 9211 slices, 291 macros(1458 instances: 956 mslices 502 lslices)
PHY-3001 : Cell area utilization is 95%
PHY-3001 : After packing: Len = 527350, Over = 520.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 95%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 8771/23099.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 706056, over cnt = 2258(6%), over = 3817, worst = 8
PHY-1002 : len = 715664, over cnt = 1582(4%), over = 2330, worst = 7
PHY-1002 : len = 736520, over cnt = 649(1%), over = 887, worst = 7
PHY-1002 : len = 748824, over cnt = 159(0%), over = 180, worst = 5
PHY-1002 : len = 755376, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.586664s wall, 2.390625s user + 0.000000s system = 2.390625s CPU (150.7%)

PHY-1001 : Congestion index: top1 = 60.04, top5 = 51.90, top10 = 47.17, top15 = 44.12.
PHY-3001 : End congestion estimation;  1.930832s wall, 2.750000s user + 0.000000s system = 2.750000s CPU (142.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82314, tnet num: 23097, tinst num: 9316, tnode num: 111143, tedge num: 134434.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.952180s wall, 1.921875s user + 0.031250s system = 1.953125s CPU (100.0%)

RUN-1004 : used memory is 672 MB, reserved memory is 656 MB, peak memory is 813 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 23097 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.995799s wall, 2.937500s user + 0.062500s system = 3.000000s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.16352e-05
PHY-3002 : Step(180): len = 518771, overlap = 489.5
PHY-3002 : Step(181): len = 512648, overlap = 503.75
PHY-3002 : Step(182): len = 507391, overlap = 521.5
PHY-3002 : Step(183): len = 505196, overlap = 533.75
PHY-3002 : Step(184): len = 501790, overlap = 533.75
PHY-3002 : Step(185): len = 498782, overlap = 532.75
PHY-3002 : Step(186): len = 496555, overlap = 535.25
PHY-3002 : Step(187): len = 495409, overlap = 534.75
PHY-3002 : Step(188): len = 493368, overlap = 535.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.32704e-05
PHY-3002 : Step(189): len = 500441, overlap = 516
PHY-3002 : Step(190): len = 507583, overlap = 500.75
PHY-3002 : Step(191): len = 506157, overlap = 506.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000166541
PHY-3002 : Step(192): len = 514620, overlap = 499
PHY-3002 : Step(193): len = 523608, overlap = 478.5
PHY-3002 : Step(194): len = 521710, overlap = 475.25
PHY-3002 : Step(195): len = 520312, overlap = 478
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(196): len = 525146, overlap = 473.75
PHY-3002 : Step(197): len = 533850, overlap = 466.25
PHY-3002 : Step(198): len = 539705, overlap = 464.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.507548s wall, 0.828125s user + 0.343750s system = 1.171875s CPU (230.9%)

PHY-3001 : Trial Legalized: Len = 911721
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 95%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 755/23099.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 1.04362e+06, over cnt = 3413(9%), over = 5957, worst = 7
PHY-1002 : len = 1.06717e+06, over cnt = 2354(6%), over = 3471, worst = 7
PHY-1002 : len = 1.10137e+06, over cnt = 966(2%), over = 1308, worst = 6
PHY-1002 : len = 1.12312e+06, over cnt = 162(0%), over = 191, worst = 4
PHY-1002 : len = 1.12783e+06, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.773823s wall, 4.312500s user + 0.109375s system = 4.421875s CPU (159.4%)

PHY-1001 : Congestion index: top1 = 62.78, top5 = 57.14, top10 = 53.83, top15 = 51.66.
PHY-3001 : End congestion estimation;  3.159231s wall, 4.687500s user + 0.109375s system = 4.796875s CPU (151.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 23097 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.335195s wall, 1.328125s user + 0.000000s system = 1.328125s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000663196
PHY-3002 : Step(199): len = 807766, overlap = 185.5
PHY-3002 : Step(200): len = 760132, overlap = 207.5
PHY-3002 : Step(201): len = 732599, overlap = 215.25
PHY-3002 : Step(202): len = 716154, overlap = 234.25
PHY-3002 : Step(203): len = 696572, overlap = 257.25
PHY-3002 : Step(204): len = 682211, overlap = 270.75
PHY-3002 : Step(205): len = 669231, overlap = 284.75
PHY-3002 : Step(206): len = 657711, overlap = 299.5
PHY-3002 : Step(207): len = 648692, overlap = 315
PHY-3002 : Step(208): len = 641214, overlap = 319.5
PHY-3002 : Step(209): len = 636586, overlap = 331.25
PHY-3002 : Step(210): len = 630750, overlap = 332.75
PHY-3002 : Step(211): len = 627017, overlap = 335.5
PHY-3002 : Step(212): len = 624076, overlap = 348.5
PHY-3002 : Step(213): len = 621320, overlap = 355.5
PHY-3002 : Step(214): len = 618665, overlap = 358.75
PHY-3002 : Step(215): len = 616426, overlap = 363
PHY-3002 : Step(216): len = 614380, overlap = 371.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00113936
PHY-3002 : Step(217): len = 615941, overlap = 367.5
PHY-3002 : Step(218): len = 617931, overlap = 367
PHY-3002 : Step(219): len = 622147, overlap = 367.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00192639
PHY-3002 : Step(220): len = 623546, overlap = 365.25
PHY-3002 : Step(221): len = 627709, overlap = 360.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  5.965410s wall, 5.890625s user + 0.046875s system = 5.937500s CPU (99.5%)

PHY-3001 : Legalized: Len = 780271, Over = 0
PHY-3001 : Spreading special nets. 29 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.095213s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (114.9%)

PHY-3001 : 53 instances has been re-located, deltaX = 39, deltaY = 42, maxDist = 4.
PHY-3001 : Final: Len = 781181, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82314, tnet num: 23097, tinst num: 9316, tnode num: 111143, tedge num: 134434.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.103110s wall, 2.093750s user + 0.015625s system = 2.109375s CPU (100.3%)

RUN-1004 : used memory is 684 MB, reserved memory is 689 MB, peak memory is 901 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2909/23099.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 911000, over cnt = 3381(9%), over = 5827, worst = 7
PHY-1002 : len = 934584, over cnt = 2193(6%), over = 3122, worst = 7
PHY-1002 : len = 961104, over cnt = 960(2%), over = 1323, worst = 5
PHY-1002 : len = 986656, over cnt = 106(0%), over = 125, worst = 4
PHY-1002 : len = 990184, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.455956s wall, 3.875000s user + 0.046875s system = 3.921875s CPU (159.7%)

PHY-1001 : Congestion index: top1 = 59.29, top5 = 54.03, top10 = 51.06, top15 = 48.92.
PHY-1001 : End incremental global routing;  2.805613s wall, 4.218750s user + 0.062500s system = 4.281250s CPU (152.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 23097 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.022297s wall, 0.984375s user + 0.031250s system = 1.015625s CPU (99.3%)

OPT-1001 : 0 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 9253 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 9316 instances, 9211 slices, 291 macros(1458 instances: 956 mslices 502 lslices)
PHY-3001 : End incremental placement; No cells to be placed.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  4.739553s wall, 6.078125s user + 0.093750s system = 6.171875s CPU (130.2%)

OPT-1001 : Current memory(MB): used = 791, reserve = 787, peak = 901.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 20800/23099.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 990184, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.150384s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (103.9%)

PHY-1001 : Congestion index: top1 = 59.29, top5 = 54.03, top10 = 51.06, top15 = 48.92.
OPT-1001 : End congestion update;  0.490336s wall, 0.500000s user + 0.000000s system = 0.500000s CPU (102.0%)

OPT-1001 : Update timing in Manhattan mode
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82314, tnet num: 23097, tinst num: 9316, tnode num: 111143, tedge num: 134434.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.097968s wall, 2.093750s user + 0.000000s system = 2.093750s CPU (99.8%)

RUN-1004 : used memory is 705 MB, reserved memory is 710 MB, peak memory is 901 MB
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 23097 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  3.001362s wall, 3.000000s user + 0.000000s system = 3.000000s CPU (100.0%)

OPT-0007 : Start: WNS 968 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 9253 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 9316 instances, 9211 slices, 291 macros(1458 instances: 956 mslices 502 lslices)
PHY-3001 : Cell area utilization is 95%
PHY-3001 : Initial: Len = 783273, Over = 0
PHY-3001 : End spreading;  0.082975s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (113.0%)

PHY-3001 : Final: Len = 783273, Over = 0
PHY-3001 : End incremental legalization;  0.523425s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (104.5%)

OPT-0007 : Iter 1: improved WNS 1818 TNS 0 NUM_FEPS 0 with 12 cells processed and 4189 slack improved
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 9253 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 9316 instances, 9211 slices, 291 macros(1458 instances: 956 mslices 502 lslices)
PHY-3001 : Cell area utilization is 95%
PHY-3001 : Initial: Len = 780077, Over = 0
PHY-3001 : End spreading;  0.069248s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (90.3%)

PHY-3001 : Final: Len = 780077, Over = 0
PHY-3001 : End incremental legalization;  0.470004s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (93.1%)

OPT-0007 : Iter 2: improved WNS 2769 TNS 0 NUM_FEPS 0 with 6 cells processed and 5058 slack improved
OPT-0007 : Iter 3: improved WNS 2769 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  4.769254s wall, 4.859375s user + 0.000000s system = 4.859375s CPU (101.9%)

OPT-1001 : Current memory(MB): used = 806, reserve = 797, peak = 901.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 23097 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.843202s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (100.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 20705/23099.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 989152, over cnt = 93(0%), over = 113, worst = 4
PHY-1002 : len = 988672, over cnt = 96(0%), over = 103, worst = 3
PHY-1002 : len = 989392, over cnt = 30(0%), over = 30, worst = 1
PHY-1002 : len = 990040, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 990088, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.877864s wall, 0.890625s user + 0.000000s system = 0.890625s CPU (101.5%)

PHY-1001 : Congestion index: top1 = 60.45, top5 = 54.92, top10 = 51.57, top15 = 49.24.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 23097 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.834334s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (101.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 2769 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 60.034483
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 2769ps with logic level 6 
OPT-1001 : End physical optimization;  14.779648s wall, 16.187500s user + 0.125000s system = 16.312500s CPU (110.4%)

RUN-1003 : finish command "place" in  85.203804s wall, 137.921875s user + 7.187500s system = 145.109375s CPU (170.3%)

RUN-1004 : used memory is 709 MB, reserved memory is 703 MB, peak memory is 901 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.719603s wall, 3.031250s user + 0.015625s system = 3.046875s CPU (177.2%)

RUN-1004 : used memory is 710 MB, reserved memory is 703 MB, peak memory is 901 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 9318 instances
RUN-1001 : 4606 mslices, 4605 lslices, 60 pads, 42 brams, 0 dsps
RUN-1001 : There are total 23099 nets
RUN-1001 : 16938 nets have 2 pins
RUN-1001 : 4457 nets have [3 - 5] pins
RUN-1001 : 1059 nets have [6 - 10] pins
RUN-1001 : 369 nets have [11 - 20] pins
RUN-1001 : 264 nets have [21 - 99] pins
RUN-1001 : 12 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82314, tnet num: 23097, tinst num: 9316, tnode num: 111143, tedge num: 134434.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.820764s wall, 1.812500s user + 0.000000s system = 1.812500s CPU (99.5%)

RUN-1004 : used memory is 686 MB, reserved memory is 678 MB, peak memory is 901 MB
PHY-1001 : 4606 mslices, 4605 lslices, 60 pads, 42 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 23097 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 887424, over cnt = 3420(9%), over = 6124, worst = 7
PHY-1002 : len = 917600, over cnt = 2162(6%), over = 3145, worst = 6
PHY-1002 : len = 949704, over cnt = 921(2%), over = 1198, worst = 5
PHY-1002 : len = 972992, over cnt = 14(0%), over = 14, worst = 1
PHY-1002 : len = 973776, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.435891s wall, 4.031250s user + 0.046875s system = 4.078125s CPU (167.4%)

PHY-1001 : Congestion index: top1 = 58.60, top5 = 53.89, top10 = 50.83, top15 = 48.61.
PHY-1001 : End global routing;  2.799996s wall, 4.390625s user + 0.046875s system = 4.437500s CPU (158.5%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 785, reserve = 779, peak = 901.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net COM3/GNRMC/u_fifo/wr_to_rd_cross_inst/secondary_clk_i will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 1062, reserve = 1056, peak = 1062.
PHY-1001 : End build detailed router design. 4.409104s wall, 4.375000s user + 0.031250s system = 4.406250s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 196488, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.848069s wall, 0.828125s user + 0.015625s system = 0.843750s CPU (99.5%)

PHY-1001 : Current memory(MB): used = 1097, reserve = 1091, peak = 1097.
PHY-1001 : End phase 1; 0.855317s wall, 0.843750s user + 0.015625s system = 0.859375s CPU (100.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 57% nets.
PHY-1001 : Routed 67% nets.
PHY-1001 : Routed 78% nets.
PHY-1001 : Routed 94% nets.
PHY-1022 : len = 2.24795e+06, over cnt = 4328(0%), over = 4406, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1116, reserve = 1109, peak = 1116.
PHY-1001 : End initial routed; 29.827546s wall, 69.187500s user + 0.359375s system = 69.546875s CPU (233.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/21870(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.685   |   0.000   |   0   
RUN-1001 :   Hold   |   0.153   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.967940s wall, 3.968750s user + 0.000000s system = 3.968750s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1126, reserve = 1119, peak = 1126.
PHY-1001 : End phase 2; 33.795615s wall, 73.156250s user + 0.359375s system = 73.515625s CPU (217.5%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 2.24795e+06, over cnt = 4328(0%), over = 4406, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.284043s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (99.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 2.17941e+06, over cnt = 1998(0%), over = 2008, worst = 2, crit = 0
PHY-1001 : End DR Iter 1; 3.601000s wall, 5.156250s user + 0.015625s system = 5.171875s CPU (143.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 2.1763e+06, over cnt = 774(0%), over = 774, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 1.917576s wall, 2.093750s user + 0.062500s system = 2.156250s CPU (112.4%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 2.18254e+06, over cnt = 193(0%), over = 193, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 1.680984s wall, 1.843750s user + 0.015625s system = 1.859375s CPU (110.6%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 2.18582e+06, over cnt = 91(0%), over = 91, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.788154s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (101.1%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 2.18936e+06, over cnt = 35(0%), over = 35, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.803597s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.2%)

PHY-1001 : ===== DR Iter 6 =====
PHY-1022 : len = 2.19127e+06, over cnt = 14(0%), over = 14, worst = 1, crit = 0
PHY-1001 : End DR Iter 6; 0.974906s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (97.8%)

PHY-1001 : ===== DR Iter 7 =====
PHY-1022 : len = 2.19169e+06, over cnt = 11(0%), over = 11, worst = 1, crit = 0
PHY-1001 : End DR Iter 7; 1.368232s wall, 1.343750s user + 0.000000s system = 1.343750s CPU (98.2%)

PHY-1001 : ===== DR Iter 8 =====
PHY-1022 : len = 2.19176e+06, over cnt = 13(0%), over = 13, worst = 1, crit = 0
PHY-1001 : End DR Iter 8; 0.215099s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (116.2%)

PHY-1001 : ==== DR Iter 9 ====
PHY-1022 : len = 2.19211e+06, over cnt = 6(0%), over = 6, worst = 1, crit = 0
PHY-1001 : End DR Iter 9; 0.206208s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (90.9%)

PHY-1001 : ==== DR Iter 10 ====
PHY-1022 : len = 2.19207e+06, over cnt = 6(0%), over = 6, worst = 1, crit = 0
PHY-1001 : End DR Iter 10; 0.235488s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (106.2%)

PHY-1001 : ==== DR Iter 11 ====
PHY-1022 : len = 2.19212e+06, over cnt = 4(0%), over = 4, worst = 1, crit = 0
PHY-1001 : End DR Iter 11; 0.383669s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (97.7%)

PHY-1001 : ==== DR Iter 12 ====
PHY-1022 : len = 2.19217e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 12; 0.598126s wall, 0.593750s user + 0.000000s system = 0.593750s CPU (99.3%)

PHY-1001 : ===== DR Iter 13 =====
PHY-1022 : len = 2.19225e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 13; 0.184087s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (93.4%)

PHY-1001 : ==== DR Iter 14 ====
PHY-1022 : len = 2.19223e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 14; 0.182938s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (111.0%)

PHY-1001 : ==== DR Iter 15 ====
PHY-1022 : len = 2.19238e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 15; 0.206501s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (98.4%)

PHY-1001 : ==== DR Iter 16 ====
PHY-1022 : len = 2.19238e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 16; 0.239750s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (97.8%)

PHY-1001 : ==== DR Iter 17 ====
PHY-1022 : len = 2.19238e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 17; 0.297801s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (94.4%)

PHY-1001 : ==== DR Iter 18 ====
PHY-1022 : len = 2.19238e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 18; 0.370619s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (101.2%)

PHY-1001 : ===== DR Iter 19 =====
PHY-1022 : len = 2.19239e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 19; 0.175590s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (89.0%)

PHY-1001 : ==== DR Iter 20 ====
PHY-1022 : len = 2.19238e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 20; 0.175962s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (115.4%)

PHY-1001 : ==== DR Iter 21 ====
PHY-1022 : len = 2.19238e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 21; 0.198651s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (102.3%)

PHY-1001 : ==== DR Iter 22 ====
PHY-1022 : len = 2.19238e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 22; 0.240300s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (97.5%)

PHY-1001 : ==== DR Iter 23 ====
PHY-1022 : len = 2.19238e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 23; 0.291821s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (112.4%)

PHY-1001 : ==== DR Iter 24 ====
PHY-1022 : len = 2.19238e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 24; 0.354622s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (101.3%)

PHY-1001 : ==== DR Iter 25 ====
PHY-1022 : len = 2.19238e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 25; 0.676055s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (99.4%)

PHY-1001 : ===== DR Iter 26 =====
PHY-1022 : len = 2.19238e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 26; 0.193157s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (97.1%)

PHY-1001 : ==== DR Iter 27 ====
PHY-1022 : len = 2.19234e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 27; 0.170038s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (101.1%)

PHY-1001 : Update timing.....
PHY-1001 : 6/21870(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |  -0.187   |  -0.187   |   1   
RUN-1001 :   Hold   |   0.153   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.722707s wall, 3.718750s user + 0.000000s system = 3.718750s CPU (99.9%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 1268 feed throughs used by 851 nets
PHY-1001 : End commit to database; 2.633880s wall, 2.609375s user + 0.031250s system = 2.640625s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 1236, reserve = 1232, peak = 1236.
PHY-1001 : End phase 3; 23.964255s wall, 25.781250s user + 0.187500s system = 25.968750s CPU (108.4%)

PHY-1001 : ===== Detail Route Phase 4 =====
PHY-1001 : Optimize timing.....
PHY-1001 : ===== OPT Iter 1 =====
PHY-1001 : Processed 1 pins with SWNS 0.538ns STNS 0.000ns FEP 0.
PHY-1001 : End OPT Iter 1; 0.154987s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (100.8%)

PHY-1022 : len = 2.19235e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.450607s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (100.6%)

PHY-0007 : Phase: 4; Congestion: {, , , }; Timing: {0.538ns, 0.000ns, 0}
PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 2.1924e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 1; 0.168109s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (102.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/21870(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.538   |   0.000   |   0   
RUN-1001 :   Hold   |   0.153   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.823250s wall, 3.812500s user + 0.015625s system = 3.828125s CPU (100.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 1268 feed throughs used by 851 nets
PHY-1001 : End commit to database; 2.686026s wall, 2.687500s user + 0.000000s system = 2.687500s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1245, reserve = 1242, peak = 1245.
PHY-1001 : End phase 4; 7.186347s wall, 7.171875s user + 0.015625s system = 7.187500s CPU (100.0%)

PHY-1003 : Routed, final wirelength = 2.1924e+06
PHY-1001 : Current memory(MB): used = 1248, reserve = 1245, peak = 1248.
PHY-1001 : End export database. 0.170482s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (100.8%)

PHY-1001 : End detail routing;  70.802265s wall, 111.921875s user + 0.609375s system = 112.531250s CPU (158.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82314, tnet num: 23097, tinst num: 9316, tnode num: 111143, tedge num: 134434.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.818228s wall, 1.812500s user + 0.000000s system = 1.812500s CPU (99.7%)

RUN-1004 : used memory is 1172 MB, reserved memory is 1179 MB, peak memory is 1248 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  80.082658s wall, 122.750000s user + 0.687500s system = 123.437500s CPU (154.1%)

RUN-1004 : used memory is 1170 MB, reserved memory is 1177 MB, peak memory is 1248 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                    10350   out of  19600   52.81%
#reg                    14541   out of  19600   74.19%
#le                     17064
  #lut only              2523   out of  17064   14.79%
  #reg only              6714   out of  17064   39.35%
  #lut&reg               7827   out of  17064   45.87%
#dsp                        0   out of     29    0.00%
#bram                      42   out of     64   65.62%
  #bram9k                  42
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                                                 Type               DriverType         Driver                    Fanout
#1        COM3/GNRMC/u_fifo/wr_to_rd_cross_inst/secondary_clk_i    GCLK               pll                CLK100M/pll_inst.clkc0    8015
#2        config_inst_syn_9                                        GCLK               config             config_inst.jtck          183
#3        clk_in_dup_1                                             GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         M3        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         P6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------------+
|Instance                            |Module                                         |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A                                    |17064  |8892    |1458    |14585   |42      |0       |
|  AnyFog_dataX                      |AnyFog                                         |211    |72      |22      |176     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E                                 |88     |55      |22      |53      |0       |0       |
|  AnyFog_dataY                      |AnyFog                                         |213    |103     |22      |170     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E                                 |95     |65      |22      |52      |0       |0       |
|  AnyFog_dataZ                      |AnyFog                                         |206    |89      |22      |170     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E                                 |87     |61      |22      |51      |0       |0       |
|  CLK100M                           |global_clock                                   |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control                                   |2825   |528     |34      |2754    |0       |0       |
|    PPPNAV_com2                     |PPPNAV                                         |215    |64      |5       |204     |0       |0       |
|    STADOP_com2                     |STADOP                                         |547    |45      |0       |540     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800                                  |65     |42      |14      |43      |0       |0       |
|    head_com2                       |uniheading                                     |269    |58      |5       |258     |0       |0       |
|    uart_com2                       |Agrica                                         |1424   |307     |10      |1404    |0       |0       |
|  COM3                              |COM3_Control                                   |2507   |1919    |44      |2413    |0       |0       |
|    GNRMC                           |GNRMC_Tx                                       |2294   |1800    |30      |2229    |0       |0       |
|      u_fifo                        |Asys_fifo8x8                                   |2235   |1764    |25      |2178    |0       |0       |
|        ram_inst                    |ram_infer_Asys_fifo8x8                         |2082   |1684    |0       |2055    |0       |0       |
|        rd_to_wr_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo8x8 |30     |24      |0       |30      |0       |0       |
|        wr_to_rd_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo8x8 |30     |24      |0       |30      |0       |0       |
|    UART_RX_COM3                    |UART_RX460800                                  |61     |36      |14      |38      |0       |0       |
|    rmc_com3                        |Gprmc                                          |152    |83      |0       |146     |0       |0       |
|  DATA                              |Data_Processing                                |8638   |4361    |1056    |6991    |0       |0       |
|    DIV_Dtemp                       |Divider                                        |780    |325     |84      |657     |0       |0       |
|    DIV_Utemp                       |Divider                                        |660    |308     |84      |535     |0       |0       |
|    DIV_accX                        |Divider                                        |643    |324     |84      |519     |0       |0       |
|    DIV_accY                        |Divider                                        |629    |320     |105     |471     |0       |0       |
|    DIV_accZ                        |Divider                                        |677    |400     |132     |475     |0       |0       |
|    DIV_rateX                       |Divider                                        |653    |353     |132     |444     |0       |0       |
|    DIV_rateY                       |Divider                                        |567    |355     |132     |364     |0       |0       |
|    DIV_rateZ                       |Divider                                        |554    |354     |132     |348     |0       |0       |
|    genclk                          |genclk                                         |83     |56      |20      |49      |0       |0       |
|  FMC                               |FMC_Ctrl                                       |446    |403     |43      |345     |0       |0       |
|  IIC                               |I2C_master                                     |293    |240     |11      |259     |0       |0       |
|  IMU_CTRL                          |SCHA634                                        |917    |719     |61      |712     |0       |0       |
|    CtrlData                        |CtrlData                                       |490    |440     |47      |328     |0       |0       |
|      usms                          |Time_1ms                                       |30     |25      |5       |20      |0       |0       |
|    SPIM                            |SPI_SCHA634                                    |427    |279     |14      |384     |0       |0       |
|  POWER                             |POWER_EN                                       |98     |50      |38      |37      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER                                 |696    |403     |105     |500     |0       |0       |
|    wrapper_cwc_top                 |cwc_top                                        |696    |403     |105     |500     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int                                    |326    |163     |0       |311     |0       |0       |
|        reg_inst                    |register                                       |325    |162     |0       |310     |0       |0       |
|        tap_inst                    |tap                                            |1      |1       |0       |1       |0       |0       |
|      trigger_inst                  |trigger                                        |370    |240     |105     |189     |0       |0       |
|        bus_inst                    |bus_top                                        |148    |98      |48      |63      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det                                        |27     |17      |10      |9       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det                                        |53     |35      |18      |19      |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det                                        |3      |3       |0       |3       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det                                        |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det                                        |3      |2       |0       |3       |0       |0       |
|          BUS_DETECTOR[5]$bus_nodes |bus_det                                        |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[6]$bus_nodes |bus_det                                        |29     |18      |10      |13      |0       |0       |
|          BUS_DETECTOR[7]$bus_nodes |bus_det                                        |28     |18      |10      |11      |0       |0       |
|          BUS_DETECTOR[9]$bus_nodes |bus_det                                        |2      |2       |0       |2       |0       |0       |
|        emb_ctrl_inst               |emb_ctrl                                       |139    |98      |29      |89      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       16877  
    #2          2       3396   
    #3          3        691   
    #4          4        370   
    #5        5-10      1113   
    #6        11-50      564   
    #7       51-100      15    
    #8       101-500      5    
    #9        >500        3    
  Average     2.21             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.134890s wall, 3.734375s user + 0.015625s system = 3.750000s CPU (175.7%)

RUN-1004 : used memory is 1170 MB, reserved memory is 1179 MB, peak memory is 1248 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82314, tnet num: 23097, tinst num: 9316, tnode num: 111143, tedge num: 134434.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.836552s wall, 1.843750s user + 0.000000s system = 1.843750s CPU (100.4%)

RUN-1004 : used memory is 1173 MB, reserved memory is 1182 MB, peak memory is 1248 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 23097 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 4 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.522822s wall, 1.500000s user + 0.015625s system = 1.515625s CPU (99.5%)

RUN-1004 : used memory is 1180 MB, reserved memory is 1186 MB, peak memory is 1248 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 8f169df8acfcb835781910b1ac29267cd80e16d8a5e1b8cf9d032ec93c0a2865 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 9316
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 23099, pip num: 184743
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 1268
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3267 valid insts, and 507980 bits set as '1'.
BIT-1004 : the usercode register value: 00000000110101001100100110110000
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.274285s wall, 122.750000s user + 0.234375s system = 122.984375s CPU (1002.0%)

RUN-1004 : used memory is 1333 MB, reserved memory is 1324 MB, peak memory is 1449 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250710_173504.log"

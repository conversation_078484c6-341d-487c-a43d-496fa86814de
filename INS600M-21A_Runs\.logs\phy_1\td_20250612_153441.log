============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Jun 12 15:34:41 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(514)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
RUN-1001 : Project manager successfully analyzed 21 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.810655s wall, 1.546875s user + 4.265625s system = 5.812500s CPU (100.0%)

RUN-1004 : used memory is 78 MB, reserved memory is 40 MB, peak memory is 90 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  2.045554s wall, 2.015625s user + 0.031250s system = 2.046875s CPU (100.1%)

RUN-1004 : used memory is 299 MB, reserved memory is 267 MB, peak memory is 302 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 58 trigger nets, 58 data nets.
KIT-1004 : Chipwatcher code = 1110111010000001
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=158) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=158) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=158)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=158)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22926/23 useful/useless nets, 19642/12 useful/useless insts
SYN-1016 : Merged 29 instances.
SYN-1032 : 22530/20 useful/useless nets, 20148/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 478 better
SYN-1014 : Optimize round 2
SYN-1032 : 22146/45 useful/useless nets, 19764/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.967153s wall, 2.937500s user + 0.046875s system = 2.984375s CPU (100.6%)

RUN-1004 : used memory is 328 MB, reserved memory is 294 MB, peak memory is 329 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22218/441 useful/useless nets, 19887/63 useful/useless insts
SYN-1016 : Merged 69 instances.
SYN-2571 : Optimize after map_dsp, round 1, 573 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 56 instances.
SYN-2501 : Optimize round 1, 114 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 22722/5 useful/useless nets, 20391/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83696, tnet num: 22722, tinst num: 20390, tnode num: 117502, tedge num: 130374.
TMR-2508 : Levelizing timing graph completed, there are 85 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.289038s wall, 1.187500s user + 0.093750s system = 1.281250s CPU (99.4%)

RUN-1004 : used memory is 471 MB, reserved memory is 439 MB, peak memory is 471 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22722 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 282 (3.29), #lev = 7 (1.66)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 282 (3.29), #lev = 7 (1.66)
SYN-3001 : Logic optimization runtime opt =   0.04 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 614 instances into 282 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 487 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 160 adder to BLE ...
SYN-4008 : Packed 160 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  5.288330s wall, 5.078125s user + 0.218750s system = 5.296875s CPU (100.2%)

RUN-1004 : used memory is 354 MB, reserved memory is 325 MB, peak memory is 581 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  8.653753s wall, 8.359375s user + 0.312500s system = 8.671875s CPU (100.2%)

RUN-1004 : used memory is 355 MB, reserved memory is 325 MB, peak memory is 581 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/rmc_com2/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/rmc_com2/GPRMC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/rmc_com2/GPRMC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/rmc_com2/GPRMC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/rmc_com2/GPRMC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/rmc_com2/GPRMC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/rmc_com2/GPRMC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/rmc_com2/GPRMC_data[0]
SYN-5055 WARNING: The kept net COM2/rx_err will be merged to another kept net COM2/rmc_com2/GPRMC_err
SYN-5055 WARNING: The kept net FMC/Error_state[0] will be merged to another kept net COM2/rmc_com2/GPRMC_sub_state_cnt0[3]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (335 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19715 instances
RUN-0007 : 5651 luts, 12517 seqs, 943 mslices, 491 lslices, 56 pads, 52 brams, 0 dsps
RUN-1001 : There are total 22070 nets
RUN-1001 : 16514 nets have 2 pins
RUN-1001 : 4406 nets have [3 - 5] pins
RUN-1001 : 783 nets have [6 - 10] pins
RUN-1001 : 241 nets have [11 - 20] pins
RUN-1001 : 102 nets have [21 - 99] pins
RUN-1001 : 24 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4742     
RUN-1001 :   No   |  No   |  Yes  |     645     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    6531     
RUN-1001 :   Yes  |  No   |  Yes  |     499     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19713 instances, 5651 luts, 12517 seqs, 1434 slices, 280 macros(1434 instances: 943 mslices 491 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1131 pins
PHY-3001 : Huge net DATA/done_div with 1650 pins
PHY-0007 : Cell area utilization is 64%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82223, tnet num: 22068, tinst num: 19713, tnode num: 116164, tedge num: 129220.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.329688s wall, 1.265625s user + 0.062500s system = 1.328125s CPU (99.9%)

RUN-1004 : used memory is 532 MB, reserved memory is 504 MB, peak memory is 581 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22068 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.311839s wall, 2.250000s user + 0.062500s system = 2.312500s CPU (100.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.39833e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19713.
PHY-3001 : Level 1 #clusters 2094.
PHY-3001 : End clustering;  0.180352s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (155.9%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 64%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 885949, overlap = 693.656
PHY-3002 : Step(2): len = 798750, overlap = 730.469
PHY-3002 : Step(3): len = 512457, overlap = 954.625
PHY-3002 : Step(4): len = 451368, overlap = 1017.97
PHY-3002 : Step(5): len = 357811, overlap = 1124.22
PHY-3002 : Step(6): len = 322348, overlap = 1160.88
PHY-3002 : Step(7): len = 264477, overlap = 1275.38
PHY-3002 : Step(8): len = 239420, overlap = 1324.12
PHY-3002 : Step(9): len = 205481, overlap = 1384.59
PHY-3002 : Step(10): len = 190447, overlap = 1408.75
PHY-3002 : Step(11): len = 172494, overlap = 1440.5
PHY-3002 : Step(12): len = 158810, overlap = 1459.12
PHY-3002 : Step(13): len = 141662, overlap = 1471.66
PHY-3002 : Step(14): len = 131541, overlap = 1485.44
PHY-3002 : Step(15): len = 120806, overlap = 1504.91
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.98952e-07
PHY-3002 : Step(16): len = 124988, overlap = 1501.19
PHY-3002 : Step(17): len = 169232, overlap = 1453.78
PHY-3002 : Step(18): len = 185954, overlap = 1334.25
PHY-3002 : Step(19): len = 190335, overlap = 1250.19
PHY-3002 : Step(20): len = 186597, overlap = 1221.03
PHY-3002 : Step(21): len = 182783, overlap = 1196.19
PHY-3002 : Step(22): len = 178399, overlap = 1190.72
PHY-3002 : Step(23): len = 174980, overlap = 1179.78
PHY-3002 : Step(24): len = 172200, overlap = 1188.72
PHY-3002 : Step(25): len = 170150, overlap = 1185.56
PHY-3002 : Step(26): len = 168131, overlap = 1185.09
PHY-3002 : Step(27): len = 166889, overlap = 1194.75
PHY-3002 : Step(28): len = 166097, overlap = 1198.09
PHY-3002 : Step(29): len = 163801, overlap = 1208.03
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 1.7979e-06
PHY-3002 : Step(30): len = 173049, overlap = 1181.84
PHY-3002 : Step(31): len = 186769, overlap = 1118.78
PHY-3002 : Step(32): len = 190287, overlap = 1051.56
PHY-3002 : Step(33): len = 192515, overlap = 1028.94
PHY-3002 : Step(34): len = 193237, overlap = 1019.38
PHY-3002 : Step(35): len = 192788, overlap = 994.562
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 3.59581e-06
PHY-3002 : Step(36): len = 202062, overlap = 974.25
PHY-3002 : Step(37): len = 220842, overlap = 885.375
PHY-3002 : Step(38): len = 226447, overlap = 812.344
PHY-3002 : Step(39): len = 227515, overlap = 799.531
PHY-3002 : Step(40): len = 226236, overlap = 795.719
PHY-3002 : Step(41): len = 224408, overlap = 797.875
PHY-3002 : Step(42): len = 223223, overlap = 795.125
PHY-3002 : Step(43): len = 221956, overlap = 812.031
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 7.19161e-06
PHY-3002 : Step(44): len = 233449, overlap = 781.031
PHY-3002 : Step(45): len = 251186, overlap = 681.625
PHY-3002 : Step(46): len = 257744, overlap = 628.625
PHY-3002 : Step(47): len = 261168, overlap = 607.594
PHY-3002 : Step(48): len = 260030, overlap = 604.594
PHY-3002 : Step(49): len = 258057, overlap = 588.75
PHY-3002 : Step(50): len = 256154, overlap = 606.156
PHY-3002 : Step(51): len = 254293, overlap = 620.312
PHY-3002 : Step(52): len = 253261, overlap = 634.531
PHY-3002 : Step(53): len = 252052, overlap = 644.188
PHY-3002 : Step(54): len = 251011, overlap = 660.094
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.43832e-05
PHY-3002 : Step(55): len = 263000, overlap = 612.031
PHY-3002 : Step(56): len = 278531, overlap = 539.344
PHY-3002 : Step(57): len = 281663, overlap = 516.156
PHY-3002 : Step(58): len = 284038, overlap = 516.938
PHY-3002 : Step(59): len = 283733, overlap = 504.969
PHY-3002 : Step(60): len = 281655, overlap = 495.469
PHY-3002 : Step(61): len = 279964, overlap = 502.031
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 2.87665e-05
PHY-3002 : Step(62): len = 292288, overlap = 495.656
PHY-3002 : Step(63): len = 302380, overlap = 503.812
PHY-3002 : Step(64): len = 305919, overlap = 483.719
PHY-3002 : Step(65): len = 307319, overlap = 489.719
PHY-3002 : Step(66): len = 305124, overlap = 474.438
PHY-3002 : Step(67): len = 303151, overlap = 472.938
PHY-3002 : Step(68): len = 301274, overlap = 473.281
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 5.75329e-05
PHY-3002 : Step(69): len = 308355, overlap = 454.188
PHY-3002 : Step(70): len = 318129, overlap = 387.25
PHY-3002 : Step(71): len = 322360, overlap = 358.875
PHY-3002 : Step(72): len = 322020, overlap = 365.875
PHY-3002 : Step(73): len = 319728, overlap = 359.75
PHY-3002 : Step(74): len = 317201, overlap = 354.562
PHY-3002 : Step(75): len = 315514, overlap = 346.812
PHY-3002 : Step(76): len = 315827, overlap = 346.906
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000115066
PHY-3002 : Step(77): len = 321163, overlap = 335.594
PHY-3002 : Step(78): len = 326788, overlap = 298.094
PHY-3002 : Step(79): len = 328423, overlap = 287.031
PHY-3002 : Step(80): len = 328728, overlap = 295.094
PHY-3002 : Step(81): len = 327900, overlap = 303.094
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.00020005
PHY-3002 : Step(82): len = 330263, overlap = 300.75
PHY-3002 : Step(83): len = 333595, overlap = 307.875
PHY-3002 : Step(84): len = 333846, overlap = 302.844
PHY-3002 : Step(85): len = 336048, overlap = 293.188
PHY-3002 : Step(86): len = 336381, overlap = 304.188
PHY-3002 : Step(87): len = 337941, overlap = 311.875
PHY-3002 : Step(88): len = 339000, overlap = 294.969
PHY-3002 : Step(89): len = 339511, overlap = 285.031
PHY-3002 : Step(90): len = 340061, overlap = 284.406
PHY-3002 : Step(91): len = 339065, overlap = 280.906
PHY-3002 : Step(92): len = 339577, overlap = 283.469
PHY-3002 : Step(93): len = 339000, overlap = 284.656
PHY-3002 : Step(94): len = 338200, overlap = 290.906
PHY-3002 : Step(95): len = 336904, overlap = 288.438
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(96): len = 338591, overlap = 281.969
PHY-3002 : Step(97): len = 341345, overlap = 271.531
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.015094s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (103.5%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/22070.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 457256, over cnt = 1267(3%), over = 5950, worst = 47
PHY-1001 : End global iterations;  1.012970s wall, 1.406250s user + 0.093750s system = 1.500000s CPU (148.1%)

PHY-1001 : Congestion index: top1 = 79.29, top5 = 55.53, top10 = 45.64, top15 = 39.79.
PHY-3001 : End congestion estimation;  1.310534s wall, 1.656250s user + 0.140625s system = 1.796875s CPU (137.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22068 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.021279s wall, 1.000000s user + 0.015625s system = 1.015625s CPU (99.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.14466e-05
PHY-3002 : Step(98): len = 384131, overlap = 191.625
PHY-3002 : Step(99): len = 395046, overlap = 171.812
PHY-3002 : Step(100): len = 393865, overlap = 170.312
PHY-3002 : Step(101): len = 393536, overlap = 164.625
PHY-3002 : Step(102): len = 395420, overlap = 158.688
PHY-3002 : Step(103): len = 397390, overlap = 159.219
PHY-3002 : Step(104): len = 399572, overlap = 164.688
PHY-3002 : Step(105): len = 402520, overlap = 153.906
PHY-3002 : Step(106): len = 399630, overlap = 145.281
PHY-3002 : Step(107): len = 399913, overlap = 144.469
PHY-3002 : Step(108): len = 400717, overlap = 144.844
PHY-3002 : Step(109): len = 398557, overlap = 147.125
PHY-3002 : Step(110): len = 398094, overlap = 146.531
PHY-3002 : Step(111): len = 397308, overlap = 147.812
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000182893
PHY-3002 : Step(112): len = 396590, overlap = 153.5
PHY-3002 : Step(113): len = 398531, overlap = 153.375
PHY-3002 : Step(114): len = 400743, overlap = 154.562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000365786
PHY-3002 : Step(115): len = 405331, overlap = 148.969
PHY-3002 : Step(116): len = 414585, overlap = 139.406
PHY-3002 : Step(117): len = 419920, overlap = 139
PHY-3002 : Step(118): len = 420959, overlap = 135.844
PHY-3002 : Step(119): len = 422667, overlap = 131.219
PHY-3002 : Step(120): len = 426344, overlap = 124.594
PHY-3002 : Step(121): len = 425069, overlap = 119.719
PHY-3002 : Step(122): len = 424106, overlap = 113.156
PHY-3002 : Step(123): len = 424499, overlap = 103.312
PHY-3002 : Step(124): len = 424927, overlap = 91.1875
PHY-3002 : Step(125): len = 425593, overlap = 85.75
PHY-3002 : Step(126): len = 427616, overlap = 78.5625
PHY-3002 : Step(127): len = 428899, overlap = 80.375
PHY-3002 : Step(128): len = 431095, overlap = 90.8125
PHY-3002 : Step(129): len = 433714, overlap = 106.5
PHY-3002 : Step(130): len = 436024, overlap = 118.656
PHY-3002 : Step(131): len = 435568, overlap = 125.969
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000731572
PHY-3002 : Step(132): len = 435306, overlap = 126.719
PHY-3002 : Step(133): len = 436865, overlap = 123.594
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(134): len = 437388, overlap = 123.719
PHY-3002 : Step(135): len = 441036, overlap = 125.656
PHY-3002 : Step(136): len = 446870, overlap = 125.906
PHY-3002 : Step(137): len = 448191, overlap = 129.562
PHY-3002 : Step(138): len = 446699, overlap = 126.031
PHY-3002 : Step(139): len = 446287, overlap = 129.844
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 52/22070.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 522608, over cnt = 2166(6%), over = 10958, worst = 42
PHY-1001 : End global iterations;  1.152354s wall, 1.890625s user + 0.109375s system = 2.000000s CPU (173.6%)

PHY-1001 : Congestion index: top1 = 83.75, top5 = 64.46, top10 = 53.76, top15 = 47.55.
PHY-3001 : End congestion estimation;  1.464798s wall, 2.203125s user + 0.109375s system = 2.312500s CPU (157.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22068 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.050224s wall, 1.015625s user + 0.015625s system = 1.031250s CPU (98.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.95277e-05
PHY-3002 : Step(140): len = 453567, overlap = 397.875
PHY-3002 : Step(141): len = 464020, overlap = 353.125
PHY-3002 : Step(142): len = 455571, overlap = 332.594
PHY-3002 : Step(143): len = 450516, overlap = 306.781
PHY-3002 : Step(144): len = 450807, overlap = 290.875
PHY-3002 : Step(145): len = 447742, overlap = 284.375
PHY-3002 : Step(146): len = 445286, overlap = 275.375
PHY-3002 : Step(147): len = 445595, overlap = 264
PHY-3002 : Step(148): len = 442160, overlap = 261.938
PHY-3002 : Step(149): len = 440006, overlap = 261.375
PHY-3002 : Step(150): len = 438377, overlap = 262.938
PHY-3002 : Step(151): len = 437884, overlap = 257.031
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000179055
PHY-3002 : Step(152): len = 436738, overlap = 235.406
PHY-3002 : Step(153): len = 439441, overlap = 219.188
PHY-3002 : Step(154): len = 441446, overlap = 214.875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000358111
PHY-3002 : Step(155): len = 443585, overlap = 207.156
PHY-3002 : Step(156): len = 449354, overlap = 193.125
PHY-3002 : Step(157): len = 452662, overlap = 188.625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000716222
PHY-3002 : Step(158): len = 453710, overlap = 181.969
PHY-3002 : Step(159): len = 457156, overlap = 167.906
PHY-3002 : Step(160): len = 462632, overlap = 156.781
PHY-3002 : Step(161): len = 464497, overlap = 150.625
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00143244
PHY-3002 : Step(162): len = 465187, overlap = 148.719
PHY-3002 : Step(163): len = 469641, overlap = 144.438
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82223, tnet num: 22068, tinst num: 19713, tnode num: 116164, tedge num: 129220.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.663809s wall, 1.625000s user + 0.031250s system = 1.656250s CPU (99.5%)

RUN-1004 : used memory is 570 MB, reserved memory is 545 MB, peak memory is 707 MB
OPT-1001 : Total overflow 514.03 peak overflow 3.81
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 539/22070.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 563120, over cnt = 2600(7%), over = 9365, worst = 26
PHY-1001 : End global iterations;  1.428755s wall, 2.125000s user + 0.015625s system = 2.140625s CPU (149.8%)

PHY-1001 : Congestion index: top1 = 58.94, top5 = 48.83, top10 = 43.90, top15 = 40.68.
PHY-1001 : End incremental global routing;  1.716297s wall, 2.406250s user + 0.015625s system = 2.421875s CPU (141.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22068 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.127262s wall, 1.109375s user + 0.015625s system = 1.125000s CPU (99.8%)

OPT-1001 : 16 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 56 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19638 has valid locations, 269 needs to be replaced
PHY-3001 : design contains 19966 instances, 5763 luts, 12658 seqs, 1434 slices, 280 macros(1434 instances: 943 mslices 491 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 488098
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17164/22323.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 578328, over cnt = 2669(7%), over = 9525, worst = 26
PHY-1001 : End global iterations;  0.227744s wall, 0.281250s user + 0.046875s system = 0.328125s CPU (144.1%)

PHY-1001 : Congestion index: top1 = 59.40, top5 = 49.09, top10 = 44.12, top15 = 40.90.
PHY-3001 : End congestion estimation;  0.505800s wall, 0.578125s user + 0.046875s system = 0.625000s CPU (123.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83087, tnet num: 22321, tinst num: 19966, tnode num: 117373, tedge num: 130442.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.663096s wall, 1.593750s user + 0.062500s system = 1.656250s CPU (99.6%)

RUN-1004 : used memory is 616 MB, reserved memory is 614 MB, peak memory is 707 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22321 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.810231s wall, 2.734375s user + 0.078125s system = 2.812500s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(164): len = 487380, overlap = 0.875
PHY-3002 : Step(165): len = 488504, overlap = 0.75
PHY-3002 : Step(166): len = 489793, overlap = 0.5625
PHY-3002 : Step(167): len = 490444, overlap = 0.5625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17192/22323.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 575736, over cnt = 2663(7%), over = 9572, worst = 26
PHY-1001 : End global iterations;  0.226365s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (117.3%)

PHY-1001 : Congestion index: top1 = 59.61, top5 = 49.28, top10 = 44.28, top15 = 41.05.
PHY-3001 : End congestion estimation;  0.503791s wall, 0.546875s user + 0.015625s system = 0.562500s CPU (111.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22321 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.131290s wall, 1.109375s user + 0.015625s system = 1.125000s CPU (99.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000591882
PHY-3002 : Step(168): len = 490535, overlap = 147.562
PHY-3002 : Step(169): len = 490948, overlap = 146.719
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00118376
PHY-3002 : Step(170): len = 491172, overlap = 145.906
PHY-3002 : Step(171): len = 491955, overlap = 146.156
PHY-3001 : Final: Len = 491955, Over = 146.156
PHY-3001 : End incremental placement;  5.927651s wall, 6.093750s user + 0.234375s system = 6.328125s CPU (106.8%)

OPT-1001 : Total overflow 519.22 peak overflow 3.81
OPT-1001 : End high-fanout net optimization;  9.399844s wall, 10.359375s user + 0.296875s system = 10.656250s CPU (113.4%)

OPT-1001 : Current memory(MB): used = 711, reserve = 692, peak = 727.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17217/22323.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 579848, over cnt = 2609(7%), over = 9013, worst = 26
PHY-1002 : len = 616160, over cnt = 1984(5%), over = 5525, worst = 22
PHY-1002 : len = 683784, over cnt = 405(1%), over = 712, worst = 11
PHY-1002 : len = 690680, over cnt = 132(0%), over = 218, worst = 11
PHY-1002 : len = 693856, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.645663s wall, 2.343750s user + 0.000000s system = 2.343750s CPU (142.4%)

PHY-1001 : Congestion index: top1 = 50.17, top5 = 44.27, top10 = 41.23, top15 = 39.18.
OPT-1001 : End congestion update;  1.933738s wall, 2.625000s user + 0.000000s system = 2.625000s CPU (135.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22321 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.043109s wall, 1.031250s user + 0.015625s system = 1.046875s CPU (100.4%)

OPT-0007 : Start: WNS 4532 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.985316s wall, 3.671875s user + 0.015625s system = 3.687500s CPU (123.5%)

OPT-1001 : Current memory(MB): used = 707, reserve = 687, peak = 727.
OPT-1001 : End physical optimization;  14.424524s wall, 16.187500s user + 0.343750s system = 16.531250s CPU (114.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5763 LUT to BLE ...
SYN-4008 : Packed 5763 LUT and 2867 SEQ to BLE.
SYN-4003 : Packing 9791 remaining SEQ's ...
SYN-4005 : Packed 3252 SEQ with LUT/SLICE
SYN-4006 : 159 single LUT's are left
SYN-4006 : 6539 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 12302/13969 primitive instances ...
PHY-3001 : End packing;  3.242219s wall, 3.218750s user + 0.015625s system = 3.234375s CPU (99.8%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8329 instances
RUN-1001 : 4108 mslices, 4108 lslices, 56 pads, 52 brams, 0 dsps
RUN-1001 : There are total 19505 nets
RUN-1001 : 13563 nets have 2 pins
RUN-1001 : 4570 nets have [3 - 5] pins
RUN-1001 : 832 nets have [6 - 10] pins
RUN-1001 : 380 nets have [11 - 20] pins
RUN-1001 : 150 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
PHY-3001 : design contains 8327 instances, 8216 slices, 280 macros(1434 instances: 943 mslices 491 lslices)
PHY-3001 : Cell area utilization is 87%
PHY-3001 : After packing: Len = 511858, Over = 368.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 8030/19505.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 658624, over cnt = 1650(4%), over = 2515, worst = 6
PHY-1002 : len = 663160, over cnt = 1084(3%), over = 1503, worst = 6
PHY-1002 : len = 677640, over cnt = 326(0%), over = 410, worst = 5
PHY-1002 : len = 682984, over cnt = 70(0%), over = 83, worst = 4
PHY-1002 : len = 685032, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.314854s wall, 2.171875s user + 0.062500s system = 2.234375s CPU (169.9%)

PHY-1001 : Congestion index: top1 = 50.06, top5 = 44.03, top10 = 40.82, top15 = 38.68.
PHY-3001 : End congestion estimation;  1.670603s wall, 2.515625s user + 0.078125s system = 2.593750s CPU (155.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68635, tnet num: 19503, tinst num: 8327, tnode num: 93666, tedge num: 113062.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.906467s wall, 1.859375s user + 0.046875s system = 1.906250s CPU (100.0%)

RUN-1004 : used memory is 602 MB, reserved memory is 585 MB, peak memory is 727 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19503 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.926191s wall, 2.843750s user + 0.078125s system = 2.921875s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.74785e-05
PHY-3002 : Step(172): len = 512855, overlap = 350.75
PHY-3002 : Step(173): len = 509719, overlap = 385.75
PHY-3002 : Step(174): len = 508778, overlap = 404
PHY-3002 : Step(175): len = 510305, overlap = 412
PHY-3002 : Step(176): len = 509472, overlap = 414
PHY-3002 : Step(177): len = 509576, overlap = 419.25
PHY-3002 : Step(178): len = 508561, overlap = 421.25
PHY-3002 : Step(179): len = 508388, overlap = 419
PHY-3002 : Step(180): len = 506408, overlap = 419.75
PHY-3002 : Step(181): len = 505522, overlap = 426
PHY-3002 : Step(182): len = 505149, overlap = 427.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000114957
PHY-3002 : Step(183): len = 507388, overlap = 419
PHY-3002 : Step(184): len = 510792, overlap = 408.25
PHY-3002 : Step(185): len = 511678, overlap = 406.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(186): len = 517951, overlap = 394.25
PHY-3002 : Step(187): len = 526500, overlap = 378
PHY-3002 : Step(188): len = 525601, overlap = 380
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.711807s wall, 0.734375s user + 0.781250s system = 1.515625s CPU (212.9%)

PHY-3001 : Trial Legalized: Len = 631343
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 529/19505.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 727200, over cnt = 2502(7%), over = 3927, worst = 7
PHY-1002 : len = 740536, over cnt = 1605(4%), over = 2222, worst = 7
PHY-1002 : len = 761352, over cnt = 535(1%), over = 689, worst = 5
PHY-1002 : len = 770232, over cnt = 143(0%), over = 181, worst = 5
PHY-1002 : len = 773856, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.046753s wall, 3.500000s user + 0.109375s system = 3.609375s CPU (176.3%)

PHY-1001 : Congestion index: top1 = 49.27, top5 = 45.10, top10 = 42.65, top15 = 40.98.
PHY-3001 : End congestion estimation;  2.438173s wall, 3.890625s user + 0.109375s system = 4.000000s CPU (164.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19503 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.983396s wall, 0.984375s user + 0.000000s system = 0.984375s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000196841
PHY-3002 : Step(189): len = 589204, overlap = 92.25
PHY-3002 : Step(190): len = 571797, overlap = 139.75
PHY-3002 : Step(191): len = 559534, overlap = 195
PHY-3002 : Step(192): len = 552400, overlap = 226
PHY-3002 : Step(193): len = 549495, overlap = 253
PHY-3002 : Step(194): len = 547746, overlap = 263.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000393683
PHY-3002 : Step(195): len = 551125, overlap = 258.25
PHY-3002 : Step(196): len = 555500, overlap = 252
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000787365
PHY-3002 : Step(197): len = 559173, overlap = 244.25
PHY-3002 : Step(198): len = 566587, overlap = 244.5
PHY-3002 : Step(199): len = 566058, overlap = 247.5
PHY-3002 : Step(200): len = 565484, overlap = 248.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.033426s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (93.5%)

PHY-3001 : Legalized: Len = 606201, Over = 0
PHY-3001 : Spreading special nets. 36 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.092367s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (101.5%)

PHY-3001 : 52 instances has been re-located, deltaX = 14, deltaY = 29, maxDist = 1.
PHY-3001 : Final: Len = 606801, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68635, tnet num: 19503, tinst num: 8327, tnode num: 93666, tedge num: 113062.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.115027s wall, 2.109375s user + 0.015625s system = 2.125000s CPU (100.5%)

RUN-1004 : used memory is 611 MB, reserved memory is 609 MB, peak memory is 727 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3984/19505.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 714280, over cnt = 2272(6%), over = 3495, worst = 6
PHY-1002 : len = 724736, over cnt = 1490(4%), over = 2052, worst = 6
PHY-1002 : len = 740736, over cnt = 661(1%), over = 891, worst = 4
PHY-1002 : len = 750712, over cnt = 231(0%), over = 284, worst = 4
PHY-1002 : len = 755936, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.854522s wall, 3.125000s user + 0.015625s system = 3.140625s CPU (169.3%)

PHY-1001 : Congestion index: top1 = 48.66, top5 = 44.38, top10 = 41.73, top15 = 39.93.
PHY-1001 : End incremental global routing;  2.224679s wall, 3.500000s user + 0.015625s system = 3.515625s CPU (158.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19503 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.222052s wall, 1.203125s user + 0.000000s system = 1.203125s CPU (98.5%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 56 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8266 has valid locations, 14 needs to be replaced
PHY-3001 : design contains 8339 instances, 8228 slices, 280 macros(1434 instances: 943 mslices 491 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 610177
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17522/19515.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 759680, over cnt = 29(0%), over = 32, worst = 2
PHY-1002 : len = 759496, over cnt = 19(0%), over = 19, worst = 1
PHY-1002 : len = 759600, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 759648, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.577202s wall, 0.593750s user + 0.015625s system = 0.609375s CPU (105.6%)

PHY-1001 : Congestion index: top1 = 48.45, top5 = 44.32, top10 = 41.81, top15 = 40.04.
PHY-3001 : End congestion estimation;  0.910670s wall, 0.937500s user + 0.015625s system = 0.953125s CPU (104.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68699, tnet num: 19513, tinst num: 8339, tnode num: 93748, tedge num: 113142.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.301121s wall, 2.281250s user + 0.000000s system = 2.281250s CPU (99.1%)

RUN-1004 : used memory is 646 MB, reserved memory is 627 MB, peak memory is 727 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19513 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.390443s wall, 3.359375s user + 0.000000s system = 3.359375s CPU (99.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(201): len = 609938, overlap = 0
PHY-3002 : Step(202): len = 609740, overlap = 0.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17516/19515.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 758248, over cnt = 31(0%), over = 34, worst = 3
PHY-1002 : len = 758304, over cnt = 19(0%), over = 19, worst = 1
PHY-1002 : len = 758360, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 758376, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 758408, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.864136s wall, 0.875000s user + 0.015625s system = 0.890625s CPU (103.1%)

PHY-1001 : Congestion index: top1 = 48.75, top5 = 44.44, top10 = 41.84, top15 = 40.06.
PHY-3001 : End congestion estimation;  1.205908s wall, 1.218750s user + 0.015625s system = 1.234375s CPU (102.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19513 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.008107s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (100.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000966725
PHY-3002 : Step(203): len = 609599, overlap = 1.75
PHY-3002 : Step(204): len = 609591, overlap = 1.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006769s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 609561, Over = 0
PHY-3001 : End spreading;  0.077502s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (100.8%)

PHY-3001 : Final: Len = 609561, Over = 0
PHY-3001 : End incremental placement;  7.200132s wall, 7.421875s user + 0.078125s system = 7.500000s CPU (104.2%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  11.213390s wall, 12.859375s user + 0.093750s system = 12.953125s CPU (115.5%)

OPT-1001 : Current memory(MB): used = 719, reserve = 703, peak = 727.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17519/19515.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 758544, over cnt = 13(0%), over = 13, worst = 1
PHY-1002 : len = 758480, over cnt = 10(0%), over = 10, worst = 1
PHY-1002 : len = 758536, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 758600, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 758608, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.796082s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (106.0%)

PHY-1001 : Congestion index: top1 = 48.36, top5 = 44.33, top10 = 41.75, top15 = 40.01.
OPT-1001 : End congestion update;  1.161055s wall, 1.203125s user + 0.000000s system = 1.203125s CPU (103.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19513 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.854888s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (100.5%)

OPT-0007 : Start: WNS 4488 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  2.021179s wall, 2.062500s user + 0.000000s system = 2.062500s CPU (102.0%)

OPT-1001 : Current memory(MB): used = 719, reserve = 703, peak = 727.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19513 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.839032s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (100.6%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17534/19515.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 758608, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.129670s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (96.4%)

PHY-1001 : Congestion index: top1 = 48.36, top5 = 44.33, top10 = 41.75, top15 = 40.01.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19513 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.853137s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (100.7%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4488 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 47.965517
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4488ps with logic level 5 
OPT-1001 : End physical optimization;  17.771082s wall, 19.437500s user + 0.125000s system = 19.562500s CPU (110.1%)

RUN-1003 : finish command "place" in  78.401275s wall, 157.609375s user + 8.203125s system = 165.812500s CPU (211.5%)

RUN-1004 : used memory is 636 MB, reserved memory is 614 MB, peak memory is 727 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.806356s wall, 3.125000s user + 0.000000s system = 3.125000s CPU (173.0%)

RUN-1004 : used memory is 637 MB, reserved memory is 614 MB, peak memory is 727 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8341 instances
RUN-1001 : 4114 mslices, 4114 lslices, 56 pads, 52 brams, 0 dsps
RUN-1001 : There are total 19515 nets
RUN-1001 : 13561 nets have 2 pins
RUN-1001 : 4568 nets have [3 - 5] pins
RUN-1001 : 842 nets have [6 - 10] pins
RUN-1001 : 385 nets have [11 - 20] pins
RUN-1001 : 149 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68699, tnet num: 19513, tinst num: 8339, tnode num: 93748, tedge num: 113142.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.878858s wall, 1.875000s user + 0.000000s system = 1.875000s CPU (99.8%)

RUN-1004 : used memory is 644 MB, reserved memory is 632 MB, peak memory is 727 MB
PHY-1001 : 4114 mslices, 4114 lslices, 56 pads, 52 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19513 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 693336, over cnt = 2391(6%), over = 3860, worst = 7
PHY-1002 : len = 708664, over cnt = 1463(4%), over = 2071, worst = 7
PHY-1002 : len = 729256, over cnt = 480(1%), over = 648, worst = 6
PHY-1002 : len = 740360, over cnt = 14(0%), over = 21, worst = 4
PHY-1002 : len = 740768, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.955713s wall, 3.296875s user + 0.031250s system = 3.328125s CPU (170.2%)

PHY-1001 : Congestion index: top1 = 48.04, top5 = 43.72, top10 = 41.11, top15 = 39.38.
PHY-1001 : End global routing;  2.322125s wall, 3.656250s user + 0.046875s system = 3.703125s CPU (159.5%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 708, reserve = 695, peak = 727.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 975, reserve = 959, peak = 975.
PHY-1001 : End build detailed router design. 5.110599s wall, 5.031250s user + 0.078125s system = 5.109375s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 194648, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.015008s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1012, reserve = 998, peak = 1012.
PHY-1001 : End phase 1; 1.025867s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (100.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 55% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.83489e+06, over cnt = 1259(0%), over = 1263, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1027, reserve = 1014, peak = 1027.
PHY-1001 : End initial routed; 25.720636s wall, 57.125000s user + 0.609375s system = 57.734375s CPU (224.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18303(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.539   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 4.022928s wall, 4.015625s user + 0.000000s system = 4.015625s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 1040, reserve = 1027, peak = 1040.
PHY-1001 : End phase 2; 29.743768s wall, 61.140625s user + 0.609375s system = 61.750000s CPU (207.6%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.83489e+06, over cnt = 1259(0%), over = 1263, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.279454s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (100.6%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.82462e+06, over cnt = 505(0%), over = 505, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.870313s wall, 1.437500s user + 0.000000s system = 1.437500s CPU (165.2%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.82455e+06, over cnt = 101(0%), over = 101, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.677135s wall, 0.890625s user + 0.015625s system = 0.906250s CPU (133.8%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.82582e+06, over cnt = 10(0%), over = 10, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.302982s wall, 0.343750s user + 0.015625s system = 0.359375s CPU (118.6%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.82608e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.196920s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (95.2%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.82608e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.183220s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (102.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18303(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.193   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.735342s wall, 3.718750s user + 0.000000s system = 3.718750s CPU (99.6%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 307 feed throughs used by 271 nets
PHY-1001 : End commit to database; 2.428474s wall, 2.421875s user + 0.015625s system = 2.437500s CPU (100.4%)

PHY-1001 : Current memory(MB): used = 1129, reserve = 1119, peak = 1129.
PHY-1001 : End phase 3; 9.266700s wall, 10.015625s user + 0.046875s system = 10.062500s CPU (108.6%)

PHY-1003 : Routed, final wirelength = 1.82608e+06
PHY-1001 : Current memory(MB): used = 1133, reserve = 1123, peak = 1133.
PHY-1001 : End export database. 0.186367s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (100.6%)

PHY-1001 : End detail routing;  45.789403s wall, 77.859375s user + 0.734375s system = 78.593750s CPU (171.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68699, tnet num: 19513, tinst num: 8339, tnode num: 93748, tedge num: 113142.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.849201s wall, 1.843750s user + 0.000000s system = 1.843750s CPU (99.7%)

RUN-1004 : used memory is 1066 MB, reserved memory is 1064 MB, peak memory is 1133 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  54.706118s wall, 88.109375s user + 0.781250s system = 88.890625s CPU (162.5%)

RUN-1004 : used memory is 1065 MB, reserved memory is 1064 MB, peak memory is 1133 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8771   out of  19600   44.75%
#reg                    12755   out of  19600   65.08%
#le                     15274
  #lut only              2519   out of  15274   16.49%
  #reg only              6503   out of  15274   42.58%
  #lut&reg               6252   out of  15274   40.93%
#dsp                        0   out of     29    0.00%
#bram                      52   out of     64   81.25%
  #bram9k                  52
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       56   out of    188   29.79%
  #ireg                     9
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    7028
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          188
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT        E11        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT        A13        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT        F10        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         E8        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         E7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT        A10        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C6        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         D6        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         C7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT        E12        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         F1        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         A7        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H13        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J11        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT        E13        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         R7        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         M7        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         P7        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         T8        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         P8        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         N8        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M9        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         T9        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         R9        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         T6        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         P9        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         N9        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT        P11        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         T5        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         B2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        L10        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        E15        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        R14        LVCMOS33           8            N/A            OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT         R2        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT        N12        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        B10        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         J6        LVCMOS33           8            N/A            NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        R15        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E6        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15274  |7337    |1434    |12797   |52      |0       |
|  AnyFog_dataX                      |AnyFog          |203    |77      |22      |167     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |58      |22      |50      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |207    |98      |22      |168     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |91     |67      |22      |52      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |214    |88      |22      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |93     |60      |22      |52      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |3490   |875     |34      |3412    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |732    |86      |5       |718     |0       |0       |
|    STADOP_com2                     |STADOP          |552    |82      |0       |547     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |43      |14      |41      |0       |0       |
|    head_com2                       |uniheading      |270    |68      |5       |256     |0       |0       |
|    rmc_com2                        |Gprmc           |148    |38      |0       |146     |0       |0       |
|    uart_com2                       |Agrica          |1437   |272     |10      |1418    |0       |0       |
|  DATA                              |Data_Processing |8618   |4321    |1062    |6936    |0       |0       |
|    DIV_Dtemp                       |Divider         |797    |327     |84      |669     |0       |0       |
|    DIV_Utemp                       |Divider         |571    |302     |84      |445     |0       |0       |
|    DIV_accX                        |Divider         |602    |313     |84      |471     |0       |0       |
|    DIV_accY                        |Divider         |660    |343     |111     |493     |0       |0       |
|    DIV_accZ                        |Divider         |666    |348     |132     |461     |0       |0       |
|    DIV_rateX                       |Divider         |656    |347     |132     |453     |0       |0       |
|    DIV_rateY                       |Divider         |643    |403     |132     |432     |0       |0       |
|    DIV_rateZ                       |Divider         |590    |377     |132     |376     |0       |0       |
|    genclk                          |genclk          |85     |56      |20      |52      |0       |0       |
|  FMC                               |FMC_Ctrl        |492    |440     |43      |353     |0       |0       |
|  IIC                               |I2C_master      |313    |260     |11      |258     |0       |0       |
|  IMU_CTRL                          |SCHA634         |889    |668     |61      |737     |0       |0       |
|    CtrlData                        |CtrlData        |448    |393     |47      |332     |0       |0       |
|      usms                          |Time_1ms        |27     |22      |5       |18      |0       |0       |
|    SPIM                            |SPI_SCHA634     |441    |275     |14      |405     |0       |0       |
|  POWER                             |POWER_EN        |101    |47      |38      |41      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |745    |463     |119     |508     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |745    |463     |119     |508     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |338    |188     |0       |321     |0       |0       |
|        reg_inst                    |register        |335    |185     |0       |318     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |407    |275     |119     |187     |0       |0       |
|        bus_inst                    |bus_top         |178    |116     |62      |65      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |25     |15      |10      |9       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |4      |4       |0       |4       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |49     |31      |18      |17      |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |98     |64      |34      |33      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |151    |114     |29      |96      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13504  
    #2          2       3608   
    #3          3        689   
    #4          4        271   
    #5        5-10       922   
    #6        11-50      425   
    #7       51-100      29    
    #8       101-500      4    
    #9        >500        2    
  Average     2.15             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.204061s wall, 3.843750s user + 0.015625s system = 3.859375s CPU (175.1%)

RUN-1004 : used memory is 1065 MB, reserved memory is 1064 MB, peak memory is 1133 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68699, tnet num: 19513, tinst num: 8339, tnode num: 93748, tedge num: 113142.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.929278s wall, 1.921875s user + 0.000000s system = 1.921875s CPU (99.6%)

RUN-1004 : used memory is 1067 MB, reserved memory is 1066 MB, peak memory is 1133 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19513 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.630574s wall, 1.609375s user + 0.015625s system = 1.625000s CPU (99.7%)

RUN-1004 : used memory is 1072 MB, reserved memory is 1070 MB, peak memory is 1133 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 28b752c1c0e7d7ba92ac5beed3c46754b7b56ac64969e4b0149cfdbc1d295011 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8339
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19515, pip num: 151468
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 307
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3224 valid insts, and 420877 bits set as '1'.
BIT-1004 : the usercode register value: 00000000000000001110111010000001
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  13.250294s wall, 131.640625s user + 0.234375s system = 131.875000s CPU (995.3%)

RUN-1004 : used memory is 1196 MB, reserved memory is 1182 MB, peak memory is 1311 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250612_153441.log"

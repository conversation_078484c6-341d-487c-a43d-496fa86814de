============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Fri Jun 20 15:44:43 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(516)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
RUN-1001 : Project manager successfully analyzed 22 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.925347s wall, 1.562500s user + 4.343750s system = 5.906250s CPU (99.7%)

RUN-1004 : used memory is 77 MB, reserved memory is 39 MB, peak memory is 89 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  2.092994s wall, 2.046875s user + 0.046875s system = 2.093750s CPU (100.0%)

RUN-1004 : used memory is 299 MB, reserved memory is 268 MB, peak memory is 302 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 58 trigger nets, 58 data nets.
KIT-1004 : Chipwatcher code = 1110111010000001
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=158) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=158) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=158)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=158)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 23133/23 useful/useless nets, 19837/12 useful/useless insts
SYN-1016 : Merged 28 instances.
SYN-1032 : 22738/20 useful/useless nets, 20344/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 478 better
SYN-1014 : Optimize round 2
SYN-1032 : 22354/45 useful/useless nets, 19960/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.868128s wall, 2.812500s user + 0.046875s system = 2.859375s CPU (99.7%)

RUN-1004 : used memory is 329 MB, reserved memory is 296 MB, peak memory is 330 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22426/441 useful/useless nets, 20083/63 useful/useless insts
SYN-1016 : Merged 69 instances.
SYN-2571 : Optimize after map_dsp, round 1, 573 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 56 instances.
SYN-2501 : Optimize round 1, 114 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 22930/5 useful/useless nets, 20587/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 84632, tnet num: 22930, tinst num: 20586, tnode num: 118625, tedge num: 131866.
TMR-2508 : Levelizing timing graph completed, there are 85 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.320666s wall, 1.281250s user + 0.031250s system = 1.312500s CPU (99.4%)

RUN-1004 : used memory is 473 MB, reserved memory is 442 MB, peak memory is 473 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22930 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 282 (3.29), #lev = 7 (1.66)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 282 (3.29), #lev = 7 (1.66)
SYN-3001 : Logic optimization runtime opt =   0.04 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 614 instances into 282 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 487 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 160 adder to BLE ...
SYN-4008 : Packed 160 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  5.282373s wall, 5.187500s user + 0.093750s system = 5.281250s CPU (100.0%)

RUN-1004 : used memory is 354 MB, reserved memory is 326 MB, peak memory is 583 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  8.536993s wall, 8.359375s user + 0.171875s system = 8.531250s CPU (99.9%)

RUN-1004 : used memory is 355 MB, reserved memory is 326 MB, peak memory is 583 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[7] will be merged to another kept net COM3/rmc_com3/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[6] will be merged to another kept net COM3/rmc_com3/GPRMC_data[6]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[5] will be merged to another kept net COM3/rmc_com3/GPRMC_data[5]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[4] will be merged to another kept net COM3/rmc_com3/GPRMC_data[4]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[3] will be merged to another kept net COM3/rmc_com3/GPRMC_data[3]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[2] will be merged to another kept net COM3/rmc_com3/GPRMC_data[2]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[1] will be merged to another kept net COM3/rmc_com3/GPRMC_data[1]
SYN-5055 WARNING: The kept net FMC/Error_state[0] will be merged to another kept net COM3/rmc_com3/GPRMC_sub_state_cnt0[3]
SYN-5055 WARNING: The kept net COM3/rmc_com3/GPRMC_sub_state_cnt0[3] will be merged to another kept net COM3/rmc_com3/GPRMC_sub_state_cnt0[2]
SYN-5055 WARNING: The kept net COM3/rmc_com3/GPRMC_sub_state_cnt0[2] will be merged to another kept net COM3/rmc_com3/GPRMC_sub_state_cnt0[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (335 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19911 instances
RUN-0007 : 5772 luts, 12577 seqs, 951 mslices, 494 lslices, 60 pads, 52 brams, 0 dsps
RUN-1001 : There are total 22278 nets
RUN-1001 : 16651 nets have 2 pins
RUN-1001 : 4439 nets have [3 - 5] pins
RUN-1001 : 793 nets have [6 - 10] pins
RUN-1001 : 265 nets have [11 - 20] pins
RUN-1001 : 106 nets have [21 - 99] pins
RUN-1001 : 24 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4744     
RUN-1001 :   No   |  No   |  Yes  |     670     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    6556     
RUN-1001 :   Yes  |  No   |  Yes  |     507     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  119  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 127
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19909 instances, 5772 luts, 12577 seqs, 1445 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1131 pins
PHY-3001 : Huge net DATA/done_div with 1644 pins
PHY-0007 : Cell area utilization is 64%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83159, tnet num: 22276, tinst num: 19909, tnode num: 117287, tedge num: 130712.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.351893s wall, 1.296875s user + 0.046875s system = 1.343750s CPU (99.4%)

RUN-1004 : used memory is 534 MB, reserved memory is 508 MB, peak memory is 583 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22276 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.436096s wall, 2.343750s user + 0.093750s system = 2.437500s CPU (100.1%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.46978e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19909.
PHY-3001 : Level 1 #clusters 2112.
PHY-3001 : End clustering;  0.162952s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (115.1%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 64%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 921406, overlap = 618.969
PHY-3002 : Step(2): len = 836785, overlap = 681.812
PHY-3002 : Step(3): len = 836785, overlap = 681.812
PHY-3002 : Step(4): len = 528422, overlap = 877.75
PHY-3002 : Step(5): len = 486871, overlap = 914.906
PHY-3002 : Step(6): len = 373677, overlap = 1004.22
PHY-3002 : Step(7): len = 349506, overlap = 1061.19
PHY-3002 : Step(8): len = 290293, overlap = 1149.31
PHY-3002 : Step(9): len = 269356, overlap = 1200.91
PHY-3002 : Step(10): len = 240767, overlap = 1278.69
PHY-3002 : Step(11): len = 224309, overlap = 1329.38
PHY-3002 : Step(12): len = 194470, overlap = 1392.34
PHY-3002 : Step(13): len = 180006, overlap = 1415.5
PHY-3002 : Step(14): len = 160849, overlap = 1454.5
PHY-3002 : Step(15): len = 151145, overlap = 1495
PHY-3002 : Step(16): len = 138918, overlap = 1523.81
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.13779e-06
PHY-3002 : Step(17): len = 140253, overlap = 1510.5
PHY-3002 : Step(18): len = 181333, overlap = 1431.62
PHY-3002 : Step(19): len = 194251, overlap = 1312.94
PHY-3002 : Step(20): len = 197265, overlap = 1268.84
PHY-3002 : Step(21): len = 194108, overlap = 1243.25
PHY-3002 : Step(22): len = 192068, overlap = 1220.09
PHY-3002 : Step(23): len = 188352, overlap = 1210.69
PHY-3002 : Step(24): len = 184732, overlap = 1211.38
PHY-3002 : Step(25): len = 182971, overlap = 1211.19
PHY-3002 : Step(26): len = 178864, overlap = 1208.38
PHY-3002 : Step(27): len = 176524, overlap = 1193.44
PHY-3002 : Step(28): len = 175047, overlap = 1185.81
PHY-3002 : Step(29): len = 174006, overlap = 1180.34
PHY-3002 : Step(30): len = 173466, overlap = 1198.06
PHY-3002 : Step(31): len = 172419, overlap = 1200
PHY-3002 : Step(32): len = 171677, overlap = 1198.94
PHY-3002 : Step(33): len = 172090, overlap = 1194.72
PHY-3002 : Step(34): len = 172786, overlap = 1178.53
PHY-3002 : Step(35): len = 171246, overlap = 1158.81
PHY-3002 : Step(36): len = 170506, overlap = 1122.16
PHY-3002 : Step(37): len = 170178, overlap = 1126.47
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.27558e-06
PHY-3002 : Step(38): len = 176854, overlap = 1109.09
PHY-3002 : Step(39): len = 189937, overlap = 1069.97
PHY-3002 : Step(40): len = 192765, overlap = 1032.97
PHY-3002 : Step(41): len = 194142, overlap = 985.844
PHY-3002 : Step(42): len = 194109, overlap = 965.781
PHY-3002 : Step(43): len = 194070, overlap = 944.219
PHY-3002 : Step(44): len = 194206, overlap = 938.875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.55116e-06
PHY-3002 : Step(45): len = 202913, overlap = 911
PHY-3002 : Step(46): len = 218028, overlap = 849.969
PHY-3002 : Step(47): len = 222917, overlap = 800.875
PHY-3002 : Step(48): len = 224553, overlap = 772.406
PHY-3002 : Step(49): len = 223489, overlap = 762.375
PHY-3002 : Step(50): len = 221847, overlap = 766.594
PHY-3002 : Step(51): len = 220960, overlap = 763.875
PHY-3002 : Step(52): len = 219874, overlap = 755.438
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 9.10232e-06
PHY-3002 : Step(53): len = 230314, overlap = 740.094
PHY-3002 : Step(54): len = 244474, overlap = 736.719
PHY-3002 : Step(55): len = 249396, overlap = 729.625
PHY-3002 : Step(56): len = 251218, overlap = 696.062
PHY-3002 : Step(57): len = 250516, overlap = 687.875
PHY-3002 : Step(58): len = 249054, overlap = 676
PHY-3002 : Step(59): len = 248337, overlap = 662.75
PHY-3002 : Step(60): len = 248045, overlap = 645.344
PHY-3002 : Step(61): len = 247680, overlap = 666.031
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.82046e-05
PHY-3002 : Step(62): len = 258492, overlap = 643.219
PHY-3002 : Step(63): len = 270935, overlap = 544.969
PHY-3002 : Step(64): len = 274657, overlap = 537
PHY-3002 : Step(65): len = 277467, overlap = 540.375
PHY-3002 : Step(66): len = 276212, overlap = 524.25
PHY-3002 : Step(67): len = 274543, overlap = 513.312
PHY-3002 : Step(68): len = 272252, overlap = 500.125
PHY-3002 : Step(69): len = 270953, overlap = 513.656
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.64093e-05
PHY-3002 : Step(70): len = 281181, overlap = 519
PHY-3002 : Step(71): len = 290557, overlap = 493.656
PHY-3002 : Step(72): len = 293849, overlap = 467.906
PHY-3002 : Step(73): len = 296532, overlap = 448.719
PHY-3002 : Step(74): len = 297578, overlap = 445.688
PHY-3002 : Step(75): len = 298681, overlap = 449
PHY-3002 : Step(76): len = 294330, overlap = 438
PHY-3002 : Step(77): len = 293797, overlap = 433.156
PHY-3002 : Step(78): len = 292169, overlap = 447.594
PHY-3002 : Step(79): len = 290649, overlap = 452.312
PHY-3002 : Step(80): len = 289415, overlap = 440.906
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 7.28186e-05
PHY-3002 : Step(81): len = 295407, overlap = 433.562
PHY-3002 : Step(82): len = 300158, overlap = 406.312
PHY-3002 : Step(83): len = 302391, overlap = 371.594
PHY-3002 : Step(84): len = 303505, overlap = 362
PHY-3002 : Step(85): len = 304098, overlap = 347.844
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000145637
PHY-3002 : Step(86): len = 307367, overlap = 336.125
PHY-3002 : Step(87): len = 312915, overlap = 304.406
PHY-3002 : Step(88): len = 316428, overlap = 317.438
PHY-3002 : Step(89): len = 317849, overlap = 298.469
PHY-3002 : Step(90): len = 317756, overlap = 299.094
PHY-3002 : Step(91): len = 317441, overlap = 292.25
PHY-3002 : Step(92): len = 316611, overlap = 281.594
PHY-3002 : Step(93): len = 316163, overlap = 271.438
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000266406
PHY-3002 : Step(94): len = 317614, overlap = 272.875
PHY-3002 : Step(95): len = 320902, overlap = 267.875
PHY-3002 : Step(96): len = 322516, overlap = 255.656
PHY-3002 : Step(97): len = 324476, overlap = 256.344
PHY-3002 : Step(98): len = 325455, overlap = 252.219
PHY-3002 : Step(99): len = 325306, overlap = 263.281
PHY-3002 : Step(100): len = 325552, overlap = 268.844
PHY-3002 : Step(101): len = 324006, overlap = 277.156
PHY-3002 : Step(102): len = 323832, overlap = 281.344
PHY-3002 : Step(103): len = 323702, overlap = 279.938
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000471803
PHY-3002 : Step(104): len = 324911, overlap = 288
PHY-3002 : Step(105): len = 330412, overlap = 281.594
PHY-3002 : Step(106): len = 333039, overlap = 277.5
PHY-3002 : Step(107): len = 333233, overlap = 268.656
PHY-3002 : Step(108): len = 333465, overlap = 267.781
PHY-3002 : Step(109): len = 334839, overlap = 281.781
PHY-3002 : Step(110): len = 335975, overlap = 282.062
PHY-3002 : Step(111): len = 335722, overlap = 263.156
PHY-3002 : Step(112): len = 336030, overlap = 267.531
PHY-3002 : Step(113): len = 335914, overlap = 266.375
PHY-3002 : Step(114): len = 335825, overlap = 260.094
PHY-3002 : Step(115): len = 334793, overlap = 262.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.016206s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (96.4%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/22278.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 447352, over cnt = 1247(3%), over = 5578, worst = 42
PHY-1001 : End global iterations;  0.921717s wall, 1.125000s user + 0.031250s system = 1.156250s CPU (125.4%)

PHY-1001 : Congestion index: top1 = 75.17, top5 = 52.94, top10 = 43.47, top15 = 37.93.
PHY-3001 : End congestion estimation;  1.222353s wall, 1.406250s user + 0.046875s system = 1.453125s CPU (118.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22276 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.050934s wall, 1.015625s user + 0.031250s system = 1.046875s CPU (99.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.32664e-05
PHY-3002 : Step(116): len = 372572, overlap = 192.406
PHY-3002 : Step(117): len = 388377, overlap = 175.688
PHY-3002 : Step(118): len = 389428, overlap = 169.469
PHY-3002 : Step(119): len = 388048, overlap = 155.5
PHY-3002 : Step(120): len = 394142, overlap = 135.031
PHY-3002 : Step(121): len = 395959, overlap = 126.344
PHY-3002 : Step(122): len = 396722, overlap = 118.25
PHY-3002 : Step(123): len = 404280, overlap = 113.219
PHY-3002 : Step(124): len = 405420, overlap = 118.031
PHY-3002 : Step(125): len = 407049, overlap = 117.938
PHY-3002 : Step(126): len = 410063, overlap = 117.25
PHY-3002 : Step(127): len = 412189, overlap = 118.531
PHY-3002 : Step(128): len = 413682, overlap = 121.531
PHY-3002 : Step(129): len = 416619, overlap = 125.188
PHY-3002 : Step(130): len = 420470, overlap = 126.344
PHY-3002 : Step(131): len = 422794, overlap = 125.656
PHY-3002 : Step(132): len = 426051, overlap = 127.656
PHY-3002 : Step(133): len = 429643, overlap = 130.688
PHY-3002 : Step(134): len = 431183, overlap = 133.031
PHY-3002 : Step(135): len = 432054, overlap = 138.656
PHY-3002 : Step(136): len = 433430, overlap = 140.531
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000166533
PHY-3002 : Step(137): len = 433405, overlap = 131.594
PHY-3002 : Step(138): len = 434874, overlap = 127.469
PHY-3002 : Step(139): len = 436849, overlap = 124.281
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(140): len = 443552, overlap = 124.125
PHY-3002 : Step(141): len = 450983, overlap = 125.156
PHY-3002 : Step(142): len = 453793, overlap = 122.906
PHY-3002 : Step(143): len = 455057, overlap = 123.875
PHY-3002 : Step(144): len = 456430, overlap = 124.406
PHY-3002 : Step(145): len = 456313, overlap = 123.125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 82/22278.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 533808, over cnt = 2290(6%), over = 10396, worst = 30
PHY-1001 : End global iterations;  1.340481s wall, 2.125000s user + 0.062500s system = 2.187500s CPU (163.2%)

PHY-1001 : Congestion index: top1 = 74.53, top5 = 58.30, top10 = 49.75, top15 = 44.73.
PHY-3001 : End congestion estimation;  1.638946s wall, 2.421875s user + 0.062500s system = 2.484375s CPU (151.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22276 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.257673s wall, 1.250000s user + 0.000000s system = 1.250000s CPU (99.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000104289
PHY-3002 : Step(146): len = 462149, overlap = 336.719
PHY-3002 : Step(147): len = 466253, overlap = 290.594
PHY-3002 : Step(148): len = 462159, overlap = 244.938
PHY-3002 : Step(149): len = 458057, overlap = 236.469
PHY-3002 : Step(150): len = 453495, overlap = 213.688
PHY-3002 : Step(151): len = 451364, overlap = 211.469
PHY-3002 : Step(152): len = 450927, overlap = 209.562
PHY-3002 : Step(153): len = 448826, overlap = 208.562
PHY-3002 : Step(154): len = 447103, overlap = 213.406
PHY-3002 : Step(155): len = 445599, overlap = 209.688
PHY-3002 : Step(156): len = 443973, overlap = 200.562
PHY-3002 : Step(157): len = 444149, overlap = 200.531
PHY-3002 : Step(158): len = 442814, overlap = 209.031
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000208577
PHY-3002 : Step(159): len = 443156, overlap = 198.344
PHY-3002 : Step(160): len = 444241, overlap = 193.219
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000386499
PHY-3002 : Step(161): len = 446182, overlap = 185.875
PHY-3002 : Step(162): len = 454401, overlap = 164.281
PHY-3002 : Step(163): len = 459139, overlap = 169.938
PHY-3002 : Step(164): len = 457401, overlap = 165.938
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000760779
PHY-3002 : Step(165): len = 458000, overlap = 158.969
PHY-3002 : Step(166): len = 463403, overlap = 154.656
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83159, tnet num: 22276, tinst num: 19909, tnode num: 117287, tedge num: 130712.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.652228s wall, 1.609375s user + 0.046875s system = 1.656250s CPU (100.2%)

RUN-1004 : used memory is 576 MB, reserved memory is 552 MB, peak memory is 712 MB
OPT-1001 : Total overflow 542.97 peak overflow 4.78
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 576/22278.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 554192, over cnt = 2630(7%), over = 9217, worst = 23
PHY-1001 : End global iterations;  1.372948s wall, 2.031250s user + 0.031250s system = 2.062500s CPU (150.2%)

PHY-1001 : Congestion index: top1 = 56.34, top5 = 47.02, top10 = 42.78, top15 = 40.02.
PHY-1001 : End incremental global routing;  1.642650s wall, 2.296875s user + 0.031250s system = 2.328125s CPU (141.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22276 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.110269s wall, 1.109375s user + 0.000000s system = 1.109375s CPU (99.9%)

OPT-1001 : 21 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19825 has valid locations, 304 needs to be replaced
PHY-3001 : design contains 20192 instances, 5909 luts, 12723 seqs, 1445 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 483697
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17355/22561.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 574000, over cnt = 2703(7%), over = 9421, worst = 23
PHY-1001 : End global iterations;  0.221276s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (105.9%)

PHY-1001 : Congestion index: top1 = 55.95, top5 = 47.42, top10 = 43.28, top15 = 40.53.
PHY-3001 : End congestion estimation;  0.489293s wall, 0.500000s user + 0.000000s system = 0.500000s CPU (102.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 84158, tnet num: 22559, tinst num: 20192, tnode num: 118642, tedge num: 132144.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.665885s wall, 1.640625s user + 0.031250s system = 1.671875s CPU (100.4%)

RUN-1004 : used memory is 623 MB, reserved memory is 613 MB, peak memory is 715 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22559 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.810214s wall, 2.750000s user + 0.062500s system = 2.812500s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(167): len = 483144, overlap = 5
PHY-3002 : Step(168): len = 484068, overlap = 5.0625
PHY-3002 : Step(169): len = 484972, overlap = 5.3125
PHY-3002 : Step(170): len = 485554, overlap = 5.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17405/22561.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 570064, over cnt = 2730(7%), over = 9522, worst = 23
PHY-1001 : End global iterations;  0.215878s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (144.8%)

PHY-1001 : Congestion index: top1 = 56.38, top5 = 47.61, top10 = 43.52, top15 = 40.81.
PHY-3001 : End congestion estimation;  0.515562s wall, 0.593750s user + 0.015625s system = 0.609375s CPU (118.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22559 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.120617s wall, 1.046875s user + 0.062500s system = 1.109375s CPU (99.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000938709
PHY-3002 : Step(171): len = 485897, overlap = 158.562
PHY-3002 : Step(172): len = 486646, overlap = 157.406
PHY-3002 : Step(173): len = 487289, overlap = 157.375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00187742
PHY-3002 : Step(174): len = 487522, overlap = 157.344
PHY-3002 : Step(175): len = 488149, overlap = 157.156
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00375483
PHY-3002 : Step(176): len = 488355, overlap = 157.219
PHY-3002 : Step(177): len = 489094, overlap = 157.406
PHY-3001 : Final: Len = 489094, Over = 157.406
PHY-3001 : End incremental placement;  6.103161s wall, 6.359375s user + 0.359375s system = 6.718750s CPU (110.1%)

OPT-1001 : Total overflow 547.84 peak overflow 4.78
OPT-1001 : End high-fanout net optimization;  9.477112s wall, 10.562500s user + 0.390625s system = 10.953125s CPU (115.6%)

OPT-1001 : Current memory(MB): used = 719, reserve = 701, peak = 735.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17392/22561.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 574216, over cnt = 2663(7%), over = 8785, worst = 23
PHY-1002 : len = 619264, over cnt = 1815(5%), over = 4439, worst = 22
PHY-1002 : len = 655464, over cnt = 764(2%), over = 1766, worst = 13
PHY-1002 : len = 672736, over cnt = 293(0%), over = 662, worst = 13
PHY-1002 : len = 683952, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.475574s wall, 2.156250s user + 0.000000s system = 2.156250s CPU (146.1%)

PHY-1001 : Congestion index: top1 = 50.47, top5 = 44.32, top10 = 41.21, top15 = 39.32.
OPT-1001 : End congestion update;  1.751896s wall, 2.421875s user + 0.000000s system = 2.421875s CPU (138.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22559 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.124530s wall, 1.109375s user + 0.000000s system = 1.109375s CPU (98.7%)

OPT-0007 : Start: WNS 4303 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.883143s wall, 3.531250s user + 0.000000s system = 3.531250s CPU (122.5%)

OPT-1001 : Current memory(MB): used = 717, reserve = 699, peak = 735.
OPT-1001 : End physical optimization;  14.359015s wall, 16.203125s user + 0.453125s system = 16.656250s CPU (116.0%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5909 LUT to BLE ...
SYN-4008 : Packed 5909 LUT and 2907 SEQ to BLE.
SYN-4003 : Packing 9816 remaining SEQ's ...
SYN-4005 : Packed 3363 SEQ with LUT/SLICE
SYN-4006 : 135 single LUT's are left
SYN-4006 : 6453 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 12362/14044 primitive instances ...
PHY-3001 : End packing;  3.185570s wall, 3.187500s user + 0.000000s system = 3.187500s CPU (100.1%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8363 instances
RUN-1001 : 4123 mslices, 4123 lslices, 60 pads, 52 brams, 0 dsps
RUN-1001 : There are total 19704 nets
RUN-1001 : 13712 nets have 2 pins
RUN-1001 : 4557 nets have [3 - 5] pins
RUN-1001 : 867 nets have [6 - 10] pins
RUN-1001 : 410 nets have [11 - 20] pins
RUN-1001 : 148 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
PHY-3001 : design contains 8361 instances, 8246 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Cell area utilization is 87%
PHY-3001 : After packing: Len = 507740, Over = 388.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7843/19704.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 647104, over cnt = 1663(4%), over = 2699, worst = 9
PHY-1002 : len = 654952, over cnt = 1063(3%), over = 1448, worst = 8
PHY-1002 : len = 667224, over cnt = 435(1%), over = 546, worst = 5
PHY-1002 : len = 673304, over cnt = 158(0%), over = 198, worst = 4
PHY-1002 : len = 677416, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.474984s wall, 2.328125s user + 0.031250s system = 2.359375s CPU (160.0%)

PHY-1001 : Congestion index: top1 = 50.80, top5 = 44.68, top10 = 41.34, top15 = 39.05.
PHY-3001 : End congestion estimation;  1.832922s wall, 2.687500s user + 0.031250s system = 2.718750s CPU (148.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69588, tnet num: 19702, tinst num: 8361, tnode num: 94762, tedge num: 114593.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.811121s wall, 1.812500s user + 0.000000s system = 1.812500s CPU (100.1%)

RUN-1004 : used memory is 609 MB, reserved memory is 595 MB, peak memory is 735 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19702 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.846753s wall, 2.796875s user + 0.046875s system = 2.843750s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.70163e-05
PHY-3002 : Step(178): len = 509163, overlap = 372
PHY-3002 : Step(179): len = 507694, overlap = 388
PHY-3002 : Step(180): len = 506178, overlap = 399.25
PHY-3002 : Step(181): len = 507132, overlap = 411.75
PHY-3002 : Step(182): len = 504422, overlap = 416.5
PHY-3002 : Step(183): len = 503276, overlap = 421.75
PHY-3002 : Step(184): len = 501587, overlap = 424
PHY-3002 : Step(185): len = 499256, overlap = 433
PHY-3002 : Step(186): len = 497759, overlap = 439.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.40327e-05
PHY-3002 : Step(187): len = 502950, overlap = 427.5
PHY-3002 : Step(188): len = 508276, overlap = 413
PHY-3002 : Step(189): len = 507850, overlap = 413.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000188065
PHY-3002 : Step(190): len = 516187, overlap = 397.5
PHY-3002 : Step(191): len = 526024, overlap = 369.75
PHY-3002 : Step(192): len = 523777, overlap = 366.75
PHY-3002 : Step(193): len = 521783, overlap = 367.5
PHY-3002 : Step(194): len = 521700, overlap = 365.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000310096
PHY-3002 : Step(195): len = 527011, overlap = 357.5
PHY-3002 : Step(196): len = 532676, overlap = 343.5
PHY-3002 : Step(197): len = 536393, overlap = 342
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.000620192
PHY-3002 : Step(198): len = 537854, overlap = 344
PHY-3002 : Step(199): len = 542093, overlap = 338.25
PHY-3002 : Step(200): len = 547964, overlap = 333.75
PHY-3002 : Step(201): len = 548519, overlap = 334
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.864209s wall, 0.750000s user + 0.953125s system = 1.703125s CPU (197.1%)

PHY-3001 : Trial Legalized: Len = 645321
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 503/19704.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 740616, over cnt = 2530(7%), over = 4055, worst = 7
PHY-1002 : len = 756344, over cnt = 1490(4%), over = 2004, worst = 6
PHY-1002 : len = 776032, over cnt = 393(1%), over = 501, worst = 5
PHY-1002 : len = 781304, over cnt = 150(0%), over = 170, worst = 4
PHY-1002 : len = 784976, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.247992s wall, 3.781250s user + 0.062500s system = 3.843750s CPU (171.0%)

PHY-1001 : Congestion index: top1 = 50.65, top5 = 45.33, top10 = 42.71, top15 = 40.89.
PHY-3001 : End congestion estimation;  2.642487s wall, 4.187500s user + 0.062500s system = 4.250000s CPU (160.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19702 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.008476s wall, 0.984375s user + 0.015625s system = 1.000000s CPU (99.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000195433
PHY-3002 : Step(202): len = 599314, overlap = 78
PHY-3002 : Step(203): len = 581401, overlap = 121.5
PHY-3002 : Step(204): len = 569486, overlap = 159.75
PHY-3002 : Step(205): len = 559373, overlap = 211.25
PHY-3002 : Step(206): len = 554228, overlap = 246.75
PHY-3002 : Step(207): len = 551321, overlap = 254.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000390865
PHY-3002 : Step(208): len = 555401, overlap = 249.5
PHY-3002 : Step(209): len = 559240, overlap = 251.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(210): len = 559960, overlap = 250
PHY-3002 : Step(211): len = 564789, overlap = 249
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.037218s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (84.0%)

PHY-3001 : Legalized: Len = 609943, Over = 0
PHY-3001 : Spreading special nets. 46 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.094741s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (99.0%)

PHY-3001 : 64 instances has been re-located, deltaX = 28, deltaY = 33, maxDist = 2.
PHY-3001 : Final: Len = 610693, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69588, tnet num: 19702, tinst num: 8361, tnode num: 94762, tedge num: 114593.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.104447s wall, 2.093750s user + 0.015625s system = 2.109375s CPU (100.2%)

RUN-1004 : used memory is 607 MB, reserved memory is 589 MB, peak memory is 735 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 5145/19704.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 717008, over cnt = 2296(6%), over = 3494, worst = 7
PHY-1002 : len = 728712, over cnt = 1295(3%), over = 1721, worst = 5
PHY-1002 : len = 744624, over cnt = 418(1%), over = 533, worst = 4
PHY-1002 : len = 750248, over cnt = 158(0%), over = 198, worst = 4
PHY-1002 : len = 753744, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.876405s wall, 2.984375s user + 0.000000s system = 2.984375s CPU (159.0%)

PHY-1001 : Congestion index: top1 = 48.43, top5 = 43.98, top10 = 41.36, top15 = 39.55.
PHY-1001 : End incremental global routing;  2.211343s wall, 3.312500s user + 0.000000s system = 3.312500s CPU (149.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19702 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.042498s wall, 1.015625s user + 0.031250s system = 1.046875s CPU (100.4%)

OPT-1001 : 3 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8295 has valid locations, 21 needs to be replaced
PHY-3001 : design contains 8379 instances, 8264 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 613133
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17780/19720.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 756192, over cnt = 25(0%), over = 30, worst = 3
PHY-1002 : len = 756296, over cnt = 10(0%), over = 10, worst = 1
PHY-1002 : len = 756336, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 756368, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.622475s wall, 0.687500s user + 0.031250s system = 0.718750s CPU (115.5%)

PHY-1001 : Congestion index: top1 = 48.43, top5 = 44.06, top10 = 41.40, top15 = 39.60.
PHY-3001 : End congestion estimation;  0.963969s wall, 1.000000s user + 0.046875s system = 1.046875s CPU (108.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69699, tnet num: 19718, tinst num: 8379, tnode num: 94900, tedge num: 114733.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.182728s wall, 2.187500s user + 0.000000s system = 2.187500s CPU (100.2%)

RUN-1004 : used memory is 674 MB, reserved memory is 671 MB, peak memory is 735 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19718 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.244414s wall, 3.234375s user + 0.015625s system = 3.250000s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(212): len = 613165, overlap = 1
PHY-3002 : Step(213): len = 613179, overlap = 1.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17771/19720.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 755456, over cnt = 31(0%), over = 34, worst = 2
PHY-1002 : len = 755504, over cnt = 14(0%), over = 14, worst = 1
PHY-1002 : len = 755632, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 755688, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 755688, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.763213s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (102.4%)

PHY-1001 : Congestion index: top1 = 48.49, top5 = 44.07, top10 = 41.41, top15 = 39.59.
PHY-3001 : End congestion estimation;  1.100767s wall, 1.093750s user + 0.015625s system = 1.109375s CPU (100.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19718 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.129611s wall, 1.125000s user + 0.000000s system = 1.125000s CPU (99.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000652731
PHY-3002 : Step(214): len = 613017, overlap = 3
PHY-3002 : Step(215): len = 612937, overlap = 3.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007157s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 613051, Over = 0
PHY-3001 : End spreading;  0.079092s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (98.8%)

PHY-3001 : Final: Len = 613051, Over = 0
PHY-3001 : End incremental placement;  7.135387s wall, 7.187500s user + 0.250000s system = 7.437500s CPU (104.2%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.967942s wall, 12.109375s user + 0.281250s system = 12.390625s CPU (113.0%)

OPT-1001 : Current memory(MB): used = 729, reserve = 714, peak = 735.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17774/19720.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 755664, over cnt = 22(0%), over = 24, worst = 2
PHY-1002 : len = 755632, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 755776, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.450040s wall, 0.468750s user + 0.000000s system = 0.468750s CPU (104.2%)

PHY-1001 : Congestion index: top1 = 48.62, top5 = 44.12, top10 = 41.43, top15 = 39.60.
OPT-1001 : End congestion update;  0.788572s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (101.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19718 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.866330s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (99.2%)

OPT-0007 : Start: WNS 4554 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.660217s wall, 1.671875s user + 0.000000s system = 1.671875s CPU (100.7%)

OPT-1001 : Current memory(MB): used = 730, reserve = 714, peak = 735.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19718 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.869156s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (98.9%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17798/19720.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 755776, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.126493s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (98.8%)

PHY-1001 : Congestion index: top1 = 48.62, top5 = 44.12, top10 = 41.43, top15 = 39.60.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19718 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.855492s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (100.5%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4554 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 48.034483
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4554ps with logic level 4 
OPT-1001 : End physical optimization;  17.198966s wall, 18.406250s user + 0.296875s system = 18.703125s CPU (108.7%)

RUN-1003 : finish command "place" in  74.533507s wall, 141.640625s user + 8.656250s system = 150.296875s CPU (201.7%)

RUN-1004 : used memory is 643 MB, reserved memory is 621 MB, peak memory is 735 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.748148s wall, 3.015625s user + 0.015625s system = 3.031250s CPU (173.4%)

RUN-1004 : used memory is 643 MB, reserved memory is 621 MB, peak memory is 735 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8381 instances
RUN-1001 : 4139 mslices, 4125 lslices, 60 pads, 52 brams, 0 dsps
RUN-1001 : There are total 19720 nets
RUN-1001 : 13710 nets have 2 pins
RUN-1001 : 4560 nets have [3 - 5] pins
RUN-1001 : 874 nets have [6 - 10] pins
RUN-1001 : 416 nets have [11 - 20] pins
RUN-1001 : 150 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69699, tnet num: 19718, tinst num: 8379, tnode num: 94900, tedge num: 114733.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.914751s wall, 1.921875s user + 0.000000s system = 1.921875s CPU (100.4%)

RUN-1004 : used memory is 650 MB, reserved memory is 642 MB, peak memory is 735 MB
PHY-1001 : 4139 mslices, 4125 lslices, 60 pads, 52 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19718 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 694056, over cnt = 2458(6%), over = 4025, worst = 7
PHY-1002 : len = 711152, over cnt = 1492(4%), over = 2046, worst = 6
PHY-1002 : len = 726048, over cnt = 746(2%), over = 998, worst = 5
PHY-1002 : len = 742680, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 742976, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.936348s wall, 3.312500s user + 0.062500s system = 3.375000s CPU (174.3%)

PHY-1001 : Congestion index: top1 = 48.79, top5 = 43.75, top10 = 41.01, top15 = 39.24.
PHY-1001 : End global routing;  2.301878s wall, 3.671875s user + 0.062500s system = 3.734375s CPU (162.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 715, reserve = 703, peak = 735.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 983, reserve = 969, peak = 983.
PHY-1001 : End build detailed router design. 4.794658s wall, 4.703125s user + 0.078125s system = 4.781250s CPU (99.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 194664, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.996401s wall, 0.984375s user + 0.000000s system = 0.984375s CPU (98.8%)

PHY-1001 : Current memory(MB): used = 1019, reserve = 1006, peak = 1019.
PHY-1001 : End phase 1; 1.003935s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (99.6%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.86722e+06, over cnt = 1467(0%), over = 1471, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1035, reserve = 1024, peak = 1035.
PHY-1001 : End initial routed; 22.288164s wall, 51.468750s user + 0.281250s system = 51.750000s CPU (232.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18496(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.365   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.661829s wall, 3.671875s user + 0.000000s system = 3.671875s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 1050, reserve = 1039, peak = 1050.
PHY-1001 : End phase 2; 25.950170s wall, 55.140625s user + 0.281250s system = 55.421875s CPU (213.6%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.86722e+06, over cnt = 1467(0%), over = 1471, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.265108s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (100.2%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.85242e+06, over cnt = 572(0%), over = 572, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.967515s wall, 1.843750s user + 0.000000s system = 1.843750s CPU (190.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.85122e+06, over cnt = 85(0%), over = 85, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.700617s wall, 0.984375s user + 0.000000s system = 0.984375s CPU (140.5%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.8517e+06, over cnt = 15(0%), over = 15, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.232157s wall, 0.328125s user + 0.031250s system = 0.359375s CPU (154.8%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.85182e+06, over cnt = 10(0%), over = 10, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.205111s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (106.6%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.85202e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.193759s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (96.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18496(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.213   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.927113s wall, 3.921875s user + 0.000000s system = 3.921875s CPU (99.9%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 380 feed throughs used by 314 nets
PHY-1001 : End commit to database; 2.670128s wall, 2.625000s user + 0.046875s system = 2.671875s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1137, reserve = 1129, peak = 1137.
PHY-1001 : End phase 3; 9.674704s wall, 10.859375s user + 0.093750s system = 10.953125s CPU (113.2%)

PHY-1003 : Routed, final wirelength = 1.85202e+06
PHY-1001 : Current memory(MB): used = 1141, reserve = 1133, peak = 1141.
PHY-1001 : End export database. 0.074956s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (104.2%)

PHY-1001 : End detail routing;  41.972068s wall, 72.250000s user + 0.453125s system = 72.703125s CPU (173.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69699, tnet num: 19718, tinst num: 8379, tnode num: 94900, tedge num: 114733.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  2.078608s wall, 2.046875s user + 0.000000s system = 2.046875s CPU (98.5%)

RUN-1004 : used memory is 1070 MB, reserved memory is 1072 MB, peak memory is 1141 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  51.195603s wall, 82.796875s user + 0.531250s system = 83.328125s CPU (162.8%)

RUN-1004 : used memory is 1069 MB, reserved memory is 1072 MB, peak memory is 1141 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8956   out of  19600   45.69%
#reg                    12821   out of  19600   65.41%
#le                     15366
  #lut only              2545   out of  15366   16.56%
  #reg only              6410   out of  15366   41.72%
  #lut&reg               6411   out of  15366   41.72%
#dsp                        0   out of     29    0.00%
#bram                      52   out of     64   81.25%
  #bram9k                  52
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                    10
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    7046
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          202
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         M3        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D14        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         P6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT        D16        LVCMOS33           8            NONE           NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        R15        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15366  |7511    |1445    |12864   |52      |0       |
|  AnyFog_dataX                      |AnyFog          |225    |119     |22      |187     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |87     |64      |22      |51      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |227    |97      |22      |186     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |90     |65      |22      |53      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |225    |141     |22      |184     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |88     |65      |22      |51      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |3341   |870     |34      |3270    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |734    |108     |5       |717     |0       |0       |
|    STADOP_com2                     |STADOP          |554    |55      |0       |552     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |42      |14      |41      |0       |0       |
|    head_com2                       |uniheading      |263    |79      |5       |254     |0       |0       |
|    uart_com2                       |Agrica          |1437   |298     |10      |1418    |0       |0       |
|  COM3                              |COM3_Control    |213    |101     |14      |183     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |61     |37      |14      |38      |0       |0       |
|    rmc_com3                        |Gprmc           |152    |64      |0       |145     |0       |0       |
|  DATA                              |Data_Processing |8642   |4376    |1059    |6936    |0       |0       |
|    DIV_Dtemp                       |Divider         |814    |335     |84      |679     |0       |0       |
|    DIV_Utemp                       |Divider         |622    |291     |84      |496     |0       |0       |
|    DIV_accX                        |Divider         |663    |319     |84      |539     |0       |0       |
|    DIV_accY                        |Divider         |593    |316     |108     |428     |0       |0       |
|    DIV_accZ                        |Divider         |616    |377     |132     |411     |0       |0       |
|    DIV_rateX                       |Divider         |673    |344     |132     |467     |0       |0       |
|    DIV_rateY                       |Divider         |622    |363     |132     |416     |0       |0       |
|    DIV_rateZ                       |Divider         |582    |389     |132     |365     |0       |0       |
|    genclk                          |genclk          |84     |50      |20      |50      |0       |0       |
|  FMC                               |FMC_Ctrl        |409    |347     |43      |337     |0       |0       |
|  IIC                               |I2C_master      |313    |279     |11      |257     |0       |0       |
|  IMU_CTRL                          |SCHA634         |921    |681     |61      |725     |0       |0       |
|    CtrlData                        |CtrlData        |496    |439     |47      |334     |0       |0       |
|      usms                          |Time_1ms        |30     |25      |5       |22      |0       |0       |
|    SPIM                            |SPI_SCHA634     |425    |242     |14      |391     |0       |0       |
|  POWER                             |POWER_EN        |100    |48      |38      |39      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |729    |452     |119     |496     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |729    |452     |119     |496     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |328    |190     |0       |311     |0       |0       |
|        reg_inst                    |register        |326    |188     |0       |309     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |401    |262     |119     |185     |0       |0       |
|        bus_inst                    |bus_top         |176    |114     |62      |63      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |26     |16      |10      |9       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |50     |32      |18      |18      |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |98     |64      |34      |34      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |146    |110     |29      |90      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13649  
    #2          2       3564   
    #3          3        680   
    #4          4        316   
    #5        5-10       971   
    #6        11-50      450   
    #7       51-100      19    
    #8       101-500      4    
    #9        >500        2    
  Average     2.17             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.216820s wall, 3.828125s user + 0.015625s system = 3.843750s CPU (173.4%)

RUN-1004 : used memory is 1070 MB, reserved memory is 1073 MB, peak memory is 1141 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69699, tnet num: 19718, tinst num: 8379, tnode num: 94900, tedge num: 114733.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.957334s wall, 1.968750s user + 0.000000s system = 1.968750s CPU (100.6%)

RUN-1004 : used memory is 1072 MB, reserved memory is 1075 MB, peak memory is 1141 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19718 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.556003s wall, 1.531250s user + 0.000000s system = 1.531250s CPU (98.4%)

RUN-1004 : used memory is 1077 MB, reserved memory is 1080 MB, peak memory is 1141 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 28b752c1c0e7d7ba92ac5beed3c46754b7b56ac64969e4b0149cfdbc1d295011 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8379
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19720, pip num: 153840
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 380
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3259 valid insts, and 427572 bits set as '1'.
BIT-1004 : the usercode register value: 00000000000000001110111010000001
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.897174s wall, 127.265625s user + 0.125000s system = 127.390625s CPU (987.7%)

RUN-1004 : used memory is 1203 MB, reserved memory is 1189 MB, peak memory is 1318 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250620_154442.log"

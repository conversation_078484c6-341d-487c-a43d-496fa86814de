============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Tue Jul  1 14:25:05 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(519)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
RUN-1001 : Project manager successfully analyzed 22 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.861394s wall, 1.656250s user + 4.218750s system = 5.875000s CPU (100.2%)

RUN-1004 : used memory is 84 MB, reserved memory is 41 MB, peak memory is 95 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  2.082307s wall, 1.968750s user + 0.093750s system = 2.062500s CPU (99.0%)

RUN-1004 : used memory is 307 MB, reserved memory is 271 MB, peak memory is 310 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 6 view nodes, 33 trigger nets, 33 data nets.
KIT-1004 : Chipwatcher code = 0100100000001111
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=6,BUS_DIN_NUM=33,BUS_CTRL_NUM=90,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb011,32'sb0100},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010,32'sb011101},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100,32'sb01001110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=112) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=112) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=6,BUS_DIN_NUM=33,BUS_CTRL_NUM=90,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb011,32'sb0100},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010,32'sb011101},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100,32'sb01001110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=6,BUS_DIN_NUM=33,BUS_CTRL_NUM=90,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb011,32'sb0100},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010,32'sb011101},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100,32'sb01001110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb011) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=6,BUS_DIN_NUM=33,BUS_CTRL_NUM=90,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb011,32'sb0100},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010,32'sb011101},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100,32'sb01001110})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=112)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=112)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=6,BUS_DIN_NUM=33,BUS_CTRL_NUM=90,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb011,32'sb0100},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010,32'sb011101},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100,32'sb01001110})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=6,BUS_DIN_NUM=33,BUS_CTRL_NUM=90,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb011,32'sb0100},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010,32'sb011101},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100,32'sb01001110})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb011)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 23036/28 useful/useless nets, 19949/14 useful/useless insts
SYN-1016 : Merged 34 instances.
SYN-1032 : 22727/20 useful/useless nets, 20333/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 4 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 4 mux instances.
SYN-1015 : Optimize round 1, 364 better
SYN-1014 : Optimize round 2
SYN-1032 : 22418/60 useful/useless nets, 20024/64 useful/useless insts
SYN-1015 : Optimize round 2, 128 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.659361s wall, 2.656250s user + 0.015625s system = 2.671875s CPU (100.5%)

RUN-1004 : used memory is 331 MB, reserved memory is 296 MB, peak memory is 332 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22454/227 useful/useless nets, 20089/38 useful/useless insts
SYN-1016 : Merged 33 instances.
SYN-2571 : Optimize after map_dsp, round 1, 298 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 31 instances.
SYN-2501 : Optimize round 1, 64 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 18 macro adder
SYN-1019 : Optimized 17 mux instances.
SYN-1016 : Merged 12 instances.
SYN-1032 : 22878/5 useful/useless nets, 20513/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83735, tnet num: 22878, tinst num: 20512, tnode num: 117991, tedge num: 130390.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.361514s wall, 1.312500s user + 0.046875s system = 1.359375s CPU (99.8%)

RUN-1004 : used memory is 473 MB, reserved memory is 440 MB, peak memory is 473 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22878 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 215 (3.50), #lev = 7 (1.91)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 215 (3.50), #lev = 7 (1.91)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 506 instances into 215 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 371 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 114 adder to BLE ...
SYN-4008 : Packed 114 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  5.117577s wall, 5.062500s user + 0.046875s system = 5.109375s CPU (99.8%)

RUN-1004 : used memory is 356 MB, reserved memory is 323 MB, peak memory is 585 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  8.145740s wall, 8.015625s user + 0.125000s system = 8.140625s CPU (99.9%)

RUN-1004 : used memory is 356 MB, reserved memory is 324 MB, peak memory is 585 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/uart_com2/AGRIC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/uart_com2/AGRIC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/uart_com2/AGRIC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/uart_com2/AGRIC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/uart_com2/AGRIC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/uart_com2/AGRIC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/uart_com2/AGRIC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/uart_com2/AGRIC_data[0]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[7] will be merged to another kept net COM3/rmc_com3/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM3/UART_RX_COM3/rx_data[6] will be merged to another kept net COM3/rmc_com3/GPRMC_data[6]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (243 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19886 instances
RUN-0007 : 5678 luts, 12690 seqs, 929 mslices, 494 lslices, 60 pads, 30 brams, 0 dsps
RUN-1001 : There are total 22275 nets
RUN-1001 : 16736 nets have 2 pins
RUN-1001 : 4398 nets have [3 - 5] pins
RUN-1001 : 791 nets have [6 - 10] pins
RUN-1001 : 224 nets have [11 - 20] pins
RUN-1001 : 102 nets have [21 - 99] pins
RUN-1001 : 24 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4742     
RUN-1001 :   No   |  No   |  Yes  |     646     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    6787     
RUN-1001 :   Yes  |  No   |  Yes  |     415     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  117  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 125
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19884 instances, 5678 luts, 12690 seqs, 1423 slices, 285 macros(1423 instances: 929 mslices 494 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1195 pins
PHY-3001 : Huge net DATA/done_div with 1714 pins
PHY-0007 : Cell area utilization is 65%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82385, tnet num: 22273, tinst num: 19884, tnode num: 116628, tedge num: 129236.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.377762s wall, 1.343750s user + 0.031250s system = 1.375000s CPU (99.8%)

RUN-1004 : used memory is 534 MB, reserved memory is 504 MB, peak memory is 585 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22273 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.383713s wall, 2.343750s user + 0.046875s system = 2.390625s CPU (100.3%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.40492e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19884.
PHY-3001 : Level 1 #clusters 2097.
PHY-3001 : End clustering;  0.176248s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (133.0%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 65%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 842648, overlap = 631.969
PHY-3002 : Step(2): len = 770007, overlap = 688.094
PHY-3002 : Step(3): len = 504610, overlap = 851.438
PHY-3002 : Step(4): len = 444852, overlap = 908.219
PHY-3002 : Step(5): len = 344189, overlap = 1051.22
PHY-3002 : Step(6): len = 308448, overlap = 1105.03
PHY-3002 : Step(7): len = 259747, overlap = 1195.19
PHY-3002 : Step(8): len = 236224, overlap = 1217.81
PHY-3002 : Step(9): len = 207128, overlap = 1287.62
PHY-3002 : Step(10): len = 193064, overlap = 1333.78
PHY-3002 : Step(11): len = 169902, overlap = 1366.94
PHY-3002 : Step(12): len = 158812, overlap = 1411.16
PHY-3002 : Step(13): len = 139783, overlap = 1439.97
PHY-3002 : Step(14): len = 132567, overlap = 1467.41
PHY-3002 : Step(15): len = 121640, overlap = 1478.28
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.03893e-06
PHY-3002 : Step(16): len = 128264, overlap = 1454.72
PHY-3002 : Step(17): len = 168054, overlap = 1354.22
PHY-3002 : Step(18): len = 177049, overlap = 1294.22
PHY-3002 : Step(19): len = 179674, overlap = 1203.34
PHY-3002 : Step(20): len = 181029, overlap = 1162.12
PHY-3002 : Step(21): len = 177519, overlap = 1134.66
PHY-3002 : Step(22): len = 174558, overlap = 1117.25
PHY-3002 : Step(23): len = 173377, overlap = 1091.59
PHY-3002 : Step(24): len = 170215, overlap = 1085.38
PHY-3002 : Step(25): len = 167502, overlap = 1080.84
PHY-3002 : Step(26): len = 164111, overlap = 1071.69
PHY-3002 : Step(27): len = 162062, overlap = 1076.41
PHY-3002 : Step(28): len = 160767, overlap = 1084.75
PHY-3002 : Step(29): len = 160022, overlap = 1086.06
PHY-3002 : Step(30): len = 159497, overlap = 1091.62
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.07786e-06
PHY-3002 : Step(31): len = 167708, overlap = 1068.78
PHY-3002 : Step(32): len = 183756, overlap = 979.656
PHY-3002 : Step(33): len = 187759, overlap = 931.75
PHY-3002 : Step(34): len = 189834, overlap = 905.312
PHY-3002 : Step(35): len = 190128, overlap = 901.562
PHY-3002 : Step(36): len = 190248, overlap = 923.312
PHY-3002 : Step(37): len = 189739, overlap = 920.219
PHY-3002 : Step(38): len = 188684, overlap = 934.438
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.15573e-06
PHY-3002 : Step(39): len = 200208, overlap = 900.188
PHY-3002 : Step(40): len = 215315, overlap = 814.562
PHY-3002 : Step(41): len = 219632, overlap = 795.5
PHY-3002 : Step(42): len = 222000, overlap = 782.938
PHY-3002 : Step(43): len = 220548, overlap = 769.688
PHY-3002 : Step(44): len = 218840, overlap = 743.75
PHY-3002 : Step(45): len = 217685, overlap = 727.25
PHY-3002 : Step(46): len = 216481, overlap = 733.531
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 8.31146e-06
PHY-3002 : Step(47): len = 228868, overlap = 675.906
PHY-3002 : Step(48): len = 245058, overlap = 608.406
PHY-3002 : Step(49): len = 248542, overlap = 554.906
PHY-3002 : Step(50): len = 249332, overlap = 529.938
PHY-3002 : Step(51): len = 247879, overlap = 515.094
PHY-3002 : Step(52): len = 246662, overlap = 525.656
PHY-3002 : Step(53): len = 244994, overlap = 513.844
PHY-3002 : Step(54): len = 244054, overlap = 522.5
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.66229e-05
PHY-3002 : Step(55): len = 255270, overlap = 514.406
PHY-3002 : Step(56): len = 267197, overlap = 480.625
PHY-3002 : Step(57): len = 270676, overlap = 451.375
PHY-3002 : Step(58): len = 272096, overlap = 439.969
PHY-3002 : Step(59): len = 271002, overlap = 437.875
PHY-3002 : Step(60): len = 270560, overlap = 432.812
PHY-3002 : Step(61): len = 269087, overlap = 436.594
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.32458e-05
PHY-3002 : Step(62): len = 278807, overlap = 376.844
PHY-3002 : Step(63): len = 287897, overlap = 373.938
PHY-3002 : Step(64): len = 290701, overlap = 359.062
PHY-3002 : Step(65): len = 291826, overlap = 342.281
PHY-3002 : Step(66): len = 290084, overlap = 356.781
PHY-3002 : Step(67): len = 290023, overlap = 366.875
PHY-3002 : Step(68): len = 289003, overlap = 374.062
PHY-3002 : Step(69): len = 289536, overlap = 369.406
PHY-3002 : Step(70): len = 288157, overlap = 366.719
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 6.64917e-05
PHY-3002 : Step(71): len = 296609, overlap = 359.375
PHY-3002 : Step(72): len = 304833, overlap = 355.031
PHY-3002 : Step(73): len = 308284, overlap = 338.219
PHY-3002 : Step(74): len = 309822, overlap = 343.156
PHY-3002 : Step(75): len = 308560, overlap = 357.094
PHY-3002 : Step(76): len = 308840, overlap = 362.344
PHY-3002 : Step(77): len = 308011, overlap = 361.219
PHY-3002 : Step(78): len = 307685, overlap = 371.594
PHY-3002 : Step(79): len = 307321, overlap = 384.219
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000132983
PHY-3002 : Step(80): len = 311852, overlap = 360.625
PHY-3002 : Step(81): len = 317827, overlap = 314.562
PHY-3002 : Step(82): len = 319632, overlap = 302.875
PHY-3002 : Step(83): len = 321709, overlap = 265.062
PHY-3002 : Step(84): len = 321397, overlap = 256.125
PHY-3002 : Step(85): len = 322803, overlap = 252.344
PHY-3002 : Step(86): len = 321579, overlap = 248.344
PHY-3002 : Step(87): len = 321630, overlap = 229.406
PHY-3002 : Step(88): len = 322780, overlap = 202.219
PHY-3002 : Step(89): len = 324695, overlap = 216.281
PHY-3002 : Step(90): len = 323441, overlap = 203.312
PHY-3002 : Step(91): len = 323294, overlap = 193.156
PHY-3002 : Step(92): len = 322051, overlap = 196.969
PHY-3002 : Step(93): len = 322336, overlap = 210.25
PHY-3002 : Step(94): len = 321538, overlap = 219.406
PHY-3002 : Step(95): len = 322511, overlap = 223.5
PHY-3002 : Step(96): len = 321174, overlap = 227.656
PHY-3002 : Step(97): len = 320574, overlap = 234.75
PHY-3002 : Step(98): len = 319210, overlap = 239.812
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000264776
PHY-3002 : Step(99): len = 322736, overlap = 227.812
PHY-3002 : Step(100): len = 326532, overlap = 227.062
PHY-3002 : Step(101): len = 327667, overlap = 221.844
PHY-3002 : Step(102): len = 328694, overlap = 219.594
PHY-3002 : Step(103): len = 328726, overlap = 220.094
PHY-3002 : Step(104): len = 329575, overlap = 216.688
PHY-3002 : Step(105): len = 328001, overlap = 208.281
PHY-3002 : Step(106): len = 327875, overlap = 207.062
PHY-3002 : Step(107): len = 327911, overlap = 214.312
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000495718
PHY-3002 : Step(108): len = 329818, overlap = 210.188
PHY-3002 : Step(109): len = 334533, overlap = 206.938
PHY-3002 : Step(110): len = 336683, overlap = 194.594
PHY-3002 : Step(111): len = 339071, overlap = 186.344
PHY-3002 : Step(112): len = 339462, overlap = 185.406
PHY-3002 : Step(113): len = 339520, overlap = 181.094
PHY-3002 : Step(114): len = 340079, overlap = 178.719
PHY-3002 : Step(115): len = 340534, overlap = 175.469
PHY-3002 : Step(116): len = 341157, overlap = 181.594
PHY-3002 : Step(117): len = 340882, overlap = 193.188
PHY-3002 : Step(118): len = 341344, overlap = 181
PHY-3002 : Step(119): len = 341358, overlap = 176.938
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.015366s wall, 0.015625s user + 0.031250s system = 0.046875s CPU (305.1%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/22275.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 453960, over cnt = 1254(3%), over = 5400, worst = 43
PHY-1001 : End global iterations;  0.977424s wall, 1.265625s user + 0.046875s system = 1.312500s CPU (134.3%)

PHY-1001 : Congestion index: top1 = 72.78, top5 = 52.70, top10 = 43.56, top15 = 38.17.
PHY-3001 : End congestion estimation;  1.253257s wall, 1.531250s user + 0.062500s system = 1.593750s CPU (127.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22273 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.078794s wall, 1.062500s user + 0.015625s system = 1.078125s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.51369e-05
PHY-3002 : Step(120): len = 379386, overlap = 149.375
PHY-3002 : Step(121): len = 394349, overlap = 150.938
PHY-3002 : Step(122): len = 393389, overlap = 138.531
PHY-3002 : Step(123): len = 391898, overlap = 137.219
PHY-3002 : Step(124): len = 396401, overlap = 137.531
PHY-3002 : Step(125): len = 402408, overlap = 139.469
PHY-3002 : Step(126): len = 405745, overlap = 142.688
PHY-3002 : Step(127): len = 406057, overlap = 142.969
PHY-3002 : Step(128): len = 408420, overlap = 135.375
PHY-3002 : Step(129): len = 414048, overlap = 135.781
PHY-3002 : Step(130): len = 413998, overlap = 135.188
PHY-3002 : Step(131): len = 414752, overlap = 140.375
PHY-3002 : Step(132): len = 415611, overlap = 142.094
PHY-3002 : Step(133): len = 415373, overlap = 144.625
PHY-3002 : Step(134): len = 416108, overlap = 147.5
PHY-3002 : Step(135): len = 415434, overlap = 145.656
PHY-3002 : Step(136): len = 416179, overlap = 145.531
PHY-3002 : Step(137): len = 416148, overlap = 137.688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000190274
PHY-3002 : Step(138): len = 416026, overlap = 130.531
PHY-3002 : Step(139): len = 417562, overlap = 127.906
PHY-3002 : Step(140): len = 419863, overlap = 122.469
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000380548
PHY-3002 : Step(141): len = 425709, overlap = 118.281
PHY-3002 : Step(142): len = 432652, overlap = 117.656
PHY-3002 : Step(143): len = 434210, overlap = 114.906
PHY-3002 : Step(144): len = 435850, overlap = 109.625
PHY-3002 : Step(145): len = 438848, overlap = 109.344
PHY-3002 : Step(146): len = 441727, overlap = 106.062
PHY-3002 : Step(147): len = 442450, overlap = 104.719
PHY-3002 : Step(148): len = 443872, overlap = 111.875
PHY-3002 : Step(149): len = 444238, overlap = 109.781
PHY-3002 : Step(150): len = 443822, overlap = 109.656
PHY-3002 : Step(151): len = 443050, overlap = 108.5
PHY-3002 : Step(152): len = 443975, overlap = 107.281
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000761095
PHY-3002 : Step(153): len = 442705, overlap = 105.625
PHY-3002 : Step(154): len = 444273, overlap = 101.906
PHY-3002 : Step(155): len = 447703, overlap = 97.8438
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(156): len = 449771, overlap = 92.5312
PHY-3002 : Step(157): len = 459582, overlap = 83.875
PHY-3002 : Step(158): len = 468273, overlap = 83.2812
PHY-3002 : Step(159): len = 467598, overlap = 83.5938
PHY-3002 : Step(160): len = 468006, overlap = 85.3125
PHY-3002 : Step(161): len = 467020, overlap = 86.0312
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 50/22275.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 538312, over cnt = 2177(6%), over = 10360, worst = 39
PHY-1001 : End global iterations;  1.216827s wall, 1.859375s user + 0.015625s system = 1.875000s CPU (154.1%)

PHY-1001 : Congestion index: top1 = 78.08, top5 = 59.61, top10 = 51.18, top15 = 46.02.
PHY-3001 : End congestion estimation;  1.536647s wall, 2.156250s user + 0.015625s system = 2.171875s CPU (141.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22273 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.298889s wall, 1.281250s user + 0.000000s system = 1.281250s CPU (98.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000102508
PHY-3002 : Step(162): len = 475057, overlap = 319.875
PHY-3002 : Step(163): len = 479457, overlap = 283.656
PHY-3002 : Step(164): len = 472643, overlap = 270.719
PHY-3002 : Step(165): len = 469027, overlap = 252.969
PHY-3002 : Step(166): len = 465591, overlap = 247.125
PHY-3002 : Step(167): len = 460938, overlap = 242.688
PHY-3002 : Step(168): len = 460305, overlap = 250.812
PHY-3002 : Step(169): len = 456940, overlap = 237.781
PHY-3002 : Step(170): len = 453867, overlap = 226.531
PHY-3002 : Step(171): len = 452194, overlap = 219.25
PHY-3002 : Step(172): len = 448790, overlap = 205.125
PHY-3002 : Step(173): len = 448137, overlap = 200.062
PHY-3002 : Step(174): len = 448060, overlap = 200.906
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000205016
PHY-3002 : Step(175): len = 446299, overlap = 194.969
PHY-3002 : Step(176): len = 447786, overlap = 187.562
PHY-3002 : Step(177): len = 449106, overlap = 180.969
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000410033
PHY-3002 : Step(178): len = 450639, overlap = 183.844
PHY-3002 : Step(179): len = 455282, overlap = 166.969
PHY-3002 : Step(180): len = 460706, overlap = 158.75
PHY-3002 : Step(181): len = 463290, overlap = 157.438
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000820066
PHY-3002 : Step(182): len = 463351, overlap = 157.438
PHY-3002 : Step(183): len = 465671, overlap = 155.156
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82385, tnet num: 22273, tinst num: 19884, tnode num: 116628, tedge num: 129236.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.701148s wall, 1.687500s user + 0.015625s system = 1.703125s CPU (100.1%)

RUN-1004 : used memory is 573 MB, reserved memory is 548 MB, peak memory is 712 MB
OPT-1001 : Total overflow 538.66 peak overflow 4.56
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 440/22275.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 553136, over cnt = 2549(7%), over = 9131, worst = 23
PHY-1001 : End global iterations;  1.425867s wall, 2.218750s user + 0.062500s system = 2.281250s CPU (160.0%)

PHY-1001 : Congestion index: top1 = 62.80, top5 = 49.72, top10 = 44.09, top15 = 40.79.
PHY-1001 : End incremental global routing;  1.810571s wall, 2.609375s user + 0.062500s system = 2.671875s CPU (147.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22273 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.335713s wall, 1.328125s user + 0.000000s system = 1.328125s CPU (99.4%)

OPT-1001 : 17 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19804 has valid locations, 246 needs to be replaced
PHY-3001 : design contains 20113 instances, 5783 luts, 12814 seqs, 1423 slices, 285 macros(1423 instances: 929 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 483310
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 72%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17645/22504.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 566392, over cnt = 2556(7%), over = 9227, worst = 23
PHY-1001 : End global iterations;  0.221438s wall, 0.281250s user + 0.031250s system = 0.312500s CPU (141.1%)

PHY-1001 : Congestion index: top1 = 63.02, top5 = 49.98, top10 = 44.36, top15 = 41.16.
PHY-3001 : End congestion estimation;  0.524626s wall, 0.578125s user + 0.031250s system = 0.609375s CPU (116.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83138, tnet num: 22502, tinst num: 20113, tnode num: 117671, tedge num: 130284.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.689772s wall, 1.640625s user + 0.046875s system = 1.687500s CPU (99.9%)

RUN-1004 : used memory is 620 MB, reserved memory is 607 MB, peak memory is 713 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22502 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.861276s wall, 2.812500s user + 0.062500s system = 2.875000s CPU (100.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(184): len = 482531, overlap = 2
PHY-3002 : Step(185): len = 482569, overlap = 2.125
PHY-3002 : Step(186): len = 482919, overlap = 2.125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 72%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17712/22504.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 565048, over cnt = 2583(7%), over = 9289, worst = 23
PHY-1001 : End global iterations;  0.194005s wall, 0.265625s user + 0.046875s system = 0.312500s CPU (161.1%)

PHY-1001 : Congestion index: top1 = 63.28, top5 = 50.32, top10 = 44.59, top15 = 41.33.
PHY-3001 : End congestion estimation;  0.469149s wall, 0.546875s user + 0.046875s system = 0.593750s CPU (126.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22502 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.126745s wall, 1.109375s user + 0.015625s system = 1.125000s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000782475
PHY-3002 : Step(187): len = 483345, overlap = 157.094
PHY-3002 : Step(188): len = 484786, overlap = 156.656
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00156495
PHY-3002 : Step(189): len = 484919, overlap = 157.125
PHY-3002 : Step(190): len = 485338, overlap = 157
PHY-3001 : Final: Len = 485338, Over = 157
PHY-3001 : End incremental placement;  5.908639s wall, 6.500000s user + 0.343750s system = 6.843750s CPU (115.8%)

OPT-1001 : Total overflow 543.16 peak overflow 4.56
OPT-1001 : End high-fanout net optimization;  9.679744s wall, 11.296875s user + 0.406250s system = 11.703125s CPU (120.9%)

OPT-1001 : Current memory(MB): used = 717, reserve = 695, peak = 733.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17688/22504.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 567456, over cnt = 2556(7%), over = 8756, worst = 23
PHY-1002 : len = 604848, over cnt = 1971(5%), over = 5375, worst = 18
PHY-1002 : len = 636592, over cnt = 1164(3%), over = 3022, worst = 18
PHY-1002 : len = 670632, over cnt = 432(1%), over = 1004, worst = 18
PHY-1002 : len = 684176, over cnt = 47(0%), over = 114, worst = 12
PHY-1001 : End global iterations;  1.498613s wall, 2.171875s user + 0.046875s system = 2.218750s CPU (148.1%)

PHY-1001 : Congestion index: top1 = 52.20, top5 = 44.78, top10 = 41.46, top15 = 39.42.
OPT-1001 : End congestion update;  1.780602s wall, 2.453125s user + 0.046875s system = 2.500000s CPU (140.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22502 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.139013s wall, 1.140625s user + 0.000000s system = 1.140625s CPU (100.1%)

OPT-0007 : Start: WNS 4269 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.926661s wall, 3.593750s user + 0.046875s system = 3.640625s CPU (124.4%)

OPT-1001 : Current memory(MB): used = 692, reserve = 672, peak = 733.
OPT-1001 : End physical optimization;  14.652116s wall, 17.062500s user + 0.500000s system = 17.562500s CPU (119.9%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5783 LUT to BLE ...
SYN-4008 : Packed 5783 LUT and 2887 SEQ to BLE.
SYN-4003 : Packing 9927 remaining SEQ's ...
SYN-4005 : Packed 3296 SEQ with LUT/SLICE
SYN-4006 : 118 single LUT's are left
SYN-4006 : 6631 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 12414/14068 primitive instances ...
PHY-3001 : End packing;  3.315018s wall, 3.312500s user + 0.000000s system = 3.312500s CPU (99.9%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8312 instances
RUN-1001 : 4108 mslices, 4109 lslices, 60 pads, 30 brams, 0 dsps
RUN-1001 : There are total 19669 nets
RUN-1001 : 13770 nets have 2 pins
RUN-1001 : 4526 nets have [3 - 5] pins
RUN-1001 : 850 nets have [6 - 10] pins
RUN-1001 : 372 nets have [11 - 20] pins
RUN-1001 : 142 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8310 instances, 8217 slices, 285 macros(1423 instances: 929 mslices 494 lslices)
PHY-3001 : Cell area utilization is 87%
PHY-3001 : After packing: Len = 502222, Over = 363.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 8066/19669.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 644912, over cnt = 1578(4%), over = 2475, worst = 6
PHY-1002 : len = 651368, over cnt = 958(2%), over = 1320, worst = 6
PHY-1002 : len = 662304, over cnt = 339(0%), over = 428, worst = 4
PHY-1002 : len = 665688, over cnt = 194(0%), over = 242, worst = 4
PHY-1002 : len = 670440, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.292428s wall, 2.093750s user + 0.015625s system = 2.109375s CPU (163.2%)

PHY-1001 : Congestion index: top1 = 51.40, top5 = 44.48, top10 = 40.77, top15 = 38.50.
PHY-3001 : End congestion estimation;  1.664108s wall, 2.468750s user + 0.015625s system = 2.484375s CPU (149.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68453, tnet num: 19667, tinst num: 8310, tnode num: 93539, tedge num: 112614.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.907624s wall, 1.859375s user + 0.046875s system = 1.906250s CPU (99.9%)

RUN-1004 : used memory is 609 MB, reserved memory is 598 MB, peak memory is 733 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19667 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.005857s wall, 2.953125s user + 0.062500s system = 3.015625s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.95502e-05
PHY-3002 : Step(191): len = 507225, overlap = 346.75
PHY-3002 : Step(192): len = 505973, overlap = 362.5
PHY-3002 : Step(193): len = 507418, overlap = 373.25
PHY-3002 : Step(194): len = 508505, overlap = 389.75
PHY-3002 : Step(195): len = 507702, overlap = 402.5
PHY-3002 : Step(196): len = 508024, overlap = 410.5
PHY-3002 : Step(197): len = 505394, overlap = 412.5
PHY-3002 : Step(198): len = 504495, overlap = 412.25
PHY-3002 : Step(199): len = 501964, overlap = 410.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.91004e-05
PHY-3002 : Step(200): len = 505070, overlap = 403.75
PHY-3002 : Step(201): len = 508804, overlap = 390.25
PHY-3002 : Step(202): len = 509380, overlap = 386
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000198201
PHY-3002 : Step(203): len = 517154, overlap = 379.5
PHY-3002 : Step(204): len = 525746, overlap = 363.25
PHY-3002 : Step(205): len = 524295, overlap = 367.25
PHY-3002 : Step(206): len = 522614, overlap = 368.25
PHY-3002 : Step(207): len = 522379, overlap = 369.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.721785s wall, 0.890625s user + 0.875000s system = 1.765625s CPU (244.6%)

PHY-3001 : Trial Legalized: Len = 636814
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 639/19669.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 729488, over cnt = 2424(6%), over = 3870, worst = 7
PHY-1002 : len = 743344, over cnt = 1573(4%), over = 2199, worst = 7
PHY-1002 : len = 758496, over cnt = 779(2%), over = 1077, worst = 5
PHY-1002 : len = 771128, over cnt = 233(0%), over = 303, worst = 3
PHY-1002 : len = 776576, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.158637s wall, 3.250000s user + 0.000000s system = 3.250000s CPU (150.6%)

PHY-1001 : Congestion index: top1 = 50.62, top5 = 45.74, top10 = 43.19, top15 = 41.42.
PHY-3001 : End congestion estimation;  2.587537s wall, 3.687500s user + 0.000000s system = 3.687500s CPU (142.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19667 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.979275s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (98.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00018455
PHY-3002 : Step(208): len = 592356, overlap = 96.75
PHY-3002 : Step(209): len = 573573, overlap = 140
PHY-3002 : Step(210): len = 562026, overlap = 175.25
PHY-3002 : Step(211): len = 554374, overlap = 217.75
PHY-3002 : Step(212): len = 550117, overlap = 240.75
PHY-3002 : Step(213): len = 547427, overlap = 255
PHY-3002 : Step(214): len = 546655, overlap = 258
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000369099
PHY-3002 : Step(215): len = 549608, overlap = 253.75
PHY-3002 : Step(216): len = 553654, overlap = 251.5
PHY-3002 : Step(217): len = 556374, overlap = 248.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(218): len = 557802, overlap = 244.5
PHY-3002 : Step(219): len = 561412, overlap = 237.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.038454s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (121.9%)

PHY-3001 : Legalized: Len = 602818, Over = 0
PHY-3001 : Spreading special nets. 31 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.095980s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (97.7%)

PHY-3001 : 44 instances has been re-located, deltaX = 14, deltaY = 22, maxDist = 1.
PHY-3001 : Final: Len = 603260, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68453, tnet num: 19667, tinst num: 8310, tnode num: 93539, tedge num: 112614.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.189139s wall, 2.171875s user + 0.015625s system = 2.187500s CPU (99.9%)

RUN-1004 : used memory is 603 MB, reserved memory is 577 MB, peak memory is 733 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 4383/19669.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 704648, over cnt = 2232(6%), over = 3405, worst = 6
PHY-1002 : len = 716160, over cnt = 1221(3%), over = 1605, worst = 5
PHY-1002 : len = 729736, over cnt = 460(1%), over = 570, worst = 4
PHY-1002 : len = 739192, over cnt = 55(0%), over = 61, worst = 3
PHY-1002 : len = 740544, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.895731s wall, 2.875000s user + 0.062500s system = 2.937500s CPU (155.0%)

PHY-1001 : Congestion index: top1 = 48.49, top5 = 43.89, top10 = 41.24, top15 = 39.50.
PHY-1001 : End incremental global routing;  2.231775s wall, 3.218750s user + 0.062500s system = 3.281250s CPU (147.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19667 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.012814s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (98.7%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8246 has valid locations, 11 needs to be replaced
PHY-3001 : design contains 8320 instances, 8227 slices, 285 macros(1423 instances: 929 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 604944
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17732/19678.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 742216, over cnt = 17(0%), over = 18, worst = 2
PHY-1002 : len = 742168, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 742264, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 742280, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.575396s wall, 0.640625s user + 0.000000s system = 0.640625s CPU (111.3%)

PHY-1001 : Congestion index: top1 = 48.51, top5 = 43.94, top10 = 41.24, top15 = 39.52.
PHY-3001 : End congestion estimation;  0.921250s wall, 0.968750s user + 0.015625s system = 0.984375s CPU (106.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68512, tnet num: 19676, tinst num: 8320, tnode num: 93608, tedge num: 112682.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.304890s wall, 2.250000s user + 0.031250s system = 2.281250s CPU (99.0%)

RUN-1004 : used memory is 651 MB, reserved memory is 638 MB, peak memory is 733 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19676 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.388572s wall, 3.296875s user + 0.078125s system = 3.375000s CPU (99.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(220): len = 605023, overlap = 0.5
PHY-3002 : Step(221): len = 605014, overlap = 0.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 87%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17729/19678.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 741864, over cnt = 20(0%), over = 22, worst = 2
PHY-1002 : len = 741808, over cnt = 14(0%), over = 14, worst = 1
PHY-1002 : len = 741944, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 742008, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 742024, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.771867s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (101.2%)

PHY-1001 : Congestion index: top1 = 48.41, top5 = 43.90, top10 = 41.27, top15 = 39.52.
PHY-3001 : End congestion estimation;  1.101800s wall, 1.109375s user + 0.000000s system = 1.109375s CPU (100.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19676 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.010780s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (100.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000614148
PHY-3002 : Step(222): len = 604963, overlap = 1.75
PHY-3002 : Step(223): len = 605004, overlap = 2
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007028s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (222.3%)

PHY-3001 : Legalized: Len = 605064, Over = 0
PHY-3001 : End spreading;  0.075468s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (103.5%)

PHY-3001 : Final: Len = 605064, Over = 0
PHY-3001 : End incremental placement;  7.100510s wall, 7.046875s user + 0.171875s system = 7.218750s CPU (101.7%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.906232s wall, 12.000000s user + 0.234375s system = 12.234375s CPU (112.2%)

OPT-1001 : Current memory(MB): used = 725, reserve = 709, peak = 733.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17729/19678.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 741872, over cnt = 9(0%), over = 10, worst = 2
PHY-1002 : len = 741856, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 741896, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.427661s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (98.6%)

PHY-1001 : Congestion index: top1 = 48.41, top5 = 43.90, top10 = 41.21, top15 = 39.49.
OPT-1001 : End congestion update;  0.754784s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (99.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19676 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.855615s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (100.4%)

OPT-0007 : Start: WNS 4087 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.616070s wall, 1.609375s user + 0.000000s system = 1.609375s CPU (99.6%)

OPT-1001 : Current memory(MB): used = 725, reserve = 709, peak = 733.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19676 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.839931s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (100.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17742/19678.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 741896, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.130978s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (107.4%)

PHY-1001 : Congestion index: top1 = 48.41, top5 = 43.90, top10 = 41.21, top15 = 39.49.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19676 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.850128s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (99.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4087 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 47.896552
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4087ps with logic level 4 
OPT-1001 : End physical optimization;  17.142909s wall, 18.375000s user + 0.265625s system = 18.640625s CPU (108.7%)

RUN-1003 : finish command "place" in  78.045794s wall, 147.140625s user + 8.921875s system = 156.062500s CPU (200.0%)

RUN-1004 : used memory is 606 MB, reserved memory is 583 MB, peak memory is 733 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.773986s wall, 3.062500s user + 0.015625s system = 3.078125s CPU (173.5%)

RUN-1004 : used memory is 607 MB, reserved memory is 584 MB, peak memory is 733 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8322 instances
RUN-1001 : 4118 mslices, 4109 lslices, 60 pads, 30 brams, 0 dsps
RUN-1001 : There are total 19678 nets
RUN-1001 : 13770 nets have 2 pins
RUN-1001 : 4528 nets have [3 - 5] pins
RUN-1001 : 853 nets have [6 - 10] pins
RUN-1001 : 375 nets have [11 - 20] pins
RUN-1001 : 143 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68512, tnet num: 19676, tinst num: 8320, tnode num: 93608, tedge num: 112682.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.874735s wall, 1.859375s user + 0.015625s system = 1.875000s CPU (100.0%)

RUN-1004 : used memory is 623 MB, reserved memory is 624 MB, peak memory is 733 MB
PHY-1001 : 4118 mslices, 4109 lslices, 60 pads, 30 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19676 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 682448, over cnt = 2324(6%), over = 3714, worst = 7
PHY-1002 : len = 697816, over cnt = 1377(3%), over = 1920, worst = 6
PHY-1002 : len = 712928, over cnt = 663(1%), over = 865, worst = 4
PHY-1002 : len = 727168, over cnt = 28(0%), over = 31, worst = 3
PHY-1002 : len = 727824, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.986898s wall, 3.265625s user + 0.031250s system = 3.296875s CPU (165.9%)

PHY-1001 : Congestion index: top1 = 48.97, top5 = 43.73, top10 = 40.92, top15 = 39.18.
PHY-1001 : End global routing;  2.363293s wall, 3.625000s user + 0.031250s system = 3.656250s CPU (154.7%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 710, reserve = 700, peak = 733.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 978, reserve = 967, peak = 978.
PHY-1001 : End build detailed router design. 4.999348s wall, 4.906250s user + 0.078125s system = 4.984375s CPU (99.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 194024, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.986856s wall, 0.968750s user + 0.015625s system = 0.984375s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 1015, reserve = 1004, peak = 1015.
PHY-1001 : End phase 1; 0.994307s wall, 0.968750s user + 0.015625s system = 0.984375s CPU (99.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.7403e+06, over cnt = 1367(0%), over = 1373, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1030, reserve = 1017, peak = 1030.
PHY-1001 : End initial routed; 18.329063s wall, 47.234375s user + 0.453125s system = 47.687500s CPU (260.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18478(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.168   |   0.000   |   0   
RUN-1001 :   Hold   |   0.172   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.864194s wall, 3.859375s user + 0.015625s system = 3.875000s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 1041, reserve = 1028, peak = 1041.
PHY-1001 : End phase 2; 22.193435s wall, 51.093750s user + 0.468750s system = 51.562500s CPU (232.3%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.7403e+06, over cnt = 1367(0%), over = 1373, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.283695s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (104.6%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.73037e+06, over cnt = 479(0%), over = 481, worst = 2, crit = 0
PHY-1001 : End DR Iter 1; 0.845934s wall, 1.375000s user + 0.000000s system = 1.375000s CPU (162.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.7305e+06, over cnt = 113(0%), over = 113, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.418664s wall, 0.656250s user + 0.000000s system = 0.656250s CPU (156.7%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.73222e+06, over cnt = 13(0%), over = 13, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.336809s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (116.0%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.73244e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.217442s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (107.8%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.73254e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.176819s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (97.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18478(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.978   |   0.000   |   0   
RUN-1001 :   Hold   |   0.172   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.907492s wall, 3.906250s user + 0.000000s system = 3.906250s CPU (100.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 292 feed throughs used by 262 nets
PHY-1001 : End commit to database; 2.374033s wall, 2.359375s user + 0.015625s system = 2.375000s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1130, reserve = 1120, peak = 1130.
PHY-1001 : End phase 3; 9.156686s wall, 10.000000s user + 0.015625s system = 10.015625s CPU (109.4%)

PHY-1003 : Routed, final wirelength = 1.73254e+06
PHY-1001 : Current memory(MB): used = 1134, reserve = 1125, peak = 1134.
PHY-1001 : End export database. 0.185641s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (101.0%)

PHY-1001 : End detail routing;  37.986420s wall, 67.625000s user + 0.578125s system = 68.203125s CPU (179.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68512, tnet num: 19676, tinst num: 8320, tnode num: 93608, tedge num: 112682.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.846019s wall, 1.843750s user + 0.000000s system = 1.843750s CPU (99.9%)

RUN-1004 : used memory is 1066 MB, reserved memory is 1062 MB, peak memory is 1134 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  47.007060s wall, 77.859375s user + 0.625000s system = 78.484375s CPU (167.0%)

RUN-1004 : used memory is 1068 MB, reserved memory is 1066 MB, peak memory is 1134 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8770   out of  19600   44.74%
#reg                    12910   out of  19600   65.87%
#le                     15364
  #lut only              2454   out of  15364   15.97%
  #reg only              6594   out of  15364   42.92%
  #lut&reg               6316   out of  15364   41.11%
#dsp                        0   out of     29    0.00%
#bram                      30   out of     64   46.88%
  #bram9k                  30
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                    10
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    7088
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          142
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         M3        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         P6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15364  |7347    |1423    |12953   |30      |0       |
|  AnyFog_dataX                      |AnyFog          |227    |93      |22      |181     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |107    |76      |22      |61      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |207    |79      |22      |170     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |85     |59      |22      |48      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |213    |107     |22      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |93     |67      |22      |53      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |3468   |861     |34      |3390    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |729    |76      |5       |717     |0       |0       |
|    STADOP_com2                     |STADOP          |557    |47      |0       |551     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |42      |14      |43      |0       |0       |
|    head_com2                       |uniheading      |265    |71      |5       |252     |0       |0       |
|    uart_com2                       |Agrica          |1561   |334     |10      |1536    |0       |0       |
|  COM3                              |COM3_Control    |214    |114     |14      |182     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |60     |43      |14      |36      |0       |0       |
|    rmc_com3                        |Gprmc           |154    |71      |0       |146     |0       |0       |
|  DATA                              |Data_Processing |8731   |4421    |1059    |7056    |0       |0       |
|    DIV_Dtemp                       |Divider         |792    |351     |84      |663     |0       |0       |
|    DIV_Utemp                       |Divider         |649    |309     |84      |526     |0       |0       |
|    DIV_accX                        |Divider         |560    |310     |84      |435     |0       |0       |
|    DIV_accY                        |Divider         |657    |396     |108     |493     |0       |0       |
|    DIV_accZ                        |Divider         |691    |348     |132     |486     |0       |0       |
|    DIV_rateX                       |Divider         |663    |397     |132     |459     |0       |0       |
|    DIV_rateY                       |Divider         |587    |372     |132     |383     |0       |0       |
|    DIV_rateZ                       |Divider         |561    |327     |132     |357     |0       |0       |
|    genclk                          |genclk          |91     |52      |20      |58      |0       |0       |
|  FMC                               |FMC_Ctrl        |444    |394     |43      |340     |0       |0       |
|  IIC                               |I2C_master      |273    |229     |11      |242     |0       |0       |
|  IMU_CTRL                          |SCHA634         |925    |683     |61      |749     |0       |0       |
|    CtrlData                        |CtrlData        |464    |409     |47      |339     |0       |0       |
|      usms                          |Time_1ms        |28     |22      |5       |20      |0       |0       |
|    SPIM                            |SPI_SCHA634     |461    |274     |14      |410     |0       |0       |
|  POWER                             |POWER_EN        |98     |52      |38      |38      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |546    |312     |97      |371     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |546    |312     |97      |371     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |237    |115     |0       |226     |0       |0       |
|        reg_inst                    |register        |234    |112     |0       |223     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |309    |197     |97      |145     |0       |0       |
|        bus_inst                    |bus_top         |109    |69      |40      |43      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |24     |14      |10      |7       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |52     |34      |18      |19      |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |15     |9       |6       |7       |0       |0       |
|          BUS_DETECTOR[5]$bus_nodes |bus_det         |16     |10      |6       |8       |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |118    |84      |29      |69      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13709  
    #2          2       3659   
    #3          3        628   
    #4          4        241   
    #5        5-10       900   
    #6        11-50      461   
    #7       51-100      10    
    #8       101-500      3    
    #9        >500        2    
  Average     2.12             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.290542s wall, 3.812500s user + 0.015625s system = 3.828125s CPU (167.1%)

RUN-1004 : used memory is 1069 MB, reserved memory is 1067 MB, peak memory is 1134 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68512, tnet num: 19676, tinst num: 8320, tnode num: 93608, tedge num: 112682.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.887429s wall, 1.890625s user + 0.000000s system = 1.890625s CPU (100.2%)

RUN-1004 : used memory is 1070 MB, reserved memory is 1069 MB, peak memory is 1134 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19676 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.562510s wall, 1.562500s user + 0.000000s system = 1.562500s CPU (100.0%)

RUN-1004 : used memory is 1076 MB, reserved memory is 1072 MB, peak memory is 1134 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: b13ad751db434fd7585008d888affb669c51ea72e1b5a539d7379cfb76cca064 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8320
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19678, pip num: 148680
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 292
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3254 valid insts, and 414785 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101001000100100000001111
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  11.802335s wall, 115.968750s user + 0.109375s system = 116.078125s CPU (983.5%)

RUN-1004 : used memory is 1197 MB, reserved memory is 1182 MB, peak memory is 1312 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250701_142505.log"

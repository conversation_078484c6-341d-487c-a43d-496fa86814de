============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Aug 28 13:23:37 2025

   Run on =     TLH-001
============================================================
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/INS600M-21A-SCH634_UM982-new20250711/INS600M-21A_Runs/phy_1/INS600M-21A_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 323 feed throughs used by 275 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import ChipWatcher
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/INS600M-21A-SCH634_UM982-new20250711/INS600M-21A_Runs/phy_1/INS600M-21A_pr.db" in  11.408769s wall, 11.593750s user + 2.093750s system = 13.687500s CPU (120.0%)

RUN-1004 : used memory is 862 MB, reserved memory is 857 MB, peak memory is 894 MB
HDL-1007 : analyze verilog file Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'FMC_Gx_Data', assumed default net type 'wire' in Src/INS600M-21A.v(109)
HDL-1007 : undeclared symbol 'FMC_Gy_Data', assumed default net type 'wire' in Src/INS600M-21A.v(110)
HDL-1007 : undeclared symbol 'FMC_Gz_Data', assumed default net type 'wire' in Src/INS600M-21A.v(111)
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |     0      |       auto       |   *    
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file Src/FMC/genclk.v
HDL-1007 : analyze verilog file Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'FMC_Gx_Data', assumed default net type 'wire' in Src/INS600M-21A.v(109)
HDL-1007 : undeclared symbol 'FMC_Gy_Data', assumed default net type 'wire' in Src/INS600M-21A.v(110)
HDL-1007 : undeclared symbol 'FMC_Gz_Data', assumed default net type 'wire' in Src/INS600M-21A.v(111)
HDL-1007 : analyze verilog file Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file Src/GNSS/GNRMC_Tx.v
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |     0      |       auto       |   *    
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'FMC_Gx_Data', assumed default net type 'wire' in Src/INS600M-21A.v(109)
HDL-1007 : undeclared symbol 'FMC_Gy_Data', assumed default net type 'wire' in Src/INS600M-21A.v(110)
HDL-1007 : undeclared symbol 'FMC_Gz_Data', assumed default net type 'wire' in Src/INS600M-21A.v(111)
HDL-1007 : analyze verilog file Src/SPI/Time_1ms.v
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |     0      |       auto       |   *    
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'FMC_Gx_Data', assumed default net type 'wire' in Src/INS600M-21A.v(109)
HDL-1007 : undeclared symbol 'FMC_Gy_Data', assumed default net type 'wire' in Src/INS600M-21A.v(110)
HDL-1007 : undeclared symbol 'FMC_Gz_Data', assumed default net type 'wire' in Src/INS600M-21A.v(111)
HDL-1007 : analyze verilog file Src/SPI/Time_1ms.v
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |     0      |       auto       |   *    
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'FMC_Gx_Data', assumed default net type 'wire' in Src/INS600M-21A.v(109)
HDL-1007 : undeclared symbol 'FMC_Gy_Data', assumed default net type 'wire' in Src/INS600M-21A.v(110)
HDL-1007 : undeclared symbol 'FMC_Gz_Data', assumed default net type 'wire' in Src/INS600M-21A.v(111)
HDL-1007 : analyze verilog file Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'gnss_timestamp_usec', assumed default net type 'wire' in Src/INS600M-21A.v(113)
HDL-1007 : undeclared symbol 'gnss_latitude_deg_1e8', assumed default net type 'wire' in Src/INS600M-21A.v(114)
HDL-1007 : undeclared symbol 'gnss_longitude_deg_1e8', assumed default net type 'wire' in Src/INS600M-21A.v(115)
HDL-1007 : undeclared symbol 'gnss_height_ellipsoid_mm', assumed default net type 'wire' in Src/INS600M-21A.v(116)
HDL-1007 : undeclared symbol 'gnss_height_msl_mm', assumed default net type 'wire' in Src/INS600M-21A.v(117)
HDL-1007 : undeclared symbol 'gnss_velocity_north_m_s', assumed default net type 'wire' in Src/INS600M-21A.v(118)
HDL-1007 : undeclared symbol 'gnss_velocity_east_m_s', assumed default net type 'wire' in Src/INS600M-21A.v(119)
HDL-1007 : undeclared symbol 'gnss_velocity_down_m_s', assumed default net type 'wire' in Src/INS600M-21A.v(120)
HDL-1007 : undeclared symbol 'gnss_velocity_accuracy_m_s', assumed default net type 'wire' in Src/INS600M-21A.v(121)
HDL-1007 : undeclared symbol 'gnss_course_over_ground', assumed default net type 'wire' in Src/INS600M-21A.v(122)
HDL-1007 : undeclared symbol 'gnss_ground_speed_m_s', assumed default net type 'wire' in Src/INS600M-21A.v(123)
HDL-1007 : undeclared symbol 'gnss_position_accuracy_mm', assumed default net type 'wire' in Src/INS600M-21A.v(124)
HDL-1007 : undeclared symbol 'gnss_hdop', assumed default net type 'wire' in Src/INS600M-21A.v(125)
HDL-1007 : undeclared symbol 'gnss_vdop', assumed default net type 'wire' in Src/INS600M-21A.v(126)
HDL-1007 : undeclared symbol 'gnss_sats_used', assumed default net type 'wire' in Src/INS600M-21A.v(127)
HDL-1007 : undeclared symbol 'gnss_sats_visible', assumed default net type 'wire' in Src/INS600M-21A.v(128)
HDL-1007 : undeclared symbol 'gnss_fix_type', assumed default net type 'wire' in Src/INS600M-21A.v(129)
HDL-1007 : undeclared symbol 'gnss_status', assumed default net type 'wire' in Src/INS600M-21A.v(130)
HDL-1007 : undeclared symbol 'gnss_valid', assumed default net type 'wire' in Src/INS600M-21A.v(131)
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |     0      |       auto       |   *    
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'gnss_timestamp_usec', assumed default net type 'wire' in Src/INS600M-21A.v(113)
HDL-1007 : undeclared symbol 'gnss_latitude_deg_1e8', assumed default net type 'wire' in Src/INS600M-21A.v(114)
HDL-1007 : undeclared symbol 'gnss_longitude_deg_1e8', assumed default net type 'wire' in Src/INS600M-21A.v(115)
HDL-1007 : undeclared symbol 'gnss_height_ellipsoid_mm', assumed default net type 'wire' in Src/INS600M-21A.v(116)
HDL-1007 : undeclared symbol 'gnss_height_msl_mm', assumed default net type 'wire' in Src/INS600M-21A.v(117)
HDL-1007 : undeclared symbol 'gnss_velocity_north_m_s', assumed default net type 'wire' in Src/INS600M-21A.v(118)
HDL-1007 : undeclared symbol 'gnss_velocity_east_m_s', assumed default net type 'wire' in Src/INS600M-21A.v(119)
HDL-1007 : undeclared symbol 'gnss_velocity_down_m_s', assumed default net type 'wire' in Src/INS600M-21A.v(120)
HDL-1007 : undeclared symbol 'gnss_velocity_accuracy_m_s', assumed default net type 'wire' in Src/INS600M-21A.v(121)
HDL-1007 : undeclared symbol 'gnss_course_over_ground', assumed default net type 'wire' in Src/INS600M-21A.v(122)
HDL-1007 : undeclared symbol 'gnss_ground_speed_m_s', assumed default net type 'wire' in Src/INS600M-21A.v(123)
HDL-1007 : undeclared symbol 'gnss_position_accuracy_mm', assumed default net type 'wire' in Src/INS600M-21A.v(124)
HDL-1007 : undeclared symbol 'gnss_hdop', assumed default net type 'wire' in Src/INS600M-21A.v(125)
HDL-1007 : undeclared symbol 'gnss_vdop', assumed default net type 'wire' in Src/INS600M-21A.v(126)
HDL-1007 : undeclared symbol 'gnss_sats_used', assumed default net type 'wire' in Src/INS600M-21A.v(127)
HDL-1007 : undeclared symbol 'gnss_sats_visible', assumed default net type 'wire' in Src/INS600M-21A.v(128)
HDL-1007 : undeclared symbol 'gnss_fix_type', assumed default net type 'wire' in Src/INS600M-21A.v(129)
HDL-1007 : undeclared symbol 'gnss_status', assumed default net type 'wire' in Src/INS600M-21A.v(130)
HDL-1007 : undeclared symbol 'gnss_valid', assumed default net type 'wire' in Src/INS600M-21A.v(131)
HDL-1007 : analyze verilog file Src/DroneCAN/dronecan_protocol_parser.v
HDL-8007 ERROR: cannot find port 'Dronecan_Rx' on this module in Src/INS600M-21A.v(110)
HDL-8007 ERROR: cannot find port 'Dronecan_Tx' on this module in Src/INS600M-21A.v(111)
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |     0      |       auto       |   *    
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'gnss_timestamp_usec', assumed default net type 'wire' in Src/INS600M-21A.v(113)
HDL-1007 : undeclared symbol 'gnss_latitude_deg_1e8', assumed default net type 'wire' in Src/INS600M-21A.v(114)
HDL-1007 : undeclared symbol 'gnss_longitude_deg_1e8', assumed default net type 'wire' in Src/INS600M-21A.v(115)
HDL-1007 : undeclared symbol 'gnss_height_ellipsoid_mm', assumed default net type 'wire' in Src/INS600M-21A.v(116)
HDL-1007 : undeclared symbol 'gnss_height_msl_mm', assumed default net type 'wire' in Src/INS600M-21A.v(117)
HDL-1007 : undeclared symbol 'gnss_velocity_north_m_s', assumed default net type 'wire' in Src/INS600M-21A.v(118)
HDL-1007 : undeclared symbol 'gnss_velocity_east_m_s', assumed default net type 'wire' in Src/INS600M-21A.v(119)
HDL-1007 : undeclared symbol 'gnss_velocity_down_m_s', assumed default net type 'wire' in Src/INS600M-21A.v(120)
HDL-1007 : undeclared symbol 'gnss_velocity_accuracy_m_s', assumed default net type 'wire' in Src/INS600M-21A.v(121)
HDL-1007 : undeclared symbol 'gnss_course_over_ground', assumed default net type 'wire' in Src/INS600M-21A.v(122)
HDL-1007 : undeclared symbol 'gnss_ground_speed_m_s', assumed default net type 'wire' in Src/INS600M-21A.v(123)
HDL-1007 : undeclared symbol 'gnss_position_accuracy_mm', assumed default net type 'wire' in Src/INS600M-21A.v(124)
HDL-1007 : undeclared symbol 'gnss_hdop', assumed default net type 'wire' in Src/INS600M-21A.v(125)
HDL-1007 : undeclared symbol 'gnss_vdop', assumed default net type 'wire' in Src/INS600M-21A.v(126)
HDL-1007 : undeclared symbol 'gnss_sats_used', assumed default net type 'wire' in Src/INS600M-21A.v(127)
HDL-1007 : undeclared symbol 'gnss_sats_visible', assumed default net type 'wire' in Src/INS600M-21A.v(128)
HDL-1007 : undeclared symbol 'gnss_fix_type', assumed default net type 'wire' in Src/INS600M-21A.v(129)
HDL-1007 : undeclared symbol 'gnss_status', assumed default net type 'wire' in Src/INS600M-21A.v(130)
HDL-1007 : undeclared symbol 'gnss_valid', assumed default net type 'wire' in Src/INS600M-21A.v(131)
HDL-1007 : analyze verilog file Src/DroneCAN/dronecan_protocol_parser.v
RUN-1001 : reset_run syn_1 phy_1.
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-6001 WARNING: syn_1: run failed.
HDL-1007 : analyze verilog file Src/INS600M-21A.v
RUN-1001 : reset_run syn_1 phy_1.
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-6001 WARNING: syn_1: run failed.
HDL-1007 : analyze verilog file Src/DroneCAN/dronecan_protocol_parser.v
RUN-1001 : reset_run syn_1 phy_1.
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-6001 WARNING: syn_1: run failed.
HDL-1007 : analyze verilog file Src/DroneCAN/dronecan_protocol_parser.v
RUN-1001 : reset_run syn_1 phy_1.
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-6001 WARNING: syn_1: run failed.
HDL-1007 : analyze verilog file Src/DroneCAN/dronecan_protocol_parser.v
RUN-1001 : reset_run syn_1 phy_1.
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-6001 WARNING: syn_1: run failed.
HDL-1007 : analyze verilog file Src/DroneCAN/dronecan_protocol_parser.v
HDL-8007 ERROR: syntax error near '[' in Src/DroneCAN/dronecan_protocol_parser.v(188)
HDL-8007 ERROR: syntax error near 'endfunction' in Src/DroneCAN/dronecan_protocol_parser.v(231)
HDL-8007 ERROR: Verilog 2000 keyword 'endfunction' used in incorrect context in Src/DroneCAN/dronecan_protocol_parser.v(231)
HDL-8007 ERROR: 'decode_node_status' is already declared in Src/DroneCAN/dronecan_protocol_parser.v(584)
HDL-1007 : previous declaration of 'decode_node_status' is from here in Src/DroneCAN/dronecan_protocol_parser.v(570)
HDL-8007 ERROR: syntax error near '[' in Src/DroneCAN/dronecan_protocol_parser.v(626)
HDL-8007 ERROR: syntax error near 'endmodule' in Src/DroneCAN/dronecan_protocol_parser.v(655)
HDL-8007 ERROR: Verilog 2000 keyword 'endmodule' used in incorrect context in Src/DroneCAN/dronecan_protocol_parser.v(655)
HDL-8007 ERROR: syntax error near 'endtask' in Src/DroneCAN/dronecan_protocol_parser.v(670)
HDL-8007 ERROR: Verilog 2000 keyword 'endtask' used in incorrect context in Src/DroneCAN/dronecan_protocol_parser.v(670)
HDL-8007 ERROR: syntax error near 'endtask' in Src/DroneCAN/dronecan_protocol_parser.v(698)
HDL-8007 ERROR: Verilog 2000 keyword 'endtask' used in incorrect context in Src/DroneCAN/dronecan_protocol_parser.v(698)
HDL-8007 ERROR: syntax error near ',' in Src/DroneCAN/dronecan_protocol_parser.v(710)
HDL-8007 ERROR: syntax error near ',' in Src/DroneCAN/dronecan_protocol_parser.v(711)
HDL-8007 ERROR: syntax error near '}' in Src/DroneCAN/dronecan_protocol_parser.v(711)
HDL-5007 WARNING: empty statement in sequential block in Src/DroneCAN/dronecan_protocol_parser.v(602)
HDL-5007 WARNING: 'bit_counter' is not declared in Src/DroneCAN/dronecan_protocol_parser.v(277)
HDL-5007 WARNING: 'bit_sample' is not declared in Src/DroneCAN/dronecan_protocol_parser.v(278)
HDL-5007 WARNING: 'bit_counter' is not declared in Src/DroneCAN/dronecan_protocol_parser.v(281)
HDL-5007 WARNING: 'bit_counter' is not declared in Src/DroneCAN/dronecan_protocol_parser.v(282)
HDL-5007 WARNING: 'bit_sample' is not declared in Src/DroneCAN/dronecan_protocol_parser.v(283)
HDL-5007 WARNING: 'bit_counter' is not declared in Src/DroneCAN/dronecan_protocol_parser.v(285)
HDL-5007 WARNING: 'bit_counter' is not declared in Src/DroneCAN/dronecan_protocol_parser.v(285)
HDL-5007 WARNING: 'bit_sample' is not declared in Src/DroneCAN/dronecan_protocol_parser.v(286)
HDL-5007 WARNING: 'bit_counter' is not declared in Src/DroneCAN/dronecan_protocol_parser.v(286)
HDL-5007 Similar messages will be suppressed.
HDL-8007 ERROR: 'validity_timer' is not a task in Src/DroneCAN/dronecan_protocol_parser.v(626)
HDL-8007 ERROR: procedural assignment to a non-register 'can_tx' is not permitted in Src/DroneCAN/dronecan_protocol_parser.v(653)
HDL-5007 WARNING: 'transfer_buffer' is not declared in Src/DroneCAN/dronecan_protocol_parser.v(715)
HDL-5007 WARNING: 'transfer_buffer' is not declared in Src/DroneCAN/dronecan_protocol_parser.v(715)
HDL-5007 WARNING: 'transfer_buffer' is not declared in Src/DroneCAN/dronecan_protocol_parser.v(716)
HDL-5007 WARNING: 'transfer_buffer' is not declared in Src/DroneCAN/dronecan_protocol_parser.v(716)
HDL-5007 WARNING: 'transfer_buffer' is not declared in Src/DroneCAN/dronecan_protocol_parser.v(720)
HDL-5007 WARNING: 'transfer_buffer' is not declared in Src/DroneCAN/dronecan_protocol_parser.v(720)
HDL-5007 WARNING: 'transfer_buffer' is not declared in Src/DroneCAN/dronecan_protocol_parser.v(721)
HDL-5007 WARNING: 'transfer_buffer' is not declared in Src/DroneCAN/dronecan_protocol_parser.v(721)
HDL-5007 WARNING: 'transfer_buffer' is not declared in Src/DroneCAN/dronecan_protocol_parser.v(725)
HDL-5007 WARNING: 'transfer_buffer' is not declared in Src/DroneCAN/dronecan_protocol_parser.v(725)
HDL-5007 Similar messages will be suppressed.
HDL-8007 ERROR: ignore module module due to previous errors in Src/DroneCAN/dronecan_protocol_parser.v(17)
HDL-1007 : Verilog file 'Src/DroneCAN/dronecan_protocol_parser.v' ignored due to errors

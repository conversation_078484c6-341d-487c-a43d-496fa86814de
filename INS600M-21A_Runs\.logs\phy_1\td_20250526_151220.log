============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Mon May 26 15:12:20 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(247)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(252)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(273)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(278)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(421)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(428)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(435)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(442)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(449)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(456)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(463)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(470)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(647)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(652)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(680)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(685)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(919)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(926)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(933)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(940)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(947)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(952)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(959)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(966)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(973)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(980)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(514)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
RUN-1001 : Project manager successfully analyzed 21 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.358797s wall, 1.312500s user + 4.031250s system = 5.343750s CPU (99.7%)

RUN-1004 : used memory is 78 MB, reserved memory is 39 MB, peak memory is 89 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.763704s wall, 1.734375s user + 0.031250s system = 1.765625s CPU (100.1%)

RUN-1004 : used memory is 298 MB, reserved memory is 267 MB, peak memory is 301 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 7 view nodes, 47 trigger nets, 47 data nets.
KIT-1004 : Chipwatcher code = 0001111110010110
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=144) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=144) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=144)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=144)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22885/31 useful/useless nets, 19668/17 useful/useless insts
SYN-1016 : Merged 37 instances.
SYN-1032 : 22509/22 useful/useless nets, 20127/18 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 4 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 4 mux instances.
SYN-1015 : Optimize round 1, 443 better
SYN-1014 : Optimize round 2
SYN-1032 : 22137/60 useful/useless nets, 19755/64 useful/useless insts
SYN-1015 : Optimize round 2, 128 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.232219s wall, 2.171875s user + 0.046875s system = 2.218750s CPU (99.4%)

RUN-1004 : used memory is 325 MB, reserved memory is 293 MB, peak memory is 327 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22197/367 useful/useless nets, 19856/52 useful/useless insts
SYN-1016 : Merged 57 instances.
SYN-2571 : Optimize after map_dsp, round 1, 476 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 44 instances.
SYN-2501 : Optimize round 1, 90 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 18 macro adder
SYN-1019 : Optimized 17 mux instances.
SYN-1016 : Merged 12 instances.
SYN-1032 : 22668/5 useful/useless nets, 20327/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83218, tnet num: 22668, tinst num: 20326, tnode num: 116913, tedge num: 129593.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.136813s wall, 1.109375s user + 0.031250s system = 1.140625s CPU (100.3%)

RUN-1004 : used memory is 468 MB, reserved memory is 436 MB, peak memory is 468 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22668 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 257 (3.41), #lev = 7 (1.78)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 257 (3.41), #lev = 7 (1.78)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 596 instances into 257 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 450 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 140 adder to BLE ...
SYN-4008 : Packed 140 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.369308s wall, 4.296875s user + 0.093750s system = 4.390625s CPU (100.5%)

RUN-1004 : used memory is 350 MB, reserved memory is 327 MB, peak memory is 578 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  6.915790s wall, 6.765625s user + 0.156250s system = 6.921875s CPU (100.1%)

RUN-1004 : used memory is 350 MB, reserved memory is 327 MB, peak memory is 578 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/rmc_com2/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/rmc_com2/GPRMC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/rmc_com2/GPRMC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/rmc_com2/GPRMC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/rmc_com2/GPRMC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/rmc_com2/GPRMC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/rmc_com2/GPRMC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/rmc_com2/GPRMC_data[0]
SYN-5055 WARNING: The kept net COM2/rx_err will be merged to another kept net COM2/rmc_com2/GPRMC_err
SYN-5055 WARNING: The kept net FMC/Error_state[0] will be merged to another kept net COM2/rmc_com2/GPRMC_sub_state_cnt0[3]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (306 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19654 instances
RUN-0007 : 5630 luts, 12497 seqs, 933 mslices, 491 lslices, 56 pads, 42 brams, 0 dsps
RUN-1001 : There are total 22019 nets
RUN-1001 : 16502 nets have 2 pins
RUN-1001 : 4375 nets have [3 - 5] pins
RUN-1001 : 777 nets have [6 - 10] pins
RUN-1001 : 240 nets have [11 - 20] pins
RUN-1001 : 101 nets have [21 - 99] pins
RUN-1001 : 24 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4743     
RUN-1001 :   No   |  No   |  Yes  |     628     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    6556     
RUN-1001 :   Yes  |  No   |  Yes  |     470     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  118  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 126
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19652 instances, 5630 luts, 12497 seqs, 1424 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1131 pins
PHY-3001 : Huge net DATA/done_div with 1644 pins
PHY-0007 : Cell area utilization is 64%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81749, tnet num: 22017, tinst num: 19652, tnode num: 115542, tedge num: 128404.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.153549s wall, 1.109375s user + 0.031250s system = 1.140625s CPU (98.9%)

RUN-1004 : used memory is 528 MB, reserved memory is 500 MB, peak memory is 578 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22017 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.984256s wall, 1.906250s user + 0.062500s system = 1.968750s CPU (99.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.47669e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19652.
PHY-3001 : Level 1 #clusters 2081.
PHY-3001 : End clustering;  0.243992s wall, 0.375000s user + 0.031250s system = 0.406250s CPU (166.5%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 64%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 881496, overlap = 658.594
PHY-3002 : Step(2): len = 814631, overlap = 714.531
PHY-3002 : Step(3): len = 516524, overlap = 868.625
PHY-3002 : Step(4): len = 455422, overlap = 950.656
PHY-3002 : Step(5): len = 367194, overlap = 1053.47
PHY-3002 : Step(6): len = 331190, overlap = 1131.22
PHY-3002 : Step(7): len = 274867, overlap = 1199.88
PHY-3002 : Step(8): len = 249472, overlap = 1269
PHY-3002 : Step(9): len = 217001, overlap = 1318.56
PHY-3002 : Step(10): len = 198202, overlap = 1343.06
PHY-3002 : Step(11): len = 172698, overlap = 1377.72
PHY-3002 : Step(12): len = 161948, overlap = 1427.06
PHY-3002 : Step(13): len = 145572, overlap = 1468.75
PHY-3002 : Step(14): len = 133629, overlap = 1488.25
PHY-3002 : Step(15): len = 122538, overlap = 1509.41
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.12785e-06
PHY-3002 : Step(16): len = 125856, overlap = 1485.97
PHY-3002 : Step(17): len = 173552, overlap = 1377.53
PHY-3002 : Step(18): len = 193058, overlap = 1233.88
PHY-3002 : Step(19): len = 194308, overlap = 1166.5
PHY-3002 : Step(20): len = 191222, overlap = 1112.78
PHY-3002 : Step(21): len = 185996, overlap = 1088.81
PHY-3002 : Step(22): len = 181492, overlap = 1083.09
PHY-3002 : Step(23): len = 175920, overlap = 1068.53
PHY-3002 : Step(24): len = 171739, overlap = 1070.75
PHY-3002 : Step(25): len = 168261, overlap = 1063.06
PHY-3002 : Step(26): len = 165808, overlap = 1083.53
PHY-3002 : Step(27): len = 164433, overlap = 1071.75
PHY-3002 : Step(28): len = 163304, overlap = 1077.75
PHY-3002 : Step(29): len = 163392, overlap = 1079.72
PHY-3002 : Step(30): len = 162849, overlap = 1086.16
PHY-3002 : Step(31): len = 162748, overlap = 1079.97
PHY-3002 : Step(32): len = 160983, overlap = 1095.78
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.25569e-06
PHY-3002 : Step(33): len = 168351, overlap = 1077.12
PHY-3002 : Step(34): len = 179799, overlap = 1031.84
PHY-3002 : Step(35): len = 182857, overlap = 988
PHY-3002 : Step(36): len = 185019, overlap = 980.312
PHY-3002 : Step(37): len = 185761, overlap = 960.281
PHY-3002 : Step(38): len = 185433, overlap = 958.25
PHY-3002 : Step(39): len = 183864, overlap = 965.562
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.51138e-06
PHY-3002 : Step(40): len = 195606, overlap = 879.438
PHY-3002 : Step(41): len = 209813, overlap = 803.625
PHY-3002 : Step(42): len = 215982, overlap = 760.688
PHY-3002 : Step(43): len = 218380, overlap = 729.031
PHY-3002 : Step(44): len = 219069, overlap = 708.5
PHY-3002 : Step(45): len = 217694, overlap = 691.5
PHY-3002 : Step(46): len = 216408, overlap = 673.75
PHY-3002 : Step(47): len = 215894, overlap = 661.969
PHY-3002 : Step(48): len = 215598, overlap = 664.094
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 9.02277e-06
PHY-3002 : Step(49): len = 225926, overlap = 614.469
PHY-3002 : Step(50): len = 239982, overlap = 561.031
PHY-3002 : Step(51): len = 243558, overlap = 529.625
PHY-3002 : Step(52): len = 247645, overlap = 520.5
PHY-3002 : Step(53): len = 246935, overlap = 530.938
PHY-3002 : Step(54): len = 245959, overlap = 541.781
PHY-3002 : Step(55): len = 243993, overlap = 554.094
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.80455e-05
PHY-3002 : Step(56): len = 256338, overlap = 499.5
PHY-3002 : Step(57): len = 269705, overlap = 457.656
PHY-3002 : Step(58): len = 273708, overlap = 445.375
PHY-3002 : Step(59): len = 274517, overlap = 439.344
PHY-3002 : Step(60): len = 272104, overlap = 444.75
PHY-3002 : Step(61): len = 269620, overlap = 449.344
PHY-3002 : Step(62): len = 268668, overlap = 438.688
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.60911e-05
PHY-3002 : Step(63): len = 277593, overlap = 442.844
PHY-3002 : Step(64): len = 286726, overlap = 442.969
PHY-3002 : Step(65): len = 290172, overlap = 409.125
PHY-3002 : Step(66): len = 291422, overlap = 387.75
PHY-3002 : Step(67): len = 290591, overlap = 387.969
PHY-3002 : Step(68): len = 289498, overlap = 395.438
PHY-3002 : Step(69): len = 287449, overlap = 374.031
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 7.21821e-05
PHY-3002 : Step(70): len = 294890, overlap = 386.844
PHY-3002 : Step(71): len = 303244, overlap = 367.344
PHY-3002 : Step(72): len = 306024, overlap = 340.812
PHY-3002 : Step(73): len = 307090, overlap = 334.938
PHY-3002 : Step(74): len = 304429, overlap = 338.562
PHY-3002 : Step(75): len = 303172, overlap = 340.125
PHY-3002 : Step(76): len = 301287, overlap = 322.031
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.00013658
PHY-3002 : Step(77): len = 305731, overlap = 311.031
PHY-3002 : Step(78): len = 310674, overlap = 301.625
PHY-3002 : Step(79): len = 312051, overlap = 274.594
PHY-3002 : Step(80): len = 313184, overlap = 273.625
PHY-3002 : Step(81): len = 312345, overlap = 275.406
PHY-3002 : Step(82): len = 311892, overlap = 274.719
PHY-3002 : Step(83): len = 311667, overlap = 261.188
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.00023782
PHY-3002 : Step(84): len = 314488, overlap = 255.062
PHY-3002 : Step(85): len = 318451, overlap = 249.625
PHY-3002 : Step(86): len = 319211, overlap = 240.969
PHY-3002 : Step(87): len = 320251, overlap = 252.594
PHY-3002 : Step(88): len = 320234, overlap = 251.562
PHY-3002 : Step(89): len = 321365, overlap = 250.906
PHY-3002 : Step(90): len = 320503, overlap = 251.219
PHY-3002 : Step(91): len = 321503, overlap = 250.844
PHY-3002 : Step(92): len = 321567, overlap = 258.062
PHY-3002 : Step(93): len = 321541, overlap = 274.688
PHY-3002 : Step(94): len = 321253, overlap = 283.781
PHY-3002 : Step(95): len = 321405, overlap = 291.625
PHY-3002 : Step(96): len = 321487, overlap = 287.125
PHY-3002 : Step(97): len = 320910, overlap = 298.156
PHY-3002 : Step(98): len = 320704, overlap = 307.5
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(99): len = 321933, overlap = 300.531
PHY-3002 : Step(100): len = 323335, overlap = 295.812
PHY-3002 : Step(101): len = 323656, overlap = 298.062
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008089s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (193.2%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/22019.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 422968, over cnt = 1171(3%), over = 5791, worst = 46
PHY-1001 : End global iterations;  0.777495s wall, 1.062500s user + 0.046875s system = 1.109375s CPU (142.7%)

PHY-1001 : Congestion index: top1 = 80.00, top5 = 54.67, top10 = 43.94, top15 = 38.06.
PHY-3001 : End congestion estimation;  1.007986s wall, 1.265625s user + 0.062500s system = 1.328125s CPU (131.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22017 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.856372s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000114895
PHY-3002 : Step(102): len = 367334, overlap = 243.938
PHY-3002 : Step(103): len = 381379, overlap = 233.531
PHY-3002 : Step(104): len = 383397, overlap = 216.312
PHY-3002 : Step(105): len = 380558, overlap = 211.062
PHY-3002 : Step(106): len = 381232, overlap = 195.312
PHY-3002 : Step(107): len = 384215, overlap = 172.812
PHY-3002 : Step(108): len = 386819, overlap = 163.844
PHY-3002 : Step(109): len = 388709, overlap = 143.75
PHY-3002 : Step(110): len = 391429, overlap = 138.156
PHY-3002 : Step(111): len = 393493, overlap = 135.219
PHY-3002 : Step(112): len = 393573, overlap = 128.75
PHY-3002 : Step(113): len = 395988, overlap = 134.719
PHY-3002 : Step(114): len = 395866, overlap = 131.875
PHY-3002 : Step(115): len = 394610, overlap = 135.219
PHY-3002 : Step(116): len = 395376, overlap = 134.281
PHY-3002 : Step(117): len = 394694, overlap = 135.562
PHY-3002 : Step(118): len = 395556, overlap = 142.531
PHY-3002 : Step(119): len = 396858, overlap = 147.344
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00022979
PHY-3002 : Step(120): len = 396192, overlap = 138.344
PHY-3002 : Step(121): len = 397108, overlap = 136.531
PHY-3002 : Step(122): len = 399870, overlap = 134.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00045958
PHY-3002 : Step(123): len = 403275, overlap = 131.062
PHY-3002 : Step(124): len = 409984, overlap = 124.094
PHY-3002 : Step(125): len = 415142, overlap = 120.25
PHY-3002 : Step(126): len = 417545, overlap = 115.094
PHY-3002 : Step(127): len = 422144, overlap = 112.812
PHY-3002 : Step(128): len = 429471, overlap = 123.688
PHY-3002 : Step(129): len = 430773, overlap = 127.719
PHY-3002 : Step(130): len = 431070, overlap = 122.469
PHY-3002 : Step(131): len = 432904, overlap = 123.312
PHY-3002 : Step(132): len = 431461, overlap = 125.625
PHY-3002 : Step(133): len = 430043, overlap = 124.562
PHY-3002 : Step(134): len = 429556, overlap = 117.625
PHY-3002 : Step(135): len = 428535, overlap = 117.656
PHY-3002 : Step(136): len = 430523, overlap = 115.062
PHY-3002 : Step(137): len = 433345, overlap = 107.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000919159
PHY-3002 : Step(138): len = 432665, overlap = 111.125
PHY-3002 : Step(139): len = 435463, overlap = 108.469
PHY-3002 : Step(140): len = 438882, overlap = 107.906
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00160759
PHY-3002 : Step(141): len = 440000, overlap = 104.906
PHY-3002 : Step(142): len = 448090, overlap = 100.281
PHY-3002 : Step(143): len = 456820, overlap = 100.594
PHY-3002 : Step(144): len = 457027, overlap = 102.5
PHY-3002 : Step(145): len = 456741, overlap = 100.25
PHY-3002 : Step(146): len = 456914, overlap = 93.0312
PHY-3002 : Step(147): len = 458191, overlap = 87.4688
PHY-3002 : Step(148): len = 457713, overlap = 86.25
PHY-3002 : Step(149): len = 456850, overlap = 89.1562
PHY-3002 : Step(150): len = 457260, overlap = 86.875
PHY-3002 : Step(151): len = 456236, overlap = 86.8125
PHY-3002 : Step(152): len = 456195, overlap = 85.5938
PHY-3002 : Step(153): len = 456278, overlap = 91.3125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 67/22019.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 520904, over cnt = 2189(6%), over = 10713, worst = 45
PHY-1001 : End global iterations;  0.947311s wall, 1.546875s user + 0.015625s system = 1.562500s CPU (164.9%)

PHY-1001 : Congestion index: top1 = 76.12, top5 = 60.23, top10 = 52.05, top15 = 46.72.
PHY-3001 : End congestion estimation;  1.201474s wall, 1.812500s user + 0.015625s system = 1.828125s CPU (152.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22017 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.883612s wall, 0.890625s user + 0.000000s system = 0.890625s CPU (100.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.13804e-05
PHY-3002 : Step(154): len = 460438, overlap = 394.969
PHY-3002 : Step(155): len = 464966, overlap = 311.625
PHY-3002 : Step(156): len = 462063, overlap = 287.406
PHY-3002 : Step(157): len = 457366, overlap = 282.219
PHY-3002 : Step(158): len = 455150, overlap = 271.688
PHY-3002 : Step(159): len = 455368, overlap = 253.125
PHY-3002 : Step(160): len = 453926, overlap = 246.906
PHY-3002 : Step(161): len = 452153, overlap = 239.125
PHY-3002 : Step(162): len = 453398, overlap = 236.438
PHY-3002 : Step(163): len = 452103, overlap = 235.688
PHY-3002 : Step(164): len = 451155, overlap = 222.5
PHY-3002 : Step(165): len = 452045, overlap = 222.844
PHY-3002 : Step(166): len = 452218, overlap = 221.594
PHY-3002 : Step(167): len = 450449, overlap = 216.875
PHY-3002 : Step(168): len = 451188, overlap = 221.312
PHY-3002 : Step(169): len = 451504, overlap = 211.938
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000182761
PHY-3002 : Step(170): len = 449835, overlap = 213.031
PHY-3002 : Step(171): len = 450618, overlap = 214.125
PHY-3002 : Step(172): len = 450932, overlap = 204.594
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000365522
PHY-3002 : Step(173): len = 456907, overlap = 177.625
PHY-3002 : Step(174): len = 467096, overlap = 159.719
PHY-3002 : Step(175): len = 469011, overlap = 161.438
PHY-3002 : Step(176): len = 468812, overlap = 159.094
PHY-3002 : Step(177): len = 468864, overlap = 162.406
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000731043
PHY-3002 : Step(178): len = 470684, overlap = 152.031
PHY-3002 : Step(179): len = 473858, overlap = 142.75
PHY-3002 : Step(180): len = 479652, overlap = 136.344
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00136395
PHY-3002 : Step(181): len = 479626, overlap = 139.781
PHY-3002 : Step(182): len = 482503, overlap = 134.562
PHY-3002 : Step(183): len = 490207, overlap = 140.406
PHY-3002 : Step(184): len = 493280, overlap = 139.438
PHY-3002 : Step(185): len = 493914, overlap = 136.781
PHY-3002 : Step(186): len = 493389, overlap = 137.25
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00236457
PHY-3002 : Step(187): len = 493750, overlap = 136.5
PHY-3002 : Step(188): len = 495204, overlap = 135.375
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81749, tnet num: 22017, tinst num: 19652, tnode num: 115542, tedge num: 128404.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.432060s wall, 1.390625s user + 0.046875s system = 1.437500s CPU (100.4%)

RUN-1004 : used memory is 567 MB, reserved memory is 542 MB, peak memory is 703 MB
OPT-1001 : Total overflow 492.03 peak overflow 3.91
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 273/22019.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 579432, over cnt = 2670(7%), over = 9583, worst = 26
PHY-1001 : End global iterations;  1.190451s wall, 1.906250s user + 0.046875s system = 1.953125s CPU (164.1%)

PHY-1001 : Congestion index: top1 = 57.89, top5 = 49.06, top10 = 44.19, top15 = 41.11.
PHY-1001 : End incremental global routing;  1.415763s wall, 2.125000s user + 0.046875s system = 2.171875s CPU (153.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22017 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.898697s wall, 0.843750s user + 0.062500s system = 0.906250s CPU (100.8%)

OPT-1001 : 22 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 56 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19571 has valid locations, 284 needs to be replaced
PHY-3001 : design contains 19914 instances, 5754 luts, 12635 seqs, 1424 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 515719
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17560/22281.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 595208, over cnt = 2692(7%), over = 9606, worst = 26
PHY-1001 : End global iterations;  0.183340s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (153.4%)

PHY-1001 : Congestion index: top1 = 58.25, top5 = 49.23, top10 = 44.42, top15 = 41.41.
PHY-3001 : End congestion estimation;  0.411411s wall, 0.500000s user + 0.000000s system = 0.500000s CPU (121.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82647, tnet num: 22279, tinst num: 19914, tnode num: 116770, tedge num: 129676.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.433523s wall, 1.390625s user + 0.046875s system = 1.437500s CPU (100.3%)

RUN-1004 : used memory is 614 MB, reserved memory is 608 MB, peak memory is 707 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22279 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.391965s wall, 2.312500s user + 0.078125s system = 2.390625s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(189): len = 515366, overlap = 0.875
PHY-3002 : Step(190): len = 516442, overlap = 1
PHY-3002 : Step(191): len = 517194, overlap = 0.9375
PHY-3002 : Step(192): len = 517713, overlap = 0.875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17597/22281.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 592712, over cnt = 2724(7%), over = 9795, worst = 26
PHY-1001 : End global iterations;  0.188692s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (140.8%)

PHY-1001 : Congestion index: top1 = 58.79, top5 = 49.57, top10 = 44.71, top15 = 41.61.
PHY-3001 : End congestion estimation;  0.429358s wall, 0.500000s user + 0.000000s system = 0.500000s CPU (116.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22279 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.958191s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000778502
PHY-3002 : Step(193): len = 517486, overlap = 137.812
PHY-3002 : Step(194): len = 517614, overlap = 137.562
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.001557
PHY-3002 : Step(195): len = 517760, overlap = 137.5
PHY-3002 : Step(196): len = 518019, overlap = 137.781
PHY-3001 : Final: Len = 518019, Over = 137.781
PHY-3001 : End incremental placement;  5.143100s wall, 5.406250s user + 0.187500s system = 5.593750s CPU (108.8%)

OPT-1001 : Total overflow 496.88 peak overflow 3.91
OPT-1001 : End high-fanout net optimization;  7.956911s wall, 9.046875s user + 0.296875s system = 9.343750s CPU (117.4%)

OPT-1001 : Current memory(MB): used = 711, reserve = 690, peak = 726.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17601/22281.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 594896, over cnt = 2676(7%), over = 9082, worst = 26
PHY-1002 : len = 643248, over cnt = 1722(4%), over = 4294, worst = 26
PHY-1002 : len = 680912, over cnt = 793(2%), over = 1558, worst = 20
PHY-1002 : len = 696456, over cnt = 279(0%), over = 579, worst = 10
PHY-1002 : len = 707488, over cnt = 31(0%), over = 48, worst = 4
PHY-1001 : End global iterations;  1.304793s wall, 1.890625s user + 0.015625s system = 1.906250s CPU (146.1%)

PHY-1001 : Congestion index: top1 = 49.27, top5 = 44.01, top10 = 41.14, top15 = 39.29.
OPT-1001 : End congestion update;  1.544200s wall, 2.125000s user + 0.015625s system = 2.140625s CPU (138.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22279 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.823889s wall, 0.796875s user + 0.015625s system = 0.812500s CPU (98.6%)

OPT-0007 : Start: WNS 3972 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.375490s wall, 2.937500s user + 0.031250s system = 2.968750s CPU (125.0%)

OPT-1001 : Current memory(MB): used = 687, reserve = 669, peak = 726.
OPT-1001 : End physical optimization;  12.069105s wall, 13.671875s user + 0.375000s system = 14.046875s CPU (116.4%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5754 LUT to BLE ...
SYN-4008 : Packed 5754 LUT and 2877 SEQ to BLE.
SYN-4003 : Packing 9758 remaining SEQ's ...
SYN-4005 : Packed 3290 SEQ with LUT/SLICE
SYN-4006 : 105 single LUT's are left
SYN-4006 : 6468 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 12222/13869 primitive instances ...
PHY-3001 : End packing;  2.778745s wall, 2.781250s user + 0.000000s system = 2.781250s CPU (100.1%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8222 instances
RUN-1001 : 4059 mslices, 4060 lslices, 56 pads, 42 brams, 0 dsps
RUN-1001 : There are total 19448 nets
RUN-1001 : 13566 nets have 2 pins
RUN-1001 : 4497 nets have [3 - 5] pins
RUN-1001 : 846 nets have [6 - 10] pins
RUN-1001 : 382 nets have [11 - 20] pins
RUN-1001 : 148 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8220 instances, 8119 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Cell area utilization is 86%
PHY-3001 : After packing: Len = 531655, Over = 380.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 8046/19448.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 665264, over cnt = 1678(4%), over = 2638, worst = 9
PHY-1002 : len = 672896, over cnt = 1089(3%), over = 1447, worst = 6
PHY-1002 : len = 683696, over cnt = 499(1%), over = 631, worst = 6
PHY-1002 : len = 690456, over cnt = 206(0%), over = 252, worst = 5
PHY-1002 : len = 695424, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.226201s wall, 1.984375s user + 0.015625s system = 2.000000s CPU (163.1%)

PHY-1001 : Congestion index: top1 = 51.83, top5 = 44.80, top10 = 41.33, top15 = 39.12.
PHY-3001 : End congestion estimation;  1.528837s wall, 2.281250s user + 0.015625s system = 2.296875s CPU (150.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68170, tnet num: 19446, tinst num: 8220, tnode num: 92978, tedge num: 112264.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.660469s wall, 1.656250s user + 0.000000s system = 1.656250s CPU (99.7%)

RUN-1004 : used memory is 604 MB, reserved memory is 592 MB, peak memory is 726 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19446 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.510005s wall, 2.484375s user + 0.015625s system = 2.500000s CPU (99.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.31953e-05
PHY-3002 : Step(197): len = 534883, overlap = 342
PHY-3002 : Step(198): len = 532008, overlap = 344.25
PHY-3002 : Step(199): len = 529928, overlap = 357.25
PHY-3002 : Step(200): len = 531842, overlap = 372
PHY-3002 : Step(201): len = 529726, overlap = 375.75
PHY-3002 : Step(202): len = 529166, overlap = 383.75
PHY-3002 : Step(203): len = 527044, overlap = 391
PHY-3002 : Step(204): len = 524633, overlap = 395.25
PHY-3002 : Step(205): len = 522761, overlap = 400.5
PHY-3002 : Step(206): len = 521373, overlap = 407.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000106391
PHY-3002 : Step(207): len = 524588, overlap = 398.5
PHY-3002 : Step(208): len = 526871, overlap = 393.5
PHY-3002 : Step(209): len = 527250, overlap = 389.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00020336
PHY-3002 : Step(210): len = 536448, overlap = 375
PHY-3002 : Step(211): len = 543163, overlap = 357.75
PHY-3002 : Step(212): len = 541320, overlap = 358.25
PHY-3002 : Step(213): len = 539891, overlap = 361.25
PHY-3002 : Step(214): len = 539074, overlap = 365.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.610275s wall, 0.593750s user + 0.718750s system = 1.312500s CPU (215.1%)

PHY-3001 : Trial Legalized: Len = 643885
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 496/19448.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 731952, over cnt = 2499(7%), over = 4064, worst = 6
PHY-1002 : len = 745568, over cnt = 1621(4%), over = 2355, worst = 6
PHY-1002 : len = 763600, over cnt = 704(2%), over = 1050, worst = 6
PHY-1002 : len = 781720, over cnt = 126(0%), over = 178, worst = 4
PHY-1002 : len = 785384, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.842099s wall, 2.937500s user + 0.015625s system = 2.953125s CPU (160.3%)

PHY-1001 : Congestion index: top1 = 50.84, top5 = 46.43, top10 = 43.68, top15 = 41.79.
PHY-3001 : End congestion estimation;  2.163308s wall, 3.265625s user + 0.015625s system = 3.281250s CPU (151.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19446 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.839068s wall, 0.812500s user + 0.031250s system = 0.843750s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000173389
PHY-3002 : Step(215): len = 603162, overlap = 82.75
PHY-3002 : Step(216): len = 585716, overlap = 127.5
PHY-3002 : Step(217): len = 574375, overlap = 173.75
PHY-3002 : Step(218): len = 567182, overlap = 211.75
PHY-3002 : Step(219): len = 563079, overlap = 247
PHY-3002 : Step(220): len = 560495, overlap = 261.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000346778
PHY-3002 : Step(221): len = 565052, overlap = 254.5
PHY-3002 : Step(222): len = 569003, overlap = 252.5
PHY-3002 : Step(223): len = 567738, overlap = 257.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(224): len = 571255, overlap = 251.75
PHY-3002 : Step(225): len = 577876, overlap = 251
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.027576s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (113.3%)

PHY-3001 : Legalized: Len = 617064, Over = 0
PHY-3001 : Spreading special nets. 67 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.078810s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (99.1%)

PHY-3001 : 97 instances has been re-located, deltaX = 18, deltaY = 70, maxDist = 2.
PHY-3001 : Final: Len = 619020, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68170, tnet num: 19446, tinst num: 8220, tnode num: 92978, tedge num: 112264.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.859368s wall, 1.812500s user + 0.046875s system = 1.859375s CPU (100.0%)

RUN-1004 : used memory is 609 MB, reserved memory is 603 MB, peak memory is 726 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 4312/19448.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 719816, over cnt = 2348(6%), over = 3700, worst = 7
PHY-1002 : len = 732752, over cnt = 1442(4%), over = 1959, worst = 7
PHY-1002 : len = 749888, over cnt = 486(1%), over = 676, worst = 7
PHY-1002 : len = 757056, over cnt = 184(0%), over = 247, worst = 4
PHY-1002 : len = 761888, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.629568s wall, 2.609375s user + 0.015625s system = 2.625000s CPU (161.1%)

PHY-1001 : Congestion index: top1 = 47.74, top5 = 43.71, top10 = 41.21, top15 = 39.56.
PHY-1001 : End incremental global routing;  1.923549s wall, 2.906250s user + 0.015625s system = 2.921875s CPU (151.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19446 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.827113s wall, 0.812500s user + 0.015625s system = 0.828125s CPU (100.1%)

OPT-1001 : 4 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 56 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8157 has valid locations, 24 needs to be replaced
PHY-3001 : design contains 8240 instances, 8139 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 622147
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17653/19469.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 765248, over cnt = 67(0%), over = 73, worst = 3
PHY-1002 : len = 765176, over cnt = 26(0%), over = 26, worst = 1
PHY-1002 : len = 765352, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 765512, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 765528, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.700355s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (102.6%)

PHY-1001 : Congestion index: top1 = 47.95, top5 = 43.84, top10 = 41.32, top15 = 39.65.
PHY-3001 : End congestion estimation;  0.977854s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (103.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68325, tnet num: 19467, tinst num: 8240, tnode num: 93165, tedge num: 112470.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.883549s wall, 1.859375s user + 0.015625s system = 1.875000s CPU (99.5%)

RUN-1004 : used memory is 642 MB, reserved memory is 629 MB, peak memory is 726 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19467 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.722459s wall, 2.703125s user + 0.015625s system = 2.718750s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(226): len = 621598, overlap = 0.75
PHY-3002 : Step(227): len = 621153, overlap = 1
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17646/19469.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 763808, over cnt = 37(0%), over = 46, worst = 4
PHY-1002 : len = 763808, over cnt = 18(0%), over = 18, worst = 1
PHY-1002 : len = 763880, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 763976, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.505096s wall, 0.515625s user + 0.031250s system = 0.546875s CPU (108.3%)

PHY-1001 : Congestion index: top1 = 48.04, top5 = 43.86, top10 = 41.32, top15 = 39.62.
PHY-3001 : End congestion estimation;  0.782856s wall, 0.796875s user + 0.031250s system = 0.828125s CPU (105.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19467 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.822368s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (100.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000588273
PHY-3002 : Step(228): len = 621295, overlap = 3.25
PHY-3002 : Step(229): len = 621120, overlap = 2.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005708s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 621170, Over = 0
PHY-3001 : End spreading;  0.060643s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.1%)

PHY-3001 : Final: Len = 621170, Over = 0
PHY-3001 : End incremental placement;  5.904314s wall, 5.828125s user + 0.140625s system = 5.968750s CPU (101.1%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  9.088615s wall, 10.156250s user + 0.171875s system = 10.328125s CPU (113.6%)

OPT-1001 : Current memory(MB): used = 716, reserve = 702, peak = 726.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17645/19469.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 763744, over cnt = 30(0%), over = 35, worst = 3
PHY-1002 : len = 763728, over cnt = 21(0%), over = 21, worst = 1
PHY-1002 : len = 763864, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 763968, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 764032, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.668435s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (107.5%)

PHY-1001 : Congestion index: top1 = 47.69, top5 = 43.70, top10 = 41.21, top15 = 39.55.
OPT-1001 : End congestion update;  0.950671s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (105.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19467 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.688115s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (97.6%)

OPT-0007 : Start: WNS 4698 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.643441s wall, 1.671875s user + 0.000000s system = 1.671875s CPU (101.7%)

OPT-1001 : Current memory(MB): used = 715, reserve = 701, peak = 726.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19467 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.700441s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (100.4%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17674/19469.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 764032, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.113651s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (110.0%)

PHY-1001 : Congestion index: top1 = 47.69, top5 = 43.70, top10 = 41.21, top15 = 39.55.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19467 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.719560s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (99.9%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4698 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 47.206897
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4698ps with logic level 6 
RUN-1001 :       #2 path slack 4698ps with logic level 6 
RUN-1001 :       #3 path slack 4741ps with logic level 6 
RUN-1001 :       #4 path slack 4741ps with logic level 6 
RUN-1001 :       #5 path slack 4755ps with logic level 6 
RUN-1001 :       #6 path slack 4755ps with logic level 6 
RUN-1001 :       #7 path slack 4798ps with logic level 6 
RUN-1001 :       #8 path slack 4798ps with logic level 6 
OPT-1001 : End physical optimization;  14.662496s wall, 15.703125s user + 0.218750s system = 15.921875s CPU (108.6%)

RUN-1003 : finish command "place" in  81.129139s wall, 138.281250s user + 7.640625s system = 145.921875s CPU (179.9%)

RUN-1004 : used memory is 597 MB, reserved memory is 593 MB, peak memory is 726 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.618660s wall, 2.812500s user + 0.015625s system = 2.828125s CPU (174.7%)

RUN-1004 : used memory is 597 MB, reserved memory is 594 MB, peak memory is 726 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8242 instances
RUN-1001 : 4077 mslices, 4062 lslices, 56 pads, 42 brams, 0 dsps
RUN-1001 : There are total 19469 nets
RUN-1001 : 13563 nets have 2 pins
RUN-1001 : 4501 nets have [3 - 5] pins
RUN-1001 : 861 nets have [6 - 10] pins
RUN-1001 : 384 nets have [11 - 20] pins
RUN-1001 : 151 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68325, tnet num: 19467, tinst num: 8240, tnode num: 93165, tedge num: 112470.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.650543s wall, 1.656250s user + 0.000000s system = 1.656250s CPU (100.3%)

RUN-1004 : used memory is 591 MB, reserved memory is 577 MB, peak memory is 726 MB
PHY-1001 : 4077 mslices, 4062 lslices, 56 pads, 42 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19467 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 696656, over cnt = 2507(7%), over = 4097, worst = 8
PHY-1002 : len = 712368, over cnt = 1599(4%), over = 2293, worst = 8
PHY-1002 : len = 735600, over cnt = 491(1%), over = 653, worst = 6
PHY-1002 : len = 746064, over cnt = 52(0%), over = 62, worst = 3
PHY-1002 : len = 747736, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.735949s wall, 2.984375s user + 0.046875s system = 3.031250s CPU (174.6%)

PHY-1001 : Congestion index: top1 = 47.72, top5 = 43.23, top10 = 40.73, top15 = 39.13.
PHY-1001 : End global routing;  2.067004s wall, 3.312500s user + 0.046875s system = 3.359375s CPU (162.5%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 700, reserve = 688, peak = 726.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 969, reserve = 958, peak = 969.
PHY-1001 : End build detailed router design. 4.453597s wall, 4.437500s user + 0.015625s system = 4.453125s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 191768, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.795684s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1005, reserve = 995, peak = 1005.
PHY-1001 : End phase 1; 0.802586s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.3%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 55% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.7556e+06, over cnt = 1543(0%), over = 1544, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1022, reserve = 1012, peak = 1022.
PHY-1001 : End initial routed; 19.602452s wall, 49.156250s user + 0.359375s system = 49.515625s CPU (252.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18269(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.413   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.158637s wall, 3.140625s user + 0.015625s system = 3.156250s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 1027, reserve = 1020, peak = 1027.
PHY-1001 : End phase 2; 22.761217s wall, 52.296875s user + 0.375000s system = 52.671875s CPU (231.4%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.7556e+06, over cnt = 1543(0%), over = 1544, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.212664s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (102.9%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.73833e+06, over cnt = 498(0%), over = 498, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 1.199657s wall, 2.000000s user + 0.000000s system = 2.000000s CPU (166.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.73893e+06, over cnt = 100(0%), over = 100, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.375406s wall, 0.562500s user + 0.000000s system = 0.562500s CPU (149.8%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.74003e+06, over cnt = 20(0%), over = 20, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.272323s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (126.2%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.7406e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.178499s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (96.3%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.74066e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.159078s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (108.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18269(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.413   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.180546s wall, 3.187500s user + 0.000000s system = 3.187500s CPU (100.2%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 415 feed throughs used by 361 nets
PHY-1001 : End commit to database; 2.123755s wall, 2.093750s user + 0.031250s system = 2.125000s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1121, reserve = 1118, peak = 1121.
PHY-1001 : End phase 3; 8.152684s wall, 9.187500s user + 0.031250s system = 9.218750s CPU (113.1%)

PHY-1003 : Routed, final wirelength = 1.74066e+06
PHY-1001 : Current memory(MB): used = 1125, reserve = 1122, peak = 1125.
PHY-1001 : End export database. 0.056696s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (82.7%)

PHY-1001 : End detail routing;  36.622387s wall, 67.156250s user + 0.437500s system = 67.593750s CPU (184.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68325, tnet num: 19467, tinst num: 8240, tnode num: 93165, tedge num: 112470.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.593001s wall, 1.593750s user + 0.000000s system = 1.593750s CPU (100.0%)

RUN-1004 : used memory is 1005 MB, reserved memory is 1020 MB, peak memory is 1125 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  44.347749s wall, 76.109375s user + 0.515625s system = 76.625000s CPU (172.8%)

RUN-1004 : used memory is 1028 MB, reserved memory is 1048 MB, peak memory is 1125 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8779   out of  19600   44.79%
#reg                    12742   out of  19600   65.01%
#le                     15213
  #lut only              2471   out of  15213   16.24%
  #reg only              6434   out of  15213   42.29%
  #lut&reg               6308   out of  15213   41.46%
#dsp                        0   out of     29    0.00%
#bram                      42   out of     64   65.62%
  #bram9k                  42
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       56   out of    188   29.79%
  #ireg                     9
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6962
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          183
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT        E11        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT        A13        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT        F10        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         E8        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         E7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT        A10        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C6        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         D6        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         C7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT        E12        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         A9        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         A7        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H13        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J11        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT        E13        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         R7        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         M7        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         P7        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         T8        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         P8        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         N8        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M9        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         T9        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         R9        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         T6        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         P9        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         N9        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT        P11        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         T5        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         B2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        L10        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        E15        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        R14        LVCMOS33           8            N/A            OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT         R1        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT        M12        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        K12        LVCMOS33           8            N/A            NONE       
    TXD_RMC        OUTPUT         C3        LVCMOS33           8            N/A            NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        R15        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E6        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15213  |7355    |1424    |12784   |42      |0       |
|  AnyFog_dataX                      |AnyFog          |219    |99      |22      |180     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |82     |50      |22      |47      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |222    |86      |22      |184     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |83     |54      |22      |49      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |223    |108     |22      |182     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |83     |51      |22      |48      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |3476   |895     |34      |3391    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |740    |71      |5       |728     |0       |0       |
|    STADOP_com2                     |STADOP          |560    |106     |0       |550     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |42      |14      |42      |0       |0       |
|    head_com2                       |uniheading      |257    |79      |5       |243     |0       |0       |
|    rmc_com2                        |Gprmc           |162    |59      |0       |151     |0       |0       |
|    uart_com2                       |Agrica          |1402   |248     |10      |1387    |0       |0       |
|  DATA                              |Data_Processing |8619   |4341    |1062    |6931    |0       |0       |
|    DIV_Dtemp                       |Divider         |796    |338     |84      |670     |0       |0       |
|    DIV_Utemp                       |Divider         |702    |298     |84      |581     |0       |0       |
|    DIV_accX                        |Divider         |582    |306     |84      |460     |0       |0       |
|    DIV_accY                        |Divider         |660    |355     |111     |482     |0       |0       |
|    DIV_accZ                        |Divider         |652    |364     |132     |448     |0       |0       |
|    DIV_rateX                       |Divider         |658    |391     |132     |454     |0       |0       |
|    DIV_rateY                       |Divider         |592    |374     |132     |387     |0       |0       |
|    DIV_rateZ                       |Divider         |558    |377     |132     |354     |0       |0       |
|    genclk                          |genclk          |79     |39      |20      |45      |0       |0       |
|  FMC                               |FMC_Ctrl        |494    |443     |43      |361     |0       |0       |
|  IIC                               |I2C_master      |270    |226     |11      |252     |0       |0       |
|  IMU_CTRL                          |SCHA634         |921    |627     |61      |755     |0       |0       |
|    CtrlData                        |CtrlData        |457    |403     |47      |332     |0       |0       |
|      usms                          |Time_1ms        |28     |23      |5       |20      |0       |0       |
|    SPIM                            |SPI_SCHA634     |464    |224     |14      |423     |0       |0       |
|  POWER                             |POWER_EN        |98     |49      |38      |38      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |665    |481     |109     |462     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |665    |481     |109     |462     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |296    |244     |0       |279     |0       |0       |
|        reg_inst                    |register        |293    |241     |0       |276     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |369    |237     |109     |183     |0       |0       |
|        bus_inst                    |bus_top         |148    |96      |52      |57      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |26     |16      |10      |10      |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |16     |10      |6       |7       |0       |0       |
|          BUS_DETECTOR[5]$bus_nodes |bus_det         |50     |32      |18      |17      |0       |0       |
|          BUS_DETECTOR[6]$bus_nodes |bus_det         |52     |34      |18      |19      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |143    |95      |29      |94      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13506  
    #2          2       3547   
    #3          3        667   
    #4          4        287   
    #5        5-10       905   
    #6        11-50      479   
    #7       51-100      12    
    #8       101-500      3    
    #9        >500        2    
  Average     2.15             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  1.888215s wall, 3.187500s user + 0.015625s system = 3.203125s CPU (169.6%)

RUN-1004 : used memory is 1029 MB, reserved memory is 1049 MB, peak memory is 1125 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68325, tnet num: 19467, tinst num: 8240, tnode num: 93165, tedge num: 112470.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.582330s wall, 1.578125s user + 0.000000s system = 1.578125s CPU (99.7%)

RUN-1004 : used memory is 1031 MB, reserved memory is 1051 MB, peak memory is 1125 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19467 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.248760s wall, 1.234375s user + 0.015625s system = 1.250000s CPU (100.1%)

RUN-1004 : used memory is 1054 MB, reserved memory is 1063 MB, peak memory is 1125 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 8f169df8acfcb835781910b1ac29267cd80e16d8a5e1b8cf9d032ec93c0a2865 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8240
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19469, pip num: 149319
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 415
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3223 valid insts, and 416246 bits set as '1'.
BIT-1004 : the usercode register value: 00000000110101000001111110010110
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  10.252406s wall, 102.890625s user + 0.140625s system = 103.031250s CPU (1004.9%)

RUN-1004 : used memory is 1194 MB, reserved memory is 1180 MB, peak memory is 1309 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250526_151220.log"

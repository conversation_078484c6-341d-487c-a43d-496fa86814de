============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu May 15 08:28:45 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(247)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(252)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(273)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(278)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(421)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(428)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(435)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(442)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(449)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(456)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(463)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(470)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(647)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(652)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(680)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(685)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(919)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(926)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(933)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(940)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(947)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(952)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(959)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(966)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(973)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(980)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(514)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
RUN-1001 : Project manager successfully analyzed 21 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.191860s wall, 1.593750s user + 3.593750s system = 5.187500s CPU (99.9%)

RUN-1004 : used memory is 85 MB, reserved memory is 39 MB, peak memory is 96 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.739795s wall, 1.640625s user + 0.093750s system = 1.734375s CPU (99.7%)

RUN-1004 : used memory is 305 MB, reserved memory is 267 MB, peak memory is 308 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 7 view nodes, 47 trigger nets, 47 data nets.
KIT-1004 : Chipwatcher code = 0001111110010110
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=144) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=144) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=144)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=144)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22832/31 useful/useless nets, 19615/17 useful/useless insts
SYN-1016 : Merged 37 instances.
SYN-1032 : 22456/22 useful/useless nets, 20074/18 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 4 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 4 mux instances.
SYN-1015 : Optimize round 1, 443 better
SYN-1014 : Optimize round 2
SYN-1032 : 22084/60 useful/useless nets, 19702/64 useful/useless insts
SYN-1015 : Optimize round 2, 128 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.310399s wall, 2.265625s user + 0.046875s system = 2.312500s CPU (100.1%)

RUN-1004 : used memory is 329 MB, reserved memory is 293 MB, peak memory is 331 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22144/367 useful/useless nets, 19803/52 useful/useless insts
SYN-1016 : Merged 57 instances.
SYN-2571 : Optimize after map_dsp, round 1, 476 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 44 instances.
SYN-2501 : Optimize round 1, 90 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 18 macro adder
SYN-1019 : Optimized 17 mux instances.
SYN-1016 : Merged 12 instances.
SYN-1032 : 22615/5 useful/useless nets, 20274/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83002, tnet num: 22615, tinst num: 20273, tnode num: 116620, tedge num: 129263.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.165733s wall, 1.171875s user + 0.000000s system = 1.171875s CPU (100.5%)

RUN-1004 : used memory is 470 MB, reserved memory is 436 MB, peak memory is 470 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22615 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 257 (3.41), #lev = 7 (1.78)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 257 (3.41), #lev = 7 (1.78)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 596 instances into 257 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 450 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 140 adder to BLE ...
SYN-4008 : Packed 140 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.360523s wall, 4.296875s user + 0.078125s system = 4.375000s CPU (100.3%)

RUN-1004 : used memory is 349 MB, reserved memory is 333 MB, peak memory is 579 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  6.978674s wall, 6.843750s user + 0.140625s system = 6.984375s CPU (100.1%)

RUN-1004 : used memory is 350 MB, reserved memory is 333 MB, peak memory is 579 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/rmc_com2/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/rmc_com2/GPRMC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/rmc_com2/GPRMC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/rmc_com2/GPRMC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/rmc_com2/GPRMC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/rmc_com2/GPRMC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/rmc_com2/GPRMC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/rmc_com2/GPRMC_data[0]
SYN-5055 WARNING: The kept net COM2/rx_err will be merged to another kept net COM2/rmc_com2/GPRMC_err
SYN-5055 WARNING: The kept net FMC/Error_state[0] will be merged to another kept net COM2/rmc_com2/GPRMC_sub_state_cnt0[3]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (306 clock/control pins, 0 other pins).
SYN-4027 : Net DATA/DIV_Dtemp/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net DATA/DIV_Dtemp/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19601 instances
RUN-0007 : 5599 luts, 12471 seqs, 933 mslices, 491 lslices, 60 pads, 42 brams, 0 dsps
RUN-1001 : There are total 21966 nets
RUN-1001 : 16481 nets have 2 pins
RUN-1001 : 4348 nets have [3 - 5] pins
RUN-1001 : 790 nets have [6 - 10] pins
RUN-1001 : 220 nets have [11 - 20] pins
RUN-1001 : 103 nets have [21 - 99] pins
RUN-1001 : 24 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4742     
RUN-1001 :   No   |  No   |  Yes  |     628     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    6531     
RUN-1001 :   Yes  |  No   |  Yes  |     470     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19599 instances, 5599 luts, 12471 seqs, 1424 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1131 pins
PHY-3001 : Huge net DATA/done_div with 1650 pins
PHY-0007 : Cell area utilization is 64%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81533, tnet num: 21964, tinst num: 19599, tnode num: 115249, tedge num: 128074.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.166264s wall, 1.156250s user + 0.015625s system = 1.171875s CPU (100.5%)

RUN-1004 : used memory is 530 MB, reserved memory is 501 MB, peak memory is 579 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21964 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.968103s wall, 1.921875s user + 0.046875s system = 1.968750s CPU (100.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.43159e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19599.
PHY-3001 : Level 1 #clusters 2055.
PHY-3001 : End clustering;  0.133364s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (128.9%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 64%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 853978, overlap = 639.906
PHY-3002 : Step(2): len = 765433, overlap = 704.344
PHY-3002 : Step(3): len = 496637, overlap = 909.625
PHY-3002 : Step(4): len = 437708, overlap = 986.031
PHY-3002 : Step(5): len = 355849, overlap = 1043.09
PHY-3002 : Step(6): len = 314508, overlap = 1112.56
PHY-3002 : Step(7): len = 255405, overlap = 1186.97
PHY-3002 : Step(8): len = 231920, overlap = 1236.09
PHY-3002 : Step(9): len = 203183, overlap = 1300.09
PHY-3002 : Step(10): len = 190727, overlap = 1329.28
PHY-3002 : Step(11): len = 169887, overlap = 1370.06
PHY-3002 : Step(12): len = 162030, overlap = 1427.09
PHY-3002 : Step(13): len = 148948, overlap = 1485.5
PHY-3002 : Step(14): len = 135749, overlap = 1533.75
PHY-3002 : Step(15): len = 127675, overlap = 1551.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.17397e-06
PHY-3002 : Step(16): len = 139430, overlap = 1473.94
PHY-3002 : Step(17): len = 196949, overlap = 1337.19
PHY-3002 : Step(18): len = 200392, overlap = 1242.5
PHY-3002 : Step(19): len = 198634, overlap = 1184.06
PHY-3002 : Step(20): len = 192391, overlap = 1140.12
PHY-3002 : Step(21): len = 189088, overlap = 1112.19
PHY-3002 : Step(22): len = 182499, overlap = 1085.53
PHY-3002 : Step(23): len = 178447, overlap = 1085.31
PHY-3002 : Step(24): len = 174282, overlap = 1067.69
PHY-3002 : Step(25): len = 172006, overlap = 1068.16
PHY-3002 : Step(26): len = 169575, overlap = 1073.22
PHY-3002 : Step(27): len = 168362, overlap = 1061.53
PHY-3002 : Step(28): len = 167736, overlap = 1041.38
PHY-3002 : Step(29): len = 167065, overlap = 1021.69
PHY-3002 : Step(30): len = 165705, overlap = 1017.22
PHY-3002 : Step(31): len = 165627, overlap = 1009.28
PHY-3002 : Step(32): len = 165031, overlap = 991.5
PHY-3002 : Step(33): len = 163537, overlap = 984.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.34795e-06
PHY-3002 : Step(34): len = 171355, overlap = 965.562
PHY-3002 : Step(35): len = 185498, overlap = 930.5
PHY-3002 : Step(36): len = 189494, overlap = 900.25
PHY-3002 : Step(37): len = 191852, overlap = 874.5
PHY-3002 : Step(38): len = 193186, overlap = 863.312
PHY-3002 : Step(39): len = 193489, overlap = 866.125
PHY-3002 : Step(40): len = 190860, overlap = 856.969
PHY-3002 : Step(41): len = 190621, overlap = 856.594
PHY-3002 : Step(42): len = 189489, overlap = 862.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.6959e-06
PHY-3002 : Step(43): len = 200285, overlap = 813.656
PHY-3002 : Step(44): len = 214368, overlap = 726.812
PHY-3002 : Step(45): len = 219002, overlap = 677.031
PHY-3002 : Step(46): len = 221212, overlap = 677.625
PHY-3002 : Step(47): len = 220524, overlap = 663.062
PHY-3002 : Step(48): len = 219702, overlap = 683.125
PHY-3002 : Step(49): len = 217932, overlap = 686.812
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 9.39179e-06
PHY-3002 : Step(50): len = 231822, overlap = 615.156
PHY-3002 : Step(51): len = 248419, overlap = 566.906
PHY-3002 : Step(52): len = 253164, overlap = 553.906
PHY-3002 : Step(53): len = 253566, overlap = 562.469
PHY-3002 : Step(54): len = 252468, overlap = 546.781
PHY-3002 : Step(55): len = 249765, overlap = 546.531
PHY-3002 : Step(56): len = 247858, overlap = 554.094
PHY-3002 : Step(57): len = 246224, overlap = 555.156
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.87836e-05
PHY-3002 : Step(58): len = 258402, overlap = 492.031
PHY-3002 : Step(59): len = 271625, overlap = 439.625
PHY-3002 : Step(60): len = 275841, overlap = 422.75
PHY-3002 : Step(61): len = 277876, overlap = 419.062
PHY-3002 : Step(62): len = 276559, overlap = 405.031
PHY-3002 : Step(63): len = 274820, overlap = 404.5
PHY-3002 : Step(64): len = 272993, overlap = 410.625
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.75672e-05
PHY-3002 : Step(65): len = 282898, overlap = 416.312
PHY-3002 : Step(66): len = 292543, overlap = 402.531
PHY-3002 : Step(67): len = 295927, overlap = 381.969
PHY-3002 : Step(68): len = 296472, overlap = 369.469
PHY-3002 : Step(69): len = 293668, overlap = 368.469
PHY-3002 : Step(70): len = 292341, overlap = 366.156
PHY-3002 : Step(71): len = 289899, overlap = 362.656
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 7.51343e-05
PHY-3002 : Step(72): len = 296335, overlap = 358.219
PHY-3002 : Step(73): len = 305024, overlap = 336.188
PHY-3002 : Step(74): len = 308135, overlap = 319
PHY-3002 : Step(75): len = 309448, overlap = 295.656
PHY-3002 : Step(76): len = 309534, overlap = 277.281
PHY-3002 : Step(77): len = 308454, overlap = 264.094
PHY-3002 : Step(78): len = 306910, overlap = 264.438
PHY-3002 : Step(79): len = 305825, overlap = 264.406
PHY-3002 : Step(80): len = 305228, overlap = 260.25
PHY-3002 : Step(81): len = 304505, overlap = 265.594
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000150269
PHY-3002 : Step(82): len = 308242, overlap = 231
PHY-3002 : Step(83): len = 313905, overlap = 233.438
PHY-3002 : Step(84): len = 316655, overlap = 237.125
PHY-3002 : Step(85): len = 319071, overlap = 245.656
PHY-3002 : Step(86): len = 319294, overlap = 248.031
PHY-3002 : Step(87): len = 319908, overlap = 282.062
PHY-3002 : Step(88): len = 320465, overlap = 267.375
PHY-3002 : Step(89): len = 321011, overlap = 291.094
PHY-3002 : Step(90): len = 320664, overlap = 289.219
PHY-3002 : Step(91): len = 319255, overlap = 288.031
PHY-3002 : Step(92): len = 319688, overlap = 278.156
PHY-3002 : Step(93): len = 318593, overlap = 274.688
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000282395
PHY-3002 : Step(94): len = 320896, overlap = 277.562
PHY-3002 : Step(95): len = 325429, overlap = 273.906
PHY-3002 : Step(96): len = 326951, overlap = 258.938
PHY-3002 : Step(97): len = 328373, overlap = 242.938
PHY-3002 : Step(98): len = 328774, overlap = 226.281
PHY-3002 : Step(99): len = 328841, overlap = 226.406
PHY-3002 : Step(100): len = 328179, overlap = 222.594
PHY-3002 : Step(101): len = 327516, overlap = 217.344
PHY-3002 : Step(102): len = 327807, overlap = 197.281
PHY-3002 : Step(103): len = 328398, overlap = 204.156
PHY-3002 : Step(104): len = 327750, overlap = 203.656
PHY-3002 : Step(105): len = 326860, overlap = 207.281
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.014526s wall, 0.015625s user + 0.031250s system = 0.046875s CPU (322.7%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21966.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 436392, over cnt = 1186(3%), over = 5200, worst = 34
PHY-1001 : End global iterations;  0.873472s wall, 1.109375s user + 0.031250s system = 1.140625s CPU (130.6%)

PHY-1001 : Congestion index: top1 = 68.45, top5 = 50.42, top10 = 41.69, top15 = 36.50.
PHY-3001 : End congestion estimation;  1.092302s wall, 1.312500s user + 0.046875s system = 1.359375s CPU (124.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21964 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.834585s wall, 0.796875s user + 0.031250s system = 0.828125s CPU (99.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.50735e-05
PHY-3002 : Step(106): len = 364982, overlap = 148.188
PHY-3002 : Step(107): len = 381481, overlap = 147.938
PHY-3002 : Step(108): len = 377801, overlap = 136.25
PHY-3002 : Step(109): len = 379597, overlap = 129.594
PHY-3002 : Step(110): len = 383544, overlap = 133.875
PHY-3002 : Step(111): len = 389067, overlap = 126.469
PHY-3002 : Step(112): len = 397430, overlap = 119.5
PHY-3002 : Step(113): len = 398704, overlap = 122.375
PHY-3002 : Step(114): len = 400155, overlap = 126.5
PHY-3002 : Step(115): len = 402140, overlap = 117.656
PHY-3002 : Step(116): len = 403108, overlap = 119.219
PHY-3002 : Step(117): len = 405637, overlap = 117.5
PHY-3002 : Step(118): len = 406598, overlap = 117.719
PHY-3002 : Step(119): len = 407738, overlap = 121.875
PHY-3002 : Step(120): len = 410272, overlap = 121.656
PHY-3002 : Step(121): len = 412151, overlap = 119.219
PHY-3002 : Step(122): len = 412552, overlap = 120.844
PHY-3002 : Step(123): len = 413824, overlap = 120
PHY-3002 : Step(124): len = 413685, overlap = 120.312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000150147
PHY-3002 : Step(125): len = 414532, overlap = 117.406
PHY-3002 : Step(126): len = 416047, overlap = 117.438
PHY-3002 : Step(127): len = 418480, overlap = 116.75
PHY-3002 : Step(128): len = 420985, overlap = 113.5
PHY-3002 : Step(129): len = 422765, overlap = 113.344
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(130): len = 423745, overlap = 115.25
PHY-3002 : Step(131): len = 426278, overlap = 112.906
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 87/21966.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 498472, over cnt = 2150(6%), over = 9205, worst = 43
PHY-1001 : End global iterations;  1.090578s wall, 1.656250s user + 0.015625s system = 1.671875s CPU (153.3%)

PHY-1001 : Congestion index: top1 = 74.12, top5 = 55.05, top10 = 46.90, top15 = 42.13.
PHY-3001 : End congestion estimation;  1.330346s wall, 1.890625s user + 0.015625s system = 1.906250s CPU (143.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21964 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.863684s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.54614e-05
PHY-3002 : Step(132): len = 429117, overlap = 370.375
PHY-3002 : Step(133): len = 437191, overlap = 302.156
PHY-3002 : Step(134): len = 430574, overlap = 279.656
PHY-3002 : Step(135): len = 429688, overlap = 253.094
PHY-3002 : Step(136): len = 429342, overlap = 240.719
PHY-3002 : Step(137): len = 428394, overlap = 235
PHY-3002 : Step(138): len = 426373, overlap = 214.406
PHY-3002 : Step(139): len = 424713, overlap = 218.312
PHY-3002 : Step(140): len = 423090, overlap = 223.594
PHY-3002 : Step(141): len = 421569, overlap = 211.844
PHY-3002 : Step(142): len = 420862, overlap = 216.719
PHY-3002 : Step(143): len = 419781, overlap = 218.125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000190923
PHY-3002 : Step(144): len = 420493, overlap = 205.688
PHY-3002 : Step(145): len = 422232, overlap = 195.75
PHY-3002 : Step(146): len = 423507, overlap = 192.031
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000381846
PHY-3002 : Step(147): len = 426188, overlap = 180.719
PHY-3002 : Step(148): len = 431889, overlap = 174.406
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000763691
PHY-3002 : Step(149): len = 433905, overlap = 158.25
PHY-3002 : Step(150): len = 437816, overlap = 162.812
PHY-3002 : Step(151): len = 442668, overlap = 151
PHY-3002 : Step(152): len = 444010, overlap = 149.375
PHY-3002 : Step(153): len = 444946, overlap = 149.594
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.001476
PHY-3002 : Step(154): len = 445614, overlap = 142.781
PHY-3002 : Step(155): len = 447738, overlap = 145.844
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81533, tnet num: 21964, tinst num: 19599, tnode num: 115249, tedge num: 128074.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.431161s wall, 1.390625s user + 0.031250s system = 1.421875s CPU (99.4%)

RUN-1004 : used memory is 571 MB, reserved memory is 545 MB, peak memory is 707 MB
OPT-1001 : Total overflow 505.59 peak overflow 4.47
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 785/21966.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 535024, over cnt = 2537(7%), over = 8892, worst = 25
PHY-1001 : End global iterations;  1.172570s wall, 1.750000s user + 0.046875s system = 1.796875s CPU (153.2%)

PHY-1001 : Congestion index: top1 = 54.03, top5 = 45.55, top10 = 41.30, top15 = 38.62.
PHY-1001 : End incremental global routing;  1.398147s wall, 1.984375s user + 0.046875s system = 2.031250s CPU (145.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21964 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.897006s wall, 0.875000s user + 0.015625s system = 0.890625s CPU (99.3%)

OPT-1001 : 15 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19521 has valid locations, 249 needs to be replaced
PHY-3001 : design contains 19833 instances, 5701 luts, 12603 seqs, 1424 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 464710
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17039/22200.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 548464, over cnt = 2586(7%), over = 8961, worst = 25
PHY-1001 : End global iterations;  0.191178s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (122.6%)

PHY-1001 : Congestion index: top1 = 54.42, top5 = 45.94, top10 = 41.57, top15 = 38.94.
PHY-3001 : End congestion estimation;  0.409677s wall, 0.468750s user + 0.000000s system = 0.468750s CPU (114.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82310, tnet num: 22198, tinst num: 19833, tnode num: 116339, tedge num: 129160.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.456892s wall, 1.453125s user + 0.000000s system = 1.453125s CPU (99.7%)

RUN-1004 : used memory is 612 MB, reserved memory is 606 MB, peak memory is 707 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22198 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.410120s wall, 2.359375s user + 0.046875s system = 2.406250s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(156): len = 464604, overlap = 2.0625
PHY-3002 : Step(157): len = 465698, overlap = 2.0625
PHY-3002 : Step(158): len = 466896, overlap = 2.1875
PHY-3002 : Step(159): len = 467785, overlap = 2.125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17066/22200.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 547192, over cnt = 2597(7%), over = 9019, worst = 25
PHY-1001 : End global iterations;  0.197249s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (103.0%)

PHY-1001 : Congestion index: top1 = 54.70, top5 = 46.11, top10 = 41.85, top15 = 39.17.
PHY-3001 : End congestion estimation;  0.493925s wall, 0.484375s user + 0.015625s system = 0.500000s CPU (101.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22198 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.931692s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000872737
PHY-3002 : Step(160): len = 467983, overlap = 148.562
PHY-3002 : Step(161): len = 468573, overlap = 147.906
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00174547
PHY-3002 : Step(162): len = 468816, overlap = 147.969
PHY-3002 : Step(163): len = 469124, overlap = 147.906
PHY-3001 : Final: Len = 469124, Over = 147.906
PHY-3001 : End incremental placement;  5.104928s wall, 5.328125s user + 0.187500s system = 5.515625s CPU (108.0%)

OPT-1001 : Total overflow 510.53 peak overflow 4.47
OPT-1001 : End high-fanout net optimization;  7.892015s wall, 8.828125s user + 0.250000s system = 9.078125s CPU (115.0%)

OPT-1001 : Current memory(MB): used = 710, reserve = 690, peak = 726.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17084/22200.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 550584, over cnt = 2521(7%), over = 8418, worst = 25
PHY-1002 : len = 596200, over cnt = 1736(4%), over = 4058, worst = 16
PHY-1002 : len = 637576, over cnt = 506(1%), over = 997, worst = 12
PHY-1002 : len = 645144, over cnt = 282(0%), over = 546, worst = 11
PHY-1002 : len = 654416, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.322428s wall, 1.765625s user + 0.000000s system = 1.765625s CPU (133.5%)

PHY-1001 : Congestion index: top1 = 47.63, top5 = 41.93, top10 = 39.17, top15 = 37.32.
OPT-1001 : End congestion update;  1.551951s wall, 2.015625s user + 0.000000s system = 2.015625s CPU (129.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22198 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.784431s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (99.6%)

OPT-0007 : Start: WNS 3969 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.341813s wall, 2.796875s user + 0.000000s system = 2.796875s CPU (119.4%)

OPT-1001 : Current memory(MB): used = 711, reserve = 690, peak = 726.
OPT-1001 : End physical optimization;  11.951054s wall, 13.468750s user + 0.296875s system = 13.765625s CPU (115.2%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5701 LUT to BLE ...
SYN-4008 : Packed 5701 LUT and 2860 SEQ to BLE.
SYN-4003 : Packing 9743 remaining SEQ's ...
SYN-4005 : Packed 3221 SEQ with LUT/SLICE
SYN-4006 : 138 single LUT's are left
SYN-4006 : 6522 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 12223/13874 primitive instances ...
PHY-3001 : End packing;  2.786162s wall, 2.781250s user + 0.000000s system = 2.781250s CPU (99.8%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8244 instances
RUN-1001 : 4069 mslices, 4068 lslices, 60 pads, 42 brams, 0 dsps
RUN-1001 : There are total 19382 nets
RUN-1001 : 13541 nets have 2 pins
RUN-1001 : 4480 nets have [3 - 5] pins
RUN-1001 : 853 nets have [6 - 10] pins
RUN-1001 : 350 nets have [11 - 20] pins
RUN-1001 : 149 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8242 instances, 8137 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Cell area utilization is 86%
PHY-3001 : After packing: Len = 487018, Over = 380.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7910/19382.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 621904, over cnt = 1627(4%), over = 2551, worst = 9
PHY-1002 : len = 627552, over cnt = 1084(3%), over = 1519, worst = 5
PHY-1002 : len = 641472, over cnt = 337(0%), over = 437, worst = 5
PHY-1002 : len = 648816, over cnt = 30(0%), over = 34, worst = 2
PHY-1002 : len = 649520, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.144002s wall, 1.796875s user + 0.015625s system = 1.812500s CPU (158.4%)

PHY-1001 : Congestion index: top1 = 49.63, top5 = 42.60, top10 = 39.40, top15 = 37.24.
PHY-3001 : End congestion estimation;  1.436767s wall, 2.093750s user + 0.015625s system = 2.109375s CPU (146.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67878, tnet num: 19380, tinst num: 8242, tnode num: 92649, tedge num: 111801.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.674907s wall, 1.656250s user + 0.031250s system = 1.687500s CPU (100.8%)

RUN-1004 : used memory is 610 MB, reserved memory is 603 MB, peak memory is 726 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19380 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.508491s wall, 2.484375s user + 0.031250s system = 2.515625s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.62111e-05
PHY-3002 : Step(164): len = 489145, overlap = 361
PHY-3002 : Step(165): len = 488164, overlap = 379
PHY-3002 : Step(166): len = 487389, overlap = 378.5
PHY-3002 : Step(167): len = 487028, overlap = 389.25
PHY-3002 : Step(168): len = 485908, overlap = 395.5
PHY-3002 : Step(169): len = 485063, overlap = 397.5
PHY-3002 : Step(170): len = 485172, overlap = 401.25
PHY-3002 : Step(171): len = 482961, overlap = 398.25
PHY-3002 : Step(172): len = 482083, overlap = 408.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.24222e-05
PHY-3002 : Step(173): len = 485424, overlap = 400.25
PHY-3002 : Step(174): len = 488028, overlap = 394
PHY-3002 : Step(175): len = 488669, overlap = 387.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000174128
PHY-3002 : Step(176): len = 498248, overlap = 375.5
PHY-3002 : Step(177): len = 508376, overlap = 363.75
PHY-3002 : Step(178): len = 505878, overlap = 361.25
PHY-3002 : Step(179): len = 504513, overlap = 362
PHY-3002 : Step(180): len = 504583, overlap = 362.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.593674s wall, 0.640625s user + 0.796875s system = 1.437500s CPU (242.1%)

PHY-3001 : Trial Legalized: Len = 621349
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 552/19382.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 710016, over cnt = 2426(6%), over = 3880, worst = 7
PHY-1002 : len = 724288, over cnt = 1394(3%), over = 2012, worst = 7
PHY-1002 : len = 736568, over cnt = 792(2%), over = 1088, worst = 6
PHY-1002 : len = 748464, over cnt = 312(0%), over = 393, worst = 3
PHY-1002 : len = 755240, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.802991s wall, 2.843750s user + 0.031250s system = 2.875000s CPU (159.5%)

PHY-1001 : Congestion index: top1 = 51.51, top5 = 45.75, top10 = 42.77, top15 = 40.80.
PHY-3001 : End congestion estimation;  2.124142s wall, 3.140625s user + 0.046875s system = 3.187500s CPU (150.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19380 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.801120s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (101.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00019887
PHY-3002 : Step(181): len = 578358, overlap = 87.25
PHY-3002 : Step(182): len = 559363, overlap = 131.5
PHY-3002 : Step(183): len = 546756, overlap = 189.5
PHY-3002 : Step(184): len = 539307, overlap = 226.75
PHY-3002 : Step(185): len = 535851, overlap = 249
PHY-3002 : Step(186): len = 533512, overlap = 261.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000397741
PHY-3002 : Step(187): len = 538316, overlap = 261.25
PHY-3002 : Step(188): len = 544257, overlap = 261.25
PHY-3002 : Step(189): len = 545648, overlap = 256.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000795481
PHY-3002 : Step(190): len = 548493, overlap = 256.25
PHY-3002 : Step(191): len = 554744, overlap = 255.25
PHY-3002 : Step(192): len = 563557, overlap = 261.25
PHY-3002 : Step(193): len = 564564, overlap = 256.75
PHY-3002 : Step(194): len = 564127, overlap = 263.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.033196s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (94.1%)

PHY-3001 : Legalized: Len = 603021, Over = 0
PHY-3001 : Spreading special nets. 45 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.074128s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (105.4%)

PHY-3001 : 62 instances has been re-located, deltaX = 15, deltaY = 45, maxDist = 2.
PHY-3001 : Final: Len = 604085, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67878, tnet num: 19380, tinst num: 8242, tnode num: 92649, tedge num: 111801.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.877560s wall, 1.859375s user + 0.015625s system = 1.875000s CPU (99.9%)

RUN-1004 : used memory is 605 MB, reserved memory is 592 MB, peak memory is 726 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3247/19382.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 703696, over cnt = 2319(6%), over = 3688, worst = 9
PHY-1002 : len = 715080, over cnt = 1449(4%), over = 2043, worst = 6
PHY-1002 : len = 729280, over cnt = 676(1%), over = 936, worst = 5
PHY-1002 : len = 737776, over cnt = 319(0%), over = 430, worst = 4
PHY-1002 : len = 745744, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.718587s wall, 2.703125s user + 0.062500s system = 2.765625s CPU (160.9%)

PHY-1001 : Congestion index: top1 = 48.75, top5 = 43.69, top10 = 40.80, top15 = 39.03.
PHY-1001 : End incremental global routing;  1.997098s wall, 2.968750s user + 0.062500s system = 3.031250s CPU (151.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19380 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.832837s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (101.3%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8177 has valid locations, 13 needs to be replaced
PHY-3001 : design contains 8253 instances, 8148 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 607166
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17495/19400.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 749208, over cnt = 34(0%), over = 42, worst = 5
PHY-1002 : len = 749152, over cnt = 17(0%), over = 17, worst = 1
PHY-1002 : len = 749208, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 749216, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 749456, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.603261s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (111.4%)

PHY-1001 : Congestion index: top1 = 48.99, top5 = 43.80, top10 = 40.91, top15 = 39.13.
PHY-3001 : End congestion estimation;  0.869476s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (107.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67982, tnet num: 19398, tinst num: 8253, tnode num: 92778, tedge num: 111959.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.857723s wall, 1.812500s user + 0.031250s system = 1.843750s CPU (99.2%)

RUN-1004 : used memory is 635 MB, reserved memory is 613 MB, peak memory is 726 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19398 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.703591s wall, 2.656250s user + 0.046875s system = 2.703125s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(195): len = 606651, overlap = 0.5
PHY-3002 : Step(196): len = 606618, overlap = 0.25
PHY-3002 : Step(197): len = 606377, overlap = 0.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17490/19400.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 747632, over cnt = 18(0%), over = 29, worst = 5
PHY-1002 : len = 747744, over cnt = 9(0%), over = 10, worst = 2
PHY-1002 : len = 747856, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 747888, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.459592s wall, 0.531250s user + 0.015625s system = 0.546875s CPU (119.0%)

PHY-1001 : Congestion index: top1 = 48.86, top5 = 43.70, top10 = 40.85, top15 = 39.09.
PHY-3001 : End congestion estimation;  0.726662s wall, 0.781250s user + 0.015625s system = 0.796875s CPU (109.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19398 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.798309s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000565549
PHY-3002 : Step(198): len = 606288, overlap = 2.25
PHY-3002 : Step(199): len = 606393, overlap = 2
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005323s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 606401, Over = 0
PHY-3001 : End spreading;  0.063602s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.3%)

PHY-3001 : Final: Len = 606401, Over = 0
PHY-3001 : End incremental placement;  5.713300s wall, 5.859375s user + 0.140625s system = 6.000000s CPU (105.0%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  8.986450s wall, 10.109375s user + 0.203125s system = 10.312500s CPU (114.8%)

OPT-1001 : Current memory(MB): used = 719, reserve = 703, peak = 726.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17493/19400.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 748608, over cnt = 22(0%), over = 29, worst = 3
PHY-1002 : len = 748648, over cnt = 14(0%), over = 16, worst = 2
PHY-1002 : len = 748696, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 748696, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 748728, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.596746s wall, 0.625000s user + 0.000000s system = 0.625000s CPU (104.7%)

PHY-1001 : Congestion index: top1 = 48.90, top5 = 43.71, top10 = 40.85, top15 = 39.08.
OPT-1001 : End congestion update;  0.857620s wall, 0.890625s user + 0.000000s system = 0.890625s CPU (103.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19398 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.692344s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (99.3%)

OPT-0007 : Start: WNS 4089 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.554220s wall, 1.578125s user + 0.000000s system = 1.578125s CPU (101.5%)

OPT-1001 : Current memory(MB): used = 719, reserve = 703, peak = 726.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19398 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.675335s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (99.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17506/19400.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 748728, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.099662s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (94.1%)

PHY-1001 : Congestion index: top1 = 48.90, top5 = 43.71, top10 = 40.85, top15 = 39.08.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19398 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.679777s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (98.8%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4089 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 48.517241
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4089ps with logic level 4 
OPT-1001 : End physical optimization;  14.370806s wall, 15.500000s user + 0.234375s system = 15.734375s CPU (109.5%)

RUN-1003 : finish command "place" in  64.128068s wall, 101.281250s user + 6.234375s system = 107.515625s CPU (167.7%)

RUN-1004 : used memory is 636 MB, reserved memory is 614 MB, peak memory is 726 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.512116s wall, 2.656250s user + 0.015625s system = 2.671875s CPU (176.7%)

RUN-1004 : used memory is 636 MB, reserved memory is 615 MB, peak memory is 726 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8255 instances
RUN-1001 : 4069 mslices, 4079 lslices, 60 pads, 42 brams, 0 dsps
RUN-1001 : There are total 19400 nets
RUN-1001 : 13546 nets have 2 pins
RUN-1001 : 4480 nets have [3 - 5] pins
RUN-1001 : 859 nets have [6 - 10] pins
RUN-1001 : 356 nets have [11 - 20] pins
RUN-1001 : 150 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67982, tnet num: 19398, tinst num: 8253, tnode num: 92778, tedge num: 111959.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.742247s wall, 1.718750s user + 0.000000s system = 1.718750s CPU (98.7%)

RUN-1004 : used memory is 619 MB, reserved memory is 597 MB, peak memory is 726 MB
PHY-1001 : 4069 mslices, 4079 lslices, 60 pads, 42 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19398 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 686328, over cnt = 2401(6%), over = 3930, worst = 9
PHY-1002 : len = 698024, over cnt = 1589(4%), over = 2409, worst = 7
PHY-1002 : len = 718448, over cnt = 677(1%), over = 972, worst = 5
PHY-1002 : len = 734296, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 734392, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.723650s wall, 2.765625s user + 0.078125s system = 2.843750s CPU (165.0%)

PHY-1001 : Congestion index: top1 = 48.47, top5 = 43.15, top10 = 40.32, top15 = 38.57.
PHY-1001 : End global routing;  2.030134s wall, 3.078125s user + 0.078125s system = 3.156250s CPU (155.5%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 701, reserve = 688, peak = 726.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net DATA/DIV_Dtemp/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 970, reserve = 956, peak = 970.
PHY-1001 : End build detailed router design. 4.334085s wall, 4.312500s user + 0.015625s system = 4.328125s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 192160, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.787053s wall, 0.765625s user + 0.015625s system = 0.781250s CPU (99.3%)

PHY-1001 : Current memory(MB): used = 1006, reserve = 993, peak = 1006.
PHY-1001 : End phase 1; 0.795107s wall, 0.781250s user + 0.015625s system = 0.796875s CPU (100.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 55% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.79284e+06, over cnt = 1303(0%), over = 1310, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1023, reserve = 1009, peak = 1023.
PHY-1001 : End initial routed; 17.927900s wall, 47.578125s user + 0.437500s system = 48.015625s CPU (267.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18196(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.011   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.292735s wall, 3.265625s user + 0.015625s system = 3.281250s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 1033, reserve = 1019, peak = 1033.
PHY-1001 : End phase 2; 21.220775s wall, 50.843750s user + 0.453125s system = 51.296875s CPU (241.7%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.79284e+06, over cnt = 1303(0%), over = 1310, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.219472s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (99.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.7776e+06, over cnt = 413(0%), over = 413, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 1.153880s wall, 1.750000s user + 0.000000s system = 1.750000s CPU (151.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.77738e+06, over cnt = 110(0%), over = 110, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.494058s wall, 0.625000s user + 0.000000s system = 0.625000s CPU (126.5%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.77908e+06, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.245977s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (120.7%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.77911e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.284983s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (98.7%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.77918e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.151517s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (103.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18196(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.037   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.381568s wall, 3.390625s user + 0.000000s system = 3.390625s CPU (100.3%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 318 feed throughs used by 275 nets
PHY-1001 : End commit to database; 2.226422s wall, 2.218750s user + 0.000000s system = 2.218750s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 1121, reserve = 1111, peak = 1121.
PHY-1001 : End phase 3; 8.659259s wall, 9.421875s user + 0.000000s system = 9.421875s CPU (108.8%)

PHY-1003 : Routed, final wirelength = 1.77918e+06
PHY-1001 : Current memory(MB): used = 1126, reserve = 1115, peak = 1126.
PHY-1001 : End export database. 0.057773s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (81.1%)

PHY-1001 : End detail routing;  35.473431s wall, 65.828125s user + 0.484375s system = 66.312500s CPU (186.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67982, tnet num: 19398, tinst num: 8253, tnode num: 92778, tedge num: 111959.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.817328s wall, 1.812500s user + 0.000000s system = 1.812500s CPU (99.7%)

RUN-1004 : used memory is 1061 MB, reserved memory is 1058 MB, peak memory is 1126 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  43.539397s wall, 74.921875s user + 0.578125s system = 75.500000s CPU (173.4%)

RUN-1004 : used memory is 1060 MB, reserved memory is 1058 MB, peak memory is 1126 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8697   out of  19600   44.37%
#reg                    12711   out of  19600   64.85%
#le                     15181
  #lut only              2470   out of  15181   16.27%
  #reg only              6484   out of  15181   42.71%
  #lut&reg               6227   out of  15181   41.02%
#dsp                        0   out of     29    0.00%
#bram                      42   out of     64   65.62%
  #bram9k                  42
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                     9
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet              Type               DriverType         Driver                    Fanout
#1        DATA/DIV_Dtemp/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6961
#2        config_inst_syn_9     GCLK               config             config_inst.jtck          176
#3        clk_in_dup_1          GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         M3        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          NONE       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         P6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15181  |7273    |1424    |12753   |42      |0       |
|  AnyFog_dataX                      |AnyFog          |204    |80      |22      |166     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |83     |50      |22      |45      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |205    |87      |22      |170     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |84     |58      |22      |49      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |201    |77      |22      |163     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |83     |57      |22      |45      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |3478   |937     |34      |3400    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |727    |105     |5       |713     |0       |0       |
|    STADOP_com2                     |STADOP          |553    |38      |0       |548     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |48      |14      |42      |0       |0       |
|    head_com2                       |uniheading      |269    |58      |5       |258     |0       |0       |
|    rmc_com2                        |Gprmc           |153    |37      |0       |149     |0       |0       |
|    uart_com2                       |Agrica          |1422   |362     |10      |1401    |0       |0       |
|  DATA                              |Data_Processing |8622   |4286    |1062    |6964    |0       |0       |
|    DIV_Dtemp                       |Divider         |743    |299     |84      |621     |0       |0       |
|    DIV_Utemp                       |Divider         |592    |344     |84      |467     |0       |0       |
|    DIV_accX                        |Divider         |603    |315     |84      |480     |0       |0       |
|    DIV_accY                        |Divider         |673    |349     |111     |505     |0       |0       |
|    DIV_accZ                        |Divider         |696    |368     |132     |492     |0       |0       |
|    DIV_rateX                       |Divider         |679    |372     |132     |476     |0       |0       |
|    DIV_rateY                       |Divider         |582    |352     |132     |376     |0       |0       |
|    DIV_rateZ                       |Divider         |578    |386     |132     |377     |0       |0       |
|    genclk                          |genclk          |101    |56      |20      |60      |0       |0       |
|  FMC                               |FMC_Ctrl        |473    |418     |43      |353     |0       |0       |
|  IIC                               |I2C_master      |289    |243     |11      |249     |0       |0       |
|  IMU_CTRL                          |SCHA634         |926    |674     |61      |728     |0       |0       |
|    CtrlData                        |CtrlData        |484    |430     |47      |327     |0       |0       |
|      usms                          |Time_1ms        |27     |22      |5       |18      |0       |0       |
|    SPIM                            |SPI_SCHA634     |442    |244     |14      |401     |0       |0       |
|  POWER                             |POWER_EN        |93     |47      |38      |31      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |672    |422     |109     |469     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |672    |422     |109     |469     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |300    |177     |0       |285     |0       |0       |
|        reg_inst                    |register        |297    |174     |0       |282     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |372    |245     |109     |184     |0       |0       |
|        bus_inst                    |bus_top         |151    |98      |52      |62      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |25     |15      |10      |9       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |4      |4       |0       |4       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |15     |9       |6       |7       |0       |0       |
|          BUS_DETECTOR[5]$bus_nodes |bus_det         |52     |33      |18      |20      |0       |0       |
|          BUS_DETECTOR[6]$bus_nodes |bus_det         |51     |33      |18      |18      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |140    |106     |29      |91      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13485  
    #2          2       3552   
    #3          3        660   
    #4          4        268   
    #5        5-10       905   
    #6        11-50      444   
    #7       51-100      16    
    #8       101-500      3    
    #9        >500        2    
  Average     2.14             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  1.995098s wall, 3.484375s user + 0.078125s system = 3.562500s CPU (178.6%)

RUN-1004 : used memory is 1061 MB, reserved memory is 1059 MB, peak memory is 1126 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67982, tnet num: 19398, tinst num: 8253, tnode num: 92778, tedge num: 111959.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.756811s wall, 1.750000s user + 0.000000s system = 1.750000s CPU (99.6%)

RUN-1004 : used memory is 1063 MB, reserved memory is 1060 MB, peak memory is 1126 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19398 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.357945s wall, 1.328125s user + 0.031250s system = 1.359375s CPU (100.1%)

RUN-1004 : used memory is 1068 MB, reserved memory is 1064 MB, peak memory is 1126 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 8f169df8acfcb835781910b1ac29267cd80e16d8a5e1b8cf9d032ec93c0a2865 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8253
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19400, pip num: 149201
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 318
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3252 valid insts, and 415529 bits set as '1'.
BIT-1004 : the usercode register value: 00000000110101000001111110010110
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  11.731665s wall, 110.000000s user + 0.250000s system = 110.250000s CPU (939.8%)

RUN-1004 : used memory is 1196 MB, reserved memory is 1180 MB, peak memory is 1311 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250515_082845.log"

============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Jul 31 10:03:40 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.165550s wall, 1.562500s user + 3.609375s system = 5.171875s CPU (100.1%)

RUN-1004 : used memory is 78 MB, reserved memory is 40 MB, peak memory is 89 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.752250s wall, 1.671875s user + 0.078125s system = 1.750000s CPU (99.9%)

RUN-1004 : used memory is 299 MB, reserved memory is 268 MB, peak memory is 302 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 2 view nodes, 24 trigger nets, 24 data nets.
KIT-1004 : Chipwatcher code = 1010010110001101
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=78) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=78) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=78)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=78)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22207/12 useful/useless nets, 19222/4 useful/useless insts
SYN-1016 : Merged 17 instances.
SYN-1032 : 21983/16 useful/useless nets, 19546/12 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 2 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 2 mux instances.
SYN-1015 : Optimize round 1, 279 better
SYN-1014 : Optimize round 2
SYN-1032 : 21774/30 useful/useless nets, 19337/32 useful/useless insts
SYN-1015 : Optimize round 2, 64 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  1.930023s wall, 1.875000s user + 0.046875s system = 1.921875s CPU (99.6%)

RUN-1004 : used memory is 324 MB, reserved memory is 291 MB, peak memory is 326 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 21798/155 useful/useless nets, 19382/29 useful/useless insts
SYN-1016 : Merged 21 instances.
SYN-2571 : Optimize after map_dsp, round 1, 205 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 24 instances.
SYN-2501 : Optimize round 1, 50 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 14 macro adder
SYN-1019 : Optimized 9 mux instances.
SYN-1016 : Merged 10 instances.
SYN-1032 : 22169/5 useful/useless nets, 19753/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80405, tnet num: 22169, tinst num: 19752, tnode num: 113005, tedge num: 125646.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.127985s wall, 1.078125s user + 0.078125s system = 1.156250s CPU (102.5%)

RUN-1004 : used memory is 460 MB, reserved memory is 428 MB, peak memory is 460 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22169 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 173 (3.57), #lev = 7 (1.79)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 173 (3.57), #lev = 7 (1.79)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 387 instances into 173 LUTs, name keeping = 77%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 290 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 108 adder to BLE ...
SYN-4008 : Packed 108 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  3.893997s wall, 3.796875s user + 0.109375s system = 3.906250s CPU (100.3%)

RUN-1004 : used memory is 360 MB, reserved memory is 342 MB, peak memory is 567 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  6.113557s wall, 5.921875s user + 0.187500s system = 6.109375s CPU (99.9%)

RUN-1004 : used memory is 361 MB, reserved memory is 342 MB, peak memory is 567 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (177 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19061 instances
RUN-0007 : 5505 luts, 12036 seqs, 937 mslices, 494 lslices, 60 pads, 24 brams, 0 dsps
RUN-1001 : There are total 21505 nets
RUN-1001 : 16210 nets have 2 pins
RUN-1001 : 4131 nets have [3 - 5] pins
RUN-1001 : 809 nets have [6 - 10] pins
RUN-1001 : 229 nets have [11 - 20] pins
RUN-1001 : 108 nets have [21 - 99] pins
RUN-1001 : 18 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4787     
RUN-1001 :   No   |  No   |  Yes  |     631     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     349     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19059 instances, 5505 luts, 12036 seqs, 1431 slices, 283 macros(1431 instances: 937 mslices 494 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 61%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 78968, tnet num: 21503, tinst num: 19059, tnode num: 111162, tedge num: 124050.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.112102s wall, 1.109375s user + 0.000000s system = 1.109375s CPU (99.8%)

RUN-1004 : used memory is 518 MB, reserved memory is 492 MB, peak memory is 567 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21503 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.878871s wall, 1.875000s user + 0.000000s system = 1.875000s CPU (99.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.50108e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19059.
PHY-3001 : Level 1 #clusters 2139.
PHY-3001 : End clustering;  0.127502s wall, 0.234375s user + 0.031250s system = 0.265625s CPU (208.3%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 61%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 840757, overlap = 587.594
PHY-3002 : Step(2): len = 757251, overlap = 625.531
PHY-3002 : Step(3): len = 485834, overlap = 791.938
PHY-3002 : Step(4): len = 434227, overlap = 855.844
PHY-3002 : Step(5): len = 335520, overlap = 952.875
PHY-3002 : Step(6): len = 303931, overlap = 1035.88
PHY-3002 : Step(7): len = 255869, overlap = 1095.72
PHY-3002 : Step(8): len = 227500, overlap = 1137.5
PHY-3002 : Step(9): len = 208831, overlap = 1196.72
PHY-3002 : Step(10): len = 185150, overlap = 1235.94
PHY-3002 : Step(11): len = 174754, overlap = 1258.78
PHY-3002 : Step(12): len = 158218, overlap = 1327.25
PHY-3002 : Step(13): len = 148529, overlap = 1346.91
PHY-3002 : Step(14): len = 135132, overlap = 1362.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.3544e-06
PHY-3002 : Step(15): len = 144046, overlap = 1349.88
PHY-3002 : Step(16): len = 195749, overlap = 1191.72
PHY-3002 : Step(17): len = 205041, overlap = 1094.25
PHY-3002 : Step(18): len = 203165, overlap = 1034.47
PHY-3002 : Step(19): len = 197896, overlap = 1031.84
PHY-3002 : Step(20): len = 192018, overlap = 1044.06
PHY-3002 : Step(21): len = 185967, overlap = 1040.06
PHY-3002 : Step(22): len = 181156, overlap = 1031.19
PHY-3002 : Step(23): len = 178329, overlap = 1033.72
PHY-3002 : Step(24): len = 175344, overlap = 1020.69
PHY-3002 : Step(25): len = 173550, overlap = 1001.34
PHY-3002 : Step(26): len = 171465, overlap = 996.344
PHY-3002 : Step(27): len = 170677, overlap = 983.188
PHY-3002 : Step(28): len = 169698, overlap = 982.281
PHY-3002 : Step(29): len = 170333, overlap = 957.875
PHY-3002 : Step(30): len = 170515, overlap = 942.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.70881e-06
PHY-3002 : Step(31): len = 177101, overlap = 924.906
PHY-3002 : Step(32): len = 192405, overlap = 853.562
PHY-3002 : Step(33): len = 197612, overlap = 799.656
PHY-3002 : Step(34): len = 199269, overlap = 778.531
PHY-3002 : Step(35): len = 198887, overlap = 757.969
PHY-3002 : Step(36): len = 197989, overlap = 749.938
PHY-3002 : Step(37): len = 196155, overlap = 748.062
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 5.41761e-06
PHY-3002 : Step(38): len = 207664, overlap = 694.312
PHY-3002 : Step(39): len = 224339, overlap = 616.562
PHY-3002 : Step(40): len = 230489, overlap = 593.469
PHY-3002 : Step(41): len = 232450, overlap = 597.375
PHY-3002 : Step(42): len = 230601, overlap = 593.844
PHY-3002 : Step(43): len = 228054, overlap = 597.062
PHY-3002 : Step(44): len = 226234, overlap = 601.438
PHY-3002 : Step(45): len = 225223, overlap = 624.969
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 1.08352e-05
PHY-3002 : Step(46): len = 234898, overlap = 576.156
PHY-3002 : Step(47): len = 249257, overlap = 544.531
PHY-3002 : Step(48): len = 254460, overlap = 513.781
PHY-3002 : Step(49): len = 256595, overlap = 504.156
PHY-3002 : Step(50): len = 255730, overlap = 496.531
PHY-3002 : Step(51): len = 255125, overlap = 496.188
PHY-3002 : Step(52): len = 253267, overlap = 504.562
PHY-3002 : Step(53): len = 252154, overlap = 498.625
PHY-3002 : Step(54): len = 250406, overlap = 485.656
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 2.16705e-05
PHY-3002 : Step(55): len = 262341, overlap = 472.875
PHY-3002 : Step(56): len = 276111, overlap = 441.625
PHY-3002 : Step(57): len = 280463, overlap = 384.844
PHY-3002 : Step(58): len = 282493, overlap = 375.875
PHY-3002 : Step(59): len = 281640, overlap = 368.375
PHY-3002 : Step(60): len = 279772, overlap = 360.594
PHY-3002 : Step(61): len = 278177, overlap = 339.562
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 4.33409e-05
PHY-3002 : Step(62): len = 288541, overlap = 295.781
PHY-3002 : Step(63): len = 299156, overlap = 314.969
PHY-3002 : Step(64): len = 303291, overlap = 289.062
PHY-3002 : Step(65): len = 304856, overlap = 286.969
PHY-3002 : Step(66): len = 303060, overlap = 285.219
PHY-3002 : Step(67): len = 300525, overlap = 294.031
PHY-3002 : Step(68): len = 297621, overlap = 295.469
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 8.66818e-05
PHY-3002 : Step(69): len = 305154, overlap = 275.25
PHY-3002 : Step(70): len = 313245, overlap = 248.656
PHY-3002 : Step(71): len = 315933, overlap = 239.688
PHY-3002 : Step(72): len = 316717, overlap = 229.438
PHY-3002 : Step(73): len = 313960, overlap = 236.938
PHY-3002 : Step(74): len = 312311, overlap = 249.219
PHY-3002 : Step(75): len = 310452, overlap = 255.562
PHY-3002 : Step(76): len = 311449, overlap = 259.469
PHY-3002 : Step(77): len = 310887, overlap = 262.062
PHY-3002 : Step(78): len = 311442, overlap = 261.469
PHY-3002 : Step(79): len = 310527, overlap = 265.656
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000171136
PHY-3002 : Step(80): len = 314956, overlap = 255.375
PHY-3002 : Step(81): len = 321013, overlap = 268.969
PHY-3002 : Step(82): len = 323723, overlap = 243.406
PHY-3002 : Step(83): len = 324675, overlap = 235.969
PHY-3002 : Step(84): len = 322637, overlap = 243
PHY-3002 : Step(85): len = 321701, overlap = 254.938
PHY-3002 : Step(86): len = 320301, overlap = 261.188
PHY-3002 : Step(87): len = 320799, overlap = 258.562
PHY-3002 : Step(88): len = 320567, overlap = 250.875
PHY-3002 : Step(89): len = 321343, overlap = 242.469
PHY-3002 : Step(90): len = 320665, overlap = 255.438
PHY-3002 : Step(91): len = 320818, overlap = 258.25
PHY-3002 : Step(92): len = 320022, overlap = 252.094
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000328629
PHY-3002 : Step(93): len = 323127, overlap = 255.219
PHY-3002 : Step(94): len = 328058, overlap = 245.031
PHY-3002 : Step(95): len = 329847, overlap = 240.562
PHY-3002 : Step(96): len = 330510, overlap = 247.281
PHY-3002 : Step(97): len = 329372, overlap = 260.375
PHY-3002 : Step(98): len = 328762, overlap = 261.5
PHY-3002 : Step(99): len = 327893, overlap = 255.75
PHY-3002 : Step(100): len = 328534, overlap = 254.781
PHY-3002 : Step(101): len = 328252, overlap = 258.5
PHY-3002 : Step(102): len = 328385, overlap = 249.531
PHY-3002 : Step(103): len = 327783, overlap = 250.812
PHY-3002 : Step(104): len = 328006, overlap = 246.406
PHY-3002 : Step(105): len = 328326, overlap = 238.75
PHY-3002 : Step(106): len = 328736, overlap = 237.312
PHY-3002 : Step(107): len = 328528, overlap = 233.219
PHY-3002 : Step(108): len = 328979, overlap = 231.688
PHY-3002 : Step(109): len = 328883, overlap = 235.438
PHY-3002 : Step(110): len = 328906, overlap = 226.375
PHY-3002 : Step(111): len = 328632, overlap = 235
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(112): len = 329449, overlap = 233.5
PHY-3002 : Step(113): len = 330832, overlap = 227.625
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.012204s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21505.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 423024, over cnt = 1137(3%), over = 5030, worst = 46
PHY-1001 : End global iterations;  0.768874s wall, 0.968750s user + 0.015625s system = 0.984375s CPU (128.0%)

PHY-1001 : Congestion index: top1 = 73.34, top5 = 50.60, top10 = 41.48, top15 = 36.28.
PHY-3001 : End congestion estimation;  0.968238s wall, 1.140625s user + 0.046875s system = 1.187500s CPU (122.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21503 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.833719s wall, 0.812500s user + 0.015625s system = 0.828125s CPU (99.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000123387
PHY-3002 : Step(114): len = 372002, overlap = 221.219
PHY-3002 : Step(115): len = 388188, overlap = 207.531
PHY-3002 : Step(116): len = 388316, overlap = 208.531
PHY-3002 : Step(117): len = 384008, overlap = 200.125
PHY-3002 : Step(118): len = 391035, overlap = 192.125
PHY-3002 : Step(119): len = 397223, overlap = 184.406
PHY-3002 : Step(120): len = 398122, overlap = 171.312
PHY-3002 : Step(121): len = 400837, overlap = 158.812
PHY-3002 : Step(122): len = 403390, overlap = 150.688
PHY-3002 : Step(123): len = 405402, overlap = 142.781
PHY-3002 : Step(124): len = 404547, overlap = 131.156
PHY-3002 : Step(125): len = 403915, overlap = 128
PHY-3002 : Step(126): len = 404796, overlap = 121.469
PHY-3002 : Step(127): len = 407101, overlap = 116.938
PHY-3002 : Step(128): len = 407959, overlap = 116.75
PHY-3002 : Step(129): len = 409387, overlap = 119.188
PHY-3002 : Step(130): len = 411544, overlap = 125.844
PHY-3002 : Step(131): len = 412843, overlap = 130.562
PHY-3002 : Step(132): len = 414794, overlap = 136.438
PHY-3002 : Step(133): len = 416489, overlap = 137.25
PHY-3002 : Step(134): len = 415965, overlap = 131.438
PHY-3002 : Step(135): len = 416117, overlap = 130.562
PHY-3002 : Step(136): len = 417319, overlap = 126.125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000246774
PHY-3002 : Step(137): len = 416764, overlap = 122.938
PHY-3002 : Step(138): len = 418365, overlap = 119.5
PHY-3002 : Step(139): len = 419190, overlap = 117.281
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000493548
PHY-3002 : Step(140): len = 423964, overlap = 105.469
PHY-3002 : Step(141): len = 431344, overlap = 94.0938
PHY-3002 : Step(142): len = 437609, overlap = 90.2812
PHY-3002 : Step(143): len = 438173, overlap = 90.9062
PHY-3002 : Step(144): len = 439706, overlap = 89.8125
PHY-3002 : Step(145): len = 437759, overlap = 87.3438
PHY-3002 : Step(146): len = 435380, overlap = 83.0625
PHY-3002 : Step(147): len = 434998, overlap = 83.9062
PHY-3002 : Step(148): len = 433646, overlap = 87.9688
PHY-3002 : Step(149): len = 431411, overlap = 91.6562
PHY-3002 : Step(150): len = 431344, overlap = 95.7812
PHY-3002 : Step(151): len = 431847, overlap = 102.781
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(152): len = 431913, overlap = 101.656
PHY-3002 : Step(153): len = 433848, overlap = 97.3125
PHY-3002 : Step(154): len = 437825, overlap = 91.125
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(155): len = 437613, overlap = 86.5938
PHY-3002 : Step(156): len = 440674, overlap = 85.625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 46/21505.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 500768, over cnt = 2161(6%), over = 9832, worst = 32
PHY-1001 : End global iterations;  0.907621s wall, 1.515625s user + 0.000000s system = 1.515625s CPU (167.0%)

PHY-1001 : Congestion index: top1 = 73.53, top5 = 57.32, top10 = 49.07, top15 = 43.99.
PHY-3001 : End congestion estimation;  1.172273s wall, 1.781250s user + 0.000000s system = 1.781250s CPU (151.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21503 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.002353s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.18552e-05
PHY-3002 : Step(157): len = 447220, overlap = 354.719
PHY-3002 : Step(158): len = 456231, overlap = 279
PHY-3002 : Step(159): len = 450599, overlap = 245.531
PHY-3002 : Step(160): len = 447277, overlap = 228.281
PHY-3002 : Step(161): len = 445777, overlap = 225.188
PHY-3002 : Step(162): len = 441996, overlap = 200.844
PHY-3002 : Step(163): len = 440746, overlap = 193.938
PHY-3002 : Step(164): len = 438934, overlap = 174.062
PHY-3002 : Step(165): len = 435806, overlap = 172.625
PHY-3002 : Step(166): len = 434285, overlap = 177.531
PHY-3002 : Step(167): len = 432258, overlap = 174.562
PHY-3002 : Step(168): len = 430707, overlap = 174.75
PHY-3002 : Step(169): len = 429824, overlap = 178.438
PHY-3002 : Step(170): len = 427249, overlap = 189.438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00018371
PHY-3002 : Step(171): len = 426847, overlap = 182.656
PHY-3002 : Step(172): len = 428124, overlap = 179.531
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000353084
PHY-3002 : Step(173): len = 431189, overlap = 172.562
PHY-3002 : Step(174): len = 439436, overlap = 157.844
PHY-3002 : Step(175): len = 441473, overlap = 155.5
PHY-3002 : Step(176): len = 440432, overlap = 149.031
PHY-3002 : Step(177): len = 440363, overlap = 146.156
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 78968, tnet num: 21503, tinst num: 19059, tnode num: 111162, tedge num: 124050.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.363949s wall, 1.343750s user + 0.015625s system = 1.359375s CPU (99.7%)

RUN-1004 : used memory is 558 MB, reserved memory is 534 MB, peak memory is 689 MB
OPT-1001 : Total overflow 510.75 peak overflow 3.69
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 525/21505.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 515576, over cnt = 2306(6%), over = 8143, worst = 25
PHY-1001 : End global iterations;  1.049083s wall, 1.765625s user + 0.015625s system = 1.781250s CPU (169.8%)

PHY-1001 : Congestion index: top1 = 60.82, top5 = 47.55, top10 = 42.29, top15 = 39.02.
PHY-1001 : End incremental global routing;  1.254081s wall, 1.968750s user + 0.015625s system = 1.984375s CPU (158.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21503 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.856895s wall, 0.828125s user + 0.031250s system = 0.859375s CPU (100.3%)

OPT-1001 : 16 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 18980 has valid locations, 223 needs to be replaced
PHY-3001 : design contains 19266 instances, 5589 luts, 12159 seqs, 1431 slices, 283 macros(1431 instances: 937 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 455298
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16972/21712.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 525744, over cnt = 2344(6%), over = 8267, worst = 25
PHY-1001 : End global iterations;  0.169172s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (101.6%)

PHY-1001 : Congestion index: top1 = 60.54, top5 = 47.65, top10 = 42.44, top15 = 39.23.
PHY-3001 : End congestion estimation;  0.378349s wall, 0.359375s user + 0.015625s system = 0.375000s CPU (99.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 79639, tnet num: 21710, tinst num: 19266, tnode num: 112113, tedge num: 124978.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.400828s wall, 1.390625s user + 0.015625s system = 1.406250s CPU (100.4%)

RUN-1004 : used memory is 601 MB, reserved memory is 590 MB, peak memory is 691 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21710 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.283801s wall, 2.250000s user + 0.046875s system = 2.296875s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(178): len = 455130, overlap = 2.6875
PHY-3002 : Step(179): len = 456424, overlap = 2.875
PHY-3002 : Step(180): len = 456819, overlap = 2.9375
PHY-3002 : Step(181): len = 457279, overlap = 2.8125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 16996/21712.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 524616, over cnt = 2351(6%), over = 8320, worst = 25
PHY-1001 : End global iterations;  0.160056s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (156.2%)

PHY-1001 : Congestion index: top1 = 61.31, top5 = 48.00, top10 = 42.72, top15 = 39.42.
PHY-3001 : End congestion estimation;  0.372222s wall, 0.421875s user + 0.031250s system = 0.453125s CPU (121.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21710 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.866631s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (97.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000501611
PHY-3002 : Step(182): len = 457120, overlap = 149.562
PHY-3002 : Step(183): len = 457217, overlap = 149.375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00100322
PHY-3002 : Step(184): len = 457258, overlap = 148.906
PHY-3002 : Step(185): len = 457606, overlap = 148.344
PHY-3001 : Final: Len = 457606, Over = 148.344
PHY-3001 : End incremental placement;  4.757431s wall, 4.812500s user + 0.281250s system = 5.093750s CPU (107.1%)

OPT-1001 : Total overflow 514.06 peak overflow 3.69
OPT-1001 : End high-fanout net optimization;  7.317310s wall, 8.109375s user + 0.328125s system = 8.437500s CPU (115.3%)

OPT-1001 : Current memory(MB): used = 693, reserve = 673, peak = 709.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16995/21712.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 527336, over cnt = 2302(6%), over = 7831, worst = 25
PHY-1002 : len = 567816, over cnt = 1623(4%), over = 4149, worst = 25
PHY-1002 : len = 598840, over cnt = 876(2%), over = 1958, worst = 15
PHY-1002 : len = 611584, over cnt = 495(1%), over = 1153, worst = 13
PHY-1002 : len = 624376, over cnt = 179(0%), over = 436, worst = 12
PHY-1001 : End global iterations;  0.978046s wall, 1.687500s user + 0.078125s system = 1.765625s CPU (180.5%)

PHY-1001 : Congestion index: top1 = 50.82, top5 = 43.82, top10 = 40.28, top15 = 38.02.
OPT-1001 : End congestion update;  1.200160s wall, 1.906250s user + 0.078125s system = 1.984375s CPU (165.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 21710 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.759092s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (100.9%)

OPT-0007 : Start: WNS 3769 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  1.965480s wall, 2.671875s user + 0.078125s system = 2.750000s CPU (139.9%)

OPT-1001 : Current memory(MB): used = 671, reserve = 655, peak = 709.
OPT-1001 : End physical optimization;  10.929215s wall, 12.578125s user + 0.421875s system = 13.000000s CPU (118.9%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5589 LUT to BLE ...
SYN-4008 : Packed 5589 LUT and 2678 SEQ to BLE.
SYN-4003 : Packing 9481 remaining SEQ's ...
SYN-4005 : Packed 3314 SEQ with LUT/SLICE
SYN-4006 : 107 single LUT's are left
SYN-4006 : 6167 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11756/13540 primitive instances ...
PHY-3001 : End packing;  2.548065s wall, 2.546875s user + 0.000000s system = 2.546875s CPU (100.0%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 7935 instances
RUN-1001 : 3923 mslices, 3923 lslices, 60 pads, 24 brams, 0 dsps
RUN-1001 : There are total 19085 nets
RUN-1001 : 13442 nets have 2 pins
RUN-1001 : 4256 nets have [3 - 5] pins
RUN-1001 : 878 nets have [6 - 10] pins
RUN-1001 : 375 nets have [11 - 20] pins
RUN-1001 : 125 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 7933 instances, 7846 slices, 283 macros(1431 instances: 937 mslices 494 lslices)
PHY-3001 : Cell area utilization is 84%
PHY-3001 : After packing: Len = 475624, Over = 351.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7685/19085.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 594016, over cnt = 1480(4%), over = 2427, worst = 9
PHY-1002 : len = 600960, over cnt = 899(2%), over = 1241, worst = 7
PHY-1002 : len = 613672, over cnt = 285(0%), over = 327, worst = 6
PHY-1002 : len = 617520, over cnt = 108(0%), over = 126, worst = 5
PHY-1002 : len = 619784, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.008813s wall, 1.687500s user + 0.031250s system = 1.718750s CPU (170.4%)

PHY-1001 : Congestion index: top1 = 49.66, top5 = 42.99, top10 = 39.57, top15 = 37.22.
PHY-3001 : End congestion estimation;  1.281866s wall, 1.953125s user + 0.031250s system = 1.984375s CPU (154.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66024, tnet num: 19083, tinst num: 7933, tnode num: 89620, tedge num: 108754.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.561222s wall, 1.546875s user + 0.015625s system = 1.562500s CPU (100.1%)

RUN-1004 : used memory is 593 MB, reserved memory is 590 MB, peak memory is 709 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19083 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.400007s wall, 2.375000s user + 0.031250s system = 2.406250s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.79533e-05
PHY-3002 : Step(186): len = 478525, overlap = 347
PHY-3002 : Step(187): len = 478651, overlap = 354
PHY-3002 : Step(188): len = 480218, overlap = 356.25
PHY-3002 : Step(189): len = 482117, overlap = 361
PHY-3002 : Step(190): len = 483778, overlap = 365
PHY-3002 : Step(191): len = 484301, overlap = 374.25
PHY-3002 : Step(192): len = 481307, overlap = 382.5
PHY-3002 : Step(193): len = 479442, overlap = 384.25
PHY-3002 : Step(194): len = 476648, overlap = 382.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.59065e-05
PHY-3002 : Step(195): len = 480551, overlap = 372.25
PHY-3002 : Step(196): len = 484907, overlap = 353.25
PHY-3002 : Step(197): len = 486044, overlap = 355
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000191813
PHY-3002 : Step(198): len = 491129, overlap = 347
PHY-3002 : Step(199): len = 500896, overlap = 320.75
PHY-3002 : Step(200): len = 501504, overlap = 317
PHY-3002 : Step(201): len = 500930, overlap = 314.75
PHY-3002 : Step(202): len = 501301, overlap = 314
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.606768s wall, 0.687500s user + 0.843750s system = 1.531250s CPU (252.4%)

PHY-3001 : Trial Legalized: Len = 608049
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 83%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 632/19085.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 686504, over cnt = 2237(6%), over = 3655, worst = 7
PHY-1002 : len = 698656, over cnt = 1501(4%), over = 2198, worst = 7
PHY-1002 : len = 720440, over cnt = 427(1%), over = 579, worst = 7
PHY-1002 : len = 725224, over cnt = 210(0%), over = 267, worst = 4
PHY-1002 : len = 729632, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.486576s wall, 2.531250s user + 0.015625s system = 2.546875s CPU (171.3%)

PHY-1001 : Congestion index: top1 = 48.81, top5 = 43.77, top10 = 41.19, top15 = 39.46.
PHY-3001 : End congestion estimation;  1.796308s wall, 2.828125s user + 0.031250s system = 2.859375s CPU (159.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19083 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.762029s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (100.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00018295
PHY-3002 : Step(203): len = 564889, overlap = 79.75
PHY-3002 : Step(204): len = 546547, overlap = 126.75
PHY-3002 : Step(205): len = 535471, overlap = 166
PHY-3002 : Step(206): len = 528975, overlap = 195.5
PHY-3002 : Step(207): len = 524765, overlap = 226.75
PHY-3002 : Step(208): len = 522928, overlap = 237
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0003659
PHY-3002 : Step(209): len = 526882, overlap = 233
PHY-3002 : Step(210): len = 531435, overlap = 229
PHY-3002 : Step(211): len = 531878, overlap = 234.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(212): len = 535238, overlap = 227.25
PHY-3002 : Step(213): len = 542435, overlap = 217.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.028856s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (108.3%)

PHY-3001 : Legalized: Len = 582305, Over = 0
PHY-3001 : Spreading special nets. 34 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.069525s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (89.9%)

PHY-3001 : 51 instances has been re-located, deltaX = 24, deltaY = 27, maxDist = 2.
PHY-3001 : Final: Len = 583589, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66024, tnet num: 19083, tinst num: 7933, tnode num: 89620, tedge num: 108754.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.792847s wall, 1.781250s user + 0.015625s system = 1.796875s CPU (100.2%)

RUN-1004 : used memory is 605 MB, reserved memory is 608 MB, peak memory is 709 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 4012/19085.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 672576, over cnt = 2030(5%), over = 3117, worst = 6
PHY-1002 : len = 685024, over cnt = 1085(3%), over = 1359, worst = 6
PHY-1002 : len = 698432, over cnt = 252(0%), over = 311, worst = 6
PHY-1002 : len = 700952, over cnt = 119(0%), over = 142, worst = 3
PHY-1002 : len = 703016, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.454154s wall, 2.375000s user + 0.062500s system = 2.437500s CPU (167.6%)

PHY-1001 : Congestion index: top1 = 47.33, top5 = 42.34, top10 = 39.76, top15 = 38.03.
PHY-1001 : End incremental global routing;  1.718078s wall, 2.640625s user + 0.062500s system = 2.703125s CPU (157.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19083 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.898722s wall, 0.906250s user + 0.000000s system = 0.906250s CPU (100.8%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 7868 has valid locations, 16 needs to be replaced
PHY-3001 : design contains 7947 instances, 7860 slices, 283 macros(1431 instances: 937 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 585199
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17166/19099.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 704400, over cnt = 29(0%), over = 34, worst = 3
PHY-1002 : len = 704320, over cnt = 17(0%), over = 17, worst = 1
PHY-1002 : len = 704488, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 704552, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.467803s wall, 0.562500s user + 0.015625s system = 0.578125s CPU (123.6%)

PHY-1001 : Congestion index: top1 = 47.33, top5 = 42.45, top10 = 39.85, top15 = 38.10.
PHY-3001 : End congestion estimation;  0.722341s wall, 0.812500s user + 0.015625s system = 0.828125s CPU (114.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66118, tnet num: 19097, tinst num: 7947, tnode num: 89731, tedge num: 108871.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.836097s wall, 1.812500s user + 0.015625s system = 1.828125s CPU (99.6%)

RUN-1004 : used memory is 633 MB, reserved memory is 618 MB, peak memory is 709 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19097 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.639672s wall, 2.625000s user + 0.015625s system = 2.640625s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(214): len = 585202, overlap = 0.25
PHY-3002 : Step(215): len = 585327, overlap = 0.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17165/19099.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 704296, over cnt = 21(0%), over = 21, worst = 1
PHY-1002 : len = 704296, over cnt = 15(0%), over = 15, worst = 1
PHY-1002 : len = 704432, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 704544, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 704576, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.635349s wall, 0.640625s user + 0.000000s system = 0.640625s CPU (100.8%)

PHY-1001 : Congestion index: top1 = 47.41, top5 = 42.45, top10 = 39.86, top15 = 38.11.
PHY-3001 : End congestion estimation;  0.899296s wall, 0.906250s user + 0.000000s system = 0.906250s CPU (100.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19097 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.802690s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (101.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(216): len = 585267, overlap = 1.25
PHY-3002 : Step(217): len = 585346, overlap = 1
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005986s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 585470, Over = 0
PHY-3001 : Spreading special nets. 1 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.062429s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.1%)

PHY-3001 : 2 instances has been re-located, deltaX = 2, deltaY = 2, maxDist = 2.
PHY-3001 : Final: Len = 585506, Over = 0
PHY-3001 : End incremental placement;  5.632874s wall, 5.921875s user + 0.203125s system = 6.125000s CPU (108.7%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  8.656161s wall, 9.859375s user + 0.265625s system = 10.125000s CPU (117.0%)

OPT-1001 : Current memory(MB): used = 705, reserve = 690, peak = 711.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17159/19099.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 704504, over cnt = 21(0%), over = 24, worst = 3
PHY-1002 : len = 704576, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 704656, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.359043s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (108.8%)

PHY-1001 : Congestion index: top1 = 47.37, top5 = 42.40, top10 = 39.81, top15 = 38.08.
OPT-1001 : End congestion update;  0.626637s wall, 0.640625s user + 0.015625s system = 0.656250s CPU (104.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19097 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.679702s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (101.1%)

OPT-0007 : Start: WNS 3739 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.311765s wall, 1.328125s user + 0.015625s system = 1.343750s CPU (102.4%)

OPT-1001 : Current memory(MB): used = 705, reserve = 690, peak = 711.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19097 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.683751s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (100.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17181/19099.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 704656, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.108134s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (101.1%)

PHY-1001 : Congestion index: top1 = 47.37, top5 = 42.40, top10 = 39.81, top15 = 38.08.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19097 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.684626s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (100.4%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3739 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 46.931034
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3739ps with logic level 4 
OPT-1001 : End physical optimization;  13.746019s wall, 14.937500s user + 0.312500s system = 15.250000s CPU (110.9%)

RUN-1003 : finish command "place" in  62.830506s wall, 114.718750s user + 7.515625s system = 122.234375s CPU (194.5%)

RUN-1004 : used memory is 589 MB, reserved memory is 566 MB, peak memory is 711 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.524779s wall, 2.562500s user + 0.015625s system = 2.578125s CPU (169.1%)

RUN-1004 : used memory is 589 MB, reserved memory is 567 MB, peak memory is 711 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 7949 instances
RUN-1001 : 3925 mslices, 3935 lslices, 60 pads, 24 brams, 0 dsps
RUN-1001 : There are total 19099 nets
RUN-1001 : 13442 nets have 2 pins
RUN-1001 : 4262 nets have [3 - 5] pins
RUN-1001 : 880 nets have [6 - 10] pins
RUN-1001 : 379 nets have [11 - 20] pins
RUN-1001 : 127 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66118, tnet num: 19097, tinst num: 7947, tnode num: 89731, tedge num: 108871.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.617924s wall, 1.609375s user + 0.000000s system = 1.609375s CPU (99.5%)

RUN-1004 : used memory is 598 MB, reserved memory is 593 MB, peak memory is 711 MB
PHY-1001 : 3925 mslices, 3935 lslices, 60 pads, 24 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19097 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 651736, over cnt = 2127(6%), over = 3489, worst = 8
PHY-1002 : len = 666160, over cnt = 1254(3%), over = 1781, worst = 7
PHY-1002 : len = 683712, over cnt = 382(1%), over = 478, worst = 5
PHY-1002 : len = 691512, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 691592, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.462717s wall, 2.437500s user + 0.031250s system = 2.468750s CPU (168.8%)

PHY-1001 : Congestion index: top1 = 47.59, top5 = 42.16, top10 = 39.38, top15 = 37.64.
PHY-1001 : End global routing;  1.766330s wall, 2.734375s user + 0.031250s system = 2.765625s CPU (156.6%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 690, reserve = 679, peak = 711.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 961, reserve = 947, peak = 961.
PHY-1001 : End build detailed router design. 4.251395s wall, 4.203125s user + 0.046875s system = 4.250000s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 187624, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.752464s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (101.7%)

PHY-1001 : Current memory(MB): used = 996, reserve = 984, peak = 996.
PHY-1001 : End phase 1; 0.760261s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (100.7%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 55% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.62445e+06, over cnt = 1169(0%), over = 1170, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1011, reserve = 999, peak = 1011.
PHY-1001 : End initial routed; 12.220178s wall, 38.046875s user + 0.343750s system = 38.390625s CPU (314.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17889(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.226   |   0.000   |   0   
RUN-1001 :   Hold   |   0.156   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.024914s wall, 3.031250s user + 0.000000s system = 3.031250s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 1023, reserve = 1011, peak = 1023.
PHY-1001 : End phase 2; 15.245267s wall, 41.078125s user + 0.343750s system = 41.421875s CPU (271.7%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.62445e+06, over cnt = 1169(0%), over = 1170, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.198573s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (102.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.6159e+06, over cnt = 435(0%), over = 435, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.515773s wall, 0.875000s user + 0.015625s system = 0.890625s CPU (172.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.61654e+06, over cnt = 72(0%), over = 72, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.343054s wall, 0.453125s user + 0.015625s system = 0.468750s CPU (136.6%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.61709e+06, over cnt = 13(0%), over = 13, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.208271s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (120.0%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.61703e+06, over cnt = 5(0%), over = 5, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.182191s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (145.8%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.6171e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.204757s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (129.7%)

PHY-1001 : ===== DR Iter 6 =====
PHY-1022 : len = 1.61713e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 6; 0.166743s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (103.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17889(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.090   |   0.000   |   0   
RUN-1001 :   Hold   |   0.156   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.034686s wall, 3.046875s user + 0.000000s system = 3.046875s CPU (100.4%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 296 feed throughs used by 253 nets
PHY-1001 : End commit to database; 2.006501s wall, 1.937500s user + 0.046875s system = 1.984375s CPU (98.9%)

PHY-1001 : Current memory(MB): used = 1110, reserve = 1101, peak = 1110.
PHY-1001 : End phase 3; 7.321963s wall, 7.890625s user + 0.093750s system = 7.984375s CPU (109.0%)

PHY-1003 : Routed, final wirelength = 1.61713e+06
PHY-1001 : Current memory(MB): used = 1115, reserve = 1106, peak = 1115.
PHY-1001 : End export database. 0.051634s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.8%)

PHY-1001 : End detail routing;  28.022876s wall, 54.390625s user + 0.484375s system = 54.875000s CPU (195.8%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66118, tnet num: 19097, tinst num: 7947, tnode num: 89731, tedge num: 108871.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.534285s wall, 1.531250s user + 0.000000s system = 1.531250s CPU (99.8%)

RUN-1004 : used memory is 1046 MB, reserved memory is 1045 MB, peak memory is 1115 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  35.202526s wall, 62.500000s user + 0.546875s system = 63.046875s CPU (179.1%)

RUN-1004 : used memory is 1045 MB, reserved memory is 1045 MB, peak memory is 1115 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8621   out of  19600   43.98%
#reg                    12258   out of  19600   62.54%
#le                     14736
  #lut only              2478   out of  14736   16.82%
  #reg only              6115   out of  14736   41.50%
  #lut&reg               6143   out of  14736   41.69%
#dsp                        0   out of     29    0.00%
#bram                      24   out of     64   37.50%
  #bram9k                  22
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6739
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          108
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         M3        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         P6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |14736  |7190    |1431    |12302   |24      |0       |
|  AnyFog_dataX                      |AnyFog          |229    |103     |22      |181     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |106    |73      |22      |58      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |210    |73      |22      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |55      |22      |51      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |205    |116     |22      |169     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |84     |61      |22      |48      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2907   |711     |39      |2824    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |62     |35      |5       |53      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |214    |79      |5       |203     |0       |0       |
|    STADOP_com2                     |STADOP          |535    |133     |0       |531     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |42      |14      |43      |0       |0       |
|    head_com2                       |uniheading      |263    |99      |5       |250     |0       |0       |
|    rmc_com2                        |Gprmc           |33     |31      |0       |30      |0       |0       |
|    uart_com2                       |Agrica          |1435   |257     |10      |1414    |0       |0       |
|  COM3                              |COM3_Control    |280    |132     |19      |238     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |60     |33      |5       |52      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |61     |40      |14      |37      |0       |0       |
|    rmc_com3                        |Gprmc           |159    |59      |0       |149     |0       |0       |
|  DATA                              |Data_Processing |8679   |4437    |1065    |6971    |0       |0       |
|    DIV_Dtemp                       |Divider         |802    |354     |84      |677     |0       |0       |
|    DIV_Utemp                       |Divider         |614    |309     |84      |482     |0       |0       |
|    DIV_accX                        |Divider         |605    |312     |84      |475     |0       |0       |
|    DIV_accY                        |Divider         |673    |348     |114     |498     |0       |0       |
|    DIV_accZ                        |Divider         |646    |367     |132     |434     |0       |0       |
|    DIV_rateX                       |Divider         |609    |367     |132     |408     |0       |0       |
|    DIV_rateY                       |Divider         |617    |404     |132     |410     |0       |0       |
|    DIV_rateZ                       |Divider         |582    |329     |132     |376     |0       |0       |
|    genclk                          |genclk          |85     |51      |20      |52      |0       |0       |
|  FMC                               |FMC_Ctrl        |435    |381     |43      |343     |0       |0       |
|  IIC                               |I2C_master      |301    |238     |11      |269     |0       |0       |
|  IMU_CTRL                          |SCHA634         |905    |630     |61      |737     |0       |0       |
|    CtrlData                        |CtrlData        |477    |422     |47      |342     |0       |0       |
|      usms                          |Time_1ms        |32     |27      |5       |21      |0       |0       |
|    SPIM                            |SPI_SCHA634     |428    |208     |14      |395     |0       |0       |
|  POWER                             |POWER_EN        |96     |49      |38      |35      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |470    |316     |89      |299     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |470    |316     |89      |299     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |192    |145     |0       |175     |0       |0       |
|        reg_inst                    |register        |191    |144     |0       |174     |0       |0       |
|        tap_inst                    |tap             |1      |1       |0       |1       |0       |0       |
|      trigger_inst                  |trigger         |278    |171     |89      |124     |0       |0       |
|        bus_inst                    |bus_top         |78     |48      |28      |29      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |27     |17      |10      |10      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |51     |31      |18      |19      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |118    |85      |29      |68      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13381  
    #2          2       3363   
    #3          3        625   
    #4          4        274   
    #5        5-10       941   
    #6        11-50      437   
    #7       51-100       8    
    #8       101-500      3    
    #9        >500        2    
  Average     2.11             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  1.791667s wall, 3.109375s user + 0.000000s system = 3.109375s CPU (173.5%)

RUN-1004 : used memory is 1046 MB, reserved memory is 1045 MB, peak memory is 1115 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66118, tnet num: 19097, tinst num: 7947, tnode num: 89731, tedge num: 108871.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.541204s wall, 1.531250s user + 0.000000s system = 1.531250s CPU (99.4%)

RUN-1004 : used memory is 1047 MB, reserved memory is 1047 MB, peak memory is 1115 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19097 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.200313s wall, 1.203125s user + 0.000000s system = 1.203125s CPU (100.2%)

RUN-1004 : used memory is 1051 MB, reserved memory is 1050 MB, peak memory is 1115 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 057bf4ae9dc0d18f7126c20ce7aec8c31cdd0d41110c6b742e6e5620711a0ef6 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 7947
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19099, pip num: 141805
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 296
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3245 valid insts, and 398742 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101000001010010110001101
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  9.666235s wall, 98.078125s user + 0.046875s system = 98.125000s CPU (1015.1%)

RUN-1004 : used memory is 1171 MB, reserved memory is 1156 MB, peak memory is 1285 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250731_100340.log"

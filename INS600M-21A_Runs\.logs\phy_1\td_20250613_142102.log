============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Fri Jun 13 14:21:02 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(516)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
RUN-1001 : Project manager successfully analyzed 22 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.360475s wall, 1.500000s user + 3.843750s system = 5.343750s CPU (99.7%)

RUN-1004 : used memory is 79 MB, reserved memory is 40 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.799220s wall, 1.687500s user + 0.093750s system = 1.781250s CPU (99.0%)

RUN-1004 : used memory is 301 MB, reserved memory is 269 MB, peak memory is 304 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 58 trigger nets, 58 data nets.
KIT-1004 : Chipwatcher code = 1110111010000001
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=158) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=158) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=158)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=158)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=58,BUS_CTRL_NUM=136,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb0100000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 23200/23 useful/useless nets, 19908/12 useful/useless insts
SYN-1016 : Merged 28 instances.
SYN-1032 : 22805/20 useful/useless nets, 20415/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 478 better
SYN-1014 : Optimize round 2
SYN-1032 : 22421/45 useful/useless nets, 20031/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.425267s wall, 2.375000s user + 0.046875s system = 2.421875s CPU (99.9%)

RUN-1004 : used memory is 330 MB, reserved memory is 296 MB, peak memory is 332 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22493/441 useful/useless nets, 20154/63 useful/useless insts
SYN-1016 : Merged 69 instances.
SYN-2571 : Optimize after map_dsp, round 1, 573 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 56 instances.
SYN-2501 : Optimize round 1, 114 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 22997/5 useful/useless nets, 20658/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 84677, tnet num: 22997, tinst num: 20657, tnode num: 118807, tedge num: 131962.
TMR-2508 : Levelizing timing graph completed, there are 85 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.185681s wall, 1.140625s user + 0.046875s system = 1.187500s CPU (100.2%)

RUN-1004 : used memory is 474 MB, reserved memory is 442 MB, peak memory is 474 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22997 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 282 (3.29), #lev = 7 (1.66)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 282 (3.29), #lev = 7 (1.66)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 614 instances into 282 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 487 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 160 adder to BLE ...
SYN-4008 : Packed 160 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.549129s wall, 4.421875s user + 0.125000s system = 4.546875s CPU (100.0%)

RUN-1004 : used memory is 357 MB, reserved memory is 323 MB, peak memory is 585 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.290057s wall, 7.093750s user + 0.187500s system = 7.281250s CPU (99.9%)

RUN-1004 : used memory is 357 MB, reserved memory is 324 MB, peak memory is 585 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/rmc_com2/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/rmc_com2/GPRMC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/rmc_com2/GPRMC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/rmc_com2/GPRMC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/rmc_com2/GPRMC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/rmc_com2/GPRMC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/rmc_com2/GPRMC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/rmc_com2/GPRMC_data[0]
SYN-5055 WARNING: The kept net COM2/rx_err will be merged to another kept net COM2/rmc_com2/GPRMC_err
SYN-5055 WARNING: The kept net FMC/Error_state[0] will be merged to another kept net COM2/rmc_com2/GPRMC_sub_state_cnt0[3]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (335 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19902 instances
RUN-0007 : 5762 luts, 12578 seqs, 951 mslices, 494 lslices, 60 pads, 52 brams, 0 dsps
RUN-1001 : There are total 22269 nets
RUN-1001 : 16592 nets have 2 pins
RUN-1001 : 4500 nets have [3 - 5] pins
RUN-1001 : 797 nets have [6 - 10] pins
RUN-1001 : 254 nets have [11 - 20] pins
RUN-1001 : 102 nets have [21 - 99] pins
RUN-1001 : 24 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4770     
RUN-1001 :   No   |  No   |  Yes  |     670     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    6531     
RUN-1001 :   Yes  |  No   |  Yes  |     507     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  116  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 124
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19900 instances, 5762 luts, 12578 seqs, 1445 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1131 pins
PHY-3001 : Huge net DATA/done_div with 1650 pins
PHY-0007 : Cell area utilization is 64%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83044, tnet num: 22267, tinst num: 19900, tnode num: 117149, tedge num: 130500.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.213116s wall, 1.218750s user + 0.000000s system = 1.218750s CPU (100.5%)

RUN-1004 : used memory is 535 MB, reserved memory is 507 MB, peak memory is 585 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22267 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.048249s wall, 2.031250s user + 0.015625s system = 2.046875s CPU (99.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.41954e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19900.
PHY-3001 : Level 1 #clusters 2064.
PHY-3001 : End clustering;  0.147906s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (158.5%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 64%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 858238, overlap = 645.594
PHY-3002 : Step(2): len = 785844, overlap = 726.531
PHY-3002 : Step(3): len = 510866, overlap = 891.031
PHY-3002 : Step(4): len = 453426, overlap = 973.844
PHY-3002 : Step(5): len = 359177, overlap = 1065.94
PHY-3002 : Step(6): len = 330349, overlap = 1110.06
PHY-3002 : Step(7): len = 280306, overlap = 1221.09
PHY-3002 : Step(8): len = 251816, overlap = 1294.16
PHY-3002 : Step(9): len = 226528, overlap = 1357.38
PHY-3002 : Step(10): len = 208691, overlap = 1407.28
PHY-3002 : Step(11): len = 190849, overlap = 1441.03
PHY-3002 : Step(12): len = 168093, overlap = 1473.22
PHY-3002 : Step(13): len = 154011, overlap = 1495.59
PHY-3002 : Step(14): len = 142145, overlap = 1517.72
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.30529e-06
PHY-3002 : Step(15): len = 150724, overlap = 1469.03
PHY-3002 : Step(16): len = 212801, overlap = 1321.59
PHY-3002 : Step(17): len = 224794, overlap = 1226.25
PHY-3002 : Step(18): len = 228695, overlap = 1116.56
PHY-3002 : Step(19): len = 222732, overlap = 1097.66
PHY-3002 : Step(20): len = 219375, overlap = 1066.62
PHY-3002 : Step(21): len = 212355, overlap = 1056.34
PHY-3002 : Step(22): len = 206512, overlap = 1054.94
PHY-3002 : Step(23): len = 201379, overlap = 1033.31
PHY-3002 : Step(24): len = 197512, overlap = 1015.78
PHY-3002 : Step(25): len = 193644, overlap = 1021.84
PHY-3002 : Step(26): len = 190835, overlap = 1023.72
PHY-3002 : Step(27): len = 188353, overlap = 1033.09
PHY-3002 : Step(28): len = 186152, overlap = 1036.28
PHY-3002 : Step(29): len = 185997, overlap = 1039.12
PHY-3002 : Step(30): len = 184692, overlap = 1031.66
PHY-3002 : Step(31): len = 183951, overlap = 1042.91
PHY-3002 : Step(32): len = 182753, overlap = 1062.03
PHY-3002 : Step(33): len = 181625, overlap = 1070.69
PHY-3002 : Step(34): len = 179840, overlap = 1079.19
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.61058e-06
PHY-3002 : Step(35): len = 187562, overlap = 1034.75
PHY-3002 : Step(36): len = 199720, overlap = 956.156
PHY-3002 : Step(37): len = 201696, overlap = 929.625
PHY-3002 : Step(38): len = 205096, overlap = 897.594
PHY-3002 : Step(39): len = 205315, overlap = 906.062
PHY-3002 : Step(40): len = 205221, overlap = 898
PHY-3002 : Step(41): len = 203380, overlap = 895.031
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 5.22116e-06
PHY-3002 : Step(42): len = 213782, overlap = 859.875
PHY-3002 : Step(43): len = 227560, overlap = 790.062
PHY-3002 : Step(44): len = 231102, overlap = 757.938
PHY-3002 : Step(45): len = 233285, overlap = 713.5
PHY-3002 : Step(46): len = 232301, overlap = 701.75
PHY-3002 : Step(47): len = 230546, overlap = 702
PHY-3002 : Step(48): len = 228943, overlap = 692.75
PHY-3002 : Step(49): len = 228318, overlap = 692.812
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 1.04423e-05
PHY-3002 : Step(50): len = 238245, overlap = 678.875
PHY-3002 : Step(51): len = 248749, overlap = 632.719
PHY-3002 : Step(52): len = 253269, overlap = 611.156
PHY-3002 : Step(53): len = 257661, overlap = 592.594
PHY-3002 : Step(54): len = 257454, overlap = 586.594
PHY-3002 : Step(55): len = 257439, overlap = 582.656
PHY-3002 : Step(56): len = 256447, overlap = 558.969
PHY-3002 : Step(57): len = 256350, overlap = 534.125
PHY-3002 : Step(58): len = 254429, overlap = 531.219
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 2.08846e-05
PHY-3002 : Step(59): len = 262947, overlap = 525.812
PHY-3002 : Step(60): len = 276405, overlap = 461.312
PHY-3002 : Step(61): len = 281034, overlap = 447.094
PHY-3002 : Step(62): len = 282086, overlap = 443.438
PHY-3002 : Step(63): len = 281116, overlap = 457.312
PHY-3002 : Step(64): len = 279961, overlap = 460
PHY-3002 : Step(65): len = 278111, overlap = 450
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 4.17693e-05
PHY-3002 : Step(66): len = 285807, overlap = 412.875
PHY-3002 : Step(67): len = 297040, overlap = 386.969
PHY-3002 : Step(68): len = 301004, overlap = 373.188
PHY-3002 : Step(69): len = 301903, overlap = 367.031
PHY-3002 : Step(70): len = 301438, overlap = 377.156
PHY-3002 : Step(71): len = 299267, overlap = 383.25
PHY-3002 : Step(72): len = 298364, overlap = 389.125
PHY-3002 : Step(73): len = 298225, overlap = 404.562
PHY-3002 : Step(74): len = 297657, overlap = 400.562
PHY-3002 : Step(75): len = 296914, overlap = 398.375
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 8.35385e-05
PHY-3002 : Step(76): len = 301896, overlap = 382.406
PHY-3002 : Step(77): len = 311208, overlap = 364.781
PHY-3002 : Step(78): len = 315076, overlap = 349.094
PHY-3002 : Step(79): len = 315438, overlap = 359.781
PHY-3002 : Step(80): len = 314306, overlap = 369.688
PHY-3002 : Step(81): len = 312266, overlap = 366.875
PHY-3002 : Step(82): len = 310948, overlap = 369.688
PHY-3002 : Step(83): len = 309859, overlap = 358.781
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000166954
PHY-3002 : Step(84): len = 313788, overlap = 350.781
PHY-3002 : Step(85): len = 318481, overlap = 349.156
PHY-3002 : Step(86): len = 320245, overlap = 334.031
PHY-3002 : Step(87): len = 321449, overlap = 310.812
PHY-3002 : Step(88): len = 321370, overlap = 305.281
PHY-3002 : Step(89): len = 322184, overlap = 290.188
PHY-3002 : Step(90): len = 321807, overlap = 280.25
PHY-3002 : Step(91): len = 321828, overlap = 272.906
PHY-3002 : Step(92): len = 321623, overlap = 274.875
PHY-3002 : Step(93): len = 322473, overlap = 284.938
PHY-3002 : Step(94): len = 320959, overlap = 287.094
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000283421
PHY-3002 : Step(95): len = 322336, overlap = 285.844
PHY-3002 : Step(96): len = 324811, overlap = 290.438
PHY-3002 : Step(97): len = 326118, overlap = 297.406
PHY-3002 : Step(98): len = 328878, overlap = 298
PHY-3002 : Step(99): len = 329557, overlap = 301.75
PHY-3002 : Step(100): len = 329797, overlap = 304.812
PHY-3002 : Step(101): len = 328250, overlap = 331.531
PHY-3002 : Step(102): len = 327542, overlap = 343.25
PHY-3002 : Step(103): len = 327451, overlap = 338.031
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(104): len = 328546, overlap = 338.969
PHY-3002 : Step(105): len = 330090, overlap = 345.688
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.012281s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (254.5%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/22269.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 440944, over cnt = 1246(3%), over = 5716, worst = 52
PHY-1001 : End global iterations;  0.804349s wall, 1.046875s user + 0.031250s system = 1.078125s CPU (134.0%)

PHY-1001 : Congestion index: top1 = 86.96, top5 = 55.85, top10 = 44.97, top15 = 38.78.
PHY-3001 : End congestion estimation;  1.055056s wall, 1.296875s user + 0.031250s system = 1.328125s CPU (125.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22267 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.881395s wall, 0.828125s user + 0.031250s system = 0.859375s CPU (97.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000100527
PHY-3002 : Step(106): len = 377445, overlap = 241.875
PHY-3002 : Step(107): len = 401799, overlap = 228.844
PHY-3002 : Step(108): len = 399776, overlap = 217.188
PHY-3002 : Step(109): len = 399432, overlap = 212.562
PHY-3002 : Step(110): len = 407476, overlap = 197.875
PHY-3002 : Step(111): len = 410830, overlap = 187.469
PHY-3002 : Step(112): len = 414750, overlap = 183.688
PHY-3002 : Step(113): len = 422592, overlap = 170.406
PHY-3002 : Step(114): len = 425540, overlap = 161.938
PHY-3002 : Step(115): len = 428697, overlap = 160.625
PHY-3002 : Step(116): len = 432753, overlap = 159.75
PHY-3002 : Step(117): len = 432534, overlap = 154.531
PHY-3002 : Step(118): len = 433936, overlap = 153.625
PHY-3002 : Step(119): len = 435873, overlap = 150.875
PHY-3002 : Step(120): len = 438951, overlap = 145.5
PHY-3002 : Step(121): len = 436946, overlap = 145.594
PHY-3002 : Step(122): len = 437001, overlap = 145.875
PHY-3002 : Step(123): len = 437034, overlap = 146.406
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000201054
PHY-3002 : Step(124): len = 438181, overlap = 142.062
PHY-3002 : Step(125): len = 441082, overlap = 136.719
PHY-3002 : Step(126): len = 444269, overlap = 141.875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000402109
PHY-3002 : Step(127): len = 455219, overlap = 137.406
PHY-3002 : Step(128): len = 466405, overlap = 136.531
PHY-3002 : Step(129): len = 466164, overlap = 135.531
PHY-3002 : Step(130): len = 468942, overlap = 135.531
PHY-3002 : Step(131): len = 471707, overlap = 128.156
PHY-3002 : Step(132): len = 472460, overlap = 120.656
PHY-3002 : Step(133): len = 472929, overlap = 118.562
PHY-3002 : Step(134): len = 476011, overlap = 109.125
PHY-3002 : Step(135): len = 478282, overlap = 98
PHY-3002 : Step(136): len = 477737, overlap = 97.1562
PHY-3002 : Step(137): len = 477621, overlap = 100.062
PHY-3002 : Step(138): len = 479515, overlap = 101.156
PHY-3002 : Step(139): len = 483076, overlap = 94.5938
PHY-3002 : Step(140): len = 482827, overlap = 91.625
PHY-3002 : Step(141): len = 483437, overlap = 94.6562
PHY-3002 : Step(142): len = 486285, overlap = 98.5625
PHY-3002 : Step(143): len = 486424, overlap = 97.6875
PHY-3002 : Step(144): len = 485824, overlap = 98.2812
PHY-3002 : Step(145): len = 486655, overlap = 98.0312
PHY-3002 : Step(146): len = 488241, overlap = 96.4688
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000804218
PHY-3002 : Step(147): len = 487874, overlap = 97.9062
PHY-3002 : Step(148): len = 490304, overlap = 93.6562
PHY-3002 : Step(149): len = 494014, overlap = 85.7812
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00149436
PHY-3002 : Step(150): len = 493467, overlap = 86.0938
PHY-3002 : Step(151): len = 497109, overlap = 94.0625
PHY-3002 : Step(152): len = 507503, overlap = 95.8438
PHY-3002 : Step(153): len = 508847, overlap = 94.1875
PHY-3002 : Step(154): len = 508221, overlap = 95
PHY-3002 : Step(155): len = 508974, overlap = 93.5938
PHY-3002 : Step(156): len = 508491, overlap = 95.3125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 44/22269.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 571576, over cnt = 2298(6%), over = 11657, worst = 46
PHY-1001 : End global iterations;  1.020930s wall, 1.609375s user + 0.062500s system = 1.671875s CPU (163.8%)

PHY-1001 : Congestion index: top1 = 85.54, top5 = 66.65, top10 = 56.67, top15 = 50.28.
PHY-3001 : End congestion estimation;  1.389599s wall, 1.984375s user + 0.062500s system = 2.046875s CPU (147.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22267 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.894815s wall, 0.875000s user + 0.015625s system = 0.890625s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.82638e-05
PHY-3002 : Step(157): len = 513533, overlap = 367.594
PHY-3002 : Step(158): len = 517538, overlap = 299.188
PHY-3002 : Step(159): len = 513987, overlap = 259.062
PHY-3002 : Step(160): len = 506715, overlap = 243.5
PHY-3002 : Step(161): len = 498932, overlap = 234.844
PHY-3002 : Step(162): len = 494191, overlap = 226.188
PHY-3002 : Step(163): len = 491448, overlap = 219.781
PHY-3002 : Step(164): len = 486647, overlap = 222.656
PHY-3002 : Step(165): len = 482684, overlap = 227.031
PHY-3002 : Step(166): len = 480834, overlap = 232.344
PHY-3002 : Step(167): len = 476385, overlap = 237.531
PHY-3002 : Step(168): len = 474969, overlap = 235.438
PHY-3002 : Step(169): len = 473618, overlap = 235.281
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000196528
PHY-3002 : Step(170): len = 472378, overlap = 218.5
PHY-3002 : Step(171): len = 473771, overlap = 215.969
PHY-3002 : Step(172): len = 474712, overlap = 207.844
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000393055
PHY-3002 : Step(173): len = 478414, overlap = 190
PHY-3002 : Step(174): len = 485375, overlap = 175.594
PHY-3002 : Step(175): len = 487921, overlap = 174.531
PHY-3002 : Step(176): len = 489881, overlap = 174.594
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000786111
PHY-3002 : Step(177): len = 490585, overlap = 167.906
PHY-3002 : Step(178): len = 493932, overlap = 157.188
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83044, tnet num: 22267, tinst num: 19900, tnode num: 117149, tedge num: 130500.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.488916s wall, 1.453125s user + 0.031250s system = 1.484375s CPU (99.7%)

RUN-1004 : used memory is 578 MB, reserved memory is 552 MB, peak memory is 713 MB
OPT-1001 : Total overflow 541.06 peak overflow 4.59
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 321/22269.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 574768, over cnt = 2532(7%), over = 9221, worst = 24
PHY-1001 : End global iterations;  1.207206s wall, 1.750000s user + 0.062500s system = 1.812500s CPU (150.1%)

PHY-1001 : Congestion index: top1 = 59.07, top5 = 49.18, top10 = 44.00, top15 = 40.83.
PHY-1001 : End incremental global routing;  1.435519s wall, 1.968750s user + 0.062500s system = 2.031250s CPU (141.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22267 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.953373s wall, 0.937500s user + 0.015625s system = 0.953125s CPU (100.0%)

OPT-1001 : 21 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19816 has valid locations, 282 needs to be replaced
PHY-3001 : design contains 20161 instances, 5879 luts, 12722 seqs, 1445 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 511656
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17641/22530.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 591136, over cnt = 2601(7%), over = 9298, worst = 24
PHY-1001 : End global iterations;  0.182557s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (162.6%)

PHY-1001 : Congestion index: top1 = 58.88, top5 = 49.57, top10 = 44.36, top15 = 41.18.
PHY-3001 : End congestion estimation;  0.415176s wall, 0.531250s user + 0.000000s system = 0.531250s CPU (128.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83949, tnet num: 22528, tinst num: 20161, tnode num: 118407, tedge num: 131788.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.495374s wall, 1.468750s user + 0.031250s system = 1.500000s CPU (100.3%)

RUN-1004 : used memory is 621 MB, reserved memory is 615 MB, peak memory is 715 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22528 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.493732s wall, 2.468750s user + 0.031250s system = 2.500000s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(179): len = 511410, overlap = 4.875
PHY-3002 : Step(180): len = 512448, overlap = 5
PHY-3002 : Step(181): len = 513143, overlap = 4.875
PHY-3002 : Step(182): len = 513562, overlap = 4.875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17678/22530.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 589384, over cnt = 2621(7%), over = 9449, worst = 24
PHY-1001 : End global iterations;  0.187091s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (125.3%)

PHY-1001 : Congestion index: top1 = 59.91, top5 = 50.00, top10 = 44.60, top15 = 41.46.
PHY-3001 : End congestion estimation;  0.413148s wall, 0.437500s user + 0.031250s system = 0.468750s CPU (113.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22528 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.110124s wall, 1.093750s user + 0.000000s system = 1.093750s CPU (98.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000623667
PHY-3002 : Step(183): len = 513515, overlap = 160.531
PHY-3002 : Step(184): len = 514015, overlap = 160.094
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00124733
PHY-3002 : Step(185): len = 514486, overlap = 159.781
PHY-3002 : Step(186): len = 514940, overlap = 159.344
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00249467
PHY-3002 : Step(187): len = 514987, overlap = 158.688
PHY-3002 : Step(188): len = 515654, overlap = 158.719
PHY-3001 : Final: Len = 515654, Over = 158.719
PHY-3001 : End incremental placement;  5.449118s wall, 5.859375s user + 0.250000s system = 6.109375s CPU (112.1%)

OPT-1001 : Total overflow 548.22 peak overflow 4.59
OPT-1001 : End high-fanout net optimization;  8.363988s wall, 9.453125s user + 0.328125s system = 9.781250s CPU (116.9%)

OPT-1001 : Current memory(MB): used = 718, reserve = 698, peak = 735.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17677/22530.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 593744, over cnt = 2568(7%), over = 8866, worst = 24
PHY-1002 : len = 646040, over cnt = 1717(4%), over = 4051, worst = 18
PHY-1002 : len = 678944, over cnt = 729(2%), over = 1578, worst = 18
PHY-1002 : len = 698208, over cnt = 172(0%), over = 313, worst = 11
PHY-1002 : len = 704080, over cnt = 1(0%), over = 1, worst = 1
PHY-1001 : End global iterations;  1.546194s wall, 2.093750s user + 0.031250s system = 2.125000s CPU (137.4%)

PHY-1001 : Congestion index: top1 = 49.78, top5 = 44.60, top10 = 41.80, top15 = 39.77.
OPT-1001 : End congestion update;  1.784697s wall, 2.328125s user + 0.031250s system = 2.359375s CPU (132.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22528 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.804202s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (101.0%)

OPT-0007 : Start: WNS 3953 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.594918s wall, 3.140625s user + 0.031250s system = 3.171875s CPU (122.2%)

OPT-1001 : Current memory(MB): used = 713, reserve = 692, peak = 735.
OPT-1001 : End physical optimization;  12.741320s wall, 14.484375s user + 0.421875s system = 14.906250s CPU (117.0%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5879 LUT to BLE ...
SYN-4008 : Packed 5879 LUT and 2907 SEQ to BLE.
SYN-4003 : Packing 9815 remaining SEQ's ...
SYN-4005 : Packed 3361 SEQ with LUT/SLICE
SYN-4006 : 110 single LUT's are left
SYN-4006 : 6454 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 12333/14087 primitive instances ...
PHY-3001 : End packing;  2.862606s wall, 2.859375s user + 0.000000s system = 2.859375s CPU (99.9%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8240 instances
RUN-1001 : 4061 mslices, 4062 lslices, 60 pads, 52 brams, 0 dsps
RUN-1001 : There are total 19674 nets
RUN-1001 : 13637 nets have 2 pins
RUN-1001 : 4626 nets have [3 - 5] pins
RUN-1001 : 858 nets have [6 - 10] pins
RUN-1001 : 408 nets have [11 - 20] pins
RUN-1001 : 135 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
PHY-3001 : design contains 8238 instances, 8123 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Cell area utilization is 86%
PHY-3001 : After packing: Len = 528967, Over = 393.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 8130/19674.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 661888, over cnt = 1697(4%), over = 2707, worst = 8
PHY-1002 : len = 667888, over cnt = 1110(3%), over = 1534, worst = 7
PHY-1002 : len = 682328, over cnt = 325(0%), over = 412, worst = 7
PHY-1002 : len = 688552, over cnt = 59(0%), over = 64, worst = 3
PHY-1002 : len = 690400, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.207681s wall, 1.796875s user + 0.000000s system = 1.796875s CPU (148.8%)

PHY-1001 : Congestion index: top1 = 50.71, top5 = 44.69, top10 = 41.28, top15 = 39.05.
PHY-3001 : End congestion estimation;  1.502412s wall, 2.078125s user + 0.015625s system = 2.093750s CPU (139.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69141, tnet num: 19672, tinst num: 8238, tnode num: 94054, tedge num: 113905.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.707193s wall, 1.656250s user + 0.031250s system = 1.687500s CPU (98.8%)

RUN-1004 : used memory is 608 MB, reserved memory is 596 MB, peak memory is 735 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19672 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.617700s wall, 2.546875s user + 0.062500s system = 2.609375s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.94044e-05
PHY-3002 : Step(189): len = 532685, overlap = 374.25
PHY-3002 : Step(190): len = 533119, overlap = 379.5
PHY-3002 : Step(191): len = 533139, overlap = 399.25
PHY-3002 : Step(192): len = 533876, overlap = 404.75
PHY-3002 : Step(193): len = 533525, overlap = 418
PHY-3002 : Step(194): len = 532943, overlap = 417.5
PHY-3002 : Step(195): len = 530704, overlap = 426.5
PHY-3002 : Step(196): len = 528948, overlap = 429.75
PHY-3002 : Step(197): len = 527066, overlap = 430
PHY-3002 : Step(198): len = 525139, overlap = 426
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.88089e-05
PHY-3002 : Step(199): len = 529546, overlap = 416
PHY-3002 : Step(200): len = 533610, overlap = 403
PHY-3002 : Step(201): len = 533874, overlap = 397.5
PHY-3002 : Step(202): len = 534859, overlap = 388.5
PHY-3002 : Step(203): len = 535616, overlap = 383.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000195046
PHY-3002 : Step(204): len = 541705, overlap = 374.75
PHY-3002 : Step(205): len = 551161, overlap = 357.25
PHY-3002 : Step(206): len = 553992, overlap = 350.75
PHY-3002 : Step(207): len = 553744, overlap = 353.5
PHY-3002 : Step(208): len = 553226, overlap = 354.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.684144s wall, 0.562500s user + 0.781250s system = 1.343750s CPU (196.4%)

PHY-3001 : Trial Legalized: Len = 667919
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 478/19674.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 757192, over cnt = 2516(7%), over = 4152, worst = 7
PHY-1002 : len = 775384, over cnt = 1416(4%), over = 1920, worst = 6
PHY-1002 : len = 789624, over cnt = 679(1%), over = 900, worst = 6
PHY-1002 : len = 802416, over cnt = 110(0%), over = 126, worst = 3
PHY-1002 : len = 805576, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.881542s wall, 3.000000s user + 0.046875s system = 3.046875s CPU (161.9%)

PHY-1001 : Congestion index: top1 = 50.71, top5 = 46.03, top10 = 43.36, top15 = 41.59.
PHY-3001 : End congestion estimation;  2.226138s wall, 3.343750s user + 0.046875s system = 3.390625s CPU (152.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19672 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.822250s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (100.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000187703
PHY-3002 : Step(209): len = 623639, overlap = 86.75
PHY-3002 : Step(210): len = 604405, overlap = 130.75
PHY-3002 : Step(211): len = 591091, overlap = 172.25
PHY-3002 : Step(212): len = 581864, overlap = 215
PHY-3002 : Step(213): len = 577022, overlap = 243
PHY-3002 : Step(214): len = 574431, overlap = 259.25
PHY-3002 : Step(215): len = 572615, overlap = 266
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000375406
PHY-3002 : Step(216): len = 577312, overlap = 262.25
PHY-3002 : Step(217): len = 582079, overlap = 253
PHY-3002 : Step(218): len = 582543, overlap = 245
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(219): len = 585146, overlap = 240.25
PHY-3002 : Step(220): len = 590455, overlap = 242
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.029003s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (107.7%)

PHY-3001 : Legalized: Len = 636915, Over = 0
PHY-3001 : Spreading special nets. 41 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.074653s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (83.7%)

PHY-3001 : 59 instances has been re-located, deltaX = 20, deltaY = 38, maxDist = 2.
PHY-3001 : Final: Len = 638363, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69141, tnet num: 19672, tinst num: 8238, tnode num: 94054, tedge num: 113905.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.896252s wall, 1.906250s user + 0.000000s system = 1.906250s CPU (100.5%)

RUN-1004 : used memory is 618 MB, reserved memory is 619 MB, peak memory is 735 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3833/19674.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 739808, over cnt = 2375(6%), over = 3749, worst = 7
PHY-1002 : len = 752656, over cnt = 1418(4%), over = 1968, worst = 6
PHY-1002 : len = 769896, over cnt = 547(1%), over = 695, worst = 6
PHY-1002 : len = 776936, over cnt = 206(0%), over = 264, worst = 6
PHY-1002 : len = 782296, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.631248s wall, 2.734375s user + 0.046875s system = 2.781250s CPU (170.5%)

PHY-1001 : Congestion index: top1 = 49.83, top5 = 44.73, top10 = 42.06, top15 = 40.32.
PHY-1001 : End incremental global routing;  1.916063s wall, 3.031250s user + 0.046875s system = 3.078125s CPU (160.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19672 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.852935s wall, 0.828125s user + 0.015625s system = 0.843750s CPU (98.9%)

OPT-1001 : 3 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8172 has valid locations, 20 needs to be replaced
PHY-3001 : design contains 8255 instances, 8140 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 641314
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17843/19692.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 784712, over cnt = 41(0%), over = 42, worst = 2
PHY-1002 : len = 784680, over cnt = 17(0%), over = 17, worst = 1
PHY-1002 : len = 784752, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 784832, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 784912, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.659404s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (109.0%)

PHY-1001 : Congestion index: top1 = 49.98, top5 = 44.77, top10 = 42.09, top15 = 40.38.
PHY-3001 : End congestion estimation;  0.950359s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (106.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69248, tnet num: 19690, tinst num: 8255, tnode num: 94193, tedge num: 114044.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.978879s wall, 1.968750s user + 0.000000s system = 1.968750s CPU (99.5%)

RUN-1004 : used memory is 648 MB, reserved memory is 636 MB, peak memory is 735 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19690 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.872270s wall, 2.843750s user + 0.015625s system = 2.859375s CPU (99.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(221): len = 640957, overlap = 0.25
PHY-3002 : Step(222): len = 640794, overlap = 0
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17836/19692.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 783784, over cnt = 48(0%), over = 52, worst = 4
PHY-1002 : len = 783800, over cnt = 23(0%), over = 23, worst = 1
PHY-1002 : len = 783904, over cnt = 12(0%), over = 12, worst = 1
PHY-1002 : len = 784024, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 784072, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.649797s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (108.2%)

PHY-1001 : Congestion index: top1 = 49.87, top5 = 44.80, top10 = 42.20, top15 = 40.42.
PHY-3001 : End congestion estimation;  0.935312s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (106.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19690 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.854594s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000635475
PHY-3002 : Step(223): len = 640752, overlap = 2.75
PHY-3002 : Step(224): len = 640713, overlap = 2.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005856s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 640753, Over = 0
PHY-3001 : End spreading;  0.065272s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (71.8%)

PHY-3001 : Final: Len = 640753, Over = 0
PHY-3001 : End incremental placement;  6.165449s wall, 6.296875s user + 0.093750s system = 6.390625s CPU (103.7%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  9.383662s wall, 10.656250s user + 0.171875s system = 10.828125s CPU (115.4%)

OPT-1001 : Current memory(MB): used = 723, reserve = 709, peak = 735.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17841/19692.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 784096, over cnt = 14(0%), over = 16, worst = 2
PHY-1002 : len = 784152, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 784216, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 784256, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 784272, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.625732s wall, 0.656250s user + 0.000000s system = 0.656250s CPU (104.9%)

PHY-1001 : Congestion index: top1 = 49.91, top5 = 44.73, top10 = 42.10, top15 = 40.35.
OPT-1001 : End congestion update;  0.902452s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (102.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19690 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.707499s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (101.6%)

OPT-0007 : Start: WNS 4060 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.614495s wall, 1.640625s user + 0.000000s system = 1.640625s CPU (101.6%)

OPT-1001 : Current memory(MB): used = 723, reserve = 709, peak = 735.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19690 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.723164s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (95.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17860/19692.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 784272, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.111144s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (98.4%)

PHY-1001 : Congestion index: top1 = 49.91, top5 = 44.73, top10 = 42.10, top15 = 40.35.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19690 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.704059s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (99.9%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4060 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 49.482759
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4060ps with logic level 4 
RUN-1001 :       #2 path slack 4135ps with logic level 4 
OPT-1001 : End physical optimization;  14.949429s wall, 16.218750s user + 0.171875s system = 16.390625s CPU (109.6%)

RUN-1003 : finish command "place" in  74.301859s wall, 113.593750s user + 6.078125s system = 119.671875s CPU (161.1%)

RUN-1004 : used memory is 606 MB, reserved memory is 596 MB, peak memory is 735 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.557549s wall, 2.687500s user + 0.000000s system = 2.687500s CPU (172.5%)

RUN-1004 : used memory is 606 MB, reserved memory is 596 MB, peak memory is 735 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8257 instances
RUN-1001 : 4065 mslices, 4075 lslices, 60 pads, 52 brams, 0 dsps
RUN-1001 : There are total 19692 nets
RUN-1001 : 13640 nets have 2 pins
RUN-1001 : 4625 nets have [3 - 5] pins
RUN-1001 : 866 nets have [6 - 10] pins
RUN-1001 : 416 nets have [11 - 20] pins
RUN-1001 : 135 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69248, tnet num: 19690, tinst num: 8255, tnode num: 94193, tedge num: 114044.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.664157s wall, 1.656250s user + 0.000000s system = 1.656250s CPU (99.5%)

RUN-1004 : used memory is 595 MB, reserved memory is 577 MB, peak memory is 735 MB
PHY-1001 : 4065 mslices, 4075 lslices, 60 pads, 52 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19690 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 719056, over cnt = 2466(7%), over = 4155, worst = 7
PHY-1002 : len = 737512, over cnt = 1485(4%), over = 2101, worst = 7
PHY-1002 : len = 754952, over cnt = 643(1%), over = 831, worst = 6
PHY-1002 : len = 768504, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 768912, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.762610s wall, 3.015625s user + 0.062500s system = 3.078125s CPU (174.6%)

PHY-1001 : Congestion index: top1 = 49.48, top5 = 44.39, top10 = 41.68, top15 = 39.92.
PHY-1001 : End global routing;  2.085384s wall, 3.328125s user + 0.078125s system = 3.406250s CPU (163.3%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 709, reserve = 699, peak = 735.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 975, reserve = 965, peak = 975.
PHY-1001 : End build detailed router design. 4.464919s wall, 4.328125s user + 0.062500s system = 4.390625s CPU (98.3%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 193880, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.855248s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (98.7%)

PHY-1001 : Current memory(MB): used = 1011, reserve = 1003, peak = 1011.
PHY-1001 : End phase 1; 0.863860s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (99.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 55% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.83022e+06, over cnt = 1487(0%), over = 1495, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1029, reserve = 1018, peak = 1029.
PHY-1001 : End initial routed; 19.032335s wall, 52.453125s user + 0.250000s system = 52.703125s CPU (276.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18468(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.392   |   0.000   |   0   
RUN-1001 :   Hold   |   0.133   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.284612s wall, 3.281250s user + 0.000000s system = 3.281250s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 1038, reserve = 1027, peak = 1038.
PHY-1001 : End phase 2; 22.317088s wall, 55.734375s user + 0.250000s system = 55.984375s CPU (250.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.83022e+06, over cnt = 1487(0%), over = 1495, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.229499s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (95.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.8145e+06, over cnt = 543(0%), over = 543, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 1.055381s wall, 1.609375s user + 0.000000s system = 1.609375s CPU (152.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.81434e+06, over cnt = 108(0%), over = 108, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.489729s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (153.1%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.81528e+06, over cnt = 35(0%), over = 35, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.260443s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (120.0%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.81564e+06, over cnt = 8(0%), over = 8, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.221642s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (105.7%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.81577e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.239230s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (104.5%)

PHY-1001 : ===== DR Iter 6 =====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 6; 0.229408s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (115.8%)

PHY-1001 : ===== DR Iter 7 =====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 7; 0.327326s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (100.2%)

PHY-1001 : ===== DR Iter 8 =====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 8; 0.154943s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (100.8%)

PHY-1001 : ==== DR Iter 9 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 9; 0.150609s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (93.4%)

PHY-1001 : ==== DR Iter 10 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 10; 0.170680s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (109.9%)

PHY-1001 : ==== DR Iter 11 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 11; 0.193623s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (145.3%)

PHY-1001 : ==== DR Iter 12 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 12; 0.290938s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (96.7%)

PHY-1001 : ===== DR Iter 13 =====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 13; 0.156154s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (100.1%)

PHY-1001 : ==== DR Iter 14 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 14; 0.155787s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (100.3%)

PHY-1001 : ==== DR Iter 15 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 15; 0.168630s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (101.9%)

PHY-1001 : ==== DR Iter 16 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 16; 0.193919s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (96.7%)

PHY-1001 : ==== DR Iter 17 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 17; 0.286609s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (98.1%)

PHY-1001 : ==== DR Iter 18 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 18; 0.356243s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (100.9%)

PHY-1001 : ===== DR Iter 19 =====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 19; 0.161563s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (106.4%)

PHY-1001 : ==== DR Iter 20 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 20; 0.156776s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (99.7%)

PHY-1001 : ==== DR Iter 21 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 21; 0.170324s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (100.9%)

PHY-1001 : ==== DR Iter 22 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 22; 0.206611s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (90.8%)

PHY-1001 : ==== DR Iter 23 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 23; 0.286287s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (103.7%)

PHY-1001 : ==== DR Iter 24 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 24; 0.352101s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (97.6%)

PHY-1001 : ==== DR Iter 25 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 25; 0.856501s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (100.3%)

PHY-1001 : ===== DR Iter 26 =====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 26; 0.171135s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (100.4%)

PHY-1001 : ==== DR Iter 27 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 27; 0.209789s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (126.6%)

PHY-1001 : ==== DR Iter 28 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 28; 0.301953s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (98.3%)

PHY-1001 : ==== DR Iter 29 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 29; 0.653891s wall, 0.578125s user + 0.000000s system = 0.578125s CPU (88.4%)

PHY-1001 : ==== DR Iter 30 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 30; 1.446214s wall, 1.250000s user + 0.000000s system = 1.250000s CPU (86.4%)

PHY-1001 : ==== DR Iter 31 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 31; 2.296669s wall, 1.921875s user + 0.015625s system = 1.937500s CPU (84.4%)

PHY-1001 : ==== DR Iter 32 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 32; 4.296441s wall, 4.234375s user + 0.015625s system = 4.250000s CPU (98.9%)

PHY-1001 : ==== DR Iter 33 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 33; 4.053003s wall, 3.968750s user + 0.015625s system = 3.984375s CPU (98.3%)

PHY-1001 : ===== DR Iter 34 =====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 34; 0.396166s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (98.6%)

PHY-1001 : ==== DR Iter 35 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 35; 0.323428s wall, 0.359375s user + 0.015625s system = 0.375000s CPU (115.9%)

PHY-1001 : ==== DR Iter 36 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 36; 0.191806s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (97.8%)

PHY-1001 : ==== DR Iter 37 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 37; 0.227779s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (102.9%)

PHY-1001 : ==== DR Iter 38 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 38; 0.343298s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (100.1%)

PHY-1001 : ==== DR Iter 39 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 39; 0.353325s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (97.3%)

PHY-1001 : ==== DR Iter 40 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 40; 0.874445s wall, 0.875000s user + 0.000000s system = 0.875000s CPU (100.1%)

PHY-1001 : ==== DR Iter 41 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 41; 0.883773s wall, 0.890625s user + 0.000000s system = 0.890625s CPU (100.8%)

PHY-1001 : ==== DR Iter 42 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 42; 0.882688s wall, 0.875000s user + 0.000000s system = 0.875000s CPU (99.1%)

PHY-1001 : ===== DR Iter 43 =====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 43; 0.153651s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (111.9%)

PHY-1001 : ==== DR Iter 44 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 44; 0.156885s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (99.6%)

PHY-1001 : ==== DR Iter 45 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 45; 0.173686s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (99.0%)

PHY-1001 : ==== DR Iter 46 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 46; 0.202855s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (100.1%)

PHY-1001 : ==== DR Iter 47 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 47; 0.303205s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (92.8%)

PHY-1001 : ==== DR Iter 48 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 48; 0.364654s wall, 0.359375s user + 0.015625s system = 0.375000s CPU (102.8%)

PHY-1001 : ==== DR Iter 49 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 49; 0.844341s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (99.9%)

PHY-1001 : ==== DR Iter 50 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 50; 0.876922s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (98.0%)

PHY-1001 : ==== DR Iter 51 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 51; 0.867700s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (99.0%)

PHY-1001 : ==== DR Iter 52 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 52; 0.869958s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (98.8%)

PHY-1001 : ===== DR Iter 53 =====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 53; 0.158594s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (98.5%)

PHY-1001 : ==== DR Iter 54 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 54; 0.153481s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (91.6%)

PHY-1001 : ==== DR Iter 55 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 55; 0.170483s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (100.8%)

PHY-1001 : ==== DR Iter 56 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 56; 0.193959s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (104.7%)

PHY-1001 : ==== DR Iter 57 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 57; 0.288905s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (97.4%)

PHY-1001 : ==== DR Iter 58 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 58; 0.342136s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (100.5%)

PHY-1001 : ==== DR Iter 59 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 59; 0.852793s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (98.9%)

PHY-1001 : ==== DR Iter 60 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 60; 0.851294s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (99.1%)

PHY-1001 : ==== DR Iter 61 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 61; 0.869325s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (97.1%)

PHY-1001 : ==== DR Iter 62 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 62; 0.893003s wall, 0.875000s user + 0.000000s system = 0.875000s CPU (98.0%)

PHY-1001 : ==== DR Iter 63 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 63; 0.922781s wall, 0.906250s user + 0.000000s system = 0.906250s CPU (98.2%)

PHY-1001 : ===== DR Iter 64 =====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 64; 0.158468s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (98.6%)

PHY-1001 : ==== DR Iter 65 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 65; 0.154432s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (101.2%)

PHY-1001 : ==== DR Iter 66 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 66; 0.170288s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (91.8%)

PHY-1001 : ==== DR Iter 67 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 67; 0.200368s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (117.0%)

PHY-1001 : ==== DR Iter 68 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 68; 0.353543s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (101.6%)

PHY-1001 : ==== DR Iter 69 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 69; 0.341117s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (100.8%)

PHY-1001 : ==== DR Iter 70 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 70; 0.823584s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (100.6%)

PHY-1001 : ==== DR Iter 71 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 71; 0.873259s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (98.4%)

PHY-1001 : ==== DR Iter 72 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 72; 0.868056s wall, 0.875000s user + 0.000000s system = 0.875000s CPU (100.8%)

PHY-1001 : ==== DR Iter 73 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 73; 0.881509s wall, 0.875000s user + 0.000000s system = 0.875000s CPU (99.3%)

PHY-1001 : ==== DR Iter 74 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 74; 0.858711s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (98.3%)

PHY-1001 : ==== DR Iter 75 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 75; 0.859695s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (98.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18468(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.199   |   0.000   |   0   
RUN-1001 :   Hold   |   0.133   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.290664s wall, 3.296875s user + 0.000000s system = 3.296875s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 1048, reserve = 1038, peak = 1048.
PHY-1001 : End phase 3; 46.351936s wall, 46.390625s user + 0.171875s system = 46.562500s CPU (100.5%)

PHY-1001 : ===== Detail Route Phase 4 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.222517s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (91.3%)

PHY-0007 : Phase: 4; Congestion: {, , , }; Timing: {2.199ns, 0.000ns, 0}
PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.148560s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (94.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.171184s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (100.4%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.244849s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (95.7%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.161108s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (97.0%)

PHY-1001 : ==== DR Iter 5 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.153742s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (101.6%)

PHY-1001 : ==== DR Iter 6 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 6; 0.170102s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (91.9%)

PHY-1001 : ==== DR Iter 7 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 7; 0.199849s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (101.6%)

PHY-1001 : ==== DR Iter 8 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 8; 0.288160s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (103.0%)

PHY-1001 : ===== DR Iter 9 =====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 9; 0.159628s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (97.9%)

PHY-1001 : ==== DR Iter 10 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 10; 0.157899s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (158.3%)

PHY-1001 : ==== DR Iter 11 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 11; 0.179046s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (113.4%)

PHY-1001 : ==== DR Iter 12 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 12; 0.197829s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (102.7%)

PHY-1001 : ==== DR Iter 13 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 13; 0.303518s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (97.8%)

PHY-1001 : ==== DR Iter 14 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 14; 0.342145s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (100.5%)

PHY-1001 : ===== DR Iter 15 =====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 15; 0.161314s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (96.9%)

PHY-1001 : ==== DR Iter 16 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 16; 0.156876s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (99.6%)

PHY-1001 : ==== DR Iter 17 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 17; 0.171309s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (100.3%)

PHY-1001 : ==== DR Iter 18 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 18; 0.197649s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (94.9%)

PHY-1001 : ==== DR Iter 19 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 19; 0.291044s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (102.0%)

PHY-1001 : ==== DR Iter 20 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 20; 0.336904s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (102.0%)

PHY-1001 : ==== DR Iter 21 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 21; 0.844933s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (99.9%)

PHY-1001 : ===== DR Iter 22 =====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 22; 0.153793s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (101.6%)

PHY-1001 : ==== DR Iter 23 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 23; 0.156559s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (99.8%)

PHY-1001 : ==== DR Iter 24 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 24; 0.172455s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (99.7%)

PHY-1001 : ==== DR Iter 25 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 25; 0.192482s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (105.5%)

PHY-1001 : ==== DR Iter 26 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 26; 0.278703s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (106.5%)

PHY-1001 : ==== DR Iter 27 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 27; 0.363160s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (99.0%)

PHY-1001 : ==== DR Iter 28 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 28; 0.841255s wall, 0.859375s user + 0.015625s system = 0.875000s CPU (104.0%)

PHY-1001 : ==== DR Iter 29 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 29; 0.861575s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (97.9%)

PHY-1001 : ===== DR Iter 30 =====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 30; 0.155549s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (110.5%)

PHY-1001 : ==== DR Iter 31 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 31; 0.153148s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (102.0%)

PHY-1001 : ==== DR Iter 32 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 32; 0.189573s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (98.9%)

PHY-1001 : ==== DR Iter 33 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 33; 0.201635s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (100.7%)

PHY-1001 : ==== DR Iter 34 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 34; 0.289055s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (97.3%)

PHY-1001 : ==== DR Iter 35 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 35; 0.344806s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (99.7%)

PHY-1001 : ==== DR Iter 36 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 36; 0.851278s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (101.0%)

PHY-1001 : ==== DR Iter 37 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 37; 0.862050s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (99.7%)

PHY-1001 : ==== DR Iter 38 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 38; 0.855543s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (100.4%)

PHY-1001 : ===== DR Iter 39 =====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 39; 0.158299s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (98.7%)

PHY-1001 : ==== DR Iter 40 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 40; 0.153222s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (142.8%)

PHY-1001 : ==== DR Iter 41 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 41; 0.166772s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (103.1%)

PHY-1001 : ==== DR Iter 42 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 42; 0.195788s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (95.8%)

PHY-1001 : ==== DR Iter 43 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 43; 0.307515s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (101.6%)

PHY-1001 : ==== DR Iter 44 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 44; 0.338769s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (101.5%)

PHY-1001 : ==== DR Iter 45 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 45; 0.828762s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (98.0%)

PHY-1001 : ==== DR Iter 46 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 46; 0.869933s wall, 0.875000s user + 0.000000s system = 0.875000s CPU (100.6%)

PHY-1001 : ==== DR Iter 47 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 47; 0.863352s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (99.5%)

PHY-1001 : ==== DR Iter 48 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 48; 0.858394s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (100.1%)

PHY-1001 : ===== DR Iter 49 =====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 49; 0.161918s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (96.5%)

PHY-1001 : ==== DR Iter 50 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 50; 0.154905s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (100.9%)

PHY-1001 : ==== DR Iter 51 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 51; 0.168406s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (92.8%)

PHY-1001 : ==== DR Iter 52 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 52; 0.194325s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (112.6%)

PHY-1001 : ==== DR Iter 53 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 53; 0.289456s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (97.2%)

PHY-1001 : ==== DR Iter 54 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 54; 0.343989s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (99.9%)

PHY-1001 : ==== DR Iter 55 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 55; 0.843788s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (100.0%)

PHY-1001 : ==== DR Iter 56 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 56; 0.857326s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (100.2%)

PHY-1001 : ==== DR Iter 57 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 57; 0.858734s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (100.1%)

PHY-1001 : ==== DR Iter 58 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 58; 0.876965s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (98.0%)

PHY-1001 : ==== DR Iter 59 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 59; 0.856200s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (100.4%)

PHY-1001 : ===== DR Iter 60 =====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 60; 0.158760s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (137.8%)

PHY-1001 : ==== DR Iter 61 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 61; 0.152526s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (102.4%)

PHY-1001 : ==== DR Iter 62 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 62; 0.169817s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (92.0%)

PHY-1001 : ==== DR Iter 63 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 63; 0.194170s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (104.6%)

PHY-1001 : ==== DR Iter 64 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 64; 0.286662s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (98.1%)

PHY-1001 : ==== DR Iter 65 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 65; 0.339303s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (101.3%)

PHY-1001 : ==== DR Iter 66 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 66; 0.832310s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (93.9%)

PHY-1001 : ==== DR Iter 67 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 67; 0.876402s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (98.1%)

PHY-1001 : ==== DR Iter 68 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 68; 0.857217s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (100.3%)

PHY-1001 : ==== DR Iter 69 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 69; 0.873304s wall, 0.875000s user + 0.000000s system = 0.875000s CPU (100.2%)

PHY-1001 : ==== DR Iter 70 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 70; 0.855913s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (100.4%)

PHY-1001 : ==== DR Iter 71 ====
PHY-1022 : len = 1.8159e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 71; 0.849789s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (99.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18468(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.199   |   0.000   |   0   
RUN-1001 :   Hold   |   0.133   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.279691s wall, 3.281250s user + 0.000000s system = 3.281250s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1049, reserve = 1039, peak = 1049.
PHY-1001 : End phase 4; 32.378557s wall, 32.453125s user + 0.125000s system = 32.578125s CPU (100.6%)

PHY-1003 : Routed, final wirelength = 1.8159e+06
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 357 feed throughs used by 310 nets
PHY-1001 : Current memory(MB): used = 1136, reserve = 1130, peak = 1136.
PHY-1001 : End export database. 2.411639s wall, 2.390625s user + 0.015625s system = 2.406250s CPU (99.8%)

PHY-1001 : Fixing routing violation through ECO...
RUN-1002 : start command "place -eco"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8257 instances
RUN-1001 : 4065 mslices, 4075 lslices, 60 pads, 52 brams, 0 dsps
RUN-1001 : There are total 19692 nets
RUN-1001 : 13640 nets have 2 pins
RUN-1001 : 4625 nets have [3 - 5] pins
RUN-1001 : 866 nets have [6 - 10] pins
RUN-1001 : 416 nets have [11 - 20] pins
RUN-1001 : 135 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |      0      
RUN-1001 :   No   |  No   |  Yes  |      0      
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |      0      
RUN-1001 :   Yes  |  No   |  Yes  |      0      
RUN-1001 :   Yes  |  Yes  |  No   |      0      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |   1   |     1      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 0
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: (1 27 1) is for feedthrough
PHY-3001 : eco cells: (1 40 2) is for feedthrough
PHY-3001 : eco cells: (2 55 2) is for feedthrough
PHY-3001 : eco cells: (2 61 2) is for feedthrough
PHY-3001 : eco cells: (3 28 0) is for feedthrough
PHY-3001 : eco cells: (3 31 3) is for feedthrough
PHY-3001 : eco cells: (3 49 3) is for feedthrough
PHY-3001 : eco cells: (3 51 1) is for feedthrough
PHY-3001 : eco cells: (3 65 3) is for feedthrough
PHY-3001 : eco cells: (4 25 1) is for feedthrough
PHY-3001 : eco cells: (4 37 3) is for feedthrough
PHY-3001 : eco cells: (4 39 3) is for feedthrough
PHY-3001 : eco cells: (4 51 0) is for feedthrough
PHY-3001 : eco cells: (5 10 2) is for feedthrough
PHY-3001 : eco cells: (5 15 1) is for feedthrough
PHY-3001 : eco cells: (5 22 1) is for feedthrough
PHY-3001 : eco cells: (5 35 3) is for feedthrough
PHY-3001 : eco cells: (5 43 0) is for feedthrough
PHY-3001 : eco cells: (5 50 1) is for feedthrough
PHY-3001 : eco cells: (5 53 0) is for feedthrough
PHY-3001 : eco cells: (6 19 0) is for feedthrough
PHY-3001 : eco cells: (6 49 3) is for feedthrough
PHY-3001 : eco cells: (6 53 2) is for feedthrough
PHY-3001 : eco cells: (6 54 1) is for feedthrough
PHY-3001 : eco cells: (6 56 1) is for feedthrough
PHY-3001 : eco cells: (6 65 1) is for feedthrough
PHY-3001 : eco cells: (7 17 2) is for feedthrough
PHY-3001 : eco cells: (7 19 2) is for feedthrough
PHY-3001 : eco cells: (7 40 3) is for feedthrough
PHY-3001 : eco cells: (7 48 0) is for feedthrough
PHY-3001 : eco cells: (7 53 2) is for feedthrough
PHY-3001 : eco cells: (7 65 1) is for feedthrough
PHY-3001 : eco cells: (9 11 1) is for feedthrough
PHY-3001 : eco cells: (9 12 3) is for feedthrough
PHY-3001 : eco cells: (9 23 2) is for feedthrough
PHY-3001 : eco cells: (9 54 3) is for feedthrough
PHY-3001 : eco cells: (9 61 1) is for feedthrough
PHY-3001 : eco cells: (10 7 0) is for feedthrough
PHY-3001 : eco cells: (10 8 0) is for feedthrough
PHY-3001 : eco cells: (10 8 1) is for feedthrough
PHY-3001 : eco cells: (10 9 1) is for feedthrough
PHY-3001 : eco cells: (10 13 0) is for feedthrough
PHY-3001 : eco cells: (10 14 1) is for feedthrough
PHY-3001 : eco cells: (10 16 0) is for feedthrough
PHY-3001 : eco cells: (10 19 0) is for feedthrough
PHY-3001 : eco cells: (10 22 1) is for feedthrough
PHY-3001 : eco cells: (10 34 2) is for feedthrough
PHY-3001 : eco cells: (10 41 1) is for feedthrough
PHY-3001 : eco cells: (10 47 3) is for feedthrough
PHY-3001 : eco cells: (10 62 0) is for feedthrough
PHY-3001 : eco cells: (11 8 3) is for feedthrough
PHY-3001 : eco cells: (11 10 2) is for feedthrough
PHY-3001 : eco cells: (11 12 2) is for feedthrough
PHY-3001 : eco cells: (11 13 3) is for feedthrough
PHY-3001 : eco cells: (11 15 2) is for feedthrough
PHY-3001 : eco cells: (11 16 1) is for feedthrough
PHY-3001 : eco cells: (11 17 1) is for feedthrough
PHY-3001 : eco cells: (11 23 0) is for feedthrough
PHY-3001 : eco cells: (11 26 0) is for feedthrough
PHY-3001 : eco cells: (11 41 1) is for feedthrough
PHY-3001 : eco cells: (11 49 2) is for feedthrough
PHY-3001 : eco cells: (11 56 0) is for feedthrough
PHY-3001 : eco cells: (11 69 0) is for feedthrough
PHY-3001 : eco cells: (12 11 2) is for feedthrough
PHY-3001 : eco cells: (12 13 2) is for feedthrough
PHY-3001 : eco cells: (12 14 2) is for feedthrough
PHY-3001 : eco cells: (12 16 0) is for feedthrough
PHY-3001 : eco cells: (12 19 0) is for feedthrough
PHY-3001 : eco cells: (12 22 0) is for feedthrough
PHY-3001 : eco cells: (12 25 3) is for feedthrough
PHY-3001 : eco cells: (12 31 2) is for feedthrough
PHY-3001 : eco cells: (12 57 3) is for feedthrough
PHY-3001 : eco cells: (13 6 2) is for feedthrough
PHY-3001 : eco cells: (13 8 2) is for feedthrough
PHY-3001 : eco cells: (13 13 2) is for feedthrough
PHY-3001 : eco cells: (13 14 1) is for feedthrough
PHY-3001 : eco cells: (13 14 3) is for feedthrough
PHY-3001 : eco cells: (13 15 2) is for feedthrough
PHY-3001 : eco cells: (13 16 0) is for feedthrough
PHY-3001 : eco cells: (13 19 1) is for feedthrough
PHY-3001 : eco cells: (13 23 1) is for feedthrough
PHY-3001 : eco cells: (13 24 3) is for feedthrough
PHY-3001 : eco cells: (13 55 3) is for feedthrough
PHY-3001 : eco cells: (13 56 3) is for feedthrough
PHY-3001 : eco cells: (14 11 0) is for feedthrough
PHY-3001 : eco cells: (14 19 1) is for feedthrough
PHY-3001 : eco cells: (14 20 0) is for feedthrough
PHY-3001 : eco cells: (14 21 2) is for feedthrough
PHY-3001 : eco cells: (14 23 0) is for feedthrough
PHY-3001 : eco cells: (14 48 2) is for feedthrough
PHY-3001 : eco cells: (15 12 0) is for feedthrough
PHY-3001 : eco cells: (15 19 3) is for feedthrough
PHY-3001 : eco cells: (15 26 3) is for feedthrough
PHY-3001 : eco cells: (15 38 0) is for feedthrough
PHY-3001 : eco cells: (17 16 0) is for feedthrough
PHY-3001 : eco cells: (17 16 1) is for feedthrough
PHY-3001 : eco cells: (17 23 2) is for feedthrough
PHY-3001 : eco cells: (17 24 3) is for feedthrough
PHY-3001 : eco cells: (17 54 0) is for feedthrough
PHY-3001 : eco cells: (17 58 2) is for feedthrough
PHY-3001 : eco cells: (17 69 1) is for feedthrough
PHY-3001 : eco cells: (18 6 2) is for feedthrough
PHY-3001 : eco cells: (18 8 2) is for feedthrough
PHY-3001 : eco cells: (18 10 3) is for feedthrough
PHY-3001 : eco cells: (18 15 1) is for feedthrough
PHY-3001 : eco cells: (18 16 0) is for feedthrough
PHY-3001 : eco cells: (18 16 3) is for feedthrough
PHY-3001 : eco cells: (18 17 2) is for feedthrough
PHY-3001 : eco cells: (18 18 2) is for feedthrough
PHY-3001 : eco cells: (18 19 0) is for feedthrough
PHY-3001 : eco cells: (18 19 3) is for feedthrough
PHY-3001 : eco cells: (18 24 0) is for feedthrough
PHY-3001 : eco cells: (18 46 3) is for feedthrough
PHY-3001 : eco cells: (18 63 3) is for feedthrough
PHY-3001 : eco cells: (18 68 2) is for feedthrough
PHY-3001 : eco cells: (19 13 2) is for feedthrough
PHY-3001 : eco cells: (19 16 1) is for feedthrough
PHY-3001 : eco cells: (19 18 0) is for feedthrough
PHY-3001 : eco cells: (19 19 0) is for feedthrough
PHY-3001 : eco cells: (19 20 0) is for feedthrough
PHY-3001 : eco cells: (19 23 0) is for feedthrough
PHY-3001 : eco cells: (19 23 3) is for feedthrough
PHY-3001 : eco cells: (19 24 1) is for feedthrough
PHY-3001 : eco cells: (19 32 0) is for feedthrough
PHY-3001 : eco cells: (19 36 0) is for feedthrough
PHY-3001 : eco cells: (19 47 3) is for feedthrough
PHY-3001 : eco cells: (19 63 2) is for feedthrough
PHY-3001 : eco cells: (19 64 1) is for feedthrough
PHY-3001 : eco cells: (19 65 0) is for feedthrough
PHY-3001 : eco cells: (19 66 0) is for feedthrough
PHY-3001 : eco cells: (20 13 0) is for feedthrough
PHY-3001 : eco cells: (20 14 1) is for feedthrough
PHY-3001 : eco cells: (20 15 2) is for feedthrough
PHY-3001 : eco cells: (20 16 2) is for feedthrough
PHY-3001 : eco cells: (20 18 1) is for feedthrough
PHY-3001 : eco cells: (20 21 0) is for feedthrough
PHY-3001 : eco cells: (20 21 1) is for feedthrough
PHY-3001 : eco cells: (20 44 1) is for feedthrough
PHY-3001 : eco cells: (20 46 1) is for feedthrough
PHY-3001 : eco cells: (20 47 3) is for feedthrough
PHY-3001 : eco cells: (20 61 0) is for feedthrough
PHY-3001 : eco cells: (20 62 1) is for feedthrough
PHY-3001 : eco cells: (20 63 0) is for feedthrough
PHY-3001 : eco cells: (20 64 1) is for feedthrough
PHY-3001 : eco cells: (20 68 2) is for feedthrough
PHY-3001 : eco cells: (20 68 3) is for feedthrough
PHY-3001 : eco cells: (21 9 1) is for feedthrough
PHY-3001 : eco cells: (21 10 2) is for feedthrough
PHY-3001 : eco cells: (21 12 1) is for feedthrough
PHY-3001 : eco cells: (21 13 0) is for feedthrough
PHY-3001 : eco cells: (21 13 1) is for feedthrough
PHY-3001 : eco cells: (21 19 0) is for feedthrough
PHY-3001 : eco cells: (21 19 1) is for feedthrough
PHY-3001 : eco cells: (21 21 2) is for feedthrough
PHY-3001 : eco cells: (21 23 2) is for feedthrough
PHY-3001 : eco cells: (21 61 1) is for feedthrough
PHY-3001 : eco cells: (21 62 1) is for feedthrough
PHY-3001 : eco cells: (22 9 0) is for feedthrough
PHY-3001 : eco cells: (22 13 2) is for feedthrough
PHY-3001 : eco cells: (22 16 2) is for feedthrough
PHY-3001 : eco cells: (22 40 0) is for feedthrough
PHY-3001 : eco cells: (22 41 2) is for feedthrough
PHY-3001 : eco cells: (22 62 1) is for feedthrough
PHY-3001 : eco cells: (22 64 0) is for feedthrough
PHY-3001 : eco cells: (23 10 0) is for feedthrough
PHY-3001 : eco cells: (23 10 1) is for feedthrough
PHY-3001 : eco cells: (23 19 1) is for feedthrough
PHY-3001 : eco cells: (23 19 3) is for feedthrough
PHY-3001 : eco cells: (23 64 0) is for feedthrough
PHY-3001 : eco cells: (23 66 2) is for feedthrough
PHY-3001 : eco cells: (25 4 1) is for feedthrough
PHY-3001 : eco cells: (25 9 2) is for feedthrough
PHY-3001 : eco cells: (25 10 0) is for feedthrough
PHY-3001 : eco cells: (25 12 0) is for feedthrough
PHY-3001 : eco cells: (25 18 0) is for feedthrough
PHY-3001 : eco cells: (25 22 1) is for feedthrough
PHY-3001 : eco cells: (25 26 3) is for feedthrough
PHY-3001 : eco cells: (25 30 0) is for feedthrough
PHY-3001 : eco cells: (25 32 0) is for feedthrough
PHY-3001 : eco cells: (25 41 3) is for feedthrough
PHY-3001 : eco cells: (25 54 0) is for feedthrough
PHY-3001 : eco cells: (25 69 3) is for feedthrough
PHY-3001 : eco cells: (26 6 0) is for feedthrough
PHY-3001 : eco cells: (26 9 0) is for feedthrough
PHY-3001 : eco cells: (26 20 2) is for feedthrough
PHY-3001 : eco cells: (26 21 3) is for feedthrough
PHY-3001 : eco cells: (26 22 0) is for feedthrough
PHY-3001 : eco cells: (26 53 2) is for feedthrough
PHY-3001 : eco cells: (26 54 1) is for feedthrough
PHY-3001 : eco cells: (26 54 2) is for feedthrough
PHY-3001 : eco cells: (26 65 1) is for feedthrough
PHY-3001 : eco cells: (27 6 1) is for feedthrough
PHY-3001 : eco cells: (27 8 2) is for feedthrough
PHY-3001 : eco cells: (27 10 1) is for feedthrough
PHY-3001 : eco cells: (27 11 1) is for feedthrough
PHY-3001 : eco cells: (27 14 2) is for feedthrough
PHY-3001 : eco cells: (27 16 1) is for feedthrough
PHY-3001 : eco cells: (27 30 3) is for feedthrough
PHY-3001 : eco cells: (27 31 0) is for feedthrough
PHY-3001 : eco cells: (27 32 3) is for feedthrough
PHY-3001 : eco cells: (27 37 0) is for feedthrough
PHY-3001 : eco cells: (27 41 3) is for feedthrough
PHY-3001 : eco cells: (27 47 3) is for feedthrough
PHY-3001 : eco cells: (27 55 1) is for feedthrough
PHY-3001 : eco cells: (27 57 0) is for feedthrough
PHY-3001 : eco cells: (27 69 1) is for feedthrough
PHY-3001 : eco cells: (28 8 1) is for feedthrough
PHY-3001 : eco cells: (28 18 1) is for feedthrough
PHY-3001 : eco cells: (28 19 2) is for feedthrough
PHY-3001 : eco cells: (28 20 1) is for feedthrough
PHY-3001 : eco cells: (28 22 1) is for feedthrough
PHY-3001 : eco cells: (28 24 1) is for feedthrough
PHY-3001 : eco cells: (28 25 3) is for feedthrough
PHY-3001 : eco cells: (28 26 1) is for feedthrough
PHY-3001 : eco cells: (28 26 2) is for feedthrough
PHY-3001 : eco cells: (28 31 0) is for feedthrough
PHY-3001 : eco cells: (28 33 0) is for feedthrough
PHY-3001 : eco cells: (28 40 1) is for feedthrough
PHY-3001 : eco cells: (28 45 2) is for feedthrough
PHY-3001 : eco cells: (28 46 2) is for feedthrough
PHY-3001 : eco cells: (28 60 3) is for feedthrough
PHY-3001 : eco cells: (29 20 1) is for feedthrough
PHY-3001 : eco cells: (29 20 3) is for feedthrough
PHY-3001 : eco cells: (29 21 2) is for feedthrough
PHY-3001 : eco cells: (29 25 1) is for feedthrough
PHY-3001 : eco cells: (29 42 1) is for feedthrough
PHY-3001 : eco cells: (29 46 0) is for feedthrough
PHY-3001 : eco cells: (29 46 1) is for feedthrough
PHY-3001 : eco cells: (29 47 0) is for feedthrough
PHY-3001 : eco cells: (29 49 0) is for feedthrough
PHY-3001 : eco cells: (29 49 1) is for feedthrough
PHY-3001 : eco cells: (29 49 3) is for feedthrough
PHY-3001 : eco cells: (29 51 1) is for feedthrough
PHY-3001 : eco cells: (29 55 1) is for feedthrough
PHY-3001 : eco cells: (29 57 0) is for feedthrough
PHY-3001 : eco cells: (29 58 0) is for feedthrough
PHY-3001 : eco cells: (29 67 1) is for feedthrough
PHY-3001 : eco cells: (30 19 1) is for feedthrough
PHY-3001 : eco cells: (30 20 1) is for feedthrough
PHY-3001 : eco cells: (30 22 0) is for feedthrough
PHY-3001 : eco cells: (30 23 0) is for feedthrough
PHY-3001 : eco cells: (30 24 1) is for feedthrough
PHY-3001 : eco cells: (30 26 0) is for feedthrough
PHY-3001 : eco cells: (30 40 3) is for feedthrough
PHY-3001 : eco cells: (30 41 0) is for feedthrough
PHY-3001 : eco cells: (30 41 2) is for feedthrough
PHY-3001 : eco cells: (30 42 1) is for feedthrough
PHY-3001 : eco cells: (30 43 2) is for feedthrough
PHY-3001 : eco cells: (30 45 0) is for feedthrough
PHY-3001 : eco cells: (30 47 3) is for feedthrough
PHY-3001 : eco cells: (30 48 0) is for feedthrough
PHY-3001 : eco cells: (30 59 0) is for feedthrough
PHY-3001 : eco cells: (30 66 3) is for feedthrough
PHY-3001 : eco cells: (31 15 1) is for feedthrough
PHY-3001 : eco cells: (31 19 0) is for feedthrough
PHY-3001 : eco cells: (31 19 2) is for feedthrough
PHY-3001 : eco cells: (31 22 1) is for feedthrough
PHY-3001 : eco cells: (31 23 2) is for feedthrough
PHY-3001 : eco cells: (31 41 0) is for feedthrough
PHY-3001 : eco cells: (31 44 0) is for feedthrough
PHY-3001 : eco cells: (31 46 0) is for feedthrough
PHY-3001 : eco cells: (31 48 2) is for feedthrough
PHY-3001 : eco cells: (31 49 0) is for feedthrough
PHY-3001 : eco cells: (33 21 2) is for feedthrough
PHY-3001 : eco cells: (33 24 1) is for feedthrough
PHY-3001 : eco cells: (33 27 2) is for feedthrough
PHY-3001 : eco cells: (33 47 3) is for feedthrough
PHY-3001 : eco cells: (33 49 3) is for feedthrough
PHY-3001 : eco cells: (33 68 0) is for feedthrough
PHY-3001 : eco cells: (34 22 3) is for feedthrough
PHY-3001 : eco cells: (34 34 0) is for feedthrough
PHY-3001 : eco cells: (34 36 1) is for feedthrough
PHY-3001 : eco cells: (34 40 1) is for feedthrough
PHY-3001 : eco cells: (34 43 3) is for feedthrough
PHY-3001 : eco cells: (34 47 3) is for feedthrough
PHY-3001 : eco cells: (34 50 2) is for feedthrough
PHY-3001 : eco cells: (34 50 3) is for feedthrough
PHY-3001 : eco cells: (34 51 2) is for feedthrough
PHY-3001 : eco cells: (34 52 3) is for feedthrough
PHY-3001 : eco cells: (34 54 0) is for feedthrough
PHY-3001 : eco cells: (34 55 0) is for feedthrough
PHY-3001 : eco cells: (35 18 3) is for feedthrough
PHY-3001 : eco cells: (35 39 2) is for feedthrough
PHY-3001 : eco cells: (35 40 0) is for feedthrough
PHY-3001 : eco cells: (35 40 1) is for feedthrough
PHY-3001 : eco cells: (35 42 0) is for feedthrough
PHY-3001 : eco cells: (35 46 3) is for feedthrough
PHY-3001 : eco cells: (35 51 0) is for feedthrough
PHY-3001 : eco cells: (35 52 2) is for feedthrough
PHY-3001 : eco cells: (35 53 0) is for feedthrough
PHY-3001 : eco cells: (35 53 1) is for feedthrough
PHY-3001 : eco cells: (35 54 0) is for feedthrough
PHY-3001 : eco cells: (35 55 1) is for feedthrough
PHY-3001 : eco cells: (35 56 1) is for feedthrough
PHY-3001 : eco cells: (35 57 0) is for feedthrough
PHY-3001 : eco cells: (35 59 0) is for feedthrough
PHY-3001 : eco cells: (35 61 1) is for feedthrough
PHY-3001 : eco cells: (36 41 1) is for feedthrough
PHY-3001 : eco cells: (36 43 1) is for feedthrough
PHY-3001 : eco cells: (36 44 0) is for feedthrough
PHY-3001 : eco cells: (36 54 0) is for feedthrough
PHY-3001 : eco cells: (36 54 1) is for feedthrough
PHY-3001 : eco cells: (36 55 0) is for feedthrough
PHY-3001 : eco cells: (36 57 1) is for feedthrough
PHY-3001 : eco cells: (36 58 1) is for feedthrough
PHY-3001 : eco cells: (36 58 3) is for feedthrough
PHY-3001 : eco cells: (36 59 1) is for feedthrough
PHY-3001 : eco cells: (36 60 0) is for feedthrough
PHY-3001 : eco cells: (36 60 3) is for feedthrough
PHY-3001 : eco cells: (36 61 0) is for feedthrough
PHY-3001 : eco cells: (37 49 1) is for feedthrough
PHY-3001 : eco cells: (37 56 0) is for feedthrough
PHY-3001 : eco cells: (37 57 0) is for feedthrough
PHY-3001 : eco cells: (37 58 0) is for feedthrough
PHY-3001 : eco cells: (37 59 3) is for feedthrough
PHY-3001 : eco cells: (37 60 0) is for feedthrough
PHY-3001 : eco cells: (38 15 0) is for feedthrough
PHY-3001 : eco cells: (38 35 0) is for feedthrough
PHY-3001 : eco cells: (38 40 3) is for feedthrough
PHY-3001 : eco cells: (38 50 1) is for feedthrough
PHY-3001 : eco cells: (38 51 3) is for feedthrough
PHY-3001 : eco cells: (39 59 3) is for feedthrough
PHY-3001 : eco cells: 8191 has valid locations, 1 needs to be replaced
PHY-3001 : design contains 8255 instances, 8140 slices, 283 macros(1445 instances: 951 mslices 494 lslices)
PHY-3001 : Start timing update ...
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 19690 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.769771s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (99.5%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 640844
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 83%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(225): len = 640850, overlap = 0
PHY-3002 : Step(226): len = 640850, overlap = 0
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.003849s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (405.9%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17856/19692.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 768992, over cnt = 3(0%), over = 5, worst = 3
PHY-1002 : len = 769024, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 769032, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 769040, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.352358s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (106.4%)

PHY-1001 : Congestion index: top1 = 49.48, top5 = 44.38, top10 = 41.67, top15 = 39.92.
PHY-3001 : End congestion estimation;  0.593767s wall, 0.625000s user + 0.000000s system = 0.625000s CPU (105.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19690 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.377938s wall, 1.375000s user + 0.000000s system = 1.375000s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(227): len = 640850, overlap = 0
PHY-3002 : Step(228): len = 640850, overlap = 0
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17860/19692.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 769040, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.081716s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (95.6%)

PHY-1001 : Congestion index: top1 = 49.48, top5 = 44.38, top10 = 41.67, top15 = 39.92.
PHY-3001 : End congestion estimation;  0.330716s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (99.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19690 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.897402s wall, 0.906250s user + 0.000000s system = 0.906250s CPU (101.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.629826
PHY-3002 : Step(229): len = 640826, overlap = 0
PHY-3002 : Step(230): len = 640784, overlap = 0
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007261s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (215.2%)

PHY-3001 : Trial Legalized: Len = 640801
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17856/19692.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 768976, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 768984, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.267824s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (99.2%)

PHY-1001 : Congestion index: top1 = 49.46, top5 = 44.39, top10 = 41.68, top15 = 39.92.
PHY-3001 : End congestion estimation;  0.551837s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (99.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19690 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.892122s wall, 0.890625s user + 0.000000s system = 0.890625s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 1.92575
PHY-3002 : Step(231): len = 640801, overlap = 0
PHY-3002 : Step(232): len = 640801, overlap = 0
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005677s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (275.2%)

PHY-3001 : Legalized: Len = 640801, Over = 0
PHY-3001 : End spreading;  0.093507s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (100.3%)

PHY-3001 : Final: Len = 640801, Over = 0
RUN-1003 : finish command "place -eco" in  6.248014s wall, 6.250000s user + 0.125000s system = 6.375000s CPU (102.0%)

RUN-1004 : used memory is 1133 MB, reserved memory is 1126 MB, peak memory is 1139 MB
RUN-1001 : Eco place succeeded
RUN-1002 : start command "route -eco"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8257 instances
RUN-1001 : 4065 mslices, 4075 lslices, 60 pads, 52 brams, 0 dsps
RUN-1001 : There are total 19692 nets
RUN-1001 : 13640 nets have 2 pins
RUN-1001 : 4625 nets have [3 - 5] pins
RUN-1001 : 866 nets have [6 - 10] pins
RUN-1001 : 416 nets have [11 - 20] pins
RUN-1001 : 135 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
PHY-1001 : 4065 mslices, 4075 lslices, 60 pads, 52 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19690 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 1139, reserve = 1131, peak = 1139.
PHY-1001 : Detailed router is running in eco mode.
PHY-1001 : Refresh detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1025 : Net FMC_addr[0]_dup_1 is open after eco import.
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1025 : Net AnyFog_dataX/UART_RX_COM3/clk is open after eco import.
PHY-1025 : Net DATA/FMC_AGRIC_ECEF_XStd[30] is open after eco import.
PHY-1025 : Net DATA/FMC_AGRIC_ECEF_XStd[25] is open after eco import.
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1025 : Net FMC/mux15_syn_19 is open after eco import.
PHY-1025 : Net FMC/dsp_outd[14] is open after eco import.
PHY-1025 : Net FMC/dsp_outd[9] is open after eco import.
PHY-1001 : eco open net = 7
PHY-1001 : Current memory(MB): used = 1147, reserve = 1140, peak = 1147.
PHY-1001 : End build detailed router design. 1.961360s wall, 1.953125s user + 0.000000s system = 1.953125s CPU (99.6%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.71886e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.874745s wall, 0.875000s user + 0.000000s system = 0.875000s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1150, reserve = 1143, peak = 1150.
PHY-1001 : End phase 1; 0.884578s wall, 0.875000s user + 0.000000s system = 0.875000s CPU (98.9%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 93% nets.
PHY-1001 : Routed 93% nets.
PHY-1001 : Routed 93% nets.
PHY-1001 : Routed 93% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.81742e+06, over cnt = 68(0%), over = 68, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 1150, reserve = 1143, peak = 1151.
PHY-1001 : End initial routed; 0.897905s wall, 0.906250s user + 0.000000s system = 0.906250s CPU (100.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18468(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.199   |   0.000   |   0   
RUN-1001 :   Hold   |   0.133   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.270131s wall, 3.265625s user + 0.000000s system = 3.265625s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 1161, reserve = 1154, peak = 1161.
PHY-1001 : End phase 2; 4.168150s wall, 4.171875s user + 0.000000s system = 4.171875s CPU (100.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.81742e+06, over cnt = 68(0%), over = 68, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.213331s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (102.5%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.81596e+06, over cnt = 41(0%), over = 41, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.221777s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (105.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.81546e+06, over cnt = 22(0%), over = 22, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.207432s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (97.9%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.81522e+06, over cnt = 4(0%), over = 4, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.191542s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (97.9%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.81522e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 4; 0.167052s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (112.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18468(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.199   |   0.000   |   0   
RUN-1001 :   Hold   |   0.133   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.252910s wall, 3.234375s user + 0.015625s system = 3.250000s CPU (99.9%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 384 feed throughs used by 312 nets
PHY-1001 : End commit to database; 2.321957s wall, 2.296875s user + 0.000000s system = 2.296875s CPU (98.9%)

PHY-1001 : Current memory(MB): used = 1172, reserve = 1165, peak = 1172.
PHY-1001 : End phase 3; 6.624272s wall, 6.593750s user + 0.031250s system = 6.625000s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1172, reserve = 1166, peak = 1172.
PHY-1001 : End export database. 0.069975s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (111.6%)

PHY-1001 : End detail ECO routing;  14.075038s wall, 14.031250s user + 0.031250s system = 14.062500s CPU (99.9%)

PHY-1001 : Routing violations:
PHY-8023 ERROR: Location: (x27y19_local4), nets: FMC/mux15_syn_19 DATA/done_div_dup_32
PHY-1001 : End of Routing Violations.
RUN-1003 : finish command "route -eco" in  15.175448s wall, 15.125000s user + 0.031250s system = 15.156250s CPU (99.9%)

RUN-1004 : used memory is 1089 MB, reserved memory is 1075 MB, peak memory is 1172 MB
RUN-1001 : Eco route succeeded
PHY-1001 : All routing violations have been resolved.
RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69248, tnet num: 19690, tinst num: 8255, tnode num: 94193, tedge num: 114044.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.698998s wall, 1.687500s user + 0.015625s system = 1.703125s CPU (100.2%)

RUN-1004 : used memory is 1093 MB, reserved memory is 1079 MB, peak memory is 1172 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  138.301673s wall, 172.812500s user + 0.875000s system = 173.687500s CPU (125.6%)

RUN-1004 : used memory is 1092 MB, reserved memory is 1081 MB, peak memory is 1172 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8920   out of  19600   45.51%
#reg                    12825   out of  19600   65.43%
#le                     15331
  #lut only              2506   out of  15331   16.35%
  #reg only              6411   out of  15331   41.82%
  #lut&reg               6414   out of  15331   41.84%
#dsp                        0   out of     29    0.00%
#bram                      52   out of     64   81.25%
  #bram9k                  52
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                    10
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6938
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          188
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         M3        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         P6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15331  |7475    |1445    |12868   |52      |0       |
|  AnyFog_dataX                      |AnyFog          |208    |84      |22      |172     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |53      |22      |50      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |207    |72      |22      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |53      |22      |52      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |208    |82      |22      |171     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |53      |22      |49      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |3348   |970     |34      |3271    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |720    |61      |5       |710     |0       |0       |
|    STADOP_com2                     |STADOP          |554    |144     |0       |549     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |69     |44      |14      |43      |0       |0       |
|    head_com2                       |uniheading      |256    |56      |5       |246     |0       |0       |
|    rmc_com2                        |Gprmc           |34     |29      |0       |32      |0       |0       |
|    uart_com2                       |Agrica          |1425   |346     |10      |1401    |0       |0       |
|  COM3                              |COM3_Control    |212    |130     |14      |183     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |61     |46      |14      |38      |0       |0       |
|    rmc_com3                        |Gprmc           |151    |84      |0       |145     |0       |0       |
|  DATA                              |Data_Processing |8660   |4380    |1059    |6956    |0       |0       |
|    DIV_Dtemp                       |Divider         |846    |302     |84      |715     |0       |0       |
|    DIV_Utemp                       |Divider         |582    |318     |84      |455     |0       |0       |
|    DIV_accX                        |Divider         |643    |297     |84      |504     |0       |0       |
|    DIV_accY                        |Divider         |669    |387     |108     |502     |0       |0       |
|    DIV_accZ                        |Divider         |675    |376     |132     |467     |0       |0       |
|    DIV_rateX                       |Divider         |637    |393     |132     |424     |0       |0       |
|    DIV_rateY                       |Divider         |571    |358     |132     |367     |0       |0       |
|    DIV_rateZ                       |Divider         |564    |355     |132     |357     |0       |0       |
|    genclk                          |genclk          |79     |45      |20      |46      |0       |0       |
|  FMC                               |FMC_Ctrl        |432    |376     |43      |342     |0       |0       |
|  IIC                               |I2C_master      |297    |232     |11      |258     |0       |0       |
|  IMU_CTRL                          |SCHA634         |883    |613     |61      |719     |0       |0       |
|    CtrlData                        |CtrlData        |466    |414     |47      |332     |0       |0       |
|      usms                          |Time_1ms        |28     |22      |5       |19      |0       |0       |
|    SPIM                            |SPI_SCHA634     |417    |199     |14      |387     |0       |0       |
|  POWER                             |POWER_EN        |99     |49      |38      |39      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |755    |483     |119     |519     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |755    |483     |119     |519     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |339    |202     |0       |322     |0       |0       |
|        reg_inst                    |register        |337    |200     |0       |320     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |416    |281     |119     |197     |0       |0       |
|        bus_inst                    |bus_top         |181    |119     |62      |67      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |27     |17      |10      |10      |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |52     |34      |18      |19      |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |100    |66      |34      |36      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |159    |125     |29      |101     |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13579  
    #2          2       3611   
    #3          3        737   
    #4          4        277   
    #5        5-10       952   
    #6        11-50      441   
    #7       51-100      24    
    #8       101-500      4    
    #9        >500        2    
  Average     2.16             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.042923s wall, 3.500000s user + 0.031250s system = 3.531250s CPU (172.9%)

RUN-1004 : used memory is 1093 MB, reserved memory is 1082 MB, peak memory is 1172 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 69248, tnet num: 19690, tinst num: 8255, tnode num: 94193, tedge num: 114044.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.794383s wall, 1.781250s user + 0.000000s system = 1.781250s CPU (99.3%)

RUN-1004 : used memory is 1095 MB, reserved memory is 1085 MB, peak memory is 1172 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19690 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.314610s wall, 1.312500s user + 0.015625s system = 1.328125s CPU (101.0%)

RUN-1004 : used memory is 1102 MB, reserved memory is 1091 MB, peak memory is 1172 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 28b752c1c0e7d7ba92ac5beed3c46754b7b56ac64969e4b0149cfdbc1d295011 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8255
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19692, pip num: 152601
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 384
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3267 valid insts, and 425037 bits set as '1'.
BIT-1004 : the usercode register value: 00000000000000001110111010000001
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  11.135395s wall, 104.531250s user + 0.375000s system = 104.906250s CPU (942.1%)

RUN-1004 : used memory is 1237 MB, reserved memory is 1224 MB, peak memory is 1352 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250613_142102.log"

============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Wed Aug 20 14:11:49 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.753208s wall, 1.734375s user + 4.000000s system = 5.734375s CPU (99.7%)

RUN-1004 : used memory is 80 MB, reserved memory is 40 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.857770s wall, 1.750000s user + 0.109375s system = 1.859375s CPU (100.1%)

RUN-1004 : used memory is 303 MB, reserved memory is 271 MB, peak memory is 306 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 3 view nodes, 40 trigger nets, 40 data nets.
KIT-1004 : Chipwatcher code = 1100001100101001
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=3,BUS_DIN_NUM=40,BUS_CTRL_NUM=92,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=114) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=114) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=3,BUS_DIN_NUM=40,BUS_CTRL_NUM=92,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=3,BUS_DIN_NUM=40,BUS_CTRL_NUM=92,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=3,BUS_DIN_NUM=40,BUS_CTRL_NUM=92,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=114)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=114)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=3,BUS_DIN_NUM=40,BUS_CTRL_NUM=92,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=3,BUS_DIN_NUM=40,BUS_CTRL_NUM=92,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22692/17 useful/useless nets, 19491/6 useful/useless insts
SYN-1016 : Merged 21 instances.
SYN-1032 : 22392/16 useful/useless nets, 19899/12 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 370 better
SYN-1014 : Optimize round 2
SYN-1032 : 22094/45 useful/useless nets, 19601/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.497031s wall, 2.375000s user + 0.125000s system = 2.500000s CPU (100.1%)

RUN-1004 : used memory is 327 MB, reserved memory is 294 MB, peak memory is 329 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22142/297 useful/useless nets, 19684/45 useful/useless insts
SYN-1016 : Merged 45 instances.
SYN-2571 : Optimize after map_dsp, round 1, 387 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 40 instances.
SYN-2501 : Optimize round 1, 82 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 22578/5 useful/useless nets, 20120/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82351, tnet num: 22578, tinst num: 20119, tnode num: 115359, tedge num: 128794.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.263910s wall, 1.218750s user + 0.046875s system = 1.265625s CPU (100.1%)

RUN-1004 : used memory is 468 MB, reserved memory is 436 MB, peak memory is 468 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22578 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 222 (3.41), #lev = 7 (1.67)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 222 (3.41), #lev = 7 (1.67)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 484 instances into 222 LUTs, name keeping = 77%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 379 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 128 adder to BLE ...
SYN-4008 : Packed 128 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.815443s wall, 4.718750s user + 0.093750s system = 4.812500s CPU (99.9%)

RUN-1004 : used memory is 366 MB, reserved memory is 346 MB, peak memory is 576 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.655593s wall, 7.421875s user + 0.234375s system = 7.656250s CPU (100.0%)

RUN-1004 : used memory is 367 MB, reserved memory is 346 MB, peak memory is 576 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (249 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19398 instances
RUN-0007 : 5614 luts, 12180 seqs, 983 mslices, 519 lslices, 59 pads, 38 brams, 0 dsps
RUN-1001 : There are total 21864 nets
RUN-1001 : 16410 nets have 2 pins
RUN-1001 : 4261 nets have [3 - 5] pins
RUN-1001 : 815 nets have [6 - 10] pins
RUN-1001 : 252 nets have [11 - 20] pins
RUN-1001 : 104 nets have [21 - 99] pins
RUN-1001 : 22 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4792     
RUN-1001 :   No   |  No   |  Yes  |     698     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     421     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19396 instances, 5614 luts, 12180 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80832, tnet num: 21862, tinst num: 19396, tnode num: 113603, tedge num: 127220.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.249663s wall, 1.218750s user + 0.031250s system = 1.250000s CPU (100.0%)

RUN-1004 : used memory is 528 MB, reserved memory is 500 MB, peak memory is 576 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21862 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.307186s wall, 2.265625s user + 0.046875s system = 2.312500s CPU (100.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.63254e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19396.
PHY-3001 : Level 1 #clusters 2107.
PHY-3001 : End clustering;  0.156383s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (129.9%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 904589, overlap = 634.719
PHY-3002 : Step(2): len = 828336, overlap = 692.594
PHY-3002 : Step(3): len = 540633, overlap = 876.5
PHY-3002 : Step(4): len = 474291, overlap = 939.562
PHY-3002 : Step(5): len = 373730, overlap = 1026.81
PHY-3002 : Step(6): len = 327953, overlap = 1118.09
PHY-3002 : Step(7): len = 273011, overlap = 1186.81
PHY-3002 : Step(8): len = 239422, overlap = 1240.53
PHY-3002 : Step(9): len = 212201, overlap = 1290.88
PHY-3002 : Step(10): len = 192587, overlap = 1335.47
PHY-3002 : Step(11): len = 173179, overlap = 1378
PHY-3002 : Step(12): len = 159534, overlap = 1398.03
PHY-3002 : Step(13): len = 147931, overlap = 1406.78
PHY-3002 : Step(14): len = 135784, overlap = 1432.28
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.29259e-06
PHY-3002 : Step(15): len = 143093, overlap = 1408.91
PHY-3002 : Step(16): len = 195670, overlap = 1329.34
PHY-3002 : Step(17): len = 207538, overlap = 1234.12
PHY-3002 : Step(18): len = 210730, overlap = 1176.78
PHY-3002 : Step(19): len = 202946, overlap = 1156.66
PHY-3002 : Step(20): len = 197095, overlap = 1139.78
PHY-3002 : Step(21): len = 189496, overlap = 1131.25
PHY-3002 : Step(22): len = 186352, overlap = 1132.66
PHY-3002 : Step(23): len = 181879, overlap = 1138.22
PHY-3002 : Step(24): len = 179838, overlap = 1130.5
PHY-3002 : Step(25): len = 177483, overlap = 1119.31
PHY-3002 : Step(26): len = 176391, overlap = 1104.62
PHY-3002 : Step(27): len = 175548, overlap = 1085.97
PHY-3002 : Step(28): len = 175397, overlap = 1076.19
PHY-3002 : Step(29): len = 174163, overlap = 1069.25
PHY-3002 : Step(30): len = 173345, overlap = 1059.41
PHY-3002 : Step(31): len = 173362, overlap = 1046.72
PHY-3002 : Step(32): len = 173318, overlap = 1046.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.58518e-06
PHY-3002 : Step(33): len = 181055, overlap = 1026.94
PHY-3002 : Step(34): len = 196796, overlap = 967.281
PHY-3002 : Step(35): len = 201096, overlap = 953.188
PHY-3002 : Step(36): len = 202906, overlap = 944.125
PHY-3002 : Step(37): len = 202244, overlap = 940.344
PHY-3002 : Step(38): len = 202377, overlap = 935.156
PHY-3002 : Step(39): len = 200521, overlap = 928.375
PHY-3002 : Step(40): len = 200512, overlap = 910.375
PHY-3002 : Step(41): len = 199712, overlap = 897.594
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 5.17036e-06
PHY-3002 : Step(42): len = 212046, overlap = 856.25
PHY-3002 : Step(43): len = 226998, overlap = 807.344
PHY-3002 : Step(44): len = 231520, overlap = 769.438
PHY-3002 : Step(45): len = 234008, overlap = 751.469
PHY-3002 : Step(46): len = 233852, overlap = 728.969
PHY-3002 : Step(47): len = 232434, overlap = 733.938
PHY-3002 : Step(48): len = 231181, overlap = 741.281
PHY-3002 : Step(49): len = 229926, overlap = 736
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 1.03407e-05
PHY-3002 : Step(50): len = 243409, overlap = 713.25
PHY-3002 : Step(51): len = 258389, overlap = 666.75
PHY-3002 : Step(52): len = 262466, overlap = 642.156
PHY-3002 : Step(53): len = 264028, overlap = 629.312
PHY-3002 : Step(54): len = 262369, overlap = 624.844
PHY-3002 : Step(55): len = 260706, overlap = 612.438
PHY-3002 : Step(56): len = 257825, overlap = 621.938
PHY-3002 : Step(57): len = 256672, overlap = 603.406
PHY-3002 : Step(58): len = 256342, overlap = 613.938
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 2.06814e-05
PHY-3002 : Step(59): len = 269489, overlap = 549.906
PHY-3002 : Step(60): len = 283620, overlap = 491.812
PHY-3002 : Step(61): len = 287632, overlap = 500.969
PHY-3002 : Step(62): len = 290678, overlap = 470.281
PHY-3002 : Step(63): len = 290442, overlap = 457.25
PHY-3002 : Step(64): len = 288864, overlap = 464.344
PHY-3002 : Step(65): len = 286313, overlap = 450.562
PHY-3002 : Step(66): len = 284133, overlap = 427.25
PHY-3002 : Step(67): len = 283006, overlap = 414.531
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 4.13629e-05
PHY-3002 : Step(68): len = 295138, overlap = 403.219
PHY-3002 : Step(69): len = 303423, overlap = 382.875
PHY-3002 : Step(70): len = 304474, overlap = 364.031
PHY-3002 : Step(71): len = 306054, overlap = 352.875
PHY-3002 : Step(72): len = 304940, overlap = 342.625
PHY-3002 : Step(73): len = 304142, overlap = 350.531
PHY-3002 : Step(74): len = 301822, overlap = 347.219
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 8.27257e-05
PHY-3002 : Step(75): len = 308072, overlap = 316.188
PHY-3002 : Step(76): len = 317104, overlap = 290.344
PHY-3002 : Step(77): len = 320518, overlap = 269.938
PHY-3002 : Step(78): len = 322014, overlap = 272.75
PHY-3002 : Step(79): len = 322080, overlap = 263.156
PHY-3002 : Step(80): len = 321613, overlap = 255.062
PHY-3002 : Step(81): len = 320479, overlap = 263.719
PHY-3002 : Step(82): len = 319797, overlap = 273.406
PHY-3002 : Step(83): len = 319555, overlap = 299.125
PHY-3002 : Step(84): len = 318193, overlap = 301.312
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000165451
PHY-3002 : Step(85): len = 322230, overlap = 295.906
PHY-3002 : Step(86): len = 328475, overlap = 295.188
PHY-3002 : Step(87): len = 330501, overlap = 284.688
PHY-3002 : Step(88): len = 330399, overlap = 292
PHY-3002 : Step(89): len = 329756, overlap = 298.969
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000290077
PHY-3002 : Step(90): len = 331831, overlap = 289.594
PHY-3002 : Step(91): len = 335416, overlap = 274.344
PHY-3002 : Step(92): len = 336296, overlap = 293.469
PHY-3002 : Step(93): len = 336980, overlap = 281.219
PHY-3002 : Step(94): len = 337118, overlap = 285.281
PHY-3002 : Step(95): len = 337197, overlap = 277.406
PHY-3002 : Step(96): len = 337467, overlap = 266.812
PHY-3002 : Step(97): len = 337606, overlap = 260.625
PHY-3002 : Step(98): len = 338155, overlap = 256.188
PHY-3002 : Step(99): len = 337307, overlap = 262.562
PHY-3002 : Step(100): len = 337245, overlap = 264.031
PHY-3002 : Step(101): len = 336166, overlap = 263.562
PHY-3002 : Step(102): len = 336396, overlap = 266.344
PHY-3002 : Step(103): len = 335932, overlap = 264.438
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(104): len = 337151, overlap = 262.594
PHY-3002 : Step(105): len = 338921, overlap = 257.312
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.014040s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (111.3%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21864.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 446640, over cnt = 1163(3%), over = 5223, worst = 38
PHY-1001 : End global iterations;  0.854661s wall, 1.171875s user + 0.062500s system = 1.234375s CPU (144.4%)

PHY-1001 : Congestion index: top1 = 75.52, top5 = 53.15, top10 = 43.02, top15 = 37.31.
PHY-3001 : End congestion estimation;  1.107072s wall, 1.406250s user + 0.078125s system = 1.484375s CPU (134.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21862 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.997725s wall, 0.984375s user + 0.000000s system = 0.984375s CPU (98.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00011059
PHY-3002 : Step(106): len = 386111, overlap = 187.094
PHY-3002 : Step(107): len = 400493, overlap = 156.188
PHY-3002 : Step(108): len = 401309, overlap = 157.344
PHY-3002 : Step(109): len = 399010, overlap = 150.844
PHY-3002 : Step(110): len = 403661, overlap = 141.781
PHY-3002 : Step(111): len = 413452, overlap = 131.844
PHY-3002 : Step(112): len = 421328, overlap = 117.656
PHY-3002 : Step(113): len = 424524, overlap = 107.719
PHY-3002 : Step(114): len = 429169, overlap = 102.25
PHY-3002 : Step(115): len = 435594, overlap = 96
PHY-3002 : Step(116): len = 436680, overlap = 94.5625
PHY-3002 : Step(117): len = 437459, overlap = 90.5938
PHY-3002 : Step(118): len = 441831, overlap = 91.25
PHY-3002 : Step(119): len = 443248, overlap = 94.0625
PHY-3002 : Step(120): len = 444283, overlap = 92.75
PHY-3002 : Step(121): len = 448348, overlap = 87.5312
PHY-3002 : Step(122): len = 451087, overlap = 86.7188
PHY-3002 : Step(123): len = 451913, overlap = 83.8125
PHY-3002 : Step(124): len = 454115, overlap = 86.8438
PHY-3002 : Step(125): len = 456140, overlap = 85.0938
PHY-3002 : Step(126): len = 458941, overlap = 81.5938
PHY-3002 : Step(127): len = 458855, overlap = 80.25
PHY-3002 : Step(128): len = 458990, overlap = 81.5938
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000221179
PHY-3002 : Step(129): len = 458910, overlap = 79.4062
PHY-3002 : Step(130): len = 461569, overlap = 80.9062
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(131): len = 465186, overlap = 80.625
PHY-3002 : Step(132): len = 471728, overlap = 82.875
PHY-3002 : Step(133): len = 476781, overlap = 84.5312
PHY-3002 : Step(134): len = 481073, overlap = 86.875
PHY-3002 : Step(135): len = 485784, overlap = 85.6562
PHY-3002 : Step(136): len = 490083, overlap = 85.375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 64/21864.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 562976, over cnt = 2298(6%), over = 10415, worst = 36
PHY-1001 : End global iterations;  1.146692s wall, 1.796875s user + 0.046875s system = 1.843750s CPU (160.8%)

PHY-1001 : Congestion index: top1 = 81.29, top5 = 62.14, top10 = 52.57, top15 = 46.97.
PHY-3001 : End congestion estimation;  1.457472s wall, 2.093750s user + 0.046875s system = 2.140625s CPU (146.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21862 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.033659s wall, 1.015625s user + 0.015625s system = 1.031250s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000110666
PHY-3002 : Step(137): len = 495649, overlap = 312.594
PHY-3002 : Step(138): len = 499258, overlap = 264.969
PHY-3002 : Step(139): len = 493674, overlap = 242.656
PHY-3002 : Step(140): len = 487973, overlap = 237.031
PHY-3002 : Step(141): len = 484216, overlap = 212.469
PHY-3002 : Step(142): len = 479883, overlap = 214.156
PHY-3002 : Step(143): len = 476248, overlap = 210.25
PHY-3002 : Step(144): len = 475393, overlap = 208
PHY-3002 : Step(145): len = 472304, overlap = 210
PHY-3002 : Step(146): len = 469038, overlap = 215.031
PHY-3002 : Step(147): len = 467684, overlap = 217.938
PHY-3002 : Step(148): len = 466298, overlap = 209.406
PHY-3002 : Step(149): len = 465741, overlap = 205.219
PHY-3002 : Step(150): len = 463293, overlap = 196.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000221332
PHY-3002 : Step(151): len = 464280, overlap = 195.719
PHY-3002 : Step(152): len = 465475, overlap = 195.906
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000418174
PHY-3002 : Step(153): len = 467397, overlap = 184.625
PHY-3002 : Step(154): len = 475909, overlap = 170.656
PHY-3002 : Step(155): len = 480932, overlap = 159.312
PHY-3002 : Step(156): len = 482929, overlap = 155.75
PHY-3002 : Step(157): len = 483792, overlap = 157.312
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000833528
PHY-3002 : Step(158): len = 485227, overlap = 153.812
PHY-3002 : Step(159): len = 488526, overlap = 142.75
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80832, tnet num: 21862, tinst num: 19396, tnode num: 113603, tedge num: 127220.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.563147s wall, 1.546875s user + 0.015625s system = 1.562500s CPU (100.0%)

RUN-1004 : used memory is 567 MB, reserved memory is 541 MB, peak memory is 700 MB
OPT-1001 : Total overflow 495.97 peak overflow 4.31
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 497/21864.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 569096, over cnt = 2598(7%), over = 8775, worst = 25
PHY-1001 : End global iterations;  1.262509s wall, 2.062500s user + 0.000000s system = 2.062500s CPU (163.4%)

PHY-1001 : Congestion index: top1 = 61.40, top5 = 49.64, top10 = 44.26, top15 = 41.04.
PHY-1001 : End incremental global routing;  1.538106s wall, 2.343750s user + 0.000000s system = 2.343750s CPU (152.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21862 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.114385s wall, 1.093750s user + 0.031250s system = 1.125000s CPU (101.0%)

OPT-1001 : 17 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19317 has valid locations, 231 needs to be replaced
PHY-3001 : design contains 19610 instances, 5702 luts, 12306 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 504167
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17226/22078.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 580096, over cnt = 2617(7%), over = 8841, worst = 26
PHY-1001 : End global iterations;  0.211784s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (132.8%)

PHY-1001 : Congestion index: top1 = 61.21, top5 = 49.84, top10 = 44.51, top15 = 41.27.
PHY-3001 : End congestion estimation;  0.475661s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (115.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81533, tnet num: 22076, tinst num: 19610, tnode num: 114603, tedge num: 128194.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.571593s wall, 1.578125s user + 0.000000s system = 1.578125s CPU (100.4%)

RUN-1004 : used memory is 611 MB, reserved memory is 591 MB, peak memory is 703 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22076 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.790155s wall, 2.796875s user + 0.000000s system = 2.796875s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(160): len = 504161, overlap = 3.75
PHY-3002 : Step(161): len = 505351, overlap = 3.8125
PHY-3002 : Step(162): len = 505652, overlap = 3.75
PHY-3002 : Step(163): len = 506048, overlap = 3.8125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17269/22078.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 580904, over cnt = 2635(7%), over = 8905, worst = 26
PHY-1001 : End global iterations;  0.214279s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (94.8%)

PHY-1001 : Congestion index: top1 = 61.68, top5 = 50.03, top10 = 44.83, top15 = 41.54.
PHY-3001 : End congestion estimation;  0.479287s wall, 0.468750s user + 0.000000s system = 0.468750s CPU (97.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22076 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.241126s wall, 1.250000s user + 0.000000s system = 1.250000s CPU (100.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000600993
PHY-3002 : Step(164): len = 505935, overlap = 145.844
PHY-3002 : Step(165): len = 506142, overlap = 145.969
PHY-3002 : Step(166): len = 506644, overlap = 145.625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00120199
PHY-3002 : Step(167): len = 506781, overlap = 146.125
PHY-3002 : Step(168): len = 507011, overlap = 145.469
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00240397
PHY-3002 : Step(169): len = 507133, overlap = 145.375
PHY-3002 : Step(170): len = 507300, overlap = 144.875
PHY-3001 : Final: Len = 507300, Over = 144.875
PHY-3001 : End incremental placement;  6.050868s wall, 7.031250s user + 0.234375s system = 7.265625s CPU (120.1%)

OPT-1001 : Total overflow 501.16 peak overflow 4.31
OPT-1001 : End high-fanout net optimization;  9.257664s wall, 11.171875s user + 0.265625s system = 11.437500s CPU (123.5%)

OPT-1001 : Current memory(MB): used = 706, reserve = 685, peak = 722.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17249/22078.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 582768, over cnt = 2579(7%), over = 8296, worst = 25
PHY-1002 : len = 623696, over cnt = 1796(5%), over = 4399, worst = 25
PHY-1002 : len = 667248, over cnt = 634(1%), over = 1283, worst = 17
PHY-1002 : len = 683096, over cnt = 109(0%), over = 165, worst = 5
PHY-1002 : len = 685944, over cnt = 3(0%), over = 4, worst = 2
PHY-1001 : End global iterations;  1.465298s wall, 2.109375s user + 0.015625s system = 2.125000s CPU (145.0%)

PHY-1001 : Congestion index: top1 = 51.92, top5 = 45.15, top10 = 41.79, top15 = 39.65.
OPT-1001 : End congestion update;  1.740581s wall, 2.406250s user + 0.015625s system = 2.421875s CPU (139.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22076 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.919403s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (100.3%)

OPT-0007 : Start: WNS 3837 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.666781s wall, 3.328125s user + 0.015625s system = 3.343750s CPU (125.4%)

OPT-1001 : Current memory(MB): used = 705, reserve = 684, peak = 722.
OPT-1001 : End physical optimization;  13.821983s wall, 16.531250s user + 0.312500s system = 16.843750s CPU (121.9%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5702 LUT to BLE ...
SYN-4008 : Packed 5702 LUT and 2731 SEQ to BLE.
SYN-4003 : Packing 9575 remaining SEQ's ...
SYN-4005 : Packed 3370 SEQ with LUT/SLICE
SYN-4006 : 127 single LUT's are left
SYN-4006 : 6205 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11907/13775 primitive instances ...
PHY-3001 : End packing;  2.834257s wall, 2.828125s user + 0.015625s system = 2.843750s CPU (100.3%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8127 instances
RUN-1001 : 4013 mslices, 4012 lslices, 59 pads, 38 brams, 0 dsps
RUN-1001 : There are total 19399 nets
RUN-1001 : 13594 nets have 2 pins
RUN-1001 : 4395 nets have [3 - 5] pins
RUN-1001 : 887 nets have [6 - 10] pins
RUN-1001 : 383 nets have [11 - 20] pins
RUN-1001 : 131 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8125 instances, 8025 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 524146, Over = 375.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7707/19399.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 654256, over cnt = 1645(4%), over = 2545, worst = 7
PHY-1002 : len = 659432, over cnt = 1132(3%), over = 1572, worst = 7
PHY-1002 : len = 668816, over cnt = 559(1%), over = 756, worst = 5
PHY-1002 : len = 678880, over cnt = 117(0%), over = 144, worst = 5
PHY-1002 : len = 680696, over cnt = 49(0%), over = 62, worst = 4
PHY-1001 : End global iterations;  1.239485s wall, 2.046875s user + 0.000000s system = 2.046875s CPU (165.1%)

PHY-1001 : Congestion index: top1 = 52.72, top5 = 44.99, top10 = 41.47, top15 = 39.20.
PHY-3001 : End congestion estimation;  1.589124s wall, 2.421875s user + 0.000000s system = 2.421875s CPU (152.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67719, tnet num: 19397, tinst num: 8125, tnode num: 91793, tedge num: 111711.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.758495s wall, 1.750000s user + 0.000000s system = 1.750000s CPU (99.5%)

RUN-1004 : used memory is 601 MB, reserved memory is 587 MB, peak memory is 722 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19397 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.741583s wall, 2.718750s user + 0.015625s system = 2.734375s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.90427e-05
PHY-3002 : Step(171): len = 526564, overlap = 363
PHY-3002 : Step(172): len = 525739, overlap = 376
PHY-3002 : Step(173): len = 525074, overlap = 378.75
PHY-3002 : Step(174): len = 524633, overlap = 389
PHY-3002 : Step(175): len = 523635, overlap = 393
PHY-3002 : Step(176): len = 522285, overlap = 388.5
PHY-3002 : Step(177): len = 521680, overlap = 380.25
PHY-3002 : Step(178): len = 519785, overlap = 388.5
PHY-3002 : Step(179): len = 517956, overlap = 397
PHY-3002 : Step(180): len = 516129, overlap = 401.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.80854e-05
PHY-3002 : Step(181): len = 520330, overlap = 388.75
PHY-3002 : Step(182): len = 524492, overlap = 376.75
PHY-3002 : Step(183): len = 525258, overlap = 374.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000196171
PHY-3002 : Step(184): len = 531121, overlap = 359.5
PHY-3002 : Step(185): len = 543148, overlap = 330.75
PHY-3002 : Step(186): len = 543159, overlap = 322.75
PHY-3002 : Step(187): len = 541442, overlap = 319
PHY-3002 : Step(188): len = 540922, overlap = 315.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.760412s wall, 0.718750s user + 0.906250s system = 1.625000s CPU (213.7%)

PHY-3001 : Trial Legalized: Len = 644650
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 668/19399.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 731352, over cnt = 2411(6%), over = 3873, worst = 8
PHY-1002 : len = 746960, over cnt = 1388(3%), over = 1915, worst = 8
PHY-1002 : len = 765152, over cnt = 415(1%), over = 577, worst = 8
PHY-1002 : len = 773208, over cnt = 105(0%), over = 149, worst = 8
PHY-1002 : len = 776104, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.024060s wall, 3.375000s user + 0.125000s system = 3.500000s CPU (172.9%)

PHY-1001 : Congestion index: top1 = 50.99, top5 = 45.43, top10 = 42.60, top15 = 40.76.
PHY-3001 : End congestion estimation;  2.412254s wall, 3.750000s user + 0.125000s system = 3.875000s CPU (160.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19397 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.953128s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000182383
PHY-3002 : Step(189): len = 603746, overlap = 80
PHY-3002 : Step(190): len = 586031, overlap = 127.25
PHY-3002 : Step(191): len = 575543, overlap = 168
PHY-3002 : Step(192): len = 569582, overlap = 197.5
PHY-3002 : Step(193): len = 566129, overlap = 222.25
PHY-3002 : Step(194): len = 564066, overlap = 237.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000364765
PHY-3002 : Step(195): len = 568130, overlap = 234
PHY-3002 : Step(196): len = 572448, overlap = 230.25
PHY-3002 : Step(197): len = 572549, overlap = 227.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00072953
PHY-3002 : Step(198): len = 575974, overlap = 226
PHY-3002 : Step(199): len = 582564, overlap = 224
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.036839s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (84.8%)

PHY-3001 : Legalized: Len = 617088, Over = 0
PHY-3001 : Spreading special nets. 24 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.084845s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (92.1%)

PHY-3001 : 37 instances has been re-located, deltaX = 15, deltaY = 17, maxDist = 2.
PHY-3001 : Final: Len = 617556, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67719, tnet num: 19397, tinst num: 8125, tnode num: 91793, tedge num: 111711.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.993680s wall, 1.953125s user + 0.031250s system = 1.984375s CPU (99.5%)

RUN-1004 : used memory is 617 MB, reserved memory is 616 MB, peak memory is 722 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 4784/19399.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 718648, over cnt = 2160(6%), over = 3336, worst = 7
PHY-1002 : len = 729032, over cnt = 1351(3%), over = 1856, worst = 6
PHY-1002 : len = 745448, over cnt = 425(1%), over = 553, worst = 5
PHY-1002 : len = 751552, over cnt = 170(0%), over = 210, worst = 5
PHY-1002 : len = 755568, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.841565s wall, 2.906250s user + 0.062500s system = 2.968750s CPU (161.2%)

PHY-1001 : Congestion index: top1 = 49.55, top5 = 44.53, top10 = 41.55, top15 = 39.67.
PHY-1001 : End incremental global routing;  2.173942s wall, 3.250000s user + 0.062500s system = 3.312500s CPU (152.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19397 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.986058s wall, 0.968750s user + 0.015625s system = 0.984375s CPU (99.8%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8061 has valid locations, 11 needs to be replaced
PHY-3001 : design contains 8134 instances, 8034 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 619057
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17402/19406.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 756816, over cnt = 27(0%), over = 29, worst = 2
PHY-1002 : len = 756792, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 756848, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 756912, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.542161s wall, 0.609375s user + 0.015625s system = 0.625000s CPU (115.3%)

PHY-1001 : Congestion index: top1 = 49.55, top5 = 44.61, top10 = 41.64, top15 = 39.74.
PHY-3001 : End congestion estimation;  0.859776s wall, 0.921875s user + 0.015625s system = 0.937500s CPU (109.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67786, tnet num: 19404, tinst num: 8134, tnode num: 91874, tedge num: 111794.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.053220s wall, 2.046875s user + 0.000000s system = 2.046875s CPU (99.7%)

RUN-1004 : used memory is 644 MB, reserved memory is 632 MB, peak memory is 722 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19404 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.069981s wall, 3.031250s user + 0.031250s system = 3.062500s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(200): len = 619057, overlap = 0
PHY-3002 : Step(201): len = 619057, overlap = 0
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17411/19406.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 756912, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.126999s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (98.4%)

PHY-1001 : Congestion index: top1 = 49.55, top5 = 44.61, top10 = 41.64, top15 = 39.74.
PHY-3001 : End congestion estimation;  0.449338s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (100.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19404 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.968509s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00261972
PHY-3002 : Step(202): len = 618954, overlap = 0.75
PHY-3002 : Step(203): len = 618938, overlap = 0.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006958s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 618947, Over = 0
PHY-3001 : End spreading;  0.078008s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (100.2%)

PHY-3001 : Final: Len = 618947, Over = 0
PHY-3001 : End incremental placement;  6.006404s wall, 6.000000s user + 0.125000s system = 6.125000s CPU (102.0%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  9.686023s wall, 10.875000s user + 0.218750s system = 11.093750s CPU (114.5%)

OPT-1001 : Current memory(MB): used = 716, reserve = 701, peak = 723.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17400/19406.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 756800, over cnt = 27(0%), over = 30, worst = 2
PHY-1002 : len = 756848, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 756904, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 756968, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.559993s wall, 0.578125s user + 0.000000s system = 0.578125s CPU (103.2%)

PHY-1001 : Congestion index: top1 = 49.74, top5 = 44.57, top10 = 41.63, top15 = 39.73.
OPT-1001 : End congestion update;  0.878491s wall, 0.906250s user + 0.000000s system = 0.906250s CPU (103.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19404 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.814953s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (99.7%)

OPT-0007 : Start: WNS 4310 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.698813s wall, 1.734375s user + 0.000000s system = 1.734375s CPU (102.1%)

OPT-1001 : Current memory(MB): used = 716, reserve = 701, peak = 723.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19404 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.807426s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (98.7%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17411/19406.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 756968, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.124683s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (100.3%)

PHY-1001 : Congestion index: top1 = 49.74, top5 = 44.57, top10 = 41.63, top15 = 39.73.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19404 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.801328s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.4%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4310 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 49.379310
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4310ps with logic level 8 
RUN-1001 :       #2 path slack 4385ps with logic level 8 
OPT-1001 : End physical optimization;  15.704870s wall, 16.859375s user + 0.296875s system = 17.156250s CPU (109.2%)

RUN-1003 : finish command "place" in  67.639363s wall, 126.500000s user + 7.000000s system = 133.500000s CPU (197.4%)

RUN-1004 : used memory is 598 MB, reserved memory is 589 MB, peak memory is 723 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.703006s wall, 2.921875s user + 0.015625s system = 2.937500s CPU (172.5%)

RUN-1004 : used memory is 599 MB, reserved memory is 590 MB, peak memory is 723 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8136 instances
RUN-1001 : 4016 mslices, 4018 lslices, 59 pads, 38 brams, 0 dsps
RUN-1001 : There are total 19406 nets
RUN-1001 : 13589 nets have 2 pins
RUN-1001 : 4399 nets have [3 - 5] pins
RUN-1001 : 889 nets have [6 - 10] pins
RUN-1001 : 389 nets have [11 - 20] pins
RUN-1001 : 131 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67786, tnet num: 19404, tinst num: 8134, tnode num: 91874, tedge num: 111794.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.745153s wall, 1.718750s user + 0.015625s system = 1.734375s CPU (99.4%)

RUN-1004 : used memory is 596 MB, reserved memory is 588 MB, peak memory is 723 MB
PHY-1001 : 4016 mslices, 4018 lslices, 59 pads, 38 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19404 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 696736, over cnt = 2341(6%), over = 3805, worst = 8
PHY-1002 : len = 712728, over cnt = 1427(4%), over = 1981, worst = 6
PHY-1002 : len = 731264, over cnt = 483(1%), over = 619, worst = 5
PHY-1002 : len = 741184, over cnt = 22(0%), over = 30, worst = 4
PHY-1002 : len = 741872, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.890286s wall, 3.078125s user + 0.078125s system = 3.156250s CPU (167.0%)

PHY-1001 : Congestion index: top1 = 48.75, top5 = 43.88, top10 = 40.98, top15 = 39.11.
PHY-1001 : End global routing;  2.249433s wall, 3.437500s user + 0.078125s system = 3.515625s CPU (156.3%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 701, reserve = 691, peak = 723.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 967, reserve = 955, peak = 967.
PHY-1001 : End build detailed router design. 4.871027s wall, 4.812500s user + 0.031250s system = 4.843750s CPU (99.4%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 191824, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.950513s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (98.6%)

PHY-1001 : Current memory(MB): used = 1004, reserve = 992, peak = 1004.
PHY-1001 : End phase 1; 0.958627s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (99.4%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.71635e+06, over cnt = 1338(0%), over = 1340, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1021, reserve = 1009, peak = 1021.
PHY-1001 : End initial routed; 17.467557s wall, 44.312500s user + 0.375000s system = 44.687500s CPU (255.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18134(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.197   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.519085s wall, 3.515625s user + 0.000000s system = 3.515625s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 1034, reserve = 1023, peak = 1034.
PHY-1001 : End phase 2; 20.986811s wall, 47.828125s user + 0.375000s system = 48.203125s CPU (229.7%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.71635e+06, over cnt = 1338(0%), over = 1340, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.257252s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (103.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.70535e+06, over cnt = 543(0%), over = 543, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.880867s wall, 1.468750s user + 0.000000s system = 1.468750s CPU (166.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.7051e+06, over cnt = 99(0%), over = 99, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.472217s wall, 0.656250s user + 0.000000s system = 0.656250s CPU (139.0%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.7058e+06, over cnt = 11(0%), over = 11, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.274417s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (102.5%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.70601e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 4; 0.182445s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (102.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18134(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.729   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.552984s wall, 3.546875s user + 0.000000s system = 3.546875s CPU (99.8%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 342 feed throughs used by 293 nets
PHY-1001 : End commit to database; 2.259021s wall, 2.234375s user + 0.031250s system = 2.265625s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 1120, reserve = 1111, peak = 1120.
PHY-1001 : End phase 3; 8.369271s wall, 9.140625s user + 0.031250s system = 9.171875s CPU (109.6%)

PHY-1003 : Routed, final wirelength = 1.70601e+06
PHY-1001 : Current memory(MB): used = 1124, reserve = 1115, peak = 1124.
PHY-1001 : End export database. 0.063094s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.1%)

PHY-1001 : End detail routing;  35.700860s wall, 63.250000s user + 0.437500s system = 63.687500s CPU (178.4%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67786, tnet num: 19404, tinst num: 8134, tnode num: 91874, tedge num: 111794.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.730181s wall, 1.718750s user + 0.000000s system = 1.718750s CPU (99.3%)

RUN-1004 : used memory is 1058 MB, reserved memory is 1055 MB, peak memory is 1124 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  44.198750s wall, 72.890625s user + 0.546875s system = 73.437500s CPU (166.2%)

RUN-1004 : used memory is 1057 MB, reserved memory is 1055 MB, peak memory is 1124 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8872   out of  19600   45.27%
#reg                    12402   out of  19600   63.28%
#le                     15022
  #lut only              2620   out of  15022   17.44%
  #reg only              6150   out of  15022   40.94%
  #lut&reg               6252   out of  15022   41.62%
#dsp                        0   out of     29    0.00%
#bram                      38   out of     64   59.38%
  #bram9k                  36
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       59   out of    188   31.38%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6818
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          143
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         F2        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         D1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         B3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         C1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         A3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         A2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         B2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         B1        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F15        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDZ        INPUT        K15        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         P2        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         N4        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         M5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         N6        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         N5        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         P5        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M3        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         M4        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         H2        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         M1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         L1        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         N1        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         N3        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         M2        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         T4        LVCMOS33           8            NONE           OREG       
    Fog_ENZ        OUTPUT         T7        LVCMOS33           8            NONE           OREG       
    Fog_INT        OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT         C3        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT        B15        LVCMOS33           8            N/A            OREG       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        R15        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         K2        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT         F6        LVCMOS25           8            N/A            NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15022  |7370    |1502    |12446   |38      |0       |
|  AnyFog_dataX                      |AnyFog          |208    |85      |22      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |56      |22      |51      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |206    |79      |22      |171     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |84     |54      |22      |49      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |205    |107     |22      |168     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |88     |66      |22      |51      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2914   |657     |39      |2839    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |60     |33      |5       |51      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |213    |74      |5       |203     |0       |0       |
|    STADOP_com2                     |STADOP          |547    |38      |0       |544     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |43      |14      |43      |0       |0       |
|    head_com2                       |uniheading      |254    |86      |5       |244     |0       |0       |
|    rmc_com2                        |Gprmc           |40     |38      |0       |33      |0       |0       |
|    uart_com2                       |Agrica          |1415   |307     |10      |1401    |0       |0       |
|  COM3                              |COM3_Control    |279    |142     |19      |236     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |60     |37      |5       |52      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |61     |38      |14      |39      |0       |0       |
|    rmc_com3                        |Gprmc           |158    |67      |0       |145     |0       |0       |
|  DATA                              |Data_Processing |8861   |4557    |1122    |7050    |0       |0       |
|    DIV_Dtemp                       |Divider         |809    |311     |84      |677     |0       |0       |
|    DIV_Utemp                       |Divider         |565    |270     |84      |437     |0       |0       |
|    DIV_accX                        |Divider         |584    |333     |84      |463     |0       |0       |
|    DIV_accY                        |Divider         |667    |345     |102     |507     |0       |0       |
|    DIV_accZ                        |Divider         |678    |358     |132     |471     |0       |0       |
|    DIV_rateX                       |Divider         |678    |399     |132     |472     |0       |0       |
|    DIV_rateY                       |Divider         |582    |376     |132     |374     |0       |0       |
|    DIV_rateZ                       |Divider         |597    |374     |132     |391     |0       |0       |
|    genclk                          |genclk          |260    |165     |89      |100     |0       |0       |
|  FMC                               |FMC_Ctrl        |445    |392     |43      |344     |0       |0       |
|  IIC                               |I2C_master      |303    |255     |11      |261     |0       |0       |
|  IMU_CTRL                          |SCHA634         |921    |680     |61      |730     |0       |0       |
|    CtrlData                        |CtrlData        |495    |440     |47      |337     |0       |0       |
|      usms                          |Time_1ms        |29     |24      |5       |21      |0       |0       |
|    SPIM                            |SPI_SCHA634     |426    |240     |14      |393     |0       |0       |
|  POWER                             |POWER_EN        |96     |57      |38      |37      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |580    |359     |103     |389     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |580    |359     |103     |389     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |247    |139     |0       |230     |0       |0       |
|        reg_inst                    |register        |244    |136     |0       |227     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |333    |220     |103     |159     |0       |0       |
|        bus_inst                    |bus_top         |131    |85      |46      |51      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |26     |16      |10      |10      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |53     |35      |18      |21      |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |52     |34      |18      |20      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |130    |97      |29      |82      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13529  
    #2          2       3401   
    #3          3        686   
    #4          4        312   
    #5        5-10       957   
    #6        11-50      442   
    #7       51-100      10    
    #8       101-500      3    
    #9        >500        2    
  Average     2.14             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.112029s wall, 3.609375s user + 0.000000s system = 3.609375s CPU (170.9%)

RUN-1004 : used memory is 1058 MB, reserved memory is 1055 MB, peak memory is 1124 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67786, tnet num: 19404, tinst num: 8134, tnode num: 91874, tedge num: 111794.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.742349s wall, 1.750000s user + 0.000000s system = 1.750000s CPU (100.4%)

RUN-1004 : used memory is 1060 MB, reserved memory is 1056 MB, peak memory is 1124 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19404 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.475896s wall, 1.468750s user + 0.000000s system = 1.468750s CPU (99.5%)

RUN-1004 : used memory is 1065 MB, reserved memory is 1061 MB, peak memory is 1124 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: e601653745e32f6a3c251c791c02121f1f191a93706cd5eb61993631bee2d7f5 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8134
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19406, pip num: 146819
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 342
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3241 valid insts, and 411791 bits set as '1'.
BIT-1004 : the usercode register value: 00000000111110101100001100101001
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.462381s wall, 124.859375s user + 0.187500s system = 125.046875s CPU (1003.4%)

RUN-1004 : used memory is 1191 MB, reserved memory is 1177 MB, peak memory is 1306 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250820_141149.log"

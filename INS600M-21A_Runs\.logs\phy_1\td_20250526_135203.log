============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Mon May 26 13:52:03 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(247)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(252)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(273)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(278)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(421)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(428)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(435)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(442)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(449)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(456)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(463)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(470)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(647)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(652)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(680)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(685)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(919)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(926)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(933)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(940)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(947)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(952)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(959)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(966)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(973)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(980)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(514)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
RUN-1001 : Project manager successfully analyzed 21 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.492493s wall, 1.328125s user + 4.156250s system = 5.484375s CPU (99.9%)

RUN-1004 : used memory is 78 MB, reserved memory is 41 MB, peak memory is 89 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.786950s wall, 1.671875s user + 0.125000s system = 1.796875s CPU (100.6%)

RUN-1004 : used memory is 298 MB, reserved memory is 267 MB, peak memory is 301 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 7 view nodes, 47 trigger nets, 47 data nets.
KIT-1004 : Chipwatcher code = 0001111110010110
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=144) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=144) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=144)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=144)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22875/31 useful/useless nets, 19659/17 useful/useless insts
SYN-1016 : Merged 36 instances.
SYN-1032 : 22500/22 useful/useless nets, 20119/18 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 4 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 4 mux instances.
SYN-1015 : Optimize round 1, 443 better
SYN-1014 : Optimize round 2
SYN-1032 : 22128/60 useful/useless nets, 19747/64 useful/useless insts
SYN-1015 : Optimize round 2, 128 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.344756s wall, 2.281250s user + 0.062500s system = 2.343750s CPU (100.0%)

RUN-1004 : used memory is 326 MB, reserved memory is 293 MB, peak memory is 328 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22188/367 useful/useless nets, 19848/52 useful/useless insts
SYN-1016 : Merged 57 instances.
SYN-2571 : Optimize after map_dsp, round 1, 476 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 44 instances.
SYN-2501 : Optimize round 1, 90 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 18 macro adder
SYN-1019 : Optimized 17 mux instances.
SYN-1016 : Merged 12 instances.
SYN-1032 : 22659/5 useful/useless nets, 20319/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 83231, tnet num: 22659, tinst num: 20318, tnode num: 116853, tedge num: 129635.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.163132s wall, 1.125000s user + 0.031250s system = 1.156250s CPU (99.4%)

RUN-1004 : used memory is 468 MB, reserved memory is 436 MB, peak memory is 468 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22659 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 257 (3.41), #lev = 7 (1.78)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 257 (3.41), #lev = 7 (1.78)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 596 instances into 257 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 450 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 140 adder to BLE ...
SYN-4008 : Packed 140 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.403759s wall, 4.343750s user + 0.062500s system = 4.406250s CPU (100.1%)

RUN-1004 : used memory is 352 MB, reserved memory is 321 MB, peak memory is 577 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.071927s wall, 6.875000s user + 0.187500s system = 7.062500s CPU (99.9%)

RUN-1004 : used memory is 353 MB, reserved memory is 322 MB, peak memory is 577 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/rmc_com2/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/rmc_com2/GPRMC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/rmc_com2/GPRMC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/rmc_com2/GPRMC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/rmc_com2/GPRMC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/rmc_com2/GPRMC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/rmc_com2/GPRMC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/rmc_com2/GPRMC_data[0]
SYN-5055 WARNING: The kept net COM2/rx_err will be merged to another kept net COM2/rmc_com2/GPRMC_err
SYN-5055 WARNING: The kept net FMC/Error_state[0] will be merged to another kept net COM2/rmc_com2/GPRMC_sub_state_cnt0[3]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (306 clock/control pins, 0 other pins).
SYN-4027 : Net DATA/DIV_Dtemp/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net DATA/DIV_Dtemp/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19646 instances
RUN-0007 : 5646 luts, 12473 seqs, 933 mslices, 491 lslices, 56 pads, 42 brams, 0 dsps
RUN-1001 : There are total 22010 nets
RUN-1001 : 16516 nets have 2 pins
RUN-1001 : 4355 nets have [3 - 5] pins
RUN-1001 : 785 nets have [6 - 10] pins
RUN-1001 : 224 nets have [11 - 20] pins
RUN-1001 : 106 nets have [21 - 99] pins
RUN-1001 : 24 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4744     
RUN-1001 :   No   |  No   |  Yes  |     628     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    6531     
RUN-1001 :   Yes  |  No   |  Yes  |     470     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19644 instances, 5646 luts, 12473 seqs, 1424 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1131 pins
PHY-3001 : Huge net DATA/done_div with 1650 pins
PHY-0007 : Cell area utilization is 64%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81762, tnet num: 22008, tinst num: 19644, tnode num: 115482, tedge num: 128446.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.163942s wall, 1.140625s user + 0.031250s system = 1.171875s CPU (100.7%)

RUN-1004 : used memory is 528 MB, reserved memory is 500 MB, peak memory is 577 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22008 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.988828s wall, 1.953125s user + 0.046875s system = 2.000000s CPU (100.6%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.42558e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19644.
PHY-3001 : Level 1 #clusters 2107.
PHY-3001 : End clustering;  0.143499s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (163.3%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 64%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 867639, overlap = 652.656
PHY-3002 : Step(2): len = 788133, overlap = 716.719
PHY-3002 : Step(3): len = 498500, overlap = 950.719
PHY-3002 : Step(4): len = 451663, overlap = 986.656
PHY-3002 : Step(5): len = 358559, overlap = 1095.72
PHY-3002 : Step(6): len = 318615, overlap = 1158.44
PHY-3002 : Step(7): len = 264217, overlap = 1222.88
PHY-3002 : Step(8): len = 233254, overlap = 1293.28
PHY-3002 : Step(9): len = 204654, overlap = 1344.47
PHY-3002 : Step(10): len = 185576, overlap = 1373.22
PHY-3002 : Step(11): len = 166303, overlap = 1406.56
PHY-3002 : Step(12): len = 150367, overlap = 1462.12
PHY-3002 : Step(13): len = 137536, overlap = 1513.16
PHY-3002 : Step(14): len = 124414, overlap = 1549.28
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.23531e-06
PHY-3002 : Step(15): len = 130030, overlap = 1526.84
PHY-3002 : Step(16): len = 184515, overlap = 1373.78
PHY-3002 : Step(17): len = 197737, overlap = 1267.84
PHY-3002 : Step(18): len = 202042, overlap = 1162.94
PHY-3002 : Step(19): len = 197742, overlap = 1104.53
PHY-3002 : Step(20): len = 191400, overlap = 1065.06
PHY-3002 : Step(21): len = 187191, overlap = 1042.94
PHY-3002 : Step(22): len = 181630, overlap = 1041.16
PHY-3002 : Step(23): len = 178959, overlap = 1024.72
PHY-3002 : Step(24): len = 175306, overlap = 1019.31
PHY-3002 : Step(25): len = 172472, overlap = 1023.75
PHY-3002 : Step(26): len = 171016, overlap = 1038.56
PHY-3002 : Step(27): len = 169466, overlap = 1050.19
PHY-3002 : Step(28): len = 168407, overlap = 1053.28
PHY-3002 : Step(29): len = 167252, overlap = 1053.12
PHY-3002 : Step(30): len = 167676, overlap = 1037.28
PHY-3002 : Step(31): len = 166060, overlap = 1030.31
PHY-3002 : Step(32): len = 166222, overlap = 1038.34
PHY-3002 : Step(33): len = 164366, overlap = 1043
PHY-3002 : Step(34): len = 163753, overlap = 1038.34
PHY-3002 : Step(35): len = 162211, overlap = 1042.84
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.47062e-06
PHY-3002 : Step(36): len = 170381, overlap = 1019.12
PHY-3002 : Step(37): len = 184607, overlap = 954.031
PHY-3002 : Step(38): len = 187900, overlap = 918.594
PHY-3002 : Step(39): len = 189914, overlap = 901.156
PHY-3002 : Step(40): len = 190775, overlap = 877.906
PHY-3002 : Step(41): len = 189405, overlap = 892.688
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.94125e-06
PHY-3002 : Step(42): len = 200359, overlap = 853.344
PHY-3002 : Step(43): len = 215394, overlap = 796.031
PHY-3002 : Step(44): len = 222432, overlap = 742.875
PHY-3002 : Step(45): len = 225278, overlap = 696.562
PHY-3002 : Step(46): len = 223758, overlap = 695.375
PHY-3002 : Step(47): len = 221454, overlap = 694.062
PHY-3002 : Step(48): len = 219692, overlap = 712.188
PHY-3002 : Step(49): len = 217074, overlap = 708.312
PHY-3002 : Step(50): len = 215728, overlap = 695.312
PHY-3002 : Step(51): len = 214828, overlap = 698.562
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 9.88249e-06
PHY-3002 : Step(52): len = 225239, overlap = 650.938
PHY-3002 : Step(53): len = 241509, overlap = 601.438
PHY-3002 : Step(54): len = 245907, overlap = 574.75
PHY-3002 : Step(55): len = 247519, overlap = 539.406
PHY-3002 : Step(56): len = 248236, overlap = 542.938
PHY-3002 : Step(57): len = 248049, overlap = 517.844
PHY-3002 : Step(58): len = 247439, overlap = 492.156
PHY-3002 : Step(59): len = 246189, overlap = 489.875
PHY-3002 : Step(60): len = 245067, overlap = 487.531
PHY-3002 : Step(61): len = 242821, overlap = 491.469
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.9765e-05
PHY-3002 : Step(62): len = 253779, overlap = 472.375
PHY-3002 : Step(63): len = 265583, overlap = 465.875
PHY-3002 : Step(64): len = 269077, overlap = 466.344
PHY-3002 : Step(65): len = 269811, overlap = 439.594
PHY-3002 : Step(66): len = 269593, overlap = 438.969
PHY-3002 : Step(67): len = 268665, overlap = 438.531
PHY-3002 : Step(68): len = 266821, overlap = 447.938
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.953e-05
PHY-3002 : Step(69): len = 276632, overlap = 440.312
PHY-3002 : Step(70): len = 284471, overlap = 448.75
PHY-3002 : Step(71): len = 287086, overlap = 436.281
PHY-3002 : Step(72): len = 287958, overlap = 433.312
PHY-3002 : Step(73): len = 286581, overlap = 417.969
PHY-3002 : Step(74): len = 286216, overlap = 405.875
PHY-3002 : Step(75): len = 283824, overlap = 400.156
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 7.906e-05
PHY-3002 : Step(76): len = 291695, overlap = 387.375
PHY-3002 : Step(77): len = 298716, overlap = 381.25
PHY-3002 : Step(78): len = 300928, overlap = 370.219
PHY-3002 : Step(79): len = 301927, overlap = 354.312
PHY-3002 : Step(80): len = 301619, overlap = 344.281
PHY-3002 : Step(81): len = 300342, overlap = 347.562
PHY-3002 : Step(82): len = 298262, overlap = 336.938
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000156338
PHY-3002 : Step(83): len = 302121, overlap = 342.875
PHY-3002 : Step(84): len = 308238, overlap = 336.812
PHY-3002 : Step(85): len = 311783, overlap = 335.375
PHY-3002 : Step(86): len = 313674, overlap = 330.469
PHY-3002 : Step(87): len = 314840, overlap = 306.781
PHY-3002 : Step(88): len = 315485, overlap = 298.656
PHY-3002 : Step(89): len = 313817, overlap = 270.156
PHY-3002 : Step(90): len = 313513, overlap = 265.062
PHY-3002 : Step(91): len = 311685, overlap = 259.75
PHY-3002 : Step(92): len = 311838, overlap = 258.5
PHY-3002 : Step(93): len = 310668, overlap = 267.594
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000266479
PHY-3002 : Step(94): len = 312443, overlap = 261.875
PHY-3002 : Step(95): len = 317074, overlap = 264.281
PHY-3002 : Step(96): len = 318715, overlap = 273.25
PHY-3002 : Step(97): len = 321077, overlap = 269.219
PHY-3002 : Step(98): len = 320727, overlap = 265.156
PHY-3002 : Step(99): len = 319343, overlap = 278.344
PHY-3002 : Step(100): len = 318541, overlap = 282.562
PHY-3002 : Step(101): len = 318624, overlap = 287.844
PHY-3002 : Step(102): len = 317808, overlap = 290.344
PHY-3002 : Step(103): len = 317704, overlap = 299.938
PHY-3002 : Step(104): len = 318092, overlap = 288.875
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(105): len = 318789, overlap = 288.5
PHY-3002 : Step(106): len = 320846, overlap = 276.406
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.012619s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (123.8%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/22010.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 427776, over cnt = 1168(3%), over = 5450, worst = 41
PHY-1001 : End global iterations;  0.781706s wall, 0.968750s user + 0.015625s system = 0.984375s CPU (125.9%)

PHY-1001 : Congestion index: top1 = 77.33, top5 = 53.38, top10 = 43.29, top15 = 37.54.
PHY-3001 : End congestion estimation;  1.057220s wall, 1.234375s user + 0.031250s system = 1.265625s CPU (119.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22008 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.884344s wall, 0.875000s user + 0.000000s system = 0.875000s CPU (98.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.34664e-05
PHY-3002 : Step(107): len = 360446, overlap = 211.469
PHY-3002 : Step(108): len = 372416, overlap = 189.531
PHY-3002 : Step(109): len = 374085, overlap = 186.312
PHY-3002 : Step(110): len = 376822, overlap = 175.531
PHY-3002 : Step(111): len = 381644, overlap = 171.438
PHY-3002 : Step(112): len = 384229, overlap = 170.625
PHY-3002 : Step(113): len = 386226, overlap = 162.375
PHY-3002 : Step(114): len = 388393, overlap = 154.281
PHY-3002 : Step(115): len = 390874, overlap = 146.344
PHY-3002 : Step(116): len = 390591, overlap = 142.375
PHY-3002 : Step(117): len = 392093, overlap = 136.031
PHY-3002 : Step(118): len = 394841, overlap = 134.344
PHY-3002 : Step(119): len = 396428, overlap = 133.656
PHY-3002 : Step(120): len = 397297, overlap = 135.438
PHY-3002 : Step(121): len = 400421, overlap = 142.469
PHY-3002 : Step(122): len = 403613, overlap = 146.781
PHY-3002 : Step(123): len = 403048, overlap = 143.906
PHY-3002 : Step(124): len = 403670, overlap = 143.156
PHY-3002 : Step(125): len = 404499, overlap = 143.531
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000186933
PHY-3002 : Step(126): len = 404610, overlap = 138.781
PHY-3002 : Step(127): len = 406147, overlap = 136.719
PHY-3002 : Step(128): len = 407420, overlap = 135.188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000360621
PHY-3002 : Step(129): len = 412921, overlap = 129.656
PHY-3002 : Step(130): len = 422395, overlap = 125.406
PHY-3002 : Step(131): len = 424929, overlap = 120.031
PHY-3002 : Step(132): len = 427001, overlap = 119.375
PHY-3002 : Step(133): len = 429547, overlap = 117.312
PHY-3002 : Step(134): len = 429572, overlap = 108.812
PHY-3002 : Step(135): len = 430360, overlap = 100.062
PHY-3002 : Step(136): len = 432659, overlap = 97.6562
PHY-3002 : Step(137): len = 434210, overlap = 88.0312
PHY-3002 : Step(138): len = 434365, overlap = 80.4062
PHY-3002 : Step(139): len = 433988, overlap = 79.9688
PHY-3002 : Step(140): len = 435033, overlap = 77.6875
PHY-3002 : Step(141): len = 436854, overlap = 90.9062
PHY-3002 : Step(142): len = 438526, overlap = 91.25
PHY-3002 : Step(143): len = 440536, overlap = 96.625
PHY-3002 : Step(144): len = 440286, overlap = 96.4375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000721243
PHY-3002 : Step(145): len = 440324, overlap = 88.9375
PHY-3002 : Step(146): len = 442555, overlap = 81.375
PHY-3002 : Step(147): len = 444981, overlap = 81.375
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00132469
PHY-3002 : Step(148): len = 444986, overlap = 82.9688
PHY-3002 : Step(149): len = 451268, overlap = 84.875
PHY-3002 : Step(150): len = 461086, overlap = 86
PHY-3002 : Step(151): len = 464358, overlap = 89.9688
PHY-3002 : Step(152): len = 464651, overlap = 92.625
PHY-3002 : Step(153): len = 463109, overlap = 92.8438
PHY-3002 : Step(154): len = 462367, overlap = 98.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 58/22010.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 528552, over cnt = 2101(5%), over = 10664, worst = 41
PHY-1001 : End global iterations;  1.071183s wall, 1.656250s user + 0.046875s system = 1.703125s CPU (159.0%)

PHY-1001 : Congestion index: top1 = 88.12, top5 = 64.87, top10 = 54.42, top15 = 48.17.
PHY-3001 : End congestion estimation;  1.351445s wall, 1.937500s user + 0.046875s system = 1.984375s CPU (146.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22008 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.892472s wall, 0.875000s user + 0.015625s system = 0.890625s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.06414e-05
PHY-3002 : Step(155): len = 474256, overlap = 405.156
PHY-3002 : Step(156): len = 481310, overlap = 350.531
PHY-3002 : Step(157): len = 471217, overlap = 322.312
PHY-3002 : Step(158): len = 464984, overlap = 288.656
PHY-3002 : Step(159): len = 463390, overlap = 273.969
PHY-3002 : Step(160): len = 459473, overlap = 260.469
PHY-3002 : Step(161): len = 454112, overlap = 246.812
PHY-3002 : Step(162): len = 450565, overlap = 238.5
PHY-3002 : Step(163): len = 445674, overlap = 227.25
PHY-3002 : Step(164): len = 442039, overlap = 234.375
PHY-3002 : Step(165): len = 440507, overlap = 228.469
PHY-3002 : Step(166): len = 436914, overlap = 226.062
PHY-3002 : Step(167): len = 436357, overlap = 215.938
PHY-3002 : Step(168): len = 434008, overlap = 217.625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000181283
PHY-3002 : Step(169): len = 433500, overlap = 207.219
PHY-3002 : Step(170): len = 435567, overlap = 201.688
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000362566
PHY-3002 : Step(171): len = 438549, overlap = 200.719
PHY-3002 : Step(172): len = 446736, overlap = 173.469
PHY-3002 : Step(173): len = 450247, overlap = 162.594
PHY-3002 : Step(174): len = 450535, overlap = 162.125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000725131
PHY-3002 : Step(175): len = 451084, overlap = 159
PHY-3002 : Step(176): len = 454697, overlap = 163.469
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81762, tnet num: 22008, tinst num: 19644, tnode num: 115482, tedge num: 128446.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.426952s wall, 1.390625s user + 0.031250s system = 1.421875s CPU (99.6%)

RUN-1004 : used memory is 568 MB, reserved memory is 543 MB, peak memory is 703 MB
OPT-1001 : Total overflow 535.66 peak overflow 4.47
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 363/22010.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 536624, over cnt = 2537(7%), over = 8883, worst = 27
PHY-1001 : End global iterations;  1.245806s wall, 1.906250s user + 0.078125s system = 1.984375s CPU (159.3%)

PHY-1001 : Congestion index: top1 = 60.91, top5 = 48.55, top10 = 42.99, top15 = 39.57.
PHY-1001 : End incremental global routing;  1.467633s wall, 2.125000s user + 0.078125s system = 2.203125s CPU (150.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22008 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.941255s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (97.9%)

OPT-1001 : 17 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 56 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19568 has valid locations, 260 needs to be replaced
PHY-3001 : design contains 19887 instances, 5755 luts, 12607 seqs, 1424 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 473085
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17151/22253.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 553200, over cnt = 2570(7%), over = 8993, worst = 27
PHY-1001 : End global iterations;  0.179415s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (156.8%)

PHY-1001 : Congestion index: top1 = 61.14, top5 = 48.73, top10 = 43.22, top15 = 39.86.
PHY-3001 : End congestion estimation;  0.422789s wall, 0.515625s user + 0.015625s system = 0.531250s CPU (125.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82588, tnet num: 22251, tinst num: 19887, tnode num: 116629, tedge num: 129612.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.502927s wall, 1.453125s user + 0.062500s system = 1.515625s CPU (100.8%)

RUN-1004 : used memory is 612 MB, reserved memory is 607 MB, peak memory is 705 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22251 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.501815s wall, 2.437500s user + 0.062500s system = 2.500000s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(177): len = 472710, overlap = 5.21875
PHY-3002 : Step(178): len = 473483, overlap = 5.15625
PHY-3002 : Step(179): len = 474116, overlap = 5.09375
PHY-3002 : Step(180): len = 474943, overlap = 5.09375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17179/22253.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 550560, over cnt = 2621(7%), over = 9133, worst = 27
PHY-1001 : End global iterations;  0.181628s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (172.1%)

PHY-1001 : Congestion index: top1 = 61.38, top5 = 48.95, top10 = 43.37, top15 = 40.01.
PHY-3001 : End congestion estimation;  0.408569s wall, 0.515625s user + 0.015625s system = 0.531250s CPU (130.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22251 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.918679s wall, 0.906250s user + 0.031250s system = 0.937500s CPU (102.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000813808
PHY-3002 : Step(181): len = 474817, overlap = 166.344
PHY-3002 : Step(182): len = 474996, overlap = 166.281
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00159688
PHY-3002 : Step(183): len = 475347, overlap = 166.562
PHY-3002 : Step(184): len = 476045, overlap = 165.844
PHY-3001 : Final: Len = 476045, Over = 165.844
PHY-3001 : End incremental placement;  5.138101s wall, 5.812500s user + 0.312500s system = 6.125000s CPU (119.2%)

OPT-1001 : Total overflow 542.28 peak overflow 4.47
OPT-1001 : End high-fanout net optimization;  8.046238s wall, 9.531250s user + 0.390625s system = 9.921875s CPU (123.3%)

OPT-1001 : Current memory(MB): used = 709, reserve = 690, peak = 727.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17185/22253.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 552472, over cnt = 2547(7%), over = 8539, worst = 27
PHY-1002 : len = 598192, over cnt = 1725(4%), over = 4288, worst = 27
PHY-1002 : len = 625936, over cnt = 899(2%), over = 2171, worst = 20
PHY-1002 : len = 643528, over cnt = 445(1%), over = 1015, worst = 20
PHY-1002 : len = 662088, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.267242s wall, 1.843750s user + 0.015625s system = 1.859375s CPU (146.7%)

PHY-1001 : Congestion index: top1 = 52.22, top5 = 44.55, top10 = 40.91, top15 = 38.61.
OPT-1001 : End congestion update;  1.495742s wall, 2.078125s user + 0.015625s system = 2.093750s CPU (140.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22251 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.817214s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (99.4%)

OPT-0007 : Start: WNS 4303 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.318525s wall, 2.890625s user + 0.015625s system = 2.906250s CPU (125.3%)

OPT-1001 : Current memory(MB): used = 707, reserve = 688, peak = 727.
OPT-1001 : End physical optimization;  12.097735s wall, 14.250000s user + 0.484375s system = 14.734375s CPU (121.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5755 LUT to BLE ...
SYN-4008 : Packed 5755 LUT and 2861 SEQ to BLE.
SYN-4003 : Packing 9746 remaining SEQ's ...
SYN-4005 : Packed 3317 SEQ with LUT/SLICE
SYN-4006 : 87 single LUT's are left
SYN-4006 : 6429 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 12184/13831 primitive instances ...
PHY-3001 : End packing;  2.849483s wall, 2.843750s user + 0.000000s system = 2.843750s CPU (99.8%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8192 instances
RUN-1001 : 4045 mslices, 4044 lslices, 56 pads, 42 brams, 0 dsps
RUN-1001 : There are total 19433 nets
RUN-1001 : 13605 nets have 2 pins
RUN-1001 : 4452 nets have [3 - 5] pins
RUN-1001 : 857 nets have [6 - 10] pins
RUN-1001 : 364 nets have [11 - 20] pins
RUN-1001 : 146 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8190 instances, 8089 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Cell area utilization is 86%
PHY-3001 : After packing: Len = 493311, Over = 390.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7897/19433.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 626688, over cnt = 1575(4%), over = 2598, worst = 8
PHY-1002 : len = 632856, over cnt = 1062(3%), over = 1582, worst = 6
PHY-1002 : len = 642816, over cnt = 573(1%), over = 835, worst = 5
PHY-1002 : len = 653120, over cnt = 192(0%), over = 263, worst = 5
PHY-1002 : len = 657744, over cnt = 8(0%), over = 9, worst = 2
PHY-1001 : End global iterations;  1.105646s wall, 1.828125s user + 0.015625s system = 1.843750s CPU (166.8%)

PHY-1001 : Congestion index: top1 = 52.56, top5 = 44.35, top10 = 40.49, top15 = 38.08.
PHY-3001 : End congestion estimation;  1.392968s wall, 2.109375s user + 0.015625s system = 2.125000s CPU (152.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68071, tnet num: 19431, tinst num: 8190, tnode num: 92800, tedge num: 112059.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.646300s wall, 1.593750s user + 0.062500s system = 1.656250s CPU (100.6%)

RUN-1004 : used memory is 601 MB, reserved memory is 585 MB, peak memory is 727 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19431 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.490397s wall, 2.421875s user + 0.078125s system = 2.500000s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.49288e-05
PHY-3002 : Step(185): len = 498001, overlap = 365.25
PHY-3002 : Step(186): len = 496527, overlap = 385.5
PHY-3002 : Step(187): len = 496691, overlap = 389.25
PHY-3002 : Step(188): len = 498355, overlap = 390.5
PHY-3002 : Step(189): len = 498149, overlap = 393
PHY-3002 : Step(190): len = 497327, overlap = 396.5
PHY-3002 : Step(191): len = 497069, overlap = 398.75
PHY-3002 : Step(192): len = 495523, overlap = 402
PHY-3002 : Step(193): len = 495030, overlap = 414.25
PHY-3002 : Step(194): len = 492866, overlap = 411.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.98576e-05
PHY-3002 : Step(195): len = 496731, overlap = 394.5
PHY-3002 : Step(196): len = 500302, overlap = 381
PHY-3002 : Step(197): len = 500490, overlap = 375.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000179715
PHY-3002 : Step(198): len = 509317, overlap = 363
PHY-3002 : Step(199): len = 519002, overlap = 344.25
PHY-3002 : Step(200): len = 516308, overlap = 344
PHY-3002 : Step(201): len = 515121, overlap = 342
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.574925s wall, 0.656250s user + 0.562500s system = 1.218750s CPU (212.0%)

PHY-3001 : Trial Legalized: Len = 631099
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 533/19433.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 721352, over cnt = 2507(7%), over = 4024, worst = 7
PHY-1002 : len = 736200, over cnt = 1568(4%), over = 2198, worst = 7
PHY-1002 : len = 756208, over cnt = 564(1%), over = 747, worst = 6
PHY-1002 : len = 761312, over cnt = 328(0%), over = 432, worst = 6
PHY-1002 : len = 770616, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.749513s wall, 3.015625s user + 0.000000s system = 3.015625s CPU (172.4%)

PHY-1001 : Congestion index: top1 = 50.09, top5 = 45.68, top10 = 43.44, top15 = 41.77.
PHY-3001 : End congestion estimation;  2.087156s wall, 3.359375s user + 0.000000s system = 3.359375s CPU (161.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19431 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.807365s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (98.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000183706
PHY-3002 : Step(202): len = 585280, overlap = 85
PHY-3002 : Step(203): len = 566636, overlap = 133
PHY-3002 : Step(204): len = 554616, overlap = 169.75
PHY-3002 : Step(205): len = 545855, overlap = 208.75
PHY-3002 : Step(206): len = 541351, overlap = 235.25
PHY-3002 : Step(207): len = 539339, overlap = 252
PHY-3002 : Step(208): len = 537674, overlap = 258
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000367412
PHY-3002 : Step(209): len = 543165, overlap = 245.75
PHY-3002 : Step(210): len = 547707, overlap = 244
PHY-3002 : Step(211): len = 547132, overlap = 244.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(212): len = 550764, overlap = 241
PHY-3002 : Step(213): len = 557554, overlap = 224.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.027515s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (113.6%)

PHY-3001 : Legalized: Len = 603225, Over = 0
PHY-3001 : Spreading special nets. 40 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.074911s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (104.3%)

PHY-3001 : 58 instances has been re-located, deltaX = 22, deltaY = 25, maxDist = 3.
PHY-3001 : Final: Len = 604021, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68071, tnet num: 19431, tinst num: 8190, tnode num: 92800, tedge num: 112059.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.872664s wall, 1.859375s user + 0.000000s system = 1.859375s CPU (99.3%)

RUN-1004 : used memory is 621 MB, reserved memory is 625 MB, peak memory is 727 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3778/19433.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 704176, over cnt = 2302(6%), over = 3659, worst = 8
PHY-1002 : len = 716384, over cnt = 1499(4%), over = 2071, worst = 5
PHY-1002 : len = 738248, over cnt = 332(0%), over = 408, worst = 4
PHY-1002 : len = 743584, over cnt = 80(0%), over = 89, worst = 2
PHY-1002 : len = 745608, over cnt = 1(0%), over = 1, worst = 1
PHY-1001 : End global iterations;  1.749321s wall, 2.828125s user + 0.015625s system = 2.843750s CPU (162.6%)

PHY-1001 : Congestion index: top1 = 48.43, top5 = 44.01, top10 = 41.62, top15 = 39.94.
PHY-1001 : End incremental global routing;  2.024997s wall, 3.109375s user + 0.015625s system = 3.125000s CPU (154.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19431 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.831167s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (97.8%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 56 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8130 has valid locations, 12 needs to be replaced
PHY-3001 : design contains 8201 instances, 8100 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 605293
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17520/19443.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 747024, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 746992, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 747048, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 747064, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.480728s wall, 0.468750s user + 0.000000s system = 0.468750s CPU (97.5%)

PHY-1001 : Congestion index: top1 = 48.45, top5 = 44.06, top10 = 41.67, top15 = 39.98.
PHY-3001 : End congestion estimation;  0.757287s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (99.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68114, tnet num: 19441, tinst num: 8201, tnode num: 92854, tedge num: 112112.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.861713s wall, 1.812500s user + 0.046875s system = 1.859375s CPU (99.9%)

RUN-1004 : used memory is 649 MB, reserved memory is 638 MB, peak memory is 727 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19441 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.717860s wall, 2.671875s user + 0.046875s system = 2.718750s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(214): len = 605215, overlap = 0.5
PHY-3002 : Step(215): len = 605231, overlap = 0.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17521/19443.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 746984, over cnt = 15(0%), over = 15, worst = 1
PHY-1002 : len = 747000, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 747056, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 747136, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.510322s wall, 0.515625s user + 0.015625s system = 0.531250s CPU (104.1%)

PHY-1001 : Congestion index: top1 = 48.45, top5 = 44.09, top10 = 41.70, top15 = 40.00.
PHY-3001 : End congestion estimation;  0.795333s wall, 0.781250s user + 0.031250s system = 0.812500s CPU (102.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19441 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.920808s wall, 0.906250s user + 0.015625s system = 0.921875s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000231956
PHY-3002 : Step(216): len = 605226, overlap = 2
PHY-3002 : Step(217): len = 605244, overlap = 2.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005840s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 605284, Over = 0
PHY-3001 : End spreading;  0.072616s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (107.6%)

PHY-3001 : Final: Len = 605284, Over = 0
PHY-3001 : End incremental placement;  5.841057s wall, 6.156250s user + 0.140625s system = 6.296875s CPU (107.8%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  9.141872s wall, 10.531250s user + 0.156250s system = 10.687500s CPU (116.9%)

OPT-1001 : Current memory(MB): used = 721, reserve = 706, peak = 727.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17521/19443.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 747280, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 747264, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 747296, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.358570s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (100.2%)

PHY-1001 : Congestion index: top1 = 48.41, top5 = 44.03, top10 = 41.69, top15 = 40.00.
OPT-1001 : End congestion update;  0.724719s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (99.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19441 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.701227s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (100.3%)

OPT-0007 : Start: WNS 4743 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.430139s wall, 1.437500s user + 0.000000s system = 1.437500s CPU (100.5%)

OPT-1001 : Current memory(MB): used = 720, reserve = 705, peak = 727.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19441 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.701043s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (98.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17531/19443.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 747296, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.118841s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (105.2%)

PHY-1001 : Congestion index: top1 = 48.41, top5 = 44.03, top10 = 41.69, top15 = 40.00.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19441 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.705530s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (99.7%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4743 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 48.068966
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4743ps with logic level 8 
RUN-1001 :       #2 path slack 4743ps with logic level 8 
RUN-1001 :       #3 path slack 4791ps with logic level 8 
RUN-1001 :       #4 path slack 4791ps with logic level 8 
OPT-1001 : End physical optimization;  14.502556s wall, 15.890625s user + 0.156250s system = 16.046875s CPU (110.6%)

RUN-1003 : finish command "place" in  74.129212s wall, 125.406250s user + 7.281250s system = 132.687500s CPU (179.0%)

RUN-1004 : used memory is 602 MB, reserved memory is 589 MB, peak memory is 727 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.555032s wall, 2.656250s user + 0.015625s system = 2.671875s CPU (171.8%)

RUN-1004 : used memory is 602 MB, reserved memory is 590 MB, peak memory is 727 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8203 instances
RUN-1001 : 4056 mslices, 4044 lslices, 56 pads, 42 brams, 0 dsps
RUN-1001 : There are total 19443 nets
RUN-1001 : 13606 nets have 2 pins
RUN-1001 : 4453 nets have [3 - 5] pins
RUN-1001 : 862 nets have [6 - 10] pins
RUN-1001 : 368 nets have [11 - 20] pins
RUN-1001 : 145 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68114, tnet num: 19441, tinst num: 8201, tnode num: 92854, tedge num: 112112.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.640779s wall, 1.640625s user + 0.000000s system = 1.640625s CPU (100.0%)

RUN-1004 : used memory is 592 MB, reserved memory is 581 MB, peak memory is 727 MB
PHY-1001 : 4056 mslices, 4044 lslices, 56 pads, 42 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19441 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 681672, over cnt = 2432(6%), over = 3988, worst = 8
PHY-1002 : len = 699184, over cnt = 1475(4%), over = 2001, worst = 8
PHY-1002 : len = 719264, over cnt = 483(1%), over = 575, worst = 5
PHY-1002 : len = 728896, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 729344, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.739762s wall, 2.781250s user + 0.062500s system = 2.843750s CPU (163.5%)

PHY-1001 : Congestion index: top1 = 48.10, top5 = 43.57, top10 = 41.11, top15 = 39.45.
PHY-1001 : End global routing;  2.048532s wall, 3.093750s user + 0.062500s system = 3.156250s CPU (154.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 703, reserve = 694, peak = 727.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net DATA/DIV_Dtemp/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 969, reserve = 959, peak = 969.
PHY-1001 : End build detailed router design. 4.482268s wall, 4.406250s user + 0.078125s system = 4.484375s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 188864, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.867858s wall, 0.843750s user + 0.015625s system = 0.859375s CPU (99.0%)

PHY-1001 : Current memory(MB): used = 1005, reserve = 996, peak = 1005.
PHY-1001 : End phase 1; 0.876348s wall, 0.859375s user + 0.015625s system = 0.875000s CPU (99.8%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 56% nets.
PHY-1001 : Routed 66% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.77323e+06, over cnt = 1522(0%), over = 1529, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1022, reserve = 1013, peak = 1022.
PHY-1001 : End initial routed; 17.904559s wall, 46.000000s user + 0.609375s system = 46.609375s CPU (260.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18243(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.585   |   0.000   |   0   
RUN-1001 :   Hold   |   0.112   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.231266s wall, 3.218750s user + 0.015625s system = 3.234375s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1034, reserve = 1025, peak = 1034.
PHY-1001 : End phase 2; 21.135978s wall, 49.218750s user + 0.625000s system = 49.843750s CPU (235.8%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.77323e+06, over cnt = 1522(0%), over = 1529, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.218469s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (100.1%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.75632e+06, over cnt = 633(0%), over = 633, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.908617s wall, 1.328125s user + 0.000000s system = 1.328125s CPU (146.2%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.75567e+06, over cnt = 146(0%), over = 146, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.419990s wall, 0.656250s user + 0.000000s system = 0.656250s CPU (156.3%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.75715e+06, over cnt = 17(0%), over = 17, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.296789s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (115.8%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.75747e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.172444s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (99.7%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.7575e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.141076s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (99.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18243(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.585   |   0.000   |   0   
RUN-1001 :   Hold   |   0.112   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.243175s wall, 3.234375s user + 0.000000s system = 3.234375s CPU (99.7%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 374 feed throughs used by 327 nets
PHY-1001 : End commit to database; 2.145631s wall, 2.093750s user + 0.062500s system = 2.156250s CPU (100.5%)

PHY-1001 : Current memory(MB): used = 1123, reserve = 1116, peak = 1123.
PHY-1001 : End phase 3; 8.029137s wall, 8.671875s user + 0.062500s system = 8.734375s CPU (108.8%)

PHY-1003 : Routed, final wirelength = 1.7575e+06
PHY-1001 : Current memory(MB): used = 1127, reserve = 1121, peak = 1127.
PHY-1001 : End export database. 0.058075s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (107.6%)

PHY-1001 : End detail routing;  34.976504s wall, 63.593750s user + 0.781250s system = 64.375000s CPU (184.1%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68114, tnet num: 19441, tinst num: 8201, tnode num: 92854, tedge num: 112112.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.623390s wall, 1.625000s user + 0.000000s system = 1.625000s CPU (100.1%)

RUN-1004 : used memory is 1058 MB, reserved memory is 1053 MB, peak memory is 1127 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  42.693410s wall, 72.328125s user + 0.875000s system = 73.203125s CPU (171.5%)

RUN-1004 : used memory is 1057 MB, reserved memory is 1053 MB, peak memory is 1127 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8733   out of  19600   44.56%
#reg                    12704   out of  19600   64.82%
#le                     15132
  #lut only              2428   out of  15132   16.05%
  #reg only              6399   out of  15132   42.29%
  #lut&reg               6305   out of  15132   41.67%
#dsp                        0   out of     29    0.00%
#bram                      42   out of     64   65.62%
  #bram9k                  42
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       56   out of    188   29.79%
  #ireg                     9
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet              Type               DriverType         Driver                    Fanout
#1        DATA/DIV_Dtemp/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6938
#2        config_inst_syn_9     GCLK               config             config_inst.jtck          173
#3        clk_in_dup_1          GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT        E11        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT        A13        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT        F10        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         E8        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         E7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT        A10        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C6        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         D6        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         C7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F7        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT        E12        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         A9        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         A7        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H13        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J11        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT        E13        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         R7        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         M7        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         P7        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         T8        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         P8        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         N8        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M9        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         T9        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         R9        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         T6        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         P9        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         N9        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT        P11        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         T5        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         B2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        L10        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        E15        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        R14        LVCMOS33           8            N/A            OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT         R1        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT        M12        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        K12        LVCMOS33           8            N/A            NONE       
    TXD_RMC        OUTPUT         C3        LVCMOS33           8            N/A            NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        R15        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E6        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15132  |7309    |1424    |12746   |42      |0       |
|  AnyFog_dataX                      |AnyFog          |202    |95      |22      |168     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |83     |50      |22      |49      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |200    |71      |22      |164     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |81     |57      |22      |45      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |204    |107     |22      |169     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |82     |59      |22      |47      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |3497   |907     |34      |3406    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |731    |70      |5       |718     |0       |0       |
|    STADOP_com2                     |STADOP          |550    |104     |0       |543     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |64     |43      |14      |41      |0       |0       |
|    head_com2                       |uniheading      |277    |80      |5       |262     |0       |0       |
|    rmc_com2                        |Gprmc           |154    |74      |0       |147     |0       |0       |
|    uart_com2                       |Agrica          |1431   |246     |10      |1405    |0       |0       |
|  DATA                              |Data_Processing |8635   |4419    |1062    |6956    |0       |0       |
|    DIV_Dtemp                       |Divider         |830    |371     |84      |704     |0       |0       |
|    DIV_Utemp                       |Divider         |620    |288     |84      |495     |0       |0       |
|    DIV_accX                        |Divider         |622    |315     |84      |495     |0       |0       |
|    DIV_accY                        |Divider         |551    |325     |111     |383     |0       |0       |
|    DIV_accZ                        |Divider         |651    |389     |132     |446     |0       |0       |
|    DIV_rateX                       |Divider         |730    |386     |132     |525     |0       |0       |
|    DIV_rateY                       |Divider         |545    |302     |132     |338     |0       |0       |
|    DIV_rateZ                       |Divider         |634    |452     |132     |428     |0       |0       |
|    genclk                          |genclk          |78     |54      |20      |45      |0       |0       |
|  FMC                               |FMC_Ctrl        |450    |396     |43      |354     |0       |0       |
|  IIC                               |I2C_master      |294    |241     |11      |253     |0       |0       |
|  IMU_CTRL                          |SCHA634         |860    |612     |61      |710     |0       |0       |
|    CtrlData                        |CtrlData        |444    |391     |47      |328     |0       |0       |
|      usms                          |Time_1ms        |30     |25      |5       |22      |0       |0       |
|    SPIM                            |SPI_SCHA634     |416    |221     |14      |382     |0       |0       |
|  POWER                             |POWER_EN        |96     |50      |38      |37      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |683    |411     |109     |476     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |683    |411     |109     |476     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |304    |170     |0       |286     |0       |0       |
|        reg_inst                    |register        |301    |167     |0       |283     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |379    |241     |109     |190     |0       |0       |
|        bus_inst                    |bus_top         |151    |93      |52      |62      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |28     |18      |10      |12      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |1      |0       |0       |1       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |15     |9       |6       |7       |0       |0       |
|          BUS_DETECTOR[5]$bus_nodes |bus_det         |50     |32      |18      |18      |0       |0       |
|          BUS_DETECTOR[6]$bus_nodes |bus_det         |53     |30      |18      |20      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |143    |105     |29      |90      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13549  
    #2          2       3515   
    #3          3        684   
    #4          4        254   
    #5        5-10       910   
    #6        11-50      448   
    #7       51-100      17    
    #8       101-500      3    
    #9        >500        2    
  Average     2.14             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  1.888705s wall, 3.250000s user + 0.031250s system = 3.281250s CPU (173.7%)

RUN-1004 : used memory is 1058 MB, reserved memory is 1054 MB, peak memory is 1127 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 68114, tnet num: 19441, tinst num: 8201, tnode num: 92854, tedge num: 112112.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.623838s wall, 1.625000s user + 0.000000s system = 1.625000s CPU (100.1%)

RUN-1004 : used memory is 1060 MB, reserved memory is 1055 MB, peak memory is 1127 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19441 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.263287s wall, 1.265625s user + 0.000000s system = 1.265625s CPU (100.2%)

RUN-1004 : used memory is 1065 MB, reserved memory is 1059 MB, peak memory is 1127 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 8f169df8acfcb835781910b1ac29267cd80e16d8a5e1b8cf9d032ec93c0a2865 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8201
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19443, pip num: 148916
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 374
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3213 valid insts, and 415801 bits set as '1'.
BIT-1004 : the usercode register value: 00000000110101000001111110010110
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  10.165850s wall, 100.546875s user + 0.046875s system = 100.593750s CPU (989.5%)

RUN-1004 : used memory is 1194 MB, reserved memory is 1180 MB, peak memory is 1308 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250526_135203.log"

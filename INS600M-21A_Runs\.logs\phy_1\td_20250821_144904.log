============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Aug 21 14:49:04 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.802925s wall, 1.937500s user + 3.859375s system = 5.796875s CPU (99.9%)

RUN-1004 : used memory is 79 MB, reserved memory is 40 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.956767s wall, 1.921875s user + 0.046875s system = 1.968750s CPU (100.6%)

RUN-1004 : used memory is 302 MB, reserved memory is 270 MB, peak memory is 305 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 6 view nodes, 43 trigger nets, 43 data nets.
KIT-1004 : Chipwatcher code = 0011101000100101
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100,32'sb01001010}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=132) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=132) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100,32'sb01001010}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100,32'sb01001010}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100,32'sb01001010})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=132)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=132)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100,32'sb01001010})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb011010,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb01000100,32'sb01001010})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22853/26 useful/useless nets, 19576/15 useful/useless insts
SYN-1016 : Merged 33 instances.
SYN-1032 : 22505/22 useful/useless nets, 20011/18 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 412 better
SYN-1014 : Optimize round 2
SYN-1032 : 22174/45 useful/useless nets, 19680/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.711829s wall, 2.609375s user + 0.109375s system = 2.718750s CPU (100.3%)

RUN-1004 : used memory is 328 MB, reserved memory is 295 MB, peak memory is 330 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22222/300 useful/useless nets, 19766/48 useful/useless insts
SYN-1016 : Merged 45 instances.
SYN-2571 : Optimize after map_dsp, round 1, 393 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 40 instances.
SYN-2501 : Optimize round 1, 82 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 22676/5 useful/useless nets, 20220/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82771, tnet num: 22676, tinst num: 20219, tnode num: 115894, tedge num: 129387.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.353682s wall, 1.250000s user + 0.078125s system = 1.328125s CPU (98.1%)

RUN-1004 : used memory is 469 MB, reserved memory is 437 MB, peak memory is 469 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22676 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 240 (3.43), #lev = 7 (1.80)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 240 (3.43), #lev = 7 (1.80)
SYN-3001 : Logic optimization runtime opt =   0.04 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 559 instances into 240 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 421 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 128 adder to BLE ...
SYN-4008 : Packed 128 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  5.271133s wall, 5.140625s user + 0.125000s system = 5.265625s CPU (99.9%)

RUN-1004 : used memory is 352 MB, reserved memory is 318 MB, peak memory is 578 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  8.352985s wall, 8.078125s user + 0.281250s system = 8.359375s CPU (100.1%)

RUN-1004 : used memory is 353 MB, reserved memory is 319 MB, peak memory is 578 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (282 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19441 instances
RUN-0007 : 5620 luts, 12213 seqs, 983 mslices, 519 lslices, 60 pads, 41 brams, 0 dsps
RUN-1001 : There are total 21905 nets
RUN-1001 : 16435 nets have 2 pins
RUN-1001 : 4272 nets have [3 - 5] pins
RUN-1001 : 821 nets have [6 - 10] pins
RUN-1001 : 251 nets have [11 - 20] pins
RUN-1001 : 104 nets have [21 - 99] pins
RUN-1001 : 22 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4783     
RUN-1001 :   No   |  No   |  Yes  |     707     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     454     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19439 instances, 5620 luts, 12213 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81095, tnet num: 21903, tinst num: 19439, tnode num: 114023, tedge num: 127655.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.404114s wall, 1.375000s user + 0.015625s system = 1.390625s CPU (99.0%)

RUN-1004 : used memory is 528 MB, reserved memory is 499 MB, peak memory is 578 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21903 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.709542s wall, 2.671875s user + 0.031250s system = 2.703125s CPU (99.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.67344e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19439.
PHY-3001 : Level 1 #clusters 2200.
PHY-3001 : End clustering;  0.168580s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (111.2%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 881785, overlap = 636.406
PHY-3002 : Step(2): len = 806104, overlap = 673.156
PHY-3002 : Step(3): len = 525842, overlap = 845.156
PHY-3002 : Step(4): len = 458418, overlap = 930.219
PHY-3002 : Step(5): len = 370623, overlap = 981.312
PHY-3002 : Step(6): len = 324172, overlap = 1054.22
PHY-3002 : Step(7): len = 277641, overlap = 1139.69
PHY-3002 : Step(8): len = 245073, overlap = 1220.97
PHY-3002 : Step(9): len = 219452, overlap = 1273.81
PHY-3002 : Step(10): len = 196298, overlap = 1317.47
PHY-3002 : Step(11): len = 179872, overlap = 1338.53
PHY-3002 : Step(12): len = 165081, overlap = 1365.12
PHY-3002 : Step(13): len = 153593, overlap = 1379.81
PHY-3002 : Step(14): len = 142468, overlap = 1395.88
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.34676e-06
PHY-3002 : Step(15): len = 151473, overlap = 1360.38
PHY-3002 : Step(16): len = 197023, overlap = 1232.06
PHY-3002 : Step(17): len = 204855, overlap = 1107
PHY-3002 : Step(18): len = 207159, overlap = 1081.66
PHY-3002 : Step(19): len = 202427, overlap = 1044.06
PHY-3002 : Step(20): len = 199286, overlap = 1010.31
PHY-3002 : Step(21): len = 190477, overlap = 1004.56
PHY-3002 : Step(22): len = 186340, overlap = 1011.12
PHY-3002 : Step(23): len = 178677, overlap = 1033.06
PHY-3002 : Step(24): len = 176244, overlap = 1012.81
PHY-3002 : Step(25): len = 173036, overlap = 986.125
PHY-3002 : Step(26): len = 172784, overlap = 972.188
PHY-3002 : Step(27): len = 170968, overlap = 978.25
PHY-3002 : Step(28): len = 170572, overlap = 980.688
PHY-3002 : Step(29): len = 168702, overlap = 974.656
PHY-3002 : Step(30): len = 167795, overlap = 982.562
PHY-3002 : Step(31): len = 166193, overlap = 999.562
PHY-3002 : Step(32): len = 164876, overlap = 999.906
PHY-3002 : Step(33): len = 163803, overlap = 1001.97
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.69353e-06
PHY-3002 : Step(34): len = 172238, overlap = 962.656
PHY-3002 : Step(35): len = 185721, overlap = 910.375
PHY-3002 : Step(36): len = 189315, overlap = 884.938
PHY-3002 : Step(37): len = 190371, overlap = 870.219
PHY-3002 : Step(38): len = 191047, overlap = 889.031
PHY-3002 : Step(39): len = 190175, overlap = 909.656
PHY-3002 : Step(40): len = 189062, overlap = 882.406
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 5.38706e-06
PHY-3002 : Step(41): len = 198912, overlap = 847.656
PHY-3002 : Step(42): len = 211484, overlap = 767.375
PHY-3002 : Step(43): len = 215761, overlap = 728.219
PHY-3002 : Step(44): len = 218096, overlap = 705.344
PHY-3002 : Step(45): len = 218431, overlap = 694.469
PHY-3002 : Step(46): len = 216555, overlap = 681.219
PHY-3002 : Step(47): len = 214960, overlap = 679.875
PHY-3002 : Step(48): len = 213435, overlap = 685.188
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 1.07741e-05
PHY-3002 : Step(49): len = 226832, overlap = 637.188
PHY-3002 : Step(50): len = 242184, overlap = 591.25
PHY-3002 : Step(51): len = 247510, overlap = 553.469
PHY-3002 : Step(52): len = 249549, overlap = 532.25
PHY-3002 : Step(53): len = 248227, overlap = 513.688
PHY-3002 : Step(54): len = 246285, overlap = 506.031
PHY-3002 : Step(55): len = 243997, overlap = 512.062
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 2.15482e-05
PHY-3002 : Step(56): len = 254431, overlap = 494.625
PHY-3002 : Step(57): len = 266511, overlap = 438.438
PHY-3002 : Step(58): len = 270405, overlap = 397.781
PHY-3002 : Step(59): len = 273075, overlap = 376.812
PHY-3002 : Step(60): len = 272538, overlap = 373.531
PHY-3002 : Step(61): len = 270523, overlap = 384.219
PHY-3002 : Step(62): len = 268364, overlap = 390.5
PHY-3002 : Step(63): len = 266714, overlap = 376.125
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 4.30965e-05
PHY-3002 : Step(64): len = 275495, overlap = 375.594
PHY-3002 : Step(65): len = 285785, overlap = 328.844
PHY-3002 : Step(66): len = 289966, overlap = 315.469
PHY-3002 : Step(67): len = 289966, overlap = 317
PHY-3002 : Step(68): len = 288149, overlap = 320.75
PHY-3002 : Step(69): len = 285981, overlap = 315.938
PHY-3002 : Step(70): len = 284046, overlap = 317.688
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 8.61929e-05
PHY-3002 : Step(71): len = 291842, overlap = 296.5
PHY-3002 : Step(72): len = 298947, overlap = 277.812
PHY-3002 : Step(73): len = 301184, overlap = 258.312
PHY-3002 : Step(74): len = 302219, overlap = 255.656
PHY-3002 : Step(75): len = 300263, overlap = 261.062
PHY-3002 : Step(76): len = 298958, overlap = 250.344
PHY-3002 : Step(77): len = 298086, overlap = 252.281
PHY-3002 : Step(78): len = 299799, overlap = 260.656
PHY-3002 : Step(79): len = 299476, overlap = 262.938
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000172386
PHY-3002 : Step(80): len = 303278, overlap = 272.969
PHY-3002 : Step(81): len = 308897, overlap = 283.938
PHY-3002 : Step(82): len = 310764, overlap = 276.688
PHY-3002 : Step(83): len = 312082, overlap = 244.312
PHY-3002 : Step(84): len = 311552, overlap = 228.312
PHY-3002 : Step(85): len = 310485, overlap = 225.625
PHY-3002 : Step(86): len = 310035, overlap = 223.906
PHY-3002 : Step(87): len = 310742, overlap = 222.312
PHY-3002 : Step(88): len = 310415, overlap = 225.906
PHY-3002 : Step(89): len = 310411, overlap = 215.812
PHY-3002 : Step(90): len = 309365, overlap = 230.469
PHY-3002 : Step(91): len = 309469, overlap = 228.125
PHY-3002 : Step(92): len = 309050, overlap = 230.781
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000334132
PHY-3002 : Step(93): len = 310831, overlap = 227.75
PHY-3002 : Step(94): len = 314752, overlap = 228.719
PHY-3002 : Step(95): len = 316421, overlap = 220.156
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000615393
PHY-3002 : Step(96): len = 316670, overlap = 210.344
PHY-3002 : Step(97): len = 319712, overlap = 209.031
PHY-3002 : Step(98): len = 320645, overlap = 208.844
PHY-3002 : Step(99): len = 321552, overlap = 202.625
PHY-3002 : Step(100): len = 322275, overlap = 192.5
PHY-3002 : Step(101): len = 322668, overlap = 192.188
PHY-3002 : Step(102): len = 323084, overlap = 188.844
PHY-3002 : Step(103): len = 323087, overlap = 177.438
PHY-3002 : Step(104): len = 323094, overlap = 191.594
PHY-3002 : Step(105): len = 323046, overlap = 181.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.009379s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (166.6%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21905.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 432472, over cnt = 1215(3%), over = 5393, worst = 43
PHY-1001 : End global iterations;  0.874590s wall, 1.203125s user + 0.031250s system = 1.234375s CPU (141.1%)

PHY-1001 : Congestion index: top1 = 69.87, top5 = 51.05, top10 = 42.29, top15 = 37.08.
PHY-3001 : End congestion estimation;  1.119826s wall, 1.437500s user + 0.031250s system = 1.468750s CPU (131.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21903 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.027775s wall, 1.015625s user + 0.015625s system = 1.031250s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000105424
PHY-3002 : Step(106): len = 366327, overlap = 161.844
PHY-3002 : Step(107): len = 380019, overlap = 143.875
PHY-3002 : Step(108): len = 384422, overlap = 142.906
PHY-3002 : Step(109): len = 384242, overlap = 140.312
PHY-3002 : Step(110): len = 388797, overlap = 129.5
PHY-3002 : Step(111): len = 393347, overlap = 128.094
PHY-3002 : Step(112): len = 397167, overlap = 131.344
PHY-3002 : Step(113): len = 404219, overlap = 139.844
PHY-3002 : Step(114): len = 406375, overlap = 144.625
PHY-3002 : Step(115): len = 407316, overlap = 145.25
PHY-3002 : Step(116): len = 406755, overlap = 156.094
PHY-3002 : Step(117): len = 406410, overlap = 149.969
PHY-3002 : Step(118): len = 407956, overlap = 147.406
PHY-3002 : Step(119): len = 409962, overlap = 145.812
PHY-3002 : Step(120): len = 409795, overlap = 140.844
PHY-3002 : Step(121): len = 411061, overlap = 141.844
PHY-3002 : Step(122): len = 411743, overlap = 138.875
PHY-3002 : Step(123): len = 413709, overlap = 136.812
PHY-3002 : Step(124): len = 413375, overlap = 140.438
PHY-3002 : Step(125): len = 412864, overlap = 137.75
PHY-3002 : Step(126): len = 413742, overlap = 135.594
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000210848
PHY-3002 : Step(127): len = 413620, overlap = 134.938
PHY-3002 : Step(128): len = 415168, overlap = 133.719
PHY-3002 : Step(129): len = 419161, overlap = 132.031
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000421696
PHY-3002 : Step(130): len = 425788, overlap = 127.219
PHY-3002 : Step(131): len = 432462, overlap = 118.312
PHY-3002 : Step(132): len = 435050, overlap = 114.938
PHY-3002 : Step(133): len = 437452, overlap = 111.531
PHY-3002 : Step(134): len = 438514, overlap = 108.719
PHY-3002 : Step(135): len = 440007, overlap = 111.062
PHY-3002 : Step(136): len = 441174, overlap = 111.25
PHY-3002 : Step(137): len = 442086, overlap = 108.688
PHY-3002 : Step(138): len = 441402, overlap = 106.906
PHY-3002 : Step(139): len = 441049, overlap = 104.75
PHY-3002 : Step(140): len = 440338, overlap = 103.531
PHY-3002 : Step(141): len = 439984, overlap = 105.688
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000843392
PHY-3002 : Step(142): len = 438739, overlap = 103.438
PHY-3002 : Step(143): len = 442776, overlap = 100.938
PHY-3002 : Step(144): len = 447065, overlap = 97.625
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00166077
PHY-3002 : Step(145): len = 446011, overlap = 98.0938
PHY-3002 : Step(146): len = 449245, overlap = 97.4375
PHY-3002 : Step(147): len = 454103, overlap = 97.8438
PHY-3002 : Step(148): len = 457379, overlap = 97.9375
PHY-3002 : Step(149): len = 462231, overlap = 100.875
PHY-3002 : Step(150): len = 466460, overlap = 103.781
PHY-3002 : Step(151): len = 466518, overlap = 103.188
PHY-3002 : Step(152): len = 465829, overlap = 100.875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 46/21905.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 530240, over cnt = 2211(6%), over = 9868, worst = 45
PHY-1001 : End global iterations;  1.271892s wall, 2.000000s user + 0.031250s system = 2.031250s CPU (159.7%)

PHY-1001 : Congestion index: top1 = 72.31, top5 = 56.60, top10 = 48.54, top15 = 44.00.
PHY-3001 : End congestion estimation;  1.564266s wall, 2.296875s user + 0.031250s system = 2.328125s CPU (148.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21903 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.064545s wall, 1.062500s user + 0.000000s system = 1.062500s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000112216
PHY-3002 : Step(153): len = 468595, overlap = 340.531
PHY-3002 : Step(154): len = 469489, overlap = 297.25
PHY-3002 : Step(155): len = 461709, overlap = 275.844
PHY-3002 : Step(156): len = 456115, overlap = 266.125
PHY-3002 : Step(157): len = 451006, overlap = 260.156
PHY-3002 : Step(158): len = 449423, overlap = 244.281
PHY-3002 : Step(159): len = 445350, overlap = 246.906
PHY-3002 : Step(160): len = 444277, overlap = 241.312
PHY-3002 : Step(161): len = 441665, overlap = 231.375
PHY-3002 : Step(162): len = 438544, overlap = 229.656
PHY-3002 : Step(163): len = 436890, overlap = 230.469
PHY-3002 : Step(164): len = 435979, overlap = 225.688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000224432
PHY-3002 : Step(165): len = 435433, overlap = 217.156
PHY-3002 : Step(166): len = 436934, overlap = 203.812
PHY-3002 : Step(167): len = 438481, overlap = 200.469
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000448865
PHY-3002 : Step(168): len = 440221, overlap = 189.469
PHY-3002 : Step(169): len = 446123, overlap = 166.719
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000838415
PHY-3002 : Step(170): len = 448998, overlap = 151.531
PHY-3002 : Step(171): len = 455464, overlap = 137.531
PHY-3002 : Step(172): len = 461919, overlap = 126.438
PHY-3002 : Step(173): len = 464670, overlap = 120.938
PHY-3002 : Step(174): len = 464013, overlap = 119.625
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81095, tnet num: 21903, tinst num: 19439, tnode num: 114023, tedge num: 127655.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.755810s wall, 1.718750s user + 0.046875s system = 1.765625s CPU (100.6%)

RUN-1004 : used memory is 568 MB, reserved memory is 543 MB, peak memory is 702 MB
OPT-1001 : Total overflow 476.75 peak overflow 4.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 602/21905.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 547320, over cnt = 2442(6%), over = 8494, worst = 25
PHY-1001 : End global iterations;  1.351957s wall, 2.203125s user + 0.046875s system = 2.250000s CPU (166.4%)

PHY-1001 : Congestion index: top1 = 57.63, top5 = 47.01, top10 = 42.36, top15 = 39.25.
PHY-1001 : End incremental global routing;  1.641806s wall, 2.484375s user + 0.046875s system = 2.531250s CPU (154.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21903 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.116436s wall, 1.093750s user + 0.015625s system = 1.109375s CPU (99.4%)

OPT-1001 : 16 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19360 has valid locations, 233 needs to be replaced
PHY-3001 : design contains 19656 instances, 5704 luts, 12346 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 479302
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17315/22122.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 558416, over cnt = 2469(7%), over = 8507, worst = 25
PHY-1001 : End global iterations;  0.201783s wall, 0.171875s user + 0.046875s system = 0.218750s CPU (108.4%)

PHY-1001 : Congestion index: top1 = 57.69, top5 = 47.21, top10 = 42.65, top15 = 39.60.
PHY-3001 : End congestion estimation;  0.474449s wall, 0.453125s user + 0.046875s system = 0.500000s CPU (105.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81805, tnet num: 22120, tinst num: 19656, tnode num: 115050, tedge num: 128641.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.839095s wall, 1.796875s user + 0.015625s system = 1.812500s CPU (98.6%)

RUN-1004 : used memory is 611 MB, reserved memory is 604 MB, peak memory is 702 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22120 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.037539s wall, 2.984375s user + 0.015625s system = 3.000000s CPU (98.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(175): len = 479584, overlap = 1.875
PHY-3002 : Step(176): len = 481266, overlap = 1.875
PHY-3002 : Step(177): len = 482377, overlap = 1.875
PHY-3002 : Step(178): len = 483523, overlap = 1.875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17346/22122.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 558784, over cnt = 2486(7%), over = 8574, worst = 25
PHY-1001 : End global iterations;  0.199958s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (117.2%)

PHY-1001 : Congestion index: top1 = 58.17, top5 = 47.32, top10 = 42.74, top15 = 39.62.
PHY-3001 : End congestion estimation;  0.476234s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (108.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22120 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.287163s wall, 1.281250s user + 0.000000s system = 1.281250s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000958277
PHY-3002 : Step(179): len = 483232, overlap = 121.719
PHY-3002 : Step(180): len = 483157, overlap = 122.031
PHY-3002 : Step(181): len = 483195, overlap = 122.156
PHY-3001 : Final: Len = 483195, Over = 122.156
PHY-3001 : End incremental placement;  6.185914s wall, 6.656250s user + 0.312500s system = 6.968750s CPU (112.7%)

OPT-1001 : Total overflow 481.97 peak overflow 4.00
OPT-1001 : End high-fanout net optimization;  9.564117s wall, 10.921875s user + 0.390625s system = 11.312500s CPU (118.3%)

OPT-1001 : Current memory(MB): used = 705, reserve = 684, peak = 721.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17356/22122.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 559664, over cnt = 2458(6%), over = 8123, worst = 25
PHY-1002 : len = 601744, over cnt = 1618(4%), over = 3934, worst = 25
PHY-1002 : len = 636184, over cnt = 576(1%), over = 1285, worst = 12
PHY-1002 : len = 646144, over cnt = 305(0%), over = 638, worst = 11
PHY-1002 : len = 656456, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.430995s wall, 2.078125s user + 0.015625s system = 2.093750s CPU (146.3%)

PHY-1001 : Congestion index: top1 = 50.02, top5 = 42.88, top10 = 39.85, top15 = 37.83.
OPT-1001 : End congestion update;  1.753177s wall, 2.390625s user + 0.015625s system = 2.406250s CPU (137.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22120 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.133131s wall, 1.125000s user + 0.000000s system = 1.125000s CPU (99.3%)

OPT-0007 : Start: WNS 3519 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.893788s wall, 3.515625s user + 0.015625s system = 3.531250s CPU (122.0%)

OPT-1001 : Current memory(MB): used = 680, reserve = 663, peak = 721.
OPT-1001 : End physical optimization;  14.560602s wall, 16.593750s user + 0.468750s system = 17.062500s CPU (117.2%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5704 LUT to BLE ...
SYN-4008 : Packed 5704 LUT and 2729 SEQ to BLE.
SYN-4003 : Packing 9617 remaining SEQ's ...
SYN-4005 : Packed 3434 SEQ with LUT/SLICE
SYN-4006 : 78 single LUT's are left
SYN-4006 : 6183 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11887/13759 primitive instances ...
PHY-3001 : End packing;  3.272000s wall, 3.265625s user + 0.000000s system = 3.265625s CPU (99.8%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8095 instances
RUN-1001 : 3994 mslices, 3995 lslices, 60 pads, 41 brams, 0 dsps
RUN-1001 : There are total 19445 nets
RUN-1001 : 13616 nets have 2 pins
RUN-1001 : 4421 nets have [3 - 5] pins
RUN-1001 : 884 nets have [6 - 10] pins
RUN-1001 : 385 nets have [11 - 20] pins
RUN-1001 : 130 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8093 instances, 7989 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 499888, Over = 357
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 8015/19445.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 628496, over cnt = 1508(4%), over = 2340, worst = 8
PHY-1002 : len = 634016, over cnt = 990(2%), over = 1346, worst = 7
PHY-1002 : len = 643056, over cnt = 495(1%), over = 662, worst = 5
PHY-1002 : len = 647056, over cnt = 331(0%), over = 445, worst = 5
PHY-1002 : len = 654080, over cnt = 38(0%), over = 45, worst = 3
PHY-1001 : End global iterations;  1.274213s wall, 2.093750s user + 0.125000s system = 2.218750s CPU (174.1%)

PHY-1001 : Congestion index: top1 = 50.82, top5 = 43.90, top10 = 40.30, top15 = 38.05.
PHY-3001 : End congestion estimation;  1.635213s wall, 2.453125s user + 0.125000s system = 2.578125s CPU (157.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67846, tnet num: 19443, tinst num: 8093, tnode num: 91966, tedge num: 111939.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.057763s wall, 2.015625s user + 0.031250s system = 2.046875s CPU (99.5%)

RUN-1004 : used memory is 598 MB, reserved memory is 588 MB, peak memory is 721 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19443 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.085763s wall, 3.046875s user + 0.031250s system = 3.078125s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.60907e-05
PHY-3002 : Step(182): len = 500277, overlap = 349.25
PHY-3002 : Step(183): len = 498482, overlap = 355.5
PHY-3002 : Step(184): len = 498118, overlap = 371
PHY-3002 : Step(185): len = 496785, overlap = 394
PHY-3002 : Step(186): len = 493882, overlap = 399.75
PHY-3002 : Step(187): len = 492147, overlap = 409.5
PHY-3002 : Step(188): len = 488292, overlap = 406.25
PHY-3002 : Step(189): len = 485581, overlap = 405.25
PHY-3002 : Step(190): len = 483641, overlap = 412.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.21813e-05
PHY-3002 : Step(191): len = 487863, overlap = 393.75
PHY-3002 : Step(192): len = 491976, overlap = 385.75
PHY-3002 : Step(193): len = 493853, overlap = 383.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(194): len = 498800, overlap = 376
PHY-3002 : Step(195): len = 506709, overlap = 358.75
PHY-3002 : Step(196): len = 507329, overlap = 346.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.745103s wall, 0.781250s user + 0.875000s system = 1.656250s CPU (222.3%)

PHY-3001 : Trial Legalized: Len = 611222
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 545/19445.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 700360, over cnt = 2374(6%), over = 3866, worst = 7
PHY-1002 : len = 712912, over cnt = 1557(4%), over = 2272, worst = 6
PHY-1002 : len = 734208, over cnt = 511(1%), over = 694, worst = 6
PHY-1002 : len = 745384, over cnt = 57(0%), over = 77, worst = 5
PHY-1002 : len = 746672, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.057417s wall, 3.484375s user + 0.046875s system = 3.531250s CPU (171.6%)

PHY-1001 : Congestion index: top1 = 48.62, top5 = 44.34, top10 = 41.86, top15 = 40.09.
PHY-3001 : End congestion estimation;  2.458506s wall, 3.906250s user + 0.046875s system = 3.953125s CPU (160.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19443 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.325679s wall, 1.312500s user + 0.000000s system = 1.312500s CPU (99.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000180169
PHY-3002 : Step(197): len = 570051, overlap = 86.5
PHY-3002 : Step(198): len = 551374, overlap = 139.25
PHY-3002 : Step(199): len = 539762, overlap = 185.5
PHY-3002 : Step(200): len = 534302, overlap = 214.5
PHY-3002 : Step(201): len = 530975, overlap = 229.5
PHY-3002 : Step(202): len = 529151, overlap = 248.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000360339
PHY-3002 : Step(203): len = 533610, overlap = 244
PHY-3002 : Step(204): len = 537470, overlap = 241.5
PHY-3002 : Step(205): len = 537662, overlap = 241.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00066458
PHY-3002 : Step(206): len = 540263, overlap = 239
PHY-3002 : Step(207): len = 545514, overlap = 225.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.036386s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (128.8%)

PHY-3001 : Legalized: Len = 586048, Over = 0
PHY-3001 : Spreading special nets. 25 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.091257s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (102.7%)

PHY-3001 : 36 instances has been re-located, deltaX = 14, deltaY = 24, maxDist = 2.
PHY-3001 : Final: Len = 586884, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67846, tnet num: 19443, tinst num: 8093, tnode num: 91966, tedge num: 111939.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.281170s wall, 2.250000s user + 0.015625s system = 2.265625s CPU (99.3%)

RUN-1004 : used memory is 604 MB, reserved memory is 596 MB, peak memory is 721 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3566/19445.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 685312, over cnt = 2195(6%), over = 3410, worst = 7
PHY-1002 : len = 696144, over cnt = 1272(3%), over = 1769, worst = 6
PHY-1002 : len = 709544, over cnt = 570(1%), over = 757, worst = 6
PHY-1002 : len = 719416, over cnt = 130(0%), over = 158, worst = 4
PHY-1002 : len = 722072, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.859435s wall, 3.140625s user + 0.062500s system = 3.203125s CPU (172.3%)

PHY-1001 : Congestion index: top1 = 46.85, top5 = 42.40, top10 = 39.86, top15 = 38.29.
PHY-1001 : End incremental global routing;  2.212754s wall, 3.484375s user + 0.062500s system = 3.546875s CPU (160.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19443 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.099950s wall, 1.109375s user + 0.000000s system = 1.109375s CPU (100.9%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8028 has valid locations, 13 needs to be replaced
PHY-3001 : design contains 8104 instances, 8000 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 588658
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17486/19458.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 723344, over cnt = 20(0%), over = 20, worst = 1
PHY-1002 : len = 723400, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 723416, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 723432, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 723448, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.732339s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (100.3%)

PHY-1001 : Congestion index: top1 = 46.94, top5 = 42.43, top10 = 39.88, top15 = 38.33.
PHY-3001 : End congestion estimation;  1.072875s wall, 1.078125s user + 0.000000s system = 1.078125s CPU (100.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67922, tnet num: 19456, tinst num: 8104, tnode num: 92057, tedge num: 112028.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.252994s wall, 2.234375s user + 0.015625s system = 2.250000s CPU (99.9%)

RUN-1004 : used memory is 636 MB, reserved memory is 621 MB, peak memory is 721 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19456 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.316830s wall, 3.250000s user + 0.062500s system = 3.312500s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(208): len = 588541, overlap = 0.75
PHY-3002 : Step(209): len = 588498, overlap = 0.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17478/19458.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 723048, over cnt = 17(0%), over = 19, worst = 2
PHY-1002 : len = 723032, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 723112, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 723160, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.553672s wall, 0.625000s user + 0.031250s system = 0.656250s CPU (118.5%)

PHY-1001 : Congestion index: top1 = 46.88, top5 = 42.42, top10 = 39.89, top15 = 38.36.
PHY-3001 : End congestion estimation;  0.881650s wall, 0.953125s user + 0.031250s system = 0.984375s CPU (111.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19456 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.013088s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (98.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000546045
PHY-3002 : Step(210): len = 588511, overlap = 1.75
PHY-3002 : Step(211): len = 588522, overlap = 0.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007105s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 588558, Over = 0
PHY-3001 : End spreading;  0.075742s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (103.1%)

PHY-3001 : Final: Len = 588558, Over = 0
PHY-3001 : End incremental placement;  6.990483s wall, 7.281250s user + 0.171875s system = 7.453125s CPU (106.6%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.885590s wall, 12.453125s user + 0.234375s system = 12.687500s CPU (116.6%)

OPT-1001 : Current memory(MB): used = 714, reserve = 701, peak = 721.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17478/19458.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 723232, over cnt = 8(0%), over = 11, worst = 4
PHY-1002 : len = 723280, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 723288, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.398860s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (109.7%)

PHY-1001 : Congestion index: top1 = 46.90, top5 = 42.41, top10 = 39.86, top15 = 38.31.
OPT-1001 : End congestion update;  0.723779s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (105.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19456 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.967550s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (100.1%)

OPT-0007 : Start: WNS 3579 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.696467s wall, 1.734375s user + 0.000000s system = 1.734375s CPU (102.2%)

OPT-1001 : Current memory(MB): used = 714, reserve = 701, peak = 721.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19456 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.973318s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (97.9%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17498/19458.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 723288, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.124138s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (100.7%)

PHY-1001 : Congestion index: top1 = 46.90, top5 = 42.41, top10 = 39.86, top15 = 38.31.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19456 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.972592s wall, 0.984375s user + 0.000000s system = 0.984375s CPU (101.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3579 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 46.586207
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3579ps with logic level 4 
OPT-1001 : End physical optimization;  17.649245s wall, 19.265625s user + 0.265625s system = 19.531250s CPU (110.7%)

RUN-1003 : finish command "place" in  78.621442s wall, 144.250000s user + 8.062500s system = 152.312500s CPU (193.7%)

RUN-1004 : used memory is 631 MB, reserved memory is 619 MB, peak memory is 721 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.903948s wall, 3.265625s user + 0.062500s system = 3.328125s CPU (174.8%)

RUN-1004 : used memory is 631 MB, reserved memory is 620 MB, peak memory is 721 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8106 instances
RUN-1001 : 4001 mslices, 3999 lslices, 60 pads, 41 brams, 0 dsps
RUN-1001 : There are total 19458 nets
RUN-1001 : 13619 nets have 2 pins
RUN-1001 : 4418 nets have [3 - 5] pins
RUN-1001 : 890 nets have [6 - 10] pins
RUN-1001 : 391 nets have [11 - 20] pins
RUN-1001 : 131 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67922, tnet num: 19456, tinst num: 8104, tnode num: 92057, tedge num: 112028.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.968089s wall, 1.953125s user + 0.000000s system = 1.953125s CPU (99.2%)

RUN-1004 : used memory is 614 MB, reserved memory is 596 MB, peak memory is 721 MB
PHY-1001 : 4001 mslices, 3999 lslices, 60 pads, 41 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19456 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 665464, over cnt = 2297(6%), over = 3778, worst = 6
PHY-1002 : len = 678632, over cnt = 1510(4%), over = 2240, worst = 6
PHY-1002 : len = 700168, over cnt = 478(1%), over = 610, worst = 5
PHY-1002 : len = 709872, over cnt = 11(0%), over = 14, worst = 2
PHY-1002 : len = 710128, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.037650s wall, 3.390625s user + 0.031250s system = 3.421875s CPU (167.9%)

PHY-1001 : Congestion index: top1 = 46.62, top5 = 42.05, top10 = 39.52, top15 = 37.86.
PHY-1001 : End global routing;  2.413557s wall, 3.750000s user + 0.031250s system = 3.781250s CPU (156.7%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 696, reserve = 687, peak = 721.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 966, reserve = 954, peak = 966.
PHY-1001 : End build detailed router design. 4.869318s wall, 4.781250s user + 0.062500s system = 4.843750s CPU (99.5%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 192920, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.983358s wall, 0.968750s user + 0.015625s system = 0.984375s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1001, reserve = 990, peak = 1001.
PHY-1001 : End phase 1; 0.991322s wall, 0.984375s user + 0.015625s system = 1.000000s CPU (100.9%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.70956e+06, over cnt = 1340(0%), over = 1347, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1016, reserve = 1004, peak = 1016.
PHY-1001 : End initial routed; 18.631208s wall, 49.218750s user + 0.500000s system = 49.718750s CPU (266.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18185(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.774   |   0.000   |   0   
RUN-1001 :   Hold   |   0.148   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.884987s wall, 3.890625s user + 0.000000s system = 3.890625s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1027, reserve = 1015, peak = 1027.
PHY-1001 : End phase 2; 22.516365s wall, 53.109375s user + 0.500000s system = 53.609375s CPU (238.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.70956e+06, over cnt = 1340(0%), over = 1347, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.263565s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (100.8%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.69864e+06, over cnt = 496(0%), over = 496, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.782269s wall, 1.359375s user + 0.000000s system = 1.359375s CPU (173.8%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.69782e+06, over cnt = 101(0%), over = 101, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.410619s wall, 0.578125s user + 0.000000s system = 0.578125s CPU (140.8%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.69937e+06, over cnt = 5(0%), over = 5, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.260418s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (120.0%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.6995e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 4; 0.185629s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (101.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18185(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.774   |   0.000   |   0   
RUN-1001 :   Hold   |   0.148   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.638795s wall, 3.640625s user + 0.000000s system = 3.640625s CPU (100.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 327 feed throughs used by 267 nets
PHY-1001 : End commit to database; 2.328084s wall, 2.312500s user + 0.015625s system = 2.328125s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1116, reserve = 1107, peak = 1116.
PHY-1001 : End phase 3; 8.408995s wall, 9.171875s user + 0.015625s system = 9.187500s CPU (109.3%)

PHY-1003 : Routed, final wirelength = 1.6995e+06
PHY-1001 : Current memory(MB): used = 1121, reserve = 1111, peak = 1121.
PHY-1001 : End export database. 0.180428s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (95.3%)

PHY-1001 : End detail routing;  37.414716s wall, 68.687500s user + 0.593750s system = 69.281250s CPU (185.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67922, tnet num: 19456, tinst num: 8104, tnode num: 92057, tedge num: 112028.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.784133s wall, 1.765625s user + 0.000000s system = 1.765625s CPU (99.0%)

RUN-1004 : used memory is 1056 MB, reserved memory is 1058 MB, peak memory is 1121 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  46.418544s wall, 78.953125s user + 0.656250s system = 79.609375s CPU (171.5%)

RUN-1004 : used memory is 1055 MB, reserved memory is 1057 MB, peak memory is 1121 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8873   out of  19600   45.27%
#reg                    12444   out of  19600   63.49%
#le                     14998
  #lut only              2554   out of  14998   17.03%
  #reg only              6125   out of  14998   40.84%
  #lut&reg               6319   out of  14998   42.13%
#dsp                        0   out of     29    0.00%
#bram                      41   out of     64   64.06%
  #bram9k                  39
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6782
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          162
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         M3        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         P6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |14998  |7371    |1502    |12488   |41      |0       |
|  AnyFog_dataX                      |AnyFog          |207    |91      |22      |169     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |85     |59      |22      |47      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |207    |76      |22      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |53      |22      |52      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |210    |73      |22      |176     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |88     |57      |22      |54      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2924   |653     |39      |2837    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |57     |35      |5       |49      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |212    |81      |5       |202     |0       |0       |
|    STADOP_com2                     |STADOP          |553    |124     |0       |547     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |64     |41      |14      |40      |0       |0       |
|    head_com2                       |uniheading      |269    |57      |5       |255     |0       |0       |
|    rmc_com2                        |Gprmc           |37     |36      |0       |30      |0       |0       |
|    uart_com2                       |Agrica          |1419   |243     |10      |1401    |0       |0       |
|  COM3                              |COM3_Control    |281    |144     |19      |237     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |59     |35      |5       |51      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |60     |45      |14      |37      |0       |0       |
|    rmc_com3                        |Gprmc           |162    |64      |0       |149     |0       |0       |
|  DATA                              |Data_Processing |8843   |4621    |1122    |7053    |0       |0       |
|    DIV_Dtemp                       |Divider         |805    |381     |84      |681     |0       |0       |
|    DIV_Utemp                       |Divider         |623    |300     |84      |499     |0       |0       |
|    DIV_accX                        |Divider         |621    |306     |84      |499     |0       |0       |
|    DIV_accY                        |Divider         |570    |306     |102     |416     |0       |0       |
|    DIV_accZ                        |Divider         |649    |390     |132     |439     |0       |0       |
|    DIV_rateX                       |Divider         |717    |379     |132     |509     |0       |0       |
|    DIV_rateY                       |Divider         |617    |382     |132     |413     |0       |0       |
|    DIV_rateZ                       |Divider         |546    |327     |132     |342     |0       |0       |
|    genclk                          |genclk          |262    |164     |89      |100     |0       |0       |
|  FMC                               |FMC_Ctrl        |407    |355     |43      |325     |0       |0       |
|  IIC                               |I2C_master      |303    |240     |11      |259     |0       |0       |
|  IMU_CTRL                          |SCHA634         |873    |703     |61      |725     |0       |0       |
|    CtrlData                        |CtrlData        |441    |392     |47      |330     |0       |0       |
|      usms                          |Time_1ms        |29     |24      |5       |21      |0       |0       |
|    SPIM                            |SPI_SCHA634     |432    |311     |14      |395     |0       |0       |
|  POWER                             |POWER_EN        |101    |48      |38      |40      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |624    |365     |103     |432     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |624    |365     |103     |432     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |287    |140     |0       |270     |0       |0       |
|        reg_inst                    |register        |285    |138     |0       |268     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |337    |225     |103     |162     |0       |0       |
|        bus_inst                    |bus_top         |133    |87      |46      |53      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |26     |16      |10      |10      |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |53     |35      |18      |21      |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[5]$bus_nodes |bus_det         |52     |34      |18      |20      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |130    |99      |29      |82      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13558  
    #2          2       3428   
    #3          3        686   
    #4          4        304   
    #5        5-10       962   
    #6        11-50      442   
    #7       51-100       8    
    #8       101-500      3    
    #9        >500        2    
  Average     2.14             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.170561s wall, 3.703125s user + 0.046875s system = 3.750000s CPU (172.8%)

RUN-1004 : used memory is 1056 MB, reserved memory is 1058 MB, peak memory is 1121 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67922, tnet num: 19456, tinst num: 8104, tnode num: 92057, tedge num: 112028.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.823652s wall, 1.828125s user + 0.000000s system = 1.828125s CPU (100.2%)

RUN-1004 : used memory is 1057 MB, reserved memory is 1060 MB, peak memory is 1121 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19456 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.496132s wall, 1.484375s user + 0.015625s system = 1.500000s CPU (100.3%)

RUN-1004 : used memory is 1062 MB, reserved memory is 1063 MB, peak memory is 1121 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: e7acc41f3f3c0493db2e7c96172f62b19e1071d28814331b86e973fbb54fbf81 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8104
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19458, pip num: 146740
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 327
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3260 valid insts, and 412114 bits set as '1'.
BIT-1004 : the usercode register value: 00000000000000110011101000100101
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.950008s wall, 128.375000s user + 0.218750s system = 128.593750s CPU (993.0%)

RUN-1004 : used memory is 1184 MB, reserved memory is 1170 MB, peak memory is 1299 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250821_144904.log"

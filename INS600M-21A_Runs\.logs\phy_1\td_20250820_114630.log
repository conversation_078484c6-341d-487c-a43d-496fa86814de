============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Wed Aug 20 11:46:30 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.314888s wall, 1.500000s user + 3.812500s system = 5.312500s CPU (100.0%)

RUN-1004 : used memory is 78 MB, reserved memory is 40 MB, peak memory is 90 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.779267s wall, 1.734375s user + 0.046875s system = 1.781250s CPU (100.1%)

RUN-1004 : used memory is 301 MB, reserved memory is 270 MB, peak memory is 305 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 2 view nodes, 24 trigger nets, 24 data nets.
KIT-1004 : Chipwatcher code = 1010010110001101
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=78) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=78) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=78)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=78)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22393/12 useful/useless nets, 19352/4 useful/useless insts
SYN-1016 : Merged 17 instances.
SYN-1032 : 22169/16 useful/useless nets, 19676/12 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 2 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 2 mux instances.
SYN-1015 : Optimize round 1, 279 better
SYN-1014 : Optimize round 2
SYN-1032 : 21960/30 useful/useless nets, 19467/32 useful/useless insts
SYN-1015 : Optimize round 2, 64 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.012900s wall, 1.953125s user + 0.062500s system = 2.015625s CPU (100.1%)

RUN-1004 : used memory is 325 MB, reserved memory is 293 MB, peak memory is 327 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 21984/155 useful/useless nets, 19512/29 useful/useless insts
SYN-1016 : Merged 21 instances.
SYN-2571 : Optimize after map_dsp, round 1, 205 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 24 instances.
SYN-2501 : Optimize round 1, 50 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 14 macro adder
SYN-1019 : Optimized 9 mux instances.
SYN-1016 : Merged 10 instances.
SYN-1032 : 22355/5 useful/useless nets, 19883/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81086, tnet num: 22355, tinst num: 19882, tnode num: 113757, tedge num: 126842.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.125845s wall, 1.125000s user + 0.015625s system = 1.140625s CPU (101.3%)

RUN-1004 : used memory is 463 MB, reserved memory is 432 MB, peak memory is 463 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22355 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 173 (3.57), #lev = 7 (1.79)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 173 (3.57), #lev = 7 (1.79)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 387 instances into 173 LUTs, name keeping = 77%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 290 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 108 adder to BLE ...
SYN-4008 : Packed 108 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.024623s wall, 3.968750s user + 0.062500s system = 4.031250s CPU (100.2%)

RUN-1004 : used memory is 348 MB, reserved memory is 314 MB, peak memory is 571 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  6.329474s wall, 6.156250s user + 0.156250s system = 6.312500s CPU (99.7%)

RUN-1004 : used memory is 349 MB, reserved memory is 315 MB, peak memory is 571 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (177 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19231 instances
RUN-0007 : 5565 luts, 12091 seqs, 973 mslices, 515 lslices, 58 pads, 24 brams, 0 dsps
RUN-1001 : There are total 21711 nets
RUN-1001 : 16361 nets have 2 pins
RUN-1001 : 4173 nets have [3 - 5] pins
RUN-1001 : 815 nets have [6 - 10] pins
RUN-1001 : 237 nets have [11 - 20] pins
RUN-1001 : 107 nets have [21 - 99] pins
RUN-1001 : 18 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4792     
RUN-1001 :   No   |  No   |  Yes  |     681     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     349     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19229 instances, 5565 luts, 12091 seqs, 1488 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 79729, tnet num: 21709, tinst num: 19229, tnode num: 112074, tedge num: 125346.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.204887s wall, 1.187500s user + 0.015625s system = 1.203125s CPU (99.9%)

RUN-1004 : used memory is 522 MB, reserved memory is 495 MB, peak memory is 571 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21709 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.030599s wall, 1.968750s user + 0.046875s system = 2.015625s CPU (99.3%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.58741e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19229.
PHY-3001 : Level 1 #clusters 2133.
PHY-3001 : End clustering;  0.134773s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (150.7%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 876651, overlap = 584.75
PHY-3002 : Step(2): len = 803114, overlap = 666
PHY-3002 : Step(3): len = 526418, overlap = 805.375
PHY-3002 : Step(4): len = 455300, overlap = 881.125
PHY-3002 : Step(5): len = 367132, overlap = 979.5
PHY-3002 : Step(6): len = 326060, overlap = 1051.56
PHY-3002 : Step(7): len = 268560, overlap = 1150.31
PHY-3002 : Step(8): len = 243422, overlap = 1240.38
PHY-3002 : Step(9): len = 216630, overlap = 1269.94
PHY-3002 : Step(10): len = 196354, overlap = 1307.72
PHY-3002 : Step(11): len = 175803, overlap = 1349.62
PHY-3002 : Step(12): len = 160139, overlap = 1362.06
PHY-3002 : Step(13): len = 149585, overlap = 1370.62
PHY-3002 : Step(14): len = 138587, overlap = 1437.44
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.10545e-06
PHY-3002 : Step(15): len = 140458, overlap = 1406.69
PHY-3002 : Step(16): len = 183998, overlap = 1258.31
PHY-3002 : Step(17): len = 195132, overlap = 1211.22
PHY-3002 : Step(18): len = 196572, overlap = 1147.19
PHY-3002 : Step(19): len = 194099, overlap = 1120.16
PHY-3002 : Step(20): len = 188241, overlap = 1115.94
PHY-3002 : Step(21): len = 184248, overlap = 1096.38
PHY-3002 : Step(22): len = 179089, overlap = 1092.69
PHY-3002 : Step(23): len = 174054, overlap = 1103.91
PHY-3002 : Step(24): len = 170895, overlap = 1096.91
PHY-3002 : Step(25): len = 167077, overlap = 1101.03
PHY-3002 : Step(26): len = 164022, overlap = 1093.84
PHY-3002 : Step(27): len = 161473, overlap = 1096.75
PHY-3002 : Step(28): len = 160804, overlap = 1088.19
PHY-3002 : Step(29): len = 158963, overlap = 1086.12
PHY-3002 : Step(30): len = 157972, overlap = 1076.84
PHY-3002 : Step(31): len = 156987, overlap = 1081.19
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.21089e-06
PHY-3002 : Step(32): len = 164015, overlap = 1057.62
PHY-3002 : Step(33): len = 180401, overlap = 962.781
PHY-3002 : Step(34): len = 184726, overlap = 939.906
PHY-3002 : Step(35): len = 186020, overlap = 924.969
PHY-3002 : Step(36): len = 185607, overlap = 938.344
PHY-3002 : Step(37): len = 184985, overlap = 935.188
PHY-3002 : Step(38): len = 184036, overlap = 942.312
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.42178e-06
PHY-3002 : Step(39): len = 194473, overlap = 915.125
PHY-3002 : Step(40): len = 210856, overlap = 832.094
PHY-3002 : Step(41): len = 216952, overlap = 817.094
PHY-3002 : Step(42): len = 220236, overlap = 803.938
PHY-3002 : Step(43): len = 219307, overlap = 778.281
PHY-3002 : Step(44): len = 217898, overlap = 765.781
PHY-3002 : Step(45): len = 215293, overlap = 759.594
PHY-3002 : Step(46): len = 214160, overlap = 757.531
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 8.84357e-06
PHY-3002 : Step(47): len = 227215, overlap = 693.5
PHY-3002 : Step(48): len = 242660, overlap = 631.812
PHY-3002 : Step(49): len = 247123, overlap = 583.656
PHY-3002 : Step(50): len = 250099, overlap = 572.531
PHY-3002 : Step(51): len = 249432, overlap = 578.125
PHY-3002 : Step(52): len = 247379, overlap = 595.562
PHY-3002 : Step(53): len = 245207, overlap = 600.094
PHY-3002 : Step(54): len = 243848, overlap = 606.469
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.76871e-05
PHY-3002 : Step(55): len = 258089, overlap = 525.375
PHY-3002 : Step(56): len = 271528, overlap = 459.062
PHY-3002 : Step(57): len = 276393, overlap = 386
PHY-3002 : Step(58): len = 279249, overlap = 349.531
PHY-3002 : Step(59): len = 277299, overlap = 346.656
PHY-3002 : Step(60): len = 275432, overlap = 363.312
PHY-3002 : Step(61): len = 273944, overlap = 358.094
PHY-3002 : Step(62): len = 272791, overlap = 354.594
PHY-3002 : Step(63): len = 272222, overlap = 348.938
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.53743e-05
PHY-3002 : Step(64): len = 284707, overlap = 343.812
PHY-3002 : Step(65): len = 295960, overlap = 328.688
PHY-3002 : Step(66): len = 297803, overlap = 298.438
PHY-3002 : Step(67): len = 299345, overlap = 288.25
PHY-3002 : Step(68): len = 298754, overlap = 271.844
PHY-3002 : Step(69): len = 297273, overlap = 286.531
PHY-3002 : Step(70): len = 295314, overlap = 293.75
PHY-3002 : Step(71): len = 294896, overlap = 282.188
PHY-3002 : Step(72): len = 294097, overlap = 270.531
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 7.07486e-05
PHY-3002 : Step(73): len = 301344, overlap = 280.188
PHY-3002 : Step(74): len = 308737, overlap = 259.906
PHY-3002 : Step(75): len = 311473, overlap = 239.938
PHY-3002 : Step(76): len = 312897, overlap = 231.312
PHY-3002 : Step(77): len = 312148, overlap = 224.781
PHY-3002 : Step(78): len = 311962, overlap = 217.031
PHY-3002 : Step(79): len = 311676, overlap = 223.469
PHY-3002 : Step(80): len = 311292, overlap = 228.969
PHY-3002 : Step(81): len = 310421, overlap = 244.469
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000141497
PHY-3002 : Step(82): len = 316447, overlap = 219.688
PHY-3002 : Step(83): len = 322354, overlap = 213.719
PHY-3002 : Step(84): len = 324177, overlap = 223.375
PHY-3002 : Step(85): len = 324389, overlap = 231.062
PHY-3002 : Step(86): len = 323642, overlap = 227.375
PHY-3002 : Step(87): len = 323802, overlap = 233.75
PHY-3002 : Step(88): len = 322917, overlap = 245.75
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.00026377
PHY-3002 : Step(89): len = 326052, overlap = 225.406
PHY-3002 : Step(90): len = 330090, overlap = 202.812
PHY-3002 : Step(91): len = 331110, overlap = 190.906
PHY-3002 : Step(92): len = 332546, overlap = 186.219
PHY-3002 : Step(93): len = 332253, overlap = 188.375
PHY-3002 : Step(94): len = 332233, overlap = 191.469
PHY-3002 : Step(95): len = 331199, overlap = 189.906
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.00042678
PHY-3002 : Step(96): len = 332741, overlap = 189.969
PHY-3002 : Step(97): len = 335708, overlap = 180.656
PHY-3002 : Step(98): len = 337713, overlap = 154.188
PHY-3002 : Step(99): len = 340595, overlap = 140.344
PHY-3002 : Step(100): len = 341401, overlap = 122.062
PHY-3002 : Step(101): len = 341385, overlap = 123.156
PHY-3002 : Step(102): len = 341502, overlap = 115.5
PHY-3002 : Step(103): len = 341041, overlap = 123.438
PHY-3002 : Step(104): len = 340813, overlap = 116.188
PHY-3001 : :::11::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(105): len = 341922, overlap = 121.688
PHY-3002 : Step(106): len = 343605, overlap = 120.656
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.013329s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (117.2%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21711.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 442552, over cnt = 1180(3%), over = 5126, worst = 36
PHY-1001 : End global iterations;  0.762892s wall, 0.984375s user + 0.015625s system = 1.000000s CPU (131.1%)

PHY-1001 : Congestion index: top1 = 70.52, top5 = 52.16, top10 = 43.15, top15 = 37.57.
PHY-3001 : End congestion estimation;  0.996880s wall, 1.218750s user + 0.015625s system = 1.234375s CPU (123.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21709 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.836217s wall, 0.750000s user + 0.078125s system = 0.828125s CPU (99.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.60553e-05
PHY-3002 : Step(107): len = 385519, overlap = 114.281
PHY-3002 : Step(108): len = 397342, overlap = 101.406
PHY-3002 : Step(109): len = 395387, overlap = 95.0312
PHY-3002 : Step(110): len = 395530, overlap = 96.0938
PHY-3002 : Step(111): len = 400998, overlap = 81.4688
PHY-3002 : Step(112): len = 405460, overlap = 80.6875
PHY-3002 : Step(113): len = 410487, overlap = 86.7188
PHY-3002 : Step(114): len = 412769, overlap = 90.5938
PHY-3002 : Step(115): len = 415944, overlap = 91.3125
PHY-3002 : Step(116): len = 420877, overlap = 100.062
PHY-3002 : Step(117): len = 420345, overlap = 104.562
PHY-3002 : Step(118): len = 419894, overlap = 101.062
PHY-3002 : Step(119): len = 420038, overlap = 104.469
PHY-3002 : Step(120): len = 419125, overlap = 99.5
PHY-3002 : Step(121): len = 419530, overlap = 100.375
PHY-3002 : Step(122): len = 419361, overlap = 93.25
PHY-3002 : Step(123): len = 419797, overlap = 92.1562
PHY-3002 : Step(124): len = 419260, overlap = 96
PHY-3002 : Step(125): len = 419403, overlap = 96.8438
PHY-3002 : Step(126): len = 420006, overlap = 103.688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000192111
PHY-3002 : Step(127): len = 419594, overlap = 101.469
PHY-3002 : Step(128): len = 421974, overlap = 98.2188
PHY-3002 : Step(129): len = 424017, overlap = 94.9688
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(130): len = 427955, overlap = 90
PHY-3002 : Step(131): len = 439485, overlap = 89.3438
PHY-3002 : Step(132): len = 447021, overlap = 90.2188
PHY-3002 : Step(133): len = 444645, overlap = 93.0938
PHY-3002 : Step(134): len = 445281, overlap = 100.625
PHY-3002 : Step(135): len = 445736, overlap = 99.4062
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 105/21711.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 512488, over cnt = 2123(6%), over = 9356, worst = 40
PHY-1001 : End global iterations;  1.097176s wall, 1.578125s user + 0.000000s system = 1.578125s CPU (143.8%)

PHY-1001 : Congestion index: top1 = 70.11, top5 = 54.88, top10 = 47.43, top15 = 42.97.
PHY-3001 : End congestion estimation;  1.409835s wall, 1.890625s user + 0.000000s system = 1.890625s CPU (134.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21709 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.840033s wall, 0.812500s user + 0.015625s system = 0.828125s CPU (98.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000109004
PHY-3002 : Step(136): len = 449834, overlap = 331.719
PHY-3002 : Step(137): len = 453731, overlap = 275.594
PHY-3002 : Step(138): len = 447979, overlap = 254.969
PHY-3002 : Step(139): len = 445234, overlap = 244.25
PHY-3002 : Step(140): len = 443164, overlap = 237.812
PHY-3002 : Step(141): len = 440819, overlap = 237.125
PHY-3002 : Step(142): len = 438748, overlap = 223.969
PHY-3002 : Step(143): len = 438450, overlap = 220.75
PHY-3002 : Step(144): len = 436364, overlap = 213.594
PHY-3002 : Step(145): len = 435534, overlap = 217.844
PHY-3002 : Step(146): len = 435467, overlap = 217.562
PHY-3002 : Step(147): len = 433290, overlap = 209.406
PHY-3002 : Step(148): len = 432741, overlap = 207.812
PHY-3002 : Step(149): len = 431628, overlap = 209.312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000218007
PHY-3002 : Step(150): len = 432366, overlap = 200.281
PHY-3002 : Step(151): len = 434602, overlap = 190.688
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000431948
PHY-3002 : Step(152): len = 437300, overlap = 182.562
PHY-3002 : Step(153): len = 446194, overlap = 166.656
PHY-3002 : Step(154): len = 450081, overlap = 159.875
PHY-3002 : Step(155): len = 449049, overlap = 163.844
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000863896
PHY-3002 : Step(156): len = 450126, overlap = 160.469
PHY-3002 : Step(157): len = 455102, overlap = 147.719
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 79729, tnet num: 21709, tinst num: 19229, tnode num: 112074, tedge num: 125346.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.421003s wall, 1.421875s user + 0.000000s system = 1.421875s CPU (100.1%)

RUN-1004 : used memory is 562 MB, reserved memory is 537 MB, peak memory is 694 MB
OPT-1001 : Total overflow 495.97 peak overflow 3.78
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 547/21711.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 531328, over cnt = 2462(6%), over = 8837, worst = 26
PHY-1001 : End global iterations;  1.265941s wall, 1.937500s user + 0.000000s system = 1.937500s CPU (153.0%)

PHY-1001 : Congestion index: top1 = 56.40, top5 = 47.70, top10 = 43.03, top15 = 39.77.
PHY-1001 : End incremental global routing;  1.498885s wall, 2.171875s user + 0.000000s system = 2.171875s CPU (144.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21709 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.958389s wall, 0.937500s user + 0.015625s system = 0.953125s CPU (99.5%)

OPT-1001 : 17 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 58 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19151 has valid locations, 220 needs to be replaced
PHY-3001 : design contains 19432 instances, 5652 luts, 12207 seqs, 1488 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 470475
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16929/21914.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 543144, over cnt = 2510(7%), over = 8933, worst = 26
PHY-1001 : End global iterations;  0.170670s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (119.0%)

PHY-1001 : Congestion index: top1 = 56.62, top5 = 47.91, top10 = 43.28, top15 = 40.07.
PHY-3001 : End congestion estimation;  0.398387s wall, 0.406250s user + 0.015625s system = 0.421875s CPU (105.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80388, tnet num: 21912, tinst num: 19432, tnode num: 112999, tedge num: 126258.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.445822s wall, 1.328125s user + 0.093750s system = 1.421875s CPU (98.3%)

RUN-1004 : used memory is 606 MB, reserved memory is 603 MB, peak memory is 697 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21912 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.419781s wall, 2.281250s user + 0.125000s system = 2.406250s CPU (99.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(158): len = 470675, overlap = 0.8125
PHY-3002 : Step(159): len = 471608, overlap = 0.9375
PHY-3002 : Step(160): len = 472396, overlap = 0.875
PHY-3002 : Step(161): len = 473803, overlap = 0.9375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 16960/21914.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 542768, over cnt = 2499(7%), over = 8908, worst = 26
PHY-1001 : End global iterations;  0.202226s wall, 0.281250s user + 0.031250s system = 0.312500s CPU (154.5%)

PHY-1001 : Congestion index: top1 = 56.72, top5 = 48.11, top10 = 43.52, top15 = 40.26.
PHY-3001 : End congestion estimation;  0.430402s wall, 0.500000s user + 0.046875s system = 0.546875s CPU (127.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21912 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.932956s wall, 0.890625s user + 0.031250s system = 0.921875s CPU (98.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000659009
PHY-3002 : Step(162): len = 473350, overlap = 149.719
PHY-3002 : Step(163): len = 473310, overlap = 149.469
PHY-3002 : Step(164): len = 473424, overlap = 149.281
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00131802
PHY-3002 : Step(165): len = 473507, overlap = 149.469
PHY-3002 : Step(166): len = 473899, overlap = 149.625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00263603
PHY-3002 : Step(167): len = 473946, overlap = 149.406
PHY-3002 : Step(168): len = 474080, overlap = 149.5
PHY-3001 : Final: Len = 474080, Over = 149.5
PHY-3001 : End incremental placement;  5.229289s wall, 5.250000s user + 0.421875s system = 5.671875s CPU (108.5%)

OPT-1001 : Total overflow 499.78 peak overflow 3.78
OPT-1001 : End high-fanout net optimization;  8.176941s wall, 9.015625s user + 0.437500s system = 9.453125s CPU (115.6%)

OPT-1001 : Current memory(MB): used = 699, reserve = 679, peak = 715.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16946/21914.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 543776, over cnt = 2441(6%), over = 8368, worst = 26
PHY-1002 : len = 580400, over cnt = 1914(5%), over = 5040, worst = 20
PHY-1002 : len = 619032, over cnt = 1029(2%), over = 2375, worst = 14
PHY-1002 : len = 644712, over cnt = 321(0%), over = 732, worst = 11
PHY-1002 : len = 657824, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.167374s wall, 1.687500s user + 0.015625s system = 1.703125s CPU (145.9%)

PHY-1001 : Congestion index: top1 = 48.62, top5 = 43.75, top10 = 40.82, top15 = 38.88.
OPT-1001 : End congestion update;  1.447352s wall, 1.984375s user + 0.015625s system = 2.000000s CPU (138.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 21912 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.791771s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (98.7%)

OPT-0007 : Start: WNS 3603 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.245609s wall, 2.781250s user + 0.015625s system = 2.796875s CPU (124.5%)

OPT-1001 : Current memory(MB): used = 696, reserve = 676, peak = 715.
OPT-1001 : End physical optimization;  12.127687s wall, 13.562500s user + 0.468750s system = 14.031250s CPU (115.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5652 LUT to BLE ...
SYN-4008 : Packed 5652 LUT and 2730 SEQ to BLE.
SYN-4003 : Packing 9477 remaining SEQ's ...
SYN-4005 : Packed 3310 SEQ with LUT/SLICE
SYN-4006 : 137 single LUT's are left
SYN-4006 : 6167 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11819/13658 primitive instances ...
PHY-3001 : End packing;  2.639421s wall, 2.625000s user + 0.000000s system = 2.625000s CPU (99.5%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8068 instances
RUN-1001 : 3990 mslices, 3991 lslices, 58 pads, 24 brams, 0 dsps
RUN-1001 : There are total 19236 nets
RUN-1001 : 13547 nets have 2 pins
RUN-1001 : 4294 nets have [3 - 5] pins
RUN-1001 : 878 nets have [6 - 10] pins
RUN-1001 : 377 nets have [11 - 20] pins
RUN-1001 : 131 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8066 instances, 7981 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 491936, Over = 370
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7575/19236.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 618024, over cnt = 1514(4%), over = 2464, worst = 9
PHY-1002 : len = 623160, over cnt = 1022(2%), over = 1502, worst = 7
PHY-1002 : len = 633472, over cnt = 494(1%), over = 698, worst = 7
PHY-1002 : len = 640896, over cnt = 228(0%), over = 320, worst = 7
PHY-1002 : len = 645000, over cnt = 68(0%), over = 98, worst = 4
PHY-1001 : End global iterations;  1.222854s wall, 1.859375s user + 0.015625s system = 1.875000s CPU (153.3%)

PHY-1001 : Congestion index: top1 = 49.70, top5 = 43.74, top10 = 40.24, top15 = 38.00.
PHY-3001 : End congestion estimation;  1.592640s wall, 2.218750s user + 0.015625s system = 2.234375s CPU (140.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66725, tnet num: 19234, tinst num: 8066, tnode num: 90533, tedge num: 109996.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.649206s wall, 1.656250s user + 0.000000s system = 1.656250s CPU (100.4%)

RUN-1004 : used memory is 602 MB, reserved memory is 598 MB, peak memory is 715 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19234 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.469183s wall, 2.437500s user + 0.015625s system = 2.453125s CPU (99.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.19135e-05
PHY-3002 : Step(169): len = 498087, overlap = 345.75
PHY-3002 : Step(170): len = 499433, overlap = 348.25
PHY-3002 : Step(171): len = 499648, overlap = 368.25
PHY-3002 : Step(172): len = 500680, overlap = 387.25
PHY-3002 : Step(173): len = 499172, overlap = 388
PHY-3002 : Step(174): len = 497895, overlap = 395.5
PHY-3002 : Step(175): len = 496290, overlap = 399.25
PHY-3002 : Step(176): len = 494725, overlap = 393
PHY-3002 : Step(177): len = 493049, overlap = 392
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000103827
PHY-3002 : Step(178): len = 497502, overlap = 381
PHY-3002 : Step(179): len = 502272, overlap = 377.75
PHY-3002 : Step(180): len = 502420, overlap = 373.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000207654
PHY-3002 : Step(181): len = 510817, overlap = 357.25
PHY-3002 : Step(182): len = 521160, overlap = 340.5
PHY-3002 : Step(183): len = 519143, overlap = 335
PHY-3002 : Step(184): len = 517666, overlap = 332.75
PHY-3002 : Step(185): len = 517934, overlap = 326.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.594017s wall, 0.687500s user + 0.687500s system = 1.375000s CPU (231.5%)

PHY-3001 : Trial Legalized: Len = 629850
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 590/19236.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 711880, over cnt = 2303(6%), over = 3713, worst = 8
PHY-1002 : len = 725520, over cnt = 1493(4%), over = 2090, worst = 8
PHY-1002 : len = 745168, over cnt = 435(1%), over = 570, worst = 6
PHY-1002 : len = 751424, over cnt = 152(0%), over = 205, worst = 4
PHY-1002 : len = 755800, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.803609s wall, 2.921875s user + 0.093750s system = 3.015625s CPU (167.2%)

PHY-1001 : Congestion index: top1 = 50.24, top5 = 45.59, top10 = 42.81, top15 = 40.95.
PHY-3001 : End congestion estimation;  2.136329s wall, 3.265625s user + 0.093750s system = 3.359375s CPU (157.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19234 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.820135s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (99.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000197823
PHY-3002 : Step(186): len = 588673, overlap = 78.75
PHY-3002 : Step(187): len = 570158, overlap = 120.75
PHY-3002 : Step(188): len = 557687, overlap = 169
PHY-3002 : Step(189): len = 551001, overlap = 199.5
PHY-3002 : Step(190): len = 546785, overlap = 225.25
PHY-3002 : Step(191): len = 544193, overlap = 240.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000395647
PHY-3002 : Step(192): len = 548326, overlap = 240
PHY-3002 : Step(193): len = 552700, overlap = 233.25
PHY-3002 : Step(194): len = 553818, overlap = 231.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000749616
PHY-3002 : Step(195): len = 556277, overlap = 228
PHY-3002 : Step(196): len = 564251, overlap = 225.25
PHY-3002 : Step(197): len = 573133, overlap = 231
PHY-3002 : Step(198): len = 571368, overlap = 228.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.031225s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (100.1%)

PHY-3001 : Legalized: Len = 610258, Over = 0
PHY-3001 : Spreading special nets. 35 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.073627s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (84.9%)

PHY-3001 : 57 instances has been re-located, deltaX = 24, deltaY = 27, maxDist = 1.
PHY-3001 : Final: Len = 611182, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66725, tnet num: 19234, tinst num: 8066, tnode num: 90533, tedge num: 109996.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.840955s wall, 1.828125s user + 0.015625s system = 1.843750s CPU (100.2%)

RUN-1004 : used memory is 608 MB, reserved memory is 607 MB, peak memory is 715 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3599/19236.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 702344, over cnt = 2106(5%), over = 3298, worst = 6
PHY-1002 : len = 713176, over cnt = 1271(3%), over = 1785, worst = 6
PHY-1002 : len = 728176, over cnt = 457(1%), over = 631, worst = 6
PHY-1002 : len = 737592, over cnt = 61(0%), over = 79, worst = 3
PHY-1002 : len = 739128, over cnt = 4(0%), over = 4, worst = 1
PHY-1001 : End global iterations;  1.601043s wall, 2.500000s user + 0.015625s system = 2.515625s CPU (157.1%)

PHY-1001 : Congestion index: top1 = 48.30, top5 = 43.69, top10 = 41.06, top15 = 39.29.
PHY-1001 : End incremental global routing;  1.879957s wall, 2.765625s user + 0.015625s system = 2.781250s CPU (147.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19234 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.825069s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (96.6%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 58 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8004 has valid locations, 8 needs to be replaced
PHY-3001 : design contains 8073 instances, 7988 slices, 289 macros(1488 instances: 973 mslices 515 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 614159
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17240/19249.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 743000, over cnt = 29(0%), over = 31, worst = 3
PHY-1002 : len = 742984, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 743048, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 743080, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 743096, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.912280s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (104.5%)

PHY-1001 : Congestion index: top1 = 48.41, top5 = 43.78, top10 = 41.17, top15 = 39.40.
PHY-3001 : End congestion estimation;  1.188358s wall, 1.218750s user + 0.000000s system = 1.218750s CPU (102.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66787, tnet num: 19247, tinst num: 8073, tnode num: 90609, tedge num: 110092.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.841369s wall, 1.828125s user + 0.000000s system = 1.828125s CPU (99.3%)

RUN-1004 : used memory is 638 MB, reserved memory is 624 MB, peak memory is 715 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19247 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.686335s wall, 2.671875s user + 0.000000s system = 2.671875s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(199): len = 613958, overlap = 0.75
PHY-3002 : Step(200): len = 613856, overlap = 0.75
PHY-3002 : Step(201): len = 613825, overlap = 1
PHY-3002 : Step(202): len = 613665, overlap = 1.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17237/19249.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 741160, over cnt = 17(0%), over = 21, worst = 3
PHY-1002 : len = 741064, over cnt = 10(0%), over = 11, worst = 2
PHY-1002 : len = 741208, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 741224, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 741256, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.625119s wall, 0.656250s user + 0.000000s system = 0.656250s CPU (105.0%)

PHY-1001 : Congestion index: top1 = 48.45, top5 = 43.78, top10 = 41.14, top15 = 39.39.
PHY-3001 : End congestion estimation;  0.894825s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (103.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19247 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.809793s wall, 0.796875s user + 0.015625s system = 0.812500s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00246579
PHY-3002 : Step(203): len = 613837, overlap = 1.5
PHY-3002 : Step(204): len = 613714, overlap = 1.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005814s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 613856, Over = 0
PHY-3001 : End spreading;  0.063171s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.9%)

PHY-3001 : Final: Len = 613856, Over = 0
PHY-3001 : End incremental placement;  6.298842s wall, 6.375000s user + 0.156250s system = 6.531250s CPU (103.7%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  9.450416s wall, 10.531250s user + 0.171875s system = 10.703125s CPU (113.3%)

OPT-1001 : Current memory(MB): used = 708, reserve = 694, peak = 715.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17237/19249.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 742000, over cnt = 15(0%), over = 17, worst = 3
PHY-1002 : len = 742016, over cnt = 12(0%), over = 12, worst = 1
PHY-1002 : len = 742088, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 742112, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 742128, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.641248s wall, 0.671875s user + 0.015625s system = 0.687500s CPU (107.2%)

PHY-1001 : Congestion index: top1 = 48.23, top5 = 43.74, top10 = 41.13, top15 = 39.39.
OPT-1001 : End congestion update;  0.909836s wall, 0.937500s user + 0.015625s system = 0.953125s CPU (104.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19247 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.682403s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (100.7%)

OPT-0007 : Start: WNS 3716 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.596391s wall, 1.625000s user + 0.015625s system = 1.640625s CPU (102.8%)

OPT-1001 : Current memory(MB): used = 708, reserve = 694, peak = 715.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19247 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.689511s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (99.7%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17248/19249.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 742128, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.107683s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (101.6%)

PHY-1001 : Congestion index: top1 = 48.23, top5 = 43.74, top10 = 41.13, top15 = 39.39.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19247 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.689087s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (99.8%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3716 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 47.655172
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3716ps with logic level 4 
RUN-1001 :       #2 path slack 3720ps with logic level 4 
OPT-1001 : End physical optimization;  14.884494s wall, 15.984375s user + 0.203125s system = 16.187500s CPU (108.8%)

RUN-1003 : finish command "place" in  68.705127s wall, 120.500000s user + 7.046875s system = 127.546875s CPU (185.6%)

RUN-1004 : used memory is 590 MB, reserved memory is 578 MB, peak memory is 715 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.588706s wall, 2.671875s user + 0.015625s system = 2.687500s CPU (169.2%)

RUN-1004 : used memory is 591 MB, reserved memory is 581 MB, peak memory is 715 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8075 instances
RUN-1001 : 3997 mslices, 3991 lslices, 58 pads, 24 brams, 0 dsps
RUN-1001 : There are total 19249 nets
RUN-1001 : 13554 nets have 2 pins
RUN-1001 : 4292 nets have [3 - 5] pins
RUN-1001 : 882 nets have [6 - 10] pins
RUN-1001 : 382 nets have [11 - 20] pins
RUN-1001 : 130 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66787, tnet num: 19247, tinst num: 8073, tnode num: 90609, tedge num: 110092.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.616432s wall, 1.609375s user + 0.000000s system = 1.609375s CPU (99.6%)

RUN-1004 : used memory is 587 MB, reserved memory is 568 MB, peak memory is 715 MB
PHY-1001 : 3997 mslices, 3991 lslices, 58 pads, 24 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19247 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 683616, over cnt = 2236(6%), over = 3744, worst = 8
PHY-1002 : len = 699048, over cnt = 1390(3%), over = 1986, worst = 7
PHY-1002 : len = 716400, over cnt = 526(1%), over = 688, worst = 7
PHY-1002 : len = 726952, over cnt = 16(0%), over = 18, worst = 3
PHY-1002 : len = 727400, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.640441s wall, 2.703125s user + 0.046875s system = 2.750000s CPU (167.6%)

PHY-1001 : Congestion index: top1 = 47.82, top5 = 42.99, top10 = 40.60, top15 = 38.96.
PHY-1001 : End global routing;  1.944947s wall, 3.000000s user + 0.046875s system = 3.046875s CPU (156.7%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 688, reserve = 682, peak = 715.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 959, reserve = 950, peak = 959.
PHY-1001 : End build detailed router design. 4.295179s wall, 4.234375s user + 0.062500s system = 4.296875s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 189176, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.795902s wall, 0.765625s user + 0.031250s system = 0.796875s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 995, reserve = 987, peak = 995.
PHY-1001 : End phase 1; 0.803799s wall, 0.781250s user + 0.031250s system = 0.812500s CPU (101.1%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 55% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.65152e+06, over cnt = 1294(0%), over = 1300, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1010, reserve = 998, peak = 1010.
PHY-1001 : End initial routed; 15.142396s wall, 45.734375s user + 0.515625s system = 46.250000s CPU (305.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17990(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.162   |   0.000   |   0   
RUN-1001 :   Hold   |   0.203   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.243692s wall, 3.218750s user + 0.015625s system = 3.234375s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 1023, reserve = 1013, peak = 1023.
PHY-1001 : End phase 2; 18.386236s wall, 48.953125s user + 0.531250s system = 49.484375s CPU (269.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.65152e+06, over cnt = 1294(0%), over = 1300, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.216044s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (101.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.64049e+06, over cnt = 397(0%), over = 397, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.744994s wall, 1.281250s user + 0.000000s system = 1.281250s CPU (172.0%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.63986e+06, over cnt = 69(0%), over = 69, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.430528s wall, 0.562500s user + 0.015625s system = 0.578125s CPU (134.3%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.6407e+06, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.189524s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (115.4%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.64092e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 4; 0.150134s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (93.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17990(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.162   |   0.000   |   0   
RUN-1001 :   Hold   |   0.203   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.180513s wall, 3.171875s user + 0.000000s system = 3.171875s CPU (99.7%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 282 feed throughs used by 240 nets
PHY-1001 : End commit to database; 2.099324s wall, 2.078125s user + 0.015625s system = 2.093750s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 1110, reserve = 1103, peak = 1110.
PHY-1001 : End phase 3; 7.502428s wall, 8.156250s user + 0.031250s system = 8.187500s CPU (109.1%)

PHY-1003 : Routed, final wirelength = 1.64092e+06
PHY-1001 : Current memory(MB): used = 1115, reserve = 1108, peak = 1115.
PHY-1001 : End export database. 0.170410s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (100.9%)

PHY-1001 : End detail routing;  31.560132s wall, 62.703125s user + 0.656250s system = 63.359375s CPU (200.8%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66787, tnet num: 19247, tinst num: 8073, tnode num: 90609, tedge num: 110092.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.648380s wall, 1.640625s user + 0.000000s system = 1.640625s CPU (99.5%)

RUN-1004 : used memory is 1048 MB, reserved memory is 1047 MB, peak memory is 1115 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  39.159012s wall, 71.328125s user + 0.718750s system = 72.046875s CPU (184.0%)

RUN-1004 : used memory is 1048 MB, reserved memory is 1047 MB, peak memory is 1115 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8779   out of  19600   44.79%
#reg                    12308   out of  19600   62.80%
#le                     14892
  #lut only              2584   out of  14892   17.35%
  #reg only              6113   out of  14892   41.05%
  #lut&reg               6195   out of  14892   41.60%
#dsp                        0   out of     29    0.00%
#bram                      24   out of     64   37.50%
  #bram9k                  22
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       58   out of    188   30.85%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6815
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          106
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         F2        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         D1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         B3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         C1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         A3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         A2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         B2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         B1        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F15        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDZ        INPUT        K15        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         P2        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A           N/A            IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         N4        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         M5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         N6        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         N5        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         P5        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M3        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         M4        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         H2        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         M1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         L1        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         N1        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         N3        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         M2        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         T4        LVCMOS33           8            NONE           OREG       
    Fog_ENZ        OUTPUT         T7        LVCMOS33           8            NONE           OREG       
    Fog_INT        OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT         C3        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT        B15        LVCMOS33           8            N/A            OREG       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        T10        LVCMOS33           8            N/A            OREG       
     cs_uno        OUTPUT        D12        LVCMOS33           8            N/A            OREG       
      mosi         OUTPUT        J14        LVCMOS33           8            N/A            OREG       
      sclk         OUTPUT         F6        LVCMOS33           8            N/A            OREG       
    I2C_SDA         INOUT         K2        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT         F5        LVCMOS25           8            N/A            NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |14892  |7291    |1488    |12352   |24      |0       |
|  AnyFog_dataX                      |AnyFog          |208    |85      |22      |170     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |55      |22      |48      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |209    |94      |22      |175     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |53      |22      |52      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |206    |108     |22      |172     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |58      |22      |52      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2890   |679     |39      |2805    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |61     |35      |5       |52      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |207    |84      |5       |198     |0       |0       |
|    STADOP_com2                     |STADOP          |557    |48      |0       |550     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |66     |41      |14      |44      |0       |0       |
|    head_com2                       |uniheading      |258    |105     |5       |247     |0       |0       |
|    rmc_com2                        |Gprmc           |36     |36      |0       |28      |0       |0       |
|    uart_com2                       |Agrica          |1386   |292     |10      |1367    |0       |0       |
|  COM3                              |COM3_Control    |281    |133     |19      |236     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |61     |40      |5       |51      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |61     |39      |14      |38      |0       |0       |
|    rmc_com3                        |Gprmc           |159    |54      |0       |147     |0       |0       |
|  DATA                              |Data_Processing |8835   |4504    |1122    |7044    |0       |0       |
|    DIV_Dtemp                       |Divider         |778    |337     |84      |649     |0       |0       |
|    DIV_Utemp                       |Divider         |600    |339     |84      |472     |0       |0       |
|    DIV_accX                        |Divider         |612    |308     |84      |479     |0       |0       |
|    DIV_accY                        |Divider         |621    |335     |102     |467     |0       |0       |
|    DIV_accZ                        |Divider         |659    |381     |132     |456     |0       |0       |
|    DIV_rateX                       |Divider         |657    |410     |132     |453     |0       |0       |
|    DIV_rateY                       |Divider         |647    |363     |132     |440     |0       |0       |
|    DIV_rateZ                       |Divider         |609    |351     |132     |401     |0       |0       |
|    genclk                          |genclk          |262    |163     |89      |100     |0       |0       |
|  FMC                               |FMC_Ctrl        |523    |475     |43      |382     |0       |0       |
|  IIC                               |I2C_master      |282    |226     |11      |256     |0       |0       |
|  IMU_CTRL                          |SCHA634         |892    |622     |61      |727     |0       |0       |
|    CtrlData                        |CtrlData        |467    |403     |47      |334     |0       |0       |
|      usms                          |Time_1ms        |30     |25      |5       |22      |0       |0       |
|    SPIM                            |SPI_SCHA634     |425    |219     |14      |393     |0       |0       |
|  POWER                             |POWER_EN        |98     |58      |38      |39      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |458    |307     |89      |292     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |458    |307     |89      |292     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |173    |126     |0       |161     |0       |0       |
|        reg_inst                    |register        |170    |123     |0       |158     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |285    |181     |89      |131     |0       |0       |
|        bus_inst                    |bus_top         |81     |53      |28      |33      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |28     |18      |10      |12      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |53     |35      |18      |21      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |121    |83      |29      |70      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13495  
    #2          2       3379   
    #3          3        622   
    #4          4        291   
    #5        5-10       935   
    #6        11-50      451   
    #7       51-100       8    
    #8       101-500      3    
    #9        >500        2    
  Average     2.11             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  1.899485s wall, 3.281250s user + 0.015625s system = 3.296875s CPU (173.6%)

RUN-1004 : used memory is 1048 MB, reserved memory is 1047 MB, peak memory is 1115 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 66787, tnet num: 19247, tinst num: 8073, tnode num: 90609, tedge num: 110092.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.627024s wall, 1.609375s user + 0.000000s system = 1.609375s CPU (98.9%)

RUN-1004 : used memory is 1051 MB, reserved memory is 1050 MB, peak memory is 1115 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19247 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.232889s wall, 1.218750s user + 0.015625s system = 1.234375s CPU (100.1%)

RUN-1004 : used memory is 1058 MB, reserved memory is 1055 MB, peak memory is 1115 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 057bf4ae9dc0d18f7126c20ce7aec8c31cdd0d41110c6b742e6e5620711a0ef6 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8073
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19249, pip num: 143652
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 282
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3204 valid insts, and 404038 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101000001010010110001101
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  9.921955s wall, 98.718750s user + 0.140625s system = 98.859375s CPU (996.4%)

RUN-1004 : used memory is 1178 MB, reserved memory is 1164 MB, peak memory is 1292 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250820_114630.log"

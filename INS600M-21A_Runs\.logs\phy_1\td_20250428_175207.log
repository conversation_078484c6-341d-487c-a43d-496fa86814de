============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.5_SP3/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     51593
   Run Date =   Mon Apr 28 17:52:07 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.697262s wall, 1.609375s user + 0.093750s system = 1.703125s CPU (100.3%)

RUN-1004 : used memory is 284 MB, reserved memory is 257 MB, peak memory is 287 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 -duty_cycle 50.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 10 view nodes, 53 trigger nets, 53 data nets.
KIT-1004 : Chipwatcher code = 1101110111001010
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.5_SP3/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=10,BUS_DIN_NUM=53,BUS_CTRL_NUM=146,BUS_WIDTH='{32'sb01,32'sb01,32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01,32'sb010,32'sb0110,32'sb01110,32'sb01111,32'sb010000,32'sb010001,32'sb010101,32'sb0100101},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01100,32'sb011000,32'sb0101100,32'sb0110010,32'sb0111000,32'sb0111110,32'sb01001010,32'sb01101110}) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=168) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=168) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=10,BUS_DIN_NUM=53,BUS_CTRL_NUM=146,BUS_WIDTH='{32'sb01,32'sb01,32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01,32'sb010,32'sb0110,32'sb01110,32'sb01111,32'sb010000,32'sb010001,32'sb010101,32'sb0100101},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01100,32'sb011000,32'sb0101100,32'sb0110010,32'sb0111000,32'sb0111110,32'sb01001010,32'sb01101110}) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=10,BUS_DIN_NUM=53,BUS_CTRL_NUM=146,BUS_WIDTH='{32'sb01,32'sb01,32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01,32'sb010,32'sb0110,32'sb01110,32'sb01111,32'sb010000,32'sb010001,32'sb010101,32'sb0100101},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01100,32'sb011000,32'sb0101100,32'sb0110010,32'sb0111000,32'sb0111110,32'sb01001010,32'sb01101110}) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.5_SP3/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.5_SP3/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=10,BUS_DIN_NUM=53,BUS_CTRL_NUM=146,BUS_WIDTH='{32'sb01,32'sb01,32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01,32'sb010,32'sb0110,32'sb01110,32'sb01111,32'sb010000,32'sb010001,32'sb010101,32'sb0100101},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01100,32'sb011000,32'sb0101100,32'sb0110010,32'sb0111000,32'sb0111110,32'sb01001010,32'sb01101110})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=168)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=168)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=10,BUS_DIN_NUM=53,BUS_CTRL_NUM=146,BUS_WIDTH='{32'sb01,32'sb01,32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01,32'sb010,32'sb0110,32'sb01110,32'sb01111,32'sb010000,32'sb010001,32'sb010101,32'sb0100101},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01100,32'sb011000,32'sb0101100,32'sb0110010,32'sb0111000,32'sb0111110,32'sb01001010,32'sb01101110})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=10,BUS_DIN_NUM=53,BUS_CTRL_NUM=146,BUS_WIDTH='{32'sb01,32'sb01,32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01,32'sb010,32'sb0110,32'sb01110,32'sb01111,32'sb010000,32'sb010001,32'sb010101,32'sb0100101},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01100,32'sb011000,32'sb0101100,32'sb0110010,32'sb0111000,32'sb0111110,32'sb01001010,32'sb01101110})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 23136/43 useful/useless nets, 19817/25 useful/useless insts
SYN-1016 : Merged 49 instances.
SYN-1032 : 22700/26 useful/useless nets, 20318/22 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 5 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 5 mux instances.
SYN-1015 : Optimize round 1, 501 better
SYN-1014 : Optimize round 2
SYN-1032 : 22266/75 useful/useless nets, 19884/80 useful/useless insts
SYN-1015 : Optimize round 2, 160 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.820648s wall, 2.656250s user + 0.156250s system = 2.812500s CPU (99.7%)

RUN-1004 : used memory is 324 MB, reserved memory is 297 MB, peak memory is 326 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.5_SP3/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 67 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22326/373 useful/useless nets, 19991/58 useful/useless insts
SYN-1016 : Merged 57 instances.
SYN-2571 : Optimize after map_dsp, round 1, 488 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 48 instances.
SYN-2501 : Optimize round 1, 98 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 20 macro adder
SYN-1019 : Optimized 21 mux instances.
SYN-1016 : Merged 13 instances.
SYN-1032 : 22838/5 useful/useless nets, 20503/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1002 : start command "get_pins config_inst.jtck"
RUN-1002 : start command "create_clock -name jtck -period 100 "
RUN-1102 : create_clock: clock name: jtck, type: 0, period: 100000, rise: 0, fall: 50000.
RUN-1002 : start command "get_clocks jtck"
RUN-1002 : start command "set_clock_uncertainty -hold 0.1 "
RUN-1002 : start command "get_clocks jtck"
RUN-1002 : start command "set_clock_groups -exclusive -group "
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 84359, tnet num: 22838, tinst num: 20502, tnode num: 118177, tedge num: 131471.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.219738s wall, 1.187500s user + 0.031250s system = 1.218750s CPU (99.9%)

RUN-1004 : used memory is 467 MB, reserved memory is 441 MB, peak memory is 467 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22838 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 281 (3.41), #lev = 7 (1.79)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 281 (3.41), #lev = 7 (1.79)
SYN-3001 : Logic optimization runtime opt =   0.05 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 683 instances into 281 LUTs, name keeping = 74%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 509 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 152 adder to BLE ...
SYN-4008 : Packed 152 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  5.138907s wall, 5.015625s user + 0.125000s system = 5.140625s CPU (100.0%)

RUN-1004 : used memory is 356 MB, reserved memory is 336 MB, peak memory is 584 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  8.313402s wall, 8.000000s user + 0.296875s system = 8.296875s CPU (99.8%)

RUN-1004 : used memory is 356 MB, reserved memory is 337 MB, peak memory is 584 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.5_SP3/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/rmc_com2/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/rmc_com2/GPRMC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/rmc_com2/GPRMC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/rmc_com2/GPRMC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/rmc_com2/GPRMC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/rmc_com2/GPRMC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/rmc_com2/GPRMC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/rmc_com2/GPRMC_data[0]
SYN-5055 WARNING: The kept net COM2/rx_err will be merged to another kept net COM2/rmc_com2/GPRMC_err
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sub_state_cnt0[3] will be merged to another kept net COM2/rmc_com2/GPRMC_sub_state_cnt0[2]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (352 clock/control pins, 0 other pins).
SYN-4027 : Net DATA/DIV_Dtemp/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net DATA/DIV_Dtemp/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19761 instances
RUN-0007 : 5684 luts, 12533 seqs, 939 mslices, 491 lslices, 61 pads, 48 brams, 0 dsps
RUN-1001 : There are total 22120 nets
RUN-1001 : 16351 nets have 2 pins
RUN-1001 : 4594 nets have [3 - 5] pins
RUN-1001 : 794 nets have [6 - 10] pins
RUN-1001 : 248 nets have [11 - 20] pins
RUN-1001 : 107 nets have [21 - 99] pins
RUN-1001 : 26 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4745     
RUN-1001 :   No   |  No   |  Yes  |     641     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    6531     
RUN-1001 :   Yes  |  No   |  Yes  |     516     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  106  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 114
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19759 instances, 5684 luts, 12533 seqs, 1430 slices, 284 macros(1430 instances: 939 mslices 491 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1131 pins
PHY-3001 : Huge net DATA/done_div with 1650 pins
PHY-0007 : Cell area utilization is 64%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 82709, tnet num: 22118, tinst num: 19759, tnode num: 116684, tedge num: 130117.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.222196s wall, 1.187500s user + 0.031250s system = 1.218750s CPU (99.7%)

RUN-1004 : used memory is 531 MB, reserved memory is 508 MB, peak memory is 584 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22118 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.225755s wall, 2.187500s user + 0.031250s system = 2.218750s CPU (99.7%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.46053e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19759.
PHY-3001 : Level 1 #clusters 2031.
PHY-3001 : End clustering;  0.176941s wall, 0.343750s user + 0.031250s system = 0.375000s CPU (211.9%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 64%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 833745, overlap = 711.812
PHY-3002 : Step(2): len = 742365, overlap = 817.562
PHY-3002 : Step(3): len = 502543, overlap = 991.5
PHY-3002 : Step(4): len = 448533, overlap = 1025.75
PHY-3002 : Step(5): len = 349672, overlap = 1164.59
PHY-3002 : Step(6): len = 307210, overlap = 1209.84
PHY-3002 : Step(7): len = 258118, overlap = 1268.78
PHY-3002 : Step(8): len = 235567, overlap = 1310.94
PHY-3002 : Step(9): len = 206565, overlap = 1357.12
PHY-3002 : Step(10): len = 190739, overlap = 1403.03
PHY-3002 : Step(11): len = 170831, overlap = 1457.78
PHY-3002 : Step(12): len = 157986, overlap = 1489.06
PHY-3002 : Step(13): len = 142653, overlap = 1531.59
PHY-3002 : Step(14): len = 134484, overlap = 1543.91
PHY-3002 : Step(15): len = 123241, overlap = 1559.28
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.0125e-06
PHY-3002 : Step(16): len = 131934, overlap = 1545.53
PHY-3002 : Step(17): len = 181416, overlap = 1430.25
PHY-3002 : Step(18): len = 194106, overlap = 1336.53
PHY-3002 : Step(19): len = 191892, overlap = 1284.44
PHY-3002 : Step(20): len = 189796, overlap = 1229.12
PHY-3002 : Step(21): len = 185922, overlap = 1182.28
PHY-3002 : Step(22): len = 183419, overlap = 1178
PHY-3002 : Step(23): len = 178799, overlap = 1166.41
PHY-3002 : Step(24): len = 176528, overlap = 1156.25
PHY-3002 : Step(25): len = 173746, overlap = 1146.66
PHY-3002 : Step(26): len = 172736, overlap = 1167.22
PHY-3002 : Step(27): len = 170197, overlap = 1169.81
PHY-3002 : Step(28): len = 169088, overlap = 1161.62
PHY-3002 : Step(29): len = 168103, overlap = 1169.16
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.025e-06
PHY-3002 : Step(30): len = 179442, overlap = 1113.44
PHY-3002 : Step(31): len = 195018, overlap = 1026.19
PHY-3002 : Step(32): len = 199117, overlap = 1001.22
PHY-3002 : Step(33): len = 201814, overlap = 985.531
PHY-3002 : Step(34): len = 202532, overlap = 970.469
PHY-3002 : Step(35): len = 202094, overlap = 959.219
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.05001e-06
PHY-3002 : Step(36): len = 216038, overlap = 878.906
PHY-3002 : Step(37): len = 234190, overlap = 789.906
PHY-3002 : Step(38): len = 241987, overlap = 747.156
PHY-3002 : Step(39): len = 245631, overlap = 712.219
PHY-3002 : Step(40): len = 244977, overlap = 698.906
PHY-3002 : Step(41): len = 242468, overlap = 692.156
PHY-3002 : Step(42): len = 239778, overlap = 695.656
PHY-3002 : Step(43): len = 238892, overlap = 695.875
PHY-3002 : Step(44): len = 237697, overlap = 706.531
PHY-3002 : Step(45): len = 236470, overlap = 725.719
PHY-3002 : Step(46): len = 235496, overlap = 748.281
PHY-3002 : Step(47): len = 233975, overlap = 754.344
PHY-3002 : Step(48): len = 233512, overlap = 737.375
PHY-3002 : Step(49): len = 232307, overlap = 723.875
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 8.10002e-06
PHY-3002 : Step(50): len = 245652, overlap = 702.125
PHY-3002 : Step(51): len = 260731, overlap = 654.25
PHY-3002 : Step(52): len = 265853, overlap = 609.625
PHY-3002 : Step(53): len = 269168, overlap = 575.125
PHY-3002 : Step(54): len = 268132, overlap = 571.25
PHY-3002 : Step(55): len = 266886, overlap = 570.5
PHY-3002 : Step(56): len = 264538, overlap = 560.469
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.62e-05
PHY-3002 : Step(57): len = 276017, overlap = 544.906
PHY-3002 : Step(58): len = 292557, overlap = 494.5
PHY-3002 : Step(59): len = 297542, overlap = 450.969
PHY-3002 : Step(60): len = 298568, overlap = 434.688
PHY-3002 : Step(61): len = 296057, overlap = 429.406
PHY-3002 : Step(62): len = 293263, overlap = 443.938
PHY-3002 : Step(63): len = 290967, overlap = 453.938
PHY-3002 : Step(64): len = 289358, overlap = 454.844
PHY-3002 : Step(65): len = 287743, overlap = 477.688
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.24001e-05
PHY-3002 : Step(66): len = 298172, overlap = 474.75
PHY-3002 : Step(67): len = 310456, overlap = 455.656
PHY-3002 : Step(68): len = 313452, overlap = 446.688
PHY-3002 : Step(69): len = 313220, overlap = 441.844
PHY-3002 : Step(70): len = 311643, overlap = 439
PHY-3002 : Step(71): len = 309888, overlap = 439.938
PHY-3002 : Step(72): len = 307649, overlap = 444.469
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 6.48001e-05
PHY-3002 : Step(73): len = 315634, overlap = 438.844
PHY-3002 : Step(74): len = 324634, overlap = 418.719
PHY-3002 : Step(75): len = 327887, overlap = 411.875
PHY-3002 : Step(76): len = 328894, overlap = 383.531
PHY-3002 : Step(77): len = 327398, overlap = 364.906
PHY-3002 : Step(78): len = 325338, overlap = 374.156
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000127808
PHY-3002 : Step(79): len = 331058, overlap = 364.312
PHY-3002 : Step(80): len = 336194, overlap = 354.438
PHY-3002 : Step(81): len = 340201, overlap = 302
PHY-3002 : Step(82): len = 345100, overlap = 289.438
PHY-3002 : Step(83): len = 343159, overlap = 287.062
PHY-3002 : Step(84): len = 341687, overlap = 290.188
PHY-3002 : Step(85): len = 339252, overlap = 290.625
PHY-3002 : Step(86): len = 338850, overlap = 280.125
PHY-3002 : Step(87): len = 337170, overlap = 308.344
PHY-3002 : Step(88): len = 336887, overlap = 308.094
PHY-3002 : Step(89): len = 335944, overlap = 314.281
PHY-3002 : Step(90): len = 336597, overlap = 306.906
PHY-3002 : Step(91): len = 335685, overlap = 311.531
PHY-3002 : Step(92): len = 335707, overlap = 305.938
PHY-3002 : Step(93): len = 335209, overlap = 326.188
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000244288
PHY-3002 : Step(94): len = 337845, overlap = 310.281
PHY-3002 : Step(95): len = 342840, overlap = 307.094
PHY-3002 : Step(96): len = 344998, overlap = 293.125
PHY-3002 : Step(97): len = 347685, overlap = 287.938
PHY-3002 : Step(98): len = 347184, overlap = 267.094
PHY-3002 : Step(99): len = 346379, overlap = 254.625
PHY-3002 : Step(100): len = 346174, overlap = 259.156
PHY-3002 : Step(101): len = 345889, overlap = 263.156
PHY-3002 : Step(102): len = 346374, overlap = 244.406
PHY-3002 : Step(103): len = 346426, overlap = 263.656
PHY-3002 : Step(104): len = 346207, overlap = 261.375
PHY-3002 : Step(105): len = 346533, overlap = 272.875
PHY-3002 : Step(106): len = 346369, overlap = 283.969
PHY-3002 : Step(107): len = 345789, overlap = 294.438
PHY-3002 : Step(108): len = 344584, overlap = 296.969
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000417848
PHY-3002 : Step(109): len = 345846, overlap = 285.812
PHY-3002 : Step(110): len = 347622, overlap = 285.125
PHY-3002 : Step(111): len = 348369, overlap = 287.5
PHY-3001 : :::11::: Try harder cell spreading with beta_ = 0.000706067
PHY-3002 : Step(112): len = 348773, overlap = 281.969
PHY-3002 : Step(113): len = 352383, overlap = 259.906
PHY-3002 : Step(114): len = 353920, overlap = 252.312
PHY-3002 : Step(115): len = 354858, overlap = 244.219
PHY-3002 : Step(116): len = 354358, overlap = 245.062
PHY-3002 : Step(117): len = 354065, overlap = 239.938
PHY-3002 : Step(118): len = 353637, overlap = 237.438
PHY-3002 : Step(119): len = 353459, overlap = 245.094
PHY-3002 : Step(120): len = 353180, overlap = 257.125
PHY-3001 : Before Legalized: Len = 376564
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.021180s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (147.5%)

PHY-3001 : After Legalized: Len = 382218, Over = 145.031
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/22120.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 475888, over cnt = 1324(3%), over = 5854, worst = 49
PHY-1001 : End global iterations;  0.960075s wall, 1.328125s user + 0.093750s system = 1.421875s CPU (148.1%)

PHY-1001 : Congestion index: top1 = 75.82, top5 = 54.29, top10 = 44.74, top15 = 39.05.
PHY-3001 : End congestion estimation;  1.207453s wall, 1.546875s user + 0.109375s system = 1.656250s CPU (137.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22118 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.047697s wall, 1.062500s user + 0.000000s system = 1.062500s CPU (101.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.20137e-05
PHY-3002 : Step(121): len = 397955, overlap = 160
PHY-3002 : Step(122): len = 414213, overlap = 138.062
PHY-3002 : Step(123): len = 412548, overlap = 143.344
PHY-3002 : Step(124): len = 408622, overlap = 135.594
PHY-3002 : Step(125): len = 411019, overlap = 132.031
PHY-3002 : Step(126): len = 413617, overlap = 129.656
PHY-3002 : Step(127): len = 417579, overlap = 131.812
PHY-3002 : Step(128): len = 421848, overlap = 135.844
PHY-3002 : Step(129): len = 423276, overlap = 134.188
PHY-3002 : Step(130): len = 426511, overlap = 137.375
PHY-3002 : Step(131): len = 426979, overlap = 144.469
PHY-3002 : Step(132): len = 426608, overlap = 148.312
PHY-3002 : Step(133): len = 426525, overlap = 147.219
PHY-3002 : Step(134): len = 427991, overlap = 145.406
PHY-3002 : Step(135): len = 427360, overlap = 145.281
PHY-3002 : Step(136): len = 426865, overlap = 135.281
PHY-3002 : Step(137): len = 428231, overlap = 132.625
PHY-3002 : Step(138): len = 426904, overlap = 132.188
PHY-3002 : Step(139): len = 426199, overlap = 131.188
PHY-3002 : Step(140): len = 427208, overlap = 130.406
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000184027
PHY-3002 : Step(141): len = 426240, overlap = 127.062
PHY-3002 : Step(142): len = 427246, overlap = 124.062
PHY-3002 : Step(143): len = 428882, overlap = 123.531
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00035844
PHY-3002 : Step(144): len = 435795, overlap = 119.062
PHY-3002 : Step(145): len = 443353, overlap = 123.938
PHY-3002 : Step(146): len = 446913, overlap = 118.062
PHY-3002 : Step(147): len = 448701, overlap = 115
PHY-3002 : Step(148): len = 449472, overlap = 113.469
PHY-3002 : Step(149): len = 448178, overlap = 110.781
PHY-3002 : Step(150): len = 447943, overlap = 118.656
PHY-3002 : Step(151): len = 448725, overlap = 114.531
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 51/22120.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 530992, over cnt = 2311(6%), over = 10108, worst = 35
PHY-1001 : End global iterations;  1.183622s wall, 1.906250s user + 0.015625s system = 1.921875s CPU (162.4%)

PHY-1001 : Congestion index: top1 = 76.57, top5 = 58.10, top10 = 50.16, top15 = 45.30.
PHY-3001 : End congestion estimation;  1.466964s wall, 2.171875s user + 0.015625s system = 2.187500s CPU (149.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22118 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.068317s wall, 1.062500s user + 0.015625s system = 1.078125s CPU (100.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000104862
PHY-3002 : Step(152): len = 458536, overlap = 358.062
PHY-3002 : Step(153): len = 465404, overlap = 291.281
PHY-3002 : Step(154): len = 458216, overlap = 273.844
PHY-3002 : Step(155): len = 453426, overlap = 249.188
PHY-3002 : Step(156): len = 451488, overlap = 224.219
PHY-3002 : Step(157): len = 450233, overlap = 225.969
PHY-3002 : Step(158): len = 448661, overlap = 218.156
PHY-3002 : Step(159): len = 447709, overlap = 214.25
PHY-3002 : Step(160): len = 447202, overlap = 227.625
PHY-3002 : Step(161): len = 447016, overlap = 224
PHY-3002 : Step(162): len = 446867, overlap = 223.719
PHY-3002 : Step(163): len = 445485, overlap = 220.781
PHY-3002 : Step(164): len = 445585, overlap = 221.312
PHY-3002 : Step(165): len = 444978, overlap = 218.781
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000209725
PHY-3002 : Step(166): len = 444513, overlap = 207.062
PHY-3002 : Step(167): len = 446569, overlap = 202.375
PHY-3002 : Step(168): len = 448986, overlap = 196.969
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00041945
PHY-3002 : Step(169): len = 449549, overlap = 195.125
PHY-3002 : Step(170): len = 455159, overlap = 179.594
PHY-3002 : Step(171): len = 462008, overlap = 172.406
PHY-3002 : Step(172): len = 464458, overlap = 172.438
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000838899
PHY-3002 : Step(173): len = 464860, overlap = 168.938
PHY-3002 : Step(174): len = 468262, overlap = 168.688
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 82709, tnet num: 22118, tinst num: 19759, tnode num: 116684, tedge num: 130117.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.566167s wall, 1.531250s user + 0.031250s system = 1.562500s CPU (99.8%)

RUN-1004 : used memory is 573 MB, reserved memory is 553 MB, peak memory is 714 MB
OPT-1001 : Total overflow 554.12 peak overflow 5.12
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 477/22120.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 555312, over cnt = 2633(7%), over = 9131, worst = 23
PHY-1001 : End global iterations;  1.375134s wall, 2.109375s user + 0.125000s system = 2.234375s CPU (162.5%)

PHY-1001 : Congestion index: top1 = 58.86, top5 = 49.31, top10 = 44.59, top15 = 41.41.
PHY-1001 : End incremental global routing;  1.632713s wall, 2.359375s user + 0.125000s system = 2.484375s CPU (152.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22118 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.133507s wall, 1.125000s user + 0.015625s system = 1.140625s CPU (100.6%)

OPT-1001 : 21 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 61 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19674 has valid locations, 297 needs to be replaced
PHY-3001 : design contains 20035 instances, 5796 luts, 12697 seqs, 1430 slices, 284 macros(1430 instances: 939 mslices 491 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 488252
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17366/22396.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 571288, over cnt = 2639(7%), over = 9232, worst = 23
PHY-1001 : End global iterations;  0.226494s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (158.7%)

PHY-1001 : Congestion index: top1 = 59.18, top5 = 49.52, top10 = 44.86, top15 = 41.76.
PHY-3001 : End congestion estimation;  0.489617s wall, 0.625000s user + 0.000000s system = 0.625000s CPU (127.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 83629, tnet num: 22394, tinst num: 20035, tnode num: 117997, tedge num: 131405.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.569211s wall, 1.531250s user + 0.031250s system = 1.562500s CPU (99.6%)

RUN-1004 : used memory is 621 MB, reserved memory is 624 MB, peak memory is 717 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22394 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.740407s wall, 2.703125s user + 0.031250s system = 2.734375s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(175): len = 487538, overlap = 3.25
PHY-3002 : Step(176): len = 488573, overlap = 3.375
PHY-3002 : Step(177): len = 489234, overlap = 3.5625
PHY-3002 : Step(178): len = 489560, overlap = 3.5625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17417/22396.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 570968, over cnt = 2687(7%), over = 9376, worst = 23
PHY-1001 : End global iterations;  0.226781s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (117.1%)

PHY-1001 : Congestion index: top1 = 59.48, top5 = 50.11, top10 = 45.36, top15 = 42.19.
PHY-3001 : End congestion estimation;  0.533826s wall, 0.562500s user + 0.000000s system = 0.562500s CPU (105.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22394 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.158249s wall, 1.109375s user + 0.046875s system = 1.156250s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00075238
PHY-3002 : Step(179): len = 489767, overlap = 172.156
PHY-3002 : Step(180): len = 490339, overlap = 173
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00150476
PHY-3002 : Step(181): len = 490567, overlap = 172.281
PHY-3002 : Step(182): len = 491166, overlap = 171.875
PHY-3001 : Final: Len = 491166, Over = 171.875
PHY-3001 : End incremental placement;  5.859366s wall, 6.468750s user + 0.312500s system = 6.781250s CPU (115.7%)

OPT-1001 : Total overflow 561.81 peak overflow 5.12
OPT-1001 : End high-fanout net optimization;  9.235310s wall, 10.734375s user + 0.468750s system = 11.203125s CPU (121.3%)

OPT-1001 : Current memory(MB): used = 722, reserve = 707, peak = 739.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17412/22396.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 573992, over cnt = 2608(7%), over = 8631, worst = 23
PHY-1002 : len = 618200, over cnt = 1858(5%), over = 4597, worst = 20
PHY-1002 : len = 663632, over cnt = 557(1%), over = 1236, worst = 20
PHY-1002 : len = 682304, over cnt = 55(0%), over = 58, worst = 3
PHY-1002 : len = 683976, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.455347s wall, 2.265625s user + 0.000000s system = 2.265625s CPU (155.7%)

PHY-1001 : Congestion index: top1 = 50.06, top5 = 44.60, top10 = 41.69, top15 = 39.78.
OPT-1001 : End congestion update;  1.727761s wall, 2.531250s user + 0.000000s system = 2.531250s CPU (146.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22394 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.971731s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (99.7%)

OPT-0007 : Start: WNS 4369 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.706544s wall, 3.515625s user + 0.000000s system = 3.515625s CPU (129.9%)

OPT-1001 : Current memory(MB): used = 699, reserve = 691, peak = 739.
OPT-1001 : End physical optimization;  13.860744s wall, 16.265625s user + 0.531250s system = 16.796875s CPU (121.2%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5796 LUT to BLE ...
SYN-4008 : Packed 5796 LUT and 2866 SEQ to BLE.
SYN-4003 : Packing 9831 remaining SEQ's ...
SYN-4005 : Packed 3347 SEQ with LUT/SLICE
SYN-4006 : 88 single LUT's are left
SYN-4006 : 6484 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 12280/13944 primitive instances ...
PHY-3001 : End packing;  2.954642s wall, 2.937500s user + 0.000000s system = 2.937500s CPU (99.4%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8217 instances
RUN-1001 : 4051 mslices, 4052 lslices, 61 pads, 48 brams, 0 dsps
RUN-1001 : There are total 19578 nets
RUN-1001 : 13455 nets have 2 pins
RUN-1001 : 4700 nets have [3 - 5] pins
RUN-1001 : 861 nets have [6 - 10] pins
RUN-1001 : 393 nets have [11 - 20] pins
RUN-1001 : 160 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8215 instances, 8103 slices, 284 macros(1430 instances: 939 mslices 491 lslices)
PHY-3001 : Cell area utilization is 86%
PHY-3001 : After packing: Len = 505544, Over = 387.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7985/19578.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 643888, over cnt = 1660(4%), over = 2618, worst = 7
PHY-1002 : len = 649816, over cnt = 1065(3%), over = 1483, worst = 6
PHY-1002 : len = 662560, over cnt = 395(1%), over = 530, worst = 6
PHY-1002 : len = 669528, over cnt = 128(0%), over = 160, worst = 4
PHY-1002 : len = 673128, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.386233s wall, 2.109375s user + 0.046875s system = 2.156250s CPU (155.5%)

PHY-1001 : Congestion index: top1 = 52.11, top5 = 45.31, top10 = 41.72, top15 = 39.43.
PHY-3001 : End congestion estimation;  1.736986s wall, 2.468750s user + 0.046875s system = 2.515625s CPU (144.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68816, tnet num: 19576, tinst num: 8215, tnode num: 93719, tedge num: 113381.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.869047s wall, 1.843750s user + 0.031250s system = 1.875000s CPU (100.3%)

RUN-1004 : used memory is 601 MB, reserved memory is 588 MB, peak memory is 739 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19576 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.902545s wall, 2.859375s user + 0.046875s system = 2.906250s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.27854e-05
PHY-3002 : Step(183): len = 501951, overlap = 374.5
PHY-3002 : Step(184): len = 497986, overlap = 382
PHY-3002 : Step(185): len = 497490, overlap = 398
PHY-3002 : Step(186): len = 497729, overlap = 405.25
PHY-3002 : Step(187): len = 496663, overlap = 417
PHY-3002 : Step(188): len = 496919, overlap = 417.5
PHY-3002 : Step(189): len = 495820, overlap = 421.25
PHY-3002 : Step(190): len = 495398, overlap = 420.75
PHY-3002 : Step(191): len = 493695, overlap = 419.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.55707e-05
PHY-3002 : Step(192): len = 498826, overlap = 402.75
PHY-3002 : Step(193): len = 503105, overlap = 385.75
PHY-3002 : Step(194): len = 502405, overlap = 379
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000171141
PHY-3002 : Step(195): len = 509919, overlap = 375.75
PHY-3002 : Step(196): len = 520104, overlap = 361.75
PHY-3002 : Step(197): len = 518585, overlap = 357.75
PHY-3002 : Step(198): len = 516291, overlap = 366
PHY-3001 : Before Legalized: Len = 516291
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.756465s wall, 0.937500s user + 0.609375s system = 1.546875s CPU (204.5%)

PHY-3001 : After Legalized: Len = 644948, Over = 0
PHY-3001 : Trial Legalized: Len = 644948
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 534/19578.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 741576, over cnt = 2603(7%), over = 4250, worst = 8
PHY-1002 : len = 757792, over cnt = 1646(4%), over = 2279, worst = 7
PHY-1002 : len = 778344, over cnt = 612(1%), over = 803, worst = 5
PHY-1002 : len = 786672, over cnt = 233(0%), over = 302, worst = 4
PHY-1002 : len = 792560, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.209262s wall, 3.578125s user + 0.015625s system = 3.593750s CPU (162.7%)

PHY-1001 : Congestion index: top1 = 53.02, top5 = 48.18, top10 = 45.01, top15 = 43.05.
PHY-3001 : End congestion estimation;  2.592288s wall, 3.953125s user + 0.015625s system = 3.968750s CPU (153.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19576 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.308704s wall, 1.312500s user + 0.000000s system = 1.312500s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000205471
PHY-3002 : Step(199): len = 593881, overlap = 92.25
PHY-3002 : Step(200): len = 573268, overlap = 136
PHY-3002 : Step(201): len = 560199, overlap = 180.5
PHY-3002 : Step(202): len = 553556, overlap = 217.75
PHY-3002 : Step(203): len = 550115, overlap = 238.5
PHY-3002 : Step(204): len = 547054, overlap = 246.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000410942
PHY-3002 : Step(205): len = 551403, overlap = 244
PHY-3002 : Step(206): len = 555587, overlap = 240
PHY-3002 : Step(207): len = 555663, overlap = 244.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000717749
PHY-3002 : Step(208): len = 558439, overlap = 243.5
PHY-3002 : Step(209): len = 563357, overlap = 243.5
PHY-3001 : Before Legalized: Len = 563357
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.042780s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (109.6%)

PHY-3001 : After Legalized: Len = 605912, Over = 0
PHY-3001 : Legalized: Len = 605912, Over = 0
PHY-3001 : Spreading special nets. 50 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.088614s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (88.2%)

PHY-3001 : 79 instances has been re-located, deltaX = 25, deltaY = 49, maxDist = 2.
PHY-3001 : Final: Len = 607316, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68816, tnet num: 19576, tinst num: 8215, tnode num: 93719, tedge num: 113381.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.140450s wall, 2.140625s user + 0.000000s system = 2.140625s CPU (100.0%)

RUN-1004 : used memory is 606 MB, reserved memory is 602 MB, peak memory is 739 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3930/19578.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 716072, over cnt = 2311(6%), over = 3679, worst = 6
PHY-1002 : len = 729312, over cnt = 1266(3%), over = 1755, worst = 6
PHY-1002 : len = 742680, over cnt = 595(1%), over = 770, worst = 6
PHY-1002 : len = 754560, over cnt = 92(0%), over = 103, worst = 3
PHY-1002 : len = 756664, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.770288s wall, 2.984375s user + 0.093750s system = 3.078125s CPU (173.9%)

PHY-1001 : Congestion index: top1 = 50.84, top5 = 45.35, top10 = 42.58, top15 = 40.79.
PHY-1001 : End incremental global routing;  2.109407s wall, 3.312500s user + 0.093750s system = 3.406250s CPU (161.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19576 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.028472s wall, 1.000000s user + 0.015625s system = 1.015625s CPU (98.8%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 61 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8149 has valid locations, 17 needs to be replaced
PHY-3001 : design contains 8230 instances, 8118 slices, 284 macros(1430 instances: 939 mslices 491 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 609666
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17706/19591.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 758512, over cnt = 28(0%), over = 30, worst = 2
PHY-1002 : len = 758464, over cnt = 16(0%), over = 16, worst = 1
PHY-1002 : len = 758448, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 758520, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 758584, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.715363s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (100.5%)

PHY-1001 : Congestion index: top1 = 50.86, top5 = 45.44, top10 = 42.65, top15 = 40.86.
PHY-3001 : End congestion estimation;  1.043319s wall, 1.046875s user + 0.000000s system = 1.046875s CPU (100.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68879, tnet num: 19589, tinst num: 8230, tnode num: 93802, tedge num: 113462.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.076117s wall, 2.046875s user + 0.031250s system = 2.078125s CPU (100.1%)

RUN-1004 : used memory is 635 MB, reserved memory is 621 MB, peak memory is 739 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19589 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.130147s wall, 3.078125s user + 0.046875s system = 3.125000s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(210): len = 609439, overlap = 1.25
PHY-3002 : Step(211): len = 609443, overlap = 1.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17703/19591.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 757808, over cnt = 22(0%), over = 28, worst = 3
PHY-1002 : len = 757760, over cnt = 15(0%), over = 18, worst = 2
PHY-1002 : len = 757856, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 757888, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.572738s wall, 0.578125s user + 0.000000s system = 0.578125s CPU (100.9%)

PHY-1001 : Congestion index: top1 = 50.73, top5 = 45.41, top10 = 42.62, top15 = 40.81.
PHY-3001 : End congestion estimation;  0.892584s wall, 0.906250s user + 0.000000s system = 0.906250s CPU (101.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19589 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.994327s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000528331
PHY-3002 : Step(212): len = 609365, overlap = 2.5
PHY-3002 : Step(213): len = 609330, overlap = 2
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006787s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (230.2%)

PHY-3001 : Legalized: Len = 609385, Over = 0
PHY-3001 : End spreading;  0.073056s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (106.9%)

PHY-3001 : Final: Len = 609385, Over = 0
PHY-3001 : End incremental placement;  6.704366s wall, 6.609375s user + 0.156250s system = 6.765625s CPU (100.9%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.369135s wall, 11.468750s user + 0.281250s system = 11.750000s CPU (113.3%)

OPT-1001 : Current memory(MB): used = 724, reserve = 716, peak = 739.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17703/19591.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 758040, over cnt = 21(0%), over = 23, worst = 2
PHY-1002 : len = 757944, over cnt = 17(0%), over = 17, worst = 1
PHY-1002 : len = 758096, over cnt = 10(0%), over = 10, worst = 1
PHY-1002 : len = 758144, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 758344, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.726657s wall, 0.781250s user + 0.015625s system = 0.796875s CPU (109.7%)

PHY-1001 : Congestion index: top1 = 50.78, top5 = 45.39, top10 = 42.65, top15 = 40.84.
OPT-1001 : End congestion update;  1.048747s wall, 1.093750s user + 0.015625s system = 1.109375s CPU (105.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19589 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.841310s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (100.3%)

OPT-0007 : Start: WNS 4553 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.895359s wall, 1.937500s user + 0.015625s system = 1.953125s CPU (103.0%)

OPT-1001 : Current memory(MB): used = 724, reserve = 716, peak = 739.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19589 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.951848s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (100.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17722/19591.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 758344, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.129096s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (108.9%)

PHY-1001 : Congestion index: top1 = 50.78, top5 = 45.39, top10 = 42.65, top15 = 40.84.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19589 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.946651s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (100.7%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4553 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 50.310345
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4553ps with logic level 4 
RUN-1001 :       #2 path slack 4626ps with logic level 4 
OPT-1001 : End physical optimization;  17.035671s wall, 18.171875s user + 0.296875s system = 18.468750s CPU (108.4%)

RUN-1003 : finish command "place" in  67.687900s wall, 101.968750s user + 7.421875s system = 109.390625s CPU (161.6%)

RUN-1004 : used memory is 574 MB, reserved memory is 555 MB, peak memory is 739 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.674271s wall, 2.828125s user + 0.031250s system = 2.859375s CPU (170.8%)

RUN-1004 : used memory is 574 MB, reserved memory is 556 MB, peak memory is 739 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.5_SP3/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8232 instances
RUN-1001 : 4066 mslices, 4052 lslices, 61 pads, 48 brams, 0 dsps
RUN-1001 : There are total 19591 nets
RUN-1001 : 13454 nets have 2 pins
RUN-1001 : 4702 nets have [3 - 5] pins
RUN-1001 : 869 nets have [6 - 10] pins
RUN-1001 : 398 nets have [11 - 20] pins
RUN-1001 : 159 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68879, tnet num: 19589, tinst num: 8230, tnode num: 93802, tedge num: 113462.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.817347s wall, 1.812500s user + 0.015625s system = 1.828125s CPU (100.6%)

RUN-1004 : used memory is 603 MB, reserved memory is 614 MB, peak memory is 739 MB
PHY-1001 : 4066 mslices, 4052 lslices, 61 pads, 48 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19589 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 694072, over cnt = 2431(6%), over = 4073, worst = 7
PHY-1002 : len = 709912, over cnt = 1625(4%), over = 2339, worst = 6
PHY-1002 : len = 731784, over cnt = 574(1%), over = 760, worst = 6
PHY-1002 : len = 743760, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 743840, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.940608s wall, 3.359375s user + 0.046875s system = 3.406250s CPU (175.5%)

PHY-1001 : Congestion index: top1 = 49.18, top5 = 44.61, top10 = 42.06, top15 = 40.25.
PHY-1001 : End global routing;  2.297906s wall, 3.703125s user + 0.046875s system = 3.750000s CPU (163.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 709, reserve = 704, peak = 739.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net DATA/DIV_Dtemp/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 977, reserve = 970, peak = 977.
PHY-1001 : End build detailed router design. 4.630987s wall, 4.609375s user + 0.015625s system = 4.625000s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 191024, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.929585s wall, 0.921875s user + 0.015625s system = 0.937500s CPU (100.9%)

PHY-1001 : Current memory(MB): used = 1014, reserve = 1009, peak = 1014.
PHY-1001 : End phase 1; 0.937396s wall, 0.921875s user + 0.015625s system = 0.937500s CPU (100.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 55% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.77824e+06, over cnt = 1637(0%), over = 1646, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1030, reserve = 1024, peak = 1030.
PHY-1001 : End initial routed; 20.464582s wall, 50.796875s user + 0.390625s system = 51.187500s CPU (250.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18382(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.450   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.232   |  -16.646  |  10   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.678387s wall, 3.656250s user + 0.000000s system = 3.656250s CPU (99.4%)

PHY-1001 : Current memory(MB): used = 1045, reserve = 1041, peak = 1045.
PHY-1001 : End phase 2; 24.143155s wall, 54.453125s user + 0.390625s system = 54.843750s CPU (227.2%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.77824e+06, over cnt = 1637(0%), over = 1646, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.262091s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (95.4%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.76201e+06, over cnt = 656(0%), over = 656, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 1.008766s wall, 1.656250s user + 0.000000s system = 1.656250s CPU (164.2%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.76121e+06, over cnt = 96(0%), over = 96, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.717863s wall, 0.890625s user + 0.000000s system = 0.890625s CPU (124.1%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.76197e+06, over cnt = 24(0%), over = 24, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.258169s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (108.9%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.76222e+06, over cnt = 6(0%), over = 6, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.203480s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (99.8%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.272402s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (97.5%)

PHY-1001 : ===== DR Iter 6 =====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 6; 0.281247s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (100.0%)

PHY-1001 : ===== DR Iter 7 =====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 7; 0.441725s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (102.6%)

PHY-1001 : ===== DR Iter 8 =====
PHY-1022 : len = 1.76227e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 8; 0.178611s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (105.0%)

PHY-1001 : ==== DR Iter 9 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 9; 0.176524s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (106.2%)

PHY-1001 : ==== DR Iter 10 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 10; 0.222559s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (98.3%)

PHY-1001 : ==== DR Iter 11 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 11; 0.249176s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (100.3%)

PHY-1001 : ==== DR Iter 12 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 12; 0.341209s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (109.9%)

PHY-1001 : ===== DR Iter 13 =====
PHY-1022 : len = 1.7623e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 13; 0.187388s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (116.7%)

PHY-1001 : ==== DR Iter 14 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 14; 0.198579s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (110.2%)

PHY-1001 : ==== DR Iter 15 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 15; 0.242905s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (96.5%)

PHY-1001 : ==== DR Iter 16 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 16; 0.245213s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (102.0%)

PHY-1001 : ==== DR Iter 17 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 17; 0.378605s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (99.0%)

PHY-1001 : ==== DR Iter 18 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 18; 0.383425s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (101.9%)

PHY-1001 : ===== DR Iter 19 =====
PHY-1022 : len = 1.7623e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 19; 0.180612s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (112.5%)

PHY-1001 : ==== DR Iter 20 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 20; 0.189531s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (98.9%)

PHY-1001 : ==== DR Iter 21 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 21; 0.243178s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (96.4%)

PHY-1001 : ==== DR Iter 22 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 22; 0.243671s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (109.0%)

PHY-1001 : ==== DR Iter 23 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 23; 0.351087s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (97.9%)

PHY-1001 : ==== DR Iter 24 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 24; 0.432163s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (97.6%)

PHY-1001 : ==== DR Iter 25 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 25; 1.080419s wall, 1.078125s user + 0.000000s system = 1.078125s CPU (99.8%)

PHY-1001 : ===== DR Iter 26 =====
PHY-1022 : len = 1.76227e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 26; 0.185266s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (101.2%)

PHY-1001 : ==== DR Iter 27 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 27; 0.186697s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (92.1%)

PHY-1001 : ==== DR Iter 28 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 28; 0.225731s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (110.8%)

PHY-1001 : ==== DR Iter 29 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 29; 0.245114s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (102.0%)

PHY-1001 : ==== DR Iter 30 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 30; 0.346445s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (99.2%)

PHY-1001 : ==== DR Iter 31 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 31; 0.381003s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (98.4%)

PHY-1001 : ==== DR Iter 32 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 32; 1.075408s wall, 1.078125s user + 0.000000s system = 1.078125s CPU (100.3%)

PHY-1001 : ==== DR Iter 33 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 33; 1.105548s wall, 1.109375s user + 0.000000s system = 1.109375s CPU (100.3%)

PHY-1001 : ===== DR Iter 34 =====
PHY-1022 : len = 1.76227e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 34; 0.178664s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (104.9%)

PHY-1001 : ==== DR Iter 35 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 35; 0.179534s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (95.7%)

PHY-1001 : ==== DR Iter 36 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 36; 0.249783s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (100.1%)

PHY-1001 : ==== DR Iter 37 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 37; 0.256336s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (97.5%)

PHY-1001 : ==== DR Iter 38 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 38; 0.347817s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (103.3%)

PHY-1001 : ==== DR Iter 39 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 39; 0.479049s wall, 0.468750s user + 0.000000s system = 0.468750s CPU (97.9%)

PHY-1001 : ==== DR Iter 40 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 40; 1.013897s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (100.2%)

PHY-1001 : ==== DR Iter 41 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 41; 1.027833s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (100.3%)

PHY-1001 : ==== DR Iter 42 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 42; 1.028487s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (100.3%)

PHY-1001 : ===== DR Iter 43 =====
PHY-1022 : len = 1.7623e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 43; 0.173724s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (98.9%)

PHY-1001 : ==== DR Iter 44 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 44; 0.180404s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (103.9%)

PHY-1001 : ==== DR Iter 45 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 45; 0.222520s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (98.3%)

PHY-1001 : ==== DR Iter 46 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 46; 0.239964s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (84.6%)

PHY-1001 : ==== DR Iter 47 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 47; 0.339204s wall, 0.343750s user + 0.031250s system = 0.375000s CPU (110.6%)

PHY-1001 : ==== DR Iter 48 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 48; 0.376007s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (99.7%)

PHY-1001 : ==== DR Iter 49 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 49; 1.006826s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (100.9%)

PHY-1001 : ==== DR Iter 50 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 50; 1.026815s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (98.9%)

PHY-1001 : ==== DR Iter 51 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 51; 1.034883s wall, 1.046875s user + 0.000000s system = 1.046875s CPU (101.2%)

PHY-1001 : ==== DR Iter 52 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 52; 1.022781s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (99.3%)

PHY-1001 : ===== DR Iter 53 =====
PHY-1022 : len = 1.7623e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 53; 0.173532s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (99.0%)

PHY-1001 : ==== DR Iter 54 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 54; 0.179607s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (95.7%)

PHY-1001 : ==== DR Iter 55 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 55; 0.224648s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (97.4%)

PHY-1001 : ==== DR Iter 56 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 56; 0.234330s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (113.4%)

PHY-1001 : ==== DR Iter 57 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 57; 0.335772s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (102.4%)

PHY-1001 : ==== DR Iter 58 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 58; 0.381664s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (98.3%)

PHY-1001 : ==== DR Iter 59 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 59; 1.001052s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (99.9%)

PHY-1001 : ==== DR Iter 60 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 60; 1.022144s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (100.9%)

PHY-1001 : ==== DR Iter 61 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 61; 1.018377s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (99.7%)

PHY-1001 : ==== DR Iter 62 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 62; 1.031930s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (99.9%)

PHY-1001 : ==== DR Iter 63 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 63; 1.033751s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (99.8%)

PHY-1001 : ===== DR Iter 64 =====
PHY-1022 : len = 1.76227e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 64; 0.172901s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (99.4%)

PHY-1001 : ==== DR Iter 65 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 65; 0.176319s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (97.5%)

PHY-1001 : ==== DR Iter 66 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 66; 0.218329s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (100.2%)

PHY-1001 : ==== DR Iter 67 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 67; 0.235767s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (99.4%)

PHY-1001 : ==== DR Iter 68 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 68; 0.338831s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (101.5%)

PHY-1001 : ==== DR Iter 69 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 69; 0.370636s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (97.0%)

PHY-1001 : ==== DR Iter 70 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 70; 1.021846s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (100.9%)

PHY-1001 : ==== DR Iter 71 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 71; 1.025249s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (97.5%)

PHY-1001 : ==== DR Iter 72 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 72; 1.025942s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (100.5%)

PHY-1001 : ==== DR Iter 73 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 73; 1.021897s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (99.4%)

PHY-1001 : ==== DR Iter 74 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 74; 1.023264s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (100.8%)

PHY-1001 : ==== DR Iter 75 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 75; 1.035772s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (99.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18382(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.450   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.232   |  -16.646  |  10   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.672620s wall, 3.671875s user + 0.000000s system = 3.671875s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1050, reserve = 1045, peak = 1050.
PHY-1001 : End phase 3; 41.786937s wall, 42.703125s user + 0.062500s system = 42.765625s CPU (102.3%)

PHY-1001 : ===== Detail Route Phase 4 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.266498s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (99.7%)

PHY-0007 : Phase: 4; Congestion: {, , , }; Timing: {2.450ns, 0.000ns, 0}
PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.188112s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (99.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.207434s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (97.9%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.297044s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (99.9%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.76227e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.174900s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (98.3%)

PHY-1001 : ==== DR Iter 5 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.174140s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (98.7%)

PHY-1001 : ==== DR Iter 6 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 6; 0.222492s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (98.3%)

PHY-1001 : ==== DR Iter 7 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 7; 0.239927s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (97.7%)

PHY-1001 : ==== DR Iter 8 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 8; 0.344100s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (104.4%)

PHY-1001 : ===== DR Iter 9 =====
PHY-1022 : len = 1.7623e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 9; 0.175361s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (106.9%)

PHY-1001 : ==== DR Iter 10 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 10; 0.184332s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (101.7%)

PHY-1001 : ==== DR Iter 11 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 11; 0.240929s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (90.8%)

PHY-1001 : ==== DR Iter 12 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 12; 0.242906s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (102.9%)

PHY-1001 : ==== DR Iter 13 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 13; 0.347043s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (99.1%)

PHY-1001 : ==== DR Iter 14 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 14; 0.377138s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (99.4%)

PHY-1001 : ===== DR Iter 15 =====
PHY-1022 : len = 1.7623e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 15; 0.175216s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (98.1%)

PHY-1001 : ==== DR Iter 16 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 16; 0.183145s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (93.8%)

PHY-1001 : ==== DR Iter 17 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 17; 0.221971s wall, 0.250000s user + 0.031250s system = 0.281250s CPU (126.7%)

PHY-1001 : ==== DR Iter 18 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 18; 0.235594s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (99.5%)

PHY-1001 : ==== DR Iter 19 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 19; 0.339279s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (96.7%)

PHY-1001 : ==== DR Iter 20 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 20; 0.374880s wall, 0.375000s user + 0.015625s system = 0.390625s CPU (104.2%)

PHY-1001 : ==== DR Iter 21 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 21; 1.015716s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (101.5%)

PHY-1001 : ===== DR Iter 22 =====
PHY-1022 : len = 1.76227e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 22; 0.174429s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (98.5%)

PHY-1001 : ==== DR Iter 23 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 23; 0.174706s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (98.4%)

PHY-1001 : ==== DR Iter 24 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 24; 0.219724s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (99.6%)

PHY-1001 : ==== DR Iter 25 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 25; 0.242176s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (116.1%)

PHY-1001 : ==== DR Iter 26 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 26; 0.349535s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (98.3%)

PHY-1001 : ==== DR Iter 27 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 27; 0.379383s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (98.8%)

PHY-1001 : ==== DR Iter 28 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 28; 1.042412s wall, 1.046875s user + 0.000000s system = 1.046875s CPU (100.4%)

PHY-1001 : ==== DR Iter 29 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 29; 1.103459s wall, 1.109375s user + 0.000000s system = 1.109375s CPU (100.5%)

PHY-1001 : ===== DR Iter 30 =====
PHY-1022 : len = 1.76227e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 30; 0.175584s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (97.9%)

PHY-1001 : ==== DR Iter 31 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 31; 0.176224s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (124.1%)

PHY-1001 : ==== DR Iter 32 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 32; 0.224124s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (97.6%)

PHY-1001 : ==== DR Iter 33 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 33; 0.240287s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (97.5%)

PHY-1001 : ==== DR Iter 34 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 34; 0.340197s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (101.0%)

PHY-1001 : ==== DR Iter 35 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 35; 0.376473s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (99.6%)

PHY-1001 : ==== DR Iter 36 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 36; 1.022371s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (100.9%)

PHY-1001 : ==== DR Iter 37 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 37; 1.022552s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (99.3%)

PHY-1001 : ==== DR Iter 38 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 38; 1.017123s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (99.9%)

PHY-1001 : ===== DR Iter 39 =====
PHY-1022 : len = 1.7623e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 39; 0.175552s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (97.9%)

PHY-1001 : ==== DR Iter 40 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 40; 0.182521s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (94.2%)

PHY-1001 : ==== DR Iter 41 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 41; 0.258062s wall, 0.312500s user + 0.031250s system = 0.343750s CPU (133.2%)

PHY-1001 : ==== DR Iter 42 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 42; 0.235801s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (99.4%)

PHY-1001 : ==== DR Iter 43 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 43; 0.339757s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (101.2%)

PHY-1001 : ==== DR Iter 44 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 44; 0.386059s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (97.1%)

PHY-1001 : ==== DR Iter 45 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 45; 1.017898s wall, 1.046875s user + 0.015625s system = 1.062500s CPU (104.4%)

PHY-1001 : ==== DR Iter 46 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 46; 1.019577s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (98.1%)

PHY-1001 : ==== DR Iter 47 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 47; 1.012935s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (100.3%)

PHY-1001 : ==== DR Iter 48 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 48; 1.064430s wall, 1.062500s user + 0.000000s system = 1.062500s CPU (99.8%)

PHY-1001 : ===== DR Iter 49 =====
PHY-1022 : len = 1.7623e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 49; 0.177531s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (105.6%)

PHY-1001 : ==== DR Iter 50 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 50; 0.183790s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (93.5%)

PHY-1001 : ==== DR Iter 51 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 51; 0.226544s wall, 0.250000s user + 0.062500s system = 0.312500s CPU (137.9%)

PHY-1001 : ==== DR Iter 52 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 52; 0.245237s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (95.6%)

PHY-1001 : ==== DR Iter 53 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 53; 0.350031s wall, 0.343750s user + 0.031250s system = 0.375000s CPU (107.1%)

PHY-1001 : ==== DR Iter 54 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 54; 0.397247s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (98.3%)

PHY-1001 : ==== DR Iter 55 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 55; 1.143369s wall, 1.156250s user + 0.000000s system = 1.156250s CPU (101.1%)

PHY-1001 : ==== DR Iter 56 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 56; 1.025967s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (97.5%)

PHY-1001 : ==== DR Iter 57 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 57; 1.032225s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (99.9%)

PHY-1001 : ==== DR Iter 58 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 58; 1.049950s wall, 1.046875s user + 0.000000s system = 1.046875s CPU (99.7%)

PHY-1001 : ==== DR Iter 59 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 59; 1.029606s wall, 1.046875s user + 0.000000s system = 1.046875s CPU (101.7%)

PHY-1001 : ===== DR Iter 60 =====
PHY-1022 : len = 1.76227e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 60; 0.178497s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (96.3%)

PHY-1001 : ==== DR Iter 61 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 61; 0.181414s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (103.4%)

PHY-1001 : ==== DR Iter 62 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 62; 0.221397s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (98.8%)

PHY-1001 : ==== DR Iter 63 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 63; 0.237046s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (92.3%)

PHY-1001 : ==== DR Iter 64 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 64; 0.350769s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (98.0%)

PHY-1001 : ==== DR Iter 65 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 65; 0.384088s wall, 0.406250s user + 0.015625s system = 0.421875s CPU (109.8%)

PHY-1001 : ==== DR Iter 66 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 66; 1.095174s wall, 1.093750s user + 0.000000s system = 1.093750s CPU (99.9%)

PHY-1001 : ==== DR Iter 67 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 67; 1.046484s wall, 1.046875s user + 0.000000s system = 1.046875s CPU (100.0%)

PHY-1001 : ==== DR Iter 68 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 68; 1.018920s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (99.7%)

PHY-1001 : ==== DR Iter 69 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 69; 1.043164s wall, 1.046875s user + 0.000000s system = 1.046875s CPU (100.4%)

PHY-1001 : ==== DR Iter 70 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 70; 1.030278s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (98.6%)

PHY-1001 : ==== DR Iter 71 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 71; 1.017615s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (99.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18382(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.450   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.232   |  -16.646  |  10   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.658568s wall, 3.656250s user + 0.000000s system = 3.656250s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 1052, reserve = 1047, peak = 1052.
PHY-1001 : End phase 4; 38.750893s wall, 38.812500s user + 0.281250s system = 39.093750s CPU (100.9%)

PHY-1003 : Routed, final wirelength = 1.76226e+06
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 479 feed throughs used by 380 nets
PHY-1001 : Current memory(MB): used = 1139, reserve = 1137, peak = 1139.
PHY-1001 : End export database. 2.360592s wall, 2.343750s user + 0.015625s system = 2.359375s CPU (99.9%)

PHY-1001 : Fixing routing violation through ECO...
RUN-1002 : start command "place -eco"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.5_SP3/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8232 instances
RUN-1001 : 4066 mslices, 4052 lslices, 61 pads, 48 brams, 0 dsps
RUN-1001 : There are total 19591 nets
RUN-1001 : 13454 nets have 2 pins
RUN-1001 : 4702 nets have [3 - 5] pins
RUN-1001 : 869 nets have [6 - 10] pins
RUN-1001 : 398 nets have [11 - 20] pins
RUN-1001 : 159 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |      0      
RUN-1001 :   No   |  No   |  Yes  |      0      
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |      0      
RUN-1001 :   Yes  |  No   |  Yes  |      0      
RUN-1001 :   Yes  |  Yes  |  No   |      0      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |   1   |     1      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 0
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 61 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: (2 7 3) is for feedthrough
PHY-3001 : eco cells: (2 44 1) is for feedthrough
PHY-3001 : eco cells: (2 53 2) is for feedthrough
PHY-3001 : eco cells: (3 6 0) is for feedthrough
PHY-3001 : eco cells: (3 6 3) is for feedthrough
PHY-3001 : eco cells: (3 7 2) is for feedthrough
PHY-3001 : eco cells: (3 39 2) is for feedthrough
PHY-3001 : eco cells: (3 55 0) is for feedthrough
PHY-3001 : eco cells: (3 64 2) is for feedthrough
PHY-3001 : eco cells: (3 65 1) is for feedthrough
PHY-3001 : eco cells: (4 6 2) is for feedthrough
PHY-3001 : eco cells: (4 7 0) is for feedthrough
PHY-3001 : eco cells: (4 7 3) is for feedthrough
PHY-3001 : eco cells: (4 12 0) is for feedthrough
PHY-3001 : eco cells: (4 44 2) is for feedthrough
PHY-3001 : eco cells: (4 46 0) is for feedthrough
PHY-3001 : eco cells: (5 39 1) is for feedthrough
PHY-3001 : eco cells: (5 44 0) is for feedthrough
PHY-3001 : eco cells: (5 44 1) is for feedthrough
PHY-3001 : eco cells: (5 45 1) is for feedthrough
PHY-3001 : eco cells: (5 58 2) is for feedthrough
PHY-3001 : eco cells: (5 68 3) is for feedthrough
PHY-3001 : eco cells: (6 8 1) is for feedthrough
PHY-3001 : eco cells: (6 9 0) is for feedthrough
PHY-3001 : eco cells: (6 10 3) is for feedthrough
PHY-3001 : eco cells: (6 18 0) is for feedthrough
PHY-3001 : eco cells: (6 19 0) is for feedthrough
PHY-3001 : eco cells: (6 38 0) is for feedthrough
PHY-3001 : eco cells: (6 43 0) is for feedthrough
PHY-3001 : eco cells: (6 46 1) is for feedthrough
PHY-3001 : eco cells: (7 11 2) is for feedthrough
PHY-3001 : eco cells: (7 19 0) is for feedthrough
PHY-3001 : eco cells: (7 25 3) is for feedthrough
PHY-3001 : eco cells: (7 45 3) is for feedthrough
PHY-3001 : eco cells: (7 52 0) is for feedthrough
PHY-3001 : eco cells: (9 13 0) is for feedthrough
PHY-3001 : eco cells: (9 14 3) is for feedthrough
PHY-3001 : eco cells: (9 16 1) is for feedthrough
PHY-3001 : eco cells: (9 17 0) is for feedthrough
PHY-3001 : eco cells: (9 18 1) is for feedthrough
PHY-3001 : eco cells: (9 19 1) is for feedthrough
PHY-3001 : eco cells: (9 19 2) is for feedthrough
PHY-3001 : eco cells: (9 20 2) is for feedthrough
PHY-3001 : eco cells: (9 24 3) is for feedthrough
PHY-3001 : eco cells: (9 44 0) is for feedthrough
PHY-3001 : eco cells: (9 66 0) is for feedthrough
PHY-3001 : eco cells: (10 10 2) is for feedthrough
PHY-3001 : eco cells: (10 13 0) is for feedthrough
PHY-3001 : eco cells: (10 14 2) is for feedthrough
PHY-3001 : eco cells: (10 16 0) is for feedthrough
PHY-3001 : eco cells: (10 18 0) is for feedthrough
PHY-3001 : eco cells: (10 18 1) is for feedthrough
PHY-3001 : eco cells: (10 19 1) is for feedthrough
PHY-3001 : eco cells: (10 20 3) is for feedthrough
PHY-3001 : eco cells: (10 22 2) is for feedthrough
PHY-3001 : eco cells: (10 26 1) is for feedthrough
PHY-3001 : eco cells: (11 6 1) is for feedthrough
PHY-3001 : eco cells: (11 14 0) is for feedthrough
PHY-3001 : eco cells: (11 14 1) is for feedthrough
PHY-3001 : eco cells: (11 15 0) is for feedthrough
PHY-3001 : eco cells: (11 16 3) is for feedthrough
PHY-3001 : eco cells: (11 20 0) is for feedthrough
PHY-3001 : eco cells: (11 20 3) is for feedthrough
PHY-3001 : eco cells: (11 21 0) is for feedthrough
PHY-3001 : eco cells: (11 23 2) is for feedthrough
PHY-3001 : eco cells: (11 29 2) is for feedthrough
PHY-3001 : eco cells: (11 40 1) is for feedthrough
PHY-3001 : eco cells: (12 1 3) is for feedthrough
PHY-3001 : eco cells: (12 4 0) is for feedthrough
PHY-3001 : eco cells: (12 15 0) is for feedthrough
PHY-3001 : eco cells: (12 15 1) is for feedthrough
PHY-3001 : eco cells: (12 16 0) is for feedthrough
PHY-3001 : eco cells: (12 17 0) is for feedthrough
PHY-3001 : eco cells: (12 18 0) is for feedthrough
PHY-3001 : eco cells: (12 21 3) is for feedthrough
PHY-3001 : eco cells: (12 22 3) is for feedthrough
PHY-3001 : eco cells: (12 23 0) is for feedthrough
PHY-3001 : eco cells: (12 24 1) is for feedthrough
PHY-3001 : eco cells: (12 25 3) is for feedthrough
PHY-3001 : eco cells: (12 58 2) is for feedthrough
PHY-3001 : eco cells: (13 3 0) is for feedthrough
PHY-3001 : eco cells: (13 3 1) is for feedthrough
PHY-3001 : eco cells: (13 4 1) is for feedthrough
PHY-3001 : eco cells: (13 13 2) is for feedthrough
PHY-3001 : eco cells: (13 15 1) is for feedthrough
PHY-3001 : eco cells: (13 21 0) is for feedthrough
PHY-3001 : eco cells: (13 21 2) is for feedthrough
PHY-3001 : eco cells: (13 25 2) is for feedthrough
PHY-3001 : eco cells: (13 26 2) is for feedthrough
PHY-3001 : eco cells: (13 27 0) is for feedthrough
PHY-3001 : eco cells: (13 48 3) is for feedthrough
PHY-3001 : eco cells: (13 64 0) is for feedthrough
PHY-3001 : eco cells: (14 6 1) is for feedthrough
PHY-3001 : eco cells: (14 13 0) is for feedthrough
PHY-3001 : eco cells: (14 13 3) is for feedthrough
PHY-3001 : eco cells: (14 15 2) is for feedthrough
PHY-3001 : eco cells: (14 20 1) is for feedthrough
PHY-3001 : eco cells: (14 22 3) is for feedthrough
PHY-3001 : eco cells: (14 24 1) is for feedthrough
PHY-3001 : eco cells: (14 44 2) is for feedthrough
PHY-3001 : eco cells: (14 56 3) is for feedthrough
PHY-3001 : eco cells: (14 64 1) is for feedthrough
PHY-3001 : eco cells: (15 11 2) is for feedthrough
PHY-3001 : eco cells: (15 16 1) is for feedthrough
PHY-3001 : eco cells: (15 18 1) is for feedthrough
PHY-3001 : eco cells: (15 23 2) is for feedthrough
PHY-3001 : eco cells: (15 30 1) is for feedthrough
PHY-3001 : eco cells: (15 30 2) is for feedthrough
PHY-3001 : eco cells: (17 13 1) is for feedthrough
PHY-3001 : eco cells: (17 15 2) is for feedthrough
PHY-3001 : eco cells: (17 16 0) is for feedthrough
PHY-3001 : eco cells: (17 18 0) is for feedthrough
PHY-3001 : eco cells: (17 21 2) is for feedthrough
PHY-3001 : eco cells: (17 24 0) is for feedthrough
PHY-3001 : eco cells: (17 24 2) is for feedthrough
PHY-3001 : eco cells: (17 59 0) is for feedthrough
PHY-3001 : eco cells: (18 14 0) is for feedthrough
PHY-3001 : eco cells: (18 15 0) is for feedthrough
PHY-3001 : eco cells: (18 16 0) is for feedthrough
PHY-3001 : eco cells: (18 16 2) is for feedthrough
PHY-3001 : eco cells: (18 17 2) is for feedthrough
PHY-3001 : eco cells: (18 19 3) is for feedthrough
PHY-3001 : eco cells: (18 20 1) is for feedthrough
PHY-3001 : eco cells: (18 21 0) is for feedthrough
PHY-3001 : eco cells: (18 22 1) is for feedthrough
PHY-3001 : eco cells: (18 23 1) is for feedthrough
PHY-3001 : eco cells: (18 24 1) is for feedthrough
PHY-3001 : eco cells: (18 25 1) is for feedthrough
PHY-3001 : eco cells: (18 25 2) is for feedthrough
PHY-3001 : eco cells: (18 41 2) is for feedthrough
PHY-3001 : eco cells: (18 47 3) is for feedthrough
PHY-3001 : eco cells: (18 55 2) is for feedthrough
PHY-3001 : eco cells: (19 6 3) is for feedthrough
PHY-3001 : eco cells: (19 17 3) is for feedthrough
PHY-3001 : eco cells: (19 19 0) is for feedthrough
PHY-3001 : eco cells: (19 21 0) is for feedthrough
PHY-3001 : eco cells: (19 21 2) is for feedthrough
PHY-3001 : eco cells: (19 23 0) is for feedthrough
PHY-3001 : eco cells: (19 24 1) is for feedthrough
PHY-3001 : eco cells: (19 25 1) is for feedthrough
PHY-3001 : eco cells: (19 26 1) is for feedthrough
PHY-3001 : eco cells: (19 41 3) is for feedthrough
PHY-3001 : eco cells: (20 13 1) is for feedthrough
PHY-3001 : eco cells: (20 14 3) is for feedthrough
PHY-3001 : eco cells: (20 15 1) is for feedthrough
PHY-3001 : eco cells: (20 15 3) is for feedthrough
PHY-3001 : eco cells: (20 18 2) is for feedthrough
PHY-3001 : eco cells: (20 19 2) is for feedthrough
PHY-3001 : eco cells: (20 21 2) is for feedthrough
PHY-3001 : eco cells: (20 22 2) is for feedthrough
PHY-3001 : eco cells: (20 23 1) is for feedthrough
PHY-3001 : eco cells: (20 25 0) is for feedthrough
PHY-3001 : eco cells: (20 28 0) is for feedthrough
PHY-3001 : eco cells: (20 38 2) is for feedthrough
PHY-3001 : eco cells: (20 42 2) is for feedthrough
PHY-3001 : eco cells: (20 42 3) is for feedthrough
PHY-3001 : eco cells: (20 47 2) is for feedthrough
PHY-3001 : eco cells: (20 52 2) is for feedthrough
PHY-3001 : eco cells: (20 54 1) is for feedthrough
PHY-3001 : eco cells: (20 56 0) is for feedthrough
PHY-3001 : eco cells: (20 63 2) is for feedthrough
PHY-3001 : eco cells: (20 67 1) is for feedthrough
PHY-3001 : eco cells: (21 10 1) is for feedthrough
PHY-3001 : eco cells: (21 12 1) is for feedthrough
PHY-3001 : eco cells: (21 13 0) is for feedthrough
PHY-3001 : eco cells: (21 16 3) is for feedthrough
PHY-3001 : eco cells: (21 18 0) is for feedthrough
PHY-3001 : eco cells: (21 18 1) is for feedthrough
PHY-3001 : eco cells: (21 19 3) is for feedthrough
PHY-3001 : eco cells: (21 23 0) is for feedthrough
PHY-3001 : eco cells: (21 23 1) is for feedthrough
PHY-3001 : eco cells: (21 24 1) is for feedthrough
PHY-3001 : eco cells: (21 24 3) is for feedthrough
PHY-3001 : eco cells: (21 25 1) is for feedthrough
PHY-3001 : eco cells: (21 25 3) is for feedthrough
PHY-3001 : eco cells: (21 40 3) is for feedthrough
PHY-3001 : eco cells: (21 66 1) is for feedthrough
PHY-3001 : eco cells: (21 67 1) is for feedthrough
PHY-3001 : eco cells: (21 70 0) is for feedthrough
PHY-3001 : eco cells: (22 10 2) is for feedthrough
PHY-3001 : eco cells: (22 11 1) is for feedthrough
PHY-3001 : eco cells: (22 12 0) is for feedthrough
PHY-3001 : eco cells: (22 13 0) is for feedthrough
PHY-3001 : eco cells: (22 16 0) is for feedthrough
PHY-3001 : eco cells: (22 17 3) is for feedthrough
PHY-3001 : eco cells: (22 18 2) is for feedthrough
PHY-3001 : eco cells: (22 18 3) is for feedthrough
PHY-3001 : eco cells: (22 19 1) is for feedthrough
PHY-3001 : eco cells: (22 20 3) is for feedthrough
PHY-3001 : eco cells: (22 22 1) is for feedthrough
PHY-3001 : eco cells: (22 23 1) is for feedthrough
PHY-3001 : eco cells: (22 23 2) is for feedthrough
PHY-3001 : eco cells: (22 23 3) is for feedthrough
PHY-3001 : eco cells: (22 24 1) is for feedthrough
PHY-3001 : eco cells: (22 28 0) is for feedthrough
PHY-3001 : eco cells: (22 31 0) is for feedthrough
PHY-3001 : eco cells: (22 32 0) is for feedthrough
PHY-3001 : eco cells: (22 47 0) is for feedthrough
PHY-3001 : eco cells: (22 68 2) is for feedthrough
PHY-3001 : eco cells: (23 13 0) is for feedthrough
PHY-3001 : eco cells: (23 19 0) is for feedthrough
PHY-3001 : eco cells: (23 21 2) is for feedthrough
PHY-3001 : eco cells: (23 24 3) is for feedthrough
PHY-3001 : eco cells: (23 66 1) is for feedthrough
PHY-3001 : eco cells: (23 68 2) is for feedthrough
PHY-3001 : eco cells: (25 9 2) is for feedthrough
PHY-3001 : eco cells: (25 10 1) is for feedthrough
PHY-3001 : eco cells: (25 10 2) is for feedthrough
PHY-3001 : eco cells: (25 11 0) is for feedthrough
PHY-3001 : eco cells: (25 11 2) is for feedthrough
PHY-3001 : eco cells: (25 13 0) is for feedthrough
PHY-3001 : eco cells: (25 14 2) is for feedthrough
PHY-3001 : eco cells: (25 16 0) is for feedthrough
PHY-3001 : eco cells: (25 17 3) is for feedthrough
PHY-3001 : eco cells: (25 20 3) is for feedthrough
PHY-3001 : eco cells: (25 21 0) is for feedthrough
PHY-3001 : eco cells: (25 21 1) is for feedthrough
PHY-3001 : eco cells: (25 22 0) is for feedthrough
PHY-3001 : eco cells: (25 23 0) is for feedthrough
PHY-3001 : eco cells: (25 24 0) is for feedthrough
PHY-3001 : eco cells: (25 28 2) is for feedthrough
PHY-3001 : eco cells: (25 55 3) is for feedthrough
PHY-3001 : eco cells: (25 57 1) is for feedthrough
PHY-3001 : eco cells: (25 57 3) is for feedthrough
PHY-3001 : eco cells: (25 66 0) is for feedthrough
PHY-3001 : eco cells: (25 67 3) is for feedthrough
PHY-3001 : eco cells: (26 9 0) is for feedthrough
PHY-3001 : eco cells: (26 10 1) is for feedthrough
PHY-3001 : eco cells: (26 11 1) is for feedthrough
PHY-3001 : eco cells: (26 12 0) is for feedthrough
PHY-3001 : eco cells: (26 12 1) is for feedthrough
PHY-3001 : eco cells: (26 13 1) is for feedthrough
PHY-3001 : eco cells: (26 14 0) is for feedthrough
PHY-3001 : eco cells: (26 15 0) is for feedthrough
PHY-3001 : eco cells: (26 15 1) is for feedthrough
PHY-3001 : eco cells: (26 15 3) is for feedthrough
PHY-3001 : eco cells: (26 18 0) is for feedthrough
PHY-3001 : eco cells: (26 20 1) is for feedthrough
PHY-3001 : eco cells: (26 21 1) is for feedthrough
PHY-3001 : eco cells: (26 21 2) is for feedthrough
PHY-3001 : eco cells: (26 22 1) is for feedthrough
PHY-3001 : eco cells: (26 24 1) is for feedthrough
PHY-3001 : eco cells: (26 25 2) is for feedthrough
PHY-3001 : eco cells: (26 31 0) is for feedthrough
PHY-3001 : eco cells: (26 48 1) is for feedthrough
PHY-3001 : eco cells: (26 57 0) is for feedthrough
PHY-3001 : eco cells: (26 57 3) is for feedthrough
PHY-3001 : eco cells: (26 60 0) is for feedthrough
PHY-3001 : eco cells: (26 63 2) is for feedthrough
PHY-3001 : eco cells: (26 67 1) is for feedthrough
PHY-3001 : eco cells: (27 4 3) is for feedthrough
PHY-3001 : eco cells: (27 10 0) is for feedthrough
PHY-3001 : eco cells: (27 11 1) is for feedthrough
PHY-3001 : eco cells: (27 12 0) is for feedthrough
PHY-3001 : eco cells: (27 12 1) is for feedthrough
PHY-3001 : eco cells: (27 12 3) is for feedthrough
PHY-3001 : eco cells: (27 13 0) is for feedthrough
PHY-3001 : eco cells: (27 13 2) is for feedthrough
PHY-3001 : eco cells: (27 14 1) is for feedthrough
PHY-3001 : eco cells: (27 15 0) is for feedthrough
PHY-3001 : eco cells: (27 16 1) is for feedthrough
PHY-3001 : eco cells: (27 16 2) is for feedthrough
PHY-3001 : eco cells: (27 17 2) is for feedthrough
PHY-3001 : eco cells: (27 19 2) is for feedthrough
PHY-3001 : eco cells: (27 21 0) is for feedthrough
PHY-3001 : eco cells: (27 21 2) is for feedthrough
PHY-3001 : eco cells: (27 22 0) is for feedthrough
PHY-3001 : eco cells: (27 22 1) is for feedthrough
PHY-3001 : eco cells: (27 23 3) is for feedthrough
PHY-3001 : eco cells: (27 24 0) is for feedthrough
PHY-3001 : eco cells: (27 24 1) is for feedthrough
PHY-3001 : eco cells: (27 28 3) is for feedthrough
PHY-3001 : eco cells: (27 38 2) is for feedthrough
PHY-3001 : eco cells: (27 43 0) is for feedthrough
PHY-3001 : eco cells: (27 43 1) is for feedthrough
PHY-3001 : eco cells: (27 44 0) is for feedthrough
PHY-3001 : eco cells: (27 56 0) is for feedthrough
PHY-3001 : eco cells: (27 62 1) is for feedthrough
PHY-3001 : eco cells: (27 64 0) is for feedthrough
PHY-3001 : eco cells: (27 65 1) is for feedthrough
PHY-3001 : eco cells: (27 66 0) is for feedthrough
PHY-3001 : eco cells: (27 68 0) is for feedthrough
PHY-3001 : eco cells: (27 69 0) is for feedthrough
PHY-3001 : eco cells: (28 7 3) is for feedthrough
PHY-3001 : eco cells: (28 10 1) is for feedthrough
PHY-3001 : eco cells: (28 11 1) is for feedthrough
PHY-3001 : eco cells: (28 11 2) is for feedthrough
PHY-3001 : eco cells: (28 12 0) is for feedthrough
PHY-3001 : eco cells: (28 12 1) is for feedthrough
PHY-3001 : eco cells: (28 13 2) is for feedthrough
PHY-3001 : eco cells: (28 13 3) is for feedthrough
PHY-3001 : eco cells: (28 16 0) is for feedthrough
PHY-3001 : eco cells: (28 18 0) is for feedthrough
PHY-3001 : eco cells: (28 19 1) is for feedthrough
PHY-3001 : eco cells: (28 20 3) is for feedthrough
PHY-3001 : eco cells: (28 21 0) is for feedthrough
PHY-3001 : eco cells: (28 21 2) is for feedthrough
PHY-3001 : eco cells: (28 22 0) is for feedthrough
PHY-3001 : eco cells: (28 23 0) is for feedthrough
PHY-3001 : eco cells: (28 30 0) is for feedthrough
PHY-3001 : eco cells: (28 38 3) is for feedthrough
PHY-3001 : eco cells: (28 46 3) is for feedthrough
PHY-3001 : eco cells: (28 54 3) is for feedthrough
PHY-3001 : eco cells: (28 57 3) is for feedthrough
PHY-3001 : eco cells: (28 62 0) is for feedthrough
PHY-3001 : eco cells: (28 64 0) is for feedthrough
PHY-3001 : eco cells: (29 6 3) is for feedthrough
PHY-3001 : eco cells: (29 8 2) is for feedthrough
PHY-3001 : eco cells: (29 9 3) is for feedthrough
PHY-3001 : eco cells: (29 11 0) is for feedthrough
PHY-3001 : eco cells: (29 11 2) is for feedthrough
PHY-3001 : eco cells: (29 12 0) is for feedthrough
PHY-3001 : eco cells: (29 13 1) is for feedthrough
PHY-3001 : eco cells: (29 13 2) is for feedthrough
PHY-3001 : eco cells: (29 13 3) is for feedthrough
PHY-3001 : eco cells: (29 14 2) is for feedthrough
PHY-3001 : eco cells: (29 15 2) is for feedthrough
PHY-3001 : eco cells: (29 16 0) is for feedthrough
PHY-3001 : eco cells: (29 16 2) is for feedthrough
PHY-3001 : eco cells: (29 17 1) is for feedthrough
PHY-3001 : eco cells: (29 20 3) is for feedthrough
PHY-3001 : eco cells: (29 21 2) is for feedthrough
PHY-3001 : eco cells: (29 22 2) is for feedthrough
PHY-3001 : eco cells: (29 22 3) is for feedthrough
PHY-3001 : eco cells: (29 23 1) is for feedthrough
PHY-3001 : eco cells: (29 24 3) is for feedthrough
PHY-3001 : eco cells: (29 37 3) is for feedthrough
PHY-3001 : eco cells: (29 44 3) is for feedthrough
PHY-3001 : eco cells: (29 64 2) is for feedthrough
PHY-3001 : eco cells: (30 9 3) is for feedthrough
PHY-3001 : eco cells: (30 12 1) is for feedthrough
PHY-3001 : eco cells: (30 13 0) is for feedthrough
PHY-3001 : eco cells: (30 13 3) is for feedthrough
PHY-3001 : eco cells: (30 14 1) is for feedthrough
PHY-3001 : eco cells: (30 16 1) is for feedthrough
PHY-3001 : eco cells: (30 17 2) is for feedthrough
PHY-3001 : eco cells: (30 20 0) is for feedthrough
PHY-3001 : eco cells: (30 21 3) is for feedthrough
PHY-3001 : eco cells: (30 22 1) is for feedthrough
PHY-3001 : eco cells: (30 44 1) is for feedthrough
PHY-3001 : eco cells: (30 66 3) is for feedthrough
PHY-3001 : eco cells: (31 12 0) is for feedthrough
PHY-3001 : eco cells: (31 12 2) is for feedthrough
PHY-3001 : eco cells: (31 13 1) is for feedthrough
PHY-3001 : eco cells: (31 19 2) is for feedthrough
PHY-3001 : eco cells: (31 21 1) is for feedthrough
PHY-3001 : eco cells: (31 28 1) is for feedthrough
PHY-3001 : eco cells: (31 30 1) is for feedthrough
PHY-3001 : eco cells: (31 43 1) is for feedthrough
PHY-3001 : eco cells: (31 44 1) is for feedthrough
PHY-3001 : eco cells: (31 63 1) is for feedthrough
PHY-3001 : eco cells: (33 14 0) is for feedthrough
PHY-3001 : eco cells: (33 14 1) is for feedthrough
PHY-3001 : eco cells: (33 17 1) is for feedthrough
PHY-3001 : eco cells: (33 23 3) is for feedthrough
PHY-3001 : eco cells: (33 29 1) is for feedthrough
PHY-3001 : eco cells: (33 30 3) is for feedthrough
PHY-3001 : eco cells: (33 33 3) is for feedthrough
PHY-3001 : eco cells: (33 43 1) is for feedthrough
PHY-3001 : eco cells: (33 49 0) is for feedthrough
PHY-3001 : eco cells: (33 52 0) is for feedthrough
PHY-3001 : eco cells: (33 53 1) is for feedthrough
PHY-3001 : eco cells: (33 56 2) is for feedthrough
PHY-3001 : eco cells: (33 56 3) is for feedthrough
PHY-3001 : eco cells: (33 63 1) is for feedthrough
PHY-3001 : eco cells: (34 14 2) is for feedthrough
PHY-3001 : eco cells: (34 16 1) is for feedthrough
PHY-3001 : eco cells: (34 17 1) is for feedthrough
PHY-3001 : eco cells: (34 27 3) is for feedthrough
PHY-3001 : eco cells: (34 32 3) is for feedthrough
PHY-3001 : eco cells: (34 61 1) is for feedthrough
PHY-3001 : eco cells: (34 62 1) is for feedthrough
PHY-3001 : eco cells: (34 66 1) is for feedthrough
PHY-3001 : eco cells: (35 11 0) is for feedthrough
PHY-3001 : eco cells: (35 15 2) is for feedthrough
PHY-3001 : eco cells: (35 18 3) is for feedthrough
PHY-3001 : eco cells: (35 23 2) is for feedthrough
PHY-3001 : eco cells: (35 29 1) is for feedthrough
PHY-3001 : eco cells: (35 41 1) is for feedthrough
PHY-3001 : eco cells: (35 45 2) is for feedthrough
PHY-3001 : eco cells: (35 46 1) is for feedthrough
PHY-3001 : eco cells: (35 50 3) is for feedthrough
PHY-3001 : eco cells: (35 56 1) is for feedthrough
PHY-3001 : eco cells: (35 56 3) is for feedthrough
PHY-3001 : eco cells: (36 13 0) is for feedthrough
PHY-3001 : eco cells: (36 14 1) is for feedthrough
PHY-3001 : eco cells: (36 16 3) is for feedthrough
PHY-3001 : eco cells: (36 32 2) is for feedthrough
PHY-3001 : eco cells: (36 42 3) is for feedthrough
PHY-3001 : eco cells: (36 47 1) is for feedthrough
PHY-3001 : eco cells: (36 48 0) is for feedthrough
PHY-3001 : eco cells: (36 50 0) is for feedthrough
PHY-3001 : eco cells: (36 50 1) is for feedthrough
PHY-3001 : eco cells: (36 54 2) is for feedthrough
PHY-3001 : eco cells: (36 57 1) is for feedthrough
PHY-3001 : eco cells: (37 10 1) is for feedthrough
PHY-3001 : eco cells: (37 13 1) is for feedthrough
PHY-3001 : eco cells: (37 13 2) is for feedthrough
PHY-3001 : eco cells: (37 26 1) is for feedthrough
PHY-3001 : eco cells: (37 43 3) is for feedthrough
PHY-3001 : eco cells: (37 44 0) is for feedthrough
PHY-3001 : eco cells: (37 45 0) is for feedthrough
PHY-3001 : eco cells: (37 45 1) is for feedthrough
PHY-3001 : eco cells: (37 47 1) is for feedthrough
PHY-3001 : eco cells: (37 48 0) is for feedthrough
PHY-3001 : eco cells: (37 52 1) is for feedthrough
PHY-3001 : eco cells: (37 53 2) is for feedthrough
PHY-3001 : eco cells: (37 55 0) is for feedthrough
PHY-3001 : eco cells: (38 8 3) is for feedthrough
PHY-3001 : eco cells: (38 17 2) is for feedthrough
PHY-3001 : eco cells: (38 33 1) is for feedthrough
PHY-3001 : eco cells: (38 46 2) is for feedthrough
PHY-3001 : eco cells: (38 47 0) is for feedthrough
PHY-3001 : eco cells: (38 50 1) is for feedthrough
PHY-3001 : eco cells: (38 51 3) is for feedthrough
PHY-3001 : eco cells: (38 52 0) is for feedthrough
PHY-3001 : eco cells: (38 58 2) is for feedthrough
PHY-3001 : eco cells: (38 63 2) is for feedthrough
PHY-3001 : eco cells: (38 66 0) is for feedthrough
PHY-3001 : eco cells: 8166 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 8230 instances, 8118 slices, 284 macros(1430 instances: 939 mslices 491 lslices)
PHY-3001 : Start timing update ...
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 19589 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.784702s wall, 1.781250s user + 0.000000s system = 1.781250s CPU (99.8%)

PHY-3001 : End placement; No cells to be placed.
RUN-1003 : finish command "place -eco" in  2.338593s wall, 2.375000s user + 0.000000s system = 2.375000s CPU (101.6%)

RUN-1004 : used memory is 1131 MB, reserved memory is 1129 MB, peak memory is 1139 MB
RUN-1001 : Eco place succeeded
RUN-1002 : start command "route -eco"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.5_SP3/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8232 instances
RUN-1001 : 4066 mslices, 4052 lslices, 61 pads, 48 brams, 0 dsps
RUN-1001 : There are total 19591 nets
RUN-1001 : 13454 nets have 2 pins
RUN-1001 : 4702 nets have [3 - 5] pins
RUN-1001 : 869 nets have [6 - 10] pins
RUN-1001 : 398 nets have [11 - 20] pins
RUN-1001 : 159 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-1001 : 4066 mslices, 4052 lslices, 61 pads, 48 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19589 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 3 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 8 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 1139, reserve = 1137, peak = 1139.
PHY-1001 : Detailed router is running in eco mode.
PHY-1001 : Refresh detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net DATA/DIV_Dtemp/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : eco open net = 0
PHY-1001 : Current memory(MB): used = 1148, reserve = 1146, peak = 1148.
PHY-1001 : End build detailed router design. 1.935265s wall, 1.921875s user + 0.000000s system = 1.921875s CPU (99.3%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End initial clock net routed; 0.019095s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (163.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.026585s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (58.8%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.027234s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (57.4%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.027412s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (114.0%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.026616s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (117.4%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.027016s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (115.7%)

PHY-1001 : Current memory(MB): used = 1148, reserve = 1146, peak = 1148.
PHY-1001 : End phase 1; 0.188372s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (99.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 1148, reserve = 1146, peak = 1148.
PHY-1001 : End initial routed; 0.147698s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (105.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18382(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.450   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.232   |  -16.646  |  10   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.627717s wall, 3.625000s user + 0.000000s system = 3.625000s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 1148, reserve = 1145, peak = 1148.
PHY-1001 : End phase 2; 3.775581s wall, 3.781250s user + 0.000000s system = 3.781250s CPU (100.2%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.252595s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (105.2%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.136377s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (91.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.136468s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (91.6%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.136834s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (102.8%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.135862s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (103.5%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.135369s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (103.9%)

PHY-1001 : ===== DR Iter 6 =====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 6; 0.137305s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (102.4%)

PHY-1001 : ===== DR Iter 7 =====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 7; 0.141274s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (99.5%)

PHY-1001 : ==== DR Iter 8 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 8; 0.136207s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (103.2%)

PHY-1001 : ==== DR Iter 9 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 9; 0.135696s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (92.1%)

PHY-1001 : ==== DR Iter 10 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 10; 0.136007s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (114.9%)

PHY-1001 : ==== DR Iter 11 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 11; 0.135089s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (104.1%)

PHY-1001 : ===== DR Iter 12 =====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 12; 0.144253s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (108.3%)

PHY-1001 : ==== DR Iter 13 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 13; 0.137250s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (125.2%)

PHY-1001 : ==== DR Iter 14 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 14; 0.136251s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (103.2%)

PHY-1001 : ==== DR Iter 15 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 15; 0.132588s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (94.3%)

PHY-1001 : ==== DR Iter 16 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 16; 0.128316s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (133.9%)

PHY-1001 : ==== DR Iter 17 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 17; 0.127720s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (134.6%)

PHY-1001 : ===== DR Iter 18 =====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 18; 0.144331s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (108.3%)

PHY-1001 : ==== DR Iter 19 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 19; 0.139055s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (134.8%)

PHY-1001 : ==== DR Iter 20 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 20; 0.134370s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (104.7%)

PHY-1001 : ==== DR Iter 21 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 21; 0.133163s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (93.9%)

PHY-1001 : ==== DR Iter 22 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 22; 0.135946s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (91.9%)

PHY-1001 : ==== DR Iter 23 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 23; 0.138567s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (101.5%)

PHY-1001 : ==== DR Iter 24 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 24; 0.133640s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (93.5%)

PHY-1001 : ===== DR Iter 25 =====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 25; 0.143520s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (108.9%)

PHY-1001 : ==== DR Iter 26 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 26; 0.136150s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (91.8%)

PHY-1001 : ==== DR Iter 27 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 27; 0.134025s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (116.6%)

PHY-1001 : ==== DR Iter 28 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 28; 0.138304s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (101.7%)

PHY-1001 : ==== DR Iter 29 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 29; 0.136441s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (103.1%)

PHY-1001 : ==== DR Iter 30 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 30; 0.138813s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (101.3%)

PHY-1001 : ==== DR Iter 31 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 31; 0.135864s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (92.0%)

PHY-1001 : ==== DR Iter 32 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 32; 0.137743s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (102.1%)

PHY-1001 : ===== DR Iter 33 =====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 33; 0.148337s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (94.8%)

PHY-1001 : ==== DR Iter 34 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 34; 0.139926s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (100.5%)

PHY-1001 : ==== DR Iter 35 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 35; 0.137604s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (102.2%)

PHY-1001 : ==== DR Iter 36 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 36; 0.137546s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (102.2%)

PHY-1001 : ==== DR Iter 37 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 37; 0.140487s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (100.1%)

PHY-1001 : ==== DR Iter 38 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 38; 0.139407s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (100.9%)

PHY-1001 : ==== DR Iter 39 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 39; 0.140709s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (99.9%)

PHY-1001 : ==== DR Iter 40 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 40; 0.138628s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (90.2%)

PHY-1001 : ==== DR Iter 41 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 41; 0.143674s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (108.8%)

PHY-1001 : ===== DR Iter 42 =====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 42; 0.142606s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (131.5%)

PHY-1001 : ==== DR Iter 43 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 43; 0.143708s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (97.9%)

PHY-1001 : ==== DR Iter 44 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 44; 0.140180s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (133.8%)

PHY-1001 : ==== DR Iter 45 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 45; 0.140096s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (100.4%)

PHY-1001 : ==== DR Iter 46 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 46; 0.138991s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (101.2%)

PHY-1001 : ==== DR Iter 47 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 47; 0.140774s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (99.9%)

PHY-1001 : ==== DR Iter 48 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 48; 0.138346s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (101.6%)

PHY-1001 : ==== DR Iter 49 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 49; 0.142672s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (98.6%)

PHY-1001 : ==== DR Iter 50 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 50; 0.139162s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (101.1%)

PHY-1001 : ==== DR Iter 51 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 51; 0.140267s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (100.3%)

PHY-1001 : ===== DR Iter 52 =====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 52; 0.148459s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (94.7%)

PHY-1001 : ==== DR Iter 53 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 53; 0.140483s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (100.1%)

PHY-1001 : ==== DR Iter 54 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 54; 0.141114s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (99.7%)

PHY-1001 : ==== DR Iter 55 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 55; 0.140083s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (100.4%)

PHY-1001 : ==== DR Iter 56 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 56; 0.140304s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (100.2%)

PHY-1001 : ==== DR Iter 57 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 57; 0.140736s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (99.9%)

PHY-1001 : ==== DR Iter 58 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 58; 0.141151s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (99.6%)

PHY-1001 : ==== DR Iter 59 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 59; 0.137719s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (102.1%)

PHY-1001 : ==== DR Iter 60 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 60; 0.138819s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (101.3%)

PHY-1001 : ==== DR Iter 61 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 61; 0.139323s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (100.9%)

PHY-1001 : ==== DR Iter 62 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 62; 0.138944s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (90.0%)

PHY-1001 : ===== DR Iter 63 =====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 63; 0.145899s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (107.1%)

PHY-1001 : ==== DR Iter 64 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 64; 0.141594s wall, 0.125000s user + 0.046875s system = 0.171875s CPU (121.4%)

PHY-1001 : ==== DR Iter 65 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 65; 0.138907s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (112.5%)

PHY-1001 : ==== DR Iter 66 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 66; 0.141173s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (110.7%)

PHY-1001 : ==== DR Iter 67 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 67; 0.141395s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (99.5%)

PHY-1001 : ==== DR Iter 68 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 68; 0.140505s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (100.1%)

PHY-1001 : ==== DR Iter 69 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 69; 0.139699s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (100.7%)

PHY-1001 : ==== DR Iter 70 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 70; 0.140536s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (100.1%)

PHY-1001 : ==== DR Iter 71 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 71; 0.138917s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (101.2%)

PHY-1001 : ==== DR Iter 72 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 72; 0.138771s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (101.3%)

PHY-1001 : ==== DR Iter 73 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 73; 0.140296s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (100.2%)

PHY-1001 : ==== DR Iter 74 ====
PHY-1022 : len = 1.76226e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 74; 0.139790s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (100.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18382(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.450   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.232   |  -16.646  |  10   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.871536s wall, 3.875000s user + 0.000000s system = 3.875000s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1147, reserve = 1145, peak = 1148.
PHY-1001 : End phase 3; 14.632058s wall, 14.828125s user + 0.171875s system = 15.000000s CPU (102.5%)

PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 479 feed throughs used by 380 nets
PHY-1001 : Current memory(MB): used = 1147, reserve = 1145, peak = 1148.
PHY-1001 : End export database. 2.463067s wall, 2.468750s user + 0.000000s system = 2.468750s CPU (100.2%)

PHY-1001 : Routing violations:
PHY-8023 ERROR: Location: (x25y24_n2beg4), nets: DATA/done_div_dup_50 COM2/uart_com2/sel8_syn_2
PHY-1001 : End of Routing Violations.
RUN-1003 : finish command "route -eco" in  24.361552s wall, 24.546875s user + 0.171875s system = 24.718750s CPU (101.5%)

RUN-1004 : used memory is 1137 MB, reserved memory is 1135 MB, peak memory is 1148 MB
RUN-8102 ERROR: Incremental route failed
PHY-1001 : Routing violations:
PHY-8023 ERROR: Location: (x25y24_n2beg4), nets: DATA/done_div_dup_50 COM2/uart_com2/sel8_syn_2
PHY-1001 : End of Routing Violations.
RUN-1003 : finish command "route" in  144.851990s wall, 177.656250s user + 1.031250s system = 178.687500s CPU (123.4%)

RUN-1004 : used memory is 1137 MB, reserved memory is 1135 MB, peak memory is 1148 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250428_175207.log"

============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Tue May  6 14:07:25 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(247)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(252)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(273)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(278)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(421)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(428)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(435)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(442)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(449)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(456)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(463)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(470)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(647)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(652)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(680)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(685)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(919)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(926)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(933)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(940)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(947)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(952)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(959)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(966)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(973)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(980)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(102)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 102 in ../../Src/INS600M-21A.v(106)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(516)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
RUN-1001 : Project manager successfully analyzed 21 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.205785s wall, 1.609375s user + 3.578125s system = 5.187500s CPU (99.6%)

RUN-1004 : used memory is 77 MB, reserved memory is 39 MB, peak memory is 89 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.720269s wall, 1.671875s user + 0.031250s system = 1.703125s CPU (99.0%)

RUN-1004 : used memory is 298 MB, reserved memory is 267 MB, peak memory is 301 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 7 view nodes, 47 trigger nets, 47 data nets.
KIT-1004 : Chipwatcher code = 0001111110010110
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=144) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=144) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=144)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=144)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=7,BUS_DIN_NUM=47,BUS_CTRL_NUM=122,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb0100,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01111,32'sb011111},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb0110010,32'sb01010110})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22880/31 useful/useless nets, 19664/17 useful/useless insts
SYN-1016 : Merged 36 instances.
SYN-1032 : 22505/22 useful/useless nets, 20124/18 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 4 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 4 mux instances.
SYN-1015 : Optimize round 1, 443 better
SYN-1014 : Optimize round 2
SYN-1032 : 22133/60 useful/useless nets, 19752/64 useful/useless insts
SYN-1015 : Optimize round 2, 128 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.270549s wall, 2.187500s user + 0.093750s system = 2.281250s CPU (100.5%)

RUN-1004 : used memory is 325 MB, reserved memory is 293 MB, peak memory is 327 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 67 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22193/367 useful/useless nets, 19853/52 useful/useless insts
SYN-1016 : Merged 57 instances.
SYN-2571 : Optimize after map_dsp, round 1, 476 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 44 instances.
SYN-2501 : Optimize round 1, 90 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 18 macro adder
SYN-1019 : Optimized 17 mux instances.
SYN-1016 : Merged 12 instances.
SYN-1032 : 22664/5 useful/useless nets, 20324/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 83242, tnet num: 22664, tinst num: 20323, tnode num: 116864, tedge num: 129642.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.151173s wall, 1.078125s user + 0.078125s system = 1.156250s CPU (100.4%)

RUN-1004 : used memory is 467 MB, reserved memory is 436 MB, peak memory is 467 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22664 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 257 (3.41), #lev = 7 (1.78)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 257 (3.41), #lev = 7 (1.78)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 596 instances into 257 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 450 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 140 adder to BLE ...
SYN-4008 : Packed 140 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.417508s wall, 4.328125s user + 0.093750s system = 4.421875s CPU (100.1%)

RUN-1004 : used memory is 352 MB, reserved memory is 321 MB, peak memory is 577 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  6.993666s wall, 6.781250s user + 0.234375s system = 7.015625s CPU (100.3%)

RUN-1004 : used memory is 352 MB, reserved memory is 322 MB, peak memory is 577 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/rmc_com2/GPRMC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/rmc_com2/GPRMC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/rmc_com2/GPRMC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/rmc_com2/GPRMC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/rmc_com2/GPRMC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/rmc_com2/GPRMC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/rmc_com2/GPRMC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/rmc_com2/GPRMC_data[0]
SYN-5055 WARNING: The kept net COM2/rx_err will be merged to another kept net COM2/rmc_com2/GPRMC_err
SYN-5055 WARNING: The kept net FMC/Error_state[0] will be merged to another kept net COM2/rmc_com2/GPRMC_sub_state_cnt0[3]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (306 clock/control pins, 0 other pins).
SYN-4027 : Net DATA/DIV_Dtemp/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net DATA/DIV_Dtemp/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19651 instances
RUN-0007 : 5646 luts, 12473 seqs, 933 mslices, 491 lslices, 61 pads, 42 brams, 0 dsps
RUN-1001 : There are total 22015 nets
RUN-1001 : 16520 nets have 2 pins
RUN-1001 : 4356 nets have [3 - 5] pins
RUN-1001 : 785 nets have [6 - 10] pins
RUN-1001 : 224 nets have [11 - 20] pins
RUN-1001 : 106 nets have [21 - 99] pins
RUN-1001 : 24 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4744     
RUN-1001 :   No   |  No   |  Yes  |     628     
RUN-1001 :   No   |  Yes  |  No   |     73      
RUN-1001 :   Yes  |  No   |  No   |    6531     
RUN-1001 :   Yes  |  No   |  Yes  |     470     
RUN-1001 :   Yes  |  Yes  |  No   |     27      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19649 instances, 5646 luts, 12473 seqs, 1424 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1131 pins
PHY-3001 : Huge net DATA/done_div with 1650 pins
PHY-0007 : Cell area utilization is 64%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 81773, tnet num: 22013, tinst num: 19649, tnode num: 115493, tedge num: 128453.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.164633s wall, 1.140625s user + 0.031250s system = 1.171875s CPU (100.6%)

RUN-1004 : used memory is 529 MB, reserved memory is 502 MB, peak memory is 577 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22013 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.008472s wall, 1.953125s user + 0.062500s system = 2.015625s CPU (100.4%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.39863e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19649.
PHY-3001 : Level 1 #clusters 2113.
PHY-3001 : End clustering;  0.139620s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (111.9%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 64%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 877339, overlap = 647
PHY-3002 : Step(2): len = 799865, overlap = 695.812
PHY-3002 : Step(3): len = 535057, overlap = 905.219
PHY-3002 : Step(4): len = 465584, overlap = 961.406
PHY-3002 : Step(5): len = 369666, overlap = 1045.19
PHY-3002 : Step(6): len = 325567, overlap = 1090.31
PHY-3002 : Step(7): len = 271599, overlap = 1157.69
PHY-3002 : Step(8): len = 246509, overlap = 1227.41
PHY-3002 : Step(9): len = 213043, overlap = 1268.81
PHY-3002 : Step(10): len = 199338, overlap = 1311.03
PHY-3002 : Step(11): len = 181634, overlap = 1352.47
PHY-3002 : Step(12): len = 165901, overlap = 1398.91
PHY-3002 : Step(13): len = 149471, overlap = 1435.44
PHY-3002 : Step(14): len = 137324, overlap = 1474.38
PHY-3002 : Step(15): len = 129841, overlap = 1475.72
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.1902e-06
PHY-3002 : Step(16): len = 138117, overlap = 1452.69
PHY-3002 : Step(17): len = 188548, overlap = 1311.81
PHY-3002 : Step(18): len = 192284, overlap = 1195.59
PHY-3002 : Step(19): len = 192391, overlap = 1123.81
PHY-3002 : Step(20): len = 189956, overlap = 1078.25
PHY-3002 : Step(21): len = 186562, overlap = 1046.47
PHY-3002 : Step(22): len = 183719, overlap = 1039.38
PHY-3002 : Step(23): len = 179837, overlap = 1048.31
PHY-3002 : Step(24): len = 176008, overlap = 1039.53
PHY-3002 : Step(25): len = 174251, overlap = 1053.91
PHY-3002 : Step(26): len = 172326, overlap = 1050.81
PHY-3002 : Step(27): len = 169946, overlap = 1052.53
PHY-3002 : Step(28): len = 168694, overlap = 1038.81
PHY-3002 : Step(29): len = 166348, overlap = 1035.66
PHY-3002 : Step(30): len = 164850, overlap = 1033.47
PHY-3002 : Step(31): len = 164640, overlap = 1021.28
PHY-3002 : Step(32): len = 163914, overlap = 1015.56
PHY-3002 : Step(33): len = 163577, overlap = 1000.38
PHY-3002 : Step(34): len = 162111, overlap = 1002.41
PHY-3002 : Step(35): len = 161317, overlap = 1003.88
PHY-3002 : Step(36): len = 159738, overlap = 1024.12
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.3804e-06
PHY-3002 : Step(37): len = 165824, overlap = 985.094
PHY-3002 : Step(38): len = 176969, overlap = 926.688
PHY-3002 : Step(39): len = 178997, overlap = 900.125
PHY-3002 : Step(40): len = 181426, overlap = 898.188
PHY-3002 : Step(41): len = 181488, overlap = 897.25
PHY-3002 : Step(42): len = 182074, overlap = 911.812
PHY-3002 : Step(43): len = 180700, overlap = 899.156
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.76081e-06
PHY-3002 : Step(44): len = 190646, overlap = 880.531
PHY-3002 : Step(45): len = 204672, overlap = 788.312
PHY-3002 : Step(46): len = 207858, overlap = 744.562
PHY-3002 : Step(47): len = 209374, overlap = 708.844
PHY-3002 : Step(48): len = 208554, overlap = 712.688
PHY-3002 : Step(49): len = 207409, overlap = 716.938
PHY-3002 : Step(50): len = 206265, overlap = 719.062
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 9.52162e-06
PHY-3002 : Step(51): len = 215808, overlap = 672.312
PHY-3002 : Step(52): len = 229139, overlap = 631.844
PHY-3002 : Step(53): len = 235536, overlap = 631.688
PHY-3002 : Step(54): len = 240119, overlap = 608.469
PHY-3002 : Step(55): len = 239280, overlap = 604.406
PHY-3002 : Step(56): len = 237240, overlap = 607.375
PHY-3002 : Step(57): len = 235452, overlap = 590.688
PHY-3002 : Step(58): len = 235033, overlap = 589.625
PHY-3002 : Step(59): len = 234440, overlap = 584.844
PHY-3002 : Step(60): len = 232156, overlap = 587.844
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.90432e-05
PHY-3002 : Step(61): len = 242896, overlap = 573.781
PHY-3002 : Step(62): len = 254030, overlap = 521.875
PHY-3002 : Step(63): len = 257463, overlap = 519.375
PHY-3002 : Step(64): len = 258992, overlap = 519.969
PHY-3002 : Step(65): len = 258945, overlap = 523.875
PHY-3002 : Step(66): len = 257830, overlap = 510.812
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.80865e-05
PHY-3002 : Step(67): len = 265045, overlap = 492.125
PHY-3002 : Step(68): len = 274939, overlap = 424.094
PHY-3002 : Step(69): len = 279684, overlap = 394.656
PHY-3002 : Step(70): len = 279461, overlap = 383
PHY-3002 : Step(71): len = 278438, overlap = 390.25
PHY-3002 : Step(72): len = 277535, overlap = 385.781
PHY-3002 : Step(73): len = 276333, overlap = 395.406
PHY-3002 : Step(74): len = 275175, overlap = 399.75
PHY-3002 : Step(75): len = 275316, overlap = 403.312
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 7.61729e-05
PHY-3002 : Step(76): len = 281146, overlap = 388.938
PHY-3002 : Step(77): len = 288386, overlap = 365.906
PHY-3002 : Step(78): len = 291655, overlap = 363.844
PHY-3002 : Step(79): len = 293498, overlap = 347.312
PHY-3002 : Step(80): len = 292025, overlap = 338.094
PHY-3002 : Step(81): len = 291381, overlap = 335.094
PHY-3002 : Step(82): len = 289310, overlap = 332.594
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000138657
PHY-3002 : Step(83): len = 293940, overlap = 312.25
PHY-3002 : Step(84): len = 300595, overlap = 297.875
PHY-3002 : Step(85): len = 303338, overlap = 292.719
PHY-3002 : Step(86): len = 305626, overlap = 283.25
PHY-3002 : Step(87): len = 305046, overlap = 281.281
PHY-3002 : Step(88): len = 304355, overlap = 262.344
PHY-3002 : Step(89): len = 302841, overlap = 275.344
PHY-3002 : Step(90): len = 304302, overlap = 253.5
PHY-3002 : Step(91): len = 306274, overlap = 246.062
PHY-3002 : Step(92): len = 305603, overlap = 255.031
PHY-3002 : Step(93): len = 303174, overlap = 252.5
PHY-3002 : Step(94): len = 302715, overlap = 253.5
PHY-3002 : Step(95): len = 302380, overlap = 254.406
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000264186
PHY-3002 : Step(96): len = 304811, overlap = 257.812
PHY-3002 : Step(97): len = 309858, overlap = 235.031
PHY-3002 : Step(98): len = 312264, overlap = 231.312
PHY-3002 : Step(99): len = 314408, overlap = 243
PHY-3002 : Step(100): len = 315162, overlap = 235.969
PHY-3002 : Step(101): len = 314486, overlap = 241.781
PHY-3002 : Step(102): len = 312756, overlap = 243.156
PHY-3002 : Step(103): len = 312188, overlap = 237.5
PHY-3002 : Step(104): len = 310988, overlap = 248.281
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.00047391
PHY-3002 : Step(105): len = 312166, overlap = 244.375
PHY-3002 : Step(106): len = 315668, overlap = 241
PHY-3002 : Step(107): len = 316477, overlap = 241.156
PHY-3001 : :::11::: Try harder cell spreading with beta_ = 0.000773209
PHY-3002 : Step(108): len = 317144, overlap = 237.375
PHY-3002 : Step(109): len = 322627, overlap = 234.656
PHY-3002 : Step(110): len = 323574, overlap = 236.344
PHY-3002 : Step(111): len = 323344, overlap = 219.781
PHY-3002 : Step(112): len = 323352, overlap = 198.125
PHY-3002 : Step(113): len = 322854, overlap = 212.031
PHY-3002 : Step(114): len = 322338, overlap = 204.781
PHY-3002 : Step(115): len = 322401, overlap = 216.75
PHY-3002 : Step(116): len = 323029, overlap = 219.188
PHY-3002 : Step(117): len = 323040, overlap = 226.375
PHY-3002 : Step(118): len = 322922, overlap = 218.25
PHY-3002 : Step(119): len = 322610, overlap = 227.531
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.013002s wall, 0.031250s user + 0.031250s system = 0.062500s CPU (480.7%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/22015.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 439480, over cnt = 1207(3%), over = 5370, worst = 34
PHY-1001 : End global iterations;  0.817760s wall, 1.078125s user + 0.031250s system = 1.109375s CPU (135.7%)

PHY-1001 : Congestion index: top1 = 76.01, top5 = 53.21, top10 = 43.38, top15 = 37.77.
PHY-3001 : End congestion estimation;  1.032609s wall, 1.250000s user + 0.078125s system = 1.328125s CPU (128.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22013 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.848012s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.66357e-05
PHY-3002 : Step(120): len = 366753, overlap = 157.312
PHY-3002 : Step(121): len = 379331, overlap = 136.969
PHY-3002 : Step(122): len = 382570, overlap = 126
PHY-3002 : Step(123): len = 382129, overlap = 128.656
PHY-3002 : Step(124): len = 387581, overlap = 120.312
PHY-3002 : Step(125): len = 393401, overlap = 104.125
PHY-3002 : Step(126): len = 400572, overlap = 102.781
PHY-3002 : Step(127): len = 403705, overlap = 103.031
PHY-3002 : Step(128): len = 404837, overlap = 103.5
PHY-3002 : Step(129): len = 408569, overlap = 106.469
PHY-3002 : Step(130): len = 412808, overlap = 108.656
PHY-3002 : Step(131): len = 411966, overlap = 108.844
PHY-3002 : Step(132): len = 412462, overlap = 108.281
PHY-3002 : Step(133): len = 414914, overlap = 109.469
PHY-3002 : Step(134): len = 415695, overlap = 105.688
PHY-3002 : Step(135): len = 415708, overlap = 103.688
PHY-3002 : Step(136): len = 418288, overlap = 102.062
PHY-3002 : Step(137): len = 420289, overlap = 105.5
PHY-3002 : Step(138): len = 419435, overlap = 111.438
PHY-3002 : Step(139): len = 420322, overlap = 113.969
PHY-3002 : Step(140): len = 420185, overlap = 116
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(141): len = 420613, overlap = 108.562
PHY-3002 : Step(142): len = 422011, overlap = 105.375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 70%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 102/22015.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 495056, over cnt = 2153(6%), over = 9621, worst = 51
PHY-1001 : End global iterations;  1.004585s wall, 1.500000s user + 0.015625s system = 1.515625s CPU (150.9%)

PHY-1001 : Congestion index: top1 = 73.92, top5 = 56.12, top10 = 47.83, top15 = 43.05.
PHY-3001 : End congestion estimation;  1.243073s wall, 1.750000s user + 0.015625s system = 1.765625s CPU (142.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22013 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.865967s wall, 0.843750s user + 0.015625s system = 0.859375s CPU (99.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.85253e-05
PHY-3002 : Step(143): len = 427946, overlap = 369
PHY-3002 : Step(144): len = 436388, overlap = 299.531
PHY-3002 : Step(145): len = 428610, overlap = 296.781
PHY-3002 : Step(146): len = 428076, overlap = 291.812
PHY-3002 : Step(147): len = 427866, overlap = 266.125
PHY-3002 : Step(148): len = 430365, overlap = 262.25
PHY-3002 : Step(149): len = 431433, overlap = 256.25
PHY-3002 : Step(150): len = 429461, overlap = 258.469
PHY-3002 : Step(151): len = 429518, overlap = 253.75
PHY-3002 : Step(152): len = 428611, overlap = 247.375
PHY-3002 : Step(153): len = 427487, overlap = 261.125
PHY-3002 : Step(154): len = 426194, overlap = 260.906
PHY-3002 : Step(155): len = 426589, overlap = 256.531
PHY-3002 : Step(156): len = 424770, overlap = 254.531
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000157051
PHY-3002 : Step(157): len = 425650, overlap = 243.438
PHY-3002 : Step(158): len = 426937, overlap = 236.125
PHY-3002 : Step(159): len = 427930, overlap = 234.469
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000314101
PHY-3002 : Step(160): len = 431682, overlap = 214.375
PHY-3002 : Step(161): len = 438381, overlap = 196.781
PHY-3002 : Step(162): len = 442296, overlap = 191.094
PHY-3002 : Step(163): len = 443504, overlap = 188.781
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000628203
PHY-3002 : Step(164): len = 444640, overlap = 183.531
PHY-3002 : Step(165): len = 450459, overlap = 171.531
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 81773, tnet num: 22013, tinst num: 19649, tnode num: 115493, tedge num: 128453.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.448608s wall, 1.437500s user + 0.015625s system = 1.453125s CPU (100.3%)

RUN-1004 : used memory is 569 MB, reserved memory is 545 MB, peak memory is 704 MB
OPT-1001 : Total overflow 545.94 peak overflow 4.22
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 619/22015.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 536880, over cnt = 2562(7%), over = 9279, worst = 21
PHY-1001 : End global iterations;  1.169811s wall, 1.859375s user + 0.031250s system = 1.890625s CPU (161.6%)

PHY-1001 : Congestion index: top1 = 59.87, top5 = 49.14, top10 = 43.95, top15 = 40.57.
PHY-1001 : End incremental global routing;  1.479477s wall, 2.171875s user + 0.031250s system = 2.203125s CPU (148.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22013 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.051221s wall, 1.031250s user + 0.015625s system = 1.046875s CPU (99.6%)

OPT-1001 : 16 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 61 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19569 has valid locations, 262 needs to be replaced
PHY-3001 : design contains 19895 instances, 5752 luts, 12613 seqs, 1424 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 469016
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17208/22261.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 555384, over cnt = 2598(7%), over = 9425, worst = 21
PHY-1001 : End global iterations;  0.183355s wall, 0.296875s user + 0.046875s system = 0.343750s CPU (187.5%)

PHY-1001 : Congestion index: top1 = 60.39, top5 = 49.59, top10 = 44.41, top15 = 41.09.
PHY-3001 : End congestion estimation;  0.423529s wall, 0.515625s user + 0.046875s system = 0.562500s CPU (132.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 82597, tnet num: 22259, tinst num: 19895, tnode num: 116649, tedge num: 129609.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.492781s wall, 1.484375s user + 0.015625s system = 1.500000s CPU (100.5%)

RUN-1004 : used memory is 612 MB, reserved memory is 604 MB, peak memory is 704 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22259 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.455561s wall, 2.421875s user + 0.031250s system = 2.453125s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(166): len = 468718, overlap = 6.1875
PHY-3002 : Step(167): len = 469432, overlap = 6
PHY-3002 : Step(168): len = 470182, overlap = 5.875
PHY-3002 : Step(169): len = 471554, overlap = 5.8125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 71%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17234/22261.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 551296, over cnt = 2625(7%), over = 9504, worst = 21
PHY-1001 : End global iterations;  0.178703s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (104.9%)

PHY-1001 : Congestion index: top1 = 60.84, top5 = 49.81, top10 = 44.58, top15 = 41.24.
PHY-3001 : End congestion estimation;  0.481232s wall, 0.468750s user + 0.015625s system = 0.484375s CPU (100.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22259 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.926549s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000662075
PHY-3002 : Step(170): len = 471492, overlap = 174.969
PHY-3002 : Step(171): len = 471816, overlap = 174.031
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00127514
PHY-3002 : Step(172): len = 472192, overlap = 173.219
PHY-3002 : Step(173): len = 472601, overlap = 173.281
PHY-3001 : Final: Len = 472601, Over = 173.281
PHY-3001 : End incremental placement;  5.138729s wall, 5.187500s user + 0.234375s system = 5.421875s CPU (105.5%)

OPT-1001 : Total overflow 551.06 peak overflow 4.22
OPT-1001 : End high-fanout net optimization;  8.160536s wall, 9.046875s user + 0.281250s system = 9.328125s CPU (114.3%)

OPT-1001 : Current memory(MB): used = 709, reserve = 690, peak = 726.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17247/22261.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 555232, over cnt = 2583(7%), over = 8904, worst = 21
PHY-1002 : len = 595112, over cnt = 1890(5%), over = 5142, worst = 21
PHY-1002 : len = 639432, over cnt = 925(2%), over = 2124, worst = 18
PHY-1002 : len = 667536, over cnt = 181(0%), over = 397, worst = 15
PHY-1002 : len = 673504, over cnt = 5(0%), over = 23, worst = 8
PHY-1001 : End global iterations;  1.430832s wall, 2.046875s user + 0.000000s system = 2.046875s CPU (143.1%)

PHY-1001 : Congestion index: top1 = 51.79, top5 = 45.03, top10 = 41.61, top15 = 39.48.
OPT-1001 : End congestion update;  1.657005s wall, 2.265625s user + 0.000000s system = 2.265625s CPU (136.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22259 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.800528s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (101.5%)

OPT-0007 : Start: WNS 4119 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.463712s wall, 3.078125s user + 0.000000s system = 3.078125s CPU (124.9%)

OPT-1001 : Current memory(MB): used = 708, reserve = 688, peak = 726.
OPT-1001 : End physical optimization;  12.393851s wall, 13.937500s user + 0.296875s system = 14.234375s CPU (114.9%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5752 LUT to BLE ...
SYN-4008 : Packed 5752 LUT and 2862 SEQ to BLE.
SYN-4003 : Packing 9751 remaining SEQ's ...
SYN-4005 : Packed 3246 SEQ with LUT/SLICE
SYN-4006 : 142 single LUT's are left
SYN-4006 : 6505 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 12257/13909 primitive instances ...
PHY-3001 : End packing;  2.802682s wall, 2.796875s user + 0.000000s system = 2.796875s CPU (99.8%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8247 instances
RUN-1001 : 4070 mslices, 4069 lslices, 61 pads, 42 brams, 0 dsps
RUN-1001 : There are total 19441 nets
RUN-1001 : 13614 nets have 2 pins
RUN-1001 : 4449 nets have [3 - 5] pins
RUN-1001 : 862 nets have [6 - 10] pins
RUN-1001 : 357 nets have [11 - 20] pins
RUN-1001 : 149 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
PHY-3001 : design contains 8245 instances, 8139 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Cell area utilization is 86%
PHY-3001 : After packing: Len = 488851, Over = 390.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 8018/19441.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 630272, over cnt = 1713(4%), over = 2763, worst = 9
PHY-1002 : len = 636424, over cnt = 1217(3%), over = 1750, worst = 9
PHY-1002 : len = 652152, over cnt = 420(1%), over = 541, worst = 5
PHY-1002 : len = 658864, over cnt = 117(0%), over = 148, worst = 4
PHY-1002 : len = 661760, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.272914s wall, 1.781250s user + 0.046875s system = 1.828125s CPU (143.6%)

PHY-1001 : Congestion index: top1 = 52.84, top5 = 45.20, top10 = 41.39, top15 = 38.97.
PHY-3001 : End congestion estimation;  1.557048s wall, 2.046875s user + 0.046875s system = 2.093750s CPU (134.5%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68140, tnet num: 19439, tinst num: 8245, tnode num: 92929, tedge num: 112150.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.639425s wall, 1.640625s user + 0.000000s system = 1.640625s CPU (100.1%)

RUN-1004 : used memory is 602 MB, reserved memory is 592 MB, peak memory is 726 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19439 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.450200s wall, 2.437500s user + 0.015625s system = 2.453125s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.56328e-05
PHY-3002 : Step(174): len = 492282, overlap = 382.75
PHY-3002 : Step(175): len = 488773, overlap = 394.25
PHY-3002 : Step(176): len = 487079, overlap = 403
PHY-3002 : Step(177): len = 488985, overlap = 411.5
PHY-3002 : Step(178): len = 489239, overlap = 410.5
PHY-3002 : Step(179): len = 489311, overlap = 419.25
PHY-3002 : Step(180): len = 488879, overlap = 423.5
PHY-3002 : Step(181): len = 486375, overlap = 432.25
PHY-3002 : Step(182): len = 485765, overlap = 438.5
PHY-3002 : Step(183): len = 483978, overlap = 438.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.12655e-05
PHY-3002 : Step(184): len = 487106, overlap = 421.75
PHY-3002 : Step(185): len = 492390, overlap = 400.5
PHY-3002 : Step(186): len = 493311, overlap = 393
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000182531
PHY-3002 : Step(187): len = 502369, overlap = 374
PHY-3002 : Step(188): len = 513691, overlap = 348.75
PHY-3002 : Step(189): len = 511338, overlap = 347.25
PHY-3002 : Step(190): len = 508986, overlap = 350.75
PHY-3002 : Step(191): len = 508118, overlap = 355.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.573241s wall, 0.609375s user + 0.765625s system = 1.375000s CPU (239.9%)

PHY-3001 : Trial Legalized: Len = 639195
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 608/19441.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 737448, over cnt = 2512(7%), over = 4067, worst = 7
PHY-1002 : len = 751152, over cnt = 1540(4%), over = 2221, worst = 7
PHY-1002 : len = 767632, over cnt = 782(2%), over = 1080, worst = 5
PHY-1002 : len = 779872, over cnt = 261(0%), over = 353, worst = 5
PHY-1002 : len = 786392, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.853038s wall, 2.984375s user + 0.000000s system = 2.984375s CPU (161.1%)

PHY-1001 : Congestion index: top1 = 52.22, top5 = 46.88, top10 = 44.08, top15 = 42.18.
PHY-3001 : End congestion estimation;  2.172652s wall, 3.296875s user + 0.000000s system = 3.296875s CPU (151.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19439 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.788780s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (101.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000206195
PHY-3002 : Step(192): len = 590473, overlap = 88
PHY-3002 : Step(193): len = 572869, overlap = 121.75
PHY-3002 : Step(194): len = 561438, overlap = 156.25
PHY-3002 : Step(195): len = 551224, overlap = 195
PHY-3002 : Step(196): len = 546125, overlap = 215
PHY-3002 : Step(197): len = 543069, overlap = 224.5
PHY-3002 : Step(198): len = 541938, overlap = 227.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00041239
PHY-3002 : Step(199): len = 546574, overlap = 228.75
PHY-3002 : Step(200): len = 552754, overlap = 223.25
PHY-3002 : Step(201): len = 554934, overlap = 233.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000767595
PHY-3002 : Step(202): len = 558985, overlap = 232.5
PHY-3002 : Step(203): len = 570331, overlap = 230
PHY-3002 : Step(204): len = 576734, overlap = 228
PHY-3002 : Step(205): len = 576638, overlap = 233.5
PHY-3002 : Step(206): len = 576373, overlap = 230.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.029498s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (105.9%)

PHY-3001 : Legalized: Len = 616031, Over = 0
PHY-3001 : Spreading special nets. 34 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.080239s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (97.4%)

PHY-3001 : 50 instances has been re-located, deltaX = 23, deltaY = 19, maxDist = 2.
PHY-3001 : Final: Len = 617107, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68140, tnet num: 19439, tinst num: 8245, tnode num: 92929, tedge num: 112150.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.832711s wall, 1.828125s user + 0.000000s system = 1.828125s CPU (99.7%)

RUN-1004 : used memory is 603 MB, reserved memory is 589 MB, peak memory is 726 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2855/19441.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 719760, over cnt = 2290(6%), over = 3655, worst = 7
PHY-1002 : len = 730280, over cnt = 1488(4%), over = 2164, worst = 7
PHY-1002 : len = 748496, over cnt = 605(1%), over = 828, worst = 7
PHY-1002 : len = 756720, over cnt = 222(0%), over = 319, worst = 6
PHY-1002 : len = 761792, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.647694s wall, 2.656250s user + 0.015625s system = 2.671875s CPU (162.2%)

PHY-1001 : Congestion index: top1 = 48.36, top5 = 44.03, top10 = 41.54, top15 = 39.83.
PHY-1001 : End incremental global routing;  1.916242s wall, 2.906250s user + 0.015625s system = 2.921875s CPU (152.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19439 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.823042s wall, 0.812500s user + 0.015625s system = 0.828125s CPU (100.6%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 61 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8180 has valid locations, 8 needs to be replaced
PHY-3001 : design contains 8252 instances, 8146 slices, 282 macros(1424 instances: 933 mslices 491 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 618694
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17535/19447.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 762728, over cnt = 13(0%), over = 15, worst = 2
PHY-1002 : len = 762704, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 762760, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 762776, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.472327s wall, 0.484375s user + 0.000000s system = 0.484375s CPU (102.6%)

PHY-1001 : Congestion index: top1 = 48.32, top5 = 44.01, top10 = 41.52, top15 = 39.82.
PHY-3001 : End congestion estimation;  0.742904s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (98.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68181, tnet num: 19445, tinst num: 8252, tnode num: 92977, tedge num: 112197.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.851302s wall, 1.859375s user + 0.000000s system = 1.859375s CPU (100.4%)

RUN-1004 : used memory is 636 MB, reserved memory is 616 MB, peak memory is 726 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19445 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.686751s wall, 2.671875s user + 0.015625s system = 2.687500s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(207): len = 618626, overlap = 0.5
PHY-3002 : Step(208): len = 618588, overlap = 0.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 86%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17532/19447.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 762504, over cnt = 16(0%), over = 19, worst = 2
PHY-1002 : len = 762504, over cnt = 13(0%), over = 13, worst = 1
PHY-1002 : len = 762592, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 762680, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 762760, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.659106s wall, 0.656250s user + 0.000000s system = 0.656250s CPU (99.6%)

PHY-1001 : Congestion index: top1 = 48.36, top5 = 44.05, top10 = 41.57, top15 = 39.86.
PHY-3001 : End congestion estimation;  0.928666s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (99.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19445 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.804560s wall, 0.796875s user + 0.015625s system = 0.812500s CPU (101.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000670272
PHY-3002 : Step(209): len = 618535, overlap = 1
PHY-3002 : Step(210): len = 618519, overlap = 0.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005438s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (287.3%)

PHY-3001 : Legalized: Len = 618530, Over = 0
PHY-3001 : End spreading;  0.060082s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (78.0%)

PHY-3001 : Final: Len = 618530, Over = 0
PHY-3001 : End incremental placement;  5.738356s wall, 5.734375s user + 0.109375s system = 5.843750s CPU (101.8%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  8.914270s wall, 10.031250s user + 0.140625s system = 10.171875s CPU (114.1%)

OPT-1001 : Current memory(MB): used = 720, reserve = 706, peak = 726.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17532/19447.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 762656, over cnt = 6(0%), over = 7, worst = 2
PHY-1002 : len = 762656, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 762664, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 762664, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.474717s wall, 0.468750s user + 0.015625s system = 0.484375s CPU (102.0%)

PHY-1001 : Congestion index: top1 = 48.32, top5 = 44.02, top10 = 41.53, top15 = 39.81.
OPT-1001 : End congestion update;  0.745049s wall, 0.750000s user + 0.015625s system = 0.765625s CPU (102.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19445 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.696285s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (98.7%)

OPT-0007 : Start: WNS 3935 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.445755s wall, 1.453125s user + 0.015625s system = 1.468750s CPU (101.6%)

OPT-1001 : Current memory(MB): used = 721, reserve = 706, peak = 726.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19445 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.706685s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (99.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17542/19447.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 762664, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.109103s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (100.2%)

PHY-1001 : Congestion index: top1 = 48.32, top5 = 44.02, top10 = 41.53, top15 = 39.81.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19445 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.693933s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (101.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3935 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 47.793103
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3935ps with logic level 4 
RUN-1001 :       #2 path slack 3952ps with logic level 4 
OPT-1001 : End physical optimization;  14.219651s wall, 15.343750s user + 0.171875s system = 15.515625s CPU (109.1%)

RUN-1003 : finish command "place" in  67.555891s wall, 105.640625s user + 6.828125s system = 112.468750s CPU (166.5%)

RUN-1004 : used memory is 638 MB, reserved memory is 619 MB, peak memory is 726 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.560004s wall, 2.750000s user + 0.015625s system = 2.765625s CPU (177.3%)

RUN-1004 : used memory is 638 MB, reserved memory is 619 MB, peak memory is 726 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8254 instances
RUN-1001 : 4077 mslices, 4069 lslices, 61 pads, 42 brams, 0 dsps
RUN-1001 : There are total 19447 nets
RUN-1001 : 13614 nets have 2 pins
RUN-1001 : 4449 nets have [3 - 5] pins
RUN-1001 : 865 nets have [6 - 10] pins
RUN-1001 : 360 nets have [11 - 20] pins
RUN-1001 : 149 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68181, tnet num: 19445, tinst num: 8252, tnode num: 92977, tedge num: 112197.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.736985s wall, 1.734375s user + 0.000000s system = 1.734375s CPU (99.8%)

RUN-1004 : used memory is 620 MB, reserved memory is 602 MB, peak memory is 726 MB
PHY-1001 : 4077 mslices, 4069 lslices, 61 pads, 42 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19445 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 700392, over cnt = 2401(6%), over = 3999, worst = 7
PHY-1002 : len = 714784, over cnt = 1523(4%), over = 2262, worst = 7
PHY-1002 : len = 732440, over cnt = 688(1%), over = 989, worst = 6
PHY-1002 : len = 747072, over cnt = 63(0%), over = 82, worst = 5
PHY-1002 : len = 748616, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.580123s wall, 2.937500s user + 0.078125s system = 3.015625s CPU (190.8%)

PHY-1001 : Congestion index: top1 = 48.56, top5 = 43.57, top10 = 41.16, top15 = 39.42.
PHY-1001 : End global routing;  1.888116s wall, 3.234375s user + 0.093750s system = 3.328125s CPU (176.3%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 699, reserve = 690, peak = 726.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net DATA/DIV_Dtemp/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 971, reserve = 959, peak = 971.
PHY-1001 : End build detailed router design. 4.339413s wall, 4.296875s user + 0.031250s system = 4.328125s CPU (99.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 192664, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.762683s wall, 0.750000s user + 0.015625s system = 0.765625s CPU (100.4%)

PHY-1001 : Current memory(MB): used = 1006, reserve = 995, peak = 1006.
PHY-1001 : End phase 1; 0.769577s wall, 0.750000s user + 0.015625s system = 0.765625s CPU (99.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 55% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.85496e+06, over cnt = 1456(0%), over = 1462, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1022, reserve = 1012, peak = 1022.
PHY-1001 : End initial routed; 19.048595s wall, 48.484375s user + 0.406250s system = 48.890625s CPU (256.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18242(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.560   |   0.000   |   0   
RUN-1001 :   Hold   |   0.131   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.125215s wall, 3.125000s user + 0.000000s system = 3.125000s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1034, reserve = 1023, peak = 1034.
PHY-1001 : End phase 2; 22.173952s wall, 51.609375s user + 0.406250s system = 52.015625s CPU (234.6%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.85496e+06, over cnt = 1456(0%), over = 1462, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.215315s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (94.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.83455e+06, over cnt = 474(0%), over = 474, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 1.218281s wall, 2.093750s user + 0.031250s system = 2.125000s CPU (174.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.83241e+06, over cnt = 95(0%), over = 95, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.816114s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (118.7%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.83354e+06, over cnt = 18(0%), over = 18, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.277489s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (112.6%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.83369e+06, over cnt = 9(0%), over = 9, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.185756s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (100.9%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.83365e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.168073s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (102.3%)

PHY-1001 : ===== DR Iter 6 =====
PHY-1022 : len = 1.83365e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 6; 0.218486s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (93.0%)

PHY-1001 : ===== DR Iter 7 =====
PHY-1022 : len = 1.83365e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 7; 0.377528s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (99.3%)

PHY-1001 : ===== DR Iter 8 =====
PHY-1022 : len = 1.83365e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 8; 0.150837s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (93.2%)

PHY-1001 : ==== DR Iter 9 ====
PHY-1022 : len = 1.8337e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 9; 0.141322s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (99.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18242(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.375   |   0.000   |   0   
RUN-1001 :   Hold   |   0.113   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.125542s wall, 3.109375s user + 0.015625s system = 3.125000s CPU (100.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 378 feed throughs used by 314 nets
PHY-1001 : End commit to database; 2.120891s wall, 2.125000s user + 0.000000s system = 2.125000s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 1124, reserve = 1116, peak = 1124.
PHY-1001 : End phase 3; 9.537181s wall, 10.578125s user + 0.046875s system = 10.625000s CPU (111.4%)

PHY-1003 : Routed, final wirelength = 1.8337e+06
PHY-1001 : Current memory(MB): used = 1128, reserve = 1120, peak = 1128.
PHY-1001 : End export database. 0.057664s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.4%)

PHY-1001 : End detail routing;  37.254586s wall, 67.671875s user + 0.500000s system = 68.171875s CPU (183.0%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68181, tnet num: 19445, tinst num: 8252, tnode num: 92977, tedge num: 112197.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.609484s wall, 1.609375s user + 0.000000s system = 1.609375s CPU (100.0%)

RUN-1004 : used memory is 1061 MB, reserved memory is 1059 MB, peak memory is 1128 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  44.916360s wall, 76.671875s user + 0.593750s system = 77.265625s CPU (172.0%)

RUN-1004 : used memory is 1060 MB, reserved memory is 1060 MB, peak memory is 1128 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        67
  #input                   26
  #output                  39
  #inout                    2

Utilization Statistics
#lut                     8736   out of  19600   44.57%
#reg                    12710   out of  19600   64.85%
#le                     15199
  #lut only              2489   out of  15199   16.38%
  #reg only              6463   out of  15199   42.52%
  #lut&reg               6247   out of  15199   41.10%
#dsp                        0   out of     29    0.00%
#bram                      42   out of     64   65.62%
  #bram9k                  42
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       61   out of    188   32.45%
  #ireg                     9
  #oreg                    32
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet              Type               DriverType         Driver                    Fanout
#1        DATA/DIV_Dtemp/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6967
#2        config_inst_syn_9     GCLK               config             config_inst.jtck          172
#3        clk_in_dup_1          GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         L4        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          NONE       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D14        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    GNSS_RMC       OUTPUT         L3        LVCMOS33           8            NONE           NONE       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         T6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT        D16        LVCMOS33           8            NONE           NONE       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        R15        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15199  |7312    |1424    |12752   |42      |0       |
|  AnyFog_dataX                      |AnyFog          |205    |81      |22      |170     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |84     |55      |22      |49      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |207    |72      |22      |172     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |85     |53      |22      |50      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |217    |140     |22      |165     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |98     |74      |22      |46      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |3511   |949     |34      |3417    |0       |0       |
|    PPPNAV_com2                     |PPPNAV          |727    |88      |5       |715     |0       |0       |
|    STADOP_com2                     |STADOP          |558    |45      |0       |553     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |47      |14      |41      |0       |0       |
|    head_com2                       |uniheading      |266    |67      |5       |254     |0       |0       |
|    rmc_com2                        |Gprmc           |172    |84      |0       |156     |0       |0       |
|    uart_com2                       |Agrica          |1431   |326     |10      |1406    |0       |0       |
|  DATA                              |Data_Processing |8630   |4343    |1062    |6943    |0       |0       |
|    DIV_Dtemp                       |Divider         |809    |330     |84      |683     |0       |0       |
|    DIV_Utemp                       |Divider         |604    |313     |84      |480     |0       |0       |
|    DIV_accX                        |Divider         |597    |315     |84      |474     |0       |0       |
|    DIV_accY                        |Divider         |672    |365     |111     |496     |0       |0       |
|    DIV_accZ                        |Divider         |647    |367     |132     |441     |0       |0       |
|    DIV_rateX                       |Divider         |661    |394     |132     |454     |0       |0       |
|    DIV_rateY                       |Divider         |608    |362     |132     |401     |0       |0       |
|    DIV_rateZ                       |Divider         |579    |348     |132     |373     |0       |0       |
|    genclk                          |genclk          |77     |44      |20      |44      |0       |0       |
|  FMC                               |FMC_Ctrl        |405    |352     |43      |330     |0       |0       |
|  IIC                               |I2C_master      |304    |251     |11      |257     |0       |0       |
|  IMU_CTRL                          |SCHA634         |921    |644     |61      |731     |0       |0       |
|    CtrlData                        |CtrlData        |491    |441     |47      |338     |0       |0       |
|      usms                          |Time_1ms        |30     |25      |5       |22      |0       |0       |
|    SPIM                            |SPI_SCHA634     |430    |203     |14      |393     |0       |0       |
|  POWER                             |POWER_EN        |97     |49      |38      |37      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |687    |431     |109     |473     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |687    |431     |109     |473     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |316    |192     |0       |299     |0       |0       |
|        reg_inst                    |register        |313    |189     |0       |296     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |371    |239     |109     |174     |0       |0       |
|        bus_inst                    |bus_top         |145    |93      |52      |48      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |24     |14      |10      |4       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |16     |10      |6       |7       |0       |0       |
|          BUS_DETECTOR[5]$bus_nodes |bus_det         |52     |34      |18      |20      |0       |0       |
|          BUS_DETECTOR[6]$bus_nodes |bus_det         |49     |31      |18      |13      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |144    |105     |29      |92      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13552  
    #2          2       3506   
    #3          3        681   
    #4          4        262   
    #5        5-10       917   
    #6        11-50      442   
    #7       51-100      15    
    #8       101-500      4    
    #9        >500        2    
  Average     2.14             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  1.916096s wall, 3.343750s user + 0.000000s system = 3.343750s CPU (174.5%)

RUN-1004 : used memory is 1061 MB, reserved memory is 1060 MB, peak memory is 1128 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 42, tpin num: 68181, tnet num: 19445, tinst num: 8252, tnode num: 92977, tedge num: 112197.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.630140s wall, 1.640625s user + 0.000000s system = 1.640625s CPU (100.6%)

RUN-1004 : used memory is 1062 MB, reserved memory is 1062 MB, peak memory is 1128 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19445 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.239606s wall, 1.234375s user + 0.000000s system = 1.234375s CPU (99.6%)

RUN-1004 : used memory is 1069 MB, reserved memory is 1068 MB, peak memory is 1128 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 8f169df8acfcb835781910b1ac29267cd80e16d8a5e1b8cf9d032ec93c0a2865 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8252
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19447, pip num: 151222
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 378
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3250 valid insts, and 420269 bits set as '1'.
BIT-1004 : the usercode register value: 00000000110101000001111110010110
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  10.723344s wall, 108.109375s user + 0.218750s system = 108.328125s CPU (1010.2%)

RUN-1004 : used memory is 1197 MB, reserved memory is 1183 MB, peak memory is 1312 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250506_140725.log"

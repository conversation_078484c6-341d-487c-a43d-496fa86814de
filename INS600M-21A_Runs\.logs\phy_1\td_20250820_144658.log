============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Wed Aug 20 14:46:58 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.856567s wall, 1.562500s user + 4.250000s system = 5.812500s CPU (99.2%)

RUN-1004 : used memory is 80 MB, reserved memory is 41 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.852959s wall, 1.781250s user + 0.078125s system = 1.859375s CPU (100.3%)

RUN-1004 : used memory is 303 MB, reserved memory is 271 MB, peak memory is 306 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 6 view nodes, 43 trigger nets, 43 data nets.
KIT-1004 : Chipwatcher code = 0010111001101000
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb01001010}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=132) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=132) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb01001010}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb01001010}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb01001010})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=132)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=132)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb01001010})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=6,BUS_DIN_NUM=43,BUS_CTRL_NUM=110,BUS_WIDTH='{32'sb01000,32'sb01,32'sb01,32'sb01,32'sb010000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb011011},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0100000,32'sb0100110,32'sb01001010})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22874/26 useful/useless nets, 19596/15 useful/useless insts
SYN-1016 : Merged 33 instances.
SYN-1032 : 22526/22 useful/useless nets, 20031/18 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 412 better
SYN-1014 : Optimize round 2
SYN-1032 : 22195/45 useful/useless nets, 19700/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.527660s wall, 2.437500s user + 0.078125s system = 2.515625s CPU (99.5%)

RUN-1004 : used memory is 329 MB, reserved memory is 295 MB, peak memory is 331 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22211/155 useful/useless nets, 19735/47 useful/useless insts
SYN-1016 : Merged 14 instances.
SYN-2571 : Optimize after map_dsp, round 1, 216 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 40 instances.
SYN-2501 : Optimize round 1, 82 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 22697/4 useful/useless nets, 20221/3 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82345, tnet num: 22697, tinst num: 20220, tnode num: 115374, tedge num: 128552.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.241443s wall, 1.203125s user + 0.031250s system = 1.234375s CPU (99.4%)

RUN-1004 : used memory is 469 MB, reserved memory is 436 MB, peak memory is 469 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22697 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 242 (3.42), #lev = 7 (1.80)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 242 (3.42), #lev = 7 (1.80)
SYN-3001 : Logic optimization runtime opt =   0.04 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 559 instances into 242 LUTs, name keeping = 72%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 421 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 128 adder to BLE ...
SYN-4008 : Packed 128 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.825209s wall, 4.687500s user + 0.125000s system = 4.812500s CPU (99.7%)

RUN-1004 : used memory is 353 MB, reserved memory is 319 MB, peak memory is 576 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.701949s wall, 7.437500s user + 0.234375s system = 7.671875s CPU (99.6%)

RUN-1004 : used memory is 353 MB, reserved memory is 319 MB, peak memory is 576 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (282 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19444 instances
RUN-0007 : 5634 luts, 12222 seqs, 983 mslices, 519 lslices, 59 pads, 22 brams, 0 dsps
RUN-1001 : There are total 21928 nets
RUN-1001 : 16438 nets have 2 pins
RUN-1001 : 4294 nets have [3 - 5] pins
RUN-1001 : 835 nets have [6 - 10] pins
RUN-1001 : 237 nets have [11 - 20] pins
RUN-1001 : 103 nets have [21 - 99] pins
RUN-1001 : 21 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4792     
RUN-1001 :   No   |  No   |  Yes  |     707     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     454     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19442 instances, 5634 luts, 12222 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80675, tnet num: 21926, tinst num: 19442, tnode num: 113509, tedge num: 126828.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.221825s wall, 1.234375s user + 0.000000s system = 1.234375s CPU (101.0%)

RUN-1004 : used memory is 527 MB, reserved memory is 500 MB, peak memory is 576 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21926 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.161158s wall, 2.171875s user + 0.000000s system = 2.171875s CPU (100.5%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.64615e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19442.
PHY-3001 : Level 1 #clusters 2093.
PHY-3001 : End clustering;  0.159213s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (117.8%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 894905, overlap = 601.125
PHY-3002 : Step(2): len = 817578, overlap = 663.75
PHY-3002 : Step(3): len = 529527, overlap = 802.281
PHY-3002 : Step(4): len = 470550, overlap = 904.406
PHY-3002 : Step(5): len = 369794, overlap = 984.094
PHY-3002 : Step(6): len = 324030, overlap = 1051.91
PHY-3002 : Step(7): len = 265436, overlap = 1147.25
PHY-3002 : Step(8): len = 243219, overlap = 1187.75
PHY-3002 : Step(9): len = 213095, overlap = 1232.41
PHY-3002 : Step(10): len = 195562, overlap = 1260.91
PHY-3002 : Step(11): len = 177496, overlap = 1329.31
PHY-3002 : Step(12): len = 167895, overlap = 1367.16
PHY-3002 : Step(13): len = 150617, overlap = 1383.56
PHY-3002 : Step(14): len = 139245, overlap = 1404.44
PHY-3002 : Step(15): len = 127200, overlap = 1441.62
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.08108e-06
PHY-3002 : Step(16): len = 133413, overlap = 1422.16
PHY-3002 : Step(17): len = 182512, overlap = 1340.09
PHY-3002 : Step(18): len = 190539, overlap = 1223.19
PHY-3002 : Step(19): len = 189715, overlap = 1138.47
PHY-3002 : Step(20): len = 188774, overlap = 1097.72
PHY-3002 : Step(21): len = 183872, overlap = 1075.62
PHY-3002 : Step(22): len = 180195, overlap = 1083
PHY-3002 : Step(23): len = 176002, overlap = 1085.53
PHY-3002 : Step(24): len = 172571, overlap = 1073.72
PHY-3002 : Step(25): len = 169544, overlap = 1085
PHY-3002 : Step(26): len = 167281, overlap = 1104
PHY-3002 : Step(27): len = 165360, overlap = 1106.91
PHY-3002 : Step(28): len = 163929, overlap = 1147.16
PHY-3002 : Step(29): len = 161376, overlap = 1158.38
PHY-3002 : Step(30): len = 161102, overlap = 1155.59
PHY-3002 : Step(31): len = 158409, overlap = 1154.03
PHY-3002 : Step(32): len = 158077, overlap = 1160.84
PHY-3002 : Step(33): len = 156134, overlap = 1158.72
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.16216e-06
PHY-3002 : Step(34): len = 164616, overlap = 1132.94
PHY-3002 : Step(35): len = 179700, overlap = 1066.28
PHY-3002 : Step(36): len = 185210, overlap = 1034.59
PHY-3002 : Step(37): len = 187598, overlap = 1017.19
PHY-3002 : Step(38): len = 187652, overlap = 988.375
PHY-3002 : Step(39): len = 187134, overlap = 972.344
PHY-3002 : Step(40): len = 185789, overlap = 980.406
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.32431e-06
PHY-3002 : Step(41): len = 195994, overlap = 940.781
PHY-3002 : Step(42): len = 211629, overlap = 868.438
PHY-3002 : Step(43): len = 217348, overlap = 833.594
PHY-3002 : Step(44): len = 218866, overlap = 816.562
PHY-3002 : Step(45): len = 217857, overlap = 798.344
PHY-3002 : Step(46): len = 216627, overlap = 799.375
PHY-3002 : Step(47): len = 215711, overlap = 806.812
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 8.64862e-06
PHY-3002 : Step(48): len = 227327, overlap = 761.344
PHY-3002 : Step(49): len = 242369, overlap = 712
PHY-3002 : Step(50): len = 247594, overlap = 697.844
PHY-3002 : Step(51): len = 249813, overlap = 695.656
PHY-3002 : Step(52): len = 249019, overlap = 663.719
PHY-3002 : Step(53): len = 247682, overlap = 666.875
PHY-3002 : Step(54): len = 245034, overlap = 654.625
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.72972e-05
PHY-3002 : Step(55): len = 258198, overlap = 615.656
PHY-3002 : Step(56): len = 274321, overlap = 564.25
PHY-3002 : Step(57): len = 278588, overlap = 506.312
PHY-3002 : Step(58): len = 280139, overlap = 475.062
PHY-3002 : Step(59): len = 279687, overlap = 461.75
PHY-3002 : Step(60): len = 276741, overlap = 450.125
PHY-3002 : Step(61): len = 274807, overlap = 422.75
PHY-3002 : Step(62): len = 275120, overlap = 425.875
PHY-3002 : Step(63): len = 275649, overlap = 419.469
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.45945e-05
PHY-3002 : Step(64): len = 286345, overlap = 387.125
PHY-3002 : Step(65): len = 296697, overlap = 353.25
PHY-3002 : Step(66): len = 300307, overlap = 325.094
PHY-3002 : Step(67): len = 301572, overlap = 334.406
PHY-3002 : Step(68): len = 298419, overlap = 322.25
PHY-3002 : Step(69): len = 297279, overlap = 315.781
PHY-3002 : Step(70): len = 295599, overlap = 291.844
PHY-3002 : Step(71): len = 295162, overlap = 303.406
PHY-3002 : Step(72): len = 294076, overlap = 302.125
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 6.9189e-05
PHY-3002 : Step(73): len = 302976, overlap = 287.375
PHY-3002 : Step(74): len = 309890, overlap = 271.344
PHY-3002 : Step(75): len = 312475, overlap = 272
PHY-3002 : Step(76): len = 313916, overlap = 264.594
PHY-3002 : Step(77): len = 312230, overlap = 255.25
PHY-3002 : Step(78): len = 311648, overlap = 245.219
PHY-3002 : Step(79): len = 309856, overlap = 231.719
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000136528
PHY-3002 : Step(80): len = 315550, overlap = 217.438
PHY-3002 : Step(81): len = 321521, overlap = 200.781
PHY-3002 : Step(82): len = 323688, overlap = 198.5
PHY-3002 : Step(83): len = 325464, overlap = 186.094
PHY-3002 : Step(84): len = 324933, overlap = 185.5
PHY-3002 : Step(85): len = 324249, overlap = 173.656
PHY-3002 : Step(86): len = 322623, overlap = 165.906
PHY-3002 : Step(87): len = 323067, overlap = 160.75
PHY-3002 : Step(88): len = 322249, overlap = 173.219
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000265566
PHY-3002 : Step(89): len = 324720, overlap = 165.375
PHY-3002 : Step(90): len = 328689, overlap = 172.969
PHY-3002 : Step(91): len = 329694, overlap = 163.594
PHY-3002 : Step(92): len = 330722, overlap = 167.469
PHY-3002 : Step(93): len = 330736, overlap = 165.594
PHY-3002 : Step(94): len = 330854, overlap = 175.219
PHY-3002 : Step(95): len = 329896, overlap = 178.75
PHY-3002 : Step(96): len = 329601, overlap = 174.469
PHY-3002 : Step(97): len = 329368, overlap = 172.344
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(98): len = 330916, overlap = 178.031
PHY-3002 : Step(99): len = 333567, overlap = 170.75
PHY-3002 : Step(100): len = 334409, overlap = 164.875
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.015240s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (102.5%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21928.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 426472, over cnt = 1142(3%), over = 4931, worst = 30
PHY-1001 : End global iterations;  0.819866s wall, 1.187500s user + 0.031250s system = 1.218750s CPU (148.7%)

PHY-1001 : Congestion index: top1 = 68.17, top5 = 50.24, top10 = 41.40, top15 = 36.19.
PHY-3001 : End congestion estimation;  1.050435s wall, 1.406250s user + 0.031250s system = 1.437500s CPU (136.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21926 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.996068s wall, 0.984375s user + 0.015625s system = 1.000000s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00010846
PHY-3002 : Step(101): len = 377148, overlap = 159.094
PHY-3002 : Step(102): len = 388617, overlap = 150.438
PHY-3002 : Step(103): len = 387003, overlap = 150.625
PHY-3002 : Step(104): len = 386610, overlap = 141.844
PHY-3002 : Step(105): len = 391511, overlap = 137.969
PHY-3002 : Step(106): len = 392097, overlap = 129.656
PHY-3002 : Step(107): len = 395428, overlap = 128.469
PHY-3002 : Step(108): len = 398768, overlap = 121.406
PHY-3002 : Step(109): len = 401150, overlap = 111.656
PHY-3002 : Step(110): len = 403480, overlap = 107.688
PHY-3002 : Step(111): len = 402984, overlap = 103.5
PHY-3002 : Step(112): len = 405193, overlap = 95.5625
PHY-3002 : Step(113): len = 406906, overlap = 87.8438
PHY-3002 : Step(114): len = 407897, overlap = 85.3438
PHY-3002 : Step(115): len = 410929, overlap = 82.0938
PHY-3002 : Step(116): len = 410177, overlap = 81.5312
PHY-3002 : Step(117): len = 408451, overlap = 85.75
PHY-3002 : Step(118): len = 409319, overlap = 86.25
PHY-3002 : Step(119): len = 410326, overlap = 85.9688
PHY-3002 : Step(120): len = 411335, overlap = 83.125
PHY-3002 : Step(121): len = 412077, overlap = 87.375
PHY-3002 : Step(122): len = 413260, overlap = 88.9375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00021692
PHY-3002 : Step(123): len = 414412, overlap = 86.2812
PHY-3002 : Step(124): len = 416476, overlap = 86.2188
PHY-3002 : Step(125): len = 419446, overlap = 84.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00043384
PHY-3002 : Step(126): len = 424120, overlap = 87.7188
PHY-3002 : Step(127): len = 432607, overlap = 95.6562
PHY-3002 : Step(128): len = 438314, overlap = 91.1562
PHY-3002 : Step(129): len = 440207, overlap = 89.3438
PHY-3002 : Step(130): len = 442805, overlap = 89.9375
PHY-3002 : Step(131): len = 446329, overlap = 86.7812
PHY-3002 : Step(132): len = 449897, overlap = 84.7812
PHY-3002 : Step(133): len = 450100, overlap = 85.5
PHY-3002 : Step(134): len = 449756, overlap = 84.4375
PHY-3002 : Step(135): len = 449971, overlap = 84.8125
PHY-3002 : Step(136): len = 448349, overlap = 87.5938
PHY-3002 : Step(137): len = 446454, overlap = 96
PHY-3002 : Step(138): len = 448088, overlap = 106.719
PHY-3002 : Step(139): len = 447168, overlap = 113.031
PHY-3002 : Step(140): len = 446571, overlap = 113.562
PHY-3002 : Step(141): len = 446799, overlap = 118.5
PHY-3002 : Step(142): len = 447517, overlap = 120.594
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(143): len = 448080, overlap = 120.812
PHY-3002 : Step(144): len = 450063, overlap = 118.375
PHY-3002 : Step(145): len = 452193, overlap = 114.719
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 51/21928.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 511944, over cnt = 2162(6%), over = 10103, worst = 32
PHY-1001 : End global iterations;  1.056406s wall, 1.906250s user + 0.078125s system = 1.984375s CPU (187.8%)

PHY-1001 : Congestion index: top1 = 75.30, top5 = 57.11, top10 = 49.05, top15 = 44.05.
PHY-3001 : End congestion estimation;  1.364579s wall, 2.218750s user + 0.078125s system = 2.296875s CPU (168.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21926 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.039563s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (99.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000101868
PHY-3002 : Step(146): len = 462025, overlap = 346.531
PHY-3002 : Step(147): len = 468817, overlap = 291.281
PHY-3002 : Step(148): len = 465798, overlap = 254.906
PHY-3002 : Step(149): len = 460859, overlap = 241.688
PHY-3002 : Step(150): len = 458877, overlap = 214.938
PHY-3002 : Step(151): len = 456486, overlap = 205.25
PHY-3002 : Step(152): len = 455751, overlap = 204.688
PHY-3002 : Step(153): len = 455748, overlap = 202.531
PHY-3002 : Step(154): len = 453134, overlap = 219.906
PHY-3002 : Step(155): len = 453533, overlap = 230.344
PHY-3002 : Step(156): len = 452954, overlap = 227.406
PHY-3002 : Step(157): len = 451008, overlap = 226.938
PHY-3002 : Step(158): len = 451301, overlap = 218.594
PHY-3002 : Step(159): len = 449727, overlap = 227.875
PHY-3002 : Step(160): len = 447413, overlap = 222.344
PHY-3002 : Step(161): len = 446453, overlap = 219.906
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000203737
PHY-3002 : Step(162): len = 446617, overlap = 217.062
PHY-3002 : Step(163): len = 448577, overlap = 211.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000378829
PHY-3002 : Step(164): len = 450540, overlap = 211.688
PHY-3002 : Step(165): len = 462152, overlap = 188.938
PHY-3002 : Step(166): len = 468136, overlap = 177.5
PHY-3002 : Step(167): len = 467072, overlap = 176.656
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000757659
PHY-3002 : Step(168): len = 467928, overlap = 168.219
PHY-3002 : Step(169): len = 475145, overlap = 153.5
PHY-3002 : Step(170): len = 480961, overlap = 146.156
PHY-3002 : Step(171): len = 482983, overlap = 137.406
PHY-3002 : Step(172): len = 484970, overlap = 132.844
PHY-3002 : Step(173): len = 485349, overlap = 129.469
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00137731
PHY-3002 : Step(174): len = 485918, overlap = 129.906
PHY-3002 : Step(175): len = 489085, overlap = 127.031
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80675, tnet num: 21926, tinst num: 19442, tnode num: 113509, tedge num: 126828.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.558378s wall, 1.546875s user + 0.015625s system = 1.562500s CPU (100.3%)

RUN-1004 : used memory is 567 MB, reserved memory is 540 MB, peak memory is 699 MB
OPT-1001 : Total overflow 492.97 peak overflow 3.66
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 458/21928.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 561672, over cnt = 2578(7%), over = 8801, worst = 23
PHY-1001 : End global iterations;  1.280943s wall, 2.062500s user + 0.031250s system = 2.093750s CPU (163.5%)

PHY-1001 : Congestion index: top1 = 58.64, top5 = 48.40, top10 = 43.43, top15 = 40.31.
PHY-1001 : End incremental global routing;  1.545137s wall, 2.328125s user + 0.031250s system = 2.359375s CPU (152.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21926 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.067370s wall, 1.015625s user + 0.046875s system = 1.062500s CPU (99.5%)

OPT-1001 : 16 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19364 has valid locations, 216 needs to be replaced
PHY-3001 : design contains 19642 instances, 5728 luts, 12328 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 503880
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17594/22128.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 574088, over cnt = 2604(7%), over = 8892, worst = 23
PHY-1001 : End global iterations;  0.202761s wall, 0.281250s user + 0.031250s system = 0.312500s CPU (154.1%)

PHY-1001 : Congestion index: top1 = 58.71, top5 = 48.65, top10 = 43.83, top15 = 40.72.
PHY-3001 : End congestion estimation;  0.468444s wall, 0.546875s user + 0.031250s system = 0.578125s CPU (123.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81322, tnet num: 22126, tinst num: 19642, tnode num: 114397, tedge num: 127722.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.557827s wall, 1.500000s user + 0.062500s system = 1.562500s CPU (100.3%)

RUN-1004 : used memory is 609 MB, reserved memory is 601 MB, peak memory is 703 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22126 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.664675s wall, 2.593750s user + 0.078125s system = 2.671875s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(176): len = 504037, overlap = 0.375
PHY-3002 : Step(177): len = 504727, overlap = 0.375
PHY-3002 : Step(178): len = 505295, overlap = 0.375
PHY-3002 : Step(179): len = 506633, overlap = 0.375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17626/22128.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 573864, over cnt = 2616(7%), over = 8977, worst = 23
PHY-1001 : End global iterations;  0.201862s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (116.1%)

PHY-1001 : Congestion index: top1 = 59.01, top5 = 49.10, top10 = 44.07, top15 = 40.90.
PHY-3001 : End congestion estimation;  0.463853s wall, 0.468750s user + 0.031250s system = 0.500000s CPU (107.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22126 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.067599s wall, 1.046875s user + 0.015625s system = 1.062500s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000619463
PHY-3002 : Step(180): len = 506426, overlap = 129.281
PHY-3002 : Step(181): len = 506294, overlap = 128.969
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00123893
PHY-3002 : Step(182): len = 506502, overlap = 129.062
PHY-3002 : Step(183): len = 506629, overlap = 129.125
PHY-3001 : Final: Len = 506629, Over = 129.125
PHY-3001 : End incremental placement;  5.621436s wall, 5.953125s user + 0.281250s system = 6.234375s CPU (110.9%)

OPT-1001 : Total overflow 497.00 peak overflow 3.66
OPT-1001 : End high-fanout net optimization;  8.787703s wall, 10.015625s user + 0.359375s system = 10.375000s CPU (118.1%)

OPT-1001 : Current memory(MB): used = 703, reserve = 682, peak = 719.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17634/22128.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 575408, over cnt = 2574(7%), over = 8488, worst = 23
PHY-1002 : len = 612840, over cnt = 1857(5%), over = 4687, worst = 23
PHY-1002 : len = 641872, over cnt = 1099(3%), over = 2581, worst = 16
PHY-1002 : len = 664392, over cnt = 506(1%), over = 1237, worst = 16
PHY-1002 : len = 683096, over cnt = 55(0%), over = 143, worst = 11
PHY-1001 : End global iterations;  1.275366s wall, 1.781250s user + 0.000000s system = 1.781250s CPU (139.7%)

PHY-1001 : Congestion index: top1 = 51.44, top5 = 44.23, top10 = 40.83, top15 = 38.71.
OPT-1001 : End congestion update;  1.545112s wall, 2.062500s user + 0.000000s system = 2.062500s CPU (133.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22126 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.919171s wall, 0.906250s user + 0.000000s system = 0.906250s CPU (98.6%)

OPT-0007 : Start: WNS 3837 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.470718s wall, 2.984375s user + 0.000000s system = 2.984375s CPU (120.8%)

OPT-1001 : Current memory(MB): used = 678, reserve = 658, peak = 719.
OPT-1001 : End physical optimization;  13.147901s wall, 14.984375s user + 0.437500s system = 15.421875s CPU (117.3%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5728 LUT to BLE ...
SYN-4008 : Packed 5728 LUT and 2734 SEQ to BLE.
SYN-4003 : Packing 9594 remaining SEQ's ...
SYN-4005 : Packed 3428 SEQ with LUT/SLICE
SYN-4006 : 110 single LUT's are left
SYN-4006 : 6166 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11894/13746 primitive instances ...
PHY-3001 : End packing;  2.884807s wall, 2.875000s user + 0.000000s system = 2.875000s CPU (99.7%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8103 instances
RUN-1001 : 4008 mslices, 4009 lslices, 59 pads, 22 brams, 0 dsps
RUN-1001 : There are total 19445 nets
RUN-1001 : 13627 nets have 2 pins
RUN-1001 : 4409 nets have [3 - 5] pins
RUN-1001 : 901 nets have [6 - 10] pins
RUN-1001 : 370 nets have [11 - 20] pins
RUN-1001 : 129 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8101 instances, 8017 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 520922, Over = 352.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7822/19445.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 647728, over cnt = 1537(4%), over = 2370, worst = 8
PHY-1002 : len = 652024, over cnt = 1071(3%), over = 1516, worst = 8
PHY-1002 : len = 665544, over cnt = 336(0%), over = 458, worst = 8
PHY-1002 : len = 671056, over cnt = 114(0%), over = 167, worst = 5
PHY-1002 : len = 674072, over cnt = 2(0%), over = 2, worst = 1
PHY-1001 : End global iterations;  1.204762s wall, 1.921875s user + 0.015625s system = 1.937500s CPU (160.8%)

PHY-1001 : Congestion index: top1 = 50.97, top5 = 43.75, top10 = 40.32, top15 = 38.21.
PHY-3001 : End congestion estimation;  1.547747s wall, 2.265625s user + 0.015625s system = 2.281250s CPU (147.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67402, tnet num: 19443, tinst num: 8101, tnode num: 91442, tedge num: 111049.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.737058s wall, 1.734375s user + 0.000000s system = 1.734375s CPU (99.8%)

RUN-1004 : used memory is 602 MB, reserved memory is 592 MB, peak memory is 719 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19443 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.696506s wall, 2.671875s user + 0.031250s system = 2.703125s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.35701e-05
PHY-3002 : Step(184): len = 524951, overlap = 335.75
PHY-3002 : Step(185): len = 523260, overlap = 354.75
PHY-3002 : Step(186): len = 522853, overlap = 376
PHY-3002 : Step(187): len = 521937, overlap = 381
PHY-3002 : Step(188): len = 519933, overlap = 382.25
PHY-3002 : Step(189): len = 518560, overlap = 385
PHY-3002 : Step(190): len = 516103, overlap = 391.25
PHY-3002 : Step(191): len = 514962, overlap = 402
PHY-3002 : Step(192): len = 513390, overlap = 403
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00010714
PHY-3002 : Step(193): len = 517489, overlap = 394.75
PHY-3002 : Step(194): len = 522224, overlap = 388
PHY-3002 : Step(195): len = 523087, overlap = 385.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(196): len = 529501, overlap = 370.75
PHY-3002 : Step(197): len = 537398, overlap = 356.75
PHY-3002 : Step(198): len = 537575, overlap = 357.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.740606s wall, 0.843750s user + 0.921875s system = 1.765625s CPU (238.4%)

PHY-3001 : Trial Legalized: Len = 639234
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 84%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 635/19445.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 722656, over cnt = 2380(6%), over = 3837, worst = 7
PHY-1002 : len = 737072, over cnt = 1493(4%), over = 2052, worst = 7
PHY-1002 : len = 754480, over cnt = 541(1%), over = 733, worst = 7
PHY-1002 : len = 764800, over cnt = 83(0%), over = 90, worst = 2
PHY-1002 : len = 766456, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.924071s wall, 3.125000s user + 0.031250s system = 3.156250s CPU (164.0%)

PHY-1001 : Congestion index: top1 = 49.40, top5 = 44.89, top10 = 42.23, top15 = 40.53.
PHY-3001 : End congestion estimation;  2.303997s wall, 3.500000s user + 0.031250s system = 3.531250s CPU (153.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19443 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.938880s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000189001
PHY-3002 : Step(199): len = 597108, overlap = 79.5
PHY-3002 : Step(200): len = 578713, overlap = 135.5
PHY-3002 : Step(201): len = 567916, overlap = 181.25
PHY-3002 : Step(202): len = 561954, overlap = 216.75
PHY-3002 : Step(203): len = 558535, overlap = 243.5
PHY-3002 : Step(204): len = 556339, overlap = 255.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000378002
PHY-3002 : Step(205): len = 560178, overlap = 251.75
PHY-3002 : Step(206): len = 563772, overlap = 244
PHY-3002 : Step(207): len = 564073, overlap = 240.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(208): len = 566558, overlap = 238.25
PHY-3002 : Step(209): len = 570711, overlap = 242.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.036871s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (127.1%)

PHY-3001 : Legalized: Len = 610164, Over = 0
PHY-3001 : Spreading special nets. 54 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.089136s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (87.6%)

PHY-3001 : 87 instances has been re-located, deltaX = 27, deltaY = 47, maxDist = 2.
PHY-3001 : Final: Len = 611604, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67402, tnet num: 19443, tinst num: 8101, tnode num: 91442, tedge num: 111049.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.004259s wall, 2.000000s user + 0.000000s system = 2.000000s CPU (99.8%)

RUN-1004 : used memory is 603 MB, reserved memory is 594 MB, peak memory is 719 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 4079/19445.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 706224, over cnt = 2171(6%), over = 3396, worst = 7
PHY-1002 : len = 717232, over cnt = 1371(3%), over = 1914, worst = 6
PHY-1002 : len = 731288, over cnt = 704(2%), over = 918, worst = 5
PHY-1002 : len = 742512, over cnt = 199(0%), over = 243, worst = 4
PHY-1002 : len = 746432, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.719368s wall, 2.843750s user + 0.000000s system = 2.843750s CPU (165.4%)

PHY-1001 : Congestion index: top1 = 47.46, top5 = 43.49, top10 = 40.98, top15 = 39.33.
PHY-1001 : End incremental global routing;  2.050532s wall, 3.187500s user + 0.000000s system = 3.187500s CPU (155.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19443 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.978975s wall, 0.937500s user + 0.031250s system = 0.968750s CPU (99.0%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8038 has valid locations, 9 needs to be replaced
PHY-3001 : design contains 8109 instances, 8025 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 612998
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17610/19452.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 747680, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 747720, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 747752, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 747768, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 747784, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.807556s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (98.7%)

PHY-1001 : Congestion index: top1 = 47.52, top5 = 43.51, top10 = 41.02, top15 = 39.36.
PHY-3001 : End congestion estimation;  1.132864s wall, 1.125000s user + 0.000000s system = 1.125000s CPU (99.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67449, tnet num: 19450, tinst num: 8109, tnode num: 91497, tedge num: 111103.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.967178s wall, 1.968750s user + 0.000000s system = 1.968750s CPU (100.1%)

RUN-1004 : used memory is 643 MB, reserved memory is 625 MB, peak memory is 719 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19450 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.957647s wall, 2.953125s user + 0.000000s system = 2.953125s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(210): len = 612845, overlap = 0
PHY-3002 : Step(211): len = 612785, overlap = 0.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17607/19452.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 747312, over cnt = 13(0%), over = 15, worst = 2
PHY-1002 : len = 747296, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 747328, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 747344, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.539695s wall, 0.546875s user + 0.031250s system = 0.578125s CPU (107.1%)

PHY-1001 : Congestion index: top1 = 47.67, top5 = 43.58, top10 = 41.07, top15 = 39.39.
PHY-3001 : End congestion estimation;  0.860052s wall, 0.859375s user + 0.031250s system = 0.890625s CPU (103.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19450 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.954043s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (101.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000777955
PHY-3002 : Step(212): len = 612777, overlap = 0.75
PHY-3002 : Step(213): len = 612733, overlap = 0.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006576s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 612765, Over = 0
PHY-3001 : End spreading;  0.074722s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (83.6%)

PHY-3001 : Final: Len = 612765, Over = 0
PHY-3001 : End incremental placement;  6.554806s wall, 6.593750s user + 0.093750s system = 6.687500s CPU (102.0%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.099131s wall, 11.406250s user + 0.125000s system = 11.531250s CPU (114.2%)

OPT-1001 : Current memory(MB): used = 714, reserve = 699, peak = 719.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17607/19452.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 747448, over cnt = 8(0%), over = 9, worst = 2
PHY-1002 : len = 747440, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 747480, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 747512, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 747608, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.717198s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (100.2%)

PHY-1001 : Congestion index: top1 = 47.59, top5 = 43.52, top10 = 41.06, top15 = 39.38.
OPT-1001 : End congestion update;  1.035191s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (99.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19450 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.796284s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (100.1%)

OPT-0007 : Start: WNS 4103 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.836547s wall, 1.828125s user + 0.000000s system = 1.828125s CPU (99.5%)

OPT-1001 : Current memory(MB): used = 714, reserve = 699, peak = 719.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19450 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.794130s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (100.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17618/19452.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 747608, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.135834s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (103.5%)

PHY-1001 : Congestion index: top1 = 47.59, top5 = 43.52, top10 = 41.06, top15 = 39.38.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19450 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.799491s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.7%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4103 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 47.068966
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4103ps with logic level 8 
RUN-1001 :       #2 path slack 4178ps with logic level 8 
OPT-1001 : End physical optimization;  16.247215s wall, 17.531250s user + 0.125000s system = 17.656250s CPU (108.7%)

RUN-1003 : finish command "place" in  72.951747s wall, 141.546875s user + 7.859375s system = 149.406250s CPU (204.8%)

RUN-1004 : used memory is 632 MB, reserved memory is 608 MB, peak memory is 719 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.737015s wall, 2.906250s user + 0.000000s system = 2.906250s CPU (167.3%)

RUN-1004 : used memory is 632 MB, reserved memory is 608 MB, peak memory is 719 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8111 instances
RUN-1001 : 4008 mslices, 4017 lslices, 59 pads, 22 brams, 0 dsps
RUN-1001 : There are total 19452 nets
RUN-1001 : 13627 nets have 2 pins
RUN-1001 : 4409 nets have [3 - 5] pins
RUN-1001 : 904 nets have [6 - 10] pins
RUN-1001 : 375 nets have [11 - 20] pins
RUN-1001 : 128 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67449, tnet num: 19450, tinst num: 8109, tnode num: 91497, tedge num: 111103.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.862715s wall, 1.859375s user + 0.000000s system = 1.859375s CPU (99.8%)

RUN-1004 : used memory is 639 MB, reserved memory is 632 MB, peak memory is 719 MB
PHY-1001 : 4008 mslices, 4017 lslices, 59 pads, 22 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19450 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 685536, over cnt = 2329(6%), over = 3737, worst = 6
PHY-1002 : len = 699512, over cnt = 1517(4%), over = 2158, worst = 6
PHY-1002 : len = 717640, over cnt = 615(1%), over = 823, worst = 6
PHY-1002 : len = 730656, over cnt = 26(0%), over = 30, worst = 2
PHY-1002 : len = 731560, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.761092s wall, 3.031250s user + 0.046875s system = 3.078125s CPU (174.8%)

PHY-1001 : Congestion index: top1 = 47.52, top5 = 43.26, top10 = 40.52, top15 = 38.88.
PHY-1001 : End global routing;  2.129157s wall, 3.406250s user + 0.046875s system = 3.453125s CPU (162.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 700, reserve = 687, peak = 719.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 973, reserve = 958, peak = 973.
PHY-1001 : End build detailed router design. 5.110150s wall, 5.031250s user + 0.078125s system = 5.109375s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 192032, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.918962s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 1007, reserve = 994, peak = 1007.
PHY-1001 : End phase 1; 0.926221s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (101.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 45% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.63534e+06, over cnt = 1243(0%), over = 1246, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1020, reserve = 1006, peak = 1020.
PHY-1001 : End initial routed; 13.782400s wall, 43.125000s user + 0.265625s system = 43.390625s CPU (314.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18180(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.912   |   0.000   |   0   
RUN-1001 :   Hold   |   0.156   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.530121s wall, 3.531250s user + 0.000000s system = 3.531250s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1033, reserve = 1019, peak = 1033.
PHY-1001 : End phase 2; 17.312690s wall, 46.656250s user + 0.265625s system = 46.921875s CPU (271.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.63534e+06, over cnt = 1243(0%), over = 1246, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.253105s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (98.8%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.62728e+06, over cnt = 394(0%), over = 394, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.558133s wall, 1.000000s user + 0.031250s system = 1.031250s CPU (184.8%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.62743e+06, over cnt = 58(0%), over = 58, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.429442s wall, 0.484375s user + 0.015625s system = 0.500000s CPU (116.4%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.62826e+06, over cnt = 11(0%), over = 11, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.224037s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (132.5%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.62841e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.183805s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (102.0%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.62846e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.158392s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (98.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18180(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.912   |   0.000   |   0   
RUN-1001 :   Hold   |   0.156   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.517734s wall, 3.531250s user + 0.000000s system = 3.531250s CPU (100.4%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 266 feed throughs used by 228 nets
PHY-1001 : End commit to database; 2.164680s wall, 2.140625s user + 0.015625s system = 2.156250s CPU (99.6%)

PHY-1001 : Current memory(MB): used = 1116, reserve = 1105, peak = 1116.
PHY-1001 : End phase 3; 7.993188s wall, 8.500000s user + 0.078125s system = 8.578125s CPU (107.3%)

PHY-1003 : Routed, final wirelength = 1.62846e+06
PHY-1001 : Current memory(MB): used = 1120, reserve = 1109, peak = 1120.
PHY-1001 : End export database. 0.061266s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.0%)

PHY-1001 : End detail routing;  31.836866s wall, 61.625000s user + 0.421875s system = 62.046875s CPU (194.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67449, tnet num: 19450, tinst num: 8109, tnode num: 91497, tedge num: 111103.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.750010s wall, 1.750000s user + 0.000000s system = 1.750000s CPU (100.0%)

RUN-1004 : used memory is 1049 MB, reserved memory is 1049 MB, peak memory is 1120 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  40.295684s wall, 71.359375s user + 0.468750s system = 71.828125s CPU (178.3%)

RUN-1004 : used memory is 1048 MB, reserved memory is 1050 MB, peak memory is 1120 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8881   out of  19600   45.31%
#reg                    12422   out of  19600   63.38%
#le                     15003
  #lut only              2581   out of  15003   17.20%
  #reg only              6122   out of  15003   40.81%
  #lut&reg               6300   out of  15003   41.99%
#dsp                        0   out of     29    0.00%
#bram                      22   out of     64   34.38%
  #bram9k                  20
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       59   out of    188   31.38%
  #ireg                    10
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6785
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          165
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         F2        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         D1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         B3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         C1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         A3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         A2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         B2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         B1        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F15        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDZ        INPUT        K15        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         P2        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         N4        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         M5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         N6        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         N5        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         P5        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M3        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         M4        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         H2        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         M1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         L1        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         N1        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         N3        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         M2        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         T4        LVCMOS33           8            NONE           OREG       
    Fog_ENZ        OUTPUT         T7        LVCMOS33           8            NONE           OREG       
    Fog_INT        OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT         C3        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT        B15        LVCMOS33           8            N/A            OREG       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        R15        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         K2        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT         F6        LVCMOS25           8            N/A            NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15003  |7379    |1502    |12466   |22      |0       |
|  AnyFog_dataX                      |AnyFog          |208    |68      |22      |172     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |53      |22      |50      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |210    |94      |22      |170     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |90     |63      |22      |50      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |207    |89      |22      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |87     |61      |22      |53      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2927   |652     |39      |2845    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |61     |37      |5       |53      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |213    |71      |5       |204     |0       |0       |
|    STADOP_com2                     |STADOP          |546    |42      |0       |541     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |41      |14      |42      |0       |0       |
|    head_com2                       |uniheading      |271    |98      |5       |258     |0       |0       |
|    rmc_com2                        |Gprmc           |36     |34      |0       |30      |0       |0       |
|    uart_com2                       |Agrica          |1426   |308     |10      |1408    |0       |0       |
|  COM3                              |COM3_Control    |270    |157     |19      |229     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |62     |35      |5       |53      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |61     |40      |14      |39      |0       |0       |
|    rmc_com3                        |Gprmc           |147    |82      |0       |137     |0       |0       |
|  DATA                              |Data_Processing |8787   |4576    |1122    |7012    |0       |0       |
|    DIV_Dtemp                       |Divider         |816    |348     |84      |694     |0       |0       |
|    DIV_Utemp                       |Divider         |657    |336     |84      |535     |0       |0       |
|    DIV_accX                        |Divider         |549    |270     |84      |428     |0       |0       |
|    DIV_accY                        |Divider         |633    |316     |102     |478     |0       |0       |
|    DIV_accZ                        |Divider         |656    |373     |132     |454     |0       |0       |
|    DIV_rateX                       |Divider         |682    |379     |132     |478     |0       |0       |
|    DIV_rateY                       |Divider         |548    |367     |132     |341     |0       |0       |
|    DIV_rateZ                       |Divider         |555    |371     |132     |354     |0       |0       |
|    genclk                          |genclk          |262    |159     |89      |103     |0       |0       |
|  FMC                               |FMC_Ctrl        |464    |415     |43      |352     |0       |0       |
|  IIC                               |I2C_master      |315    |278     |11      |270     |0       |0       |
|  IMU_CTRL                          |SCHA634         |907    |642     |61      |740     |0       |0       |
|    CtrlData                        |CtrlData        |449    |392     |47      |332     |0       |0       |
|      usms                          |Time_1ms        |30     |25      |5       |20      |0       |0       |
|    SPIM                            |SPI_SCHA634     |458    |250     |14      |408     |0       |0       |
|  POWER                             |POWER_EN        |99     |43      |38      |39      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |599    |365     |103     |410     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |599    |365     |103     |410     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |278    |162     |0       |268     |0       |0       |
|        reg_inst                    |register        |276    |160     |0       |266     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |321    |203     |103     |142     |0       |0       |
|        bus_inst                    |bus_top         |130    |79      |46      |49      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |27     |15      |10      |11      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |51     |33      |18      |18      |0       |0       |
|          BUS_DETECTOR[5]$bus_nodes |bus_det         |50     |29      |18      |18      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |117    |80      |29      |67      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13567  
    #2          2       3439   
    #3          3        681   
    #4          4        289   
    #5        5-10       960   
    #6        11-50      436   
    #7       51-100      11    
    #8       101-500      3    
    #9        >500        2    
  Average     2.11             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.123481s wall, 3.640625s user + 0.015625s system = 3.656250s CPU (172.2%)

RUN-1004 : used memory is 1049 MB, reserved memory is 1051 MB, peak memory is 1120 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67449, tnet num: 19450, tinst num: 8109, tnode num: 91497, tedge num: 111103.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.733613s wall, 1.718750s user + 0.000000s system = 1.718750s CPU (99.1%)

RUN-1004 : used memory is 1051 MB, reserved memory is 1052 MB, peak memory is 1120 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19450 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.449123s wall, 1.453125s user + 0.000000s system = 1.453125s CPU (100.3%)

RUN-1004 : used memory is 1055 MB, reserved memory is 1056 MB, peak memory is 1120 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 853bcefd6f8683164bbfb7926f14a230bf207ba9ebe1befe0692b850b36b2763 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8109
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19452, pip num: 144791
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 266
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3228 valid insts, and 407206 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101000110010111001101000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.566021s wall, 125.875000s user + 0.140625s system = 126.015625s CPU (1002.8%)

RUN-1004 : used memory is 1179 MB, reserved memory is 1164 MB, peak memory is 1293 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250820_144658.log"
